#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JDY-31蓝牙连接测试工具
用于验证蓝牙串口连接是否正常
"""

import serial
import serial.tools.list_ports
import time
import sys

def list_available_ports():
    """列出所有可用的串口"""
    print("=== 可用串口列表 ===")
    ports = serial.tools.list_ports.comports()
    
    if not ports:
        print("未找到任何串口设备")
        return []
    
    for i, port in enumerate(ports):
        print(f"{i+1}. {port.device} - {port.description}")
        if "蓝牙" in port.description or "Bluetooth" in port.description:
            print(f"   *** 这可能是蓝牙串口 ***")
    
    return [port.device for port in ports]

def test_bluetooth_connection(port_name, baudrate=9600):
    """测试蓝牙连接"""
    try:
        print(f"\n=== 测试蓝牙连接 ===")
        print(f"端口: {port_name}")
        print(f"波特率: {baudrate}")
        
        # 打开串口
        ser = serial.Serial(
            port=port_name,
            baudrate=baudrate,
            bytesize=serial.EIGHTBITS,
            parity=serial.PARITY_NONE,
            stopbits=serial.STOPBITS_ONE,
            timeout=2
        )
        
        print("串口打开成功！")
        
        # 发送测试命令
        test_commands = [
            "STATUS\r\n",
            "HELP\r\n", 
            "INFO\r\n"
        ]
        
        for cmd in test_commands:
            print(f"\n发送命令: {cmd.strip()}")
            ser.write(cmd.encode())
            time.sleep(0.5)
            
            # 读取响应
            if ser.in_waiting > 0:
                response = ser.read(ser.in_waiting).decode('utf-8', errors='ignore')
                print(f"收到响应: {response}")
            else:
                print("未收到响应")
        
        # 监听自动状态报告
        print("\n=== 监听自动状态报告 (10秒) ===")
        start_time = time.time()
        
        while time.time() - start_time < 10:
            if ser.in_waiting > 0:
                data = ser.read(ser.in_waiting).decode('utf-8', errors='ignore')
                print(f"接收数据: {data}")
            time.sleep(0.1)
        
        ser.close()
        print("\n测试完成！")
        return True
        
    except serial.SerialException as e:
        print(f"串口错误: {e}")
        return False
    except Exception as e:
        print(f"其他错误: {e}")
        return False

def main():
    """主函数"""
    print("JDY-31蓝牙连接测试工具")
    print("=" * 40)
    
    # 列出可用端口
    ports = list_available_ports()
    
    if not ports:
        print("\n请检查：")
        print("1. JDY-31蓝牙模块是否已配对")
        print("2. 是否已手动添加蓝牙串口")
        print("3. 蓝牙服务是否正常运行")
        return
    
    # 选择端口
    try:
        choice = int(input(f"\n请选择要测试的端口 (1-{len(ports)}): ")) - 1
        if 0 <= choice < len(ports):
            selected_port = ports[choice]
        else:
            print("选择无效")
            return
    except ValueError:
        print("输入无效")
        return
    
    # 测试连接
    success = test_bluetooth_connection(selected_port)
    
    if success:
        print("\n✅ 蓝牙连接测试成功！")
        print("可以使用串口调试工具连接此端口")
    else:
        print("\n❌ 蓝牙连接测试失败")
        print("请检查蓝牙连接和模块配置")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n程序错误: {e}")
    
    input("\n按回车键退出...")
