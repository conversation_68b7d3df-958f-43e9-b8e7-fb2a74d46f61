#ifndef __MEMORY_POINT_H
#define __MEMORY_POINT_H

#include "stdint.h"
#include "Servo.h"

/**
 * 记忆点控制模块
 * 实现三按键控制方案：
 * - PB0: 舵机卸载，可手动调整
 * - PB1: 舵机上载并记录当前位置为记忆点
 * - PB11: 立刻回到记忆点位置
 */

// 记忆点数据结构
typedef struct {
    float pan_angle;     // 水平舵机角度 (度)
    float tilt_angle;    // 垂直舵机角度 (度)
    uint8_t is_valid;    // 记忆点是否有效 (0=无效, 1=有效)
    uint32_t timestamp;  // 记录时间戳 (系统tick)
} MemoryPoint_t;

// 系统状态定义
typedef enum {
    MEMORY_STATE_IDLE = 0,      // 空闲状态
    MEMORY_STATE_UNLOADED,      // 舵机已卸载状态
    MEMORY_STATE_RECORDING,     // 正在记录记忆点
    MEMORY_STATE_RETURNING,     // 正在回到记忆点
    MEMORY_STATE_ERROR          // 错误状态
} MemoryState_t;

// 操作结果定义
typedef enum {
    MEMORY_OK = 0,              // 操作成功
    MEMORY_ERROR_INVALID_POINT, // 记忆点无效
    MEMORY_ERROR_SERVO_COMM,    // 舵机通信错误
    MEMORY_ERROR_TIMEOUT,       // 操作超时
    MEMORY_ERROR_STATE          // 状态错误
} MemoryResult_t;

// 舵机ID定义 (与现有项目保持一致)
#define SERVO_PAN_ID    1       // 水平舵机ID
#define SERVO_TILT_ID   2       // 垂直舵机ID

// 时间参数定义
#define MEMORY_RETURN_TIME_MS   1000    // 回到记忆点的时间 (毫秒)
#define MEMORY_RECORD_DELAY_MS  100     // 记录前的延迟时间
#define MEMORY_OPERATION_TIMEOUT_MS 3000 // 操作超时时间

// 函数声明
void MemoryPoint_Init(void);
MemoryResult_t MemoryPoint_UnloadServos(void);
MemoryResult_t MemoryPoint_RecordPosition(void);
MemoryResult_t MemoryPoint_ReturnToMemory(void);
MemoryState_t MemoryPoint_GetState(void);
MemoryPoint_t* MemoryPoint_GetData(void);
uint8_t MemoryPoint_IsValid(void);
void MemoryPoint_ClearMemory(void);
void MemoryPoint_Update(void);

// 调试和状态查询函数
const char* MemoryPoint_GetStateString(void);
const char* MemoryPoint_GetResultString(MemoryResult_t result);
void MemoryPoint_PrintStatus(void);

#endif /* __MEMORY_POINT_H */
