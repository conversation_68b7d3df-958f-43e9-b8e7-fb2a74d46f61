..\obj\main.o: main.c
..\obj\main.o: ..\SYSTEM\sys\sys.h
..\obj\main.o: ..\USER\stm32f4xx.h
..\obj\main.o: ..\CORE\core_cm4.h
..\obj\main.o: E:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
..\obj\main.o: ..\CORE\core_cmInstr.h
..\obj\main.o: ..\CORE\core_cmFunc.h
..\obj\main.o: ..\CORE\core_cm4_simd.h
..\obj\main.o: ..\USER\system_stm32f4xx.h
..\obj\main.o: ..\USER\stm32f4xx_conf.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_adc.h
..\obj\main.o: ..\USER\stm32f4xx.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_crc.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_dbgmcu.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_dma.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_exti.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_flash.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_gpio.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_pwr.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_rcc.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_syscfg.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_tim.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_usart.h
..\obj\main.o: ..\FWLIB\inc\misc.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_fsmc.h
..\obj\main.o: ..\SYSTEM\delay\delay.h
..\obj\main.o: ..\SYSTEM\sys\sys.h
..\obj\main.o: ..\SYSTEM\usart\usart.h
..\obj\main.o: E:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
..\obj\main.o: ..\HAREWARE\ATD5984\ATD5984.h
..\obj\main.o: ..\SYSTEM\sys\../../HAREWARE/MOTOR_CONTROL/motor_control.h
..\obj\main.o: E:\Keil_v5\ARM\ARMCC\Bin\..\include\stdbool.h
..\obj\main.o: E:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
..\obj\main.o: E:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
..\obj\main.o: E:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
..\obj\main.o: ../HAREWARE/K230_COMM/k230_comm.h
..\obj\main.o: ../HAREWARE/KEY/KEY.h
