# 舵机复位功能调试指南

## 🔍 **问题分析**

您反馈复位开关按下后舵机不动，我已经添加了PB1按键功能并增强了调试功能。

### **可能的问题原因**

1. **串口通信问题**
   - USART2 (PA2/PA3) 连接错误
   - 波特率不匹配
   - 舵机驱动板问题

2. **舵机硬件问题**
   - 舵机ID设置错误
   - 电源供电不足
   - 舵机损坏或卡死

3. **协议格式问题**
   - 命令格式错误
   - 校验和计算错误
   - 参数设置错误

## 🔧 **修复内容**

### **1. 添加PB1按键功能**
```c
// 按键初始化
void Key_Init(void);

// 按键处理
void Process_KeyCommand(void);

// PB1按键按下 → 舵机复位
if (key == KEY_PB1) {
    Servo_ResetWithDebug();
}
```

### **2. 增强舵机复位调试**
```c
void Servo_ResetWithDebug(void)
{
    OLED_ShowString(4, 1, "Resetting...");
    OLED_ShowString(4, 1, "S1 -> 90.0");    // 显示舵机1复位
    Servo_SetPositionWithTime(1, 90.0f, 1000);
    OLED_ShowString(4, 1, "S2 -> 120.0");   // 显示舵机2复位
    Servo_SetPositionWithTime(2, 120.0f, 1000);
    OLED_ShowString(4, 1, "Reset Complete!");
}
```

### **3. 改进舵机通信**
```c
// 调试版本发送函数 - 发送2次确保可靠性
static void Servo_SendCommandDebug(uint8_t id, uint8_t cmd, uint8_t* params, uint8_t param_len)
{
    for (int i = 0; i < 2; i++) {  // 发送2次
        Serial2_SendArray(buffer, index);
        Delay_ms(20);  // 增加延时
    }
}
```

## 🚀 **测试方法**

### **方法1：串口屏复位测试**
1. **操作**：按串口屏按键3 (发送0x03)
2. **观察OLED显示**：
   ```
   Screen+Servo Sys
   01:LED 02:LED 03:RESET
   S1:090.0 S2:120.0
   Screen Reset...
   ```
3. **预期结果**：
   - OLED显示 `S1 -> 90.0`
   - OLED显示 `S2 -> 120.0`
   - OLED显示 `Reset Complete!`
   - 舵机转动到指定角度

### **方法2：PB1按键复位测试**
1. **操作**：按下STM32的PB1引脚按键
2. **观察OLED显示**：
   ```
   Screen+Servo Sys
   01:LED 02:LED 03:RESET
   S1:090.0 S2:120.0
   Key Reset...
   ```
3. **预期结果**：
   - OLED显示 `S1 -> 90.0`
   - OLED显示 `S2 -> 120.0`
   - OLED显示 `Reset Complete!`
   - 舵机转动到指定角度

## 🔍 **调试步骤**

### **步骤1：硬件连接检查**

#### **串口连接验证**
```
STM32F103C8T6 → 舵机驱动板
PA2 (USART2_TX) → RXD
PA3 (USART2_RX) → TXD
GND             → GND
5V              → VCC
```

#### **按键连接验证**
```
STM32F103C8T6 → 按键
PB1           → 按键一端
GND           → 按键另一端 (或使用上拉电阻)
```

### **步骤2：舵机ID验证**
1. **确认舵机ID设置**：
   - 舵机1：ID = 1
   - 舵机2：ID = 2
2. **使用舵机调试软件验证ID**
3. **检查舵机是否响应基本命令**

### **步骤3：电源供电检查**
1. **电源电压**：确保5V稳定供电
2. **电流容量**：建议3A以上
3. **电源纹波**：检查电源质量
4. **接线牢固**：确保所有连接可靠

### **步骤4：通信协议验证**

#### **使用示波器/逻辑分析仪**
监控PA2输出信号，验证发送的命令格式：
```
期望信号 (舵机1到90度)：
55 55 01 06 01 [POS_L] [POS_H] [TIME_L] [TIME_H] [CRC]

具体数值：
55 55 01 06 01 6C 01 E8 03 [CRC]
解释：
- 55 55: 帧头
- 01: 舵机ID
- 06: 数据长度
- 01: 移动命令
- 6C 01: 位置值 (90度 = 375 = 0x016C)
- E8 03: 时间值 (1000ms = 0x03E8)
- [CRC]: 校验和
```

### **步骤5：软件调试**

#### **检查OLED显示**
1. **复位触发**：观察是否显示 `Resetting...`
2. **命令发送**：观察是否显示 `S1 -> 90.0` 和 `S2 -> 120.0`
3. **完成状态**：观察是否显示 `Reset Complete!`

#### **检查函数调用**
```c
// 在Servo_SetPositionWithTime函数中添加调试
void Servo_SetPositionWithTime(uint8_t id, float angle, uint16_t time_ms)
{
    // 添加调试输出
    OLED_ShowString(4, 1, "Sending CMD...");
    
    // 原有代码...
    Servo_SendCommandDebug(id, SERVO_CMD_MOVE_TIME_WRITE, params, 4);
    
    OLED_ShowString(4, 1, "CMD Sent!");
}
```

## ⚠️ **常见问题排查**

### **问题1：OLED显示复位信息但舵机不动**
**可能原因**：
- 串口2连接错误 (PA2/PA3)
- 舵机驱动板问题
- 舵机ID设置错误

**解决方法**：
- 检查PA2/PA3连接
- 用示波器检查PA2输出
- 验证舵机ID设置

### **问题2：按键无响应**
**可能原因**：
- PB1连接错误
- 按键硬件问题
- 代码中按键处理错误

**解决方法**：
- 检查PB1连接
- 测试按键硬件
- 验证Key_GetNum()函数

### **问题3：串口屏复位有效，PB1按键无效**
**可能原因**：
- PB1按键硬件问题
- 按键初始化错误
- 主循环中未调用Process_KeyCommand()

**解决方法**：
- 检查按键硬件连接
- 验证Key_Init()调用
- 确认主循环中有Process_KeyCommand()

### **问题4：舵机只有一个动作**
**可能原因**：
- 舵机ID冲突
- 电源供电不足
- 命令发送间隔太短

**解决方法**：
- 检查舵机ID设置
- 增加电源容量
- 增加命令间隔时间

## 📋 **调试检查清单**

### **硬件检查**
- [ ] PA2/PA3连接正确 (TX↔RX)
- [ ] PB1按键连接正确
- [ ] 舵机ID设置为1和2
- [ ] 电源供电充足 (5V/3A+)
- [ ] 所有连接牢固可靠

### **软件检查**
- [ ] Key_Init()已调用
- [ ] Process_KeyCommand()在主循环中
- [ ] OLED显示复位过程信息
- [ ] 舵机命令格式正确
- [ ] 波特率设置为115200

### **功能检查**
- [ ] 串口屏0x03命令有效
- [ ] PB1按键触发复位
- [ ] OLED显示调试信息
- [ ] 舵机实际转动
- [ ] 角度显示更新

## 📞 **进一步调试建议**

如果以上方法都无效，建议：

1. **简化测试**：
   - 只测试一个舵机
   - 使用固定角度值
   - 减少命令复杂度

2. **硬件验证**：
   - 用万用表测试连接
   - 用示波器查看信号
   - 更换舵机测试

3. **软件验证**：
   - 添加更多调试输出
   - 简化复位函数
   - 单步调试代码

---

**调试版本**：V2.1  
**更新日期**：2024-07-18  
**主要改进**：添加PB1按键，增强调试功能，改进通信可靠性
