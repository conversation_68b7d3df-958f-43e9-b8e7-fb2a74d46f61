/**
 ******************************************************************************
 * @file    TIM.h
 * <AUTHOR> Name]
 * @version V1.0
 * @date    2025-01-31
 * @brief   STM32F407 TIM2定时器驱动头文件
 *          
 *          本文件定义了TIM2定时器的初始化和中断配置接口
 *          主要用于系统定时任务和周期性事件处理
 * 
 * @note    定时器配置:
 *          - 使用TIM2 (32位定时器)
 *          - 向上计数模式
 *          - 中断优先级: 抢占优先级1, 子优先级1
 *          - 中断处理函数: TIM2_IRQHandler (在stm32f4xx_it.c中定义)
 *          
 *          时间计算公式:
 *          定时周期(s) = ((arr+1) * (psc+1)) / 84MHz
 ******************************************************************************
 */

#ifndef __TIM_H
#define __TIM_H

#include "sys.h"

/* 函数声明 ------------------------------------------------------------------*/

/**
 * @brief  TIM2定时器初始化
 * @param  arr: 自动重装载值 (0-0xFFFFFFFF)
 * @param  psc: 预分频系数 (0-0xFFFF)
 * @retval None
 * @note   配置TIM2为定时中断模式，使能中断
 *         定时周期 = ((arr+1) * (psc+1)) / 84MHz
 *         例如: arr=199, psc=7199 -> 周期 = 200*7200/84MHz = 10ms
 *         中断处理函数需要在stm32f4xx_it.c中实现TIM2_IRQHandler
 */
void TIM2_Init(u16 arr, u16 psc);

#endif
