{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bin": {"rimraf": "bin.js"}, "dependencies": {"glob": "^7.1.3"}, "description": "A deep deletion module for node (like `rm -rf`)", "devDependencies": {"mkdirp": "^0.5.1", "tap": "^12.1.1"}, "files": ["LICENSE", "README.md", "bin.js", "rimraf.js"], "funding": {"url": "https://github.com/sponsors/isaacs"}, "homepage": "https://github.com/isaacs/rimraf#readme", "license": "ISC", "main": "rimraf.js", "name": "<PERSON><PERSON><PERSON>", "repository": {"type": "git", "url": "git://github.com/isaacs/rimraf.git"}, "version": "3.0.2"}