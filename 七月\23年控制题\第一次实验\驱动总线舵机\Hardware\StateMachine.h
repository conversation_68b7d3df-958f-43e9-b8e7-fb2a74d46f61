#ifndef __STATE_MACHINE_H
#define __STATE_MACHINE_H

#include "stdint.h"
#include "Geometry.h"
#include "Servo.h"
#include "Key.h"

// 激光云台系统状态定义
typedef enum {
    STATE_WAIT_POINT_A = 0,     // 等待记录A点
    STATE_WAIT_POINT_B = 1,     // 等待记录B点  
    STATE_AUTO_MOVING = 2       // 自动往返移动
} SystemState_t;

// 移动方向定义
typedef enum {
    DIRECTION_A_TO_B = 0,       // A点到B点
    DIRECTION_B_TO_A = 1        // B点到A点
} MoveDirection_t;

// 系统状态机结构体
typedef struct {
    SystemState_t current_state;        // 当前状态
    SystemState_t last_state;           // 上一个状态
    
    // 记录的点位信息
    WallPoint_t point_a;                // A点墙面坐标
    WallPoint_t point_b;                // B点墙面坐标
    ServoAngle_t servo_a;               // A点舵机角度
    ServoAngle_t servo_b;               // B点舵机角度
    uint8_t point_a_recorded;           // A点是否已记录
    uint8_t point_b_recorded;           // B点是否已记录
    
    // 自动移动控制
    PathInterpolation_t path;           // 路径插值对象
    MoveDirection_t move_direction;     // 当前移动方向
    uint8_t is_moving;                  // 是否正在移动
    uint32_t move_start_time;           // 移动开始时间
    uint32_t last_step_time;            // 上次步进时间
    
    // 系统状态
    uint8_t system_ready;               // 系统是否就绪
    uint8_t error_flag;                 // 错误标志
    uint32_t last_key_time;             // 上次按键时间(防抖)
} LaserGimbalState_t;

// 状态机错误代码
typedef enum {
    SM_OK = 0,
    SM_ERROR_NOT_READY,
    SM_ERROR_INVALID_STATE,
    SM_ERROR_POINT_NOT_RECORDED,
    SM_ERROR_SERVO_COMMUNICATION,
    SM_ERROR_GEOMETRY_CALCULATION
} StateMachineError_t;

// 状态机初始化和控制
void StateMachine_Init(LaserGimbalState_t* sm);
StateMachineError_t StateMachine_Update(LaserGimbalState_t* sm);
void StateMachine_Reset(LaserGimbalState_t* sm);

// 状态转换函数
StateMachineError_t StateMachine_HandleRecordKey(LaserGimbalState_t* sm);
StateMachineError_t StateMachine_HandleTriggerKey(LaserGimbalState_t* sm);
StateMachineError_t StateMachine_TransitionTo(LaserGimbalState_t* sm, SystemState_t new_state);

// 点位记录功能
StateMachineError_t StateMachine_RecordPointA(LaserGimbalState_t* sm);
StateMachineError_t StateMachine_RecordPointB(LaserGimbalState_t* sm);
StateMachineError_t StateMachine_ReadCurrentPosition(LaserGimbalState_t* sm, 
                                                   ServoAngle_t* servo_angle, 
                                                   WallPoint_t* wall_point);

// 自动移动控制
StateMachineError_t StateMachine_StartAutoMovement(LaserGimbalState_t* sm);
StateMachineError_t StateMachine_UpdateAutoMovement(LaserGimbalState_t* sm);
StateMachineError_t StateMachine_StopAutoMovement(LaserGimbalState_t* sm);
StateMachineError_t StateMachine_ToggleDirection(LaserGimbalState_t* sm);

// 舵机控制封装
StateMachineError_t StateMachine_SetServoTorque(uint8_t id, uint8_t enable);
StateMachineError_t StateMachine_MoveServoToPosition(ServoAngle_t target_angle, uint16_t time_ms);

// 状态查询函数
SystemState_t StateMachine_GetCurrentState(LaserGimbalState_t* sm);
uint8_t StateMachine_IsPointRecorded(LaserGimbalState_t* sm, uint8_t point_id);
uint8_t StateMachine_IsSystemReady(LaserGimbalState_t* sm);
uint8_t StateMachine_IsMoving(LaserGimbalState_t* sm);
float StateMachine_GetMoveProgress(LaserGimbalState_t* sm);

// 调试和状态显示
void StateMachine_PrintState(LaserGimbalState_t* sm);
const char* StateMachine_GetStateString(SystemState_t state);
const char* StateMachine_GetDirectionString(MoveDirection_t direction);

// 时间管理 (简单的毫秒计数器)
uint32_t StateMachine_GetTick(void);
uint8_t StateMachine_IsTimeout(uint32_t start_time, uint32_t timeout_ms);

#endif
