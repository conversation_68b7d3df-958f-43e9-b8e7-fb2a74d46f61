# STM32F407电机控制开发指南

## 一、项目概述

本指南详细介绍基于STM32F407ZGT6+D36A双路步进驱动的单轴瞄准系统电机控制开发流程。系统要求在2秒内完成目标识别、云台调整和激光发射。

### 硬件配置
- **主控**: STM32F407ZGT6
- **驱动器**: D36A双路步进电机驱动器（ATD5984芯片）
- **电机**: 42步进电机（1.8°步距角）
- **供电**: 7.4V DC
- **调试器**: CMSIS-DAP

### 核心需求
- 快速响应：2秒内完成瞄准
- 精确控制：激光准确命中靶心
- 单轴简化：仅控制水平轴

## 二、基础运动控制完善

### 2.1 速度控制实现

#### 梯形加减速算法
```c
typedef struct {
    uint32_t current_pos;      // 当前位置（步数）
    uint32_t target_pos;       // 目标位置（步数）
    uint32_t current_speed;    // 当前速度（Hz）
    uint32_t max_speed;        // 最大速度（Hz）
    uint32_t accel_rate;       // 加速度（Hz/s）
    uint32_t decel_point;      // 减速点位置
    uint8_t  motion_state;     // 运动状态
} MotorControl_t;

// 运动状态定义
#define MOTOR_IDLE      0
#define MOTOR_ACCEL     1
#define MOTOR_CONSTANT  2
#define MOTOR_DECEL     3
```

#### PWM频率动态调整
- TIM2配置为PWM输出模式
- 根据目标速度实时调整ARR和PSC寄存器
- 使用DMA减少CPU占用

#### 关键参数配置
```c
// D36A驱动器1/16细分时的参数
#define MICROSTEP_DIV     16
#define STEPS_PER_REV     (200 * MICROSTEP_DIV)  // 3200步/圈
#define MAX_SPEED_HZ      10000   // 最大10kHz
#define ACCEL_RATE        50000   // 50kHz/s加速度
#define ANGLE_PER_STEP    (360.0f / STEPS_PER_REV)  // 0.1125°/步
```

### 2.2 位置控制系统

#### 核心控制函数
```c
// 绝对位置控制
void Motor_MoveTo(float angle);

// 相对位置控制  
void Motor_MoveBy(float delta_angle);

// 速度模式控制
void Motor_SetSpeed(int16_t speed);

// 紧急停止
void Motor_EmergencyStop(void);

// 获取当前位置
float Motor_GetPosition(void);
```

#### 角度与步数转换
```c
// 角度转步数
uint32_t AngleToSteps(float angle) {
    return (uint32_t)(angle / ANGLE_PER_STEP);
}

// 步数转角度
float StepsToAngle(uint32_t steps) {
    return steps * ANGLE_PER_STEP;
}
```

### 2.3 中断驱动的运动控制

#### 定时器中断处理
```c
// TIM3用于运动控制状态机
void TIM3_IRQHandler(void) {
    if (TIM_GetITStatus(TIM3, TIM_IT_Update)) {
        // 更新运动状态
        Motor_UpdateMotion();
        
        // 清除中断标志
        TIM_ClearITPendingBit(TIM3, TIM_IT_Update);
    }
}
```

## 三、精度优化方案

### 3.1 细分驱动优化

#### 细分选择策略
- **1/16细分**: 平衡精度与速度的最佳选择
- **步进分辨率**: 0.1125°
- **拨码开关设置**: SW1=ON, SW2=OFF, SW3=OFF

#### 电流配置
- **推荐电流**: 0.77A（SW4=OFF, SW5=ON, SW6=OFF）
- **保持电流**: 设置为运行电流的50%以减少发热

### 3.2 位置精度保证

#### 软件位置追踪
```c
// 全局位置变量（原子操作）
volatile int32_t g_motor_position = 0;

// 步进脉冲计数（在PWM中断中调用）
void Motor_StepCounter(uint8_t direction) {
    if (direction == DIR_CW) {
        g_motor_position++;
    } else {
        g_motor_position--;
    }
}
```

#### 防累积误差机制
- 定期校准：每次回到机械零点时清零计数
- 软限位保护：防止超出机械运动范围
- 步数验证：移动完成后验证实际步数

### 3.3 响应优化

#### 快速启动策略
```c
// 小角度快速定位（无加减速）
void Motor_QuickMove(float angle) {
    if (fabs(angle) < 5.0f) {  // 5度以内
        // 直接以中速移动
        Motor_SetSpeed(MAX_SPEED_HZ / 2);
        // 计算步数并移动
        uint32_t steps = AngleToSteps(fabs(angle));
        Motor_RunSteps(steps, angle > 0 ? DIR_CW : DIR_CCW);
    }
}
```

## 四、与视觉系统的接口设计

### 4.1 通信接口

#### UART配置
```c
// USART2: PA2(TX), PA3(RX)
USART_InitTypeDef USART_InitStructure;
USART_InitStructure.USART_BaudRate = 115200;
USART_InitStructure.USART_WordLength = USART_WordLength_8b;
USART_InitStructure.USART_StopBits = USART_StopBits_1;
USART_InitStructure.USART_Parity = USART_Parity_No;
```

### 4.2 命令处理

#### 控制命令定义
```c
#define CMD_FAST_LEFT   0x01
#define CMD_SLOW_LEFT   0x02
#define CMD_STOP        0x00
#define CMD_SLOW_RIGHT  0x03
#define CMD_FAST_RIGHT  0x04
#define CMD_LASER_ON    0x10
#define CMD_LASER_ACK   0x11

// 速度映射
#define FAST_SPEED      (MAX_SPEED_HZ * 0.8f)
#define SLOW_SPEED      (MAX_SPEED_HZ * 0.3f)
```

### 4.3 激光器控制

#### GPIO配置
```c
// PC0作为激光器控制引脚
GPIO_InitTypeDef GPIO_InitStructure;
GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0;
GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
GPIO_InitStructure.GPIO_Speed = GPIO_Speed_2MHz;
GPIO_Init(GPIOC, &GPIO_InitStructure);

// 激光器控制宏
#define LASER_ON()   GPIO_SetBits(GPIOC, GPIO_Pin_0)
#define LASER_OFF()  GPIO_ResetBits(GPIOC, GPIO_Pin_0)
```

## 五、调试与测试

### 5.1 调试工具

#### 串口调试输出
```c
// 使用USART1作为调试端口
void Debug_Printf(const char* format, ...) {
    char buffer[128];
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    USART_SendString(USART1, buffer);
}
```

### 5.2 测试用例

#### 基础功能测试
1. **速度控制测试**: 不同速度下的稳定性
2. **位置精度测试**: 重复定位精度验证
3. **响应时间测试**: 命令到执行的延迟

#### 系统集成测试
1. **通信可靠性**: 高速命令流测试
2. **实时性验证**: 2秒内完成全流程
3. **精度验证**: 激光命中率统计

### 5.3 常见问题与解决

#### 问题1: 电机抖动
- **原因**: 主循环有阻塞操作
- **解决**: 使用DMA和中断，避免轮询

#### 问题2: 位置偏差
- **原因**: 加减速过程丢步
- **解决**: 降低加速度，增加电流

#### 问题3: 响应延迟
- **原因**: 通信或处理延迟
- **解决**: 提高中断优先级，优化代码

## 六、优化建议

### 6.1 性能优化
- 使用DMA进行UART收发
- PWM使用硬件定时器
- 关键代码使用内联函数

### 6.2 可靠性提升
- 添加看门狗定时器
- 通信数据校验
- 异常状态恢复机制

### 6.3 后续改进方向
- 添加编码器反馈
- 实现S曲线加减速
- 双轴协同控制预留

## 七、快速开发检查清单

- [ ] PWM输出配置完成
- [ ] 加减速算法实现
- [ ] 位置控制接口完成
- [ ] UART通信测试通过
- [ ] 激光器控制正常
- [ ] 与K230通信协议匹配
- [ ] 2秒响应时间达标
- [ ] 定位精度满足要求