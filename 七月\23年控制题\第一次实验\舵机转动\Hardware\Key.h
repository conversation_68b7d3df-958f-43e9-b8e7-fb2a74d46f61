#ifndef __KEY_H
#define __KEY_H

#include "stdint.h"

// 激光云台项目按键定义
#define KEY_RECORD_PIN      GPIO_Pin_0    // PB0 - 记录点位按键
#define KEY_TRIGGER_PIN     GPIO_Pin_1    // PB1 - 触发移动按键
#define KEY_PORT            GPIOB
#define KEY_RCC             RCC_APB2Periph_GPIOB

// 按键ID定义
typedef enum {
    KEY_NONE = 0,
    KEY_RECORD = 1,     // PB0 - 记录A/B点位
    KEY_TRIGGER = 2     // PB1 - 触发自动往返移动
} KeyID_t;

// 按键事件定义
typedef enum {
    KEY_EVENT_NONE = 0,
    KEY_EVENT_PRESS,    // 按键按下事件
    KEY_EVENT_RELEASE,  // 按键释放事件
    KEY_EVENT_CLICK     // 按键点击事件(按下后释放)
} KeyEvent_t;

// 按键状态结构体
typedef struct {
    uint8_t current_state;      // 当前物理状态 (0=按下, 1=释放)
    uint8_t last_state;         // 上次状态
    uint8_t stable_state;       // 消抖后的稳定状态
    uint16_t debounce_counter;  // 消抖计数器
    uint8_t press_flag;         // 按下标志
    uint8_t release_flag;       // 释放标志
    uint8_t click_flag;         // 点击标志
} KeyState_t;

// 按键配置参数
#define KEY_DEBOUNCE_TIME   20      // 消抖时间 (ms)
#define KEY_SCAN_INTERVAL   5       // 扫描间隔 (ms)

// 按键初始化和基础功能
void Key_Init(void);
void Key_Scan(void);                           // 按键扫描 (需要定时调用)
uint8_t Key_GetNum(void);                      // 兼容原有接口

// 激光云台项目专用接口
KeyEvent_t Key_GetEvent(KeyID_t key_id);       // 获取按键事件
uint8_t Key_IsPressed(KeyID_t key_id);         // 检查按键是否按下
uint8_t Key_IsReleased(KeyID_t key_id);        // 检查按键是否释放
uint8_t Key_IsClicked(KeyID_t key_id);         // 检查按键是否被点击
void Key_ClearEvent(KeyID_t key_id);           // 清除按键事件标志

// 内部函数
void Key_UpdateState(KeyID_t key_id);
uint8_t Key_ReadPin(KeyID_t key_id);

#endif
