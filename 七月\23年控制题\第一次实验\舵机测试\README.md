# STM32F407ZGT6 + HTS-25L舵机控制工程

> **项目名称**: HTS25L_ServoControl  
> **创建日期**: 2025年7月17日  
> **开发平台**: Keil MDK-ARM 5 + STM32F407ZGT6  
> **目标功能**: 按键控制总线舵机30度步进旋转  

---

## 🎯 项目功能

### 核心功能
- **按键触发**: 板载按键K1(PE3)每按一次舵机增加30°
- **循环运动**: 0°→30°→60°→...→240°→0°循环
- **运动控制**: 动作时间600ms，等待700ms后允许下次按键
- **状态指示**: PF9心跳闪烁，PF10运动期间点亮

### 技术参数
- **MCU**: STM32F407ZGT6，系统时钟168MHz
- **舵机**: HTS-25L总线舵机，ID=1
- **通信**: USART2半双工，PA2引脚，115200bps
- **供电**: 舵机7.4V独立供电，与MCU共地

---

## 🏗️ 工程结构

```
HTS25L_ServoControl/
├── User/                   # 用户代码
│   ├── main.c             # 主程序
│   ├── stm32f4xx_conf.h   # 外设库配置
│   ├── stm32f4xx_it.c     # 中断服务程序
│   └── stm32f4xx_it.h     # 中断头文件
├── BSP/                    # 板级支持包
│   ├── bsp_led.c/h        # LED驱动
│   ├── bsp_key.c/h        # 按键驱动
│   └── bsp_usart.c/h      # USART驱动
├── Driver/                 # 设备驱动
│   └── hts25l_driver.c/h  # HTS-25L舵机驱动
├── App/                    # 应用层
│   └── app_servo.c/h      # 舵机应用逻辑
├── System/                 # 系统文件
│   └── system_stm32f4xx.c/h # 系统初始化
├── Startup/                # 启动文件
│   └── startup_stm32f40_41xxx.s
└── Library/                # STM32标准外设库
    └── (需要添加SPL库文件)
```

---

## 🔧 Keil工程配置

### 1. 添加STM32标准外设库

**步骤**：
1. 从STM32F4xx_StdPeriph_Lib_V1.8.0中复制以下文件到`Library/`目录：
   ```
   stm32f4xx_rcc.c/h
   stm32f4xx_gpio.c/h
   stm32f4xx_usart.c/h
   stm32f4xx_flash.c/h
   misc.c/h
   stm32f4xx.h
   ```

2. 从CMSIS复制以下文件到`Startup/`目录：
   ```
   startup_stm32f40_41xxx.s
   core_cm4.h
   core_cmFunc.h
   core_cmInstr.h
   core_cmSimd.h
   ```

### 2. 工程配置要点

**Target设置**：
- Device: STM32F407ZGTx
- Use MicroLIB: 不勾选
- Code Generation: ARM Compiler 5

**C/C++设置**：
- Optimization: Level 0 (调试时)
- Define: `STM32F40_41xxx,USE_STDPERIPH_DRIVER`
- Include Paths: `.\User;.\BSP;.\Driver;.\App;.\System;.\Library`

**Debug设置**：
- Use: ST-Link Debugger
- Port: SW (SWD)
- Flash Download: 勾选所有选项

---

## 📡 硬件连接

### 引脚分配
| 功能 | STM32引脚 | 连接说明 |
|------|-----------|----------|
| 舵机通信 | PA2 | 连接HTS-25L的SIG引脚 |
| 按键K1 | PE3 | 板载按键，按下为低电平 |
| LED心跳 | PF9 | 板载LED0，低电平点亮 |
| LED运动 | PF10 | 板载LED1，低电平点亮 |

### 电源连接
- **舵机供电**: 7.4V独立电源（2S 18650电池）
- **共地连接**: 舵机GND与STM32 GND相连
- **信号连接**: 舵机SIG与STM32 PA2相连

---

## 🚀 使用说明

### 1. 编译下载
1. 打开Keil工程文件`HTS25L_ServoControl.uvprojx`
2. 添加STM32标准外设库文件（见配置说明）
3. 编译工程（Build Project）
4. 连接ST-Link下载器
5. 下载程序到开发板

### 2. 运行测试
1. 上电后LED0开始心跳闪烁
2. 舵机自动初始化并回到0度位置
3. 按下K1按键，舵机旋转30度，LED1点亮600ms
4. 等待700ms后可再次按键
5. 重复按键直到240度后自动回到0度

### 3. 状态指示
- **LED0慢闪**: 系统正常运行（心跳）
- **LED1点亮**: 舵机正在运动
- **LED0快闪**: 通信异常或系统错误

---

## 🔍 调试说明

### 常见问题
1. **舵机无响应**
   - 检查7.4V供电是否正常
   - 确认共地连接
   - 验证PA2与舵机SIG连接

2. **按键无反应**
   - 检查PE3引脚配置
   - 确认按键消抖时间设置

3. **通信错误**
   - 验证波特率115200设置
   - 检查半双工模式配置
   - 确认协议帧格式

### 调试工具
- 可使用USART1(PA9/PA10)添加调试输出
- 利用LED状态判断系统运行状态
- ST-Link调试器单步调试

---

## 📋 技术特点

### 软件架构
- **分层设计**: BSP→Driver→App清晰分层
- **状态机控制**: 空闲→运动→等待状态管理
- **非阻塞设计**: SysTick定时器驱动
- **错误处理**: 通信超时和异常保护

### 协议实现
- **HTS-25L协议**: 完整实现总线舵机通信协议
- **校验机制**: 数据包校验和验证
- **半双工通信**: 发送后自动切换接收状态

---

## 📞 技术支持

### 开发环境
- **IDE**: Keil MDK-ARM 5.06+
- **编译器**: ARM Compiler 5
- **调试器**: ST-Link V2/V3
- **库版本**: STM32F4xx_StdPeriph_Lib_V1.8.0

### 联系信息
- **开发者**: Contest Analysis Expert
- **项目地址**: C:\Users\<USER>\Desktop\七月\23年控制题\第一次实验\舵机测试
- **参考文档**: 总线舵机开发文档.md

---

**项目完成！按照说明配置Keil工程即可编译运行。** 🎯
