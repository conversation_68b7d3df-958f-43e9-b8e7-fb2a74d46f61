#include "servo.h"
#include "usart.h"
#include "Delay.h"
#include <stddef.h>  // 添加NULL定义

void Servo_Init(void) {
    USART1_Init();
    Delay_ms(100);
}

uint16_t Servo_AngleToPosition(float angle) {
    if (angle < 0) angle = 0;
    if (angle > SERVO_MAX_ANGLE) angle = SERVO_MAX_ANGLE;
    return (uint16_t)(angle * SERVO_MAX_POSITION / SERVO_MAX_ANGLE);
}

float Servo_PositionToAngle(uint16_t position) {
    if (position > SERVO_MAX_POSITION) position = SERVO_MAX_POSITION;
    return (float)position * SERVO_MAX_ANGLE / SERVO_MAX_POSITION;
}

uint8_t Servo_CalculateChecksum(uint8_t* data, uint8_t length) {
    uint16_t sum = 0;
    for (uint8_t i = 0; i < length; i++) {
        sum += data[i];
    }
    return (~sum) & 0xFF;
}

void Servo_SendCommand(uint8_t id, uint8_t cmd, uint8_t* params, uint8_t param_len) {
    uint8_t buffer[16];
    uint8_t index = 0;

    // 帧头
    buffer[index++] = SERVO_HEADER;
    buffer[index++] = SERVO_HEADER;

    buffer[index++] = id;
    buffer[index++] = param_len + 3;   // Length = Cmd + Params + Checksum（长度字段本身不参与计算）

    buffer[index++] = cmd;

    // 参数部分
    for (uint8_t i = 0; i < param_len; i++) {
        buffer[index++] = params[i];
    }

    // 校验和（不包含帧头）
    uint8_t checksum = Servo_CalculateChecksum(&buffer[2], param_len + 3);
    buffer[index++] = checksum;

    USART1_SendBuffer(buffer, index);
    Delay_ms(10);
}

void Servo_SetPosition(uint8_t id, float angle) {
    Servo_SetPositionWithTime(id, angle, 1000);  // 默认1000ms
}

void Servo_SetPositionWithTime(uint8_t id, float angle, uint16_t time_ms) {
    uint16_t pos = Servo_AngleToPosition(angle);
    uint8_t params[4];

    params[0] = pos & 0xFF;
    params[1] = (pos >> 8) & 0xFF;
    params[2] = time_ms & 0xFF;
    params[3] = (time_ms >> 8) & 0xFF;

    Servo_SendCommand(id, SERVO_CMD_MOVE_TIME_WRITE, params, 4);
}

/**
 * 接收舵机响应数据
 * @param id 舵机ID
 * @param cmd 期望的命令
 * @param data 接收数据缓冲区
 * @param data_len 接收数据长度指针
 * @return 错误代码
 */
ServoError_t Servo_ReceiveResponse(uint8_t id, uint8_t cmd, uint8_t* data, uint8_t* data_len) {
    uint8_t buffer[16];
    uint8_t index = 0;
    uint32_t timeout = SERVO_TIMEOUT_MS * 1000; // 转换为微秒级延时计数

    // 等待接收数据
    while (timeout > 0) {
        if (USART_GetFlagStatus(USART1, USART_FLAG_RXNE) == SET) {
            buffer[index] = USART_ReceiveData(USART1);
            index++;

            // 检查是否接收到完整帧头
            if (index >= 2 && buffer[0] == SERVO_HEADER && buffer[1] == SERVO_HEADER) {
                // 继续接收ID和长度
                if (index >= 4) {
                    uint8_t recv_id = buffer[2];
                    uint8_t length = buffer[3];

                    // 检查ID是否匹配
                    if (recv_id != id) {
                        index = 0; // 重新开始
                        continue;
                    }

                    // 接收剩余数据
                    uint8_t total_len = length + 4; // 包含帧头和ID、长度
                    while (index < total_len && timeout > 0) {
                        if (USART_GetFlagStatus(USART1, USART_FLAG_RXNE) == SET) {
                            buffer[index] = USART_ReceiveData(USART1);
                            index++;
                        }
                        timeout--;
                        for (volatile int i = 0; i < 10; i++); // 微延时
                    }

                    if (index >= total_len) {
                        // 验证校验和
                        uint8_t checksum = Servo_CalculateChecksum(&buffer[2], length - 1);
                        if (checksum == buffer[total_len - 1]) {
                            // 检查命令是否匹配
                            if (buffer[4] == cmd) {
                                // 提取数据部分
                                *data_len = length - 3; // 减去命令和校验和
                                for (uint8_t i = 0; i < *data_len; i++) {
                                    data[i] = buffer[5 + i];
                                }
                                return SERVO_OK;
                            }
                        } else {
                            return SERVO_ERROR_CHECKSUM;
                        }
                    }
                    break;
                }
            }
        }
        timeout--;
        for (volatile int i = 0; i < 10; i++); // 微延时
    }

    return SERVO_ERROR_TIMEOUT;
}

/**
 * 读取舵机当前角度 - 激光云台项目核心功能
 * @param id 舵机ID (1=水平Pan, 2=垂直Tilt)
 * @param angle 返回的角度值指针
 * @return 错误代码
 */
ServoError_t Servo_ReadPosition(uint8_t id, float* angle) {
    uint8_t data[8];
    uint8_t data_len;

    // 发送读取位置命令
    Servo_SendCommand(id, SERVO_CMD_POS_READ, NULL, 0);

    // 接收响应
    ServoError_t result = Servo_ReceiveResponse(id, SERVO_CMD_POS_READ, data, &data_len);
    if (result == SERVO_OK && data_len >= 2) {
        // 解析位置数据 (小端格式)
        uint16_t position = data[0] | (data[1] << 8);
        *angle = Servo_PositionToAngle(position);
        return SERVO_OK;
    }

    return result;
}

/**
 * 设置舵机扭矩状态 - 激光云台项目核心功能
 * @param id 舵机ID
 * @param enable 1=加载(有扭矩), 0=卸载(无扭矩,可手动调整)
 * @return 错误代码
 */
ServoError_t Servo_SetTorqueEnable(uint8_t id, uint8_t enable) {
    uint8_t params[1];
    params[0] = enable ? SERVO_LOAD_STATE : SERVO_UNLOAD_STATE;

    Servo_SendCommand(id, SERVO_CMD_LOAD_OR_UNLOAD_WRITE, params, 1);

    // 等待命令执行
    Delay_ms(50);

    return SERVO_OK;
}

/**
 * 获取舵机完整状态信息
 * @param id 舵机ID
 * @param status 状态结构体指针
 * @return 错误代码
 */
ServoError_t Servo_GetStatus(uint8_t id, ServoStatus_t* status) {
    ServoError_t result;

    // 读取位置
    result = Servo_ReadPosition(id, &status->angle);
    if (result != SERVO_OK) return result;
    status->position = Servo_AngleToPosition(status->angle);

    // 读取温度
    result = Servo_ReadTemperature(id, &status->temperature);
    if (result != SERVO_OK) return result;

    // 读取电压
    result = Servo_ReadVoltage(id, &status->voltage);
    if (result != SERVO_OK) return result;

    // 读取加载状态
    uint8_t data[8];
    uint8_t data_len;
    Servo_SendCommand(id, SERVO_CMD_LOAD_OR_UNLOAD_READ, NULL, 0);
    result = Servo_ReceiveResponse(id, SERVO_CMD_LOAD_OR_UNLOAD_READ, data, &data_len);
    if (result == SERVO_OK && data_len >= 1) {
        status->load_state = data[0];
    }

    return SERVO_OK;
}

/**
 * 读取舵机温度
 * @param id 舵机ID
 * @param temperature 温度指针 (°C)
 * @return 错误代码
 */
ServoError_t Servo_ReadTemperature(uint8_t id, uint8_t* temperature) {
    uint8_t data[8];
    uint8_t data_len;

    Servo_SendCommand(id, SERVO_CMD_TEMP_READ, NULL, 0);
    ServoError_t result = Servo_ReceiveResponse(id, SERVO_CMD_TEMP_READ, data, &data_len);

    if (result == SERVO_OK && data_len >= 1) {
        *temperature = data[0];
        return SERVO_OK;
    }

    return result;
}

/**
 * 读取舵机电压
 * @param id 舵机ID
 * @param voltage 电压指针 (mV)
 * @return 错误代码
 */
ServoError_t Servo_ReadVoltage(uint8_t id, uint16_t* voltage) {
    uint8_t data[8];
    uint8_t data_len;

    Servo_SendCommand(id, SERVO_CMD_VIN_READ, NULL, 0);
    ServoError_t result = Servo_ReceiveResponse(id, SERVO_CMD_VIN_READ, data, &data_len);

    if (result == SERVO_OK && data_len >= 2) {
        *voltage = data[0] | (data[1] << 8);
        return SERVO_OK;
    }

    return result;
}
