#include "ManualRecord.h"
#include "Key.h"
#include "LED.h"
#include "OLED.h"
#include "Delay.h"
#include <stdio.h>
#include <stddef.h>
#include <math.h>

// 全局点位数据存储
static WallPoint_t saved_points[3] = {0};      // [0]保留, [1]A点, [2]B点
static ServoAngle_t saved_servos[3] = {0};     // [0]保留, [1]A点, [2]B点
static uint8_t point_saved_flags[3] = {0};     // 点位保存标志

/**
 * 初始化手动记录过程
 * @param process 记录过程结构体指针
 */
void ManualRecord_Init(ManualRecordProcess_t* process)
{
    if (process == NULL) return;
    
    process->current_state = RECORD_STATE_IDLE;
    process->point_id = 0;
    process->state_start_time = 0;
    process->manual_start_time = 0;
    process->retry_count = 0;
    process->user_confirmed = 0;
    
    // 初始化结果
    process->result.success = 0;
    process->result.wall_point.x = 0.0f;
    process->result.wall_point.y = 0.0f;
    process->result.servo_angle.pan = 120.0f;
    process->result.servo_angle.tilt = 120.0f;
    process->result.record_time = 0;
    process->result.point_id = 0;
    process->result.final_state = RECORD_STATE_IDLE;
}

/**
 * 开始手动记录过程 - 激光云台核心功能
 * @param process 记录过程结构体指针
 * @param point_id 点位ID (1=A点, 2=B点)
 * @return 错误代码
 */
RecordError_t ManualRecord_Start(ManualRecordProcess_t* process, uint8_t point_id)
{
    if (process == NULL || point_id < 1 || point_id > 2) {
        return RECORD_ERROR_INVALID_PARAM;
    }
    
    // 检查系统是否忙碌
    if (process->current_state != RECORD_STATE_IDLE) {
        return RECORD_ERROR_SYSTEM_BUSY;
    }
    
    // 初始化记录过程
    ManualRecord_Init(process);
    process->point_id = point_id;
    process->result.point_id = point_id;
    
    // 显示开始信息
    ManualRecord_ShowInstructions(point_id);
    
    // 转换到卸载状态
    return ManualRecord_TransitionTo(process, RECORD_STATE_UNLOADING);
}

/**
 * 更新手动记录过程 - 状态机核心
 * @param process 记录过程结构体指针
 * @return 错误代码
 */
RecordError_t ManualRecord_Update(ManualRecordProcess_t* process)
{
    if (process == NULL) return RECORD_ERROR_INVALID_PARAM;
    
    RecordError_t error = RECORD_OK;
    
    switch (process->current_state) {
        case RECORD_STATE_IDLE:
            // 空闲状态，无需处理
            break;
            
        case RECORD_STATE_UNLOADING:
            // 卸载舵机
            if (Timer_IsTimeout(process->state_start_time, RECORD_UNLOAD_DELAY_MS)) {
                error = ManualRecord_UnloadServos();
                if (error == RECORD_OK) {
                    error = ManualRecord_TransitionTo(process, RECORD_STATE_MANUAL_ADJUST);
                }
            }
            break;
            
        case RECORD_STATE_MANUAL_ADJUST:
            // 等待用户手动调整
            if (ManualRecord_WaitUserAdjustment(process)) {
                error = ManualRecord_TransitionTo(process, RECORD_STATE_READING);
            } else if (Timer_IsTimeout(process->manual_start_time, RECORD_MANUAL_TIMEOUT_MS)) {
                error = RECORD_ERROR_TIMEOUT;
            }
            break;
            
        case RECORD_STATE_READING:
            // 读取当前位置
            error = ManualRecord_ReadCurrentPosition(&process->result.servo_angle, 
                                                   &process->result.wall_point);
            if (error == RECORD_OK) {
                // 验证位置有效性
                error = ManualRecord_ValidatePosition(process->result.wall_point);
                if (error == RECORD_OK) {
                    error = ManualRecord_TransitionTo(process, RECORD_STATE_LOADING);
                } else {
                    // 位置无效，重新调整
                    process->retry_count++;
                    if (process->retry_count < RECORD_READ_RETRY_COUNT) {
                        error = ManualRecord_TransitionTo(process, RECORD_STATE_MANUAL_ADJUST);
                    } else {
                        error = RECORD_ERROR_GEOMETRY_CALC;
                    }
                }
            }
            break;
            
        case RECORD_STATE_LOADING:
            // 重新加载舵机
            if (Timer_IsTimeout(process->state_start_time, RECORD_LOAD_DELAY_MS)) {
                error = ManualRecord_LoadServos();
                if (error == RECORD_OK) {
                    // 保存数据
                    error = ManualRecord_SavePointData(process->point_id, 
                                                     process->result.wall_point,
                                                     process->result.servo_angle);
                    if (error == RECORD_OK) {
                        error = ManualRecord_TransitionTo(process, RECORD_STATE_COMPLETE);
                    }
                }
            }
            break;
            
        case RECORD_STATE_COMPLETE:
            // 记录完成
            process->result.success = 1;
            process->result.record_time = Timer_GetTick();
            break;
            
        case RECORD_STATE_ERROR:
            // 错误状态
            process->result.success = 0;
            break;
            
        default:
            error = RECORD_ERROR_INVALID_PARAM;
            break;
    }
    
    // 处理错误
    if (error != RECORD_OK) {
        ManualRecord_TransitionTo(process, RECORD_STATE_ERROR);
        process->result.success = 0;
    }
    
    // 更新进度显示
    ManualRecord_ShowProgress(process);
    
    return error;
}

/**
 * 状态转换函数
 * @param process 记录过程结构体指针
 * @param new_state 新状态
 * @return 错误代码
 */
RecordError_t ManualRecord_TransitionTo(ManualRecordProcess_t* process, RecordState_t new_state)
{
    if (process == NULL) return RECORD_ERROR_INVALID_PARAM;
    
    process->current_state = new_state;
    process->state_start_time = Timer_GetTick();
    process->result.final_state = new_state;
    
    // 状态进入处理
    switch (new_state) {
        case RECORD_STATE_UNLOADING:
            LED_ON();  // 指示开始记录
            break;
            
        case RECORD_STATE_MANUAL_ADJUST:
            process->manual_start_time = Timer_GetTick();
            process->user_confirmed = 0;
            LED_OFF();
            break;
            
        case RECORD_STATE_READING:
            LED_ON();  // 指示正在读取
            break;
            
        case RECORD_STATE_LOADING:
            break;
            
        case RECORD_STATE_COMPLETE:
            LED_OFF();
            // 成功指示 - LED快速闪烁3次
            for (int i = 0; i < 3; i++) {
                LED_ON();
                Delay_ms(100);
                LED_OFF();
                Delay_ms(100);
            }
            break;
            
        case RECORD_STATE_ERROR:
            LED_OFF();
            // 错误指示 - LED长亮2秒
            LED_ON();
            Delay_ms(2000);
            LED_OFF();
            break;
            
        default:
            break;
    }
    
    return RECORD_OK;
}

/**
 * 卸载舵机 - 允许手动调整
 * @return 错误代码
 */
RecordError_t ManualRecord_UnloadServos(void)
{
    ServoError_t error1 = Servo_SetTorqueEnable(SERVO_PAN_ID, 0);
    ServoError_t error2 = Servo_SetTorqueEnable(SERVO_TILT_ID, 0);
    
    if (error1 != SERVO_OK || error2 != SERVO_OK) {
        return RECORD_ERROR_SERVO_COMM;
    }
    
    return RECORD_OK;
}

/**
 * 加载舵机 - 锁定位置
 * @return 错误代码
 */
RecordError_t ManualRecord_LoadServos(void)
{
    ServoError_t error1 = Servo_SetTorqueEnable(SERVO_PAN_ID, 1);
    ServoError_t error2 = Servo_SetTorqueEnable(SERVO_TILT_ID, 1);
    
    if (error1 != SERVO_OK || error2 != SERVO_OK) {
        return RECORD_ERROR_SERVO_COMM;
    }
    
    // 等待舵机稳定
    Delay_ms(RECORD_STABILIZE_DELAY_MS);
    
    return RECORD_OK;
}

/**
 * 读取当前位置
 * @param servo_angle 输出舵机角度
 * @param wall_point 输出墙面坐标
 * @return 错误代码
 */
RecordError_t ManualRecord_ReadCurrentPosition(ServoAngle_t* servo_angle, WallPoint_t* wall_point)
{
    if (servo_angle == NULL || wall_point == NULL) {
        return RECORD_ERROR_INVALID_PARAM;
    }
    
    // 读取舵机角度
    ServoError_t error1 = Servo_ReadPosition(SERVO_PAN_ID, &servo_angle->pan);
    ServoError_t error2 = Servo_ReadPosition(SERVO_TILT_ID, &servo_angle->tilt);
    
    if (error1 != SERVO_OK || error2 != SERVO_OK) {
        return RECORD_ERROR_SERVO_COMM;
    }
    
    // 转换为墙面坐标
    GeometryError_t geo_error = Geometry_ServoToWall(*servo_angle, wall_point);
    if (geo_error != GEOMETRY_OK) {
        return RECORD_ERROR_GEOMETRY_CALC;
    }
    
    return RECORD_OK;
}

/**
 * 验证位置有效性
 * @param wall_point 墙面坐标
 * @return 错误代码
 */
RecordError_t ManualRecord_ValidatePosition(WallPoint_t wall_point)
{
    if (!Geometry_IsWallPointValid(wall_point)) {
        return RECORD_ERROR_GEOMETRY_CALC;
    }
    
    if (!ManualRecord_IsPositionReasonable(wall_point)) {
        return RECORD_ERROR_GEOMETRY_CALC;
    }
    
    return RECORD_OK;
}

/**
 * 等待用户手动调整
 * @param process 记录过程结构体指针
 * @return 1=用户确认完成, 0=继续等待
 */
uint8_t ManualRecord_WaitUserAdjustment(ManualRecordProcess_t* process)
{
    if (process == NULL) return 0;

    // 检查用户确认按键 (再次按下记录键确认)
    if (Key_IsClicked(KEY_RECORD)) {
        Key_ClearEvent(KEY_RECORD);
        return 1;  // 用户确认完成调整
    }

    // 检查取消按键 (按下触发键取消)
    if (Key_IsClicked(KEY_TRIGGER)) {
        Key_ClearEvent(KEY_TRIGGER);
        process->current_state = RECORD_STATE_ERROR;
        return 0;  // 用户取消
    }

    return 0;  // 继续等待
}

/**
 * 显示操作指引
 * @param point_id 点位ID
 */
void ManualRecord_ShowInstructions(uint8_t point_id)
{
    char point_name = (point_id == 1) ? 'A' : 'B';

    OLED_ShowString(1, 1, "Record Point");

    char str[16];
    sprintf(str, "Point %c", point_name);
    OLED_ShowString(1, 13, str);

    OLED_ShowString(2, 1, "1.Unloading...");
    OLED_ShowString(3, 1, "2.Manual adjust");
    OLED_ShowString(4, 1, "3.Press PB0");
}

/**
 * 显示记录进度
 * @param process 记录过程结构体指针
 */
void ManualRecord_ShowProgress(ManualRecordProcess_t* process)
{
    if (process == NULL) return;

    const char* state_str = ManualRecord_GetStateString(process->current_state);

    char progress_str[16];
    sprintf(progress_str, "S:%s", state_str);
    OLED_ShowString(2, 1, progress_str);

    // 显示当前位置 (如果在手动调整状态)
    if (process->current_state == RECORD_STATE_MANUAL_ADJUST) {
        // 尝试读取当前位置用于显示
        ServoAngle_t current_servo;
        WallPoint_t current_wall;

        if (ManualRecord_ReadCurrentPosition(&current_servo, &current_wall) == RECORD_OK) {
            char pos_str[16];
            sprintf(pos_str, "X:%.0f Y:%.0f", current_wall.x, current_wall.y);
            OLED_ShowString(3, 1, pos_str);

            sprintf(pos_str, "P:%.1f T:%.1f", current_servo.pan, current_servo.tilt);
            OLED_ShowString(4, 1, pos_str);
        }
    }
}

/**
 * 保存点位数据
 * @param point_id 点位ID
 * @param wall_point 墙面坐标
 * @param servo_angle 舵机角度
 * @return 错误代码
 */
RecordError_t ManualRecord_SavePointData(uint8_t point_id, WallPoint_t wall_point, ServoAngle_t servo_angle)
{
    if (point_id < 1 || point_id > 2) {
        return RECORD_ERROR_INVALID_PARAM;
    }

    saved_points[point_id] = wall_point;
    saved_servos[point_id] = servo_angle;
    point_saved_flags[point_id] = 1;

    return RECORD_OK;
}

/**
 * 加载点位数据
 * @param point_id 点位ID
 * @param wall_point 输出墙面坐标
 * @param servo_angle 输出舵机角度
 * @return 错误代码
 */
RecordError_t ManualRecord_LoadPointData(uint8_t point_id, WallPoint_t* wall_point, ServoAngle_t* servo_angle)
{
    if (point_id < 1 || point_id > 2 || wall_point == NULL || servo_angle == NULL) {
        return RECORD_ERROR_INVALID_PARAM;
    }

    if (!point_saved_flags[point_id]) {
        return RECORD_ERROR_INVALID_PARAM;  // 数据未保存
    }

    *wall_point = saved_points[point_id];
    *servo_angle = saved_servos[point_id];

    return RECORD_OK;
}

/**
 * 检查点位是否已保存
 * @param point_id 点位ID
 * @return 1=已保存, 0=未保存
 */
uint8_t ManualRecord_IsPointSaved(uint8_t point_id)
{
    if (point_id < 1 || point_id > 2) return 0;
    return point_saved_flags[point_id];
}

/**
 * 检查位置是否合理
 * @param wall_point 墙面坐标
 * @return 1=合理, 0=不合理
 */
uint8_t ManualRecord_IsPositionReasonable(WallPoint_t wall_point)
{
    // 检查坐标范围 (比标准范围稍微严格一些)
    if (fabsf(wall_point.x) > 1500.0f || fabsf(wall_point.y) > 1500.0f) {
        return 0;
    }

    // 检查是否在原点附近 (可能是错误读取)
    float distance = sqrtf(wall_point.x * wall_point.x + wall_point.y * wall_point.y);
    if (distance < 50.0f) {
        return 0;  // 太接近原点，可能是错误
    }

    return 1;
}

/**
 * 取消记录过程
 * @param process 记录过程结构体指针
 * @return 错误代码
 */
RecordError_t ManualRecord_Cancel(ManualRecordProcess_t* process)
{
    if (process == NULL) return RECORD_ERROR_INVALID_PARAM;

    // 如果舵机已卸载，重新加载
    if (process->current_state == RECORD_STATE_MANUAL_ADJUST) {
        ManualRecord_LoadServos();
    }

    // 转换到错误状态
    ManualRecord_TransitionTo(process, RECORD_STATE_ERROR);
    process->result.success = 0;

    return RECORD_OK;
}

/**
 * 检查记录是否完成
 * @param process 记录过程结构体指针
 * @return 1=完成, 0=未完成
 */
uint8_t ManualRecord_IsComplete(ManualRecordProcess_t* process)
{
    if (process == NULL) return 0;
    return (process->current_state == RECORD_STATE_COMPLETE ||
            process->current_state == RECORD_STATE_ERROR);
}

/**
 * 获取记录结果
 * @param process 记录过程结构体指针
 * @return 记录结果
 */
RecordResult_t ManualRecord_GetResult(ManualRecordProcess_t* process)
{
    if (process == NULL) {
        RecordResult_t empty_result = {0};
        return empty_result;
    }

    return process->result;
}

/**
 * 获取状态字符串
 * @param state 状态
 * @return 状态字符串
 */
const char* ManualRecord_GetStateString(RecordState_t state)
{
    switch (state) {
        case RECORD_STATE_IDLE:         return "Idle";
        case RECORD_STATE_UNLOADING:    return "Unload";
        case RECORD_STATE_MANUAL_ADJUST: return "Adjust";
        case RECORD_STATE_READING:      return "Read";
        case RECORD_STATE_LOADING:      return "Load";
        case RECORD_STATE_COMPLETE:     return "Done";
        case RECORD_STATE_ERROR:        return "Error";
        default:                        return "Unknown";
    }
}
