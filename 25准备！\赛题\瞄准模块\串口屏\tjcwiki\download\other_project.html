<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>网友提供应用样例 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="游戏工程" href="tjc_game.html" />
    <link rel="prev" title="UI样例工程" href="ui_demo.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">资料下载</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="usart_hmi.html">上位机下载</a></li>
<li class="toctree-l2"><a class="reference internal" href="development_doc.html">开发文档下载</a></li>
<li class="toctree-l2"><a class="reference internal" href="tools_download.html">常用工具下载</a></li>
<li class="toctree-l2"><a class="reference internal" href="default_project.html">标准出厂工程样例</a></li>
<li class="toctree-l2"><a class="reference internal" href="moreProject/index.html">官方样例工程</a></li>
<li class="toctree-l2"><a class="reference internal" href="ui_demo.html">UI样例工程</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">网友提供应用样例</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#id2">多功能测量仪</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id4">全键盘输入、密码登录</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id6">简易计算器</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id8">打兔子，无需单片机支持</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id10">电压数据采集及系统参数设置</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id12">串口屏控制3路继电器</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id14">九宫格全英文输入（支持串口打印字符）</a></li>
<li class="toctree-l3"><a class="reference internal" href="#loading">酷炫Loading进度条界面</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id17">86面板智能家居</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id19">简洁圆形数字键盘（支持全局输入）</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id21">多种控件效果</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="tjc_game.html">游戏工程</a></li>
<li class="toctree-l2"><a class="reference internal" href="font_download.html">免费字体下载</a></li>
<li class="toctree-l2"><a class="reference internal" href="voice_download.html">声音资源下载</a></li>
<li class="toctree-l2"><a class="reference internal" href="scheme_download.html">原理图下载</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">资料下载</a> &raquo;</li>
      <li>网友提供应用样例</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>网友提供应用样例<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>以下样例为网友提交,我们不保证每一个例子都能完全正常使用，仅做参考!</p>
</div>
<section id="id2">
<h2>多功能测量仪<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<p>素材说明：界面华丽，带有示波器功能。</p>
<img alt="../_images/3_1.jpg" src="../_images/3_1.jpg" />
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/网友提供应用样例/多功能测量仪.HMI">《多功能测量仪》下载</a></p>
<hr class="docutils" />
</section>
<section id="id4">
<h2>全键盘输入、密码登录<a class="headerlink" href="#id4" title="此标题的永久链接"></a></h2>
<p>素材说明：</p>
<p>1、全键盘输入，支持大小写切换，数字，字符。</p>
<p>2、密码登录，利用掉电存储空间保存，修改密码。</p>
<p>3、进入设置页面的万能密码为890327。</p>
<img alt="../_images/3_3.png" src="../_images/3_3.png" />
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/网友提供应用样例/全键盘输入、密码登录.HMI">《全键盘输入、密码登录》下载</a></p>
<hr class="docutils" />
</section>
<section id="id6">
<h2>简易计算器<a class="headerlink" href="#id6" title="此标题的永久链接"></a></h2>
<p>素材说明：</p>
<blockquote>
<div><p>第一次接触串口屏，就随便做了个简易计算器，试试手，免MCU控制。
不支持负数和小数点，计算过大或出现负数时会有溢出问题。</p>
</div></blockquote>
<img alt="../_images/3_4.jpg" src="../_images/3_4.jpg" />
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/网友提供应用样例/简易计算器.HMI">《简易计算器》下载</a></p>
<hr class="docutils" />
</section>
<section id="id8">
<h2>打兔子，无需单片机支持<a class="headerlink" href="#id8" title="此标题的永久链接"></a></h2>
<dl class="simple">
<dt>素材说明：</dt><dd><p>根据前面的打兔子修改，无需单片机支持，直接运行，增加时间显示。</p>
</dd>
</dl>
<img alt="../_images/3_5.png" src="../_images/3_5.png" />
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/网友提供应用样例/打兔子，无需单片机支持.HMI">《打兔子，无需单片机支持》下载</a></p>
<hr class="docutils" />
</section>
<section id="id10">
<h2>电压数据采集及系统参数设置<a class="headerlink" href="#id10" title="此标题的永久链接"></a></h2>
<dl class="simple">
<dt>素材说明：</dt><dd><p>该案例单片机电压显示已有很多案例，不再多言，主要是屏本身参数设定。有如下功能：
1休眠时间：通过双态按钮实现休眠的开关功能。当休眠开时，可以通过cal界面来设置休眠时间；当休眠关时，休眠时间设置无效。
2工作时间：当屏处于非休眠状态时，可以一直“不间断”地记录工作时间。这里不间断是指切换页面的过程，它也一直保持工作。
3背光亮度：通过改变滑块的位置改变屏背光的亮度。</p>
</dd>
</dl>
<img alt="../_images/3_6.jpg" src="../_images/3_6.jpg" />
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/网友提供应用样例/电压数据采集及系统参数设置.HMI">《电压数据采集及系统参数设置》下载</a></p>
<hr class="docutils" />
</section>
<section id="id12">
<h2>串口屏控制3路继电器<a class="headerlink" href="#id12" title="此标题的永久链接"></a></h2>
<dl class="simple">
<dt>素材说明：</dt><dd><p>这个案例主要通过printh指令，从屏幕发送字节到串口，单片机收到后加以判断，并对指定的IO输出高低电平，实现继电器开关，并返回开关状态；
单片机端使用STC89C52；晶振11.0592；继电器控制IOP1^0,P1^1,P1^2；波特率9600</p>
</dd>
</dl>
<img alt="../_images/3_8.png" src="../_images/3_8.png" />
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/网友提供应用样例/串口屏控制3路继电器.HMI">《串口屏控制3路继电器》下载</a></p>
<hr class="docutils" />
</section>
<section id="id14">
<h2>九宫格全英文输入（支持串口打印字符）<a class="headerlink" href="#id14" title="此标题的永久链接"></a></h2>
<dl class="simple">
<dt>素材说明：</dt><dd><p>极简九宫格全英文输出（支持串口打印字符）
对于小于3.5寸的小屏如果做全键盘会影响触摸体验，因为键太多屏幕不够大只能缩小按键尺寸，影响视觉体验和触摸体验。九宫格键盘可以解决这个问题，10个按键布局实现全键盘的功能。数字，英文均可输入。您值得尝试一下!</p>
</dd>
</dl>
<img alt="../_images/3_9.jpg" src="../_images/3_9.jpg" />
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/网友提供应用样例/九宫格全英文输入（支持串口打印字符）.HMI">《九宫格全英文输入（支持串口打印字符）》下载</a></p>
<hr class="docutils" />
</section>
<section id="loading">
<h2>酷炫Loading进度条界面<a class="headerlink" href="#loading" title="此标题的永久链接"></a></h2>
<dl class="simple">
<dt>素材说明：</dt><dd><p>酷炫Loading进度条界面
为节约FLASH空间本实例没有使用切换整图来实现动画，而是使用单个小图片元素通过定时器来实现超炫动画效果</p>
</dd>
</dl>
<img alt="../_images/3_10.jpg" src="../_images/3_10.jpg" />
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/网友提供应用样例/酷炫Loading进度条界面.HMI">《酷炫Loading进度条界面》下载</a></p>
<hr class="docutils" />
</section>
<section id="id17">
<h2>86面板智能家居<a class="headerlink" href="#id17" title="此标题的永久链接"></a></h2>
<dl class="simple">
<dt>素材说明：</dt><dd><p>特别说:明对于单片机通讯方面，每个人的方法都不一样
所以我的通讯协议大家未必适合，大家可以用自己的方法实现单片机的控制</p>
</dd>
</dl>
<img alt="../_images/3_11.png" src="../_images/3_11.png" />
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/网友提供应用样例/86面板智能家居.zip">《86面板智能家居》下载</a></p>
<hr class="docutils" />
</section>
<section id="id19">
<h2>简洁圆形数字键盘（支持全局输入）<a class="headerlink" href="#id19" title="此标题的永久链接"></a></h2>
<dl class="simple">
<dt>素材说明：</dt><dd><p>简洁圆形数字键盘（支持全局输入）</p>
</dd>
</dl>
<img alt="../_images/3_12.jpg" src="../_images/3_12.jpg" />
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/网友提供应用样例/圆形数字键盘.HMI">《圆形数字键盘》下载</a></p>
<hr class="docutils" />
</section>
<section id="id21">
<h2>多种控件效果<a class="headerlink" href="#id21" title="此标题的永久链接"></a></h2>
<dl class="simple">
<dt>素材说明：</dt><dd><p>在同一个页面中实现下拉列表框效果，并支持滚动条</p>
</dd>
</dl>
<img alt="../_images/3_13.png" src="../_images/3_13.png" />
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/网友提供应用样例/多种控件效果.HMI">《多种控件效果》下载</a></p>
<hr class="docutils" />
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="ui_demo.html" class="btn btn-neutral float-left" title="UI样例工程" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="tjc_game.html" class="btn btn-neutral float-right" title="游戏工程" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>