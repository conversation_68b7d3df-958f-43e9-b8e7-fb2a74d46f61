#include "Geometry.h"
#include <stdio.h>
#include <stddef.h>  // 添加NULL定义

/**
 * 舵机角度转换为墙面坐标 - 激光云台核心算法
 * 
 * 几何模型说明:
 * - 云台位于原点，激光器指向墙面
 * - Pan轴控制水平旋转，Tilt轴控制垂直旋转
 * - 墙面距离云台1000mm
 * - 坐标系：X轴水平向右，Y轴垂直向上
 * 
 * @param servo_angle 舵机角度
 * @param wall_point 输出的墙面坐标
 * @return 错误代码
 */
GeometryError_t Geometry_ServoToWall(ServoAngle_t servo_angle, WallPoint_t* wall_point)
{
    if (wall_point == NULL) {
        return GEOMETRY_ERROR_INVALID_PARAM;
    }
    
    // 检查舵机角度范围
    if (!Geometry_IsServoAngleValid(servo_angle)) {
        return GEOMETRY_ERROR_OUT_OF_RANGE;
    }
    
    // 将舵机角度转换为相对于正前方的偏移角度
    // 假设120度为正前方位置
    float pan_offset = servo_angle.pan - 120.0f;
    float tilt_offset = servo_angle.tilt - 120.0f;
    
    // 转换为弧度
    float pan_rad = pan_offset * DEG_TO_RAD;
    float tilt_rad = tilt_offset * DEG_TO_RAD;
    
    // 计算墙面坐标
    // X = 距离 * tan(水平角度)
    // Y = 距离 * tan(垂直角度)
    wall_point->x = WALL_DISTANCE_MM * tanf(pan_rad);
    wall_point->y = WALL_DISTANCE_MM * tanf(tilt_rad);
    
    // 检查计算结果是否有效
    if (isnan(wall_point->x) || isnan(wall_point->y) || 
        isinf(wall_point->x) || isinf(wall_point->y)) {
        return GEOMETRY_ERROR_MATH_ERROR;
    }
    
    return GEOMETRY_OK;
}

/**
 * 墙面坐标转换为舵机角度 - 激光云台核心算法
 * 
 * @param wall_point 墙面坐标
 * @param servo_angle 输出的舵机角度
 * @return 错误代码
 */
GeometryError_t Geometry_WallToServo(WallPoint_t wall_point, ServoAngle_t* servo_angle)
{
    if (servo_angle == NULL) {
        return GEOMETRY_ERROR_INVALID_PARAM;
    }
    
    // 检查墙面坐标范围
    if (!Geometry_IsWallPointValid(wall_point)) {
        return GEOMETRY_ERROR_OUT_OF_RANGE;
    }
    
    // 计算相对于正前方的偏移角度
    float pan_offset_rad = atanf(wall_point.x / WALL_DISTANCE_MM);
    float tilt_offset_rad = atanf(wall_point.y / WALL_DISTANCE_MM);
    
    // 转换为度数
    float pan_offset = pan_offset_rad * RAD_TO_DEG;
    float tilt_offset = tilt_offset_rad * RAD_TO_DEG;
    
    // 转换为舵机绝对角度 (120度为正前方)
    servo_angle->pan = 120.0f + pan_offset;
    servo_angle->tilt = 120.0f + tilt_offset;
    
    // 角度标准化和限制
    *servo_angle = Geometry_ClampServoAngle(*servo_angle);
    
    return GEOMETRY_OK;
}

/**
 * 检查舵机角度是否在有效范围内
 */
uint8_t Geometry_IsServoAngleValid(ServoAngle_t servo_angle)
{
    return (servo_angle.pan >= PAN_ANGLE_MIN && servo_angle.pan <= PAN_ANGLE_MAX &&
            servo_angle.tilt >= TILT_ANGLE_MIN && servo_angle.tilt <= TILT_ANGLE_MAX);
}

/**
 * 检查墙面坐标是否在有效范围内
 */
uint8_t Geometry_IsWallPointValid(WallPoint_t wall_point)
{
    return (wall_point.x >= WALL_X_MIN && wall_point.x <= WALL_X_MAX &&
            wall_point.y >= WALL_Y_MIN && wall_point.y <= WALL_Y_MAX);
}

/**
 * 角度标准化 (0-360度)
 */
float Geometry_NormalizeAngle(float angle)
{
    while (angle < 0) angle += 360.0f;
    while (angle >= 360.0f) angle -= 360.0f;
    return angle;
}

/**
 * 限制舵机角度在安全范围内
 */
ServoAngle_t Geometry_ClampServoAngle(ServoAngle_t servo_angle)
{
    ServoAngle_t result;
    
    if (servo_angle.pan < PAN_ANGLE_MIN) result.pan = PAN_ANGLE_MIN;
    else if (servo_angle.pan > PAN_ANGLE_MAX) result.pan = PAN_ANGLE_MAX;
    else result.pan = servo_angle.pan;
    
    if (servo_angle.tilt < TILT_ANGLE_MIN) result.tilt = TILT_ANGLE_MIN;
    else if (servo_angle.tilt > TILT_ANGLE_MAX) result.tilt = TILT_ANGLE_MAX;
    else result.tilt = servo_angle.tilt;
    
    return result;
}

/**
 * 限制墙面坐标在有效范围内
 */
WallPoint_t Geometry_ClampWallPoint(WallPoint_t wall_point)
{
    WallPoint_t result;
    
    if (wall_point.x < WALL_X_MIN) result.x = WALL_X_MIN;
    else if (wall_point.x > WALL_X_MAX) result.x = WALL_X_MAX;
    else result.x = wall_point.x;
    
    if (wall_point.y < WALL_Y_MIN) result.y = WALL_Y_MIN;
    else if (wall_point.y > WALL_Y_MAX) result.y = WALL_Y_MAX;
    else result.y = wall_point.y;
    
    return result;
}

/**
 * 计算两点间距离
 */
float Geometry_CalculateDistance(WallPoint_t point1, WallPoint_t point2)
{
    float dx = point2.x - point1.x;
    float dy = point2.y - point1.y;
    return sqrtf(dx * dx + dy * dy);
}

/**
 * 计算两点间的角度 (弧度)
 */
float Geometry_CalculateAngleBetweenPoints(WallPoint_t point1, WallPoint_t point2)
{
    float dx = point2.x - point1.x;
    float dy = point2.y - point1.y;
    return atan2f(dy, dx);
}

/**
 * 几何转换测试函数
 */
GeometryError_t Geometry_TestConversion(void)
{
    // 测试用例1: 中心点
    ServoAngle_t test_servo = {120.0f, 120.0f};
    WallPoint_t test_wall;
    ServoAngle_t result_servo;

    GeometryError_t error = Geometry_ServoToWall(test_servo, &test_wall);
    if (error != GEOMETRY_OK) return error;

    error = Geometry_WallToServo(test_wall, &result_servo);
    if (error != GEOMETRY_OK) return error;

    // 检查转换精度 (允许0.1度误差)
    if (fabsf(test_servo.pan - result_servo.pan) > 0.1f ||
        fabsf(test_servo.tilt - result_servo.tilt) > 0.1f) {
        return GEOMETRY_ERROR_MATH_ERROR;
    }

    return GEOMETRY_OK;
}

// ==================== 直线路径插值算法实现 ====================

/**
 * 初始化路径插值 - 激光云台核心功能
 * @param path 路径插值结构体指针
 * @param point_a A点墙面坐标
 * @param point_b B点墙面坐标
 * @return 错误代码
 */
GeometryError_t Path_Initialize(PathInterpolation_t* path, WallPoint_t point_a, WallPoint_t point_b)
{
    if (path == NULL) {
        return GEOMETRY_ERROR_INVALID_PARAM;
    }

    // 验证路径有效性
    GeometryError_t error = Path_ValidatePath(point_a, point_b);
    if (error != GEOMETRY_OK) {
        return error;
    }

    // 初始化路径参数
    path->start_point = point_a;
    path->end_point = point_b;
    path->current_point = point_a;
    path->current_step = 0;
    path->total_steps = PATH_INTERPOLATION_STEPS;
    path->is_active = 1;
    path->direction = 0;  // 默认A→B方向

    // 计算初始舵机角度
    error = Geometry_WallToServo(path->current_point, &path->current_servo);
    if (error != GEOMETRY_OK) {
        path->is_active = 0;
        return error;
    }

    return GEOMETRY_OK;
}

/**
 * 获取下一步的舵机角度 - 激光云台核心功能
 * @param path 路径插值结构体指针
 * @param next_servo_angle 输出的下一步舵机角度
 * @return 错误代码
 */
GeometryError_t Path_GetNextStep(PathInterpolation_t* path, ServoAngle_t* next_servo_angle)
{
    if (path == NULL || next_servo_angle == NULL) {
        return GEOMETRY_ERROR_INVALID_PARAM;
    }

    if (!path->is_active || Path_IsComplete(path)) {
        return GEOMETRY_ERROR_OUT_OF_RANGE;
    }

    // 移动到下一步
    path->current_step++;

    // 根据方向确定起点和终点
    WallPoint_t start = path->direction ? path->end_point : path->start_point;
    WallPoint_t end = path->direction ? path->start_point : path->end_point;

    // 线性插值计算下一个墙面坐标
    GeometryError_t error = Path_LinearInterpolate(start, end,
                                                 path->current_step,
                                                 path->total_steps,
                                                 &path->current_point);
    if (error != GEOMETRY_OK) {
        return error;
    }

    // 转换为舵机角度
    error = Geometry_WallToServo(path->current_point, &path->current_servo);
    if (error != GEOMETRY_OK) {
        return error;
    }

    *next_servo_angle = path->current_servo;
    return GEOMETRY_OK;
}

/**
 * 设置移动方向
 * @param path 路径插值结构体指针
 * @param direction 方向 (0=A→B, 1=B→A)
 * @return 错误代码
 */
GeometryError_t Path_SetDirection(PathInterpolation_t* path, uint8_t direction)
{
    if (path == NULL) {
        return GEOMETRY_ERROR_INVALID_PARAM;
    }

    path->direction = direction;
    path->current_step = 0;  // 重置步数

    // 设置起始点
    path->current_point = direction ? path->end_point : path->start_point;

    // 更新当前舵机角度
    return Geometry_WallToServo(path->current_point, &path->current_servo);
}

/**
 * 检查路径是否完成
 * @param path 路径插值结构体指针
 * @return 1=完成, 0=未完成
 */
uint8_t Path_IsComplete(PathInterpolation_t* path)
{
    if (path == NULL) return 1;
    return (path->current_step >= path->total_steps);
}

/**
 * 重置路径
 * @param path 路径插值结构体指针
 */
void Path_Reset(PathInterpolation_t* path)
{
    if (path == NULL) return;

    path->current_step = 0;
    path->current_point = path->direction ? path->end_point : path->start_point;
    path->is_active = 1;

    // 更新当前舵机角度
    Geometry_WallToServo(path->current_point, &path->current_servo);
}

/**
 * 直线插值核心算法 - 确保墙面轨迹为直线
 * @param start 起始点
 * @param end 终点
 * @param step 当前步数
 * @param total_steps 总步数
 * @param result 输出的插值结果
 * @return 错误代码
 */
GeometryError_t Path_LinearInterpolate(WallPoint_t start, WallPoint_t end,
                                     uint16_t step, uint16_t total_steps,
                                     WallPoint_t* result)
{
    if (result == NULL || total_steps == 0) {
        return GEOMETRY_ERROR_INVALID_PARAM;
    }

    if (step > total_steps) {
        return GEOMETRY_ERROR_OUT_OF_RANGE;
    }

    // 线性插值公式: P = P1 + t * (P2 - P1), 其中 t = step / total_steps
    float t = (float)step / (float)total_steps;

    // 在墙面坐标空间进行线性插值 (这是确保直线的关键)
    result->x = start.x + t * (end.x - start.x);
    result->y = start.y + t * (end.y - start.y);

    // 检查结果有效性
    if (!Geometry_IsWallPointValid(*result)) {
        *result = Geometry_ClampWallPoint(*result);
    }

    return GEOMETRY_OK;
}

/**
 * 验证路径有效性
 * @param point_a A点坐标
 * @param point_b B点坐标
 * @return 错误代码
 */
GeometryError_t Path_ValidatePath(WallPoint_t point_a, WallPoint_t point_b)
{
    // 检查点是否在有效范围内
    if (!Geometry_IsWallPointValid(point_a) || !Geometry_IsWallPointValid(point_b)) {
        return GEOMETRY_ERROR_OUT_OF_RANGE;
    }

    // 检查两点是否相同
    float distance = Geometry_CalculateDistance(point_a, point_b);
    if (distance < 10.0f) {  // 最小距离10mm
        return GEOMETRY_ERROR_INVALID_PARAM;
    }

    // 检查路径是否过长 (避免超出舵机范围)
    if (distance > 2000.0f) {  // 最大距离2000mm
        return GEOMETRY_ERROR_OUT_OF_RANGE;
    }

    return GEOMETRY_OK;
}

/**
 * 计算路径长度
 * @param point_a A点坐标
 * @param point_b B点坐标
 * @return 路径长度 (mm)
 */
float Path_CalculatePathLength(WallPoint_t point_a, WallPoint_t point_b)
{
    return Geometry_CalculateDistance(point_a, point_b);
}

/**
 * 计算最优插值步数 (基于路径长度)
 * @param point_a A点坐标
 * @param point_b B点坐标
 * @return 建议的插值步数
 */
uint16_t Path_CalculateOptimalSteps(WallPoint_t point_a, WallPoint_t point_b)
{
    float length = Path_CalculatePathLength(point_a, point_b);

    // 基于路径长度计算步数 (每5mm一步)
    uint16_t steps = (uint16_t)(length / 5.0f);

    // 限制在合理范围内
    if (steps < 20) steps = 20;
    if (steps > 200) steps = 200;

    return steps;
}

/**
 * 路径插值测试函数
 * @return 错误代码
 */
GeometryError_t Path_TestInterpolation(void)
{
    // 测试用例: 从左下角到右上角的直线
    WallPoint_t point_a = {-300.0f, -200.0f};
    WallPoint_t point_b = {300.0f, 200.0f};

    PathInterpolation_t test_path;
    GeometryError_t error = Path_Initialize(&test_path, point_a, point_b);
    if (error != GEOMETRY_OK) return error;

    // 测试几个插值点
    for (int i = 0; i < 5; i++) {
        ServoAngle_t servo_angle;
        error = Path_GetNextStep(&test_path, &servo_angle);
        if (error != GEOMETRY_OK) return error;

        // 验证插值点在直线上
        WallPoint_t expected;
        error = Path_LinearInterpolate(point_a, point_b, test_path.current_step,
                                     test_path.total_steps, &expected);
        if (error != GEOMETRY_OK) return error;

        // 检查精度 (允许1mm误差)
        float dx = test_path.current_point.x - expected.x;
        float dy = test_path.current_point.y - expected.y;
        if (sqrtf(dx*dx + dy*dy) > 1.0f) {
            return GEOMETRY_ERROR_MATH_ERROR;
        }
    }

    return GEOMETRY_OK;
}
