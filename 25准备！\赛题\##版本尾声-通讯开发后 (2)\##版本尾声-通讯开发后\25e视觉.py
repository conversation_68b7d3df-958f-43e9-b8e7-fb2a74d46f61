import time, os, sys
import math
import cv_lite  # 导入cv_lite扩展模块
import ulab.numpy as np  # 导入numpy库
from media.sensor import *
from media.display import *
from media.media import *
from machine import UART, FPIOA, Pin
# 串口初始化 (修改为串口2)
fpioa = FPIOA()
# 根据文档，串口2的TXD为GPIO 11, RXD为GPIO 12
fpioa.set_function(11, FPIOA.UART2_TXD)
fpioa.set_function(12, FPIOA.UART2_RXD)
uart = UART(UART.UART2, 115200) # 使用UART.UART2

# 激光器控制GPIO初始化
fpioa.set_function(33, FPIOA.GPIO33)  # 激光器控制引脚
laser_pin = Pin(33, Pin.OUT)
laser_pin.value(0)  # 初始化时关闭激光器

# 屏幕分辨率设置
lcd_width = 800
lcd_height = 480

# 摄像头初始化（保留RGB模式用于色块检测，后续转为灰度图用于矩形检测）
sensor = Sensor(id=2)
sensor.reset() # 构造Sensor对象后必须调用此函数
sensor.set_framesize(width=600, height=360)  # 设置为600x360分辨率
sensor.set_pixformat(Sensor.RGB565)  # find_blobs等API支持RGB565格式

# 显示初始化
Display.init(Display.ST7701, width=lcd_width, height=lcd_height, to_ide=True)
MediaManager.init()
sensor.run() # 启动图像传感器输出

# 矩形检测核心参数（基于cv_lite）
canny_thresh1 = 50      # Canny边缘检测低阈值
canny_thresh2 = 150     # Canny边缘检测高阈值
approx_epsilon = 0.04   # 多边形拟合精度（越小越精确）
area_min_ratio = 0.005  # 最小面积比例（相对于图像总面积）
max_angle_cos = 0.3     # 角度余弦阈值（越小越接近矩形）
gaussian_blur_size = 3    # 高斯模糊核尺寸（奇数）

# 原有筛选参数
MIN_AREA = 100          # 最小面积阈值
MAX_AREA = 100000       # 最大面积阈值
MIN_ASPECT_RATIO = 0.3    # 最小宽高比
MAX_ASPECT_RATIO = 3.0    # 最大宽高比

# 数据发送频率控制
SEND_INTERVAL_MS = 100   # 100ms发送一次，频率降到10Hz

# 靶心位置判断参数
CENTER_THRESHOLD = 5   # 中心区域阈值（像素）

# 系统运行状态控制
running_state = False   # 系统运行状态，默认为停止状态

# 矩形检测参数
RECT_WIDTH = 210      # 固定矩形宽度
RECT_HEIGHT = 95      # 固定矩形高度
def calculate_distance(p1, p2):
    return math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])** 2)

def calculate_center(points):
    if not points:
        return (0, 0)
    sum_x = sum(p[0] for p in points)
    sum_y = sum(p[1] for p in points)
    return (sum_x / len(points), sum_y / len(points))

def is_valid_rect(corners):
    edges = [calculate_distance(corners[i], corners[(i+1)%4]) for i in range(4)]

    # 对边比例校验
    ratio1 = edges[0] / max(edges[2], 0.1)
    ratio2 = edges[1] / max(edges[3], 0.1)
    valid_ratio = 0.5 < ratio1 < 1.5 and 0.5 < ratio2 < 1.5

    # 面积校验
    area = 0
    for i in range(4):
        x1, y1 = corners[i]
        x2, y2 = corners[(i+1) % 4]
        area += (x1 * y2 - x2 * y1)
    area = abs(area) / 2
    valid_area = MIN_AREA < area < MAX_AREA

    # 宽高比校验
    width = max(p[0] for p in corners) - min(p[0] for p in corners)
    height = max(p[1] for p in corners) - min(p[1] for p in corners)
    aspect_ratio = width / max(height, 0.1)
    valid_aspect = MIN_ASPECT_RATIO < aspect_ratio < MAX_ASPECT_RATIO

    return valid_ratio and valid_area and valid_aspect

# 新的坐标发送函数：发送方向和距离信息
def send_target_info(center_x, screen_center_x):
    global running_state
    # 计算水平距离（靶心到画面中心竖直线的距离）
    distance = abs(center_x - screen_center_x)
    
    # 判断方向
    if distance <= CENTER_THRESHOLD:
        direction = "center"
        # 瞄准完成，自动停止系统
        running_state = False
        laser_pin.value(0)  # 关闭激光器
        uart.write("COMPLETED")  # 通知STM32瞄准完成
        print(f"瞄准完成！靶心已居中，系统自动停止")
        return
    elif center_x < screen_center_x:
        direction = "left"
    else:
        direction = "right"
    
    # 发送格式: 方向,距离
    msg = f"{direction},{distance}"
    uart.write(msg)
    # print(f"发送靶心信息: {msg}")

def sort_corners(corners):
    """将矩形角点按左上、右上、右下、左下顺序排序"""
    center = calculate_center(corners)
    sorted_corners = sorted(corners, key=lambda p: math.atan2(p[1]-center[1], p[0]-center[0]))

    # 调整顺序为左上、右上、右下、左下
    if len(sorted_corners) == 4:
        left_top = min(sorted_corners, key=lambda p: p[0]+p[1])
        index = sorted_corners.index(left_top)
        sorted_corners = sorted_corners[index:] + sorted_corners[:index]
    return sorted_corners

# 接收STM32控制指令的函数
def check_uart_command():
    global running_state
    try:
        if uart.any():
            cmd = uart.read().decode('utf-8').strip()
            print(f"收到指令: '{cmd}'")  # 调试信息
            if cmd == "START":
                running_state = True
                laser_pin.value(1)  # 启动时打开激光器
                print("收到启动指令，开始视觉识别，激光器开启")
            elif cmd == "STOP":
                running_state = False
                laser_pin.value(0)  # 停止时关闭激光器
                print("收到停止指令，停止视觉识别，激光器关闭")
            else:
                print(f"未知指令: '{cmd}'")  # 调试未知指令
    except Exception as e:
        print(f"串口接收错误: {e}")  # 调试接收错误

clock = time.clock()
image_shape = [sensor.height(), sensor.width()]  # [高, 宽] 用于cv_lite
last_send_time = 0  # 上次发送时间

while True:
    clock.tick()
    img = sensor.snapshot() # 从指定输出通道中捕获一帧图像数据
    
    # 检查来自STM32的控制指令
    check_uart_command()

    # 2. 矩形检测（使用cv_lite）
    # 将RGB图像转为灰度图
    gray_img = img.to_grayscale() # 将图像转换为灰度图像
    img_np = gray_img.to_numpy_ref()  # 转为numpy数组供cv_lite使用

    # 调用cv_lite矩形检测
    rects = cv_lite.grayscale_find_rectangles_with_corners(
        image_shape,      # 图像尺寸 [高, 宽]
        img_np,           # 灰度图数据
        canny_thresh1,    # Canny低阈值
        canny_thresh2,    # Canny高阈值
        approx_epsilon,   # 多边形拟合精度
        area_min_ratio,   # 最小面积比例
        max_angle_cos,    # 角度余弦阈值
        gaussian_blur_size # 高斯模糊尺寸
    )

    # 3. 筛选最小矩形
    min_area = float('inf')
    smallest_rect = None
    smallest_rect_corners = None  # 存储最小矩形的角点

    for rect in rects:
        # rect格式: [x, y, w, h, c1.x, c1.y, c2.x, c2.y, c3.x, c3.y, c4.x, c4.y]
        x, y, w, h = rect[0], rect[1], rect[2], rect[3]
        # 提取四个角点
        corners = [
            (rect[4], rect[5]),   # 角点1
            (rect[6], rect[7]),   # 角点2
            (rect[8], rect[9]),   # 角点3
            (rect[10], rect[11])  # 角点4
        ]

        # 验证矩形有效性
        if is_valid_rect(corners):
            # 计算面积
            area = w * h  # 直接使用矩形宽高计算面积（更高效）
            # 更新最小矩形
            if area < min_area:
                min_area = area
                smallest_rect = (x, y, w, h)
                smallest_rect_corners = corners

    # 4. 处理检测结果
    if smallest_rect and smallest_rect_corners:
        # 如果找到矩形，则处理并发送坐标
        x, y, w, h = smallest_rect
        corners = smallest_rect_corners

        # 对矩形角点进行排序
        sorted_corners = sort_corners(corners)

        # 绘制矩形边框和角点
        for i in range(4):
            x1, y1 = sorted_corners[i]
            x2, y2 = sorted_corners[(i+1) % 4]
            img.draw_line(int(x1), int(y1), int(x2), int(y2), color=(255, 0, 0), thickness=2)
        for p in sorted_corners:
            img.draw_circle(int(p[0]), int(p[1]), 5, color=(0, 255, 0), thickness=2)

        # 计算并绘制矩形中心点
        rect_center = calculate_center(sorted_corners)
        rect_center_int = (int(round(rect_center[0])), int(round(rect_center[1])))
        img.draw_circle(rect_center_int[0], rect_center_int[1], 4, color=(0, 255, 255), thickness=2)

        # 绘制画面中心竖直线（参考线）
        screen_center_x = sensor.width() // 2
        img.draw_line(screen_center_x, 0, screen_center_x, sensor.height(), color=(255, 255, 0), thickness=1)
        
        # 只在运行状态下发送数据 - 控制发送频率
        if running_state:
            current_time = time.ticks_ms()
            if time.ticks_diff(current_time, last_send_time) >= SEND_INTERVAL_MS:
                send_target_info(rect_center[0], screen_center_x)
                last_send_time = current_time
    else:
        # 只在运行状态下发送"NO"信号（也遵循频率限制）
        if running_state:
            current_time = time.ticks_ms()
            if time.ticks_diff(current_time, last_send_time) >= SEND_INTERVAL_MS:
                uart.write("NO")
                last_send_time = current_time

    # 5. 显示图像和性能统计
    fps = clock.fps()
    img.draw_string_advanced(10, 10, 20, f"FPS: {fps:.1f}", color=(255, 255, 255))  # 显示FPS
    
    # 显示系统运行状态
    status_text = "运行中" if running_state else "等待启动"
    status_color = (0, 255, 0) if running_state else (255, 0, 0)
    img.draw_string_advanced(10, 40, 20, f"状态: {status_text}", color=status_color)

    # 显示图像
    Display.show_image(img,
                       x=round((lcd_width-sensor.width())/2),
                       y=round((lcd_height-sensor.height())/2))

