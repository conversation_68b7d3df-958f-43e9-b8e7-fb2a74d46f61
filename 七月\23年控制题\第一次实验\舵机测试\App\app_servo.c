/**
  ******************************************************************************
  * @file    app_servo.c
  * <AUTHOR> Analysis Expert
  * @version V1.0
  * @date    2025-07-17
  * @brief   舵机应用层实现
  ******************************************************************************
  */

#include "app_servo.h"
#include "hts25l_driver.h"
#include "bsp_led.h"
#include "bsp_key.h"

/* 全局变量定义 */
volatile uint32_t g_systick_count = 0;

/* 静态变量 */
static ServoState_t servo_state = SERVO_STATE_IDLE;
static float current_angle = 0.0f;
static uint32_t state_start_time = 0;

/**
  * @brief  舵机应用初始化
  * @param  None
  * @retval None
  */
void App_Servo_Init(void)
{
    // 初始化HTS25L驱动
    HTS25L_Init();
    
    // 延时等待舵机稳定
    for(volatile uint32_t i = 0; i < 1000000; i++);
    
    // 设置伺服模式
    HTS25L_OrMotorModeWrite(SERVO_ID, HTS25L_MODE_SERVO, 0);
    for(volatile uint32_t i = 0; i < 500000; i++);
    
    // 加载力矩
    HTS25L_LoadOrUnloadWrite(SERVO_ID, HTS25L_TORQUE_LOAD);
    for(volatile uint32_t i = 0; i < 500000; i++);
    
    // 回零位
    current_angle = 0.0f;
    uint16_t zero_pos = HTS25L_AngleDegToPos(current_angle);
    HTS25L_MoveTimeWrite(SERVO_ID, zero_pos, SERVO_MOVE_TIME);
    
    // 设置初始状态
    servo_state = SERVO_STATE_MOVING;
    state_start_time = g_systick_count;
    BSP_LED_On(LED_MOVING);
}

/**
  * @brief  舵机30度步进
  * @param  None
  * @retval None
  */
static void App_Servo_Step30Degree(void)
{
    // 计算下一个角度
    float next_angle = current_angle + SERVO_STEP_ANGLE;
    
    // 边界检查和复位逻辑
    if(next_angle > 240.0f)
    {
        next_angle = 0.0f;  // 超过240度回到0度
    }
    
    // 转换为位置值并发送指令
    uint16_t target_pos = HTS25L_AngleDegToPos(next_angle);
    if(HTS25L_MoveTimeWrite(SERVO_ID, target_pos, SERVO_MOVE_TIME) == 0)
    {
        current_angle = next_angle;
        servo_state = SERVO_STATE_MOVING;
        state_start_time = g_systick_count;
        BSP_LED_On(LED_MOVING);
    }
}

/**
  * @brief  舵机应用任务
  * @param  None
  * @retval None
  */
void App_Servo_Task(void)
{
    uint32_t elapsed_time = g_systick_count - state_start_time;
    
    switch(servo_state)
    {
        case SERVO_STATE_IDLE:
            // 检测按键
            if(BSP_Key_Scan(KEY_K1) == KEY_PRESSED)
            {
                App_Servo_Step30Degree();
            }
            break;
            
        case SERVO_STATE_MOVING:
            // 等待运动完成
            if(elapsed_time >= SERVO_MOVE_TIME)
            {
                servo_state = SERVO_STATE_WAITING;
                state_start_time = g_systick_count;
                BSP_LED_Off(LED_MOVING);
            }
            break;
            
        case SERVO_STATE_WAITING:
            // 等待额外时间后允许下次按键
            if(elapsed_time >= (SERVO_WAIT_TIME - SERVO_MOVE_TIME))
            {
                servo_state = SERVO_STATE_IDLE;
            }
            break;
            
        default:
            servo_state = SERVO_STATE_IDLE;
            break;
    }
}

/**
  * @brief  SysTick中断调用函数
  * @param  None
  * @retval None
  */
void App_Servo_SysTick(void)
{
    g_systick_count++;
}
