<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\OBJ\RTC.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\OBJ\RTC.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060300: Last Updated: Sat Sep 12 00:29:27 2020
<BR><P>
<H3>Maximum Stack Usage =        392 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; Adjust_Time &rArr; Time_Display &rArr; LCD_DisplayHZstr &rArr; LCD_Display1HZ &rArr; Get_HzMat &rArr; W25QXX_Read &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1c]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1c]">ADC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1c]">ADC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4]">BusFault_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1e]">CAN1_RX0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1f]">CAN1_RX1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[20]">CAN1_SCE_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1d]">CAN1_TX_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4a]">CAN2_RX0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4b]">CAN2_RX1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4c]">CAN2_SCE_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[49]">CAN2_TX_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[58]">DCMI_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[15]">DMA1_Stream0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[16]">DMA1_Stream1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[17]">DMA1_Stream2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[18]">DMA1_Stream3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[19]">DMA1_Stream4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1a]">DMA1_Stream5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1b]">DMA1_Stream6_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[39]">DMA1_Stream7_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[42]">DMA2_Stream0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[43]">DMA2_Stream1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[44]">DMA2_Stream2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[45]">DMA2_Stream3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[46]">DMA2_Stream4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4e]">DMA2_Stream5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4f]">DMA2_Stream6_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[50]">DMA2_Stream7_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[7]">DebugMon_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[47]">ETH_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[48]">ETH_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[10]">EXTI0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[32]">EXTI15_10_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[11]">EXTI1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[12]">EXTI2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[13]">EXTI3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[14]">EXTI4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[21]">EXTI9_5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[e]">FLASH_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3a]">FMC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5a]">FPU_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5e]">HAL_SRAM_DMA_XferCpltCallback</a> from stm32f4xx_hal_sram.o(.text) referenced from stm32f4xx_hal_sram.o(.text)
 <LI><a href="#[5f]">HAL_SRAM_DMA_XferErrorCallback</a> from stm32f4xx_hal_sram.o(.text) referenced from stm32f4xx_hal_sram.o(.text)
 <LI><a href="#[59]">HASH_RNG_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2a]">I2C1_ER_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[29]">I2C1_EV_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2c]">I2C2_ER_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2b]">I2C2_EV_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[53]">I2C3_ER_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[52]">I2C3_EV_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3]">MemManage_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4d]">OTG_FS_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[34]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[55]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[54]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[57]">OTG_HS_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[56]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[b]">PVD_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[8]">PendSV_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[f]">RCC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[33]">RTC_Alarm_IRQHandler</a> from rtc.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[d]">RTC_WKUP_IRQHandler</a> from rtc.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3b]">SDIO_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[6b]">SD_DMAError</a> from stm32f4xx_hal_sd.o(.text) referenced from stm32f4xx_hal_sd.o(.text)
 <LI><a href="#[6a]">SD_DMAReceiveCplt</a> from stm32f4xx_hal_sd.o(.text) referenced from stm32f4xx_hal_sd.o(.text)
 <LI><a href="#[6e]">SD_DMARxAbort</a> from stm32f4xx_hal_sd.o(.text) referenced 2 times from stm32f4xx_hal_sd.o(.text)
 <LI><a href="#[6c]">SD_DMATransmitCplt</a> from stm32f4xx_hal_sd.o(.text) referenced from stm32f4xx_hal_sd.o(.text)
 <LI><a href="#[6d]">SD_DMATxAbort</a> from stm32f4xx_hal_sd.o(.text) referenced 2 times from stm32f4xx_hal_sd.o(.text)
 <LI><a href="#[2d]">SPI1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2e]">SPI2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3d]">SPI3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[75]">SPI_2linesRxISR_16BIT</a> from stm32f4xx_hal_spi.o(.text) referenced from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[74]">SPI_2linesRxISR_16BITCRC</a> from stm32f4xx_hal_spi.o(.text) referenced from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[77]">SPI_2linesRxISR_8BIT</a> from stm32f4xx_hal_spi.o(.text) referenced from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[73]">SPI_2linesRxISR_8BITCRC</a> from stm32f4xx_hal_spi.o(.text) referenced from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[76]">SPI_2linesTxISR_16BIT</a> from stm32f4xx_hal_spi.o(.text) referenced from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[78]">SPI_2linesTxISR_8BIT</a> from stm32f4xx_hal_spi.o(.text) referenced from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[83]">SPI_AbortRx_ISR</a> from stm32f4xx_hal_spi.o(.text) referenced 2 times from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[82]">SPI_AbortTx_ISR</a> from stm32f4xx_hal_spi.o(.text) referenced 2 times from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[86]">SPI_DMAAbortOnError</a> from stm32f4xx_hal_spi.o(.text) referenced 2 times from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[7d]">SPI_DMAError</a> from stm32f4xx_hal_spi.o(.text) referenced 2 times from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[7e]">SPI_DMAHalfReceiveCplt</a> from stm32f4xx_hal_spi.o(.text) referenced from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[7b]">SPI_DMAHalfTransmitCplt</a> from stm32f4xx_hal_spi.o(.text) referenced from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[80]">SPI_DMAHalfTransmitReceiveCplt</a> from stm32f4xx_hal_spi.o(.text) referenced from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[7f]">SPI_DMAReceiveCplt</a> from stm32f4xx_hal_spi.o(.text) referenced from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[85]">SPI_DMARxAbortCallback</a> from stm32f4xx_hal_spi.o(.text) referenced from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[7c]">SPI_DMATransmitCplt</a> from stm32f4xx_hal_spi.o(.text) referenced from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[81]">SPI_DMATransmitReceiveCplt</a> from stm32f4xx_hal_spi.o(.text) referenced from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[84]">SPI_DMATxAbortCallback</a> from stm32f4xx_hal_spi.o(.text) referenced from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[79]">SPI_RxISR_16BIT</a> from stm32f4xx_hal_spi.o(.text) referenced from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[72]">SPI_RxISR_16BITCRC</a> from stm32f4xx_hal_spi.o(.text) referenced from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[7a]">SPI_RxISR_8BIT</a> from stm32f4xx_hal_spi.o(.text) referenced from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[71]">SPI_RxISR_8BITCRC</a> from stm32f4xx_hal_spi.o(.text) referenced from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[6f]">SPI_TxISR_16BIT</a> from stm32f4xx_hal_spi.o(.text) referenced from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[70]">SPI_TxISR_8BIT</a> from stm32f4xx_hal_spi.o(.text) referenced from stm32f4xx_hal_spi.o(.text)
 <LI><a href="#[6]">SVC_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[9]">SysTick_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5c]">SystemInit</a> from system_stm32f4xx.o(.text) referenced from startup_stm32f407xx.o(.text)
 <LI><a href="#[c]">TAMP_STAMP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[22]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[25]">TIM1_CC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[24]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[23]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[26]">TIM2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[27]">TIM3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[28]">TIM4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3c]">TIM5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[40]">TIM6_DAC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[41]">TIM7_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[35]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[38]">TIM8_CC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[37]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[36]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3e]">UART4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3f]">UART5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[69]">UART_DMAAbortOnError</a> from stm32f4xx_hal_uart.o(.text) referenced from stm32f4xx_hal_uart.o(.text)
 <LI><a href="#[62]">UART_DMAError</a> from stm32f4xx_hal_uart.o(.text) referenced from stm32f4xx_hal_uart.o(.text)
 <LI><a href="#[63]">UART_DMAReceiveCplt</a> from stm32f4xx_hal_uart.o(.text) referenced from stm32f4xx_hal_uart.o(.text)
 <LI><a href="#[66]">UART_DMARxAbortCallback</a> from stm32f4xx_hal_uart.o(.text) referenced from stm32f4xx_hal_uart.o(.text)
 <LI><a href="#[64]">UART_DMARxHalfCplt</a> from stm32f4xx_hal_uart.o(.text) referenced from stm32f4xx_hal_uart.o(.text)
 <LI><a href="#[68]">UART_DMARxOnlyAbortCallback</a> from stm32f4xx_hal_uart.o(.text) referenced from stm32f4xx_hal_uart.o(.text)
 <LI><a href="#[60]">UART_DMATransmitCplt</a> from stm32f4xx_hal_uart.o(.text) referenced from stm32f4xx_hal_uart.o(.text)
 <LI><a href="#[65]">UART_DMATxAbortCallback</a> from stm32f4xx_hal_uart.o(.text) referenced from stm32f4xx_hal_uart.o(.text)
 <LI><a href="#[61]">UART_DMATxHalfCplt</a> from stm32f4xx_hal_uart.o(.text) referenced from stm32f4xx_hal_uart.o(.text)
 <LI><a href="#[67]">UART_DMATxOnlyAbortCallback</a> from stm32f4xx_hal_uart.o(.text) referenced from stm32f4xx_hal_uart.o(.text)
 <LI><a href="#[2f]">USART1_IRQHandler</a> from usart1.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[30]">USART2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[31]">USART3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[51]">USART6_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5]">UsageFault_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[a]">WWDG_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5d]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32f407xx.o(.text)
 <LI><a href="#[5b]">main</a> from main.o(.text) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[5d]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(.text)
</UL>
<P><STRONG><a name="[22b]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[87]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[22a]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[22c]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[22d]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[22e]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[22f]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[230]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[231]"></a>WFI_SET</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, common.o(.emb_text), UNUSED)

<P><STRONG><a name="[ea]"></a>INTX_DISABLE</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, common.o(.emb_text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WriteSDisk
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_ReadSDisk
</UL>

<P><STRONG><a name="[ec]"></a>INTX_ENABLE</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, common.o(.emb_text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WriteSDisk
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_ReadSDisk
</UL>

<P><STRONG><a name="[232]"></a>MSR_MSP</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, common.o(.emb_text), UNUSED)

<P><STRONG><a name="[89]"></a>Time_Display</STRONG> (Thumb, 194 bytes, Stack size 16 bytes, main.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = Time_Display &rArr; LCD_DisplayHZstr &rArr; LCD_Display1HZ &rArr; Get_HzMat &rArr; W25QXX_Read &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayNum
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayHZstr
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Adjust_Time
</UL>

<P><STRONG><a name="[8c]"></a>Adjust_Time</STRONG> (Thumb, 408 bytes, Stack size 24 bytes, main.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 376<LI>Call Chain = Adjust_Time &rArr; Time_Display &rArr; LCD_DisplayHZstr &rArr; LCD_Display1HZ &rArr; Get_HzMat &rArr; W25QXX_Read &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayString_color
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayString
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_Display
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5b]"></a>main</STRONG> (Thumb, 710 bytes, Stack size 16 bytes, main.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = main &rArr; Adjust_Time &rArr; Time_Display &rArr; LCD_DisplayHZstr &rArr; LCD_Display1HZ &rArr; Get_HzMat &rArr; W25QXX_Read &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1_init
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_scan
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;font_init
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Init
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stm32_Clock_Init
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_SetTimes
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_InitConfig
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_GetTimes
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Memory_Init
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mem_malloc
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayString_color
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayString
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayHZstr
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Init
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetLunarCalendarStr
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetJieQiStr
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Adjust_Time
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_Display
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>SysTick_Handler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTick_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>SystemInit</STRONG> (Thumb, 82 bytes, Stack size 0 bytes, system_stm32f4xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(.text)
</UL>
<P><STRONG><a name="[233]"></a>SystemCoreClockUpdate</STRONG> (Thumb, 174 bytes, Stack size 16 bytes, system_stm32f4xx.o(.text), UNUSED)

<P><STRONG><a name="[109]"></a>HAL_MspInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_msp.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[10b]"></a>HAL_MspDeInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_msp.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DeInit
</UL>

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[94]"></a>LED_Init</STRONG> (Thumb, 84 bytes, Stack size 32 bytes, led.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = LED_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[93]"></a>KEY_Init</STRONG> (Thumb, 66 bytes, Stack size 32 bytes, key.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = KEY_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9c]"></a>key_scan</STRONG> (Thumb, 192 bytes, Stack size 0 bytes, key.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a7]"></a>LCD_WriteReg</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lcd.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Scan_Direction
</UL>

<P><STRONG><a name="[a4]"></a>LCD_ReadReg</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, lcd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>

<P><STRONG><a name="[ac]"></a>lcdm_delay</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, lcd.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_GetPoint
</UL>

<P><STRONG><a name="[ae]"></a>LCD_WriteGRAM</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lcd.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Draw_Picture
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Fill_onecolor
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawPoint
</UL>

<P><STRONG><a name="[234]"></a>LCD_DisplayOn</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lcd.o(.text), UNUSED)

<P><STRONG><a name="[235]"></a>LCD_DisplayOff</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lcd.o(.text), UNUSED)

<P><STRONG><a name="[a8]"></a>LCD_Open_Window</STRONG> (Thumb, 166 bytes, Stack size 12 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = LCD_Open_Window
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Scan_Direction
</UL>

<P><STRONG><a name="[a6]"></a>Set_Scan_Direction</STRONG> (Thumb, 204 bytes, Stack size 16 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Set_Scan_Direction &rArr; LCD_Open_Window
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Open_Window
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteReg
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Display_Mode
</UL>

<P><STRONG><a name="[a9]"></a>Set_Display_Mode</STRONG> (Thumb, 214 bytes, Stack size 8 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = Set_Display_Mode &rArr; Set_Scan_Direction &rArr; LCD_Open_Window
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Scan_Direction
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[ab]"></a>LCD_SetCursor</STRONG> (Thumb, 186 bytes, Stack size 0 bytes, lcd.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Draw_Picture
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Fill_onecolor
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawPoint
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_GetPoint
</UL>

<P><STRONG><a name="[aa]"></a>LCD_GetPoint</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, lcd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcdm_delay
</UL>

<P><STRONG><a name="[ad]"></a>LCD_DrawPoint</STRONG> (Thumb, 66 bytes, Stack size 12 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = LCD_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteGRAM
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Color_DrawPoint
</UL>

<P><STRONG><a name="[af]"></a>LCD_Color_DrawPoint</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = LCD_Color_DrawPoint &rArr; LCD_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawPoint
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayChar
<LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Display1HZ
</UL>

<P><STRONG><a name="[b6]"></a>Ssd1963_Set_BackLight</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, lcd.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[b0]"></a>LCD_Clear</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = LCD_Clear
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteGRAM
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_hzfont
</UL>

<P><STRONG><a name="[b1]"></a>HAL_SRAM_MspInit</STRONG> (Thumb, 238 bytes, Stack size 32 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_SRAM_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_Init
</UL>

<P><STRONG><a name="[b2]"></a>LCD_FSMC_Config</STRONG> (Thumb, 122 bytes, Stack size 64 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = LCD_FSMC_Config &rArr; HAL_SRAM_Init &rArr; HAL_SRAM_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_Init
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[b4]"></a>ILI9341_Read_id</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, lcd.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[b5]"></a>SSD1963_Read_id</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, lcd.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[95]"></a>LCD_Init</STRONG> (Thumb, 1020 bytes, Stack size 32 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = LCD_Init &rArr; LCD_FSMC_Config &rArr; HAL_SRAM_Init &rArr; HAL_SRAM_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SSD1963_Read_id
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9341_Read_id
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_FSMC_Config
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ssd1963_Set_BackLight
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Display_Mode
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b7]"></a>LCD_Fill_onecolor</STRONG> (Thumb, 82 bytes, Stack size 36 bytes, lcd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteGRAM
</UL>
<BR>[Called By]<UL><LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_hzfont
</UL>

<P><STRONG><a name="[b8]"></a>LCD_Draw_Picture</STRONG> (Thumb, 94 bytes, Stack size 40 bytes, lcd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteGRAM
</UL>

<P><STRONG><a name="[b9]"></a>LCD_DisplayChar</STRONG> (Thumb, 206 bytes, Stack size 52 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = LCD_DisplayChar &rArr; LCD_Color_DrawPoint &rArr; LCD_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Color_DrawPoint
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayString
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayNum
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayHZstr
</UL>

<P><STRONG><a name="[8e]"></a>LCD_DisplayString</STRONG> (Thumb, 64 bytes, Stack size 20 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = LCD_DisplayString &rArr; LCD_DisplayChar &rArr; LCD_Color_DrawPoint &rArr; LCD_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayChar
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayString_color
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayNum
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Adjust_Time
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_hzfont
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fupd_prog
</UL>

<P><STRONG><a name="[8d]"></a>LCD_DisplayString_color</STRONG> (Thumb, 68 bytes, Stack size 36 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = LCD_DisplayString_color &rArr; LCD_DisplayString &rArr; LCD_DisplayChar &rArr; LCD_Color_DrawPoint &rArr; LCD_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayString
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Adjust_Time
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_hzfont
</UL>

<P><STRONG><a name="[ba]"></a>Counter_Power</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Counter_Power
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayNum
</UL>

<P><STRONG><a name="[8b]"></a>LCD_DisplayNum</STRONG> (Thumb, 244 bytes, Stack size 56 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 156<LI>Call Chain = LCD_DisplayNum &rArr; LCD_DisplayString &rArr; LCD_DisplayChar &rArr; LCD_Color_DrawPoint &rArr; LCD_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Counter_Power
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayChar
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayString
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayNum_color
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_Display
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fupd_prog
</UL>

<P><STRONG><a name="[bb]"></a>LCD_DisplayNum_color</STRONG> (Thumb, 78 bytes, Stack size 60 bytes, lcd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayNum
</UL>

<P><STRONG><a name="[bc]"></a>HAL_UART_MspInit</STRONG> (Thumb, 144 bytes, Stack size 32 bytes, usart1.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_UART_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MultiProcessor_Init
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_LIN_Init
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_HalfDuplex_Init
</UL>

<P><STRONG><a name="[92]"></a>uart1_init</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, usart1.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = uart1_init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_IT
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c2]"></a>uart1SendChar</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, usart1.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1SendChars
</UL>

<P><STRONG><a name="[c1]"></a>uart1SendChars</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, usart1.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1SendChar
</UL>

<P><STRONG><a name="[2f]"></a>USART1_IRQHandler</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, usart1.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = USART1_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UART_RxCpltCallback &rArr; strcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_IT
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_GetState
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[c5]"></a>HAL_UART_RxCpltCallback</STRONG> (Thumb, 148 bytes, Stack size 16 bytes, usart1.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_UART_RxCpltCallback &rArr; strcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
</UL>

<P><STRONG><a name="[cc]"></a>mymemcpy</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, malloc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Remem_malloc
</UL>

<P><STRONG><a name="[c7]"></a>mymemset</STRONG> (Thumb, 20 bytes, Stack size 12 bytes, malloc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = mymemset
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Memory_Init
</UL>

<P><STRONG><a name="[99]"></a>Memory_Init</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, malloc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = Memory_Init &rArr; mymemset
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mymemset
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memin_free
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memin_malloc
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[236]"></a>Mem_perused</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, malloc.o(.text), UNUSED)

<P><STRONG><a name="[c8]"></a>memin_malloc</STRONG> (Thumb, 156 bytes, Stack size 28 bytes, malloc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = memin_malloc &rArr; Memory_Init &rArr; mymemset
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Memory_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Remem_malloc
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mem_malloc
</UL>

<P><STRONG><a name="[c9]"></a>memin_free</STRONG> (Thumb, 84 bytes, Stack size 20 bytes, malloc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Memory_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mem_free
</UL>

<P><STRONG><a name="[ca]"></a>Mem_free</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, malloc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memin_free
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Remem_malloc
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_hzfont
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;updata_fontx
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
</UL>

<P><STRONG><a name="[9a]"></a>Mem_malloc</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, malloc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = Mem_malloc &rArr; memin_malloc &rArr; Memory_Init &rArr; mymemset
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memin_malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_hzfont
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;updata_fontx
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
</UL>

<P><STRONG><a name="[cb]"></a>Remem_malloc</STRONG> (Thumb, 60 bytes, Stack size 20 bytes, malloc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mem_free
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memin_malloc
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mymemcpy
</UL>

<P><STRONG><a name="[cd]"></a>SPI1_ReadWriteByte</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write_Page
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Read
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_WAKEUP
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_PowerDown
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Erase_Sector
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Erase_Chip
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write_Disable
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write_Enable
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write_SR
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_ReadSR
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_ReadID
</UL>

<P><STRONG><a name="[cf]"></a>SPI1_Init</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = SPI1_Init &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Init
</UL>

<P><STRONG><a name="[d1]"></a>HAL_SPI_MspInit</STRONG> (Thumb, 106 bytes, Stack size 32 bytes, spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
</UL>

<P><STRONG><a name="[d3]"></a>SPI1_Setclock</STRONG> (Thumb, 66 bytes, Stack size 0 bytes, spi.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Init
</UL>

<P><STRONG><a name="[d2]"></a>W25QXX_ReadID</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, w25qxx.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = W25QXX_ReadID &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Init
</UL>

<P><STRONG><a name="[97]"></a>W25QXX_Init</STRONG> (Thumb, 86 bytes, Stack size 32 bytes, w25qxx.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = W25QXX_Init &rArr; SPI1_Init &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Setclock
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_ReadID
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;font_init
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[d4]"></a>W25QXX_ReadSR</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, w25qxx.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Wait_Busy
</UL>

<P><STRONG><a name="[d5]"></a>W25QXX_Write_SR</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, w25qxx.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>

<P><STRONG><a name="[d6]"></a>W25QXX_Write_Enable</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, w25qxx.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write_Page
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Erase_Sector
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Erase_Chip
</UL>

<P><STRONG><a name="[d7]"></a>W25QXX_Wait_Busy</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, w25qxx.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_ReadSR
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write_Page
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Erase_Sector
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Erase_Chip
</UL>

<P><STRONG><a name="[d8]"></a>W25QXX_Write_Disable</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, w25qxx.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>

<P><STRONG><a name="[d9]"></a>W25QXX_Erase_Chip</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, w25qxx.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Wait_Busy
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write_Enable
</UL>

<P><STRONG><a name="[da]"></a>W25QXX_Erase_Sector</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, w25qxx.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Wait_Busy
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write_Enable
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_SectorWrite
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_hzfont
</UL>

<P><STRONG><a name="[db]"></a>W25QXX_PowerDown</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, w25qxx.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>

<P><STRONG><a name="[dc]"></a>W25QXX_WAKEUP</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, w25qxx.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>

<P><STRONG><a name="[dd]"></a>W25QXX_Read</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, w25qxx.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = W25QXX_Read &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;font_init
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_SectorWrite
<LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_HzMat
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_hzfont
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_convert
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>

<P><STRONG><a name="[de]"></a>W25QXX_Write_Page</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, w25qxx.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Wait_Busy
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write_Enable
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_PageWrite
</UL>

<P><STRONG><a name="[df]"></a>W25QXX_PageWrite</STRONG> (Thumb, 290 bytes, Stack size 40 bytes, w25qxx.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write_Page
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_SectorWrite
</UL>

<P><STRONG><a name="[e0]"></a>W25QXX_SectorWrite</STRONG> (Thumb, 208 bytes, Stack size 40 bytes, w25qxx.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_PageWrite
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Read
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Erase_Sector
</UL>
<BR>[Called By]<UL><LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_hzfont
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;updata_fontx
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>

<P><STRONG><a name="[e1]"></a>SD_Init</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, tfcard_sdio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardInfo
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
</UL>
<BR>[Called By]<UL><LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_hzfont
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[e5]"></a>HAL_SD_MspInit</STRONG> (Thumb, 180 bytes, Stack size 32 bytes, tfcard_sdio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Init
</UL>

<P><STRONG><a name="[e6]"></a>SD_GetCardInfo</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, tfcard_sdio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardInfo
</UL>

<P><STRONG><a name="[e7]"></a>SD_GetCardState</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, tfcard_sdio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WriteSDisk
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_ReadSDisk
</UL>

<P><STRONG><a name="[e9]"></a>SD_ReadSDisk</STRONG> (Thumb, 76 bytes, Stack size 32 bytes, tfcard_sdio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INTX_ENABLE
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INTX_DISABLE
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_GetCardState
</UL>
<BR>[Called By]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>

<P><STRONG><a name="[ed]"></a>SD_WriteSDisk</STRONG> (Thumb, 76 bytes, Stack size 32 bytes, tfcard_sdio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INTX_ENABLE
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INTX_DISABLE
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_GetCardState
</UL>
<BR>[Called By]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>

<P><STRONG><a name="[ef]"></a>RTC_GetWeek</STRONG> (Thumb, 102 bytes, Stack size 20 bytes, rtc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = RTC_GetWeek
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_SetTimes
</UL>

<P><STRONG><a name="[9f]"></a>RTC_SetTimes</STRONG> (Thumb, 92 bytes, Stack size 56 bytes, rtc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = RTC_SetTimes &rArr; HAL_RTC_SetTime &rArr; RTC_EnterInitMode
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_GetWeek
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_InitConfig
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9b]"></a>RTC_GetTimes</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, rtc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = RTC_GetTimes &rArr; HAL_RTC_GetTime
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[96]"></a>RTC_InitConfig</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, rtc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = RTC_InitConfig &rArr; HAL_RTC_Init &rArr; HAL_RTC_MspInit &rArr; HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_SetTimes
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_BKUPWrite
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_BKUPRead
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f7]"></a>HAL_RTC_MspInit</STRONG> (Thumb, 84 bytes, Stack size 80 bytes, rtc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_RTC_MspInit &rArr; HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWR_EnableBkUpAccess
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
</UL>

<P><STRONG><a name="[fb]"></a>RTC_Set_WakeUp</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, rtc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_SetWakeUpTimer_IT
</UL>

<P><STRONG><a name="[d]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, rtc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RTC_WKUP_IRQHandler &rArr; HAL_RTCEx_WakeUpTimerIRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_WakeUpTimerIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1cf]"></a>HAL_RTCEx_WakeUpTimerEventCallback</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, rtc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_WakeUpTimerIRQHandler
</UL>

<P><STRONG><a name="[fe]"></a>RTC_SetAlarmA</STRONG> (Thumb, 84 bytes, Stack size 64 bytes, rtc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetAlarm_IT
</UL>

<P><STRONG><a name="[33]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, rtc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RTC_Alarm_IRQHandler &rArr; HAL_RTC_AlarmIRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_AlarmIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[102]"></a>GetMoonDay</STRONG> (Thumb, 282 bytes, Stack size 0 bytes, lunar_calendar.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetChinaCalendar
</UL>

<P><STRONG><a name="[101]"></a>GetChinaCalendar</STRONG> (Thumb, 506 bytes, Stack size 64 bytes, lunar_calendar.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = GetChinaCalendar
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetMoonDay
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetLunarCalendarStr
</UL>

<P><STRONG><a name="[104]"></a>GetSkyEarth</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, lunar_calendar.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = GetSkyEarth
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetLunarCalendarStr
</UL>

<P><STRONG><a name="[103]"></a>StrCopyss</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, lunar_calendar.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = StrCopyss
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetLunarCalendarStr
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetJieQiStr
</UL>

<P><STRONG><a name="[9d]"></a>GetLunarCalendarStr</STRONG> (Thumb, 296 bytes, Stack size 32 bytes, lunar_calendar.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = GetLunarCalendarStr &rArr; GetChinaCalendar
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StrCopyss
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSkyEarth
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetChinaCalendar
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[105]"></a>GetJieQi</STRONG> (Thumb, 158 bytes, Stack size 24 bytes, lunar_calendar.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = GetJieQi
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetJieQiStr
</UL>

<P><STRONG><a name="[9e]"></a>GetJieQiStr</STRONG> (Thumb, 300 bytes, Stack size 32 bytes, lunar_calendar.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = GetJieQiStr &rArr; GetJieQi
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetJieQi
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StrCopyss
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[106]"></a>HAL_InitTick</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, stm32f4xx_hal.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_DeInit
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SetTickFreq
</UL>

<P><STRONG><a name="[8f]"></a>HAL_Init</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, stm32f4xx_hal.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10a]"></a>HAL_DeInit</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, stm32f4xx_hal.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspDeInit
</UL>

<P><STRONG><a name="[a1]"></a>HAL_IncTick</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[10e]"></a>HAL_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_SendSDStatus
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSMC_NAND_GetECC
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_PollForTransfer
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_DeInit
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_DisablePLLI2S
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_EnablePLLI2S
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_PollForAlarmBEvent
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_SetSynchroShift
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_SetSmoothCalib
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_PollForWakeUpTimerEvent
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_DeactivateWakeUpTimer
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_SetWakeUpTimer
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_PollForTamper2Event
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_PollForTamper1Event
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_PollForTimeStampEvent
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_PollForAlarmAEvent
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_DeactivateAlarm
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetAlarm
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_DeInit
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_EnterInitMode
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_WaitForSynchro
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Receive
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Transmit
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMARxAbortCallback
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMATransmitReceiveCplt
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAReceiveCplt
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMATransmitCplt
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRxTx_ISR
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRx_ISR
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseTx_ISR
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
</UL>

<P><STRONG><a name="[237]"></a>HAL_GetTickPrio</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text), UNUSED)

<P><STRONG><a name="[10c]"></a>HAL_SetTickFreq</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, stm32f4xx_hal.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[238]"></a>HAL_GetTickFreq</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text), UNUSED)

<P><STRONG><a name="[10d]"></a>HAL_Delay</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, stm32f4xx_hal.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[239]"></a>HAL_SuspendTick</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text), UNUSED)

<P><STRONG><a name="[23a]"></a>HAL_ResumeTick</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text), UNUSED)

<P><STRONG><a name="[23b]"></a>HAL_GetHalVersion</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text), UNUSED)

<P><STRONG><a name="[21e]"></a>HAL_GetREVID</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stm32_Clock_Init
</UL>

<P><STRONG><a name="[23c]"></a>HAL_GetDEVID</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text), UNUSED)

<P><STRONG><a name="[23d]"></a>HAL_DBGMCU_EnableDBGSleepMode</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text), UNUSED)

<P><STRONG><a name="[23e]"></a>HAL_DBGMCU_DisableDBGSleepMode</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text), UNUSED)

<P><STRONG><a name="[23f]"></a>HAL_DBGMCU_EnableDBGStopMode</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text), UNUSED)

<P><STRONG><a name="[240]"></a>HAL_DBGMCU_DisableDBGStopMode</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text), UNUSED)

<P><STRONG><a name="[241]"></a>HAL_DBGMCU_EnableDBGStandbyMode</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text), UNUSED)

<P><STRONG><a name="[242]"></a>HAL_DBGMCU_DisableDBGStandbyMode</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text), UNUSED)

<P><STRONG><a name="[243]"></a>HAL_EnableCompensationCell</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text), UNUSED)

<P><STRONG><a name="[244]"></a>HAL_DisableCompensationCell</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text), UNUSED)

<P><STRONG><a name="[245]"></a>HAL_GetUIDw0</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text), UNUSED)

<P><STRONG><a name="[246]"></a>HAL_GetUIDw1</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text), UNUSED)

<P><STRONG><a name="[247]"></a>HAL_GetUIDw2</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text), UNUSED)

<P><STRONG><a name="[108]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[be]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 124 bytes, Stack size 40 bytes, stm32f4xx_hal_cortex.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_GetPriorityGrouping
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_SetAlarmA
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Set_WakeUp
</UL>

<P><STRONG><a name="[bd]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_SetAlarmA
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Set_WakeUp
</UL>

<P><STRONG><a name="[248]"></a>HAL_NVIC_DisableIRQ</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, stm32f4xx_hal_cortex.o(.text), UNUSED)

<P><STRONG><a name="[249]"></a>HAL_NVIC_SystemReset</STRONG> (Thumb, 64 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(.text), UNUSED)

<P><STRONG><a name="[107]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, stm32f4xx_hal_cortex.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_SYSTICK_Config &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[24a]"></a>HAL_MPU_Disable</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(.text), UNUSED)

<P><STRONG><a name="[24b]"></a>HAL_MPU_Enable</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(.text), UNUSED)

<P><STRONG><a name="[24c]"></a>HAL_MPU_ConfigRegion</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(.text), UNUSED)

<P><STRONG><a name="[111]"></a>HAL_NVIC_GetPriorityGrouping</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f4xx_hal_cortex.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_GetPriorityGrouping
</UL>

<P><STRONG><a name="[24d]"></a>HAL_NVIC_GetPriority</STRONG> (Thumb, 138 bytes, Stack size 28 bytes, stm32f4xx_hal_cortex.o(.text), UNUSED)

<P><STRONG><a name="[24e]"></a>HAL_NVIC_SetPendingIRQ</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32f4xx_hal_cortex.o(.text), UNUSED)

<P><STRONG><a name="[24f]"></a>HAL_NVIC_GetPendingIRQ</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, stm32f4xx_hal_cortex.o(.text), UNUSED)

<P><STRONG><a name="[250]"></a>HAL_NVIC_ClearPendingIRQ</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, stm32f4xx_hal_cortex.o(.text), UNUSED)

<P><STRONG><a name="[251]"></a>HAL_NVIC_GetActive</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, stm32f4xx_hal_cortex.o(.text), UNUSED)

<P><STRONG><a name="[21f]"></a>HAL_SYSTICK_CLKSourceConfig</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
</UL>

<P><STRONG><a name="[113]"></a>HAL_SYSTICK_Callback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_IRQHandler
</UL>

<P><STRONG><a name="[112]"></a>HAL_SYSTICK_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f4xx_hal_cortex.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Callback
</UL>

<P><STRONG><a name="[a2]"></a>HAL_GPIO_Init</STRONG> (Thumb, 466 bytes, Stack size 24 bytes, stm32f4xx_hal_gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_MspInit
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Init
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_MCOConfig
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_MspInit
</UL>

<P><STRONG><a name="[252]"></a>HAL_GPIO_DeInit</STRONG> (Thumb, 316 bytes, Stack size 20 bytes, stm32f4xx_hal_gpio.o(.text), UNUSED)

<P><STRONG><a name="[253]"></a>HAL_GPIO_ReadPin</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f4xx_hal_gpio.o(.text), UNUSED)

<P><STRONG><a name="[a3]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_hal_gpio.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
</UL>

<P><STRONG><a name="[254]"></a>HAL_GPIO_TogglePin</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_hal_gpio.o(.text), UNUSED)

<P><STRONG><a name="[255]"></a>HAL_GPIO_LockPin</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, stm32f4xx_hal_gpio.o(.text), UNUSED)

<P><STRONG><a name="[115]"></a>HAL_GPIO_EXTI_Callback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_gpio.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>

<P><STRONG><a name="[114]"></a>HAL_GPIO_EXTI_IRQHandler</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32f4xx_hal_gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_Callback
</UL>

<P><STRONG><a name="[256]"></a>HAL_PWR_DeInit</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_hal_pwr.o(.text), UNUSED)

<P><STRONG><a name="[f8]"></a>HAL_PWR_EnableBkUpAccess</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_hal_pwr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspInit
</UL>

<P><STRONG><a name="[257]"></a>HAL_PWR_DisableBkUpAccess</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_hal_pwr.o(.text), UNUSED)

<P><STRONG><a name="[258]"></a>HAL_PWR_ConfigPVD</STRONG> (Thumb, 172 bytes, Stack size 0 bytes, stm32f4xx_hal_pwr.o(.text), UNUSED)

<P><STRONG><a name="[259]"></a>HAL_PWR_EnablePVD</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_hal_pwr.o(.text), UNUSED)

<P><STRONG><a name="[25a]"></a>HAL_PWR_DisablePVD</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_hal_pwr.o(.text), UNUSED)

<P><STRONG><a name="[25b]"></a>HAL_PWR_EnableWakeUpPin</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_hal_pwr.o(.text), UNUSED)

<P><STRONG><a name="[25c]"></a>HAL_PWR_DisableWakeUpPin</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_hal_pwr.o(.text), UNUSED)

<P><STRONG><a name="[25d]"></a>HAL_PWR_EnterSLEEPMode</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, stm32f4xx_hal_pwr.o(.text), UNUSED)

<P><STRONG><a name="[25e]"></a>HAL_PWR_EnterSTOPMode</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, stm32f4xx_hal_pwr.o(.text), UNUSED)

<P><STRONG><a name="[25f]"></a>HAL_PWR_EnterSTANDBYMode</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f4xx_hal_pwr.o(.text), UNUSED)

<P><STRONG><a name="[117]"></a>HAL_PWR_PVDCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_pwr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWR_PVD_IRQHandler
</UL>

<P><STRONG><a name="[116]"></a>HAL_PWR_PVD_IRQHandler</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, stm32f4xx_hal_pwr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWR_PVDCallback
</UL>

<P><STRONG><a name="[260]"></a>HAL_PWR_EnableSleepOnExit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f4xx_hal_pwr.o(.text), UNUSED)

<P><STRONG><a name="[261]"></a>HAL_PWR_DisableSleepOnExit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f4xx_hal_pwr.o(.text), UNUSED)

<P><STRONG><a name="[262]"></a>HAL_PWR_EnableSEVOnPend</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f4xx_hal_pwr.o(.text), UNUSED)

<P><STRONG><a name="[263]"></a>HAL_PWR_DisableSEVOnPend</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f4xx_hal_pwr.o(.text), UNUSED)

<P><STRONG><a name="[f9]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 1086 bytes, Stack size 24 bytes, stm32f4xx_hal_rcc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stm32_Clock_Init
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspInit
</UL>

<P><STRONG><a name="[118]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 164 bytes, Stack size 32 bytes, stm32f4xx_hal_rcc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[11a]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 388 bytes, Stack size 16 bytes, stm32f4xx_hal_rcc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stm32_Clock_Init
</UL>

<P><STRONG><a name="[11b]"></a>HAL_RCC_MCOConfig</STRONG> (Thumb, 186 bytes, Stack size 40 bytes, stm32f4xx_hal_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>

<P><STRONG><a name="[264]"></a>HAL_RCC_EnableCSS</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(.text), UNUSED)

<P><STRONG><a name="[265]"></a>HAL_RCC_DisableCSS</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(.text), UNUSED)

<P><STRONG><a name="[11d]"></a>HAL_RCC_GetHCLKFreq</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
</UL>

<P><STRONG><a name="[11c]"></a>HAL_RCC_GetPCLK1Freq</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, stm32f4xx_hal_rcc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_RCC_GetPCLK1Freq
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[11e]"></a>HAL_RCC_GetPCLK2Freq</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, stm32f4xx_hal_rcc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_RCC_GetPCLK2Freq
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[266]"></a>HAL_RCC_GetOscConfig</STRONG> (Thumb, 278 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(.text), UNUSED)

<P><STRONG><a name="[267]"></a>HAL_RCC_GetClockConfig</STRONG> (Thumb, 66 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(.text), UNUSED)

<P><STRONG><a name="[120]"></a>HAL_RCC_CSSCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_NMI_IRQHandler
</UL>

<P><STRONG><a name="[11f]"></a>HAL_RCC_NMI_IRQHandler</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, stm32f4xx_hal_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_CSSCallback
</UL>

<P><STRONG><a name="[fa]"></a>HAL_RCCEx_PeriphCLKConfig</STRONG> (Thumb, 396 bytes, Stack size 24 bytes, stm32f4xx_hal_rcc_ex.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspInit
</UL>

<P><STRONG><a name="[268]"></a>HAL_RCCEx_GetPeriphCLKConfig</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc_ex.o(.text), UNUSED)

<P><STRONG><a name="[269]"></a>HAL_RCCEx_GetPeriphCLKFreq</STRONG> (Thumb, 128 bytes, Stack size 16 bytes, stm32f4xx_hal_rcc_ex.o(.text), UNUSED)

<P><STRONG><a name="[121]"></a>HAL_RCCEx_EnablePLLI2S</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, stm32f4xx_hal_rcc_ex.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>

<P><STRONG><a name="[122]"></a>HAL_RCCEx_DisablePLLI2S</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, stm32f4xx_hal_rcc_ex.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>

<P><STRONG><a name="[123]"></a>HAL_RCC_DeInit</STRONG> (Thumb, 398 bytes, Stack size 8 bytes, stm32f4xx_hal_rcc_ex.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[124]"></a>HAL_DMA_Init</STRONG> (Thumb, 232 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CheckFifoParam
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CalcBaseAndBitshift
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>

<P><STRONG><a name="[127]"></a>HAL_DMA_DeInit</STRONG> (Thumb, 112 bytes, Stack size 16 bytes, stm32f4xx_hal_dma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CalcBaseAndBitshift
</UL>

<P><STRONG><a name="[128]"></a>HAL_DMA_Start</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_SetConfig
</UL>

<P><STRONG><a name="[12a]"></a>HAL_DMA_Start_IT</STRONG> (Thumb, 158 bytes, Stack size 32 bytes, stm32f4xx_hal_dma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks_DMA
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks_DMA
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_DMA
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit_DMA
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_Write_DMA
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_Read_DMA
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Receive_DMA
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive_DMA
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Transmit_DMA
</UL>

<P><STRONG><a name="[12b]"></a>HAL_DMA_Abort</STRONG> (Thumb, 180 bytes, Stack size 16 bytes, stm32f4xx_hal_dma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Abort
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_AbortReceive
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_AbortTransmit
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Abort
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_PollForTransfer
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_DMAStop
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Abort
</UL>

<P><STRONG><a name="[152]"></a>HAL_DMA_Abort_IT</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Abort_IT
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_IRQHandler
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_AbortReceive_IT
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_AbortTransmit_IT
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Abort_IT
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_IRQHandler
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Abort_IT
</UL>

<P><STRONG><a name="[12c]"></a>HAL_DMA_PollForTransfer</STRONG> (Thumb, 346 bytes, Stack size 40 bytes, stm32f4xx_hal_dma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>

<P><STRONG><a name="[26a]"></a>HAL_DMA_IRQHandler</STRONG> (Thumb, 570 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(.text), UNUSED)

<P><STRONG><a name="[26b]"></a>HAL_DMA_RegisterCallback</STRONG> (Thumb, 94 bytes, Stack size 8 bytes, stm32f4xx_hal_dma.o(.text), UNUSED)

<P><STRONG><a name="[26c]"></a>HAL_DMA_UnRegisterCallback</STRONG> (Thumb, 124 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(.text), UNUSED)

<P><STRONG><a name="[26d]"></a>HAL_DMA_GetState</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(.text), UNUSED)

<P><STRONG><a name="[14d]"></a>HAL_DMA_GetError</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_AbortReceive
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_AbortTransmit
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Abort
</UL>

<P><STRONG><a name="[12e]"></a>FSMC_NORSRAM_Init</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, stm32f4xx_ll_fsmc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = FSMC_NORSRAM_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_Init
</UL>

<P><STRONG><a name="[133]"></a>FSMC_NORSRAM_DeInit</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_ll_fsmc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_DeInit
</UL>

<P><STRONG><a name="[12f]"></a>FSMC_NORSRAM_Timing_Init</STRONG> (Thumb, 68 bytes, Stack size 12 bytes, stm32f4xx_ll_fsmc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = FSMC_NORSRAM_Timing_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_Init
</UL>

<P><STRONG><a name="[130]"></a>FSMC_NORSRAM_Extended_Timing_Init</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, stm32f4xx_ll_fsmc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = FSMC_NORSRAM_Extended_Timing_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_Init
</UL>

<P><STRONG><a name="[137]"></a>FSMC_NORSRAM_WriteOperation_Enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_ll_fsmc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_WriteOperation_Enable
</UL>

<P><STRONG><a name="[139]"></a>FSMC_NORSRAM_WriteOperation_Disable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_ll_fsmc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_WriteOperation_Disable
</UL>

<P><STRONG><a name="[26e]"></a>FSMC_NAND_Init</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, stm32f4xx_ll_fsmc.o(.text), UNUSED)

<P><STRONG><a name="[26f]"></a>FSMC_NAND_CommonSpace_Timing_Init</STRONG> (Thumb, 54 bytes, Stack size 12 bytes, stm32f4xx_ll_fsmc.o(.text), UNUSED)

<P><STRONG><a name="[270]"></a>FSMC_NAND_AttributeSpace_Timing_Init</STRONG> (Thumb, 54 bytes, Stack size 12 bytes, stm32f4xx_ll_fsmc.o(.text), UNUSED)

<P><STRONG><a name="[271]"></a>FSMC_NAND_DeInit</STRONG> (Thumb, 66 bytes, Stack size 0 bytes, stm32f4xx_ll_fsmc.o(.text), UNUSED)

<P><STRONG><a name="[272]"></a>FSMC_NAND_ECC_Enable</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, stm32f4xx_ll_fsmc.o(.text), UNUSED)

<P><STRONG><a name="[273]"></a>FSMC_NAND_ECC_Disable</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, stm32f4xx_ll_fsmc.o(.text), UNUSED)

<P><STRONG><a name="[12d]"></a>FSMC_NAND_GetECC</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, stm32f4xx_ll_fsmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>

<P><STRONG><a name="[274]"></a>FSMC_PCCARD_Init</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32f4xx_ll_fsmc.o(.text), UNUSED)

<P><STRONG><a name="[275]"></a>FSMC_PCCARD_CommonSpace_Timing_Init</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32f4xx_ll_fsmc.o(.text), UNUSED)

<P><STRONG><a name="[276]"></a>FSMC_PCCARD_AttributeSpace_Timing_Init</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32f4xx_ll_fsmc.o(.text), UNUSED)

<P><STRONG><a name="[277]"></a>FSMC_PCCARD_IOSpace_Timing_Init</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32f4xx_ll_fsmc.o(.text), UNUSED)

<P><STRONG><a name="[278]"></a>FSMC_PCCARD_DeInit</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f4xx_ll_fsmc.o(.text), UNUSED)

<P><STRONG><a name="[b3]"></a>HAL_SRAM_Init</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, stm32f4xx_hal_sram.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_SRAM_Init &rArr; HAL_SRAM_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_MspInit
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSMC_NORSRAM_Extended_Timing_Init
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSMC_NORSRAM_Timing_Init
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSMC_NORSRAM_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_FSMC_Config
</UL>

<P><STRONG><a name="[132]"></a>HAL_SRAM_MspDeInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_sram.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_DeInit
</UL>

<P><STRONG><a name="[131]"></a>HAL_SRAM_DeInit</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, stm32f4xx_hal_sram.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_MspDeInit
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSMC_NORSRAM_DeInit
</UL>

<P><STRONG><a name="[5e]"></a>HAL_SRAM_DMA_XferCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_sram.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_sram.o(.text)
</UL>
<P><STRONG><a name="[5f]"></a>HAL_SRAM_DMA_XferErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_sram.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_sram.o(.text)
</UL>
<P><STRONG><a name="[279]"></a>HAL_SRAM_Read_8b</STRONG> (Thumb, 70 bytes, Stack size 12 bytes, stm32f4xx_hal_sram.o(.text), UNUSED)

<P><STRONG><a name="[27a]"></a>HAL_SRAM_Write_8b</STRONG> (Thumb, 82 bytes, Stack size 12 bytes, stm32f4xx_hal_sram.o(.text), UNUSED)

<P><STRONG><a name="[27b]"></a>HAL_SRAM_Read_16b</STRONG> (Thumb, 70 bytes, Stack size 12 bytes, stm32f4xx_hal_sram.o(.text), UNUSED)

<P><STRONG><a name="[27c]"></a>HAL_SRAM_Write_16b</STRONG> (Thumb, 82 bytes, Stack size 12 bytes, stm32f4xx_hal_sram.o(.text), UNUSED)

<P><STRONG><a name="[27d]"></a>HAL_SRAM_Read_32b</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, stm32f4xx_hal_sram.o(.text), UNUSED)

<P><STRONG><a name="[27e]"></a>HAL_SRAM_Write_32b</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, stm32f4xx_hal_sram.o(.text), UNUSED)

<P><STRONG><a name="[134]"></a>HAL_SRAM_Read_DMA</STRONG> (Thumb, 86 bytes, Stack size 24 bytes, stm32f4xx_hal_sram.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>

<P><STRONG><a name="[135]"></a>HAL_SRAM_Write_DMA</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, stm32f4xx_hal_sram.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>

<P><STRONG><a name="[136]"></a>HAL_SRAM_WriteOperation_Enable</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, stm32f4xx_hal_sram.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSMC_NORSRAM_WriteOperation_Enable
</UL>

<P><STRONG><a name="[138]"></a>HAL_SRAM_WriteOperation_Disable</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, stm32f4xx_hal_sram.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSMC_NORSRAM_WriteOperation_Disable
</UL>

<P><STRONG><a name="[27f]"></a>HAL_SRAM_GetState</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_hal_sram.o(.text), UNUSED)

<P><STRONG><a name="[bf]"></a>HAL_UART_Init</STRONG> (Thumb, 114 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1_init
</UL>

<P><STRONG><a name="[13b]"></a>HAL_HalfDuplex_Init</STRONG> (Thumb, 128 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[13c]"></a>HAL_LIN_Init</STRONG> (Thumb, 142 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[13d]"></a>HAL_MultiProcessor_Init</STRONG> (Thumb, 154 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[13f]"></a>HAL_UART_MspDeInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DeInit
</UL>

<P><STRONG><a name="[13e]"></a>HAL_UART_DeInit</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspDeInit
</UL>

<P><STRONG><a name="[141]"></a>HAL_UART_Transmit</STRONG> (Thumb, 214 bytes, Stack size 32 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>

<P><STRONG><a name="[142]"></a>HAL_UART_Receive</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>

<P><STRONG><a name="[280]"></a>HAL_UART_Transmit_IT</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)

<P><STRONG><a name="[c0]"></a>HAL_UART_Receive_IT</STRONG> (Thumb, 112 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_UART_Receive_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1_init
</UL>

<P><STRONG><a name="[145]"></a>HAL_UART_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAAbortOnError
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[146]"></a>HAL_UART_TxHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMATxHalfCplt
</UL>

<P><STRONG><a name="[147]"></a>HAL_UART_TxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTransmit_IT
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMATransmitCplt
</UL>

<P><STRONG><a name="[148]"></a>HAL_UART_Transmit_DMA</STRONG> (Thumb, 142 bytes, Stack size 32 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>

<P><STRONG><a name="[149]"></a>HAL_UART_RxHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
</UL>

<P><STRONG><a name="[14a]"></a>HAL_UART_Receive_DMA</STRONG> (Thumb, 182 bytes, Stack size 32 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>

<P><STRONG><a name="[281]"></a>HAL_UART_DMAPause</STRONG> (Thumb, 124 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)

<P><STRONG><a name="[282]"></a>HAL_UART_DMAResume</STRONG> (Thumb, 126 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)

<P><STRONG><a name="[14b]"></a>HAL_UART_DMAStop</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
</UL>

<P><STRONG><a name="[14c]"></a>HAL_UART_Abort</STRONG> (Thumb, 170 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_GetError
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
</UL>

<P><STRONG><a name="[14e]"></a>HAL_UART_AbortTransmit</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_GetError
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
</UL>

<P><STRONG><a name="[14f]"></a>HAL_UART_AbortReceive</STRONG> (Thumb, 124 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_GetError
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
</UL>

<P><STRONG><a name="[150]"></a>HAL_UART_AbortCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Abort_IT
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMATxAbortCallback
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxAbortCallback
</UL>

<P><STRONG><a name="[151]"></a>HAL_UART_Abort_IT</STRONG> (Thumb, 216 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_AbortCpltCallback
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
</UL>

<P><STRONG><a name="[153]"></a>HAL_UART_AbortTransmitCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_AbortTransmit_IT
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMATxOnlyAbortCallback
</UL>

<P><STRONG><a name="[154]"></a>HAL_UART_AbortTransmit_IT</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_AbortTransmitCpltCallback
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
</UL>

<P><STRONG><a name="[155]"></a>HAL_UART_AbortReceiveCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_AbortReceive_IT
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxOnlyAbortCallback
</UL>

<P><STRONG><a name="[156]"></a>HAL_UART_AbortReceive_IT</STRONG> (Thumb, 116 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_AbortReceiveCpltCallback
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
</UL>

<P><STRONG><a name="[c3]"></a>HAL_UART_IRQHandler</STRONG> (Thumb, 354 bytes, Stack size 32 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UART_RxCpltCallback &rArr; strcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Transmit_IT
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTransmit_IT
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[283]"></a>HAL_LIN_SendBreak</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)

<P><STRONG><a name="[284]"></a>HAL_MultiProcessor_EnterMuteMode</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)

<P><STRONG><a name="[285]"></a>HAL_MultiProcessor_ExitMuteMode</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)

<P><STRONG><a name="[286]"></a>HAL_HalfDuplex_EnableTransmitter</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)

<P><STRONG><a name="[287]"></a>HAL_HalfDuplex_EnableReceiver</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)

<P><STRONG><a name="[c4]"></a>HAL_UART_GetState</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[288]"></a>HAL_UART_GetError</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)

<P><STRONG><a name="[160]"></a>HAL_SD_GetCardCSD</STRONG> (Thumb, 496 bytes, Stack size 12 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[168]"></a>HAL_SD_InitCard</STRONG> (Thumb, 128 bytes, Stack size 48 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_PowerState_ON
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_Init
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Init
</UL>

<P><STRONG><a name="[e2]"></a>HAL_SD_Init</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_Init
</UL>

<P><STRONG><a name="[16d]"></a>HAL_SD_MspDeInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_DeInit
</UL>

<P><STRONG><a name="[16c]"></a>HAL_SD_DeInit</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerOFF
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_MspDeInit
</UL>

<P><STRONG><a name="[eb]"></a>HAL_SD_ReadBlocks</STRONG> (Thumb, 588 bytes, Stack size 64 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadSingleBlock
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadMultiBlock
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ReadFIFO
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ConfigData
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_ReadSDisk
</UL>

<P><STRONG><a name="[ee]"></a>HAL_SD_WriteBlocks</STRONG> (Thumb, 522 bytes, Stack size 64 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_WriteFIFO
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ConfigData
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteSingleBlock
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteMultiBlock
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WriteSDisk
</UL>

<P><STRONG><a name="[177]"></a>HAL_SD_ReadBlocks_IT</STRONG> (Thumb, 248 bytes, Stack size 48 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadSingleBlock
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadMultiBlock
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ConfigData
</UL>

<P><STRONG><a name="[178]"></a>HAL_SD_WriteBlocks_IT</STRONG> (Thumb, 246 bytes, Stack size 48 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ConfigData
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteSingleBlock
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteMultiBlock
</UL>

<P><STRONG><a name="[17b]"></a>HAL_SD_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_sd.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_IRQHandler
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_DMATxAbort
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_DMARxAbort
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_DMAReceiveCplt
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_DMAError
</UL>

<P><STRONG><a name="[e8]"></a>HAL_SD_GetCardState</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, stm32f4xx_hal_sd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_SD_GetCardState &rArr; SD_SendStatus &rArr; SDMMC_CmdSendStatus &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_SendStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Abort_IT
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Abort
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_DMATxAbort
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_DMARxAbort
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_DMAError
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_GetCardState
</UL>

<P><STRONG><a name="[17c]"></a>HAL_SD_RxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_sd.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_IRQHandler
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_DMAReceiveCplt
</UL>

<P><STRONG><a name="[17d]"></a>HAL_SD_ReadBlocks_DMA</STRONG> (Thumb, 284 bytes, Stack size 48 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadSingleBlock
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadMultiBlock
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ConfigData
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>

<P><STRONG><a name="[17e]"></a>HAL_SD_WriteBlocks_DMA</STRONG> (Thumb, 282 bytes, Stack size 48 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ConfigData
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteSingleBlock
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteMultiBlock
</UL>

<P><STRONG><a name="[17f]"></a>HAL_SD_Erase</STRONG> (Thumb, 274 bytes, Stack size 24 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSDEraseStartAdd
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSDEraseEndAdd
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdErase
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
</UL>

<P><STRONG><a name="[183]"></a>HAL_SD_AbortCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_sd.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Abort_IT
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_IRQHandler
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_DMATxAbort
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_DMARxAbort
</UL>

<P><STRONG><a name="[187]"></a>HAL_SD_TxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_IRQHandler
</UL>

<P><STRONG><a name="[186]"></a>HAL_SD_IRQHandler</STRONG> (Thumb, 504 bytes, Stack size 16 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_Write_IT
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_Read_IT
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_DMATxAbort
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_DMARxAbort
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_TxCpltCallback
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_AbortCallback
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_RxCpltCallback
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ErrorCallback
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
</UL>

<P><STRONG><a name="[289]"></a>HAL_SD_GetState</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)

<P><STRONG><a name="[28a]"></a>HAL_SD_GetError</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)

<P><STRONG><a name="[28b]"></a>HAL_SD_GetCardCID</STRONG> (Thumb, 204 bytes, Stack size 8 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)

<P><STRONG><a name="[18a]"></a>HAL_SD_GetCardStatus</STRONG> (Thumb, 202 bytes, Stack size 88 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_SendSDStatus
</UL>

<P><STRONG><a name="[e3]"></a>HAL_SD_GetCardInfo</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_GetCardInfo
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_Init
</UL>

<P><STRONG><a name="[e4]"></a>HAL_SD_ConfigWideBusOperation</STRONG> (Thumb, 168 bytes, Stack size 56 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_Init
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WideBus_Enable
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WideBus_Disable
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_Init
</UL>

<P><STRONG><a name="[190]"></a>HAL_SD_Abort</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
</UL>

<P><STRONG><a name="[191]"></a>HAL_SD_Abort_IT</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_AbortCallback
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
</UL>

<P><STRONG><a name="[162]"></a>SDIO_Init</STRONG> (Thumb, 52 bytes, Stack size 24 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
</UL>

<P><STRONG><a name="[172]"></a>SDIO_ReadFIFO</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_SendSDStatus
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_Read_IT
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>

<P><STRONG><a name="[176]"></a>SDIO_WriteFIFO</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_Write_IT
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
</UL>

<P><STRONG><a name="[169]"></a>SDIO_PowerState_ON</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[16b]"></a>SDIO_PowerState_OFF</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerOFF
</UL>

<P><STRONG><a name="[15b]"></a>SDIO_GetPowerState</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[194]"></a>SDIO_SendCommand</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, stm32f4xx_ll_sdmmc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendSCR
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCSD
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCID
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSelDesel
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSDEraseStartAdd
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSDEraseEndAdd
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadSingleBlock
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadMultiBlock
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdOperCond
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdGoIdleState
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdErase
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBusWidth
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppOperCommand
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSwitch
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdOpCondition
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdEraseEndAdd
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdEraseStartAdd
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteSingleBlock
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteMultiBlock
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStatusRegister
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSetRelAdd
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendStatus
</UL>

<P><STRONG><a name="[193]"></a>SDIO_GetCommandResponse</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp6
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>

<P><STRONG><a name="[15d]"></a>SDIO_GetResponse</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, stm32f4xx_ll_sdmmc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SDIO_GetResponse
</UL>
<BR>[Called By]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Erase
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WideBus_Enable
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WideBus_Disable
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_SendSDStatus
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_SendStatus
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp6
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>

<P><STRONG><a name="[16f]"></a>SDIO_ConfigData</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks_DMA
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks_DMA
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks_IT
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks_IT
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_SendSDStatus
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>

<P><STRONG><a name="[28c]"></a>SDIO_GetDataCounter</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)

<P><STRONG><a name="[28d]"></a>SDIO_GetFIFOCount</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)

<P><STRONG><a name="[28e]"></a>SDIO_SetSDMMCReadWaitMode</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)

<P><STRONG><a name="[16e]"></a>SDMMC_CmdBlockLength</STRONG> (Thumb, 58 bytes, Stack size 40 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks_DMA
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks_DMA
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks_IT
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks_IT
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_SendSDStatus
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>

<P><STRONG><a name="[171]"></a>SDMMC_CmdReadSingleBlock</STRONG> (Thumb, 58 bytes, Stack size 40 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks_DMA
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks_IT
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>

<P><STRONG><a name="[170]"></a>SDMMC_CmdReadMultiBlock</STRONG> (Thumb, 58 bytes, Stack size 40 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks_DMA
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks_IT
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>

<P><STRONG><a name="[175]"></a>SDMMC_CmdWriteSingleBlock</STRONG> (Thumb, 58 bytes, Stack size 40 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks_DMA
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks_IT
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
</UL>

<P><STRONG><a name="[174]"></a>SDMMC_CmdWriteMultiBlock</STRONG> (Thumb, 58 bytes, Stack size 40 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks_DMA
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks_IT
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
</UL>

<P><STRONG><a name="[180]"></a>SDMMC_CmdSDEraseStartAdd</STRONG> (Thumb, 58 bytes, Stack size 40 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Erase
</UL>

<P><STRONG><a name="[181]"></a>SDMMC_CmdSDEraseEndAdd</STRONG> (Thumb, 58 bytes, Stack size 40 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Erase
</UL>

<P><STRONG><a name="[195]"></a>SDMMC_CmdEraseStartAdd</STRONG> (Thumb, 58 bytes, Stack size 40 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>

<P><STRONG><a name="[196]"></a>SDMMC_CmdEraseEndAdd</STRONG> (Thumb, 58 bytes, Stack size 40 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>

<P><STRONG><a name="[182]"></a>SDMMC_CmdErase</STRONG> (Thumb, 58 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Erase
</UL>

<P><STRONG><a name="[173]"></a>SDMMC_CmdStopTransfer</STRONG> (Thumb, 66 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = SDMMC_CmdStopTransfer &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Abort_IT
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Abort
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_IRQHandler
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_DMATxAbort
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_DMARxAbort
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_DMAReceiveCplt
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_DMAError
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>

<P><STRONG><a name="[161]"></a>SDMMC_CmdSelDesel</STRONG> (Thumb, 60 bytes, Stack size 40 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[164]"></a>SDMMC_CmdGoIdleState</STRONG> (Thumb, 46 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdError
</UL>
<BR>[Called By]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
</UL>

<P><STRONG><a name="[165]"></a>SDMMC_CmdOperCond</STRONG> (Thumb, 54 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp7
</UL>
<BR>[Called By]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
</UL>

<P><STRONG><a name="[166]"></a>SDMMC_CmdAppCommand</STRONG> (Thumb, 58 bytes, Stack size 40 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WideBus_Enable
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WideBus_Disable
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_SendSDStatus
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
</UL>

<P><STRONG><a name="[167]"></a>SDMMC_CmdAppOperCommand</STRONG> (Thumb, 56 bytes, Stack size 40 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp3
</UL>
<BR>[Called By]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
</UL>

<P><STRONG><a name="[18e]"></a>SDMMC_CmdBusWidth</STRONG> (Thumb, 58 bytes, Stack size 40 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WideBus_Enable
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WideBus_Disable
</UL>

<P><STRONG><a name="[18c]"></a>SDMMC_CmdSendSCR</STRONG> (Thumb, 58 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[15c]"></a>SDMMC_CmdSendCID</STRONG> (Thumb, 52 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp2
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[15f]"></a>SDMMC_CmdSendCSD</STRONG> (Thumb, 52 bytes, Stack size 40 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp2
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[15e]"></a>SDMMC_CmdSetRelAdd</STRONG> (Thumb, 58 bytes, Stack size 40 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp6
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[17a]"></a>SDMMC_CmdSendStatus</STRONG> (Thumb, 58 bytes, Stack size 40 bytes, stm32f4xx_ll_sdmmc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = SDMMC_CmdSendStatus &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_SendStatus
</UL>

<P><STRONG><a name="[189]"></a>SDMMC_CmdStatusRegister</STRONG> (Thumb, 58 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_SendSDStatus
</UL>

<P><STRONG><a name="[19c]"></a>SDMMC_CmdOpCondition</STRONG> (Thumb, 52 bytes, Stack size 40 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp3
</UL>

<P><STRONG><a name="[19d]"></a>SDMMC_CmdSwitch</STRONG> (Thumb, 58 bytes, Stack size 40 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>

<P><STRONG><a name="[d0]"></a>HAL_SPI_Init</STRONG> (Thumb, 160 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
</UL>

<P><STRONG><a name="[19f]"></a>HAL_SPI_MspDeInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_DeInit
</UL>

<P><STRONG><a name="[19e]"></a>HAL_SPI_DeInit</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspDeInit
</UL>

<P><STRONG><a name="[1a2]"></a>HAL_SPI_Transmit</STRONG> (Thumb, 474 bytes, Stack size 40 bytes, stm32f4xx_hal_spi.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
</UL>

<P><STRONG><a name="[ce]"></a>HAL_SPI_TransmitReceive</STRONG> (Thumb, 722 bytes, Stack size 56 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Receive
</UL>

<P><STRONG><a name="[1a4]"></a>HAL_SPI_Receive</STRONG> (Thumb, 528 bytes, Stack size 32 bytes, stm32f4xx_hal_spi.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTransaction
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
</UL>

<P><STRONG><a name="[1a7]"></a>HAL_SPI_TxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMATransmitCplt
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseTx_ISR
</UL>

<P><STRONG><a name="[1a6]"></a>HAL_SPI_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_IRQHandler
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAAbortOnError
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMATransmitReceiveCplt
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAReceiveCplt
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMATransmitCplt
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAError
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRxTx_ISR
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRx_ISR
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseTx_ISR
</UL>

<P><STRONG><a name="[28f]"></a>HAL_SPI_Transmit_IT</STRONG> (Thumb, 194 bytes, Stack size 12 bytes, stm32f4xx_hal_spi.o(.text), UNUSED)

<P><STRONG><a name="[1a9]"></a>HAL_SPI_RxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAReceiveCplt
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRxTx_ISR
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRx_ISR
</UL>

<P><STRONG><a name="[1ab]"></a>HAL_SPI_TxRxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMATransmitReceiveCplt
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRxTx_ISR
</UL>

<P><STRONG><a name="[1ad]"></a>HAL_SPI_TransmitReceive_IT</STRONG> (Thumb, 222 bytes, Stack size 20 bytes, stm32f4xx_hal_spi.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Receive_IT
</UL>

<P><STRONG><a name="[1ac]"></a>HAL_SPI_Receive_IT</STRONG> (Thumb, 230 bytes, Stack size 20 bytes, stm32f4xx_hal_spi.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive_IT
</UL>

<P><STRONG><a name="[1ae]"></a>HAL_SPI_TxHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAHalfTransmitCplt
</UL>

<P><STRONG><a name="[1af]"></a>HAL_SPI_Transmit_DMA</STRONG> (Thumb, 310 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>

<P><STRONG><a name="[1b0]"></a>HAL_SPI_RxHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAHalfReceiveCplt
</UL>

<P><STRONG><a name="[1b1]"></a>HAL_SPI_TxRxHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAHalfTransmitReceiveCplt
</UL>

<P><STRONG><a name="[1b2]"></a>HAL_SPI_TransmitReceive_DMA</STRONG> (Thumb, 378 bytes, Stack size 32 bytes, stm32f4xx_hal_spi.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Receive_DMA
</UL>

<P><STRONG><a name="[1b3]"></a>HAL_SPI_Receive_DMA</STRONG> (Thumb, 294 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive_DMA
</UL>

<P><STRONG><a name="[1b4]"></a>HAL_SPI_Abort</STRONG> (Thumb, 388 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
</UL>

<P><STRONG><a name="[1b5]"></a>HAL_SPI_AbortCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Abort_IT
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMATxAbortCallback
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMARxAbortCallback
</UL>

<P><STRONG><a name="[1b6]"></a>HAL_SPI_Abort_IT</STRONG> (Thumb, 362 bytes, Stack size 32 bytes, stm32f4xx_hal_spi.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_AbortCpltCallback
</UL>

<P><STRONG><a name="[290]"></a>HAL_SPI_DMAPause</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text), UNUSED)

<P><STRONG><a name="[291]"></a>HAL_SPI_DMAResume</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text), UNUSED)

<P><STRONG><a name="[1b7]"></a>HAL_SPI_DMAStop</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
</UL>

<P><STRONG><a name="[1b8]"></a>HAL_SPI_IRQHandler</STRONG> (Thumb, 372 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
</UL>

<P><STRONG><a name="[292]"></a>HAL_SPI_GetState</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text), UNUSED)

<P><STRONG><a name="[293]"></a>HAL_SPI_GetError</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text), UNUSED)

<P><STRONG><a name="[1b9]"></a>HAL_RTC_WaitForSynchro</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_RTC_WaitForSynchro
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_SetSynchroShift
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_DeInit
</UL>

<P><STRONG><a name="[1ba]"></a>RTC_EnterInitMode</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RTC_EnterInitMode
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_DeactivateRefClock
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_SetRefClock
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_DeactivateCoarseCalib
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_SetCoarseCalib
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_DeInit
</UL>

<P><STRONG><a name="[f4]"></a>HAL_RTC_Init</STRONG> (Thumb, 212 bytes, Stack size 8 bytes, stm32f4xx_hal_rtc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_RTC_Init &rArr; HAL_RTC_MspInit &rArr; HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspInit
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_EnterInitMode
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_WaitForSynchro
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_InitConfig
</UL>

<P><STRONG><a name="[1bc]"></a>HAL_RTC_MspDeInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_rtc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_DeInit
</UL>

<P><STRONG><a name="[1bb]"></a>HAL_RTC_DeInit</STRONG> (Thumb, 254 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspDeInit
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_EnterInitMode
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_WaitForSynchro
</UL>

<P><STRONG><a name="[1bd]"></a>RTC_ByteToBcd2</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_hal_rtc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetAlarm_IT
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetAlarm
</UL>

<P><STRONG><a name="[f1]"></a>HAL_RTC_SetTime</STRONG> (Thumb, 296 bytes, Stack size 24 bytes, stm32f4xx_hal_rtc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_RTC_SetTime &rArr; RTC_EnterInitMode
</UL>
<BR>[Calls]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_ByteToBcd2
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_EnterInitMode
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_WaitForSynchro
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_SetTimes
</UL>

<P><STRONG><a name="[1be]"></a>RTC_Bcd2ToByte</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32f4xx_hal_rtc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_GetTimeStamp
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetAlarm
</UL>

<P><STRONG><a name="[f3]"></a>HAL_RTC_GetTime</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_RTC_GetTime
</UL>
<BR>[Calls]<UL><LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Bcd2ToByte
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_GetTimes
</UL>

<P><STRONG><a name="[f0]"></a>HAL_RTC_SetDate</STRONG> (Thumb, 268 bytes, Stack size 24 bytes, stm32f4xx_hal_rtc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_RTC_SetDate &rArr; RTC_EnterInitMode
</UL>
<BR>[Calls]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_ByteToBcd2
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_EnterInitMode
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_WaitForSynchro
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_SetTimes
</UL>

<P><STRONG><a name="[f2]"></a>HAL_RTC_GetDate</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_RTC_GetDate
</UL>
<BR>[Calls]<UL><LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Bcd2ToByte
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_GetTimes
</UL>

<P><STRONG><a name="[1bf]"></a>HAL_RTC_SetAlarm</STRONG> (Thumb, 460 bytes, Stack size 32 bytes, stm32f4xx_hal_rtc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_ByteToBcd2
</UL>

<P><STRONG><a name="[ff]"></a>HAL_RTC_SetAlarm_IT</STRONG> (Thumb, 522 bytes, Stack size 28 bytes, stm32f4xx_hal_rtc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_ByteToBcd2
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_SetAlarmA
</UL>

<P><STRONG><a name="[1c0]"></a>HAL_RTC_DeactivateAlarm</STRONG> (Thumb, 244 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>

<P><STRONG><a name="[1c1]"></a>HAL_RTC_GetAlarm</STRONG> (Thumb, 154 bytes, Stack size 24 bytes, stm32f4xx_hal_rtc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Bcd2ToByte
</UL>

<P><STRONG><a name="[1c2]"></a>HAL_RTC_AlarmAEventCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_rtc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_AlarmIRQHandler
</UL>

<P><STRONG><a name="[100]"></a>HAL_RTC_AlarmIRQHandler</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, stm32f4xx_hal_rtc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_RTC_AlarmIRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_AlarmAEventCallback
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_AlarmBEventCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Alarm_IRQHandler
</UL>

<P><STRONG><a name="[1c4]"></a>HAL_RTC_PollForAlarmAEvent</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>

<P><STRONG><a name="[294]"></a>HAL_RTC_GetState</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal_rtc.o(.text), UNUSED)

<P><STRONG><a name="[295]"></a>HAL_RTCEx_SetTimeStamp</STRONG> (Thumb, 122 bytes, Stack size 12 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)

<P><STRONG><a name="[296]"></a>HAL_RTCEx_SetTimeStamp_IT</STRONG> (Thumb, 178 bytes, Stack size 12 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)

<P><STRONG><a name="[297]"></a>HAL_RTCEx_DeactivateTimeStamp</STRONG> (Thumb, 96 bytes, Stack size 0 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)

<P><STRONG><a name="[1c5]"></a>HAL_RTCEx_GetTimeStamp</STRONG> (Thumb, 166 bytes, Stack size 32 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Bcd2ToByte
</UL>

<P><STRONG><a name="[298]"></a>HAL_RTCEx_SetTamper</STRONG> (Thumb, 124 bytes, Stack size 8 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)

<P><STRONG><a name="[299]"></a>HAL_RTCEx_SetTamper_IT</STRONG> (Thumb, 200 bytes, Stack size 8 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)

<P><STRONG><a name="[29a]"></a>HAL_RTCEx_DeactivateTamper</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)

<P><STRONG><a name="[1c9]"></a>HAL_RTCEx_Tamper2EventCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_TamperTimeStampIRQHandler
</UL>

<P><STRONG><a name="[1c8]"></a>HAL_RTCEx_Tamper1EventCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_TamperTimeStampIRQHandler
</UL>

<P><STRONG><a name="[1c7]"></a>HAL_RTCEx_TimeStampEventCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_TamperTimeStampIRQHandler
</UL>

<P><STRONG><a name="[1c6]"></a>HAL_RTCEx_TamperTimeStampIRQHandler</STRONG> (Thumb, 146 bytes, Stack size 8 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_TimeStampEventCallback
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_Tamper1EventCallback
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_Tamper2EventCallback
</UL>

<P><STRONG><a name="[1ca]"></a>HAL_RTCEx_PollForTimeStampEvent</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>

<P><STRONG><a name="[1cb]"></a>HAL_RTCEx_PollForTamper1Event</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>

<P><STRONG><a name="[1cc]"></a>HAL_RTCEx_PollForTamper2Event</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>

<P><STRONG><a name="[1cd]"></a>HAL_RTCEx_SetWakeUpTimer</STRONG> (Thumb, 254 bytes, Stack size 24 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>

<P><STRONG><a name="[fc]"></a>HAL_RTCEx_SetWakeUpTimer_IT</STRONG> (Thumb, 316 bytes, Stack size 12 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Set_WakeUp
</UL>

<P><STRONG><a name="[1ce]"></a>HAL_RTCEx_DeactivateWakeUpTimer</STRONG> (Thumb, 152 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>

<P><STRONG><a name="[29b]"></a>HAL_RTCEx_GetWakeUpTimer</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)

<P><STRONG><a name="[fd]"></a>HAL_RTCEx_WakeUpTimerIRQHandler</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, stm32f4xx_hal_rtc_ex.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_RTCEx_WakeUpTimerIRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_WakeUpTimerEventCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_WKUP_IRQHandler
</UL>

<P><STRONG><a name="[1d0]"></a>HAL_RTCEx_PollForWakeUpTimerEvent</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>

<P><STRONG><a name="[f6]"></a>HAL_RTCEx_BKUPWrite</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, stm32f4xx_hal_rtc_ex.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_RTCEx_BKUPWrite
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_InitConfig
</UL>

<P><STRONG><a name="[f5]"></a>HAL_RTCEx_BKUPRead</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_hal_rtc_ex.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_InitConfig
</UL>

<P><STRONG><a name="[1d1]"></a>HAL_RTCEx_SetCoarseCalib</STRONG> (Thumb, 138 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_EnterInitMode
</UL>

<P><STRONG><a name="[1d2]"></a>HAL_RTCEx_DeactivateCoarseCalib</STRONG> (Thumb, 126 bytes, Stack size 8 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_EnterInitMode
</UL>

<P><STRONG><a name="[1d3]"></a>HAL_RTCEx_SetSmoothCalib</STRONG> (Thumb, 172 bytes, Stack size 24 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>

<P><STRONG><a name="[1d4]"></a>HAL_RTCEx_SetSynchroShift</STRONG> (Thumb, 224 bytes, Stack size 24 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_WaitForSynchro
</UL>

<P><STRONG><a name="[29c]"></a>HAL_RTCEx_SetCalibrationOutPut</STRONG> (Thumb, 100 bytes, Stack size 0 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)

<P><STRONG><a name="[29d]"></a>HAL_RTCEx_DeactivateCalibrationOutPut</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)

<P><STRONG><a name="[1d5]"></a>HAL_RTCEx_SetRefClock</STRONG> (Thumb, 126 bytes, Stack size 8 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_EnterInitMode
</UL>

<P><STRONG><a name="[1d6]"></a>HAL_RTCEx_DeactivateRefClock</STRONG> (Thumb, 126 bytes, Stack size 8 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_EnterInitMode
</UL>

<P><STRONG><a name="[29e]"></a>HAL_RTCEx_EnableBypassShadow</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)

<P><STRONG><a name="[29f]"></a>HAL_RTCEx_DisableBypassShadow</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)

<P><STRONG><a name="[1c3]"></a>HAL_RTCEx_AlarmBEventCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_rtc_ex.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_AlarmIRQHandler
</UL>

<P><STRONG><a name="[1d7]"></a>HAL_RTCEx_PollForAlarmBEvent</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc_ex.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>

<P><STRONG><a name="[1d8]"></a>disk_initialize</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, diskio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Init
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>

<P><STRONG><a name="[1ff]"></a>disk_status</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, diskio.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>

<P><STRONG><a name="[1d9]"></a>disk_read</STRONG> (Thumb, 122 bytes, Stack size 24 bytes, diskio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_ReadSDisk
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_Init
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>

<P><STRONG><a name="[1da]"></a>disk_write</STRONG> (Thumb, 122 bytes, Stack size 24 bytes, diskio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WriteSDisk
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_Init
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_SectorWrite
</UL>
<BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_truncate
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
</UL>

<P><STRONG><a name="[1e1]"></a>disk_ioctl</STRONG> (Thumb, 124 bytes, Stack size 8 bytes, diskio.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
</UL>

<P><STRONG><a name="[203]"></a>get_fattime</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, diskio.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_setlabel
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[1db]"></a>ff_memalloc</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, diskio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mem_malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_utime
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_chmod
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_stat
<LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[1dc]"></a>ff_memfree</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, diskio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mem_free
</UL>
<BR>[Called By]<UL><LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_utime
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_chmod
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_stat
<LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[1e7]"></a>clust2sect</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, ff.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
</UL>

<P><STRONG><a name="[1e2]"></a>get_fat</STRONG> (Thumb, 232 bytes, Stack size 24 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_truncate
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
</UL>

<P><STRONG><a name="[1e3]"></a>put_fat</STRONG> (Thumb, 310 bytes, Stack size 32 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_truncate
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
</UL>

<P><STRONG><a name="[201]"></a>f_mount</STRONG> (Thumb, 86 bytes, Stack size 40 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_ldnumber
</UL>

<P><STRONG><a name="[202]"></a>f_open</STRONG> (Thumb, 384 bytes, Stack size 96 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_clust
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_clust
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
</UL>
<BR>[Called By]<UL><LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_hzfont
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;updata_fontx
</UL>

<P><STRONG><a name="[205]"></a>f_read</STRONG> (Thumb, 450 bytes, Stack size 64 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clmt_clust
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;updata_fontx
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_gets
</UL>

<P><STRONG><a name="[207]"></a>f_write</STRONG> (Thumb, 498 bytes, Stack size 64 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clmt_clust
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_printf
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_puts
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_putc
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;putc_bfd
</UL>

<P><STRONG><a name="[208]"></a>f_sync</STRONG> (Thumb, 176 bytes, Stack size 24 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_clust
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>
<BR>[Called By]<UL><LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
</UL>

<P><STRONG><a name="[209]"></a>f_close</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
</UL>
<BR>[Called By]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;updata_fontx
</UL>

<P><STRONG><a name="[20a]"></a>f_lseek</STRONG> (Thumb, 654 bytes, Stack size 48 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clmt_clust
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>

<P><STRONG><a name="[20b]"></a>f_opendir</STRONG> (Thumb, 136 bytes, Stack size 40 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_clust
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
</UL>

<P><STRONG><a name="[20c]"></a>f_closedir</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
</UL>

<P><STRONG><a name="[20d]"></a>f_readdir</STRONG> (Thumb, 114 bytes, Stack size 32 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fileinfo
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
</UL>

<P><STRONG><a name="[20e]"></a>f_stat</STRONG> (Thumb, 84 bytes, Stack size 72 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fileinfo
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
</UL>

<P><STRONG><a name="[20f]"></a>f_getfree</STRONG> (Thumb, 258 bytes, Stack size 56 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>

<P><STRONG><a name="[210]"></a>f_truncate</STRONG> (Thumb, 184 bytes, Stack size 16 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>

<P><STRONG><a name="[211]"></a>f_unlink</STRONG> (Thumb, 194 bytes, Stack size 112 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_remove
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_clust
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
</UL>

<P><STRONG><a name="[212]"></a>f_mkdir</STRONG> (Thumb, 380 bytes, Stack size 88 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_clust
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
</UL>

<P><STRONG><a name="[213]"></a>f_chmod</STRONG> (Thumb, 112 bytes, Stack size 88 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
</UL>

<P><STRONG><a name="[214]"></a>f_utime</STRONG> (Thumb, 110 bytes, Stack size 80 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
</UL>

<P><STRONG><a name="[215]"></a>f_rename</STRONG> (Thumb, 314 bytes, Stack size 144 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_ldnumber
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_remove
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_clust
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_clust
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
</UL>

<P><STRONG><a name="[216]"></a>f_getlabel</STRONG> (Thumb, 190 bytes, Stack size 72 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
</UL>

<P><STRONG><a name="[217]"></a>f_setlabel</STRONG> (Thumb, 470 bytes, Stack size 88 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_wtoupper
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_convert
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_alloc
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_chr
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
</UL>

<P><STRONG><a name="[218]"></a>f_mkfs</STRONG> (Thumb, 1570 bytes, Stack size 112 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_ldnumber
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_ioctl
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[219]"></a>f_gets</STRONG> (Thumb, 82 bytes, Stack size 40 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
</UL>

<P><STRONG><a name="[21b]"></a>f_putc</STRONG> (Thumb, 64 bytes, Stack size 96 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;putc_bfd
</UL>

<P><STRONG><a name="[21c]"></a>f_puts</STRONG> (Thumb, 74 bytes, Stack size 96 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;putc_bfd
</UL>

<P><STRONG><a name="[21d]"></a>f_printf</STRONG> (Thumb, 674 bytes, Stack size 160 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;putc_bfd
</UL>

<P><STRONG><a name="[1f7]"></a>ff_convert</STRONG> (Thumb, 146 bytes, Stack size 40 bytes, cc936.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_setlabel
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fileinfo
</UL>

<P><STRONG><a name="[1eb]"></a>ff_wtoupper</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, cc936.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_setlabel
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmp_lfn
</UL>

<P><STRONG><a name="[2a0]"></a>GPIO_group_OUT</STRONG> (Thumb, 454 bytes, Stack size 0 bytes, common.o(.text), UNUSED)

<P><STRONG><a name="[2a1]"></a>GPIO_bits_OUT</STRONG> (Thumb, 92 bytes, Stack size 20 bytes, common.o(.text), UNUSED)

<P><STRONG><a name="[90]"></a>Stm32_Clock_Init</STRONG> (Thumb, 190 bytes, Stack size 96 bytes, common.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = Stm32_Clock_Init &rArr; HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetREVID
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[91]"></a>delay_init</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, common.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = delay_init
</UL>
<BR>[Calls]<UL><LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_CLKSourceConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a5]"></a>delay_us</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, common.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ReadReg
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_WAKEUP
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_PowerDown
</UL>

<P><STRONG><a name="[a0]"></a>delay_ms</STRONG> (Thumb, 24 bytes, Stack size 12 bytes, common.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = delay_ms &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_FSMC_Config
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;font_init
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_hzfont
</UL>

<P><STRONG><a name="[220]"></a>fupd_prog</STRONG> (Thumb, 144 bytes, Stack size 48 bytes, updatefont.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayString
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayNum
</UL>
<BR>[Called By]<UL><LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_hzfont
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;updata_fontx
</UL>

<P><STRONG><a name="[221]"></a>updata_fontx</STRONG> (Thumb, 346 bytes, Stack size 64 bytes, updatefont.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mem_free
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mem_malloc
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_SectorWrite
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fupd_prog
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>
<BR>[Called By]<UL><LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_hzfont
</UL>

<P><STRONG><a name="[222]"></a>update_hzfont</STRONG> (Thumb, 1040 bytes, Stack size 48 bytes, updatefont.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mem_free
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Fill_onecolor
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mem_malloc
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayString_color
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayString
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_Init
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_SectorWrite
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Read
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Erase_Sector
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;updata_fontx
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fupd_prog
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>

<P><STRONG><a name="[98]"></a>font_init</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, updatefont.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = font_init &rArr; W25QXX_Init &rArr; SPI1_Init &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Init
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[224]"></a>Get_HzMat</STRONG> (Thumb, 220 bytes, Stack size 40 bytes, showhz.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = Get_HzMat &rArr; W25QXX_Read &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Display1HZ
</UL>

<P><STRONG><a name="[225]"></a>LCD_Display1HZ</STRONG> (Thumb, 186 bytes, Stack size 128 bytes, showhz.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = LCD_Display1HZ &rArr; Get_HzMat &rArr; W25QXX_Read &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Color_DrawPoint
<LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_HzMat
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayHZstr
</UL>

<P><STRONG><a name="[8a]"></a>LCD_DisplayHZstr</STRONG> (Thumb, 136 bytes, Stack size 32 bytes, showhz.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = LCD_DisplayHZstr &rArr; LCD_Display1HZ &rArr; Get_HzMat &rArr; W25QXX_Read &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayChar
<LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Display1HZ
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_Display
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Str_Mid
</UL>

<P><STRONG><a name="[226]"></a>Show_Str_Mid</STRONG> (Thumb, 86 bytes, Stack size 32 bytes, showhz.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayHZstr
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>

<P><STRONG><a name="[119]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
</UL>

<P><STRONG><a name="[227]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Str_Mid
</UL>

<P><STRONG><a name="[c6]"></a>strcmp</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, strcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
</UL>

<P><STRONG><a name="[223]"></a>strcpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, strcpy.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_hzfont
</UL>

<P><STRONG><a name="[229]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[2a2]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[228]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[2a3]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[88]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[2a4]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[2a5]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[2a6]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[2a7]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[2a8]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[2a9]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[110]"></a>__NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32f4xx_hal_cortex.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>

<P><STRONG><a name="[10f]"></a>__NVIC_GetPriorityGrouping</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_GetPriorityGrouping
</UL>

<P><STRONG><a name="[126]"></a>DMA_CalcBaseAndBitshift</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_DeInit
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[125]"></a>DMA_CheckFifoParam</STRONG> (Thumb, 170 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[129]"></a>DMA_SetConfig</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, stm32f4xx_hal_dma.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start
</UL>

<P><STRONG><a name="[13a]"></a>UART_SetConfig</STRONG> (Thumb, 864 bytes, Stack size 24 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = UART_SetConfig &rArr; HAL_RCC_GetPCLK2Freq
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MultiProcessor_Init
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_LIN_Init
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_HalfDuplex_Init
</UL>

<P><STRONG><a name="[140]"></a>UART_WaitOnFlagUntilTimeout</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, stm32f4xx_hal_uart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>

<P><STRONG><a name="[144]"></a>UART_EndRxTransfer</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[143]"></a>UART_EndTxTransfer</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[62]"></a>UART_DMAError</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_DMAError
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(.text)
</UL>
<P><STRONG><a name="[61]"></a>UART_DMATxHalfCplt</STRONG> (Thumb, 14 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_DMATxHalfCplt
</UL>
<BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxHalfCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(.text)
</UL>
<P><STRONG><a name="[60]"></a>UART_DMATransmitCplt</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_DMATransmitCplt
</UL>
<BR>[Calls]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(.text)
</UL>
<P><STRONG><a name="[64]"></a>UART_DMARxHalfCplt</STRONG> (Thumb, 14 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_DMARxHalfCplt
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxHalfCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(.text)
</UL>
<P><STRONG><a name="[63]"></a>UART_DMAReceiveCplt</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = UART_DMAReceiveCplt &rArr; HAL_UART_RxCpltCallback &rArr; strcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(.text)
</UL>
<P><STRONG><a name="[66]"></a>UART_DMARxAbortCallback</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_DMARxAbortCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_AbortCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(.text)
</UL>
<P><STRONG><a name="[65]"></a>UART_DMATxAbortCallback</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_DMATxAbortCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_AbortCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(.text)
</UL>
<P><STRONG><a name="[67]"></a>UART_DMATxOnlyAbortCallback</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_DMATxOnlyAbortCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_AbortTransmitCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(.text)
</UL>
<P><STRONG><a name="[68]"></a>UART_DMARxOnlyAbortCallback</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_DMARxOnlyAbortCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_AbortReceiveCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(.text)
</UL>
<P><STRONG><a name="[157]"></a>UART_EndTransmit_IT</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_EndTransmit_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[159]"></a>UART_Transmit_IT</STRONG> (Thumb, 104 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[69]"></a>UART_DMAAbortOnError</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_DMAAbortOnError
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(.text)
</UL>
<P><STRONG><a name="[158]"></a>UART_Receive_IT</STRONG> (Thumb, 166 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = UART_Receive_IT &rArr; HAL_UART_RxCpltCallback &rArr; strcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[15a]"></a>SD_InitCard</STRONG> (Thumb, 252 bytes, Stack size 72 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCSD
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCID
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSelDesel
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_Init
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetPowerState
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardCSD
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSetRelAdd
</UL>
<BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[163]"></a>SD_PowerON</STRONG> (Thumb, 238 bytes, Stack size 24 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdOperCond
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdGoIdleState
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppOperCommand
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
</UL>
<BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[16a]"></a>SD_PowerOFF</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_PowerState_OFF
</UL>
<BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_DeInit
</UL>

<P><STRONG><a name="[179]"></a>SD_SendStatus</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, stm32f4xx_hal_sd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = SD_SendStatus &rArr; SDMMC_CmdSendStatus &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
</UL>

<P><STRONG><a name="[6b]"></a>SD_DMAError</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, stm32f4xx_hal_sd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = SD_DMAError &rArr; HAL_SD_GetCardState &rArr; SD_SendStatus &rArr; SDMMC_CmdSendStatus &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ErrorCallback
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_sd.o(.text)
</UL>
<P><STRONG><a name="[6a]"></a>SD_DMAReceiveCplt</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, stm32f4xx_hal_sd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = SD_DMAReceiveCplt &rArr; SDMMC_CmdStopTransfer &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_RxCpltCallback
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ErrorCallback
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_sd.o(.text)
</UL>
<P><STRONG><a name="[6c]"></a>SD_DMATransmitCplt</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_hal_sd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_sd.o(.text)
</UL>
<P><STRONG><a name="[6e]"></a>SD_DMARxAbort</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32f4xx_hal_sd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = SD_DMARxAbort &rArr; HAL_SD_GetCardState &rArr; SD_SendStatus &rArr; SDMMC_CmdSendStatus &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_AbortCallback
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ErrorCallback
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_sd.o(.text)
</UL>
<P><STRONG><a name="[6d]"></a>SD_DMATxAbort</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32f4xx_hal_sd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = SD_DMATxAbort &rArr; HAL_SD_GetCardState &rArr; SD_SendStatus &rArr; SDMMC_CmdSendStatus &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_AbortCallback
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ErrorCallback
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_sd.o(.text)
</UL>
<P><STRONG><a name="[184]"></a>SD_Read_IT</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ReadFIFO
</UL>
<BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_IRQHandler
</UL>

<P><STRONG><a name="[185]"></a>SD_Write_IT</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_WriteFIFO
</UL>
<BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_IRQHandler
</UL>

<P><STRONG><a name="[188]"></a>SD_SendSDStatus</STRONG> (Thumb, 306 bytes, Stack size 48 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ReadFIFO
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ConfigData
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStatusRegister
</UL>
<BR>[Called By]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardStatus
</UL>

<P><STRONG><a name="[18b]"></a>SD_FindSCR</STRONG> (Thumb, 310 bytes, Stack size 56 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendSCR
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ReadFIFO
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ConfigData
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WideBus_Enable
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WideBus_Disable
</UL>

<P><STRONG><a name="[18d]"></a>SD_WideBus_Disable</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBusWidth
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
</UL>

<P><STRONG><a name="[18f]"></a>SD_WideBus_Enable</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, stm32f4xx_hal_sd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBusWidth
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
</UL>

<P><STRONG><a name="[192]"></a>SDMMC_GetCmdResp1</STRONG> (Thumb, 412 bytes, Stack size 24 bytes, stm32f4xx_ll_sdmmc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetCommandResponse
</UL>
<BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendSCR
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSelDesel
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSDEraseStartAdd
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSDEraseEndAdd
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadSingleBlock
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadMultiBlock
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdErase
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBusWidth
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSwitch
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdEraseEndAdd
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdEraseStartAdd
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteSingleBlock
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteMultiBlock
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStatusRegister
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendStatus
</UL>

<P><STRONG><a name="[197]"></a>SDMMC_GetCmdError</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdGoIdleState
</UL>

<P><STRONG><a name="[198]"></a>SDMMC_GetCmdResp7</STRONG> (Thumb, 82 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdOperCond
</UL>

<P><STRONG><a name="[199]"></a>SDMMC_GetCmdResp3</STRONG> (Thumb, 74 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppOperCommand
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdOpCondition
</UL>

<P><STRONG><a name="[19a]"></a>SDMMC_GetCmdResp2</STRONG> (Thumb, 88 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCSD
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCID
</UL>

<P><STRONG><a name="[19b]"></a>SDMMC_GetCmdResp6</STRONG> (Thumb, 184 bytes, Stack size 24 bytes, stm32f4xx_ll_sdmmc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetCommandResponse
</UL>
<BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSetRelAdd
</UL>

<P><STRONG><a name="[1a0]"></a>SPI_WaitFlagStateUntilTimeout</STRONG> (Thumb, 168 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Receive
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMATransmitReceiveCplt
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAReceiveCplt
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTransaction
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
</UL>

<P><STRONG><a name="[1a1]"></a>SPI_EndRxTxTransaction</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Transmit
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMARxAbortCallback
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMATransmitReceiveCplt
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMATransmitCplt
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRxTx_ISR
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseTx_ISR
</UL>

<P><STRONG><a name="[1a3]"></a>SPI_EndRxTransaction</STRONG> (Thumb, 148 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SPI_EndRxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Receive
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAReceiveCplt
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRx_ISR
</UL>

<P><STRONG><a name="[1a5]"></a>SPI_CloseTx_ISR</STRONG> (Thumb, 154 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = SPI_CloseTx_ISR &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TxCpltCallback
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_TxISR_16BIT
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_TxISR_8BIT
</UL>

<P><STRONG><a name="[70]"></a>SPI_TxISR_8BIT</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = SPI_TxISR_8BIT &rArr; SPI_CloseTx_ISR &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseTx_ISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[6f]"></a>SPI_TxISR_16BIT</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = SPI_TxISR_16BIT &rArr; SPI_CloseTx_ISR &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseTx_ISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[1a8]"></a>SPI_CloseRx_ISR</STRONG> (Thumb, 128 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = SPI_CloseRx_ISR &rArr; SPI_EndRxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_RxCpltCallback
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTransaction
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_RxISR_16BIT
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_RxISR_16BITCRC
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_RxISR_8BIT
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_RxISR_8BITCRC
</UL>

<P><STRONG><a name="[71]"></a>SPI_RxISR_8BITCRC</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = SPI_RxISR_8BITCRC &rArr; SPI_CloseRx_ISR &rArr; SPI_EndRxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRx_ISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[7a]"></a>SPI_RxISR_8BIT</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = SPI_RxISR_8BIT &rArr; SPI_CloseRx_ISR &rArr; SPI_EndRxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRx_ISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[72]"></a>SPI_RxISR_16BITCRC</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = SPI_RxISR_16BITCRC &rArr; SPI_CloseRx_ISR &rArr; SPI_EndRxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRx_ISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[79]"></a>SPI_RxISR_16BIT</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = SPI_RxISR_16BIT &rArr; SPI_CloseRx_ISR &rArr; SPI_EndRxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRx_ISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[1aa]"></a>SPI_CloseRxTx_ISR</STRONG> (Thumb, 244 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = SPI_CloseRxTx_ISR &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TxRxCpltCallback
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_RxCpltCallback
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_2linesRxISR_16BIT
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_2linesRxISR_16BITCRC
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_2linesTxISR_16BIT
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_2linesRxISR_8BIT
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_2linesRxISR_8BITCRC
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_2linesTxISR_8BIT
</UL>

<P><STRONG><a name="[78]"></a>SPI_2linesTxISR_8BIT</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = SPI_2linesTxISR_8BIT &rArr; SPI_CloseRxTx_ISR &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRxTx_ISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[73]"></a>SPI_2linesRxISR_8BITCRC</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = SPI_2linesRxISR_8BITCRC &rArr; SPI_CloseRxTx_ISR &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRxTx_ISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[77]"></a>SPI_2linesRxISR_8BIT</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = SPI_2linesRxISR_8BIT &rArr; SPI_CloseRxTx_ISR &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRxTx_ISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[76]"></a>SPI_2linesTxISR_16BIT</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = SPI_2linesTxISR_16BIT &rArr; SPI_CloseRxTx_ISR &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRxTx_ISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[74]"></a>SPI_2linesRxISR_16BITCRC</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = SPI_2linesRxISR_16BITCRC &rArr; SPI_CloseRxTx_ISR &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRxTx_ISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[75]"></a>SPI_2linesRxISR_16BIT</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = SPI_2linesRxISR_16BIT &rArr; SPI_CloseRxTx_ISR &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRxTx_ISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[7d]"></a>SPI_DMAError</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SPI_DMAError
</UL>
<BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[7c]"></a>SPI_DMATransmitCplt</STRONG> (Thumb, 128 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = SPI_DMATransmitCplt &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TxCpltCallback
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[7b]"></a>SPI_DMAHalfTransmitCplt</STRONG> (Thumb, 14 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SPI_DMAHalfTransmitCplt
</UL>
<BR>[Calls]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TxHalfCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[7f]"></a>SPI_DMAReceiveCplt</STRONG> (Thumb, 162 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = SPI_DMAReceiveCplt &rArr; SPI_EndRxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_RxCpltCallback
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTransaction
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[7e]"></a>SPI_DMAHalfReceiveCplt</STRONG> (Thumb, 14 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SPI_DMAHalfReceiveCplt
</UL>
<BR>[Calls]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_RxHalfCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[81]"></a>SPI_DMATransmitReceiveCplt</STRONG> (Thumb, 168 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = SPI_DMATransmitReceiveCplt &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TxRxCpltCallback
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[80]"></a>SPI_DMAHalfTransmitReceiveCplt</STRONG> (Thumb, 14 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SPI_DMAHalfTransmitReceiveCplt
</UL>
<BR>[Calls]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TxRxHalfCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[83]"></a>SPI_AbortRx_ISR</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI_AbortRx_ISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[82]"></a>SPI_AbortTx_ISR</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[85]"></a>SPI_DMARxAbortCallback</STRONG> (Thumb, 144 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = SPI_DMARxAbortCallback &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_AbortCpltCallback
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[84]"></a>SPI_DMATxAbortCallback</STRONG> (Thumb, 166 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SPI_DMATxAbortCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_AbortCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[86]"></a>SPI_DMAAbortOnError</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SPI_DMAAbortOnError
</UL>
<BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text)
</UL>
<P><STRONG><a name="[1ed]"></a>mem_cpy</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, ff.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_setlabel
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getlabel
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gen_numname
</UL>

<P><STRONG><a name="[1e0]"></a>mem_set</STRONG> (Thumb, 20 bytes, Stack size 12 bytes, ff.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_setlabel
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_remove
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
</UL>

<P><STRONG><a name="[1f0]"></a>mem_cmp</STRONG> (Thumb, 38 bytes, Stack size 20 bytes, ff.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[1f9]"></a>chk_chr</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, ff.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_setlabel
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>

<P><STRONG><a name="[1dd]"></a>sync_window</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>
<BR>[Called By]<UL><LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>

<P><STRONG><a name="[1de]"></a>move_window</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getlabel
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_remove
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_alloc
</UL>

<P><STRONG><a name="[1df]"></a>sync_fs</STRONG> (Thumb, 200 bytes, Stack size 16 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_ioctl
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>
<BR>[Called By]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_setlabel
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_utime
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_chmod
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
</UL>

<P><STRONG><a name="[1e4]"></a>remove_chain</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
</UL>
<BR>[Called By]<UL><LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_truncate
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[1e5]"></a>create_chain</STRONG> (Thumb, 214 bytes, Stack size 32 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
</UL>
<BR>[Called By]<UL><LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>

<P><STRONG><a name="[206]"></a>clmt_clust</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, ff.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
</UL>

<P><STRONG><a name="[1e6]"></a>dir_sdi</STRONG> (Thumb, 158 bytes, Stack size 24 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
</UL>
<BR>[Called By]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_setlabel
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getlabel
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_remove
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_alloc
</UL>

<P><STRONG><a name="[1e8]"></a>dir_next</STRONG> (Thumb, 272 bytes, Stack size 24 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
</UL>
<BR>[Called By]<UL><LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_remove
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_alloc
</UL>

<P><STRONG><a name="[1e9]"></a>dir_alloc</STRONG> (Thumb, 92 bytes, Stack size 24 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_setlabel
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[1fb]"></a>ld_clust</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, ff.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
</UL>

<P><STRONG><a name="[204]"></a>st_clust</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, ff.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[1ea]"></a>cmp_lfn</STRONG> (Thumb, 138 bytes, Stack size 32 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_wtoupper
</UL>
<BR>[Called By]<UL><LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[1f2]"></a>pick_lfn</STRONG> (Thumb, 112 bytes, Stack size 20 bytes, ff.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
</UL>

<P><STRONG><a name="[1f4]"></a>fit_lfn</STRONG> (Thumb, 122 bytes, Stack size 20 bytes, ff.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[1ec]"></a>gen_numname</STRONG> (Thumb, 194 bytes, Stack size 40 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[1ef]"></a>sum_sfn</STRONG> (Thumb, 32 bytes, Stack size 12 bytes, ff.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[1ee]"></a>dir_find</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sum_sfn
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmp_lfn
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cmp
</UL>
<BR>[Called By]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[1f1]"></a>dir_read</STRONG> (Thumb, 196 bytes, Stack size 40 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sum_sfn
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pick_lfn
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_setlabel
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getlabel
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
</UL>

<P><STRONG><a name="[1f3]"></a>dir_register</STRONG> (Thumb, 308 bytes, Stack size 48 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sum_sfn
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gen_numname
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fit_lfn
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_alloc
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
</UL>
<BR>[Called By]<UL><LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[1f5]"></a>dir_remove</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
</UL>
<BR>[Called By]<UL><LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
</UL>

<P><STRONG><a name="[1f6]"></a>get_fileinfo</STRONG> (Thumb, 268 bytes, Stack size 32 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_convert
</UL>
<BR>[Called By]<UL><LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_stat
<LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
</UL>

<P><STRONG><a name="[1f8]"></a>create_name</STRONG> (Thumb, 604 bytes, Stack size 56 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_wtoupper
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_convert
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_chr
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
</UL>
<BR>[Called By]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
</UL>

<P><STRONG><a name="[1fa]"></a>follow_path</STRONG> (Thumb, 144 bytes, Stack size 32 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_clust
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
</UL>
<BR>[Called By]<UL><LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_utime
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_chmod
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_stat
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[1fe]"></a>get_ldnumber</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, ff.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>

<P><STRONG><a name="[1fc]"></a>check_fs</STRONG> (Thumb, 140 bytes, Stack size 16 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>

<P><STRONG><a name="[1fd]"></a>find_volume</STRONG> (Thumb, 916 bytes, Stack size 80 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_ldnumber
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>
<BR>[Called By]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_setlabel
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getlabel
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_utime
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_chmod
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_stat
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
</UL>

<P><STRONG><a name="[200]"></a>validate</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
</UL>
<BR>[Called By]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_truncate
<LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_closedir
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
</UL>

<P><STRONG><a name="[21a]"></a>putc_bfd</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, ff.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
</UL>
<BR>[Called By]<UL><LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_printf
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_puts
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_putc
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
