﻿为方便 Electron 开发者调试和接入腾讯云游戏多媒体引擎客户端 API，本文为您介绍适用于 Electron 实时语音功能的开发接入技术文档。

## 使用 GME 重要事项

GME 提供实时语音服务、语音消息服务及转文本服务，使用 GME 服务都依赖 Init 和 Poll 等核心接口。

#### 重点提示

- 已完成 GME 应用创建，并获取 SDK AppID 和 Key。请参见 [服务开通指引]( https://cloud.tencent.com/document/product/607/10782)。
- 已开通 **GME 实时语音服务、语音消息服务以及转文本服务**。请参见 [服务开通指引](https://cloud.tencent.com/document/product/607/10782)。
- GME 使用前请对工程进行配置，否则 SDK 不生效。
- GME 的接口调用成功后返回值为 GmeError.AV_OK，数值为 0。
- GME 的接口调用要在同一个线程下。
- GME 需要周期性的调用 Poll 接口触发事件回调。
- 错误码详情可参见 <dx-tag-link link="https://cloud.tencent.com/document/product/607/15173" tag="ErrorCode">错误码</dx-tag-link>。

## 接入 SDK

### 重要步骤

接入 SDK 重要流程如下：

<img src="https://qcloudimg.tencent-cloud.cn/raw/5e05e6ffe1749725a6ba077926286f16.png"  width="70%" /></img>

<dx-steps>
-<dx-tag-link link="#Init" tag="接口：Init">初始化 GME</dx-tag-link>
-<dx-tag-link link="#Poll" tag="接口：Poll">周期性调用 Poll 触发回调</dx-tag-link>
-<dx-tag-link link="#EnterRoom" tag="接口：EnterRoom">进入实时语音房间</dx-tag-link>
-<dx-tag-link link="#EnableMic" tag="接口：EnableMic">打开麦克风</dx-tag-link>
-<dx-tag-link link="#EnableSpeaker" tag="接口：EnableSpeaker">打开扬声器</dx-tag-link>
-<dx-tag-link link="#ExitRoom" tag="接口：ExitRoom">退出语音房间</dx-tag-link>
-<dx-tag-link link="#UnInit" tag="接口：UnInit">反初始化 GME</dx-tag-link>
</dx-steps>

## 核心接口

| 接口   |   接口含义   |
| ------ | :----------: |
| Init   |  初始化 GME  |
| Poll   | 触发事件回调 |
| Pause  |   系统暂停   |
| Resume |   系统恢复   |
| Uninit | 反初始化 GME |

### 引用Gme模块
```
const { GmeContext } = require('gme-electron-sdk');
```

### 获取实例

在使用语音功能时，需要首先获取 GmeSDK 对象。

```
context = new GmeContext();
```

[](id:Init)
### 初始化 SDK

未初始化前，SDK 处于未初始化阶段，**需要通过接口 Init 初始化 SDK**，才可以使用实时语音服务、语音消息服务及转文本服务。调用 Init 接口的线程必须于其他接口在同一线程,建议都在主线程调用接口。

#### 接口原型

```
//class GmeSDK
Init(appid: string, openid: string): number;
```

| 参数     |  类型  | 含义                                                         |
| -------- | :----: | ------------------------------------------------------------ |
| sdkAppId | string | 来自 [腾讯云控制台](https://console.cloud.tencent.com/gamegme) 的 GME 服务提供的 AppID，获取请参见 [语音服务开通指引](https://cloud.tencent.com/document/product/607/10782#.E9.87.8D.E7.82.B9.E5.8F.82.E6.95.B0)。 |
| openID   | string | openID 只支持int64 类型（转为 string 传入），规则由 App 开发者自行制定，App 内不重复即可。如需使用字符串作为 Openid 传入，可 [提交工单](https://console.cloud.tencent.com/workorder/category?level1_id=438&level2_id=445&source=0&data_title=%E6%B8%B8%E6%88%8F%E5%A4%9A%E5%AA%92%E4%BD%93%E5%BC%95%E6%93%8EGME&step=1) 联系开发者。|

#### 返回值

| 返回值                          | 处理                                          |
| ------------------------------- | --------------------------------------------- |
| GmeError.AV_OK= 0                  | 初始化 SDK 成功                               |
| AV_ERR_SDK_NOT_FULL_UPDATE=7015 | 检查 SDK 文件是否完整，建议删除后重新导入 SDK |

<dx-alert infotype="notice" title="关于7015错误提示">

- 7015错误码是通过 md5 进行判断，在接入过程中若出现此错误，请根据提示检查 SDK 文件是否完整、SDK 文件版本是否一致。
- 出现返回值 AV_ERR_SDK_NOT_FULL_UPDATE 时，此返回值**只有提示作用**，并不会造成初始化失败。
- 由于第三方加固、Unity 打包机制等因素会影响库文件 md5，造成误判，所以**正式发布请在逻辑中忽略此错误**，并尽量不在 UI 中提示。
  </dx-alert>

#### 示例代码 

```
string SDKAPPID3RD = "14000xxxxx";
string openId="10001";
number ret = context.Init(SDKAPPID3RD, openId);
//通过返回值判断是否初始化成功
if (ret != GmeError.AV_OK)
{
		console.log("SDK初始化失败:");
		return;
}
```


### 设置回调

接口类采用 Delegate 方法用于向应用程序发送回调通知。将回调函数注册给 SDK，用于接收回调的信息，需要在进房之前设置。

#### 函数原型及示例代码

设置回调，用于接收回调的信息，需要在进房之前设置。
```
SetTMGDelegate(cb: ITMGDelegate);
//在初始化 SDK 时候
context =  GmeSDK.GetInstance();
context.setTMGDelegate(function(eventId, msg){
  if (type == ITMG_MAIN_EVENT_TYPE_ENTER_ROOM)
  {
            //回调处理
  }
});
```

[](id:Poll)
### 触发事件回调

需要周期的调用 Poll 可以触发事件回调。Poll 是 GME 的消息泵，GME 需要周期性的调用 Poll 接口触发事件回调。如果没有调用 Poll ，将会导致整个 SDK 服务运行异常。详情请参见 [Sample Project](https://cloud.tencent.com/document/product/607/18521)  中的 EnginePollHelper 文件。

<dx-alert infotype="alarm" title="务必周期性调用 Poll 接口">
务必周期性调用 Poll 接口且在主线程调用，以免接口回调异常。
</dx-alert>

#### 接口原型

```
Poll()：number;
```

#### 示例代码

```
setInterval(function () {
      context.Poll();
    }, 50);
```

### 系统暂停

当系统发生 Pause 事件时，需要同时通知引擎进行 Pause。例如在应用退后台时候（OnApplicationPause, isPause=True），如果不需要后台播放房间内声音，请调用 Pause 接口暂停整个 GME 服务。

#### 接口原型

```
Pause() :number
```

### 系统恢复

当系统发生 Resume 事件时，需要同时通知引擎进行 Resume。Resume 接口只恢复实时语音。

#### 接口原型

```
Resume() :number
```


[](id:UnInit)
### 反初始化 SDK

反初始化 SDK，进入未初始化状态。**如果游戏业务侧账号与 openid 是绑定的，那切换游戏账号需要反初始化 GME，再用新的 openid 初始化**。

#### 接口原型

```
Uninit() : number;
```

## 实时语音房间相关接口

初始化之后，SDK 调用进房后进去了房间，才可以进行实时语音通话。
使用问题可参见 [实时语音相关问题](https://cloud.tencent.com/document/product/607/30411)。

![](https://qcloudimg.tencent-cloud.cn/raw/a32a684c9d6f3dd6d9c26b0168e61de6.png)

| 接口          |       接口含义       |
| ------------- | :------------------: |
| GenAuthBuffer |     本地鉴权计算     |
| EnterRoom     |       加入房间       |
| ExitRoom      |       退出房间       |
| IsRoomEntered | 判断是否已经进入房间 |

### 本地鉴权计算

生成 AuthBuffer，用于相关功能的加密和鉴权，如正式发布请使用后台部署密钥，后台部署请参见 [鉴权密钥](https://cloud.tencent.com/document/product/607/12218)。    

#### 接口原型

```
GenAuthBuffer(appId: string,roomId: string, openId:string, appKey: number) :string;
```

| 参数   |  类型  | 含义                                                         |
| ------ | :----: | ------------------------------------------------------------ |
| appId  |  string   | 来自腾讯云控制台的 AppID 号码。                              |
| roomId | string | 房间号，最大支持127字符。                                    |
| openId | string | 用户标识。与 Init 时候的 openID 相同。                       |
| key    | number | 来自腾讯云 [控制台](https://console.cloud.tencent.com/gamegme) 的权限密钥。 |

#### 示例代码  

```
 let userSig = context.GenAuthBuffer(this.appid, this.roomId, this.userId, this.authKey)
 context.EnterRoom(this.roomId, this.roomType, userSig);
```

[](id:EnterRoom)
### 加入房间

用生成的鉴权信息进房，加入房间默认不打开麦克风及扬声器。

<dx-alert infotype="alarm" title="注意">
- 加入房间事件回调结果 result 为 0 代表进房成功，进房接口 EnterRoom 返回值为 0 不代表进房成功。
- 房间的音频类型由第一个进房的人确定，此后房间里有成员修改房间类型，将对此房间所有成员生效。例如第一个进入房间的人使用的房间音频类型是流畅音质，第二个进房的是即使进房时候调用接口的音频类型参数是高清音质，进入房间之后也会变成流畅音质。需要有成员调用 ChangeRoomType 才会修改房间的音频类型。
</dx-alert>

#### 接口原型

```
EnterRoom(roomid: string, roomType: number, appKey: string) :number;
```

| 参数       |     类型     | 含义                                       |
| ---------- | :----------: | ------------------------------------------ |
| roomId     |    string    | 房间号，最大支持127字符                    |
| roomType   | ITMGRoomType | 房间类型，游戏建议使用  ITMG_ROOM_TYPE_FLUENCY。房间音频类型请参见 [音质选择](https://cloud.tencent.com/document/product/607/18522)。|
| appKey |    string    | 鉴权码                                     |

#### 示例代码  

```
context.EnterRoom(roomID, ITMG_ROOM_TYPE_STANDARD, retAuthBuff);
```

#### 加入房间事件回调

加入房间完成后会通过回调返回ITMG_MAIN_EVENT_TYPE_ENTER_ROOM事件类型返回进房结果，监听进房结果事件后进行处理。如果回调为成功，即此时进房成功，开始进行**计费**。

<dx-fold-block title="计费问题参考">
[购买指南。](https://cloud.tencent.com/document/product/607/17808)
[计费相关问题。](https://cloud.tencent.com/document/product/607/51459)
[使用实时语音后，如果客户端掉线了，是否还会继续计费？](https://cloud.tencent.com/document/product/607/51459#.E4.BD.BF.E7.94.A8.E5.AE.9E.E6.97.B6.E8.AF.AD.E9.9F.B3.E5.90.8E.EF.BC.8C.E5.A6.82.E6.9E.9C.E5.AE.A2.E6.88.B7.E7.AB.AF.E6.8E.89.E7.BA.BF.E4.BA.86.EF.BC.8C.E6.98.AF.E5.90.A6.E8.BF.98.E4.BC.9A.E7.BB.A7.E7.BB.AD.E8.AE.A1.E8.B4.B9.EF.BC.9F)
</dx-fold-block>

#### 示例代码  

```
//对事件进行监听：
 gmeContext.setTMGDelegate(function(eventId, msg){
  switch (eventId) {
      case ITMG_MAIN_EVENT_TYPE_ENTER_ROOM:
      {
      }
  }
});
```

#### Data 详情

| 消息                                 |        Data        | 例子                                                         |
| ------------------------------------ | :----------------: | ------------------------------------------------------------ |
| ITMG_MAIN_EVENT_TYPE_ENTER_ROOM      | result; error_info | {"error_info":"","result":0}                                 |
| ITMG_MAIN_EVENT_TYPE_ROOM_DISCONNECT | result; error_info | {"error_info":"waiting timeout, please check your network","result":0} |

如果断网，将会有断网的回调提示 `ITMG_MAIN_EVENT_TYPE.ITMG_MAIN_EVENT_TYPE_ROOM_DISCONNECT`，此时 SDK 会自动进行重连，回调是 `ITMG_MAIN_EVENT_TYPE_RECONNECT_START`，当重连成功时，会有 `ITMG_MAIN_EVENT_TYPE_RECONNECT_SUCCESS` 回调。

#### 错误码

| 错误码值 | 原因及建议方案                                               |
| -------- | ------------------------------------------------------------ |
| 7006     | 鉴权失败原因：<li>AppID 不存在或者错误<li>authbuff 鉴权错误<li>鉴权过期 <li>OpenId 不符合规范 |
| 7007     | 已经在其它房间                                               |
| 1001     | 已经在进房过程中，然后又重复了此操作。建议在进房回调返回之前不要再调用进房接口 |
| 1003     | 已经进房了在房间中，又调用一次进房接口                       |
| 1101     | 确保已经初始化 SDK，确保 OpenId 是否符合规则，或者确保在同一线程调用接口，以及确保 Poll 接口正常调用 |

[](id:ExitRoom)	
### 退出房间

通过调用此接口可以退出所在房间。这是一个异步接口，返回值为 AV_OK 的时候代表异步投递成功。如果应用中有退房后立即进房的场景，在接口调用流程上，开发者无需要等待 ExitRoom 的回调 RoomExitComplete 通知，只需直接调用接口。

#### 接口原型  

```
ExitRoom(): number;
```

#### 示例代码  

```
context.ExitRoom();
```

#### 退出房间事件回调

退出房间完成后会有回调，消息为 ITMG_MAIN_EVENT_TYPE_EXIT_ROOM。示例代码如下：

#### 示例代码  

```
gmeContext.setTMGDelegate(function(eventId, msg){
  switch (eventId) {
      case ITMG_MAIN_EVENT_TYPE_EXIT_ROOM:
      {
			 //进行处理
        break;
      }
  }
});
```

### 判断是否已经进入房间

通过调用此接口可以判断是否已经进入房间，返回值为 boolean 类型。请勿在进房过程中调用。

#### 接口原型  

```
IsRoomEntered() :boolean
```

#### 示例代码  

```
context.IsRoomEntered();
```

## 房间内状态维护

此部分接口用于业务层显示说话成员、进退房成员，以及将房间内某成员禁言等功能。

![](https://main.qcloudimg.com/raw/df7c21589702c13259c2ebab1dc9da64.png)

| 接口/通知                        |       含义       |
| -------------------------------- | :--------------: |
| ITMG_MAIN_EVNET_TYPE_USER_UPDATE | 成员状态变化通知 |
| AddAudioBlackList                | 房间中禁言某成员 |
| RemoveAudioBlackList             |     移除禁言     |
| IsOpenIdInAudioBlackList             |    查询某openid是否被禁言     |

### 成员进房、说话状态通知事件

- 该事件适用于获取房间中说话的人并在 UI 中展示，以及有人进入、退出语音房间的一个通知。
- 该事件在状态变化才通知，状态不变化的情况下不通知。如需实时获取成员状态，请在业务层收到通知时缓存，事件消息为 ITMG_MAIN_EVNET_TYPE_USER_UPDATE，包含 event_id、count 及 openIdList，在 OnEvent 通知中对事件消息进行判断。
- 音频事件 EVENT_ID_ENDPOINT_NO_AUDIO 的通知有一个阈值，超过这个阈值才会发送通知。即本端两秒没采集到声音后， 房间其他成员才收到本端停止说话的通知。
- 音频事件只会返回成员说话状态，没有返回具体的音量。如需房间内成员具体音量可使用接口 GetVolumeById 进行获取。

| event_id                    |                         含义                          | 应用侧维护内容         |
| --------------------------- | :---------------------------------------------------: | ---------------------- |
| EVENT_ID_ENDPOINT_ENTER     |         有成员进入房间，返回此时进房的 openid         | 应用侧维护成员列表     |
| EVENT_ID_ENDPOINT_EXIT      |         有成员退出房间，返回此时退房的 openid         | 应用侧维护成员列表     |
| EVENT_ID_ENDPOINT_HAS_AUDIO |     有成员发送音频包，返回此时房间内说话的 openid，通过此事件可以判断用户是否说话，并展示声纹效果     | 应用侧维护通话成员列表 |
| EVENT_ID_ENDPOINT_NO_AUDIO  | 有成员停止发送音频包，返回此时房间内停止说话的 openid | 应用侧维护通话成员列表 |
| EVENT_ID_ENDPOINT_MIC_OPENED |     有成员打开了麦克风上行音频（但不一定在说话）     | 应用侧维护通话成员列表 |
| EVENT_ID_ENDPOINT_MIC_CLOSED  | 有成员关闭了麦克风上行音频 | 应用侧维护通话成员列表 |

#### 示例代码

```
context.setTMGDelegate(function(eventId, msg){
  if (type == ITMG_MAIN_EVENT_TYPE_ENTER_ROOM)
  {
           	//进行处理
		    switch (eventID)
 		    {
 		    case EVENT_ID_ENDPOINT_ENTER:
  			    //有成员进入房间
  			    break;
 		    case EVENT_ID_ENDPOINT_EXIT:
  			    //有成员退出房间
			    break;
		    case EVENT_ID_ENDPOINT_HAS_AUDIO:
			    //有成员发送音频包
			    break;
		    case EVENT_ID_ENDPOINT_NO_AUDIO:
			    //有成员停止发送音频包
			    break;
		  
		    default:
			    break;
 		    }
		break;
  }
});
```

### 房间中禁言某成员

将某个 ID 加入音频数据黑名单，即不接受某人的语音， 只对本端生效，不会影响其他端。返回值为 0 表示调用成功。例如 ：A、B、C 都在同一个房间开麦说话： 

- 如果 A 设置了 C 的黑名单， 则 A 只能听见 B 的声音。
- B 因为没有设置黑名单， 仍旧可以听见 A 和 C 的声音。
- C 同样因为没有设置黑名单， 可以听见 A 和 B 的声音。

此接口适用于在语音房间中将某用户禁言的场景。

#### 接口原型  

```
AddAudioBlackList(openId: string) :number
```

| 参数   | 类型  | 含义               |
| ------ | :---: | ------------------ |
| openId | string | 需添加黑名单的用户 openid |

#### 示例代码  

```
context.AddAudioBlackList(openId);
```

### 移除禁言

将某个 Id 移除音频数据黑名单。返回值为0表示调用成功。

#### 接口原型  

```
RemoveAudioBlackList(openId: string) :number
```

| 参数   | 类型  | 含义              |
| ------ | :---: | ----------------- |
| openId |string | 需移除黑名单的 ID |

#### 示例代码  

```
context.RemoveAudioBlackList(openId);
```

### 查询用户是否被禁言

查询某个 Id 是否在黑名单。返回值为true表示在黑名单，false表示不在黑名单。

#### 接口原型  

```
IsOpenIdInAudioBlackList(openId: string) :boolean
```

| 参数   | 类型  | 含义              |
| ------ | :---: | ----------------- |
| openId |string | 需查询是否在黑名单的 ID |

#### 示例代码  

```
boolean isInBlackList = context.IsOpenIdInAudioBlackList(openId);
```

## 实时语音采集相关接口

- 初始化 SDK 之后进房，在房间中，才可以调用实时音频语音相关接口。
- 当用户界面单击打开/关闭麦克风/扬声器按钮时，建议采用 EnableMic 以及 EnableSpeaker 接口进行调用。
- 当用户界面按住麦克风按钮时发言，放开按钮不发言，建议采用进房时候调用 EnableAudioCaptureDevice 一次，后续按住发言调用 EnableAudioSend 来实现。

| 接口                        |       接口含义       |
| --------------------------- | :------------------: |
| EnableMic                   |      开关麦克风      |
| GetMicState                 |    获取麦克风状态    |
| EnableAudioCaptureDevice    |     开关采集设备     |
| IsAudioCaptureDeviceEnabled |   获取采集设备状态   |
| EnableAudioSend             |   打开关闭音频上行   |
| IsAudioSendEnabled          |   获取音频上行状态   |
| GetMicLevel                 |  获取实时麦克风音量  |
| GetSendStreamLevel          | 获取音频上行实时音量 |
| SetMicVolume                |    设置麦克风音量    |
| GetMicVolume                |    获取麦克风音量    |

	
[](id:EnableMic)	
### 开启或关闭麦克风

此接口用来开启关闭麦克风。加入房间默认不打开麦克风及扬声器。**EnableMic = EnableAudioCaptureDevice + EnableAudioSend**

#### 接口原型  

```
EnableMic(bEnable: boolean) : number
```

| 参数      |  类型   | 含义                                                         |
| --------- | :-----: | ------------------------------------------------------------ |
| isEnabled | boolean | 如果需要打开麦克风，则传入的参数为 true，如果关闭麦克风，则参数为 false |

#### 示例代码  

```
//打开麦克风
context.EnableMic(true);
```

### 麦克风状态获取

此接口用于获取麦克风状态，返回值0为关闭麦克风状态，返回值1为打开麦克风状态。

#### 接口原型  

```
GetMicState() :number
```

#### 示例代码  

```
context.GetMicState();
```

### 开启或关闭采集设备

此接口用来开启/关闭采集设备。加入房间默认不打开设备。

- 只能在进房后调用此接口，退房会自动关闭设备。
- 在移动端，打开采集设备通常会伴随权限申请，音量类型调整等操作。

#### 接口原型  

```
EnableAudioCaptureDevice(enable:boolean) :number
```

| 参数      | 类型 | 含义                                                         |
| --------- | :--: | ------------------------------------------------------------ |
| enable | boolean | 如果需要打开采集设备，则传入的参数为 true，如果关闭采集设备，则参数为 false |

#### 示例代码

```
//打开采集设备
context.EnableAudioCaptureDevice(true);
```

### 采集设备状态获取

此接口用于采集设备状态获取。

#### 接口原型

```
IsAudioCaptureDeviceEnabled():boolean
```

#### 示例代码

```
boolean IsAudioCaptureDevice = context.IsAudioCaptureDeviceEnabled();
```

### 打开或关闭音频上行

此接口用于打开/关闭音频上行。如果采集设备已经打开，那么会发送采集到的音频数据。如果采集设备没有打开，那么仍旧无声。采集设备的打开关闭请参见接口 EnableAudioCaptureDevice。

#### 接口原型

```
EnableAudioSend(bEnable: boolean) :number
```

| 参数      | 类型 | 含义                                                         |
| --------- | :--: | ------------------------------------------------------------ |
| isEnabled | boolean | 如果需要打开音频上行，则传入的参数为 true，如果关闭音频上行，则参数为 false |

#### 示例代码  

```
context.EnableAudioSend(true);
```

### 音频上行状态获取

此接口用于音频上行状态获取。

#### 接口原型  

```
IsAudioSendEnabled():boolean
```

#### 示例代码  

```
boolean IsAudioSend = context.IsAudioSendEnabled();
```

### 获取麦克风实时音量

此接口用于获取麦克风实时音量，返回值为 number 类型。建议20ms获取一次。值域为0 - 100，通过此接口可以获取到麦克风采集到的实时音量情况。

 

#### 接口原型  

```
GetMicLevel():number
```

#### 示例代码  

```
context.GetMicLevel();
```

 ### 获取音频上行实时音量

此接口用于获取自己音频上行实时音量，返回值为 number 类型，取值范围为0 - 100。

#### 接口原型  

```
GetSendStreamLevel() :number
```

#### 示例代码  

```
context.GetSendStreamLevel();
```

### 设置麦克风软件音量

此接口用于设置麦克风的音量。参数 volume 用于设置麦克风的音量，相当于对采集的声音做衰减或增益。
#### 接口原型  

```
SetMicVolume(volume:number) :number
```

| 参数   | 类型 | 含义                                                         |
| ------ | :--: | ------------------------------------------------------------ |
| volume | number  | 取值范围为 0-200，数值为0的时候表示静音，当数值为100的时候表示音量不增不减，默认数值为100。 |

#### 示例代码  

```
number micVol = (value * 100);
context.SetMicVolume (micVol);
```

### 获取麦克风软件音量

此接口用于获取麦克风的音量。返回值为一个number类型数值，返回值为101代表没调用过接口 SetMicVolume。

 

#### 接口原型  

```
GetMicVolume()
```

#### 示例代码  

```
context.GetMicVolume();
```

## 实时语音播放相关接口

| 接口                     |            接口含义            |
| ------------------------ | :----------------------------: |
| EnableSpeaker            |           开关扬声器           |
| GetSpeakerState          |         获取扬声器状态         |
| EnableAudioPlayDevice    |          开关播放设备          |
| IsAudioPlayDeviceEnabled |        获取播放设备状态        |
| EnableAudioRecv          |        打开关闭音频下行        |
| IsAudioRecvEnabled       |        获取音频下行状态        |
| GetSpeakerLevel          |       获取实时扬声器音量       |
| GetRecvStreamLevel       | 获取房间内其他成员下行实时音量 |
| SetSpeakerVolume         |         设置扬声器音量         |
| GetSpeakerVolume         |         获取扬声器音量         |

	
[](id:EnableSpeaker)	
### 开启或关闭扬声器

此接口用于开启关闭扬声器。**EnableSpeaker = EnableAudioPlayDevice +  EnableAudioRecv**

#### 接口原型  

```
EnableSpeaker(bEnable: boolean) : number;
```

| 参数      | 类型 | 含义                                                         |
| --------- | :--: | ------------------------------------------------------------ |
| bEnable | boolean | 如果需要关闭扬声器，则传入的参数为 false，如果打开扬声器，则参数为 true |

#### 示例代码  

```
//打开扬声器
context.EnableSpeaker(true);
```

### 扬声器状态获取

此接口用于扬声器状态获取。返回值0为关闭扬声器状态，返回值1为打开扬声器状态。

#### 接口原型  

```
GetSpeakerState() :number
```

#### 示例代码  

```
context.GetSpeakerState();
```

### 开启或关闭播放设备

此接口用于开启关闭播放设备。

#### 接口原型  

```
EnableAudioPlayDevice(enable:boolean) :number
```

| 参数      | 类型 | 含义                                                         |
| --------- | :--: | ------------------------------------------------------------ |
| enable | boolean | 如果需要关闭播放设备，则传入的参数为 false，如果打开播放设备，则参数为 true |

#### 示例代码  

```
context.EnableAudioPlayDevice(true);
```

### 播放设备状态获取

此接口用于播放设备状态获取。

#### 接口原型

```
IsAudioPlayDeviceEnabled() :boolean
```

#### 示例代码  

```
boolean enable = context.IsAudioPlayDeviceEnabled();
```

### 打开或关闭音频下行

此接口用于打开/关闭音频下行。如果播放设备已经打开，那么会播放房间里其他人的音频数据。如果播放设备没有打开，那么仍旧无声。播放设备的打开关闭参见接口请参见 EnableAudioPlayDevice。

#### 接口原型  

```
EnableAudioRecv(bEnable: boolean) :number
```

| 参数      | 类型 | 含义                                                         |
| --------- | :--: | ------------------------------------------------------------ |
| isEnabled | boolean | 如果需要打开音频下行，则传入的参数为 true，如果关闭音频下行，则参数为 false |

#### 示例代码  

```
context.EnableAudioRecv(true);
```



### 音频下行状态获取

此接口用于音频下行状态获取。

#### 接口原型  

```
IsAudioRecvEnabled():boolean
```

#### 示例代码  

```
boolean IsAudioRecv = context.IsAudioRecvEnabled();
```

### 获取扬声器实时音量

此接口用于获取扬声器实时音量。返回值为 number 类型数值，表示扬声器实时音量。建议20ms获取一次。

#### 接口原型  

```
GetSpeakerLevel():number
```

#### 示例代码  

```
context.GetSpeakerLevel();
```

### 获取房间内其他成员下行实时音量

此接口用于获取房间内其他成员下行实时音量，返回值为 number 类型，取值范围为0 - 200。

#### 接口原型  

```
GetRecvStreamLevel(openId: string) :number
```

| 参数   |  类型  | 含义                  |
| ------ | :----: | --------------------- |
| openId | string | 房间其他成员的openId |

#### 示例代码  

```
number level =GetRecvStreamLevel(openId);
```

### 动态设置房间内某成员音量

此接口用于设置房间内某成员的说话音量大小，此设置只在本端生效。

#### 接口原型  

```
SetSpeakerVolumeByOpenID(openId: string, volume:number) :number;
```

|参数   |类型   |含义   |
|----------|-------|-------|
|openId       |string   |需要调节音量大小的OpenID|
|volume  |number       |百分比，建议[0-200]，其中100为默认值|

#### 示例代码  

```
context.SetSpeakerVolumeByOpenID(openId,  100);
```

### 获取设置的声音百分比

调用此接口获取 SetSpeakerVolumeByOpenID 设置的能量值

#### 接口原型

```
GetSpeakerVolumeByOpenID(openId: string) :number;
```

|参数   |类型   |含义   |
|----------|-------|-------|
|openId       |string|需要调节音量大小的OpenID|


#### 返回值

接口返回 OpenID 设置的能量百分比， 默认返回100。

#### 示例代码  

```
context.GetSpeakerVolumeByOpenID(openId);
```

### 设置扬声器的音量

此接口用于设置扬声器的音量。

#### 接口原型  

```
SetSpeakerVolume(volume:number) :number
```

| 参数   | 类型 | 含义                                                         |
| ------ | :--: | ------------------------------------------------------------ |
| volume | number  | 设置音量，范围0 - 200，当数值为0时，表示静音，当数值为100时，表示音量不增不减，默认数值为100。 |

#### 示例代码  

```
number vol = 100;
context.SetSpeakerVolume(vol);
```

### 获取扬声器的音量

此接口用于获取扬声器的音量。返回值为 number 类型数值，代表扬声器的音量，返回值为101代表没调用过接口 SetSpeakerVolume。
Level 是实时音量，Volume 是扬声器的音量，最终声音音量 =  Level × Volume %。例如实时音量是数值是100，此时 Volume 的数值是60，那么最终发出来的声音数值也是60。

#### 接口原型  

```
GetSpeakerVolume() :number
```

#### 示例代码  

```
numbet volume = context.GetSpeakerVolume();
```

## 设备选择相关接口

设备选择相关接口只能在 PC 端上使用。

| 接口                        |       接口含义       |
| --------------------------- | :------------------: |
| GetMicListCount             |       获取麦克风设备数量       |
| GetMicList                  |         枚举麦克风设备         |
| GetSpeakerListCount         |       获取扬声器设备数量       |
| GetSpeakerList              |         枚举扬声器设备         |
| SelectMic                   |         选定麦克风设备         |
| SelectSpeaker               |         选定扬声器设备         |

### 获取麦克风设备数量

此接口用来获取麦克风设备数量。

#### 函数原型  

```
GetMicListCount() :number
```

#### 示例代码  

```
var micListCount = context.GetMicListCount();
```

### 枚举麦克风设备

此接口用来枚举麦克风设备。配合 GetMicListCount 接口使用。

#### 函数原型 

```
GetMicList() :GmeAudioDeviceInfo[];
```

#### 示例代码  

```
var micList = context.GetMicList();
```

### 选中麦克风设备

此接口用来选中麦克风设备。如果不调用或者传入 "DEVICEID_DEFAULT"，则选中系统默认设备。
GetMicList接口中返回的第0个设备 id 为默认设备，未选中设备时通话设备为默认设备，选中后业务层维护通话设备。如果该通话设备被拔出，则此时通话设备为默认设备，拔出的通话设备插入后，此时通话设备恢复为插入的通话设备。

#### 函数原型  

```
SelectMic(micId: string) :number;
```

| 参数   | 类型  | 含义          |
| ------ | :---: | ------------- |
| micId | string | 麦克风设备 ID，设备 ID 来自于 GetMicList 返回列表。 |

#### 示例代码  

```
context.SelectMic(deviceID);
```

此接口用来获取扬声器设备数量。

#### 函数原型  

```
 GetSpeakerListCount() :number;
```

#### 示例代码  

```
context.GetSpeakerListCount();
```

### 枚举扬声器设备

此接口用来枚举扬声器设备。配合 GetSpeakerListCount 接口使用。

#### 函数原型  

```
GetSpeakerList(): GmeAudioDeviceInfo[]
```

#### 示例代码  

```
var speakList = GetSpeakerList();
```

### 选中扬声器设备

此接口用来选中播放设备。如果不调用或者传入 "DEVICEID_DEFAULT"，则选中系统默认播放设备。

#### 函数原型  

```
SelectSpeaker(speakerId: string) :number
```

| 参数       | 类型  | 含义          |
| ---------- | :---: | ------------- |
| speakerId | string | 扬声器设备 ID，设备 ID 来自于 GetSpeakerList 返回列表。 |

#### 示例代码  

```
var ret = SelectSpeaker(deviceID);
```

## 高级 API

### 启动耳返

此接口用于启动耳返，需要 EnableLoopBack+EnableSpeaker 才可以听到自己声音。

#### 接口原型  

```
EnableLoopBack(bEnable: boolean) :number
```

| 参数   | 类型 | 含义         |
| ------ | :--: | ------------ |
| enable | boolean | 设置是否启动 |

#### 示例代码  

```
context.EnableLoopBack(true);
```

### 获取用户房间音频类型

此接口用于获取用户房间音频类型，返回值为房间音频类型，返回值为0时代表获取用户房间音频类型发生错误，房间音频类型参考 EnterRoom 接口。

#### 接口原型  

```
GetRoomType() :number
```

#### 示例代码  

```
context.GetRoomType();
```

### 房间类型修改

此接口用于修改用户房间音频类型，结果参见回调事件，事件类型为 ITMG_MAIN_EVENT_TYPE_CHANGE_ROOM_TYPE。房间的音频类型由第一个进房的人确定，此后房间里有成员修改房间类型，将对此房间所有成员生效。
#### 接口原型  

```
ChangeRoomType(roomType: number) :number
```

| 参数     |     类型     | 含义                                                  |
| -------- | :----------: | ----------------------------------------------------- |
| roomtype | number | 房间切换成的目标类型，房间音频类型参考 EnterRoom 接口  |

#### 示例代码  

```
context.ChangeRoomType(ITMG_ROOM_TYPE_FLUENCY);
```

#### 回调事件

房间类型设置完成后，回调的事件消息为 ITMG_MAIN_EVENT_TYPE_CHANGE_ROOM_TYPE，返回的参数为 result、error_info 及 new_room_type，new_room_type 代表的信息如下，在 OnEvent 函数中对事件消息进行判断。

| 事件子类型     |  代表参数  | 含义                                                         |
| -------- | :----: | ------------------------------------------------------------ |
| ITMG_ROOM_CHANGE_EVENT_ENTERROOM | 1 | 表示在进房的过程中，自带的音频类型与房间不符合，被修改为所进入房间的音频类型 |
| ITMG_ROOM_CHANGE_EVENT_START | 2 | 表示已经在房间内，音频类型开始切换（例如调用 ChangeRoomType 接口后切换音频类型 ） |
| ITMG_ROOM_CHANGE_EVENT_COMPLETE | 3 | 表示已经在房间，音频类型切换完成 |
| ITMG_ROOM_CHANGE_EVENT_REQUEST | 4 | 表示房间成员调用 ChangeRoomType 接口，请求切换房间音频类型 |

#### 示例代码  

```
context.setTMGDelegate(function(eventId, msg){
  if (ITMGContext.ITMG_MAIN_EVENT_TYPE.ITMG_MAIN_EVENT_TYPE_CHANGE_ROOM_TYPE == type)
        {
        //对房间类型事件进行处理
     }
});
```

### 房间通话质量监控事件

质量监控事件，此通知事件适用于监听网络质量，如果用户网络差的话，业务层将通过 UI 提醒用户切换网络。在进房后触发，事件2秒回调一次，事件消息为 ITMG_MAIN_EVENT_TYPE_CHANGE_ROOM_QUALITY，返回的参数为 weight、loss 及 delay，代表的信息如下：

| 参数   | 类型   | 含义                                                         |
| ------ | ------ | ------------------------------------------------------------ |
| weight | number   | 范围是1 - 50，数值为50是音质评分极好，数值为1是音质评分很差，几乎不能使用，数值为0代表初始值，无含义。一般数值在30以下就可以提醒用户网络较差，建议切换网络。 |
| loss   | var | 上行丢包率。|
| delay  | number    | 音频触达延迟时间（ms）。                                     |

### 开始播放伴奏

此接口用于开始播放伴奏和背景音乐，支持 m4a、wav、mp3 一共三种格式，同时开始推流

#### 接口原型

```
StartAccompany(file: string, loopBack = true, loopCount = 1 , msTime = 0) :number
```

| 参数   | 类型   | 含义                                                         |
| ------ | ------ | ------------------------------------------------------------ |
| file | string   | 背景音乐的播放文件 |
| loopBack   | boolean | 是否混音发送，一般都设置为 true，即其他人也能听到伴奏。|
| loopCount  | number    | 循环次数，数值为-1表示无限循环。填0不播放。                                     |
| msTime  | number    | 延迟时间（ms）。                                     |

#### 示例代码  

```
context.StartAccompany(filePath,true,-1,0);
```

### 播放伴奏的回调

开始播放伴奏完成后，回调函数调用 OnEvent，事件消息为 ITMG_MAIN_EVENT_TYPE_ACCOMPANY_FINISH，在 OnEvent 函数中对事件消息进行判断。
传递的参数 data 包含两个信息，一个是 result，另一个是 file_path。

```
void TMGTestScene::OnEvent(ITMG_MAIN_EVENT_TYPE eventType,const char* data){
    switch (eventType) {
        case ITMG_MAIN_EVENT_TYPE_ENTER_ROOM:
        {
        //进行处理
        break;
       	}
        ...
        case ITMG_MAIN_EVENT_TYPE_ACCOMPANY_FINISH:
        {
        //进行处理
        break;
        }
    }
}
```

### 停止播放伴奏

此接口用于停止播放伴奏，同时停止推流

#### 接口原型  

```
StopAccompany(duckerTime:number) :number
```

| 参数   | 类型   | 含义                                                         |
| ------ | ------ | ------------------------------------------------------------ |
| duckerTime  | number    | 淡出时间（ms）。                                     |

#### 示例代码  

```
context.StopAccompany(0);
```
### 设置伴奏音量

此接口用于设置伴奏音量，默认值为100，数值大于100音量增益，数值小于100音量减益，值域为0 - 200。

#### 接口原型  

```
SetAccompanyVolume(volume: number) :number
```

| 参数   | 类型   | 含义                                                         |
| ------ | ------ | ------------------------------------------------------------ |
| volume  | number    | 音量数值。                                     |

#### 示例代码  

```
context.SetAccompanyVolume(100);

```

### 暂停播放伴奏

此接口用于暂停播放伴奏。

#### 接口原型  

```
PauseAccompany() :number
```

#### 示例代码  

```
context.PauseAccompany();

```

### 恢复播放伴奏

此接口用于恢复播放伴奏。

#### 接口原型  

```
ResumeAccompany() :number
```

#### 示例代码  

```
context.ResumeAccompany();

```

### 开启获取房间用户音量的回调

该接口用于获取房间用户音量值

#### 接口原型

```
TrackingVolume(timeS: number) : number
```

#### 参数含义

| 参数       | 类型           | 含义                                                         |
| ---------- | -------------- | ------------------------------------------------------------ |
| timeS | number | 设置用户音量回调时间间隔 ，单位：秒|

#### 房间用户音量回调事件

事件消息为 ITMG_MAIN_EVENT_TYPE_USER_VOLUMES，返回的json内部参数为 openId、volume，代表的信息如下：

| 参数   | 类型   | 含义                                                         |
| ------ | ------ | ------------------------------------------------------------ |
| openId | string   | 用户ID。 |
| volume   | number | 当前音量。|


#### 示例代码  

```
context.TrackingVolume(0.5);
```

### 停止获取房间用户音量的回调

该接口用于停止获取房间用户音量值

#### 接口原型

```
StopTrackingVolume() : number
```

#### 示例代码  

```
context.StopTrackingVolume();
```

### 获取当前选中的扬声器设备

该接口用于获取当前选中的扬声器设备

#### 接口原型

```
GetCurrentSpeaker()  : [number, GmeAudioDeviceInfo | undefined]
```
#### 返回值说明

| 返回值       | 说明                 |
| --------------------- | -------------------- |
| number    | 0:成功，其他:失败|
| GmeAudioDeviceInfo   | 成功时GmeAudioDeviceInfo有意义，具体参考 GmeAudioDeviceInfo定义，失败时返回undefined |

#### GmeAudioDeviceInfo说明
```
export class GmeAudioDeviceInfo{
    deviceName:string;
    deviceId:string;
    constructor(name:string, id:string){
        this.deviceName = name;
        this.deviceId = id;
    };
}
```
#### 示例代码  

```
let [nRet, devInfo] = context.GetCurrentSpeaker();
```

### 获取当前选中的麦克风设备

该接口用于获取当选中的麦克风设备

#### 接口原型

```
GetCurrentMic()  : [number, GmeAudioDeviceInfo | undefined]
```

#### 返回值说明

| 返回值       | 说明                 |
| --------------------- | -------------------- |
| number    | 0:成功，其他:失败|
| GmeAudioDeviceInfo   | 成功时GmeAudioDeviceInfo有意义，具体参考 GmeAudioDeviceInfo定义，失败时返回undefined |

#### 示例代码  

```
let [nRet, devInfo] = context.GetCurrentMic();
```

### 获取版本号

获取 SDK 版本号，用于分析。

#### 接口原型

```
GetSDKVersion() :string
```

#### 示例代码  

```
context.GetSDKVersion();
```
### 设置应用名称和版本

该接口用于设置应用名称和版本

#### 接口原型

```
SetAppVersion(appVersion: string) : number
```

#### 参数含义

| 参数       | 类型           | 含义                                                         |
| ---------- | -------------- | ------------------------------------------------------------ |
| appVersion | string | 应用名称和版本 |

#### 示例代码  

```
context.SetAppVersion("gme V2.0.0");
```



### 设置打印日志等级

用于设置打印日志等级。建议保持默认等级。需要在 Init 之前调用。

#### 接口原型

```
SetLogLevel(level: number) : number
```

#### 参数含义

| 参数       | 类型           | 含义                                                         |
| ---------- | -------------- | ------------------------------------------------------------ |
| level | number | 设置日志的等级，TMG_LOG_LEVEL_NONE 表示不写入，默认为 TMG_LOG_LEVEL_INFO |

level 说明如下：

| level        | 含义                 |
| --------------------- | -------------------- |
| TMG_LOG_LEVEL_NONE    | 不打印日志           |
| TMG_LOG_LEVEL_ERROR   | 打印错误日志（默认） |
| TMG_LOG_LEVEL_INFO    | 打印提示日志         |
| TMG_LOG_LEVEL_DEBUG   | 打印开发调试日志     |
| TMG_LOG_LEVEL_VERBOSE | 打印高频日志         |

#### 示例代码  

```
context.SetLogLevel(TMG_LOG_LEVEL_INFO);
```



### 设置打印日志路径

用于设置打印日志路径。默认路径如下。需要在 Init 之前调用。

| 平台    | 路径                                                         |
| ------- | ------------------------------------------------------------ |
| Windows | %appdata%\GMEGLOBAL\GME\ProcessName                            |

#### 接口原型

```
SetLogPath(logPath: string)
```

| 参数   |  类型  | 含义 |
| ------ | :----: | ---- |
| logPath | string | 路径 |

#### 示例代码  

```
string logDir = ""//自行设置路径
context.SetLogPath(logDir);
```

### 获取诊断信息

获取音视频通话的实时通话质量的相关信息。该接口主要用来查看实时通话质量、排查问题等，业务侧可以忽略。

#### 接口原型  

```
GetQualityTips() :string
```

#### 示例代码  

```
string tips = context.GetQualityTips();
```

### 回调消息

| 消息     |  含义  | Data | 例子｜
| -------- | :----: | :----: | ------------------------------------------------------------ |
| ITMG_MAIN_EVENT_TYPE_ENTER_ROOM | 进入音频房间消息 | result; error_info |{"error_info":"","result":0} |
| ITMG_MAIN_EVENT_TYPE_EXIT_ROOM | 退出音频房间消息 | result; error_info |{"error_info":"","result":0} |
| ITMG_MAIN_EVENT_TYPE_ROOM_DISCONNECT | 房间因为网络等原因断开消息 | result; error_info |{"error_info":"waiting timeout, please check your network","result":0} |
| ITMG_MAIN_EVNET_TYPE_USER_UPDATE | 房间成员更新消息 | user_list; event_id |{"event_id":1,"user_list":["0"]} |
| ITMG_MAIN_EVENT_TYPE_NUMBER_OF_USERS_UPDATE | 房间成员数量更新消息 | AccUser;AllUser;ProxyUser|{"AccUser":2,"AllUser":2,"ProxyUser":0}|
| ITMG_MAIN_EVENT_TYPE_NUMBER_OF_AUDIOSTREAMS_UPDATE | 房间内音频流数量更新| AudioStreams|{"AudioStreams":0}|
| ITMG_MAIN_EVENT_TYPE_RECONNECT_START | 房间重连开始消息 | result; error_info |{"error_info":"","result":0} |
| ITMG_MAIN_EVENT_TYPE_RECONNECT_SUCCESS | 房间重连成功消息 | result; error_info |{"error_info":"","result":0} |
| ITMG_MAIN_EVENT_TYPE_SWITCH_ROOM | 快速切换房间消息 | result; error_info |{"error_info":"","result":0} |
| ITMG_MAIN_EVENT_TYPE_CHANGE_ROOM_TYPE | 房间状态切换消息 |result; error_info; sub_event_type; new_room_type|{"error_info":"","new_room_type":0,"result":0} |
| ITMG_MAIN_EVENT_TYPE_AUDIO_DATA_EMPTY | 音频数据为空消息 |reason |{"reason":65536} |
| ITMG_MAIN_EVENT_TYPE_ROOM_SHARING_START | 开始跨房连麦消息 | result |{"result":0} |
| ITMG_MAIN_EVENT_TYPE_ROOM_SHARING_STOP | 跨房连麦停止消息 | result |{"result":0}|
| ITMG_MAIN_EVENT_TYPE_RECORD_COMPLETED | K歌录制音频文件完成消息 |result;filepath;duration|{"result":0,"filepath":"","duration":0}|
| ITMG_MAIN_EVENT_TYPE_RECORD_PREVIEW_COMPLETED | K歌预览录制音频文件完成消息 | result |{"result":0}|
| ITMG_MAIN_EVENT_TYPE_RECORD_MIX_COMPLETED | 合成录制文件完成消息 | result;filepath; mic_filepath;duration |{"result":0,"filepath":"","mic_filepath":"", "duration" : 0}|
| ITMG_MAIN_EVENT_TYPE_IOS_MUTE_SWITCH_RESULT | 检测ios静音开关消息 | muteSwitchIsOn |{"muteSwitchIsOn":true}|
| ITMG_MAIN_EVENT_TYPE_SPEAKER_DEFAULT_DEVICE_CHANGED | 默认扬声器设备修改消息 | result; error_info |{"deviceID":"{0.0.0.00000000}.{a4f1e8be-49fa-43e2-b8cf-dd00542b47ae}","deviceName":"扬声器 (Realtek High Definition Audio)","error_info":"","isNewDevice":true,"isUsedDevice":false,"result":0} |
| ITMG_MAIN_EVENT_TYPE_SPEAKER_NEW_DEVICE | 新增扬声器设备消息 | result; error_info |{"deviceID":"{0.0.0.00000000}.{a4f1e8be-49fa-43e2-b8cf-dd00542b47ae}","deviceName":"扬声器 (Realtek High Definition Audio)","error_info":"","isNewDevice":true,"isUsedDevice":false,"result":0} |
| ITMG_MAIN_EVENT_TYPE_SPEAKER_LOST_DEVICE | 丢失扬声器设备消息 | result; error_info |{"deviceID":"{0.0.0.00000000}.{a4f1e8be-49fa-43e2-b8cf-dd00542b47ae}","deviceName":"扬声器 (Realtek High Definition Audio)","error_info":"","isNewDevice":false,"isUsedDevice":false,"result":0} |
| ITMG_MAIN_EVENT_TYPE_MIC_NEW_DEVICE | 新增麦克风设备消息 | result; error_info |{"deviceID":"{0.0.1.00000000}.{5fdf1a5b-f42d-4ab2-890a-7e454093f229}","deviceName":"麦克风 (Realtek High Definition Audio)","error_info":"","isNewDevice":true,"isUsedDevice":true,"result":0} |
| ITMG_MAIN_EVENT_TYPE_MIC_LOST_DEVICE | 丢失麦克风设备消息 | result; error_info |{"deviceID":"{0.0.1.00000000}.{5fdf1a5b-f42d-4ab2-890a-7e454093f229}","deviceName":"麦克风 (Realtek High Definition Audio)","error_info":"","isNewDevice":false,"isUsedDevice":true,"result":0} |
| ITMG_MAIN_EVENT_TYPE_MIC_DEFAULT_DEVICE_CHANGED | 默认麦克风设备修改消息 | result; error_info |{"deviceID":"{0.0.1.00000000}.{5fdf1a5b-f42d-4ab2-890a-7e454093f229}","deviceName":"麦克风 (Realtek High Definition Audio)","error_info":"","isNewDevice":false,"isUsedDevice":true,"result":0} |
| ITMG_MAIN_EVENT_TYPE_CHANGE_ROOM_QUALITY | 房间质量消息 | weight; loss; delay |{"weight":5,"loss":0.1,"delay":1} |
| ITMG_MAIN_EVENT_TYPE_AUDIO_ROUTE_CHANGED | 音频路由改变消息 | route_type |{"route_type":1} |
| ITMG_MAIN_EVENT_TYPE_ACCOMPANY_FINISH | 伴奏播放完成消息 | file_path;is_finished;result |{"file_path":"","is_finished":false,"result":0} |
| ITMG_MAIN_EVENT_TYPE_SERVER_AUDIO_ROUTE_EVENT | 设置服务音频路由转发消息 | ErrorCode;error_info|{"ErrorCode" : 0,"error_info" : ""} |
| ITMG_MAIN_EVENT_TYPE_CUSTOMDATA_UPDATE | 收到发送的自定义音频文本 消息 | sub_type;content;senderid |{"sub_type: 0,"content":"","senderid":""} |
| ITMG_MAIN_EVNET_TYPE_PTT_RECORD_COMPLETE | 语音消息录制完成消息 | result; file_path |{"file_path":"","result":0} |
| ITMG_MAIN_EVNET_TYPE_PTT_UPLOAD_COMPLETE | 语音消息上传完成消息 | result; file_path;file_id |{"file_id":"","file_path":"","result":0} |
| ITMG_MAIN_EVNET_TYPE_PTT_DOWNLOAD_COMPLETE | 语音消息下载完成消息 | result; file_path;file_id |{"file_id":"","file_path":"","result":0} |
| ITMG_MAIN_EVNET_TYPE_PTT_PLAY_COMPLETE | 语音消息播放完成消息 |result; file_path |{"file_path":"","result":0} |
| ITMG_MAIN_EVNET_TYPE_PTT_SPEECH2TEXT_COMPLETE | 语音消息极速转文本完成消息 | result; text;file_id |{"file_id":"","text":"","result":0} |
| ITMG_MAIN_EVNET_TYPE_PTT_STREAMINGRECOGNITION_COMPLETE | 语音消息流式转文本完成消息 | result; file_path; text;file_id |{{"file_id":"","file_path":","text":"","result":0}} |
| ITMG_MAIN_EVNET_TYPE_PTT_STREAMINGRECOGNITION_IS_RUNNING | 语音消息正在流式转文本中 | result; file_path; text;file_id |{{"file_id":"","file_path":","text":"","result":0}} |
| ITMG_MAIN_EVNET_TYPE_PTT_TEXT2SPEECH_COMPLETE | 文本转语音完成消息 | result; text;file_id |{{"file_id":"","text":"","result":0}} |
| ITMG_MAIN_EVNET_TYPE_PTT_TRANSLATE_TEXT_COMPLETE | 文本翻译完成消息 | result; text;file_id |{{"file_id":"","text":"","result":0}} |
| ITMG_MAIN_EVENT_TYPE_CHORUS_EVENT | 收到合唱请求消息 | error_info; result,Accompanier_Openid;sub_type|{ "error_info" : "","result" : 0, “Accompanier_Openid”："","sub_type" : 2}|
| ITMG_MAIN_EVENT_TYPE_CHANGETEAMID | 更改TEAMID消息 | error_info;result;sub_type;teamid |{"error_info" : "","result" : 0,"sub_type" : 452,"teamid" : 321} |
| ITMG_MAIN_EVENT_TYPE_AGE_DETECTED | 年龄识别结果消息| result; age_detected_result |{ "age_detected_result" : 0, "result" : 0} |
| ITMG_MAIN_EVENT_TYPE_HARDWARE_TEST_RECORD_FINISH | 测试延迟录制结束消息| result; file_path;is_finished |{"file_path" : “”, "result" : 0，“is_finished” ： true} |
| ITMG_MAIN_EVENT_TYPE_HARDWARE_TEST_PREVIEW_FINISH | 测试延迟预览结束消息| result; file_path;is_finished |{"file_path" : “”, "result" : 0，“is_finished” ： true} |
| ITMG_MAIN_EVNET_TYPE_ROOM_MANAGEMENT_OPERATOR | 房间管理操作方法消息| SenderID; ReceiverID;OperateType;Result;OperateValue|{"SenderID":"","ReceiverID":"","OperateType":0,"Result": 0,"OperateValue":0} |
| ITMG_MAIN_EVNET_TYPE_VOICE_CHANGER_FETCH_COMPLETE | 获取变声模版完成消息| result; voice_list|{"result":740000,"voice_list":null} |
| ITMG_MAIN_EVENT_TYPE_USER_VOLUMES | 获取用户音量消息| result; voice_list |{"open_id":"","volume":10} |

	

