# 串口屏LED控制系统

## 📋 **系统概述**

本系统基于STM32F0C8T6微控制器(兼容STM32F103)，通过串口屏按键控制LED灯的亮灭。

### **主要功能**
- ✅ 串口屏按键1：发送0x01，PA1输出高电平，LED亮
- ✅ 串口屏按键2：发送0x02，PA1输出低电平，LED灭
- ✅ OLED实时显示系统状态和接收到的命令
- ✅ 支持单字节命令和数据包两种通信模式

## 🔌 **硬件连接**

### **STM32F0C8T6引脚分配**
```
PA9  (USART1_TX)  → 串口屏RXD
PA10 (USART1_RX)  → 串口屏TXD
PA1               → LED正极 (高电平亮，低电平灭)
PB6               → OLED_SCL
PB7               → OLED_SDA
GND               → 公共地
3.3V/5V           → 电源正极
```

### **串口屏连接**
```
串口屏    →    STM32F0C8T6
TXD       →    PA10 (RX)
RXD       →    PA9  (TX)
VCC       →    3.3V/5V
GND       →    GND
```

### **LED连接**
```
STM32F0C8T6  →  LED
PA1          →  LED正极 (通过限流电阻)
GND          →  LED负极
```

## 📊 **OLED显示界面**

### **启动界面**
```
Serial Screen
LED Controller
Status: Ready
LED: OFF
```

### **运行界面**
```
Serial Screen    T:12345
RxData: 01 00 00 00
CMD: LED ON
LED: ON
```

**显示格式说明**：
- 第1行：系统标题和运行时间计数
- 第2行：接收到的原始数据 (HEX格式)
- 第3行：解析后的命令状态
- 第4行：当前LED状态

## 🎮 **操作说明**

### **串口屏按键功能**

#### **按键1 - 亮灯**
- **发送命令**：0x01
- **单片机动作**：PA1输出高电平
- **LED状态**：亮
- **OLED显示**：
  ```
  CMD: LED ON
  LED: ON
  ```

#### **按键2 - 灭灯**
- **发送命令**：0x02
- **单片机动作**：PA1输出低电平
- **LED状态**：灭
- **OLED显示**：
  ```
  CMD: LED OFF
  LED: OFF
  ```

### **通信协议**

#### **模式1：单字节命令 (推荐)**
```
串口屏直接发送：
- 0x01  →  LED亮
- 0x02  →  LED灭
```

#### **模式2：数据包格式**
```
发送格式：FF [CMD] [DATA1] [DATA2] [DATA3] FE
示例：
- FF 01 00 00 00 FE  →  LED亮
- FF 02 00 00 00 FE  →  LED灭
```

## ⚙️ **技术参数**

### **通信参数**
- **协议**：UART串口通信
- **波特率**：9600 bps
- **数据格式**：8N1 (8位数据，无校验，1位停止位)
- **硬件流控**：无

### **GPIO配置**
- **PA1**：推挽输出，50MHz，LED控制
- **PA9**：复用推挽输出，USART1_TX
- **PA10**：上拉输入，USART1_RX

### **系统性能**
- **响应时间**：<10ms
- **命令处理**：中断方式，实时响应
- **显示更新**：主循环，约100Hz

## 🔧 **代码结构**

### **模块化设计**
```
Hardware/
├── LED.h/c        # LED控制模块
├── Serial.h/c     # 串口通信模块
├── OLED.h/c       # OLED显示模块
└── Key.h/c        # 按键模块 (保留)

User/
├── main.c         # 主程序
├── stm32f10x_it.c # 中断处理
└── stm32f10x_conf.h # 配置文件

System/
└── Delay.h/c      # 延时函数
```

### **主要函数**

#### **LED控制函数**
```c
void LED_Init(void);            // LED初始化
void LED1_ON(void);             // LED开启 (PA1高电平)
void LED1_OFF(void);            // LED关闭 (PA1低电平)
void LED1_Turn(void);           // LED状态翻转
uint8_t LED1_GetStatus(void);   // 获取LED状态
```

#### **串口通信函数**
```c
void Serial_Init(void);         // 串口初始化
uint8_t Serial_GetRxFlag(void); // 获取接收标志
void USART1_IRQHandler(void);   // 串口中断处理
```

#### **主程序函数**
```c
void System_Init(void);         // 系统初始化
void Process_SerialCommand(void); // 处理串口命令
void Update_Display(void);      // 更新显示
```

## 🚀 **使用流程**

### **1. 系统启动**
1. 连接硬件并上电
2. 观察OLED显示启动信息
3. 确认LED初始状态为灭

### **2. 功能测试**
1. **亮灯测试**：
   - 按下串口屏按键1
   - 观察LED亮起
   - 确认OLED显示"LED: ON"

2. **灭灯测试**：
   - 按下串口屏按键2
   - 观察LED熄灭
   - 确认OLED显示"LED: OFF"

### **3. 状态监控**
- OLED第2行显示接收到的原始数据
- OLED第3行显示解析后的命令
- OLED第4行显示当前LED状态

## ⚠️ **注意事项**

### **硬件注意事项**
- 确保串口屏和单片机共地连接
- LED需要串联限流电阻 (建议220Ω-1kΩ)
- 电源电压匹配 (3.3V或5V)
- 避免PA1引脚短路

### **软件注意事项**
- 波特率必须匹配 (9600 bps)
- 串口屏发送格式要正确
- 避免频繁发送命令 (建议间隔>100ms)

### **故障排除**
1. **LED不亮**：检查PA1连接、电源、限流电阻
2. **无响应**：检查串口连接、波特率、地线
3. **显示异常**：检查OLED连接、I2C通信
4. **命令错误**：确认串口屏发送的数据格式

## 📈 **扩展功能**

### **可扩展特性**
- 支持更多LED控制 (PA2, PA3等)
- 添加PWM调光功能
- 实现定时控制
- 添加状态反馈到串口屏
- 支持更复杂的命令协议

### **性能优化**
- 使用DMA提高串口效率
- 添加命令缓冲区
- 实现错误检测和重传
- 优化显示刷新算法

---

**版本信息**：V1.0  
**更新日期**：2024-07-18  
**适用硬件**：STM32F0C8T6 / STM32F103C8T6  
**技术支持**：STM32开发团队
