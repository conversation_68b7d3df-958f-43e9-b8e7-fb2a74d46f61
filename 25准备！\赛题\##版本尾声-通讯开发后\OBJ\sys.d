..\obj\sys.o: ..\SYSTEM\sys\sys.c
..\obj\sys.o: ..\SYSTEM\sys\sys.h
..\obj\sys.o: ..\USER\stm32f4xx.h
..\obj\sys.o: ..\CORE\core_cm4.h
..\obj\sys.o: E:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
..\obj\sys.o: ..\CORE\core_cmInstr.h
..\obj\sys.o: ..\CORE\core_cmFunc.h
..\obj\sys.o: ..\CORE\core_cm4_simd.h
..\obj\sys.o: ..\USER\system_stm32f4xx.h
..\obj\sys.o: ..\USER\stm32f4xx_conf.h
..\obj\sys.o: ..\FWLIB\inc\stm32f4xx_adc.h
..\obj\sys.o: ..\USER\stm32f4xx.h
..\obj\sys.o: ..\FWLIB\inc\stm32f4xx_crc.h
..\obj\sys.o: ..\FWLIB\inc\stm32f4xx_dbgmcu.h
..\obj\sys.o: ..\FWLIB\inc\stm32f4xx_dma.h
..\obj\sys.o: ..\FWLIB\inc\stm32f4xx_exti.h
..\obj\sys.o: ..\FWLIB\inc\stm32f4xx_flash.h
..\obj\sys.o: ..\FWLIB\inc\stm32f4xx_gpio.h
..\obj\sys.o: ..\FWLIB\inc\stm32f4xx_pwr.h
..\obj\sys.o: ..\FWLIB\inc\stm32f4xx_rcc.h
..\obj\sys.o: ..\FWLIB\inc\stm32f4xx_syscfg.h
..\obj\sys.o: ..\FWLIB\inc\stm32f4xx_tim.h
..\obj\sys.o: ..\FWLIB\inc\stm32f4xx_usart.h
..\obj\sys.o: ..\FWLIB\inc\misc.h
..\obj\sys.o: ..\FWLIB\inc\stm32f4xx_fsmc.h
..\obj\sys.o: ..\SYSTEM\delay\delay.h
..\obj\sys.o: ..\SYSTEM\sys\sys.h
..\obj\sys.o: ..\SYSTEM\usart\usart.h
..\obj\sys.o: E:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
..\obj\sys.o: ..\HAREWARE\ATD5984\ATD5984.h
..\obj\sys.o: ..\SYSTEM\sys\../../HAREWARE/MOTOR_CONTROL/motor_control.h
..\obj\sys.o: E:\Keil_v5\ARM\ARMCC\Bin\..\include\stdbool.h
..\obj\sys.o: E:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
..\obj\sys.o: E:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
..\obj\sys.o: E:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
