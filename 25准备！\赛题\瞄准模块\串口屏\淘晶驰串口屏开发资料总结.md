# 淘晶驰串口屏开发资料总结

## 📋 资料概述

### 核心文档资源
- **完整技术文档库**: tjcwiki目录包含HTML格式的完整开发文档
- **版本信息**: 淘晶驰串口屏资料中心 v1.1.0 (2025-07-24)
- **字库资源**: ziku.zi中文字库文件
- **文档模块**: 11个主要技术模块，涵盖从入门到高级应用

### 文档结构
| 模块 | 内容 | 重要程度 |
|------|------|----------|
| **快速入门** | 硬件连接、软件安装、首次调试 | ⭐⭐⭐⭐⭐ |
| **串口屏调试** | 调试工具、通信测试、常见问题 | ⭐⭐⭐⭐⭐ |
| **控件详解** | 25+种控件的详细使用说明 | ⭐⭐⭐⭐⭐ |
| **基本指令集** | 常用指令、高级指令、文件操作 | ⭐⭐⭐⭐⭐ |
| **系统变量** | 系统参数、IO控制、时钟管理 | ⭐⭐⭐⭐ |
| **书写语法** | 编程规则、语法结构 | ⭐⭐⭐⭐ |
| **返回数据格式** | 通信协议、事件处理 | ⭐⭐⭐⭐ |
| **高级应用** | 扩展功能、特殊应用 | ⭐⭐⭐ |
| **资料下载** | 开发工具、示例工程 | ⭐⭐⭐ |
| **常见问题** | 136个FAQ | ⭐⭐⭐ |
| **电赛专题** | 电赛专用指导 | ⭐⭐⭐⭐⭐ |

## 🎯 产品系列分析

### 产品线对比

| 系列 | 定位 | 尺寸范围 | 分辨率 | Flash容量 | 功耗特性 | 扩展功能 | 价格区间 |
|------|------|----------|--------|-----------|----------|----------|----------|
| **X5** | 高端旗舰 | 4.0"-10.1" | 最高1024×600 | 128M | 中等 | RTC+扩展IO | 高 |
| **X2** | 中级性能 | 4.0"-7.0" | 480×480-800×480 | 8-16M | 中等 | 部分扩展 | 中高 |
| **X3** | 简化高性能 | 4.3"-7.0" | 480×272-800×480 | 8-16M | 中等 | 无扩展 | 中高 |
| **K0** | 经济实用 | 2.2"-7.0" | 320×240-800×480 | 16-32M | 低(15mA休眠) | RTC+扩展IO | 低 |
| **T1** | 超低功耗 | 1.8"-3.5" | 160×128-480×320 | 4-8M | 极低(0.25mA) | 无扩展 | 极低 |

### 瞄准模块推荐选型

#### 🏆 **首选：K0系列**

**推荐型号**：
- **TJC4827K043**（4.3寸，480×272）- 主力推荐
- **TJC3224K028**（2.8寸，320×240）- 紧凑型选择

**选择理由**：
1. ✅ **性价比最优** - 功能完整，价格适中
2. ✅ **功耗友好** - 休眠15mA，工作85-510mA
3. ✅ **扩展完整** - 有RTC时钟、扩展IO、eeprom
4. ✅ **规格合适** - 分辨率和尺寸适合瞄准显示
5. ✅ **开发友好** - 资料齐全，调试方便

**技术规格**：
- **处理器**：48-108MHz（性能够用）
- **内存**：3584-8192 Bytes运行内存
- **存储**：16-32M Flash
- **电压**：3.3-5.2V（兼容性好）
- **通信**：UART（TTL 3.3V/5V）
- **工作温度**：-20℃ to +70℃

#### 🥈 **备选：T1系列**（功耗敏感场景）

**适用场景**：
- 电池供电要求极高
- 显示内容简单
- 成本预算有限

**限制**：无扩展IO，功能受限

## 🔧 核心技术要点

### 控件系统

#### 瞄准模块关键控件

**1. Button（按钮控件）**
```javascript
// 基本用法
b0.txt="启动瞄准"        // 设置按钮文本
b0.bco=63488           // 设置背景色
b0.pco=65535           // 设置字体色

// 状态切换示例
if(b0.txt=="启动")
{
    b0.txt="停止"
    b0.bco=2016        // 红色背景
}else{
    b0.txt="启动"
    b0.bco=1024        // 绿色背景
}
```

**2. Number（数字控件）**
```c
// 单片机控制示例
printf("n0.val=%d\xff\xff\xff", target_x);      // 目标X坐标
printf("n1.val=%d\xff\xff\xff", target_y);      // 目标Y坐标
printf("n2.val=%d\xff\xff\xff", distance);      // 距离值
```

**3. Gauge（指针控件）**
```c
// 角度指示（0-360度）
printf("z0.val=%d\xff\xff\xff", azimuth_angle);   // 方位角
printf("z1.val=%d\xff\xff\xff", elevation_angle); // 俯仰角
```

**4. Text（文本控件）**
```c
// 状态信息显示
printf("t0.txt=\"目标: 锁定\"\xff\xff\xff");
printf("t1.txt=\"状态: 就绪\"\xff\xff\xff");
printf("t2.txt=\"模式: 自动\"\xff\xff\xff");
```

**5. Progress_bar（进度条控件）**
```c
// 信号强度显示
printf("j0.val=%d\xff\xff\xff", signal_strength);
```

### 指令系统

#### 基本指令速查

| 指令 | 功能 | 语法 | 应用场景 |
|------|------|------|----------|
| `page` | 页面跳转 | `page main` | 界面切换 |
| `prints` | 串口发送 | `prints n0.val,0` | 数据回传 |
| `vis` | 显示控制 | `vis b0,1` | 控件显示/隐藏 |
| `tsw` | 触摸控制 | `tsw b0,0` | 启用/禁用触摸 |
| `click` | 模拟点击 | `click b0,1` | 程序触发事件 |
| `get` | 获取属性 | `get n0.val` | 读取控件值 |
| `covx` | 类型转换 | `covx t0.txt,n0.val,0,0` | 数据转换 |

#### 系统变量

| 变量 | 功能 | 范围 | 瞄准模块用途 |
|------|------|------|--------------|
| `sys0-sys2` | 通用变量 | 整型 | 临时计算、循环控制 |
| `dp` | 当前页面 | 页面ID | 页面状态管理 |
| `dim` | 背光亮度 | 0-100 | 节能控制 |
| `rtc0-rtc6` | 时钟变量 | 时间 | 时间戳、定时 |
| `pio0-pio7` | 扩展IO | 0/1 | 外设控制 |
| `pwm4-pwm7` | PWM输出 | 0-100 | 伺服控制 |
| `tch0-tch3` | 触摸坐标 | 坐标值 | 手势识别 |

### 通信协议

#### 指令格式
```c
// 标准格式：指令内容 + 0xFF 0xFF 0xFF
printf("n0.val=100\xff\xff\xff");           // 数字赋值
printf("t0.txt=\"Hello\"\xff\xff\xff");     // 文本赋值
printf("page main\xff\xff\xff");            // 页面跳转
```

#### 返回数据格式

**事件返回**：
```c
// 按钮点击：65 页面ID 控件ID 事件类型 FF FF FF
case 0x65:
    page_id = data[1];
    control_id = data[2];
    event_type = data[3];
    break;

// 页面切换：66 页面ID FF FF FF
case 0x66:
    current_page = data[1];
    break;
```

**指令状态返回**：
| 返回码 | 含义 | 处理方式 |
|--------|------|----------|
| `01 FF FF FF` | 指令成功 | 继续执行 |
| `1C FF FF FF` | 赋值失败 | 检查语法 |
| `02 FF FF FF` | 控件ID无效 | 检查控件 |
| `03 FF FF FF` | 页面ID无效 | 检查页面 |

## 🎨 瞄准模块界面设计

### 推荐界面布局

#### 主控制页面
```
┌─────────────────────────────────────┐
│  瞄准系统控制台                      │
├─────────────────────────────────────┤
│  目标坐标: [123] , [456]             │
│  距离: [1500]m  角度: [45]°          │
├─────────────────────────────────────┤
│  [启动瞄准]  [停止]  [设置]          │
├─────────────────────────────────────┤
│  状态: 就绪    精度: ████████ 90%    │
└─────────────────────────────────────┘
```

#### 实时显示页面
```
┌─────────────────────────────────────┐
│        实时瞄准显示                  │
├─────────────────────────────────────┤
│    方位角                俯仰角      │
│       ◐                    ◑       │
│     [180°]                [30°]     │
├─────────────────────────────────────┤
│  X: [1234]  Y: [5678]  D: [1500]m   │
│  状态: [目标锁定]  [返回主页]        │
└─────────────────────────────────────┘
```

### 控件配置示例

#### 主要控件属性设置
```javascript
// 按钮控件配置
b0.bco=1024      // 绿色背景（启动）
b0.bco2=2016     // 红色背景（停止）
b0.pco=65535     // 白色字体
b0.font=1        // 使用字库1

// 数字控件配置
n0.pco=65535     // 白色数字
n0.font=3        // 大号字体
n0.format=0      // 十进制显示

// 文本控件配置
t0.pco=65535     // 白色文字
t0.font=1        // 中号字体
t0.txt_maxl=20   // 最大20字符
```

## 💻 开发环境配置

### 必需软件工具

1. **串口屏编辑器**
   - 下载地址：tjcwiki/download目录
   - 功能：界面设计、控件配置、代码编写

2. **调试工具**
   - **SSCOM串口助手** - 基础调试
   - **淘晶驰调试助手** - 专用工具
   - **VSPD虚拟串口** - 仿真调试

3. **字库文件**
   - **ziku.zi** - 中文字库
   - 支持GB2312编码

### 开发流程

#### 1. 硬件连接
```
串口屏          单片机
  VCC    ←→     5V/3.3V
  GND    ←→     GND
  RX     ←→     TX（单片机发送）
  TX     ←→     RX（单片机接收）
```

#### 2. 波特率设置
```c
// 推荐波特率：9600, 38400, 115200
// 设置方法：
printf("baud=115200\xff\xff\xff");  // 动态设置
// 或在编辑器中预设置
```

#### 3. 基础测试代码
```c
#include <stdio.h>

void hmi_init(void) {
    // 设置波特率
    printf("baud=115200\xff\xff\xff");
    delay_ms(100);
    
    // 跳转到主页面
    printf("page main\xff\xff\xff");
    delay_ms(50);
    
    // 初始化显示
    printf("t0.txt=\"系统初始化...\"\xff\xff\xff");
    delay_ms(50);
}

void update_target_info(int x, int y, int distance) {
    printf("n0.val=%d\xff\xff\xff", x);
    printf("n1.val=%d\xff\xff\xff", y);
    printf("n2.val=%d\xff\xff\xff", distance);
}

void set_status(char* status) {
    printf("t1.txt=\"%s\"\xff\xff\xff", status);
}
```

## 🚨 关键注意事项

### 开发陷阱
1. **字符编码**：必须使用GB2312，避免UTF-8乱码
2. **指令结尾**：必须添加`\xff\xff\xff`结束符
3. **波特率匹配**：确保单片机与串口屏波特率一致
4. **控件命名**：避免使用系统保留关键字
5. **内存管理**：文本控件要设置合适的txt_maxl

### 性能优化
1. **批量更新**：避免频繁单个控件更新
2. **条件刷新**：仅在数据变化时更新显示
3. **页面设计**：合理使用全局/局部控件
4. **资源管理**：图片和字库文件大小控制

### 调试技巧
1. **使用bkcmd**：开启指令返回确认调试
2. **串口监控**：实时监控通信数据
3. **分步测试**：从简单控件开始逐步复杂化
4. **错误码参考**：建立返回码处理机制

## 📚 进阶应用

### 扩展IO应用
```c
// 控制外部LED
printf("pio0=1\xff\xff\xff");  // 点亮LED
printf("pio0=0\xff\xff\xff");  // 熄灭LED

// PWM控制伺服电机
printf("pwmf=50\xff\xff\xff");    // 设置PWM频率50Hz
printf("pwm4=75\xff\xff\xff");    // 设置占空比75%
```

### 数据存储
```c
// 保存用户设置到EEPROM
printf("wepo n0.val,100\xff\xff\xff");  // 保存数据到地址100
printf("repo n0.val,100\xff\xff\xff");  // 从地址100读取数据
```

### 定时任务
```javascript
// 定时器控件设置
tm0.tim=1000    // 1秒定时
tm0.en=1        // 启用定时器

// 定时器事件处理
if(tm0.en==0)
{
    // 定时时间到，执行任务
    n0.val++
    tm0.en=1    // 重新启动
}
```

## 🎯 瞄准模块集成方案

### 系统架构
```
┌─────────────┐    UART     ┌─────────────┐
│   主控MCU   │ ←────────→  │  串口屏K0   │
│ (MSPM0G3507)│             │  (4.3寸)    │
└─────────────┘             └─────────────┘
       │                           │
       │ PWM/IO                    │ 扩展IO
       ↓                           ↓
┌─────────────┐             ┌─────────────┐
│  步进电机   │             │  状态指示   │
│  驱动系统   │             │   LED等     │
└─────────────┘             └─────────────┘
```

### 通信协议设计
```c
// 瞄准系统数据结构
typedef struct {
    uint16_t target_x;      // 目标X坐标
    uint16_t target_y;      // 目标Y坐标
    uint16_t distance;      // 目标距离
    int16_t  azimuth;       // 方位角(-180~180)
    int16_t  elevation;     // 俯仰角(-90~90)
    uint8_t  lock_status;   // 锁定状态(0:搜索,1:跟踪,2:锁定)
    uint8_t  system_mode;   // 系统模式(0:手动,1:自动)
} AimingData_t;

// 数据更新函数
void updateHMI(AimingData_t *data) {
    // 更新坐标显示
    printf("n0.val=%d\xff\xff\xff", data->target_x);
    printf("n1.val=%d\xff\xff\xff", data->target_y);
    printf("n2.val=%d\xff\xff\xff", data->distance);
    
    // 更新角度指示
    printf("z0.val=%d\xff\xff\xff", (data->azimuth + 180));  // 转换为0-360
    printf("z1.val=%d\xff\xff\xff", (data->elevation + 90)); // 转换为0-180
    
    // 更新状态文本
    const char* status_text[] = {"搜索中", "跟踪中", "已锁定"};
    printf("t0.txt=\"%s\"\xff\xff\xff", status_text[data->lock_status]);
    
    // 更新系统模式
    const char* mode_text[] = {"手动", "自动"};
    printf("t1.txt=\"%s\"\xff\xff\xff", mode_text[data->system_mode]);
}
```

### 电赛应用建议
1. **显示内容优先级**：目标坐标 > 状态信息 > 角度指示 > 系统设置
2. **操作简化**：减少复杂菜单，突出核心功能
3. **实时性要求**：坐标更新频率10-20Hz，状态更新1-5Hz
4. **功耗管理**：非工作时降低亮度，利用休眠功能
5. **可靠性设计**：添加通信超时检测和状态恢复机制

---

**📄 文档版本**: v1.0  
**📅 最后更新**: 2025-07-30  
**📧 技术支持**: 基于淘晶驰官方文档 v1.1.0  
**⭐ 推荐配置**: K0系列 + MSPM0G3507 + D36A步进驱动