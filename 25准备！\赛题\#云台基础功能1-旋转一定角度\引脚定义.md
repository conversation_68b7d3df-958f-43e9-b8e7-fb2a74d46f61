STM32F407ZGT6 是 STM32F4 系列的微控制器，采用 LQFP144 封装。该文档提取了其引脚定义，基于官方数据手册。表格格式类似于 STM32F103C8T6 的引脚定义，包括引脚号、引脚名称、I/O 结构、主功能、复用功能和备注。



\- \*\*I/O 结构\*\*：I (输入)、O (输出)、I/O (双向)、S (电源/地)。

\- \*\*主功能\*\*：默认功能，通常为 GPIO 或电源。

\- \*\*复用功能\*\*：备选功能，如定时器、串口等（列出常见复用，非 exhaustive）。

\- \*\*备注\*\*：特殊说明，如 5V 耐压、JTAG 等。



\## 引脚定义表格



| 引脚 | 引脚名称 | I/O结构 | 主功能 | 复用功能 | 备注 |

|------|----------|---------|--------|----------|------|

| 1 | PE2 | I/O | FT | TIM3\_CH2, TRACECK, FSMC\_A23, SAI1\_SD\_B, ETH\_MII\_TXD3, EVENTOUT |  

| 2 | PE3 | I/O | FT | TIM3\_CH3, TRACED0, FSMC\_A19, SAI1\_FS\_B, EVENTOUT | 

| 3 | PE4 | I/O | FT | TIM3\_CH4, TRACED1, FSMC\_A20, SAI1\_SCK\_B, DCMI\_D4, EVENTOUT | 

| 4 | PE5 | I/O | FT | TIM3\_CH1, TRACED2, FSMC\_A21, SAI1\_MCLK\_B, TIM9\_CH1, DCMI\_D6, EVENTOUT | 

| 5 | PE6 | I/O | FT | TIM10\_CH1, TRACED3, FSMC\_A22, SAI1\_SD\_A, TIM9\_CH2, DCMI\_D7, EVENTOUT | 

| 6 | VBAT | S | - | - | 

| 7 | PC13 | I/O | TTa | - | RTC\_TAMP1, RTC\_TS, RTC\_OUT, TAMP1, WKUP2, TIM1\_BKIN2 |

| 8 | PC14-OSC32\_IN | I/O | TTa | PC14 | OSC32\_IN |

| 9 | PC15-OSC32\_OUT | I/O | TTa | PC15 | OSC32\_OUT |

| 10 | PH0-OSC\_IN | I/O | TTa | PH0 | OSC\_IN |

| 11 | PH1-OSC\_OUT | I/O | TTa | PH1 | OSC\_OUT |

| 12 | NRST | I/O | RST | - | 系统复位 |

| 13 | PC0 | I/O | FT | OTG\_HS\_ULPI\_STP | ADC123\_IN10 |

| 14 | PC1 | I/O | FT | ETH\_MDC | ADC123\_IN11 |

| 15 | PC2 | I/O | FT | ETH\_MII\_TXD2 | ADC123\_IN12 |

| 16 | PC3 | I/O | FT | ETH\_MII\_TX\_CLK | ADC123\_IN13 |

| 17 | VDD | S | - | - |

| 18 | VSSA | S | - | - | 

| 19 | VREF- | S | - | - |

| 20 | VREF+ | S | - | - | 

| 21 | VDDA | S | - | - | 

| 22 | PA0 | I/O | FT | TIM2\_CH1\_ETR, TIM5\_CH1, TIM8\_ETR, USART2\_CTS, UART4\_TX | ADC12\_IN0, WKUP1 |

| 23 | PA1 | I/O | FT | TIM2\_CH2, TIM5\_CH2, USART2\_RTS, UART4\_RX, ETH\_MII\_RX\_CLK, ETH\_RMII\_REF\_CLK | ADC12\_IN1 |

| 24 | PA2 | I/O | FT | TIM2\_CH3, TIM5\_CH3, TIM9\_CH1, USART2\_TX, ETH\_MDIO | ADC12\_IN2 |

| 25 | PA3 | I/O | FT | TIM2\_CH4, TIM5\_CH4, TIM9\_CH2, USART2\_RX, OTG\_HS\_ULPI\_D0, ETH\_MII\_COL | ADC12\_IN3 |

| 26 | VSS | S | - | - | 

| 27 | VDD | S | - | - | 

| 28 | PA4 | I/O | FT | SPI1\_NSS, SPI3\_NSS, USART2\_CK | ADC12\_IN4, DAC\_OUT1 |

| 29 | PA5 | I/O | FT | SPI1\_SCK | ADC12\_IN5, DAC\_OUT2 |

| 30 | PA6 | I/O | FT | SPI1\_MISO, TIM1\_BKIN, TIM3\_CH1, TIM8\_BKIN, TIM13\_CH1 | DCMI\_PIXCLK |

| 31 | PA7 | I/O | FT | SPI1\_MOSI, TIM1\_CH1N, TIM3\_CH2, TIM8\_CH1N, TIM14\_CH1, ETH\_MII\_RX\_DV, ETH\_RMII\_CRS\_DV | - |

| 32 | PC4 | I/O | FT | ETH\_MII\_RXD0, ETH\_RMII\_RXD0 | ADC12\_IN14 |

| 33 | PC5 | I/O | FT | ETH\_MII\_RXD1, ETH\_RMII\_RXD1 | ADC12\_IN15 |

| 34 | PB0 | I/O | FT | TIM1\_CH2N, TIM3\_CH3, TIM8\_CH2N, OTG\_HS\_ULPI\_D1, ETH\_MII\_RXD2 | ADC12\_IN8 |

| 35 | PB1 | I/O | FT | TIM1\_CH3N, TIM3\_CH4, TIM8\_CH3N, OTG\_HS\_ULPI\_D2, ETH\_MII\_RXD3 | ADC12\_IN9 |

| 36 | PB2 | I/O | FT | - | - |

| 37 | PE7 | I/O | FT | TIM1\_ETR, FSMC\_D4 | DCMI\_D3 |

| 38 | PE8 | I/O | FT | TIM1\_CH1N, FSMC\_D5 | DCMI\_D4 |

| 39 | PE9 | I/O | FT | TIM1\_CH1, FSMC\_D6 | DCMI\_D5 |

| 40 | PE10 | I/O | FT | TIM1\_CH2N, FSMC\_D7 | DCMI\_D6 |

| 41 | PE11 | I/O | FT | TIM1\_CH2, FSMC\_D8 | DCMI\_D7 |

| 42 | PE12 | I/O | FT | TIM1\_CH3N, FSMC\_D9 | DCMI\_D8 |

| 43 | PE13 | I/O | FT | TIM1\_CH3, FSMC\_D10 | DCMI\_D9 |

| 44 | PE14 | I/O | FT | TIM1\_CH4, FSMC\_D11 | DCMI\_D10 |

| 45 | PE15 | I/O | FT | TIM1\_BKIN, FSMC\_D12 | DCMI\_D11 |

| 46 | VSS | S | - | - | 

| 47 | VDD | S | - | - |

| 48 | PB10 | I/O | FT | I2C2\_SCL, TIM2\_CH3, USART3\_TX, OTG\_HS\_ULPI\_D3, ETH\_MII\_RX\_ER | - |

| 49 | PB11 | I/O | FT | I2C2\_SDA, TIM2\_CH4, USART3\_RX, OTG\_HS\_ULPI\_D4, ETH\_MII\_TX\_EN, ETH\_RMII\_TX\_EN | - |

| 50 | VCAP\_1 | S | - | - | 

| 51 | VSS | S | - | - | 

| 52 | VDD | S | - | - |

| 53 | PB12 | I/O | FT | I2C2\_SMBA, TIM1\_BKIN, USART3\_CK, SPI2\_NSS, I2S2\_WS, CAN2\_RX, OTG\_HS\_ULPI\_D5, ETH\_MII\_TXD0, ETH\_RMII\_TXD0 | - |

| 54 | PB13 | I/O | FT | USART3\_CTS, TIM1\_CH1N, SPI2\_SCK, I2S2\_CK, CAN2\_TX, OTG\_HS\_ULPI\_D6, ETH\_MII\_TXD1, ETH\_RMII\_TXD1 | - |

| 55 | PB14 | I/O | FT | USART3\_RTS, TIM1\_CH2N, TIM8\_CH2N, TIM12\_CH1, SPI2\_MISO, OTG\_HS\_DM | - |

| 56 | PB15 | I/O | FT | RTC\_REFIN, TIM1\_CH3N, TIM8\_CH3N, TIM12\_CH2, SPI2\_MOSI, I2S2\_SD, OTG\_HS\_DP | - |

| 57 | PD8 | I/O | FT | FSMC\_D13, USART3\_TX | - |

| 58 | PD9 | I/O | FT | FSMC\_D14, USART3\_RX | - |

| 59 | PD10 | I/O | FT | FSMC\_D15, USART3\_CK | - |

| 60 | PD11 | I/O | FT | FSMC\_A16, FSMC\_CLE, USART3\_CTS | - |

| 61 | PD12 | I/O | FT | FSMC\_A17, FSMC\_ALE, TIM4\_CH1, USART3\_RTS | - |

| 62 | PD13 | I/O | FT | FSMC\_A18, TIM4\_CH2 | - |

| 63 | VSS | S | - | - | 

| 64 | VDD | S | - | - | 

| 65 | PD14 | I/O | FT | FSMC\_D0, TIM4\_CH3 | - |

| 66 | PD15 | I/O | FT | FSMC\_D1, TIM4\_CH4 | - |

| 67 | PC6 | I/O | FT | TIM3\_CH1, TIM8\_CH1, I2S2\_MCK, USART6\_TX, SDIO\_D6, DCMI\_D0 | - |

| 68 | PC7 | I/O | FT | TIM3\_CH2, TIM8\_CH2, I2S3\_MCK, USART6\_RX, SDIO\_D7, DCMI\_D1 | - |

| 69 | PC8 | I/O | FT | TIM3\_CH3, TIM8\_CH3, USART6\_CK, SDIO\_D0, DCMI\_D2 | - |

| 70 | PC9 | I/O | FT | TIM3\_CH4, TIM8\_CH4, I2C3\_SDA, I2S\_CKIN, SDIO\_D1, DCMI\_D3 | - |

| 71 | PA8 | I/O | FT | USART1\_CK, TIM1\_CH1, I2C3\_SCL, OTG\_FS\_SOF, MCO1 | - |

| 72 | PA9 | I/O | FT | USART1\_TX, TIM1\_CH2, I2C3\_SMBA, DCMI\_D0 | VBUS\_FS |

| 73 | PA10 | I/O | FT | USART1\_RX, TIM1\_CH3, OTG\_FS\_ID, DCMI\_D1 | - |

| 74 | PA11 | I/O | FT | USART1\_CTS, TIM1\_CH4, CAN1\_RX, OTG\_FS\_DM | - |

| 75 | PA12 | I/O | FT | USART1\_RTS, TIM1\_ETR, CAN1\_TX, OTG\_FS\_DP | - |

| 76 | PA13 | I/O | FT | JTMS-SWDIO | - |

| 77 | VSS | S | - | - | 

| 78 | VDD | S | - | - | 

| 79 | PA14 | I/O | FT | JTCK-SWCLK | - |

| 80 | PA15 | I/O | FT | JTDI, TIM2\_CH1, TIM2\_ETR, SPI1\_NSS, SPI3\_NSS | - |

| 81 | PC10 | I/O | FT | SPI3\_SCK, I2S3\_CK, USART3\_TX, UART4\_TX, SDIO\_D2, DCMI\_D8 | - |

| 82 | PC11 | I/O | FT | SPI3\_MISO, USART3\_RX, UART4\_RX, SDIO\_D3, DCMI\_D4 | - |

| 83 | PC12 | I/O | FT | SPI3\_MOSI, I2S3\_SD, USART3\_CK, UART5\_TX, SDIO\_CK, DCMI\_D9 | - |

| 84 | PD0 | I/O | FT | FSMC\_D2, CAN1\_RX | - |

| 85 | PD1 | I/O | FT | FSMC\_D3, CAN1\_TX | - |

| 86 | PD2 | I/O | FT | TIM3\_ETR, UART5\_RX, SDIO\_CMD, DCMI\_D11 | - |

| 87 | PD3 | I/O | FT | FSMC\_CLK, USART2\_CTS | - |

| 88 | PD4 | I/O | FT | FSMC\_NOE, USART2\_RTS | - |

| 89 | PD5 | I/O | FT | FSMC\_NWE, USART2\_TX | - |

| 90 | PD6 | I/O | FT | FSMC\_NWAIT, USART2\_RX, SDIO\_D2 | - |

| 91 | PD7 | I/O | FT | FSMC\_NE1, FSMC\_NCE2, USART2\_CK | - |

| 92 | VSS | S | - | - | 

| 93 | VDD | S | - | - |

| 94 | PG2 | I/O | FT | FSMC\_A12 | - |

| 95 | PG3 | I/O | FT | FSMC\_A13 | - |

| 96 | PG4 | I/O | FT | FSMC\_A14 | - |

| 97 | PG5 | I/O | FT | FSMC\_A15 | - |

| 98 | PG6 | I/O | FT | FSMC\_INT | - |

| 99 | PG7 | I/O | FT | FSMC\_INT, USART6\_CK | - |

| 100 | PG8 | I/O | FT | SPI6\_NSS, USART6\_RTS, SDIO\_D3 | - |

| 101 | VCAP\_2 | S | - | - | 

| 102 | VSS | S | - | - | 

| 103 | VDD | S | - | - | 

| 104 | PG9 | I/O | FT | FSMC\_NE2, FSMC\_NCE3, USART6\_RX | - |

| 105 | PG10 | I/O | FT | FSMC\_NE3, USART6\_TX | - |

| 106 | PG11 | I/O | FT | FSMC\_NE4, ETH\_MII\_TX\_EN, ETH\_RMII\_TX\_EN | - |

| 107 | PG12 | I/O | FT | FSMC\_NE4, USART6\_RTS | - |

| 108 | PG13 | I/O | FT | FSMC\_A24, USART6\_CTS, ETH\_MII\_TXD0, ETH\_RMII\_TXD0 | - |

| 109 | PG14 | I/O | FT | FSMC\_A25, USART6\_TX, ETH\_MII\_TXD1, ETH\_RMII\_TXD1 | - |

| 110 | PG15 | I/O | FT | FSMC\_NCE2, USART6\_CTS | - |

| 111 | PB3 | I/O | FT | JTDO, TIM2\_CH2, SPI1\_SCK, SPI3\_SCK | - |

| 112 | PB4 | I/O | FT | NJTRST, TIM3\_CH1, SPI1\_MISO, SPI3\_MISO | - |

| 113 | PB5 | I/O | FT | I2C1\_SMBA, TIM3\_CH2, SPI1\_MOSI, SPI3\_MOSI | - |

| 114 | PB6 | I/O | FT | I2C1\_SCL, TIM4\_CH1, USART1\_TX | - |

| 115 | PB7 | I/O | FT | I2C1\_SDA, TIM4\_CH2, USART1\_RX | PVD\_IN |

| 116 | BOOT0 | I | FT | - | 

| 117 | PB8 | I/O | FT | TIM4\_CH3, TIM10\_CH1, I2C1\_SCL, CAN1\_RX, SDIO\_D4 | - |

| 118 | PB9 | I/O | FT | TIM4\_CH4, TIM11\_CH1, I2C1\_SDA, CAN1\_TX, SDIO\_D5 | - |

| 119 | PE0 | I/O | FT | TIM4\_ETR, FSMC\_NBL0 | DCMI\_D2 |

| 120 | PE1 | I/O | FT | FSMC\_NBL1 | DCMI\_D3 |

| 121 | VSS | S | - | - | 

| 122 | VDD | S | - | - | 

| 123 | PI0 | I/O | FT | TIM5\_CH1, SPI2\_NSS, I2S2\_WS | DCMI\_D13 |

| 124 | PI1 | I/O | FT | TIM5\_CH2, SPI2\_SCK, I2S2\_CK | DCMI\_D8 |

| 125 | PI2 | I/O | FT | TIM5\_CH3, SPI2\_MISO | DCMI\_D9 |

| 126 | PI3 | I/O | FT | TIM5\_CH4, SPI2\_MOSI, I2S2\_SD | DCMI\_D10 |

| 127 | PI4 | I/O | FT | TIM5\_ETR | DCMI\_D5 |

| 128 | PI5 | I/O | FT | TIM8\_CH1 | DCMI\_VSYNC |

| 129 | PI6 | I/O | FT | TIM8\_CH2 | DCMI\_D6 |

| 130 | PI7 | I/O | FT | TIM8\_CH3 | DCMI\_D7 |

| 131 | PI8 | I/O | TTa | - | RTC\_TAMP1, RTC\_TAMP2, RTC\_TS |

| 132 | PI9 | I/O | FT | CAN1\_RX | - |

| 133 | PI10 | I/O | FT | ETH\_MII\_RX\_ER | - |

| 134 | PI11 | I/O | FT | OTG\_HS\_ULPI\_DIR | - |

| 135 | VSS | S | - | - | 

| 136 | VDD | S | - | - | 

| 137 | PF0 | I/O | FT | I2C2\_SDA, FSMC\_A0 | - |

| 138 | PF1 | I/O | FT | I2C2\_SCL, FSMC\_A1 | - |

| 139 | PF2 | I/O | FT | I2C2\_SMBA, FSMC\_A2 | - |

| 140 | PF3 | I/O | FT | FSMC\_A3 | ADC3\_IN9 |

| 141 | PF4 | I/O | FT | FSMC\_A4 | ADC3\_IN14 |

| 142 | PF5 | I/O | FT | FSMC\_A5 | ADC3\_IN15 |

| 143 | VSS | S | - | - | 

| 144 | VDD | S | - | - | 

