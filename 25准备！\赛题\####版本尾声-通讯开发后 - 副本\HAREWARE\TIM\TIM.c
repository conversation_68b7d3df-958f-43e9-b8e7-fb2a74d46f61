#include "../../SYSTEM/sys/sys.h"
#include "TIM.h"

/**
 * @brief  TIM2定时器初始化函数
 * @param  arr: 自动重装载值，决定定时周期
 * @param  psc: 预分频系数，决定计数频率
 * @retval None
 * @note   配置TIM2为定时中断模式，用于周期性任务调度
 *         当前配置: 10ms定时中断 (arr=199, psc=7199)
 */
void TIM2_Init(u16 arr, u16 psc)
{
	TIM_TimeBaseInitTypeDef TIM_BaseStructure;
	NVIC_InitTypeDef NVIC_InitStructure;
	
	/* 使能TIM2时钟 */
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2, ENABLE);  // TIM2挂载在APB1总线上
	
	/* 配置TIM2基本参数 */
	TIM_BaseStructure.TIM_Period = arr;                    // 自动重装载值 (ARR)
	TIM_BaseStructure.TIM_Prescaler = psc;                 // 预分频系数 (PSC)
	TIM_BaseStructure.TIM_CounterMode = TIM_CounterMode_Up; // 向上计数模式
	TIM_BaseStructure.TIM_ClockDivision = 0;               // 时钟分割 (不分割)
	TIM_TimeBaseInit(TIM2, &TIM_BaseStructure);
	
	/* 使能TIM2更新中断 */
	TIM_ITConfig(TIM2, TIM_IT_Update, ENABLE);
	
	/* 配置TIM2中断优先级 */
	NVIC_InitStructure.NVIC_IRQChannel = TIM2_IRQn;                    // TIM2中断通道
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;          // 抢占优先级1
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;                 // 子优先级1
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;                    // 使能中断通道
	NVIC_Init(&NVIC_InitStructure);
	
	/* 启动TIM2定时器 */
	TIM_Cmd(TIM2, ENABLE);
}

/**
 * @note   TIM2中断处理函数在stm32f4xx_it.c中定义为TIM2_IRQHandler
 *         当前配置下每10ms触发一次中断，可用于:
 *         - 系统定时任务
 *         - 步进电机控制时序
 *         - 传感器数据采集
 *         - 状态机更新等周期性处理
 */
