#include "StateMachine.h"
#include "ManualRecord.h"
#include "Timer.h"
#include "Delay.h"
#include "Bluetooth.h"
#include <stdio.h>
#include <stddef.h>

/**
 * 状态机初始化 - 激光云台系统核心
 * @param sm 状态机结构体指针
 */
void StateMachine_Init(LaserGimbalState_t* sm)
{
    if (sm == NULL) return;
    
    // 初始化状态
    sm->current_state = STATE_WAIT_POINT_A;
    sm->last_state = STATE_WAIT_POINT_A;
    
    // 清空点位记录
    sm->point_a.x = 0.0f;
    sm->point_a.y = 0.0f;
    sm->point_b.x = 0.0f;
    sm->point_b.y = 0.0f;
    sm->servo_a.pan = 120.0f;
    sm->servo_a.tilt = 120.0f;
    sm->servo_b.pan = 120.0f;
    sm->servo_b.tilt = 120.0f;
    sm->point_a_recorded = 0;
    sm->point_b_recorded = 0;
    
    // 初始化移动控制
    sm->move_direction = DIRECTION_A_TO_B;
    sm->is_moving = 0;
    sm->move_start_time = 0;
    sm->last_step_time = 0;
    
    // 系统状态
    sm->system_ready = 1;
    sm->error_flag = 0;
    sm->last_key_time = 0;
    
    // 初始化路径插值对象
    WallPoint_t default_a = {-200.0f, -200.0f};
    WallPoint_t default_b = {200.0f, 200.0f};
    Path_Initialize(&sm->path, default_a, default_b);
    sm->path.is_active = 0;  // 默认不激活

    // 初始化定时器
    Timer_Init();
    Timer_Start();
}

/**
 * 状态机主更新函数 - 需要在主循环中定期调用
 * @param sm 状态机结构体指针
 * @return 错误代码
 */
StateMachineError_t StateMachine_Update(LaserGimbalState_t* sm)
{
    if (sm == NULL) return SM_ERROR_NOT_READY;

    // 扫描按键
    Key_Scan();
    
    // 处理按键事件
    if (Key_IsClicked(KEY_RECORD)) {
        StateMachineError_t error = StateMachine_HandleRecordKey(sm);
        Key_ClearEvent(KEY_RECORD);
        if (error != SM_OK) return error;
    }
    
    // 注释：旧的KEY_TRIGGER处理，新版本使用三按键方案
    // if (Key_IsClicked(KEY_TRIGGER)) {
    //     StateMachineError_t error = StateMachine_HandleTriggerKey(sm);
    //     Key_ClearEvent(KEY_TRIGGER);
    //     if (error != SM_OK) return error;
    // }
    
    // 根据当前状态执行相应逻辑
    switch (sm->current_state) {
        case STATE_WAIT_POINT_A:
            // 等待记录A点，无特殊处理
            break;
            
        case STATE_WAIT_POINT_B:
            // 等待记录B点，无特殊处理
            break;
            
        case STATE_AUTO_MOVING:
            // 更新自动移动
            return StateMachine_UpdateAutoMovement(sm);
            
        default:
            sm->error_flag = 1;
            return SM_ERROR_INVALID_STATE;
    }
    
    return SM_OK;
}

/**
 * 处理记录按键事件
 * @param sm 状态机结构体指针
 * @return 错误代码
 */
StateMachineError_t StateMachine_HandleRecordKey(LaserGimbalState_t* sm)
{
    if (sm == NULL) return SM_ERROR_NOT_READY;
    
    switch (sm->current_state) {
        case STATE_WAIT_POINT_A:
            return StateMachine_RecordPointA(sm);
            
        case STATE_WAIT_POINT_B:
            return StateMachine_RecordPointB(sm);
            
        case STATE_AUTO_MOVING:
            // 自动移动状态下，记录键用于停止移动
            return StateMachine_StopAutoMovement(sm);
            
        default:
            return SM_ERROR_INVALID_STATE;
    }
}

/**
 * 处理触发按键事件
 * @param sm 状态机结构体指针
 * @return 错误代码
 */
StateMachineError_t StateMachine_HandleTriggerKey(LaserGimbalState_t* sm)
{
    if (sm == NULL) return SM_ERROR_NOT_READY;
    
    switch (sm->current_state) {
        case STATE_WAIT_POINT_A:
        case STATE_WAIT_POINT_B:
            // 等待状态下，触发键无效
            break;
            
        case STATE_AUTO_MOVING:
            // 自动移动状态下，触发键用于切换方向
            return StateMachine_ToggleDirection(sm);
            
        default:
            return SM_ERROR_INVALID_STATE;
    }
    
    return SM_OK;
}

/**
 * 状态转换函数
 * @param sm 状态机结构体指针
 * @param new_state 新状态
 * @return 错误代码
 */
StateMachineError_t StateMachine_TransitionTo(LaserGimbalState_t* sm, SystemState_t new_state)
{
    if (sm == NULL) return SM_ERROR_NOT_READY;
    
    sm->last_state = sm->current_state;
    sm->current_state = new_state;
    
    // 状态进入处理
    switch (new_state) {
        case STATE_WAIT_POINT_A:
            // 进入等待A点状态
            break;
            
        case STATE_WAIT_POINT_B:
            // 进入等待B点状态
            break;
            
        case STATE_AUTO_MOVING:
            // 进入自动移动状态
            if (sm->point_a_recorded && sm->point_b_recorded) {
                return StateMachine_StartAutoMovement(sm);
            } else {
                return SM_ERROR_POINT_NOT_RECORDED;
            }
            
        default:
            return SM_ERROR_INVALID_STATE;
    }
    
    return SM_OK;
}

/**
 * 记录A点位置 - 使用新的手动记录模块
 * @param sm 状态机结构体指针
 * @return 错误代码
 */
StateMachineError_t StateMachine_RecordPointA(LaserGimbalState_t* sm)
{
    if (sm == NULL) return SM_ERROR_NOT_READY;

    // 简化版：直接读取当前舵机位置
    float pan_angle = 0, tilt_angle = 0;
    ServoError_t pan_error = Servo_ReadPosition(SERVO_PAN_ID, &pan_angle);
    ServoError_t tilt_error = Servo_ReadPosition(SERVO_TILT_ID, &tilt_angle);

    if (pan_error == SERVO_OK && tilt_error == SERVO_OK) {
        // 记录成功，保存数据
        sm->servo_a.pan = pan_angle;
        sm->servo_a.tilt = tilt_angle;

        // 简化的墙面坐标计算（假设1米距离）
        sm->point_a.x = 1000.0f * tan((pan_angle - 120.0f) * 3.14159f / 180.0f);
        sm->point_a.y = 1000.0f * tan((tilt_angle - 120.0f) * 3.14159f / 180.0f);

        sm->point_a_recorded = 1;

        // 发送蓝牙成功信息
        Bluetooth_SendKeyAction(sm, "Point A Recorded Successfully");

        // 转换到等待B点状态
        return StateMachine_TransitionTo(sm, STATE_WAIT_POINT_B);
    } else {
        // 记录失败，发送蓝牙错误信息
        Bluetooth_SendKeyAction(sm, "Point A Record Failed");
        return SM_ERROR_SERVO_COMMUNICATION;
    }
}

/**
 * 记录B点位置 - 使用新的手动记录模块
 * @param sm 状态机结构体指针
 * @return 错误代码
 */
StateMachineError_t StateMachine_RecordPointB(LaserGimbalState_t* sm)
{
    if (sm == NULL) return SM_ERROR_NOT_READY;

    // 简化版：直接读取当前舵机位置
    float pan_angle = 0, tilt_angle = 0;
    ServoError_t pan_error = Servo_ReadPosition(SERVO_PAN_ID, &pan_angle);
    ServoError_t tilt_error = Servo_ReadPosition(SERVO_TILT_ID, &tilt_angle);

    if (pan_error == SERVO_OK && tilt_error == SERVO_OK) {
        // 记录成功，保存数据
        sm->servo_b.pan = pan_angle;
        sm->servo_b.tilt = tilt_angle;

        // 简化的墙面坐标计算（假设1米距离）
        sm->point_b.x = 1000.0f * tan((pan_angle - 120.0f) * 3.14159f / 180.0f);
        sm->point_b.y = 1000.0f * tan((tilt_angle - 120.0f) * 3.14159f / 180.0f);

        sm->point_b_recorded = 1;

        // 发送蓝牙成功信息
        Bluetooth_SendKeyAction(sm, "Point B Recorded - Ready for Auto Movement");

        // 转换到自动移动状态
        return StateMachine_TransitionTo(sm, STATE_AUTO_MOVING);
    } else {
        // 记录失败，发送蓝牙错误信息
        Bluetooth_SendKeyAction(sm, "Point B Record Failed");
        return SM_ERROR_SERVO_COMMUNICATION;
    }
}

/**
 * 读取当前舵机位置
 * @param sm 状态机结构体指针
 * @param servo_angle 输出舵机角度
 * @param wall_point 输出墙面坐标
 * @return 错误代码
 */
StateMachineError_t StateMachine_ReadCurrentPosition(LaserGimbalState_t* sm, 
                                                   ServoAngle_t* servo_angle, 
                                                   WallPoint_t* wall_point)
{
    if (sm == NULL || servo_angle == NULL || wall_point == NULL) {
        return SM_ERROR_NOT_READY;
    }
    
    // 读取舵机角度
    ServoError_t servo_error = Servo_ReadPosition(SERVO_PAN_ID, &servo_angle->pan);
    if (servo_error != SERVO_OK) return SM_ERROR_SERVO_COMMUNICATION;
    
    servo_error = Servo_ReadPosition(SERVO_TILT_ID, &servo_angle->tilt);
    if (servo_error != SERVO_OK) return SM_ERROR_SERVO_COMMUNICATION;
    
    // 转换为墙面坐标
    GeometryError_t geo_error = Geometry_ServoToWall(*servo_angle, wall_point);
    if (geo_error != GEOMETRY_OK) return SM_ERROR_GEOMETRY_CALCULATION;
    
    return SM_OK;
}

/**
 * 开始自动移动
 * @param sm 状态机结构体指针
 * @return 错误代码
 */
StateMachineError_t StateMachine_StartAutoMovement(LaserGimbalState_t* sm)
{
    if (sm == NULL) return SM_ERROR_NOT_READY;

    if (!sm->point_a_recorded || !sm->point_b_recorded) {
        return SM_ERROR_POINT_NOT_RECORDED;
    }

    // 初始化路径插值
    GeometryError_t error = Path_Initialize(&sm->path, sm->point_a, sm->point_b);
    if (error != GEOMETRY_OK) return SM_ERROR_GEOMETRY_CALCULATION;

    // 设置移动参数
    sm->move_direction = DIRECTION_A_TO_B;
    sm->is_moving = 1;
    sm->move_start_time = Timer_GetTick();
    sm->last_step_time = sm->move_start_time;

    // 发送蓝牙启动信息
    Bluetooth_SendKeyAction(sm, "Auto Movement Started");

    // 启动路径移动定时
    Timer_StartPathMovement();

    // 移动到起始位置
    return StateMachine_MoveServoToPosition(sm->servo_a, 1000);
}

/**
 * 更新自动移动状态
 * @param sm 状态机结构体指针
 * @return 错误代码
 */
StateMachineError_t StateMachine_UpdateAutoMovement(LaserGimbalState_t* sm)
{
    if (sm == NULL || !sm->is_moving) return SM_ERROR_NOT_READY;

    // 检查是否到了下一步的时间 (使用精确定时器)
    if (Timer_IsPathStepReady()) {
        ServoAngle_t next_servo;
        GeometryError_t error = Path_GetNextStep(&sm->path, &next_servo);

        if (error == GEOMETRY_OK) {
            // 移动到下一个位置
            StateMachineError_t sm_error = StateMachine_MoveServoToPosition(next_servo, PATH_STEP_TIME_MS);
            if (sm_error != SM_OK) return sm_error;

            // 重置步进计时
            Timer_ResetPathStep();
            sm->last_step_time = Timer_GetTick();
        } else {
            // 路径完成，切换方向
            return StateMachine_ToggleDirection(sm);
        }
    }

    return SM_OK;
}

/**
 * 停止自动移动
 * @param sm 状态机结构体指针
 * @return 错误代码
 */
StateMachineError_t StateMachine_StopAutoMovement(LaserGimbalState_t* sm)
{
    if (sm == NULL) return SM_ERROR_NOT_READY;

    sm->is_moving = 0;
    sm->path.is_active = 0;

    // 发送蓝牙停止信息
    Bluetooth_SendKeyAction(sm, "Auto Movement Stopped");

    // 停止路径移动定时
    Timer_StopPathMovement();

    // 转换回等待A点状态
    return StateMachine_TransitionTo(sm, STATE_WAIT_POINT_A);
}

/**
 * 切换移动方向
 * @param sm 状态机结构体指针
 * @return 错误代码
 */
StateMachineError_t StateMachine_ToggleDirection(LaserGimbalState_t* sm)
{
    if (sm == NULL) return SM_ERROR_NOT_READY;

    // 切换方向
    sm->move_direction = (sm->move_direction == DIRECTION_A_TO_B) ?
                        DIRECTION_B_TO_A : DIRECTION_A_TO_B;

    // 重新设置路径方向
    GeometryError_t error = Path_SetDirection(&sm->path, sm->move_direction);
    if (error != GEOMETRY_OK) return SM_ERROR_GEOMETRY_CALCULATION;

    sm->move_start_time = Timer_GetTick();
    sm->last_step_time = sm->move_start_time;

    // 重新启动路径移动定时
    Timer_StartPathMovement();

    return SM_OK;
}

/**
 * 舵机扭矩控制封装
 * @param id 舵机ID
 * @param enable 1=加载, 0=卸载
 * @return 错误代码
 */
StateMachineError_t StateMachine_SetServoTorque(uint8_t id, uint8_t enable)
{
    ServoError_t error = Servo_SetTorqueEnable(id, enable);
    return (error == SERVO_OK) ? SM_OK : SM_ERROR_SERVO_COMMUNICATION;
}

/**
 * 舵机位置控制封装
 * @param target_angle 目标角度
 * @param time_ms 移动时间
 * @return 错误代码
 */
StateMachineError_t StateMachine_MoveServoToPosition(ServoAngle_t target_angle, uint16_t time_ms)
{
    Servo_SetPositionWithTime(SERVO_PAN_ID, target_angle.pan, time_ms);
    Servo_SetPositionWithTime(SERVO_TILT_ID, target_angle.tilt, time_ms);
    return SM_OK;
}

/**
 * 获取当前状态
 */
SystemState_t StateMachine_GetCurrentState(LaserGimbalState_t* sm)
{
    return (sm != NULL) ? sm->current_state : STATE_WAIT_POINT_A;
}

/**
 * 检查点位是否已记录
 * @param sm 状态机结构体指针
 * @param point_id 点位ID (1=A点, 2=B点)
 * @return 1=已记录, 0=未记录
 */
uint8_t StateMachine_IsPointRecorded(LaserGimbalState_t* sm, uint8_t point_id)
{
    if (sm == NULL) return 0;
    return (point_id == 1) ? sm->point_a_recorded : sm->point_b_recorded;
}

/**
 * 检查系统是否就绪
 */
uint8_t StateMachine_IsSystemReady(LaserGimbalState_t* sm)
{
    return (sm != NULL) ? sm->system_ready : 0;
}

/**
 * 检查是否正在移动
 */
uint8_t StateMachine_IsMoving(LaserGimbalState_t* sm)
{
    return (sm != NULL) ? sm->is_moving : 0;
}

/**
 * 获取移动进度 (0.0-1.0)
 */
float StateMachine_GetMoveProgress(LaserGimbalState_t* sm)
{
    if (sm == NULL || !sm->is_moving) return 0.0f;
    return (float)sm->path.current_step / (float)sm->path.total_steps;
}

/**
 * 获取系统时间戳 (使用精确定时器)
 */
uint32_t StateMachine_GetTick(void)
{
    return Timer_GetTick();
}

/**
 * 检查超时 (使用精确定时器)
 */
uint8_t StateMachine_IsTimeout(uint32_t start_time, uint32_t timeout_ms)
{
    return Timer_IsTimeout(start_time, timeout_ms);
}

/**
 * 获取状态字符串
 */
const char* StateMachine_GetStateString(SystemState_t state)
{
    switch (state) {
        case STATE_WAIT_POINT_A: return "Wait A";
        case STATE_WAIT_POINT_B: return "Wait B";
        case STATE_AUTO_MOVING:  return "Moving";
        default: return "Unknown";
    }
}

/**
 * 获取方向字符串
 */
const char* StateMachine_GetDirectionString(MoveDirection_t direction)
{
    return (direction == DIRECTION_A_TO_B) ? "A->B" : "B->A";
}
