/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024-07-17
  * @brief   STM32F407ZGT6开发板工程模板
  ******************************************************************************
  * @attention
  *
  * 硬件配置：
  * - MCU: STM32F407ZGT6
  * - 系统时钟: 168MHz
  * - LED: PF9(LED0), PF10(LED1) - 共阳极，低电平点亮
  * - 按键: PE4(K0), PE3(K1) - 低电平有效，内部上拉
  *
  ******************************************************************************
  */

#include "stm32f4xx.h"
#include "Delay.h"

/**
  * @brief  系统初始化函数
  * @param  None
  * @retval None
  */
void System_Init(void)
{
	// 延时初始化
	Delay_Init(168);  // STM32F407时钟168MHz
}

/**
  * @brief  GPIO初始化函数
  * @param  None
  * @retval None
  */
void GPIO_Init_Config(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;

	// 使能GPIO时钟
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOF, ENABLE);  // LED引脚
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOE, ENABLE);  // 按键引脚

	// 配置LED引脚 PF9和PF10为推挽输出
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9 | GPIO_Pin_10;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
	GPIO_Init(GPIOF, &GPIO_InitStructure);

	// 配置按键引脚 PE4和PE3为输入
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4 | GPIO_Pin_3;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;  // 上拉输入
	GPIO_Init(GPIOE, &GPIO_InitStructure);

	// 初始化LED状态：熄灭（共阳极LED，高电平熄灭）
	GPIO_SetBits(GPIOF, GPIO_Pin_9 | GPIO_Pin_10);
}

/**
  * @brief  主函数
  * @param  None
  * @retval None
  */
int main(void)
{
	// 系统初始化
	System_Init();

	// GPIO初始化
	GPIO_Init_Config();

	// 主循环
	while (1)
	{
		// TODO: 在此处添加用户代码

		// 示例：LED闪烁
		GPIO_ResetBits(GPIOF, GPIO_Pin_9);  // 点亮LED0
		Delay_ms(500);
		GPIO_SetBits(GPIOF, GPIO_Pin_9);    // 熄灭LED0
		Delay_ms(500);
	}
}