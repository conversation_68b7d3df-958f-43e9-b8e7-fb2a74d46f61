# HTS-25L总线舵机快速测试指南

## 🎯 **测试目标**

验证STM32F103C8T6能够正确控制HTS-25L总线舵机，确保ID1舵机转到90°，ID2舵机转到120°。

## 🔧 **硬件准备**

### **必需硬件**
- STM32F103C8T6开发板
- 2个HTS-25L总线舵机 (ID设置为1和2)
- OLED显示屏 (I2C接口)
- LED指示灯
- 5V电源 (至少2A电流)
- 连接线

### **连接检查清单**
- [ ] PA9连接到舵机信号线
- [ ] PA10连接到舵机信号线 (可选)
- [ ] 5V电源连接到舵机VCC
- [ ] GND连接到舵机GND和STM32GND
- [ ] OLED连接到PB8(SCL)和PB9(SDA)
- [ ] LED连接到PC13

## 📋 **测试步骤**

### **1. 编译下载**
1. 在Keil中打开项目
2. 编译项目 (应该无错误)
3. 下载程序到STM32F103C8T6

### **2. 硬件连接**
1. 按照连接图连接所有硬件
2. 确保舵机ID已正确设置 (ID1和ID2)
3. 检查电源供电 (5V, 足够电流)

### **3. 上电测试**
1. **上电瞬间**:
   - OLED显示系统信息
   - LED指示灯亮起
   - 系统延时1秒

2. **舵机动作**:
   - ID1舵机转动到90°位置
   - 延时500ms
   - ID2舵机转动到120°位置
   - 延时500ms
   - LED闪烁表示完成

### **4. 结果验证**
- [ ] ID1舵机位置正确 (90°)
- [ ] ID2舵机位置正确 (120°)
- [ ] OLED显示正常
- [ ] LED指示正常
- [ ] 无异常声音或发热

## 🔍 **信号测试**

### **串口信号测试**
使用示波器或逻辑分析仪测试PA9引脚：

**预期信号**:
- 波特率: 115200 bps
- 数据格式: 8N1
- 命令长度: 6字节

**ID1舵机命令** (90°):
```
0xFF 0x01 0x03 0x01 0x77 0x7C
```

**ID2舵机命令** (120°):
```
0xFF 0x02 0x03 0x01 0xF4 0xFB
```

### **命令解析**
```
字节0: 0xFF     - 数据头
字节1: 0x01/02  - 舵机ID
字节2: 0x03     - 设置位置指令
字节3: 0x01     - 位置高字节
字节4: 0x77/F4  - 位置低字节
字节5: 校验和   - 前5字节之和
```

## ✅ **验收标准**

### **功能验收**
- [ ] 系统正常启动
- [ ] OLED显示正确信息
- [ ] ID1舵机转到90°位置
- [ ] ID2舵机转到120°位置
- [ ] LED指示灯正常工作
- [ ] 串口命令发送正确

### **性能验收**
- [ ] 舵机响应时间 <500ms
- [ ] 位置精度 ±2°
- [ ] 系统稳定运行
- [ ] 无异常重启

### **信号验收**
- [ ] 串口波特率正确 (115200)
- [ ] 命令格式正确
- [ ] 校验和计算正确
- [ ] 字节顺序正确 (高字节在前)

## 🔧 **故障排除**

### **舵机不动作**
**可能原因**:
- 电源供电不足
- 舵机ID设置错误
- 串口连接问题
- 命令格式错误

**解决方案**:
1. 检查5V电源电流能力
2. 确认舵机ID设置 (使用舵机调试软件)
3. 检查PA9连线
4. 用示波器检查串口信号

### **OLED无显示**
**可能原因**:
- I2C连线错误
- OLED地址不匹配
- 电源问题

**解决方案**:
1. 检查PB8、PB9连线
2. 确认OLED I2C地址
3. 检查3.3V电源

### **串口信号异常**
**可能原因**:
- 波特率设置错误
- GPIO配置问题
- 时钟配置错误

**解决方案**:
1. 确认波特率115200
2. 检查PA9配置为复用推挽输出
3. 检查系统时钟配置

### **舵机位置不准确**
**可能原因**:
- 角度转换公式错误
- 舵机机械限位
- 位置值计算错误

**解决方案**:
1. 检查转换公式: position = angle * 1000 / 240
2. 确认舵机物理限位
3. 验证位置值计算

## 📊 **测试记录表**

### **硬件连接检查**
| 连接 | 检查结果 | 备注 |
|------|----------|------|
| PA9→舵机信号线 | □ 正常 | |
| 5V→舵机电源 | □ 正常 | |
| GND连接 | □ 正常 | |
| OLED连接 | □ 正常 | |
| LED连接 | □ 正常 | |

### **功能测试记录**
| 测试项目 | 预期结果 | 实际结果 | 通过/失败 |
|----------|----------|----------|-----------|
| 系统启动 | 正常启动 | | |
| OLED显示 | 显示系统信息 | | |
| ID1舵机 | 转到90° | | |
| ID2舵机 | 转到120° | | |
| LED指示 | 闪烁完成 | | |

### **信号测试记录**
| 信号参数 | 预期值 | 测量值 | 通过/失败 |
|----------|--------|--------|-----------|
| 波特率 | 115200 | | |
| 命令长度 | 6字节 | | |
| ID1命令 | FF 01 03 01 77 7C | | |
| ID2命令 | FF 02 03 01 F4 FB | | |

### **测试结论**
- **测试日期**: ___________
- **测试人员**: ___________
- **测试结果**: □ 通过 □ 失败
- **问题记录**: ___________
- **改进建议**: ___________

## 🚀 **扩展测试**

### **多角度测试**
修改main.c中的角度值，测试其他位置：
```c
Servo_SetPosition(1, 0.0f);    // 0度
Servo_SetPosition(1, 60.0f);   // 60度
Servo_SetPosition(1, 180.0f);  // 180度
Servo_SetPosition(1, 240.0f);  // 240度
```

### **连续运动测试**
```c
// 在主循环中添加连续运动
for(int angle = 0; angle <= 240; angle += 30) {
    Servo_SetPosition(1, (float)angle);
    Delay_ms(1000);
}
```

### **双舵机协调测试**
```c
// 同时控制两个舵机
Servo_SetPosition(1, 90.0f);
Servo_SetPosition(2, 90.0f);
```

---
*完成测试后，确认系统工作正常，可以进行后续开发或应用。*
