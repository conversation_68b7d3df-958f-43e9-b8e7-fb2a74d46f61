 	**1**

暂时使用https://ovoai.apicenter.top 后面新域名审核完后更改为https://api.ovoai.xyz



**********

liu1024

 	sk-KdujPK1u6uDT51tbZyLRkFn7rxhuFllaCJ37ioBVT8Fi8Uk8



**********

liu10241

 	sk-OjYH787fitMAXeFCuyy3XhApc1nxU0hs0q36Jb0ciavk6g6L



liu10242

sk-DFrfgCjWE79AVH5R0sDlsYVYo4ElKqR3PWNgNz7Y2VN19K8Q







 	**2**

https://api.qdgf.top

sk-r1ifeboa8V8Jb3irUxRcLNyW7D-Qa0SHlUuinCT\_LbM0xUe7



API 拥有者：**********



{

&nbsp; "LOG": true,

&nbsp; "API\_TIMEOUT\_MS": 120000,

&nbsp; "PROXY\_URL": "",

&nbsp; "Providers": \[

&nbsp;   {

&nbsp;     "name": "qdgf",

&nbsp;     "api\_base\_url": "https://api.qdgf.top/v1/chat/completions",

&nbsp;     "api\_key": "sk-r1ifeboa8V8Jb3irUxRcLNyW7D-Qa0SHlUuinCT\_LbM0xUe7",

&nbsp;     "models": \["gemini-2.5-pro-search"]

&nbsp;   }

&nbsp; ],

&nbsp; "Router": {

&nbsp;   "default": "qdgf,gemini-2.5-pro-search",

&nbsp;   "background": "qdgf,gemini-2.5-pro-search",

&nbsp;   "think": "qdgf,gemini-2.5-pro-search",

&nbsp;   "longContext": "qdgf,gemini-2.5-pro-search",

&nbsp;   "longContextThreshold": 60000,

&nbsp;   "webSearch": "qdgf,gemini-2.5-pro-search"

&nbsp; }

}



