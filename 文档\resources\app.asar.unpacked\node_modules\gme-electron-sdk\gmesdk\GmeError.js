"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var GmeError = /** @class */ (function () {
    function GmeError() {
    }
    GmeError.AV_OK = 0;
    GmeError.AV_ERR_REPETITIVE_OPERATION = 1001;
    GmeError.AV_ERR_INFO_REPETITIVE_OPERATION = "repetitive operation";
    GmeError.AV_ERR_EXCLUSIVE_OPERATION = 1002;
    GmeError.AV_ERR_EXCLUSIVE_OPERATION_INFO = "exclusive operation";
    GmeError.AV_ERR_HAS_IN_THE_STATE = 1003;
    GmeError.AV_ERR_HAS_IN_THE_STATE_INFO = "just in the state";
    GmeError.AV_ERR_INVALID_ARGUMENT = 1004;
    GmeError.AV_ERR_INVALID_ARGUMENT_INFO = "invalid argument";
    GmeError.AV_ERR_TIMEOUT = 1005;
    GmeError.AV_ERR_TIMEOUT_INFO = "waiting timeout, please check your network";
    GmeError.AV_ERR_NOT_IMPLEMENTED = 1006;
    GmeError.AV_ERR_NOT_IMPLEMENTED_INFO = "function not implemented";
    GmeError.AV_ERR_NOT_ON_MAIN_THREAD = 1007;
    GmeError.AV_ERR_INFO_NOT_ON_MAIN_THREAD = "not on the main thread";
    GmeError.AV_ERR_CONTEXT_NOT_START = 1101;
    GmeError.AV_ERR_INFO_CONTEXT_NOT_START = "AVContext did not start";
    GmeError.AV_ERR_ROOM_NOT_EXIST = 1201;
    GmeError.AV_ERR_ROOM_NOT_EXIST_INFO = "room not exist";
    GmeError.AV_ERR_ROOM_NOT_EXITED = 1202;
    GmeError.AV_ERR_ROOM_NOT_EXITED_INFO = "room not exited";
    GmeError.AV_ERR_DEVICE_NOT_EXIST = 1301;
    GmeError.AV_ERR_DEVICE_NOT_EXIST_INFO = "device not exist";
    GmeError.AV_ERR_SERVER_FAIL = 10001;
    GmeError.AV_ERR_SERVER_FAIL_INFO = "server response error";
    GmeError.AV_ERR_SERVER_NO_PERMISSION = 10003;
    GmeError.AV_ERR_SERVER_NO_PERMISSION_INFO = "server refused because of no permission";
    GmeError.AV_ERR_SERVER_REQUEST_ROOM_ADDRESS_FAIL = 10004;
    GmeError.AV_ERR_SERVER_REQUEST_ROOM_ADDRESS_FAIL_INFO = "request room server address failed";
    GmeError.AV_ERR_SERVER_CONNECT_ROOM_FAIL = 10005;
    GmeError.AV_ERR_SERVER_CONNECT_ROOM_FAIL_INFO = "connect room server response error, mostly arguments wrong.";
    GmeError.AV_ERR_SERVER_ROOM_DISSOLVED = 10007;
    GmeError.AV_ERR_SERVER_ROOM_DISSOLVED_INFO = "room dissolved because of overuse";
    GmeError.AV_ERR_IMSDK_FAIL = 6999;
    GmeError.AV_ERR_IMSDK_FAIL_INFO = "imsdk return failed";
    GmeError.AV_ERR_IMSDK_TIMEOUT = 7000;
    GmeError.AV_ERR_IMSDK_TIMEOUT_INFO = "imsdk waiting timeout";
    GmeError.AV_ERR_HTTP_REQ_FAIL = 7001;
    GmeError.AV_ERR_HTTP_REQ_FAIL_INFO = "http request failed";
    GmeError.AV_ERR_3DVOICE_ERR_FILE_DAMAGED = 7002; //3d voice model file is damaged.
    GmeError.AV_ERR_3DVOICE_ERR_NOT_INITED = 7003; //should call InitSpatializer first
    GmeError.AV_ERR_NET_REQUEST_FALLED = 7004;
    GmeError.AV_ERR_CHARGE_OVERDUE = 7005;
    GmeError.AV_ERR_AUTH_FIALD = 7006;
    GmeError.AV_ERR_IN_OTHER_ROOM = 7007;
    GmeError.AV_ERR_DISSOLVED_OVERUSER = 7008;
    GmeError.AV_ERR_NO_PERMISSION = 7009;
    GmeError.AV_ERR_FILE_CANNOT_ACCESS = 7010;
    GmeError.AV_ERR_FILE_DAMAGED = 7011;
    GmeError.AV_ERR_SERVICE_NOT_OPENED = 7012;
    GmeError.AV_ERR_USER_CANCELED = 7013;
    GmeError.AV_ERR_LOAD_LIB_FAILED = 7014; ///< 
    GmeError.AV_ERR_SDK_NOT_FULL_UPDATE = 7015; //升级SDK的时候没有升级所有的文件,导致某些模块不匹配
    GmeError.AV_ERR_ROOMMANAGER_TIMESTAMP_CHECK_FAIL = 7016;
    GmeError.AV_ERR_ASR_CONNECT_CLOSED = 7017;
    GmeError.AV_ERR_MUTESWITCH_DECTECT_ERR = 7018; //iOS静音开关检测错误;
    GmeError.AV_ERR_DB_ERROR = 7019; // 数据库错误
    GmeError.AV_ERR_SYSTEM_INTERNAL_ERROR = 7020; // 系统内部错误
    GmeError.AV_ERR_INVALID_REQ = 7021; // 无效请求
    GmeError.AV_ERR_BUS_ERROR = 7022; // 业务不支持或者没有开通
    /*
    ---------------------------------------------------------------------------------------
    @name 实时语音伴奏相关错误
    ---------------------------------------------------------------------------------------
    */
    GmeError.AV_ERR_ACC_OPENFILE_FAILED = 4001; ///< 打开文件失败
    GmeError.AV_ERR_ACC_FILE_FORAMT_NOTSUPPORT = 4002; ///< 不支持的文件格式
    GmeError.AV_ERR_ACC_DECODER_FAILED = 4003; ///< 解码失败
    GmeError.AV_ERR_ACC_BAD_PARAM = 4004; ///< 参数错误
    GmeError.AV_ERR_ACC_MEMORY_ALLOC_FAILED = 4005; ///< 内存分配失败
    GmeError.AV_ERR_ACC_CREATE_THREAD_FAILED = 4006; ///< 创建线程失败
    GmeError.AV_ERR_ACC_STATE_ILLIGAL = 4007; ///< 状态非法
    GmeError.AV_ERR_START_ACC_FIRST = 4008; ///< 设备采集播放延迟录制开始前,要先开启伴奏
    GmeError.AV_ERR_START_ACC_IS_STARTED = 4009; ///< 设备采集播放延迟预览开始前,要先停止伴奏
    GmeError.AV_ERR_HARDWARE_TEST_RECORD_IS_STARTED = 4010; ///<设备采集播放延迟预览前,要先停止录制
    GmeError.AV_ERR_HARDWARE_TEST_PREVIEW_IS_STARTED = 4011; ///<设备采集播放延迟录制开始前,要先停止预览
    GmeError.AV_ERR_HARDWARE_TEST_PREVIEW_DATA_IS_EMPTY = 4012; ///<设备采集播放延迟录制开始前,要先停止预览
    /*
    ---------------------------------------------------------------------------------------
    @name 实时语音音效相关错误
    ---------------------------------------------------------------------------------------
    */
    GmeError.AV_ERR_EFFECT_OPENFILE_FAILED = 4051; ///< 打开文件失败
    GmeError.AV_ERR_EFFECT_FILE_FORAMT_NOTSUPPORT = 4052; ///< 不支持的文件格式
    GmeError.AV_ERR_EFFECT_DECODER_FAILED = 4053; ///< 解码失败
    GmeError.AV_ERR_EFFECT_BAD_PARAM = 4054; ///< 参数错误
    GmeError.AV_ERR_EFFECT_MEMORY_ALLOC_FAILED = 4055; ///< 内存分配失败
    GmeError.AV_ERR_EFFECT_CREATE_THREAD_FAILED = 4056; ///< 创建线程失败
    GmeError.AV_ERR_EFFECT_STATE_ILLIGAL = 4057; ///< 状态非法
    /*
    ---------------------------------------------------------------------------------------
    @name 实时语音录制相关错误
    ---------------------------------------------------------------------------------------
    */
    GmeError.AV_ERR_RECORD_OPENFILE_FAILED = 5001; ///< 打开文件失败
    GmeError.AV_ERR_RECORD_FILE_FORAMT_NOTSUPPORT = 5002; ///< 不支持的文件格式
    GmeError.AV_ERR_RECORD_DECODER_FAILED = 5003; ///< 解码失败
    GmeError.AV_ERR_RECORD_BAD_PARAM = 5004; ///< 参数错误
    GmeError.AV_ERR_RECORD_MEMORY_ALLOC_FAILED = 5005; ///< 内存分配失败
    GmeError.AV_ERR_RECORD_CREATE_THREAD_FAILED = 5006; ///< 创建线程失败
    GmeError.AV_ERR_RECORD_STATE_ILLIGAL = 5007; ///< 状态非法
    GmeError.AV_ERR_UNKNOWN = 65536;
    GmeError.AV_ERR_INFO_UNKNOWN = "unknown error";
    GmeError.VOICE_RECORDER_PARAM_NULL = 0x1001;
    GmeError.VOICE_RECORDER_INIT_ERROR = 0x1002;
    GmeError.VOICE_RECORDER_RECORDING_ERROR = 0x1003;
    GmeError.VOICE_RECORDER_NO_AUDIO_DATA_WARN = 0x1004;
    GmeError.VOICE_RECORDER_OPENFILE_ERROR = 0x1005;
    GmeError.VOICE_RECORDER_MIC_PERMISSION_ERROR = 0x1006;
    GmeError.VOICE_RECORD_AUDIO_TOO_SHORT = 0x1007;
    GmeError.VOICE_RECORD_NOT_START = 0x1008;
    GmeError.VOICE_UPLOAD_FILE_ACCESSERROR = 0x2001; //读文件错误
    GmeError.VOICE_UPLOAD_SIGN_CHECK_FAIL = 0x2002; //鉴权失败
    GmeError.VOICE_UPLOAD_COS_INTERNAL_FAIL = 0x2003; //上传cos失败
    GmeError.VOICE_UPLOAD_GET_TOKEN_NETWORK_FAIL = 0x2004; // 访问业务服务器的时候网络出错
    GmeError.VOICE_UPLOAD_SYSTEM_INNER_ERROR = 0x2005; //服务器内部错误
    GmeError.VOICE_UPLOAD_RSP_DATA_DECODE_FAIL = 0x2006; //解析json失败
    GmeError.VOICE_UPLOAD_APPINFO_UNSET = 0x2008; //未设置ptt authbuffer 
    //public static readonly VOICE_UPLOAD_NETWORK_FAIL          :number = 0x2003; abandoned 废弃
    //public static readonly VOICE_UPLOAD_GET_TOKEN_RESP_NULL   :number = 0x2005;abandoned  废弃
    //public static readonly VOICE_UPLOAD_GET_TOKEN_RESP_INVALID:number = 0x2006;abandoned  废弃
    //public static readonly VOICE_UPLOAD_TOKEN_CHECK_EXPIRED   :number = 0x2007;abandoned  废弃
    GmeError.VOICE_DOWNLOAD_FILE_ACCESS_ERROR = 0x3001; //12289 写文件失败
    GmeError.VOICE_DOWNLOAD_SIGN_CHECK_FAIL = 0x3002; //12290 token签名校验失败，可以尝试重新获取
    GmeError.VOICE_DOWNLOAD_COS_INTERNAL_FAIL = 0x3003; //12291 COS存储系统内部失败，也可能是访问COS网络超时
    GmeError.VOICE_DOWNLOAD_REMOTEFILE_ACCESS_ERROR = 0x3004; //12292 访问cos失败
    GmeError.VOICE_DOWNLOAD_GET_SIGN_NETWORK_FAIL = 0x3005; //12293 获取下载参数过程中，http网络失败
    GmeError.VOICE_DOWNLOAD_SYSTEM_INNER_ERROR = 0x3006; //服务器内部错误
    GmeError.VOICE_DOWNLOAD_GET_SIGN_RSP_DATA_DECODE_FAIL = 0x3007; //12295 获取下载参数过程中，回包解包失败
    GmeError.VOICE_DOWNLOAD_APPINFO_UNSET = 0x3009; //12297 app info为空
    //public static readonly VOICE_DOWNLOAD_GET_SIGN_RSP_DATA_NULL       :number = 0x3006;  abandoned   废弃
    //public static readonly VOICE_DOWNLOAD_SIGN_CHECK_EXPIRED           :number = 0x3008;  abandoned   废弃 
    GmeError.VOICE_PLAY_INIT_ERROR = 0x5001; //内部错误 如初始化播放器错误,解码失败等等，需要结合日志定位问题
    GmeError.VOICE_PLAY_PLAYING_ERROR = 0x5002; //
    GmeError.VOICE_PLAY_PARAM_NULL = 0x5003; //参数为空
    GmeError.VOICE_PLAY_OPEN_FILE_ERROR = 0x5004; //打开音频文件失败
    GmeError.VOICE_PLAY_NOT_START = 0x5005; //音频未开始播放
    GmeError.VOICE_PLAYER_SILKFILE_NULL = 0x5006; //音频播放文件内容为空
    GmeError.VOICE_PLAYER_SILKFILE_READ_ERROR = 0x5007; //读取音频文件失败
    GmeError.VOICE_PLAYER_INIT_DEVICE_ERROR = 0x5008; //设备初始化失败
    GmeError.VOICE_PLAYER_ERROR = 0x5009; //播放失败，系统内部错误，例如线程创建，内存申请申请释放出错
    GmeError.VOICE_ERR_VOICE_S2T_SYSTEM_INTERNAL_ERROR = 0x8001; // INNER ERROR
    GmeError.VOICE_ERR_VOICE_S2T_NETWORK_FAIL = 0x8002;
    GmeError.VOICE_ERR_VOICE_S2T_RSP_DATA_DECODE_FAIL = 0x8004; // unpack error
    GmeError.VOICE_ERR_VOICE_S2T_APPINFO_UNSET = 0x8006; // 没有设置 appinfo
    GmeError.VOICE_ERR_VOICE_STREAMIN_RECORD_SUC_REC_FAIL = 0x8007; // Failed while Uploading, but recorded success
    GmeError.VOICE_ERR_VOICE_S2T_SIGN_CHECK_FAIL = 0x8008; // AuthBuffer Check Failed
    GmeError.VOICE_ERR_VOICE_STREAMIN_UPLOADANDRECORD_SUC_REC_FAIL = 0x8009; // Failed while converting, but uploaded and recorded
    GmeError.VOICE_ERR_VOICE_S2T_PARAM_NULL = 0x8010; //file ID is NULL
    GmeError.VOICE_ERR_VOICE_S2T_AUTO_SPEECH_REC_ERROR = 0x8011; //AudioFileDecode error,asr error,http param error,etc
    GmeError.VOICE_ERR_VOICE_STREAMIN_RUNING_ERROR = 0x8012;
    GmeError.VOICE_ERR_VOICE_STREAMING_ASR_ERROR = 50012; //asr error
    GmeError.AV_ERR_SHARE_ROOM = 700000;
    GmeError.AV_ERR_SHARE_ROOM_NORMAL = 700001;
    GmeError.AV_ERR_SHARE_ROOM_NO_SPACE = 700002;
    GmeError.AV_ERR_SHARE_ROOM_BUF_NOT_ENOUGH = 700003;
    GmeError.AV_ERR_SHARE_ROOM_INVALID_VALUE = 700004;
    GmeError.AV_ERR_SHARE_ROOM_NULL_POINT = 700005;
    GmeError.AV_ERR_SHARE_ROOM_FULL_ROOM = 700006;
    GmeError.AV_ERR_SHARE_ROOM_FULL_USER = 700007;
    GmeError.AV_ERR_SHARE_ROOM_TIMEOUT = 700008;
    GmeError.AV_ERR_SHARE_ROOM_REPEAT = 700009;
    GmeError.AV_ERR_SHARE_ROOM_NO_ROOM = 700010;
    GmeError.AV_ERR_SHARE_ROOM_NO_USER = 700011;
    GmeError.AV_ERR_SHARE_ROOM_INVALID_KEY = 700012;
    GmeError.AV_ERR_SHARE_ROOM_INVALID_ABILITY = 700013;
    GmeError.AV_ERR_SHARE_ROOM_NO_PRIV = 700014;
    GmeError.AV_ERR_SHARE_ROOM_PPT_FULL = 700015;
    GmeError.AV_ERR_SHARE_ROOM_TYPE_ERR = 700016;
    GmeError.AV_ERR_SHARE_ROOM_PB_SERIALIZE_ERR = 700017;
    GmeError.AV_ERR_SHARE_ROOM_PB_PARSE_ERR = 700018;
    GmeError.AV_ERR_SHARE_ROOM_OUT_MAX_ROOM_USER = 700019;
    GmeError.AV_ERR_SHARE_ROOM_PB_NOT_BODY_ERR = 700020;
    GmeError.AV_ERR_SHARE_ROOM_UIN = 700021;
    GmeError.AV_ERR_SHARE_ROOM_NEED_REDIRECT = 700100;
    GmeError.AV_ERR_SHARE_ROOM_PROTOCOL_CHK_ERROR = 710001;
    GmeError.AV_ERR_SHARE_ROOM_PROTOCOL_ERROR = 710002;
    GmeError.AV_ERR_SHARE_ROOM_APPID_ERROR = 710003;
    GmeError.AV_ERR_SHARE_ROOM_SEARCH_ERROR = 710004;
    GmeError.AV_ERR_SHARE_ROOM_SEARCH_VIA_ERROR = 710005;
    GmeError.AV_ERR_SHARE_ROOM_SEARCH_SEARCH_ERROR = 710006;
    GmeError.AV_ERR_CHORUS_OPENID_DO_NOT_MATCH = 720000;
    GmeError.AV_ERR_CHORUS_WRONG_STATUS = 720001;
    GmeError.AV_ERR_AGE_DETECTED_SUCCESS_USER_CHILD = 730000;
    GmeError.AV_ERR_AGE_DETECTED_SUCCESS_USER_ADULT = 730001;
    GmeError.AV_ERR_AGE_DETECTED_SUCCESS_USER_TEENAGER = 730002;
    GmeError.AV_ERR_AGE_DETECTED_INTERNAL_ERROR = 730003;
    GmeError.AV_ERR_AGE_DETECTED_USER_SILENCE = 730004;
    return GmeError;
}());
exports.default = GmeError;
