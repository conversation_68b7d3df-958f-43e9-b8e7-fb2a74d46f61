const fs = require("fs");
const path = require("path");
const crypto = require("crypto");

// 计算文件的MD5
function calculateFileMd5(filePath) {
  return new Promise((resolve, reject) => {
    const hash = crypto.createHash("md5");
    const stream = fs.createReadStream(filePath);
    stream.on("error", error => {
      process.send({
        code: 1,
        error,
        msg: `${filePath} calculateFileMd5 failed`
      });
      reject(error);
    });
    stream.on("data", data => {
      hash.update(data);
    });
    stream.on("end", () => {
      resolve(hash.digest("hex"));
    });
  });
}

// 对比目录文件md5
async function checkDir({ csgoInstalldir, md5FilePath }) {
  let pwaCsgoLocal = {};
  let noExist = [];
  let hasDiff = [];

  try {
    const _pwaCsgoLocal = fs.readFileSync(md5FilePath);

    pwaCsgoLocal = JSON.parse(_pwaCsgoLocal);
    if (pwaCsgoLocal) {
      const promises = Object.keys(pwaCsgoLocal).map(filePath => {
        return new Promise(async (resolve, reject) => {
          const resPath = path.join(csgoInstalldir, filePath);
          const isExist = fs.existsSync(resPath);

          if (isExist) {
            const fileMd5 = await calculateFileMd5(resPath);
            if (fileMd5 !== pwaCsgoLocal[filePath]) {
              hasDiff.push(filePath);
            }

            resolve();
          } else {
            noExist.push(filePath);
            resolve();
          }
        });
      });

      await Promise.all(promises);

      const info = {
        noExist,
        hasDiff
      };
      console.log(
        "checkDir info",
        noExist.length,
        hasDiff.length,
        promises.length
      );

      process.send({
        code: 1,
        noExist: noExist.length,
        hasDiff: hasDiff.length,
        promises: promises.length
      });

      return info;
    }
  } catch (e) {
    console.log("eeee", e);
    process.send({
      code: 1,
      e,
      msg: `checkDir error ${e}`
    });
  }
}

// 同步拷贝文件
function copyFile(source, target) {
  return new Promise((resolve, reject) => {
    let index = target.lastIndexOf("\\");
    const curDir = target.slice(0, index);
    fs.mkdirSync(curDir, { recursive: true });

    const rd = fs.createReadStream(source);
    rd.on("error", err => {
      console.log("err", err);
      process.send({
        code: 1,
        err,
        msg: `createReadStream error ${source}`
      });
      reject(err);
    });
    const wr = fs.createWriteStream(target);
    wr.on("error", err => {
      process.send({
        code: 1,
        err,
        msg: `createWriteStream error ${target}`
      });
      reject(err);
    });
    wr.on("close", () => resolve());
    rd.pipe(wr);
  });
}

function overwriteFile(source, destination) {
  return new Promise(async (resolve, reject) => {
    try {
      fs.unlinkSync(destination); // 删除目标文件
      await copyFile(source, destination); // 拷贝源文件到目标
      resolve();
    } catch (e) {
      reject();
      process.send({
        code: 1,
        e,
        msg: `overwriteFile error ${source} ${destination}`
      });
    }
  });
}

async function handleCopyAndWrite({ csgoInstalldir, currentDir, md5FilePath }) {
  try {
    const { noExist, hasDiff } = await checkDir({
      csgoInstalldir,
      md5FilePath
    });

    for (let i = 0; i < noExist.length; i++) {
      const file = noExist[i];

      await copyFile(
        path.join(currentDir, file),
        path.join(csgoInstalldir, file)
      );
    }

    for (let i = 0; i < hasDiff.length; i++) {
      const file = hasDiff[i];
      await overwriteFile(
        path.join(currentDir, file),
        path.join(csgoInstalldir, file)
      );
    }
  } catch (e) {
    process.send({
      code: 1,
      e,
      msg: `handleCopyAndWrite error ${e}`
    });
  }
}

process.on("message", async ({ csgoInstalldir, currentDir, md5FilePath }) => {
  try {
    await handleCopyAndWrite({ csgoInstalldir, currentDir, md5FilePath });
    process.send({ code: 0 });
  } catch (e) {
    process.send({ code: 2, msg: e });
  } finally {
    process.disconnect();
  }
});

// 递归遍历目录并生成MD5
// async function generateMd5ForDirectory(dirPath, md5FilePath) {
//   const files = fs.readdirSync(dirPath)
//   const promises = files.map(async file => {
//     const filePath = `${dirPath}\\${file}`
//     const stats = fs.statSync(filePath)
//     if (stats.isDirectory()) {
//       await generateMd5ForDirectory(filePath, md5FilePath)
//     } else {
//       const fileMd5 = await calculateFileMd5(filePath)
//       resObj[filePath] = fileMd5
//     }
//   })
//   await Promise.all(promises)
//   fs.writeFileSync(md5FilePath, JSON.stringify(resObj)) // 清空md5文件
// }

// generateMd5ForDirectory(dirPath, md5FilePath)
//   .then(() => {
//     console.log('MD5 file generated successfully')
//   })
//   .catch((error) => {
//     console.error(error)
//   })

// checkDir({
//   csgoInstalldir:
//     'C:\\Users\\<USER>\\Desktop\\fs-copy-directory\\csgo_resource_1\\pwa_csgo',
//   md5FilePath,
// })
