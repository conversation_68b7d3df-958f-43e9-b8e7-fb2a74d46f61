Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(i.main) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to usart.o(i.uart_init) for uart_init
    main.o(i.main) refers to usart.o(i.uart2_init) for uart2_init
    main.o(i.main) refers to led.o(i.LED_Init) for LED_Init
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to usart.o(i.LaserSystem_Init) for LaserSystem_Init
    main.o(i.main) refers to usart.o(i.Screen_ProcessCommand) for Screen_ProcessCommand
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    system_stm32f4xx.o(i.SystemInit) refers to system_stm32f4xx.o(i.SetSysClock) for SetSysClock
    led.o(i.LED_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    led.o(i.LED_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    led.o(i.LED_Init) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    beep.o(i.BEEP_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    beep.o(i.BEEP_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    beep.o(i.BEEP_Init) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    key.o(i.KEY_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    key.o(i.KEY_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    key.o(i.KEY_Scan) refers to stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    key.o(i.KEY_Scan) refers to delay.o(i.delay_ms) for delay_ms
    key.o(i.KEY_Scan) refers to key.o(.data) for key_up
    delay.o(i.delay_init) refers to misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    delay.o(i.delay_init) refers to delay.o(.data) for fac_us
    delay.o(i.delay_ms) refers to delay.o(i.delay_xms) for delay_xms
    delay.o(i.delay_us) refers to delay.o(.data) for fac_us
    delay.o(i.delay_xms) refers to delay.o(.data) for fac_ms
    usart.o(.rev16_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.revsh_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.LaserSystem_AnglesToCoords) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.LaserSystem_AnglesToCoords) refers to usart.o(.data) for laser_system_state
    usart.o(i.LaserSystem_AnglesToCoords) refers to usart.o(.bss) for calibration_points
    usart.o(i.LaserSystem_CoordsToAngles) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.LaserSystem_CoordsToAngles) refers to usart.o(.data) for laser_system_state
    usart.o(i.LaserSystem_CoordsToAngles) refers to usart.o(.bss) for calibration_points
    usart.o(i.LaserSystem_Init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.LaserSystem_Init) refers to delay.o(i.delay_ms) for delay_ms
    usart.o(i.LaserSystem_Init) refers to usart.o(i.Servo_CalculateChecksum) for Servo_CalculateChecksum
    usart.o(i.LaserSystem_Init) refers to usart.o(i.USART_SendArray) for USART_SendArray
    usart.o(i.LaserSystem_Init) refers to usart.o(i.LaserSystem_UpdateScreenStatus) for LaserSystem_UpdateScreenStatus
    usart.o(i.LaserSystem_Init) refers to usart.o(.data) for laser_system_state
    usart.o(i.LaserSystem_MoveLinearToA) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.LaserSystem_MoveLinearToA) refers to usart.o(i.LaserSystem_CoordsToAngles) for LaserSystem_CoordsToAngles
    usart.o(i.LaserSystem_MoveLinearToA) refers to usart.o(i.Servo_MoveToPosition) for Servo_MoveToPosition
    usart.o(i.LaserSystem_MoveLinearToA) refers to delay.o(i.delay_ms) for delay_ms
    usart.o(i.LaserSystem_MoveLinearToA) refers to usart.o(i.LaserSystem_UpdateScreenStatus) for LaserSystem_UpdateScreenStatus
    usart.o(i.LaserSystem_MoveLinearToA) refers to usart.o(.data) for laser_system_state
    usart.o(i.LaserSystem_MoveLinearToB) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.LaserSystem_MoveLinearToB) refers to usart.o(i.LaserSystem_CoordsToAngles) for LaserSystem_CoordsToAngles
    usart.o(i.LaserSystem_MoveLinearToB) refers to usart.o(i.Servo_MoveToPosition) for Servo_MoveToPosition
    usart.o(i.LaserSystem_MoveLinearToB) refers to delay.o(i.delay_ms) for delay_ms
    usart.o(i.LaserSystem_MoveLinearToB) refers to usart.o(i.LaserSystem_UpdateScreenStatus) for LaserSystem_UpdateScreenStatus
    usart.o(i.LaserSystem_MoveLinearToB) refers to usart.o(.data) for laser_system_state
    usart.o(i.LaserSystem_MoveToA) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.LaserSystem_MoveToA) refers to usart.o(i.LaserSystem_MoveToPoint) for LaserSystem_MoveToPoint
    usart.o(i.LaserSystem_MoveToA) refers to usart.o(.data) for laser_system_state
    usart.o(i.LaserSystem_MoveToB) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.LaserSystem_MoveToB) refers to usart.o(i.LaserSystem_MoveToPoint) for LaserSystem_MoveToPoint
    usart.o(i.LaserSystem_MoveToB) refers to usart.o(.data) for laser_system_state
    usart.o(i.LaserSystem_MoveToPoint) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.LaserSystem_MoveToPoint) refers to usart.o(i.Servo_MoveToPosition) for Servo_MoveToPosition
    usart.o(i.LaserSystem_MoveToPoint) refers to delay.o(i.delay_ms) for delay_ms
    usart.o(i.LaserSystem_MoveToPoint) refers to usart.o(i.LaserSystem_UpdateScreenStatus) for LaserSystem_UpdateScreenStatus
    usart.o(i.LaserSystem_MoveToPoint) refers to usart.o(.data) for laser_system_state
    usart.o(i.LaserSystem_RecordCalibrationPoint) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.LaserSystem_RecordCalibrationPoint) refers to usart.o(i.Servo_ReadPosition) for Servo_ReadPosition
    usart.o(i.LaserSystem_RecordCalibrationPoint) refers to delay.o(i.delay_ms) for delay_ms
    usart.o(i.LaserSystem_RecordCalibrationPoint) refers to usart.o(i.LaserSystem_UpdateScreenStatus) for LaserSystem_UpdateScreenStatus
    usart.o(i.LaserSystem_RecordCalibrationPoint) refers to usart.o(i.Servo_LoadMotor) for Servo_LoadMotor
    usart.o(i.LaserSystem_RecordCalibrationPoint) refers to usart.o(.data) for laser_system_state
    usart.o(i.LaserSystem_RecordCalibrationPoint) refers to usart.o(.bss) for calibration_points
    usart.o(i.LaserSystem_RecordPointA) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.LaserSystem_RecordPointA) refers to usart.o(i.Servo_ReadPosition) for Servo_ReadPosition
    usart.o(i.LaserSystem_RecordPointA) refers to delay.o(i.delay_ms) for delay_ms
    usart.o(i.LaserSystem_RecordPointA) refers to usart.o(i.LaserSystem_AnglesToCoords) for LaserSystem_AnglesToCoords
    usart.o(i.LaserSystem_RecordPointA) refers to usart.o(i.LaserSystem_UpdateScreenStatus) for LaserSystem_UpdateScreenStatus
    usart.o(i.LaserSystem_RecordPointA) refers to usart.o(.data) for laser_system_state
    usart.o(i.LaserSystem_RecordPointB) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.LaserSystem_RecordPointB) refers to usart.o(i.Servo_ReadPosition) for Servo_ReadPosition
    usart.o(i.LaserSystem_RecordPointB) refers to delay.o(i.delay_ms) for delay_ms
    usart.o(i.LaserSystem_RecordPointB) refers to usart.o(i.LaserSystem_AnglesToCoords) for LaserSystem_AnglesToCoords
    usart.o(i.LaserSystem_RecordPointB) refers to usart.o(i.LaserSystem_UpdateScreenStatus) for LaserSystem_UpdateScreenStatus
    usart.o(i.LaserSystem_RecordPointB) refers to usart.o(.data) for laser_system_state
    usart.o(i.LaserSystem_StartCalibration) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.LaserSystem_StartCalibration) refers to usart.o(i.Servo_CalculateChecksum) for Servo_CalculateChecksum
    usart.o(i.LaserSystem_StartCalibration) refers to usart.o(i.USART_SendArray) for USART_SendArray
    usart.o(i.LaserSystem_StartCalibration) refers to delay.o(i.delay_ms) for delay_ms
    usart.o(i.LaserSystem_StartCalibration) refers to usart.o(i.LaserSystem_UpdateScreenStatus) for LaserSystem_UpdateScreenStatus
    usart.o(i.LaserSystem_StartCalibration) refers to usart.o(.data) for laser_system_state
    usart.o(i.LaserSystem_UpdateScreenStatus) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.LaserSystem_UpdateScreenStatus) refers to usart.o(i.USART2_SendArray) for USART2_SendArray
    usart.o(i.LaserSystem_UpdateScreenStatus) refers to delay.o(i.delay_ms) for delay_ms
    usart.o(i.LaserSystem_UpdateScreenStatus) refers to usart.o(.data) for laser_system_state
    usart.o(i.Screen_ProcessCommand) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.Screen_ProcessCommand) refers to usart.o(i.LaserSystem_StartCalibration) for LaserSystem_StartCalibration
    usart.o(i.Screen_ProcessCommand) refers to usart.o(i.LaserSystem_RecordCalibrationPoint) for LaserSystem_RecordCalibrationPoint
    usart.o(i.Screen_ProcessCommand) refers to usart.o(i.Servo_CalculateChecksum) for Servo_CalculateChecksum
    usart.o(i.Screen_ProcessCommand) refers to usart.o(i.USART_SendArray) for USART_SendArray
    usart.o(i.Screen_ProcessCommand) refers to delay.o(i.delay_ms) for delay_ms
    usart.o(i.Screen_ProcessCommand) refers to usart.o(i.Servo_LoadMotor) for Servo_LoadMotor
    usart.o(i.Screen_ProcessCommand) refers to usart.o(i.LaserSystem_RecordPointA) for LaserSystem_RecordPointA
    usart.o(i.Screen_ProcessCommand) refers to usart.o(i.LaserSystem_RecordPointB) for LaserSystem_RecordPointB
    usart.o(i.Screen_ProcessCommand) refers to usart.o(i.LaserSystem_MoveToA) for LaserSystem_MoveToA
    usart.o(i.Screen_ProcessCommand) refers to usart.o(i.LaserSystem_MoveToB) for LaserSystem_MoveToB
    usart.o(i.Screen_ProcessCommand) refers to usart.o(i.LaserSystem_MoveLinearToB) for LaserSystem_MoveLinearToB
    usart.o(i.Screen_ProcessCommand) refers to usart.o(i.LaserSystem_MoveLinearToA) for LaserSystem_MoveLinearToA
    usart.o(i.Screen_ProcessCommand) refers to usart.o(.data) for SCREEN_RX_STA
    usart.o(i.Screen_ProcessCommand) refers to usart.o(.bss) for SCREEN_RX_BUF
    usart.o(i.Servo_CalculateChecksum) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.Servo_LoadMotor) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.Servo_LoadMotor) refers to usart.o(i.Servo_CalculateChecksum) for Servo_CalculateChecksum
    usart.o(i.Servo_LoadMotor) refers to usart.o(i.USART_SendArray) for USART_SendArray
    usart.o(i.Servo_MoveToPosition) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.Servo_MoveToPosition) refers to usart.o(i.Servo_CalculateChecksum) for Servo_CalculateChecksum
    usart.o(i.Servo_MoveToPosition) refers to usart.o(i.USART_SendArray) for USART_SendArray
    usart.o(i.Servo_ProcessPacket) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.Servo_ProcessPacket) refers to usart.o(i.Servo_CalculateChecksum) for Servo_CalculateChecksum
    usart.o(i.Servo_ProcessPacket) refers to usart.o(.data) for SERVO_RX_CNT
    usart.o(i.Servo_ProcessPacket) refers to usart.o(.bss) for SERVO_RX_BUF
    usart.o(i.Servo_ReadPosition) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.Servo_ReadPosition) refers to usart.o(i.Servo_CalculateChecksum) for Servo_CalculateChecksum
    usart.o(i.Servo_ReadPosition) refers to usart.o(i.USART_SendArray) for USART_SendArray
    usart.o(i.Servo_SetMotorMode) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.Servo_SetMotorMode) refers to usart.o(i.Servo_CalculateChecksum) for Servo_CalculateChecksum
    usart.o(i.Servo_SetMotorMode) refers to usart.o(i.USART_SendArray) for USART_SendArray
    usart.o(i.USART1_IRQHandler) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART1_IRQHandler) refers to usart.o(i.Servo_ProcessPacket) for Servo_ProcessPacket
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for SERVO_RX_STA
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for SERVO_RX_BUF
    usart.o(i.USART2_IRQHandler) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART2_IRQHandler) refers to stm32f4xx_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART2_IRQHandler) refers to stm32f4xx_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART2_IRQHandler) refers to usart.o(.data) for SCREEN_RX_CNT
    usart.o(i.USART2_IRQHandler) refers to usart.o(.bss) for SCREEN_RX_BUF
    usart.o(i.USART2_SendArray) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART2_SendArray) refers to stm32f4xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.USART2_SendArray) refers to stm32f4xx_usart.o(i.USART_SendData) for USART_SendData
    usart.o(i.USART_SendArray) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART_SendArray) refers to stm32f4xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.USART_SendArray) refers to stm32f4xx_usart.o(i.USART_SendData) for USART_SendData
    usart.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart2_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart2_init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    usart.o(i.uart2_init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usart.o(i.uart2_init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart.o(i.uart2_init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.uart2_init) refers to stm32f4xx_usart.o(i.USART_Init) for USART_Init
    usart.o(i.uart2_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(i.uart2_init) refers to stm32f4xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.uart2_init) refers to stm32f4xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.uart_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    usart.o(i.uart_init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.uart_init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart.o(i.uart_init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_Init) for USART_Init
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.uart_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    startup_stm32f40_41xxx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(HEAP) for Heap_Mem
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(STACK) for Stack_Mem
    stm32f4xx_gpio.o(i.GPIO_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    stm32f4xx_rcc.o(i.RCC_GetClocksFreq) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f4xx_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f4xx_syscfg.o(i.SYSCFG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_usart.o(i.USART_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f40_41xxx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (192 bytes).
    Removing system_stm32f4xx.o(.data), (20 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing beep.o(.rev16_text), (4 bytes).
    Removing beep.o(.revsh_text), (4 bytes).
    Removing beep.o(i.BEEP_Init), (64 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(i.KEY_Init), (68 bytes).
    Removing key.o(i.KEY_Scan), (128 bytes).
    Removing key.o(.data), (1 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(i.delay_us), (76 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.emb_text), (16 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(i.Servo_SetMotorMode), (80 bytes).
    Removing usart.o(i.fputc), (28 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_DeInit), (312 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_PinLockConfig), (34 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ResetBits), (4 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_StructInit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ToggleBits), (8 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_DeInit), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetFlagStatus), (64 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSEConfig), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_I2SCLKConfig), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEConfig), (44 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEModeConfig), (36 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LTDCCLKDivConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO1Config), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO2Config), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLConfig), (36 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SConfig), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAIConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKConfig), (60 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockACLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockBCLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLI2SClkDivConfig), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLSAIClkDivConfig), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_TIMCLKPresConfig), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_CompensationCellCmd), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_DeInit), (22 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig), (64 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_GetCompensationCellStatus), (24 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemoryRemapConfig), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemorySwappingBank), (12 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearITPendingBit), (30 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockInit), (32 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f4xx_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_DeInit), (240 bytes).
    Removing stm32f4xx_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f4xx_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_WakeUpConfig), (18 bytes).

129 unused section(s) (total 3479 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ..\CORE\startup_stm32f40_41xxx.s         0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    ..\FWLIB\src\misc.c                      0x00000000   Number         0  misc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_gpio.c            0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rcc.c             0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_syscfg.c          0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_usart.c           0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\HARDWARE\BEEP\beep.c                  0x00000000   Number         0  beep.o ABSOLUTE
    ..\HARDWARE\KEY\key.c                    0x00000000   Number         0  key.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\FWLIB\\src\\misc.c                   0x00000000   Number         0  misc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_gpio.c         0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rcc.c          0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_syscfg.c       0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_usart.c        0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\\HARDWARE\\BEEP\\beep.c               0x00000000   Number         0  beep.o ABSOLUTE
    ..\\HARDWARE\\KEY\\key.c                 0x00000000   Number         0  key.o ABSOLUTE
    ..\\HARDWARE\\LED\\led.c                 0x00000000   Number         0  led.o ABSOLUTE
    ..\\SYSTEM\\delay\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    ..\\SYSTEM\\usart\\usart.c               0x00000000   Number         0  usart.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x080001fc   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x080001fe   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000202   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000204   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08000206   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x08000208   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000208   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000208   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800020e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800020e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000212   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000212   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800021a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800021c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800021c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000220   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000228   Section       64  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x08000228   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x08000268   Section        2  use_no_semi_2.o(.text)
    .text                                    0x0800026a   Section        0  heapauxi.o(.text)
    .text                                    0x08000270   Section        2  use_no_semi.o(.text)
    .text                                    0x08000272   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080002bc   Section        0  exit.o(.text)
    .text                                    0x080002d0   Section        8  libspace.o(.text)
    i.BusFault_Handler                       0x080002d8   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x080002dc   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.GPIO_Init                              0x080002de   Section        0  stm32f4xx_gpio.o(i.GPIO_Init)
    i.GPIO_PinAFConfig                       0x0800036e   Section        0  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    i.GPIO_SetBits                           0x080003b4   Section        0  stm32f4xx_gpio.o(i.GPIO_SetBits)
    i.HardFault_Handler                      0x080003b8   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.LED_Init                               0x080003bc   Section        0  led.o(i.LED_Init)
    i.LaserSystem_AnglesToCoords             0x080003fc   Section        0  usart.o(i.LaserSystem_AnglesToCoords)
    i.LaserSystem_CoordsToAngles             0x08000614   Section        0  usart.o(i.LaserSystem_CoordsToAngles)
    i.LaserSystem_Init                       0x080007a8   Section        0  usart.o(i.LaserSystem_Init)
    i.LaserSystem_MoveLinearToA              0x08000848   Section        0  usart.o(i.LaserSystem_MoveLinearToA)
    i.LaserSystem_MoveLinearToB              0x080009d0   Section        0  usart.o(i.LaserSystem_MoveLinearToB)
    i.LaserSystem_MoveToA                    0x08000b58   Section        0  usart.o(i.LaserSystem_MoveToA)
    i.LaserSystem_MoveToB                    0x08000b84   Section        0  usart.o(i.LaserSystem_MoveToB)
    i.LaserSystem_MoveToPoint                0x08000bb0   Section        0  usart.o(i.LaserSystem_MoveToPoint)
    i.LaserSystem_RecordCalibrationPoint     0x08000c6c   Section        0  usart.o(i.LaserSystem_RecordCalibrationPoint)
    i.LaserSystem_RecordPointA               0x08000d08   Section        0  usart.o(i.LaserSystem_RecordPointA)
    i.LaserSystem_RecordPointB               0x08000d84   Section        0  usart.o(i.LaserSystem_RecordPointB)
    i.LaserSystem_StartCalibration           0x08000e00   Section        0  usart.o(i.LaserSystem_StartCalibration)
    i.LaserSystem_UpdateScreenStatus         0x08000e90   Section        0  usart.o(i.LaserSystem_UpdateScreenStatus)
    i.MemManage_Handler                      0x080011f4   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080011f8   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x080011fc   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x08001274   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.PendSV_Handler                         0x08001288   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.RCC_AHB1PeriphClockCmd                 0x0800128c   Section        0  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    i.RCC_APB1PeriphClockCmd                 0x080012ac   Section        0  stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x080012cc   Section        0  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x080012ec   Section        0  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x080013d4   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.Screen_ProcessCommand                  0x080013d8   Section        0  usart.o(i.Screen_ProcessCommand)
    i.Servo_CalculateChecksum                0x080014f8   Section        0  usart.o(i.Servo_CalculateChecksum)
    i.Servo_LoadMotor                        0x08001518   Section        0  usart.o(i.Servo_LoadMotor)
    i.Servo_MoveToPosition                   0x08001554   Section        0  usart.o(i.Servo_MoveToPosition)
    i.Servo_ProcessPacket                    0x080015a8   Section        0  usart.o(i.Servo_ProcessPacket)
    i.Servo_ReadPosition                     0x08001624   Section        0  usart.o(i.Servo_ReadPosition)
    i.SetSysClock                            0x0800165c   Section        0  system_stm32f4xx.o(i.SetSysClock)
    SetSysClock                              0x0800165d   Thumb Code   220  system_stm32f4xx.o(i.SetSysClock)
    i.SysTick_CLKSourceConfig                0x08001748   Section        0  misc.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Handler                        0x08001770   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08001774   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.USART1_IRQHandler                      0x080017dc   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x080018b4   Section        0  usart.o(i.USART2_IRQHandler)
    i.USART2_SendArray                       0x08001934   Section        0  usart.o(i.USART2_SendArray)
    i.USART_Cmd                              0x08001970   Section        0  stm32f4xx_usart.o(i.USART_Cmd)
    i.USART_GetFlagStatus                    0x08001988   Section        0  stm32f4xx_usart.o(i.USART_GetFlagStatus)
    i.USART_GetITStatus                      0x080019a2   Section        0  stm32f4xx_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x080019f6   Section        0  stm32f4xx_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08001a40   Section        0  stm32f4xx_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08001b14   Section        0  stm32f4xx_usart.o(i.USART_ReceiveData)
    i.USART_SendArray                        0x08001b20   Section        0  usart.o(i.USART_SendArray)
    i.USART_SendData                         0x08001b5c   Section        0  stm32f4xx_usart.o(i.USART_SendData)
    i.UsageFault_Handler                     0x08001b64   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i._sys_exit                              0x08001b68   Section        0  usart.o(i._sys_exit)
    i.delay_init                             0x08001b6c   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x08001ba8   Section        0  delay.o(i.delay_ms)
    i.delay_xms                              0x08001be0   Section        0  delay.o(i.delay_xms)
    i.main                                   0x08001c2c   Section        0  main.o(i.main)
    i.uart2_init                             0x08001c7c   Section        0  usart.o(i.uart2_init)
    i.uart_init                              0x08001d28   Section        0  usart.o(i.uart_init)
    x$fpl$fpinit                             0x08001dd4   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08001dd4   Number         0  fpinit.o(x$fpl$fpinit)
    .data                                    0x20000000   Section        4  delay.o(.data)
    fac_us                                   0x20000000   Data           1  delay.o(.data)
    fac_ms                                   0x20000002   Data           2  delay.o(.data)
    .data                                    0x20000004   Section       76  usart.o(.data)
    .data                                    0x20000050   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x20000050   Data          16  stm32f4xx_rcc.o(.data)
    .bss                                     0x20000060   Section      286  usart.o(.bss)
    .bss                                     0x20000180   Section       96  libspace.o(.bss)
    HEAP                                     0x200001e0   Section      512  startup_stm32f40_41xxx.o(HEAP)
    Heap_Mem                                 0x200001e0   Data         512  startup_stm32f40_41xxx.o(HEAP)
    STACK                                    0x200003e0   Section     1024  startup_stm32f40_41xxx.o(STACK)
    Stack_Mem                                0x200003e0   Data        1024  startup_stm32f40_41xxx.o(STACK)
    __initial_sp                             0x200007e0   Data           0  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x080001fd   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080001ff   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_alloca_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000205   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x08000209   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000209   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000209   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800020f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800020f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000213   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000213   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800021b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800021d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800021d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000221   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000229   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream0_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream2_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI0_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM2_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM3_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM4_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM5_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM6_DAC_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART3_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __user_initial_stackheap                 0x08000245   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __use_no_semihosting                     0x08000269   Thumb Code     2  use_no_semi_2.o(.text)
    __use_two_region_memory                  0x0800026b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800026d   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800026f   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x08000271   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000271   Thumb Code     2  use_no_semi.o(.text)
    __user_setup_stackheap                   0x08000273   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x080002bd   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x080002d1   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080002d1   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080002d1   Thumb Code     0  libspace.o(.text)
    BusFault_Handler                         0x080002d9   Thumb Code     4  stm32f4xx_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x080002dd   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    GPIO_Init                                0x080002df   Thumb Code   144  stm32f4xx_gpio.o(i.GPIO_Init)
    GPIO_PinAFConfig                         0x0800036f   Thumb Code    70  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    GPIO_SetBits                             0x080003b5   Thumb Code     4  stm32f4xx_gpio.o(i.GPIO_SetBits)
    HardFault_Handler                        0x080003b9   Thumb Code     4  stm32f4xx_it.o(i.HardFault_Handler)
    LED_Init                                 0x080003bd   Thumb Code    60  led.o(i.LED_Init)
    LaserSystem_AnglesToCoords               0x080003fd   Thumb Code   518  usart.o(i.LaserSystem_AnglesToCoords)
    LaserSystem_CoordsToAngles               0x08000615   Thumb Code   386  usart.o(i.LaserSystem_CoordsToAngles)
    LaserSystem_Init                         0x080007a9   Thumb Code   138  usart.o(i.LaserSystem_Init)
    LaserSystem_MoveLinearToA                0x08000849   Thumb Code   358  usart.o(i.LaserSystem_MoveLinearToA)
    LaserSystem_MoveLinearToB                0x080009d1   Thumb Code   358  usart.o(i.LaserSystem_MoveLinearToB)
    LaserSystem_MoveToA                      0x08000b59   Thumb Code    32  usart.o(i.LaserSystem_MoveToA)
    LaserSystem_MoveToB                      0x08000b85   Thumb Code    32  usart.o(i.LaserSystem_MoveToB)
    LaserSystem_MoveToPoint                  0x08000bb1   Thumb Code   176  usart.o(i.LaserSystem_MoveToPoint)
    LaserSystem_RecordCalibrationPoint       0x08000c6d   Thumb Code   134  usart.o(i.LaserSystem_RecordCalibrationPoint)
    LaserSystem_RecordPointA                 0x08000d09   Thumb Code    98  usart.o(i.LaserSystem_RecordPointA)
    LaserSystem_RecordPointB                 0x08000d85   Thumb Code    98  usart.o(i.LaserSystem_RecordPointB)
    LaserSystem_StartCalibration             0x08000e01   Thumb Code   132  usart.o(i.LaserSystem_StartCalibration)
    LaserSystem_UpdateScreenStatus           0x08000e91   Thumb Code   854  usart.o(i.LaserSystem_UpdateScreenStatus)
    MemManage_Handler                        0x080011f5   Thumb Code     4  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080011f9   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    NVIC_Init                                0x080011fd   Thumb Code   106  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x08001275   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    PendSV_Handler                           0x08001289   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    RCC_AHB1PeriphClockCmd                   0x0800128d   Thumb Code    26  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    RCC_APB1PeriphClockCmd                   0x080012ad   Thumb Code    26  stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x080012cd   Thumb Code    26  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x080012ed   Thumb Code   214  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x080013d5   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    Screen_ProcessCommand                    0x080013d9   Thumb Code   268  usart.o(i.Screen_ProcessCommand)
    Servo_CalculateChecksum                  0x080014f9   Thumb Code    32  usart.o(i.Servo_CalculateChecksum)
    Servo_LoadMotor                          0x08001519   Thumb Code    60  usart.o(i.Servo_LoadMotor)
    Servo_MoveToPosition                     0x08001555   Thumb Code    82  usart.o(i.Servo_MoveToPosition)
    Servo_ProcessPacket                      0x080015a9   Thumb Code   108  usart.o(i.Servo_ProcessPacket)
    Servo_ReadPosition                       0x08001625   Thumb Code    54  usart.o(i.Servo_ReadPosition)
    SysTick_CLKSourceConfig                  0x08001749   Thumb Code    40  misc.o(i.SysTick_CLKSourceConfig)
    SysTick_Handler                          0x08001771   Thumb Code     2  stm32f4xx_it.o(i.SysTick_Handler)
    SystemInit                               0x08001775   Thumb Code    88  system_stm32f4xx.o(i.SystemInit)
    USART1_IRQHandler                        0x080017dd   Thumb Code   194  usart.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x080018b5   Thumb Code   110  usart.o(i.USART2_IRQHandler)
    USART2_SendArray                         0x08001935   Thumb Code    56  usart.o(i.USART2_SendArray)
    USART_Cmd                                0x08001971   Thumb Code    24  stm32f4xx_usart.o(i.USART_Cmd)
    USART_GetFlagStatus                      0x08001989   Thumb Code    26  stm32f4xx_usart.o(i.USART_GetFlagStatus)
    USART_GetITStatus                        0x080019a3   Thumb Code    84  stm32f4xx_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x080019f7   Thumb Code    74  stm32f4xx_usart.o(i.USART_ITConfig)
    USART_Init                               0x08001a41   Thumb Code   204  stm32f4xx_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08001b15   Thumb Code    10  stm32f4xx_usart.o(i.USART_ReceiveData)
    USART_SendArray                          0x08001b21   Thumb Code    56  usart.o(i.USART_SendArray)
    USART_SendData                           0x08001b5d   Thumb Code     8  stm32f4xx_usart.o(i.USART_SendData)
    UsageFault_Handler                       0x08001b65   Thumb Code     4  stm32f4xx_it.o(i.UsageFault_Handler)
    _sys_exit                                0x08001b69   Thumb Code     4  usart.o(i._sys_exit)
    delay_init                               0x08001b6d   Thumb Code    52  delay.o(i.delay_init)
    delay_ms                                 0x08001ba9   Thumb Code    56  delay.o(i.delay_ms)
    delay_xms                                0x08001be1   Thumb Code    72  delay.o(i.delay_xms)
    main                                     0x08001c2d   Thumb Code    76  main.o(i.main)
    uart2_init                               0x08001c7d   Thumb Code   162  usart.o(i.uart2_init)
    uart_init                                0x08001d29   Thumb Code   164  usart.o(i.uart_init)
    _fp_init                                 0x08001dd5   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08001ddd   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08001ddd   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    Region$$Table$$Base                      0x08001de0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08001e00   Number         0  anon$$obj.o(Region$$Table)
    __stdout                                 0x20000004   Data           4  usart.o(.data)
    USART_RX_STA                             0x20000008   Data           2  usart.o(.data)
    SERVO_RX_STA                             0x2000000a   Data           1  usart.o(.data)
    SERVO_RX_CNT                             0x2000000b   Data           1  usart.o(.data)
    SERVO_PKT_LEN                            0x2000000c   Data           1  usart.o(.data)
    SCREEN_RX_STA                            0x2000000d   Data           1  usart.o(.data)
    SCREEN_RX_CNT                            0x2000000e   Data           1  usart.o(.data)
    SERVO1_POSITION                          0x20000010   Data           2  usart.o(.data)
    SERVO2_POSITION                          0x20000012   Data           2  usart.o(.data)
    laser_system_state                       0x20000014   Data           1  usart.o(.data)
    current_position                         0x20000015   Data           1  usart.o(.data)
    calibration_step                         0x20000016   Data           1  usart.o(.data)
    calibration_coords                       0x20000018   Data          32  usart.o(.data)
    point_A_angles                           0x20000038   Data           4  usart.o(.data)
    point_B_angles                           0x2000003c   Data           4  usart.o(.data)
    point_A_coord                            0x20000040   Data           8  usart.o(.data)
    point_B_coord                            0x20000048   Data           8  usart.o(.data)
    USART_RX_BUF                             0x20000060   Data         200  usart.o(.bss)
    SERVO_RX_BUF                             0x20000128   Data          20  usart.o(.bss)
    SCREEN_RX_BUF                            0x2000013c   Data          50  usart.o(.bss)
    calibration_points                       0x2000016e   Data          16  usart.o(.bss)
    __libspace_start                         0x20000180   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200001e0   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00001e60, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00001e00, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO          605    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000008   Code   RO         1336  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         1493    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         1495    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         1497    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000002   Code   RO         1365    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001fe   0x080001fe   0x00000004   Code   RO         1371    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1374    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1377    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1379    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1381    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1384    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1386    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1388    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1390    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1392    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1394    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1396    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1398    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1400    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1402    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1404    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1408    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1410    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1412    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1414    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000002   Code   RO         1415    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000204   0x08000204   0x00000002   Code   RO         1433    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000206   0x08000206   0x00000000   Code   RO         1443    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000000   Code   RO         1445    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000000   Code   RO         1447    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000000   Code   RO         1450    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000000   Code   RO         1453    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000000   Code   RO         1455    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000000   Code   RO         1458    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000002   Code   RO         1459    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x08000208   0x08000208   0x00000000   Code   RO         1340    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000208   0x08000208   0x00000000   Code   RO         1342    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000208   0x08000208   0x00000006   Code   RO         1354    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         1344    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800020e   0x0800020e   0x00000004   Code   RO         1345    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         1347    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000212   0x08000212   0x00000008   Code   RO         1348    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800021a   0x0800021a   0x00000002   Code   RO         1369    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800021c   0x0800021c   0x00000000   Code   RO         1417    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800021c   0x0800021c   0x00000004   Code   RO         1418    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000220   0x08000220   0x00000006   Code   RO         1419    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000226   0x08000226   0x00000002   PAD
    0x08000228   0x08000228   0x00000040   Code   RO          606    .text               startup_stm32f40_41xxx.o
    0x08000268   0x08000268   0x00000002   Code   RO         1332    .text               c_w.l(use_no_semi_2.o)
    0x0800026a   0x0800026a   0x00000006   Code   RO         1334    .text               c_w.l(heapauxi.o)
    0x08000270   0x08000270   0x00000002   Code   RO         1338    .text               c_w.l(use_no_semi.o)
    0x08000272   0x08000272   0x0000004a   Code   RO         1356    .text               c_w.l(sys_stackheap_outer.o)
    0x080002bc   0x080002bc   0x00000012   Code   RO         1358    .text               c_w.l(exit.o)
    0x080002ce   0x080002ce   0x00000002   PAD
    0x080002d0   0x080002d0   0x00000008   Code   RO         1366    .text               c_w.l(libspace.o)
    0x080002d8   0x080002d8   0x00000004   Code   RO          147    i.BusFault_Handler  stm32f4xx_it.o
    0x080002dc   0x080002dc   0x00000002   Code   RO          148    i.DebugMon_Handler  stm32f4xx_it.o
    0x080002de   0x080002de   0x00000090   Code   RO          657    i.GPIO_Init         stm32f4xx_gpio.o
    0x0800036e   0x0800036e   0x00000046   Code   RO          658    i.GPIO_PinAFConfig  stm32f4xx_gpio.o
    0x080003b4   0x080003b4   0x00000004   Code   RO          665    i.GPIO_SetBits      stm32f4xx_gpio.o
    0x080003b8   0x080003b8   0x00000004   Code   RO          149    i.HardFault_Handler  stm32f4xx_it.o
    0x080003bc   0x080003bc   0x00000040   Code   RO          281    i.LED_Init          led.o
    0x080003fc   0x080003fc   0x00000218   Code   RO          412    i.LaserSystem_AnglesToCoords  usart.o
    0x08000614   0x08000614   0x00000194   Code   RO          413    i.LaserSystem_CoordsToAngles  usart.o
    0x080007a8   0x080007a8   0x000000a0   Code   RO          414    i.LaserSystem_Init  usart.o
    0x08000848   0x08000848   0x00000188   Code   RO          415    i.LaserSystem_MoveLinearToA  usart.o
    0x080009d0   0x080009d0   0x00000188   Code   RO          416    i.LaserSystem_MoveLinearToB  usart.o
    0x08000b58   0x08000b58   0x0000002c   Code   RO          417    i.LaserSystem_MoveToA  usart.o
    0x08000b84   0x08000b84   0x0000002c   Code   RO          418    i.LaserSystem_MoveToB  usart.o
    0x08000bb0   0x08000bb0   0x000000bc   Code   RO          419    i.LaserSystem_MoveToPoint  usart.o
    0x08000c6c   0x08000c6c   0x0000009c   Code   RO          420    i.LaserSystem_RecordCalibrationPoint  usart.o
    0x08000d08   0x08000d08   0x0000007c   Code   RO          421    i.LaserSystem_RecordPointA  usart.o
    0x08000d84   0x08000d84   0x0000007c   Code   RO          422    i.LaserSystem_RecordPointB  usart.o
    0x08000e00   0x08000e00   0x00000090   Code   RO          423    i.LaserSystem_StartCalibration  usart.o
    0x08000e90   0x08000e90   0x00000364   Code   RO          424    i.LaserSystem_UpdateScreenStatus  usart.o
    0x080011f4   0x080011f4   0x00000004   Code   RO          150    i.MemManage_Handler  stm32f4xx_it.o
    0x080011f8   0x080011f8   0x00000002   Code   RO          151    i.NMI_Handler       stm32f4xx_it.o
    0x080011fa   0x080011fa   0x00000002   PAD
    0x080011fc   0x080011fc   0x00000078   Code   RO          612    i.NVIC_Init         misc.o
    0x08001274   0x08001274   0x00000014   Code   RO          613    i.NVIC_PriorityGroupConfig  misc.o
    0x08001288   0x08001288   0x00000002   Code   RO          152    i.PendSV_Handler    stm32f4xx_it.o
    0x0800128a   0x0800128a   0x00000002   PAD
    0x0800128c   0x0800128c   0x00000020   Code   RO          754    i.RCC_AHB1PeriphClockCmd  stm32f4xx_rcc.o
    0x080012ac   0x080012ac   0x00000020   Code   RO          763    i.RCC_APB1PeriphClockCmd  stm32f4xx_rcc.o
    0x080012cc   0x080012cc   0x00000020   Code   RO          766    i.RCC_APB2PeriphClockCmd  stm32f4xx_rcc.o
    0x080012ec   0x080012ec   0x000000e8   Code   RO          775    i.RCC_GetClocksFreq  stm32f4xx_rcc.o
    0x080013d4   0x080013d4   0x00000002   Code   RO          153    i.SVC_Handler       stm32f4xx_it.o
    0x080013d6   0x080013d6   0x00000002   PAD
    0x080013d8   0x080013d8   0x00000120   Code   RO          425    i.Screen_ProcessCommand  usart.o
    0x080014f8   0x080014f8   0x00000020   Code   RO          426    i.Servo_CalculateChecksum  usart.o
    0x08001518   0x08001518   0x0000003c   Code   RO          427    i.Servo_LoadMotor   usart.o
    0x08001554   0x08001554   0x00000052   Code   RO          428    i.Servo_MoveToPosition  usart.o
    0x080015a6   0x080015a6   0x00000002   PAD
    0x080015a8   0x080015a8   0x0000007c   Code   RO          429    i.Servo_ProcessPacket  usart.o
    0x08001624   0x08001624   0x00000036   Code   RO          430    i.Servo_ReadPosition  usart.o
    0x0800165a   0x0800165a   0x00000002   PAD
    0x0800165c   0x0800165c   0x000000ec   Code   RO          244    i.SetSysClock       system_stm32f4xx.o
    0x08001748   0x08001748   0x00000028   Code   RO          616    i.SysTick_CLKSourceConfig  misc.o
    0x08001770   0x08001770   0x00000002   Code   RO          154    i.SysTick_Handler   stm32f4xx_it.o
    0x08001772   0x08001772   0x00000002   PAD
    0x08001774   0x08001774   0x00000068   Code   RO          246    i.SystemInit        system_stm32f4xx.o
    0x080017dc   0x080017dc   0x000000d8   Code   RO          432    i.USART1_IRQHandler  usart.o
    0x080018b4   0x080018b4   0x00000080   Code   RO          433    i.USART2_IRQHandler  usart.o
    0x08001934   0x08001934   0x0000003c   Code   RO          434    i.USART2_SendArray  usart.o
    0x08001970   0x08001970   0x00000018   Code   RO         1148    i.USART_Cmd         stm32f4xx_usart.o
    0x08001988   0x08001988   0x0000001a   Code   RO         1151    i.USART_GetFlagStatus  stm32f4xx_usart.o
    0x080019a2   0x080019a2   0x00000054   Code   RO         1152    i.USART_GetITStatus  stm32f4xx_usart.o
    0x080019f6   0x080019f6   0x0000004a   Code   RO         1154    i.USART_ITConfig    stm32f4xx_usart.o
    0x08001a40   0x08001a40   0x000000d4   Code   RO         1155    i.USART_Init        stm32f4xx_usart.o
    0x08001b14   0x08001b14   0x0000000a   Code   RO         1162    i.USART_ReceiveData  stm32f4xx_usart.o
    0x08001b1e   0x08001b1e   0x00000002   PAD
    0x08001b20   0x08001b20   0x0000003c   Code   RO          435    i.USART_SendArray   usart.o
    0x08001b5c   0x08001b5c   0x00000008   Code   RO         1165    i.USART_SendData    stm32f4xx_usart.o
    0x08001b64   0x08001b64   0x00000004   Code   RO          155    i.UsageFault_Handler  stm32f4xx_it.o
    0x08001b68   0x08001b68   0x00000004   Code   RO          436    i._sys_exit         usart.o
    0x08001b6c   0x08001b6c   0x0000003c   Code   RO          354    i.delay_init        delay.o
    0x08001ba8   0x08001ba8   0x00000038   Code   RO          355    i.delay_ms          delay.o
    0x08001be0   0x08001be0   0x0000004c   Code   RO          357    i.delay_xms         delay.o
    0x08001c2c   0x08001c2c   0x00000050   Code   RO            3    i.main              main.o
    0x08001c7c   0x08001c7c   0x000000ac   Code   RO          438    i.uart2_init        usart.o
    0x08001d28   0x08001d28   0x000000ac   Code   RO          439    i.uart_init         usart.o
    0x08001dd4   0x08001dd4   0x0000000a   Code   RO         1425    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08001dde   0x08001dde   0x00000002   PAD
    0x08001de0   0x08001de0   0x00000020   Data   RO         1491    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08001e00, Size: 0x000007e0, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08001e00   0x00000004   Data   RW          358    .data               delay.o
    0x20000004   0x08001e04   0x0000004c   Data   RW          441    .data               usart.o
    0x20000050   0x08001e50   0x00000010   Data   RW          807    .data               stm32f4xx_rcc.o
    0x20000060        -       0x0000011e   Zero   RW          440    .bss                usart.o
    0x2000017e   0x08001e60   0x00000002   PAD
    0x20000180        -       0x00000060   Zero   RW         1367    .bss                c_w.l(libspace.o)
    0x200001e0        -       0x00000200   Zero   RW          604    HEAP                startup_stm32f40_41xxx.o
    0x200003e0        -       0x00000400   Zero   RW          603    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       192         12          0          4          0       2148   delay.o
        64          4          0          0          0        583   led.o
        80          4          0          0          0     262659   main.o
       180         24          0          0          0       2257   misc.o
        64         26        392          0       1536        868   startup_stm32f40_41xxx.o
       218          0          0          0          0       2506   stm32f4xx_gpio.o
        26          0          0          0          0       4490   stm32f4xx_it.o
       328         36          0         16          0       4895   stm32f4xx_rcc.o
       438          8          0          0          0       5845   stm32f4xx_usart.o
       340         32          0          0          0       1617   system_stm32f4xx.o
      5028        382          0         76        286      24374   usart.o

    ----------------------------------------------------------------------
      6972        <USER>        <GROUP>         96       1824     312242   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        14          0          0          0          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         2          0          0          0          0          0   libinit.o
         6          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        10          0          0          0          0        116   fpinit.o

    ----------------------------------------------------------------------
       284         <USER>          <GROUP>          0         96        700   Library Totals
         8          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       266         12          0          0         96        584   c_w.l
        10          0          0          0          0        116   fz_wm.l

    ----------------------------------------------------------------------
       284         <USER>          <GROUP>          0         96        700   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      7256        540        424         96       1920     307686   Grand Totals
      7256        540        424         96       1920     307686   ELF Image Totals
      7256        540        424         96          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 7680 (   7.50kB)
    Total RW  Size (RW Data + ZI Data)              2016 (   1.97kB)
    Total ROM Size (Code + RO Data + RW Data)       7776 (   7.59kB)

==============================================================================

