<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>数据记录控件 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="文件浏览器控件" href="FileBrowser.html" />
    <link rel="prev" title="滑动文本控件" href="SLText.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">控件详解</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="component.html">认识控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Page.html">页面控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Text.html">文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Scrolling_text.html">滚动文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Number.html">数字控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Xfloat.html">虚拟浮点数控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Button.html">按钮控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Progress_bar.html">进度条控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Picture.html">图片控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Crop.html">切图控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Hotspot.html">触摸热区控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="TouchCap.html">触摸捕捉控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Gauge.html">指针控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Waveform.html">曲线波形控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Slider.html">滑块控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Timer.html">定时器控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Variable.html">变量控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Dual-state_button.html">双态按钮控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Checkbox.html">复选框控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Radio.html">单选框控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="QRcode.html">二维码控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Switch.html">状态开关控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="ComboBox.html">下拉框控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="TextSelect.html">选择文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="SLText.html">滑动文本控件</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">数据记录控件</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#id2">数据记录控件-使用详解</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id3">数据记录控件有哪些方法</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id4">数据记录控件插入一条数据</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id5">数据记录控件插入数字控件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#rtc">数据记录控件插入rtc时间信息</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id6">数据记录控件删除选中的数据</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id7">数据记录控件清空数据</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id8">数据记录控件更新被选中的数据</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id9">数据记录控件修改某一行的背景色和字体色</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id11">数据记录翻页功能实现</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id13">数据记录控件回到顶部,回到底部,回到中间</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id14">获取数据记录某一行中某一个格</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id15">数据记录超过12列</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id16">数据记录控件由3列修改为4列或更多列</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id17">如何修改数据记录控件显示的字体大小和样式</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id18">隐藏数据记录控件表头</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#id19">数据记录控件-常见问题</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id20">数据记录控件变黑报错，黑屏</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id24">将数据记录控件保存在内存中</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#id25">数据记录控件-样例工程下载</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id37">数据记录控件-相关链接</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id38">数据记录控件-属性详解</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="FileBrowser.html">文件浏览器控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="FileStream.html">文件流控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Gmov.html">动画控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Video.html">视频控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Audio.html">音频控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="ExPicture.html">外部图片控件</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">所有控件详解</a> &raquo;</li>
      <li>数据记录控件</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>数据记录控件<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<p>数据记录控件仅X2、X3、X5系列支持</p>
<p>数据记录控件一定是全局的，无法更改为私有属性，每个工程最多使用8个数据记录控件，多了会出现黑屏，目前没有办法支持更多的数据记录控件。</p>
<p>数据记录控件类似于数据库，但是目前只有增删改三个功能，使用前需要提前导入字库，制作字库请参考 <a class="reference internal" href="../start/create_project/create_project_5.html#id1"><span class="std std-ref">创建字库和导入字库</span></a></p>
<p>数据记录是通过相关方法来添加，修改数据。而不是通过属性的txt，val赋值。</p>
<p>数据记录控件会自动创建对应的.data文件用于记录数据，请勿自己创建文件,如果自己提前创建了文件,文件结构是不符合数据记录控件的文件结构,会导致黑屏报错。</p>
<p>如何修改显示的字体大小：需要提前导入不同大小的字库，需要修改控件显示的字体大小时，通过上位机编辑或者通过指令修改控件的font属性即可，请参考 <a class="reference internal" href="../QA/QA45.html#id1"><span class="std std-ref">如何修改控件显示的字体</span></a></p>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>导入字库请参考:  <a class="reference internal" href="../QA/QA115.html#id1"><span class="std std-ref">如何导入字库</span></a></p>
<p>导入图片请参考:  <a class="reference internal" href="../QA/QA116.html#id1"><span class="std std-ref">如何导入图片</span></a></p>
<p>导入动画请参考:  <a class="reference internal" href="../QA/QA117.html#id1"><span class="std std-ref">如何导入动画</span></a></p>
<p>导入视频请参考:  <a class="reference internal" href="../QA/QA118.html#id1"><span class="std std-ref">如何导入视频</span></a></p>
<p>导入音频请参考:  <a class="reference internal" href="../QA/QA119.html#id1"><span class="std std-ref">如何导入音频</span></a></p>
</div>
<section id="id2">
<h2>数据记录控件-使用详解<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<section id="id3">
<h3>数据记录控件有哪些方法<a class="headerlink" href="#id3" title="此标题的永久链接"></a></h3>
<img alt="../_images/datarecord_1.jpg" src="../_images/datarecord_1.jpg" />
<p>数据记录控件有4个方法，分别为insert，delete，up，clear</p>
<p>insert：追加一条记录(成功返回1,失败返回0)</p>
<p>delete：删除数据(成功返回1,失败返回0)</p>
<p>up：修改一条记录(成功返回1,失败返回0)</p>
<p>clear：清除所有数据记录</p>
</section>
<section id="id4">
<h3>数据记录控件插入一条数据<a class="headerlink" href="#id4" title="此标题的永久链接"></a></h3>
<p>假设文本控件t0，t1，t2分别记录了你需要插入数据记录控件的数据,还需要创建一个文本控件tmp来组合这些数据，请注意将tmp的txt_maxl设置得足够大</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span> //每个字段之间以^隔开
<span class="linenos">2</span> tmp.txt<span class="o">=</span>t0.txt+<span class="s2">&quot;^&quot;</span>+t1.txt+<span class="s2">&quot;^&quot;</span>+t2.txt
<span class="linenos">3</span>
<span class="linenos">4</span> //将合并好的文本插入数据记录控件中
<span class="linenos">5</span> data0.insert<span class="o">(</span>tmp.txt<span class="o">)</span>
</pre></div>
</div>
<img alt="../_images/datarecord_7.jpg" src="../_images/datarecord_7.jpg" />
<img alt="../_images/datarecord_8.jpg" src="../_images/datarecord_8.jpg" />
</section>
<section id="id5">
<h3>数据记录控件插入数字控件<a class="headerlink" href="#id5" title="此标题的永久链接"></a></h3>
<p>假设数字控件n0，n1，n2分别记录了你需要插入数据记录控件的数据,文本控件t0，t1，t2用来存储转换后的数据,还需要创建一个文本控件tmp来组合这些数据，请注意将tmp的txt_maxl设置得足够大</p>
<p>用covx把数字控件转换为文本控件，再拼接即可</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span> covx n0.val,t0.txt,0,0
<span class="linenos">2</span> covx n1.val,t1.txt,0,0
<span class="linenos">3</span> covx n2.val,t2.txt,0,0
<span class="linenos">4</span>
<span class="linenos">5</span> //每个字段之间以^隔开
<span class="linenos">6</span> tmp.txt<span class="o">=</span>t0.txt+<span class="s2">&quot;^&quot;</span>+t1.txt+<span class="s2">&quot;^&quot;</span>+t2.txt
<span class="linenos">7</span>
<span class="linenos">8</span> //将合并好的文本插入数据记录控件中
<span class="linenos">9</span> data0.insert<span class="o">(</span>tmp.txt<span class="o">)</span>
</pre></div>
</div>
<img alt="../_images/datarecord_9.jpg" src="../_images/datarecord_9.jpg" />
<img alt="../_images/datarecord_10.jpg" src="../_images/datarecord_10.jpg" />
</section>
<section id="rtc">
<h3>数据记录控件插入rtc时间信息<a class="headerlink" href="#rtc" title="此标题的永久链接"></a></h3>
<p>请注意将tmp和time的txt_maxl设置得足够大，rtc功能仅x5和k0系列支持</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span> covx rtc0,time0.txt,0,0
<span class="linenos"> 2</span> covx rtc1,time1.txt,0,0
<span class="linenos"> 3</span> covx rtc2,time2.txt,0,0
<span class="linenos"> 4</span> covx rtc3,time3.txt,0,0
<span class="linenos"> 5</span> covx rtc4,time4.txt,0,0
<span class="linenos"> 6</span> covx rtc5,time5.txt,0,0
<span class="linenos"> 7</span> time.txt<span class="o">=</span>time0.txt+time1.txt+time2.txt+time3.txt+time4.txt+time5.txt
<span class="linenos"> 8</span>
<span class="linenos"> 9</span> //每个字段之间以^隔开
<span class="linenos">10</span> tmp.txt<span class="o">=</span>time.txt+<span class="s2">&quot;^&quot;</span>+t1.txt+<span class="s2">&quot;^&quot;</span>+t2.txt
<span class="linenos">11</span>
<span class="linenos">12</span> //将合并好的文本插入数据记录控件中
<span class="linenos">13</span> data0.insert<span class="o">(</span>tmp.txt<span class="o">)</span>
</pre></div>
</div>
<img alt="../_images/datarecord_11.jpg" src="../_images/datarecord_11.jpg" />
<img alt="../_images/datarecord_12.jpg" src="../_images/datarecord_12.jpg" />
</section>
<section id="id6">
<h3>数据记录控件删除选中的数据<a class="headerlink" href="#id6" title="此标题的永久链接"></a></h3>
<p>当配置了数据记录控件的dis属性（是否允许触摸选中记录项）时，数据记录控件的val属性将会变为选中项所在的行数</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span> //删除被选中的行
<span class="linenos">2</span> data0.delete<span class="o">(</span>data0.val,1<span class="o">)</span>
</pre></div>
</div>
</section>
<section id="id7">
<h3>数据记录控件清空数据<a class="headerlink" href="#id7" title="此标题的永久链接"></a></h3>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span> //清空数据记录控件
<span class="linenos">2</span> data0.clear<span class="o">()</span>
</pre></div>
</div>
</section>
<section id="id8">
<h3>数据记录控件更新被选中的数据<a class="headerlink" href="#id8" title="此标题的永久链接"></a></h3>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span> //每个字段之间以^隔开
<span class="linenos">2</span> tmp.txt<span class="o">=</span>t0.txt+<span class="s2">&quot;^&quot;</span>+t1.txt+<span class="s2">&quot;^&quot;</span>+t2.txt
<span class="linenos">3</span>
<span class="linenos">4</span> //更新被选中的数据
<span class="linenos">5</span> data0.up<span class="o">(</span>tmp.txt,data0.val<span class="o">)</span>
</pre></div>
</div>
</section>
<section id="id9">
<h3>数据记录控件修改某一行的背景色和字体色<a class="headerlink" href="#id9" title="此标题的永久链接"></a></h3>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span>//将背景颜色设置为1024（绿色），将字体颜色设置为63488（红色），添加内容为1 <span class="m">2</span> <span class="m">3</span>
<span class="linenos">2</span>data0.insert<span class="o">(</span><span class="s2">&quot;&lt;font b=1024,p=63488&gt;1^2^3&quot;</span><span class="o">)</span>
<span class="linenos">3</span>
<span class="linenos">4</span>data0.up<span class="o">(</span><span class="s2">&quot;&lt;font b=1024,p=63488&gt;1^2^3&quot;</span>,0<span class="o">)</span>
</pre></div>
</div>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span>//（将n1设置的颜色改变选中表格）
<span class="linenos">2</span>covx n1.val,va0.txt,0,0
<span class="linenos">3</span>//请注意va2的长度以及数据记录控件的lenth属性是否足够大
<span class="linenos">4</span>va2.txt<span class="o">=</span><span class="s2">&quot;&lt;font b=&quot;</span>+va0.txt+<span class="s2">&quot;&gt;&quot;</span>+t0.txt
<span class="linenos">5</span>//（将原来的内容添加上背景颜色为n1.val）
<span class="linenos">6</span>data0.up<span class="o">(</span>va2.txt,n0.val<span class="o">)</span>
</pre></div>
</div>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/数据记录控件/数据记录控件彩色.HMI">《数据记录控件彩色》演示工程下载</a></p>
</section>
<section id="id11">
<h3>数据记录翻页功能实现<a class="headerlink" href="#id11" title="此标题的永久链接"></a></h3>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">sys0</span><span class="o">=</span><span class="n">n</span><span class="o">*</span><span class="n">data0</span><span class="o">.</span><span class="n">hig</span>  <span class="o">//</span><span class="n">翻页n行</span>
<span class="n">data0</span><span class="o">.</span><span class="n">val_y</span><span class="o">+=</span><span class="n">sys0</span>
</pre></div>
</div>
<p>hig:显示记录的行高度</p>
<p>n:翻页的行数，n为正数向后翻页，n为负数向前翻页</p>
<p>具体例子：向前翻页5行</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">sys0</span><span class="o">=-</span><span class="mi">5</span><span class="o">*</span><span class="n">data0</span><span class="o">.</span><span class="n">hig</span>
<span class="n">data0</span><span class="o">.</span><span class="n">val_y</span><span class="o">+=</span><span class="n">sys0</span>
</pre></div>
</div>
<p>具体例子：向后翻页5行</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">sys0</span><span class="o">=</span><span class="mi">5</span><span class="o">*</span><span class="n">data0</span><span class="o">.</span><span class="n">hig</span>
<span class="n">data0</span><span class="o">.</span><span class="n">val_y</span><span class="o">+=</span><span class="n">sys0</span>
</pre></div>
</div>
<p>参考工程下载地址</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/数据记录控件/数据记录控件实现翻页.HMI">《数据记录控件实现翻页》演示工程下载</a></p>
</section>
<section id="id13">
<h3>数据记录控件回到顶部,回到底部,回到中间<a class="headerlink" href="#id13" title="此标题的永久链接"></a></h3>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="o">//</span><span class="n">回到顶部</span>
<span class="n">data0</span><span class="o">.</span><span class="n">val_y</span><span class="o">=</span><span class="mi">0</span>

<span class="o">//</span><span class="n">回到底部</span>
<span class="n">data0</span><span class="o">.</span><span class="n">val_y</span><span class="o">=</span><span class="n">data0</span><span class="o">.</span><span class="n">maxval_y</span>

<span class="o">//</span><span class="n">回到中间</span>
<span class="n">data0</span><span class="o">.</span><span class="n">val_y</span><span class="o">=</span><span class="n">data0</span><span class="o">.</span><span class="n">maxval_y</span><span class="o">/</span><span class="mi">2</span>
</pre></div>
</div>
</section>
<section id="id14">
<h3>获取数据记录某一行中某一个格<a class="headerlink" href="#id14" title="此标题的永久链接"></a></h3>
<p>获取数据记录某一行（点击相应数据记录中的记录或者对属性val赋值）中某一个格使用到的是 spstr指令获取</p>
<p>参考： <a class="reference internal" href="../commands/spstr.html#spstr"><span class="std std-ref">spstr-字符串分割</span></a></p>
</section>
<section id="id15">
<h3>数据记录超过12列<a class="headerlink" href="#id15" title="此标题的永久链接"></a></h3>
<p><a class="reference internal" href="#id25"><span class="std std-ref">数据记录控件-样例工程下载</span></a></p>
</section>
<section id="id16">
<h3>数据记录控件由3列修改为4列或更多列<a class="headerlink" href="#id16" title="此标题的永久链接"></a></h3>
<p>需要修改数据记录控件的dez属性，只能在上位机中修改，不能通过指令修改，修改后数据记录控件会显示黑屏和报错信息</p>
<p>此时你需要到SD卡或者虚拟SD卡的目录下删除原有的.data文件，重新调试运行后，控件会自动生成新的.data文件</p>
<p>修改了length属性或者maxval属性导致的黑屏也是同样的操作进行解决</p>
</section>
<section id="id17">
<h3>如何修改数据记录控件显示的字体大小和样式<a class="headerlink" href="#id17" title="此标题的永久链接"></a></h3>
<p>请参考 <a class="reference internal" href="../QA/QA45.html#id1"><span class="std std-ref">如何修改控件显示的字体</span></a></p>
</section>
<section id="id18">
<h3>隐藏数据记录控件表头<a class="headerlink" href="#id18" title="此标题的永久链接"></a></h3>
<p>删除数据记录控件的dir属性里的值即可</p>
</section>
</section>
<section id="id19">
<h2>数据记录控件-常见问题<a class="headerlink" href="#id19" title="此标题的永久链接"></a></h2>
<section id="id20">
<h3>数据记录控件变黑报错，黑屏<a class="headerlink" href="#id20" title="此标题的永久链接"></a></h3>
<section id="file-configuration-data-does-not-match-component-configuration-data-it-is-recommended-to-delete-this-file-the-system-will-recreate-the-correct-data-file-sd0-xxx-data">
<h4>File configuration data does not match Component configuration data. It is recommended to delete this file. The system will recreate the correct data <a class="reference external" href="file:sd0/">file:sd0/</a> xxx.data<a class="headerlink" href="#file-configuration-data-does-not-match-component-configuration-data-it-is-recommended-to-delete-this-file-the-system-will-recreate-the-correct-data-file-sd0-xxx-data" title="此标题的永久链接"></a></h4>
<img alt="../_images/datarecord_2.jpg" src="../_images/datarecord_2.jpg" />
<p>因为记录的字段和所指定的.data文件中的字段数量不符导致的，有多种解决方法</p>
<p>1.你改动了数据记录控件的length、maxval、dez属性，将其恢复即可</p>
<p>2.把存储卡或者虚拟sd卡文件夹中的原本绑定的对应的的.data文件删除</p>
<p>3.修改数据记录控件的path属性，将其指向一个不存在的文件（将会自动创建），例如原本是sd0/1.data，改为sd0/123.data</p>
<img alt="../_images/datarecord_error_1.jpg" src="../_images/datarecord_error_1.jpg" />
<img alt="../_images/datarecord_error_2.jpg" src="../_images/datarecord_error_2.jpg" />
<img alt="../_images/datarecord_error_3.jpg" src="../_images/datarecord_error_3.jpg" />
</section>
<section id="file-lost">
<h4>提示file lost<a class="headerlink" href="#file-lost" title="此标题的永久链接"></a></h4>
<p>没插micro sd(tf)卡</p>
<p>卡的格式不对（非fat32格式）</p>
<p>尝试换一张卡</p>
</section>
<section id="id21">
<h4>多个数据记录打开同一个路径下文件导致黑屏<a class="headerlink" href="#id21" title="此标题的永久链接"></a></h4>
<p>这是因为文件已经被上一个数据记录打开导致新的数据记录无法打开文件。</p>
<p>举例:</p>
<p>page0,page1,page2各有一个数据记录控件，都同时调用 sd0/1.data ，此时只有page0页面是正常的，其他页面的数据记录都会黑屏</p>
<p>我们可以通过以下方法解决</p>
<p>即每次跳转时，将当前页面的数据记录控件指向一个临时的文件，从而解除目标文件的占用状态，再将即将跳转到的页面的数据记录控件指向目标文件</p>
<p>当需要从page0跳转到page1时，在对应的控件中写下</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">page0</span><span class="o">.</span><span class="n">data0</span><span class="o">.</span><span class="n">path</span><span class="o">=</span><span class="s2">&quot;sd0/tmp0.data&quot;</span>
<span class="n">page1</span><span class="o">.</span><span class="n">data0</span><span class="o">.</span><span class="n">path</span><span class="o">=</span><span class="s2">&quot;sd0/1.data&quot;</span>
<span class="n">page</span> <span class="n">page1</span>
</pre></div>
</div>
<p>当需要从page1跳转到page2时，在对应的控件中写下</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">page1</span><span class="o">.</span><span class="n">data0</span><span class="o">.</span><span class="n">path</span><span class="o">=</span><span class="s2">&quot;sd0/tmp1.data&quot;</span>
<span class="n">page2</span><span class="o">.</span><span class="n">data0</span><span class="o">.</span><span class="n">path</span><span class="o">=</span><span class="s2">&quot;sd0/1.data&quot;</span>
<span class="n">page</span> <span class="n">page2</span>
</pre></div>
</div>
<p>当需要从page2跳转到page0时，在对应的控件中写下</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">page2</span><span class="o">.</span><span class="n">data0</span><span class="o">.</span><span class="n">path</span><span class="o">=</span><span class="s2">&quot;sd0/tmp2.data&quot;</span>
<span class="n">page0</span><span class="o">.</span><span class="n">data0</span><span class="o">.</span><span class="n">path</span><span class="o">=</span><span class="s2">&quot;sd0/1.data&quot;</span>
<span class="n">page</span> <span class="n">page0</span>
</pre></div>
</div>
</section>
<section id="path">
<h4>数据记录path属性赋值注意事项<a class="headerlink" href="#path" title="此标题的永久链接"></a></h4>
<p>不要拼接时直接赋值给数据记录path属性,要通过一个中间变量拼接好后整体赋值给数据记录path属性</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">page1</span><span class="o">.</span><span class="n">data0</span><span class="o">.</span><span class="n">path</span><span class="o">=</span><span class="s2">&quot;sd0/&quot;</span><span class="o">+</span><span class="n">t1</span><span class="o">.</span><span class="n">txt</span><span class="o">+</span><span class="s2">&quot;.data&quot;</span> <span class="o">//</span><span class="n">错误方式</span>
</pre></div>
</div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">t0</span><span class="o">.</span><span class="n">txt</span><span class="o">=</span><span class="s2">&quot;sd0/&quot;</span><span class="o">+</span><span class="n">t1</span><span class="o">.</span><span class="n">txt</span><span class="o">+</span><span class="s2">&quot;.data&quot;</span> <span class="o">//</span><span class="n">正确方式</span>
<span class="n">page1</span><span class="o">.</span><span class="n">data0</span><span class="o">.</span><span class="n">path</span><span class="o">=</span><span class="n">t0</span><span class="o">.</span><span class="n">txt</span>
</pre></div>
</div>
</section>
<section id="id22">
<h4>数据记录超过8个的解决方法<a class="headerlink" href="#id22" title="此标题的永久链接"></a></h4>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/数据记录控件/数据记录超过8个的解决方法.HMI">《数据记录超过8个的解决方法》演示工程下载</a></p>
</section>
</section>
<section id="id24">
<h3>将数据记录控件保存在内存中<a class="headerlink" href="#id24" title="此标题的永久链接"></a></h3>
<p>不需要sd卡，但是重启后数据会消失</p>
<p>先打开虚拟SD卡文件夹，查看默认设置下生成的.data文件有多大</p>
<p>例如当前绑定的数据记录文件为“sd0/1.data”</p>
<p>则查看“1.data”文件的体积</p>
<img alt="../_images/datarecord_6.jpg" src="../_images/datarecord_6.jpg" />
<p>设备-工程-内存文件存储区大小设置为100000Byte(理论上稍大于文件占用空间69632Byte 几个KB就行了，取100000Byte是因为方便)，这个值只建议比生成的文件刚好大一点点，如果设置得过大，会占用过多内存，导致使用其他控件时出现问题</p>
<img alt="../_images/datarecord_3.jpg" src="../_images/datarecord_3.jpg" />
<img alt="../_images/datarecord_4.jpg" src="../_images/datarecord_4.jpg" />
<p>将数据记录的位置指定到ram中</p>
<img alt="../_images/datarecord_5.jpg" src="../_images/datarecord_5.jpg" />
</section>
</section>
<section id="id25">
<h2>数据记录控件-样例工程下载<a class="headerlink" href="#id25" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/数据记录控件/使用滑块滑动数据记录.HMI">《使用滑块滑动数据记录》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/数据记录控件/数据记录超过8个的解决方法.HMI">《数据记录超过8个的解决方法》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/数据记录控件/数据记录控件彩色.HMI">《数据记录控件彩色》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/数据记录控件/数据记录控件超过12个字段解决方案.HMI">《数据记录控件超过12个字段解决方案》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/数据记录控件/数据记录控件打开列数不同的文件.HMI">《数据记录控件打开列数不同的文件》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/数据记录控件/数据记录控件获取选中的行.HMI">《数据记录控件获取选中的行》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/数据记录控件/数据记录控件实现翻页.HMI">《数据记录控件实现翻页》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/数据记录控件/数据记录例程1.HMI">《数据记录例程1》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/数据记录控件/数据记录例程2.HMI">《数据记录例程2》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/数据记录控件/数据记录例程3.HMI">《数据记录例程3》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/文件浏览器控件/文件搜索.HMI">《文件搜索》演示工程下载</a></p>
</section>
<section id="id37">
<h2>数据记录控件-相关链接<a class="headerlink" href="#id37" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="../QA/QA10.html#id1"><span class="std std-ref">哪些控件属性可以运行中修改，哪些不能运行中修改，绿色属性和黑色属性有什么区别？</span></a></p>
<p><a class="reference internal" href="../QA/QA11.html#txtval"><span class="std std-ref">txt属性和val属性有什么区别，为什么有些属性赋值要加双引号，有些不用加双引号</span></a></p>
<p><a class="reference internal" href="../grammar/global_variable.html#id1"><span class="std std-ref">跨页面赋值，全局变量操作</span></a></p>
</section>
<section id="id38">
<h2>数据记录控件-属性详解<a class="headerlink" href="#id38" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="component.html#id6"><span class="std std-ref">控件属性解析</span></a></p>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>绿色属性可以通过上位机或者串口屏指令进行修改，黑色属性只能在上位机中修改或者不可修改，可通过上位机进行修改指“选中控件后通过属性栏修改控件的属性”</p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">type属性</span></code> -控件类型，固定值，不同类型的控件type值不同，相同类型的控件type值相同，可读，不可通过上位机修改，不可通过指令修改。参考： <a class="reference internal" href="component.html#id10"><span class="std std-ref">控件属性-控件id对照表</span></a></p>
<p><code class="docutils literal notranslate"><span class="pre">id属性</span></code> -控件ID，可通过上位机左上角的上下箭头置顶或置底，可读，可通过上位机修改左上角的箭头置顶或置地间接修改，不可通过指令修改。参考： <a class="reference internal" href="../QA/QA28.html#id1"><span class="std std-ref">如何更改控件的前后图层关系</span></a></p>
<p><code class="docutils literal notranslate"><span class="pre">objname属性</span></code> -控件名称。不可读，可通过上位机进行修改，不可通过指令更改。</p>
<p><code class="docutils literal notranslate"><span class="pre">vscope属性</span></code> -内存占用(私有占用只能在当前页面被访问，全局占用可以在所有页面被访问)，当设置为私有时，跳转页面后，该控件占用的内存会被释放，重新返回该页面后该控件会恢复到最初的设置。可读，可通过上位机进行修改，不可通过指令更改。参考：<a class="reference internal" href="../grammar/global_variable.html#id1"><span class="std std-ref">跨页面赋值，全局变量操作</span></a></p>
<p>数据记录控件的vscope只能是全局</p>
<p><code class="docutils literal notranslate"><span class="pre">drag属性</span></code> -是否支持拖动:0-否;1-是。仅x系列支持。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">aph属性</span></code> -不透明度(0-127)，0为完全透明，127为完全不透明。仅x系列支持。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">effect属性</span></code> -加载特效:0-立即加载;1-上边飞入;2-下边飞入;3-左边飞入;4-右边飞入;5-左上角飞入;6-右上角飞入;7-左下角飞入;8-右下角飞入。仅x系列支持，在上位机中设置为立即加载时，无法通过指令变为其他特效，当在上位机中设置为非立即加载的特效时，可以变为立即加载，也可以再改为其他特效</p>
<p><code class="docutils literal notranslate"><span class="pre">sta属性</span></code> -背景填充方式:0-切图;1-单色;2-图片;3-透明（仅x系列支持透明）。可读，可通过上位机修改，不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">picc属性</span></code> -切图背景(必须是全屏图片)，sta为切图时才有这个属性。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">bco属性</span></code> -背景色，sta为单色时才有这个属性。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">pic属性</span></code> -背景图片，sta为图片时才有这个属性。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">pco属性</span></code> -字体色。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">borderc属性</span></code> -边框颜色。可读，可通过上位机修改，x系列可通过指令修改，其他系列不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">borderw属性</span></code> 边框粗细。最大值:255。可读，可通过上位机修改，x系列可通过指令修改，其他系列不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">font属性</span></code> -控件调用的字库id，调用不同的字库会显示不同的字体或字号。可读，可通过上位机修改，可通过指令修改。参考：1、 <a class="reference internal" href="../start/create_project/create_project_5.html#id1"><span class="std std-ref">创建字库和导入字库</span></a>  2、   <a class="reference internal" href="../QA/QA8.html#id1"><span class="std std-ref">指定字库</span></a></p>
<p><code class="docutils literal notranslate"><span class="pre">xcen属性</span></code> -水平对齐:0-靠左;1-居中;2-靠右。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">path属性</span></code> -绑定数据文件路径(如:”sd0/1.data”)。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">lenth属性</span></code> -当前绑定数据记录文件中每条记录最大字节数(最大长度255字节)。可读，可通过上位机修改，不可通过指令修改。通过上位机修改需删除原有的.data文件，否则会黑屏。</p>
<p><code class="docutils literal notranslate"><span class="pre">maxval属性</span></code> -当前绑定数据记录文件可存入的最大行数,超出之后的新增记录将循环覆盖老数据。可读，可通过上位机修改，不可通过指令修改。通过上位机修改需删除原有的.data文件，否则会黑屏。</p>
<p><code class="docutils literal notranslate"><span class="pre">dez属性</span></code> -当前绑定数据记录文件中设置的字段数量(最小1,最大12)。可读，可通过上位机修改，不可通过指令修改。通过上位机修改需删除原有的.data文件，否则会黑屏。</p>
<p><code class="docutils literal notranslate"><span class="pre">format属性</span></code> -字段宽度自定义(直接输入字段宽度值,多个字段使用^分隔,如:100^100)。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">dir属性</span></code> -表头名称自定义(多个字段使用^分隔)。可读，可通过上位机修改，可通过指令修改。值为空时会隐藏表头。</p>
<p><code class="docutils literal notranslate"><span class="pre">mode属性</span></code> -是否允许自动创建文件(path路径无效时):0-不允许;1-允许。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">dis属性</span></code> -是否允许触摸选中记录项:0-不允许;1-允许。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">order属性</span></code> -显示顺序:0-新数据在前;1-新数据在后。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">qty属性</span></code> -当前数据文件总记录数。可读，不可通过上位机修改，不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">spax属性</span></code> -字符横向间距(最小0,最大255)。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">hig属性</span></code> -显示记录的行高度(最小1,最大255)。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">left属性</span></code> -是否显进度条:0-不显示;1-操作时显示;2-持续显示。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">gdc属性</span></code> -表格线颜色。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">gdw属性</span></code> -横向表格线宽度(0为关闭)。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">gdh属性</span></code> -纵向表格线宽度(0为关闭)。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">bco1属性</span></code> -表头背景色。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">pco1属性</span></code> -表头字体色。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">bco2属性</span></code> -选中项背景色。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">pco2属性</span></code> -选中项字体色。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">val属性</span></code> -当前选中行记录ID(每变化一次,txt属性将重新加载此ID的行记录内容)。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">txt属性</span></code> -行记录内容(val值代表的行号记录内容，只可读取不可修改)</p>
<p><code class="docutils literal notranslate"><span class="pre">ch属性</span></code> -滑动惯性力度(0-32,0为无惯性)。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">maxval_y属性</span></code> -最大纵向滑动值(运行中根据字符内容自动改变,只可读取不可设置)</p>
<p><code class="docutils literal notranslate"><span class="pre">val_y属性</span></code> -当前纵向滑动值(最小0,最大maxval_y)。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">maxval_x属性</span></code> -最大横向滑动值(运行中根据字符内容自动改变,只可读取不可设置)</p>
<p><code class="docutils literal notranslate"><span class="pre">val_x属性</span></code> -当前横向滑动值(最小0,最大maxval_x)。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">x属性</span></code> -控件的X坐标。可读，可通过上位机修改，x系列可通过指令修改，其他系列不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">y属性</span></code> -控件的Y坐标。可读，可通过上位机修改，x系列可通过指令修改，其他系列不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">w属性</span></code> -控件的宽度。可读，可通过上位机修改，不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">h属性</span></code> -控件的高度。可读，可通过上位机修改，不可通过指令修改。</p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="SLText.html" class="btn btn-neutral float-left" title="滑动文本控件" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="FileBrowser.html" class="btn btn-neutral float-right" title="文件浏览器控件" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>