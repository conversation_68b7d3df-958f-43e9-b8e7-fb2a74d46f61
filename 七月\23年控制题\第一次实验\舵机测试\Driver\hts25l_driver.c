/**
  ******************************************************************************
  * @file    hts25l_driver.c
  * <AUTHOR> Analysis Expert
  * @version V1.0
  * @date    2025-07-17
  * @brief   HTS-25L总线舵机驱动实现
  ******************************************************************************
  */

#include "hts25l_driver.h"
#include "bsp_usart.h"
#include <stddef.h>  // for NULL

/**
  * @brief  计算校验和
  * @param  id: 舵机ID
  * @param  length: 长度字段
  * @param  cmd: 指令
  * @param  params: 参数数组
  * @param  param_len: 参数长度
  * @retval 校验和
  */
static uint8_t HTS25L_CalcChecksum(uint8_t id, uint8_t length, uint8_t cmd, 
                                   const uint8_t *params, uint8_t param_len)
{
    uint16_t sum = id + length + cmd;
    for(uint8_t i = 0; i < param_len; i++)
    {
        sum += params[i];
    }
    return (uint8_t)(~(sum & 0xFF));
}

/**
  * @brief  发送协议包
  * @param  id: 舵机ID
  * @param  cmd: 指令
  * @param  params: 参数数组
  * @param  param_len: 参数长度
  * @retval 0:成功, -1:失败
  */
static int HTS25L_SendPacket(uint8_t id, uint8_t cmd, const uint8_t *params, uint8_t param_len)
{
    uint8_t packet[64];
    uint8_t length = param_len + 3;  // cmd + params + checksum
    uint8_t idx = 0;
    
    // 构建数据包
    packet[idx++] = HTS25L_HEADER;
    packet[idx++] = HTS25L_HEADER;
    packet[idx++] = id;
    packet[idx++] = length;
    packet[idx++] = cmd;
    
    // 添加参数
    for(uint8_t i = 0; i < param_len; i++)
    {
        packet[idx++] = params[i];
    }
    
    // 计算并添加校验和
    packet[idx++] = HTS25L_CalcChecksum(id, length, cmd, params, param_len);
    
    // 发送数据包
    return BSP_USART2_SendData(packet, idx, BSP_USART_TX_TIMEOUT);
}

/**
  * @brief  接收协议包
  * @param  expect_id: 期望的舵机ID
  * @param  expect_cmd: 期望的指令
  * @param  payload: 接收缓冲区
  * @param  payload_len: 缓冲区长度
  * @retval 实际接收长度, -1:失败
  */
static int HTS25L_ReceivePacket(uint8_t expect_id, uint8_t expect_cmd, 
                                uint8_t *payload, uint8_t payload_len)
{
    uint8_t header[2];
    uint8_t id, length, cmd;
    uint8_t checksum, calc_checksum;
    int recv_len;
    
    // 接收帧头
    recv_len = BSP_USART2_ReceiveData(header, 2, BSP_USART_RX_TIMEOUT);
    if(recv_len != 2 || header[0] != HTS25L_HEADER || header[1] != HTS25L_HEADER)
        return -1;
    
    // 接收ID、长度、指令
    recv_len = BSP_USART2_ReceiveData(&id, 1, BSP_USART_RX_TIMEOUT);
    if(recv_len != 1) return -1;
    
    recv_len = BSP_USART2_ReceiveData(&length, 1, BSP_USART_RX_TIMEOUT);
    if(recv_len != 1) return -1;
    
    recv_len = BSP_USART2_ReceiveData(&cmd, 1, BSP_USART_RX_TIMEOUT);
    if(recv_len != 1) return -1;
    
    // 计算参数长度
    uint8_t param_len = length - 3;  // 减去cmd和checksum
    if(param_len > payload_len) return -1;
    
    // 接收参数
    if(param_len > 0)
    {
        recv_len = BSP_USART2_ReceiveData(payload, param_len, BSP_USART_RX_TIMEOUT);
        if(recv_len != param_len) return -1;
    }
    
    // 接收校验和
    recv_len = BSP_USART2_ReceiveData(&checksum, 1, BSP_USART_RX_TIMEOUT);
    if(recv_len != 1) return -1;
    
    // 验证校验和
    calc_checksum = HTS25L_CalcChecksum(id, length, cmd, payload, param_len);
    if(checksum != calc_checksum) return -1;
    
    // 验证ID和指令
    if(id != expect_id || cmd != expect_cmd) return -1;
    
    return param_len;
}

/**
  * @brief  HTS25L驱动初始化
  * @param  None
  * @retval None
  */
void HTS25L_Init(void)
{
    // USART已在BSP中初始化，这里可以添加其他初始化代码
}

/**
  * @brief  发送运动时间写指令
  * @param  id: 舵机ID
  * @param  pos: 目标位置 (0-1000)
  * @param  time_ms: 运动时间 (ms)
  * @retval 0:成功, -1:失败
  */
int HTS25L_MoveTimeWrite(uint8_t id, uint16_t pos, uint16_t time_ms)
{
    uint8_t params[4];
    
    params[0] = pos & 0xFF;         // 位置低字节
    params[1] = (pos >> 8) & 0xFF;  // 位置高字节
    params[2] = time_ms & 0xFF;     // 时间低字节
    params[3] = (time_ms >> 8) & 0xFF; // 时间高字节
    
    return HTS25L_SendPacket(id, HTS25L_CMD_MOVE_TIME_WRITE, params, 4);
}

/**
  * @brief  发送伺服/电机模式写指令
  * @param  id: 舵机ID
  * @param  mode: 模式 (0:伺服, 1:电机)
  * @param  speed: 速度 (-1000~1000)
  * @retval 0:成功, -1:失败
  */
int HTS25L_OrMotorModeWrite(uint8_t id, uint8_t mode, int16_t speed)
{
    uint8_t params[4];
    
    params[0] = mode;
    params[1] = 0;  // 保留字节
    params[2] = speed & 0xFF;         // 速度低字节
    params[3] = (speed >> 8) & 0xFF;  // 速度高字节
    
    return HTS25L_SendPacket(id, HTS25L_CMD_OR_MOTOR_MODE_WRITE, params, 4);
}

/**
  * @brief  发送力矩装载/卸载指令
  * @param  id: 舵机ID
  * @param  load: 力矩状态 (0:卸载, 1:装载)
  * @retval 0:成功, -1:失败
  */
int HTS25L_LoadOrUnloadWrite(uint8_t id, uint8_t load)
{
    uint8_t params[1];
    
    params[0] = load ? 1 : 0;
    
    return HTS25L_SendPacket(id, HTS25L_CMD_LOAD_OR_UNLOAD_WRITE, params, 1);
}

/**
  * @brief  读取舵机位置
  * @param  id: 舵机ID
  * @param  pos: 位置指针
  * @retval 0:成功, -1:失败
  */
int HTS25L_ReadPos(uint8_t id, uint16_t *pos)
{
    uint8_t payload[4];
    int recv_len;
    
    // 发送读取位置指令
    if(HTS25L_SendPacket(id, HTS25L_CMD_POS_READ, NULL, 0) != 0)
        return -1;
    
    // 接收应答
    recv_len = HTS25L_ReceivePacket(id, HTS25L_CMD_POS_READ, payload, sizeof(payload));
    if(recv_len < 2) return -1;
    
    // 解析位置数据
    *pos = payload[0] | (payload[1] << 8);
    
    return 0;
}

/**
  * @brief  角度转位置
  * @param  deg: 角度 (0-240度)
  * @retval 位置值 (0-1000)
  */
uint16_t HTS25L_AngleDegToPos(float deg)
{
    if(deg < 0) deg = 0;
    if(deg > HTS25L_ANGLE_MAX_DEG) deg = HTS25L_ANGLE_MAX_DEG;
    return (uint16_t)((deg / HTS25L_ANGLE_MAX_DEG) * HTS25L_POS_MAX + 0.5f);
}

/**
  * @brief  位置转角度
  * @param  pos: 位置值 (0-1000)
  * @retval 角度 (0-240度)
  */
float HTS25L_PosToAngleDeg(uint16_t pos)
{
    if(pos > HTS25L_POS_MAX) pos = HTS25L_POS_MAX;
    return ((float)pos / (float)HTS25L_POS_MAX) * HTS25L_ANGLE_MAX_DEG;
}
