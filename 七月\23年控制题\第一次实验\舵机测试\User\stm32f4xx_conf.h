/**
  ******************************************************************************
  * @file    stm32f4xx_conf.h
  * <AUTHOR> Analysis Expert
  * @version V1.0
  * @date    2025-07-17
  * @brief   STM32F4xx标准外设库配置文件
  ******************************************************************************
  */

#ifndef __STM32F4XX_CONF_H
#define __STM32F4XX_CONF_H

/* 芯片类型和外设库配置 */
#if !defined(STM32F40_41xxx)
  #define STM32F40_41xxx
#endif

#if !defined(USE_STDPERIPH_DRIVER)
  #define USE_STDPERIPH_DRIVER
#endif

#if !defined(HSE_VALUE)
  #define HSE_VALUE    ((uint32_t)8000000)
#endif

/* 包含外设头文件 */
#include "stm32f4xx_rcc.h"
#include "stm32f4xx_gpio.h"
#include "stm32f4xx_usart.h"
#include "stm32f4xx_flash.h"
#include "misc.h"

/* 断言宏定义 */
#ifdef  USE_FULL_ASSERT
  #define assert_param(expr) ((expr) ? (void)0 : assert_failed((uint8_t *)__FILE__, __LINE__))
  void assert_failed(uint8_t* file, uint32_t line);
#else
  #define assert_param(expr) ((void)0)
#endif /* USE_FULL_ASSERT */

#endif /* __STM32F4XX_CONF_H */
