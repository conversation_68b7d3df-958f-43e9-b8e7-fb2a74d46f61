/**
  ******************************************************************************
  * @file    bsp_led.h
  * <AUTHOR> Analysis Expert
  * @version V1.0
  * @date    2025-07-17
  * @brief   LED板级支持包头文件
  ******************************************************************************
  */

#ifndef __BSP_LED_H
#define __BSP_LED_H

#include "stm32f4xx.h"

/* LED定义 */
#define LED_HEARTBEAT    0    // PF9 - 心跳指示
#define LED_MOVING       1    // PF10 - 运动指示

/* LED GPIO定义 */
#define LED_GPIO_PORT    GPIOF
#define LED_GPIO_CLK     RCC_AHB1Periph_GPIOF

#define LED_HEARTBEAT_PIN    GPIO_Pin_9
#define LED_MOVING_PIN       GPIO_Pin_10

/* 函数声明 */
void BSP_LED_Init(void);
void BSP_LED_On(uint8_t led);
void BSP_LED_Off(uint8_t led);
void BSP_LED_Toggle(uint8_t led);

#endif /* __BSP_LED_H */
