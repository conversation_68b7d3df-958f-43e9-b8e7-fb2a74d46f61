<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="软件说明文档">
    <title>BLEdebug 使用说明</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
            display: flex;
            min-height: 100vh;
            flex-direction: row;
        }

        header {
            background-color: #f4f4f4;
            color: white;
            padding: 10px 20px;
            text-align: center;
            width: 100%;
        }

        h1 {
            font-size: 2em;
        }

        .content {
            margin-left: 220px;
            /* 给内容区域留出空间，避免被目录遮挡 */
            padding: 20px;
            width: 100%;
            overflow-y: auto;
            /* 允许内容区域滚动 */
            flex-grow: 1;
        }

        h2 {
            font-size: 1.6em;
            margin-top: 20px;
            color: #333;
        }

        h4 {
            margin-top: 0px;
            text-indent: 2em;
            margin-bottom: 0px;
        }

        p {
            text-align: left;
            text-indent: 2em;
            font-size: 1em;
            color: #000;
        }

        ul {
            list-style-type: disc;
            margin-left: 20px;
        }

        /* 固定目录区域 */
        .toc {
            position: fixed;
            top: 20px;
            left: 20px;
            background-color: #e9ecef;
            padding: 15px;
            width: 180px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            height: calc(100vh - 40px);
            /* 使目录高度填满整个页面 */
            overflow-y: auto;
            /* 允许目录滚动 */
        }

        .toc h2 {
            font-size: 1.4em;
        }

        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }

        .toc li {
            margin: 10px 0;
        }

        .toc a {
            text-decoration: none;
            color: #007bff;
        }

        img {
            display: block;
            margin: auto;
        }

        footer {
            background-color: #f4f4f4;
            color: white;
            padding: 10px;
            text-align: center;
            position: fixed;
            width: 100%;
            bottom: 0;
        }
    </style>
</head>

<body>

    <!-- <header>
    <h1>软件说明文档</h1>
    <p>欢迎使用我们的软件！</p>
</header> -->

    <div class="toc">
        <h2>目录</h2>
        <ul>
            <li><a href="#intro">1. 软件简介</a></li>
            <li><a href="#platform">2. 运行环境</a></li>
            <li><a href="#usage">3. 开始使用</a>
                <ul><li><a href="#usage_1">3.1 使用软件前期准备</a></li>
                    <li><a href="#usage_2">3.2 查看广播设备信息</a></li>
                    <li><a href="#usage_3">3.3 连接设备</a></li>
                    <li><a href="#usage_4">3.4 服务、特征调试</a></li>
                </ul>
            </li>
            <li><a href="#features">4. 软件功能简介</a>
                <ul><li><a href="#features_1">4.1 界面总览</a></li>
                    <li><a href="#features_2">4.2 设备列表</a></li>
                    <li><a href="#features_3">4.3 服务/特征调试</a></li>
                    <li><a href="#features_4">4.4 自动连接管理</a></li>
                    <li><a href="#features_5">4.5 输出窗口</a></li>
                </ul>
            </li>
            <li><a href="#faq">5. 常见问题解答</a></li>
        </ul>
    </div>

    <div class="content">
        <section id="intro">
            <h2>1. 软件简介</h2>
            <p>BLEDebug 是一款 Windows 平台下的低功耗蓝牙调试工具软件，软件支持以下功能：</p>
            <ul>
                <li>BLE 广播设备扫描、广播信息查看</li>
                <li>BLE 设备连接、设备配对</li>
                <li>设备特征、服务信息展示及特征调试</li>
                <li>广播设备自动连接</li>
                <li>调试日志输出</li>
            </ul>
        </section>

        <section id="platform">
            <h2>2. 运行环境</h2>
            <p>软件运行环境需要 Windows 10 及以上系统，且具有蓝牙适配器的 PC。</p>
        </section>

        <section id="usage">
            <h2>3. 开始使用</h2>
            <h3 id="usage_1">3.1 使用软件前期准备</h3>
            <p>请先确保电脑端有蓝牙适配器，且如下图所示在系统设置中开启蓝牙功能。</p>
            <img src="document.files/开启系统蓝牙.png" alt="开启蓝牙" width="600" />
            <h3 id="usage_2">3.2 查看广播设备信息</h3>
            <p>软件打开后会自动扫描当前射频信号覆盖区域内的蓝牙广播信号。
                BLEDebug 通过扫描到的广播包类型判断设备是否可连接，
                如果没有扫描到可连接类型的广播包（比如 ConnectableUndirected 类型），
                设备列表中不会显示 “连接”、“配对” 按钮。
            </p>
            <p>点击设备列表中的设备时，列表下方显示该设备的广播数据信息；
                广播数据以表格的形式分类型进行展示，如果有多种类型的广播包，则会显示多张表格信息。</p>
            <h3 id="usage_3">3.3 连接设备</h3>
            <p>点击“连接”按钮，右侧的服务、特征调试窗口会显示该设备的连接状态。
                在成功连接到蓝牙设备之后，将会自动枚举 Service 与每个 Service 包含的 Characteristics 信息，
                并获取 MTU 大小，如下图所示：</p>
            <img src="document.files/连接设备.png" alt="连接设备" width="600" style="display: block; margin: auto;" />
            <p>GATT 信息以树形结构展示，双击操作可以展开/收缩 Service 条目。
                在 Characteristics 条目的右侧通过不同的图标标识该条目支持的读、写和通知操作。
                在这里，您可以看到设备的全部功能并与其功能进行交互。
            </p>
            <p>BLEDebug 支持多设备连接调试，当连接多个设备时，在服务、特征调试窗口会出现多个 Tab 页面，对应不同的设备。
                此时，点击窗口上方的图标可以分栏显示设备信息。 
            </p>
            <h3 id="usage_4">3.4 服务、特征调试</h3>
            <p>在 GATT 信息树中，选中待调试的目标 Characteristics，在其右侧弹出相应的调试窗口。</p>
            <p>这里以“OPPO Enco Free3”这个设备为例，
                连接之后可以看到设备支持很多服务，
                有具体名称的是蓝牙联盟定义的，
                自定义的显示为 Unknown Service。
                点开某个具体的服务，
                案例点开的是 “Published Audio Capabilities Service”，
                点开某个具体的特征值，这个特征值的属性写了读，写和通知，对应右侧的读写和通知。
                可在这个窗口读取特征值，写入数据，或者订阅通知。
                读取及写入的数据均支持HEX形式与ASCII码形式，
                发送/读取的数据字节及实时速度都会自动更新。
            </p>
            <img src="document.files/特征调试.png" alt="特征调试" width="600" />
            <p>写入数据时，支持单次发送数据，连续发送数据，
                编辑框内填写的是间隔时间，支出文件发送（需要手动选择待发送的文件）。
                发送与读取数据到的数据在输出窗口的日志信息均有记录，方便查阅。</p>
        </section>

        <section id="features">
            <h2>4. 软件功能简介</h2>
            <h3 id="features_1">4.1 界面总览</h3>
            <img src="document.files/界面总览.png" alt="界面总览" width="600" />
            <p>整个界面主体分为三个部分：分别是设备列表及设备信息，服务/特征调试及输出窗口。</p>
            <ul>
                <li>设备列表及设备信息：设备列表展示了附近扫描到的蓝牙设备，并展示了他们的名称，MAC地址和RSSI信息(动态)。同时可以按名称和MAC地址过滤设备，支持按MAC地址和RSSI强度重新排列设备列表。
                </li>
                <li>服务/特征调试：展示连接成功的蓝牙设备的服务与特征。
                </li>
                <li>输出窗口：输出窗口可以看见操作过程的具体步骤，包括设备的连接与断开的信息，设备读取与写入的数据，是否开启推送。</li>
            </ul>
            <h3 id="features_2">4.2 设备列表</h3>
            <p>通过广播设备列表，您可以发现、连接和探索您附近的 BLE 外设。</p>
                <img src="document.files/设备列表.png" alt="设备列表" width="300" />
            <p>软件打开后自动扫描周围环境中的 BLE 外设。在设备列表中动态显示扫描结果，每个列表条目包含：</p>
            <ol style="text-indent: 2em;" >
                <li>设备名称（如果可用）</li>
                <li>MAC 地址，唯一标识符</li>
                <li>信号强度指示</li>
                <li>广播间隔指示</li>
            </ol>
            <p><b>设备名称</b>
                来源于设备广播中的 Local Name 数据，如果没有扫描到相关数据，则显示 N/A。</p> 
            <p><b>信号强度指示</b>
                信号强度即 RSSI 值，表示为以 dBm 为单位的负值。可以用来指示外设备与主机间的距离。越接近 0 的值表示信号越强。</p>
            <p>在 BLEDebug 的设备列表中，介于 0 到 -50 dBm 之间的值图标表示满格，从 -50 dBm 到 -70 dBm 的值表示次之。
                图标显示 2 格时表示 RSSI 在 -70 dBm 和 -80 dBm 之间。图标显示 1 格时表示 RSSI 不足 -80 dBm </p> 
            <p><b>广播间隔指示</b>表示了扫描到的同一设备的连续两个广播包之间的时间间隔，间隔以毫秒为单位</p> 
            <p>默认情况下按扫描到的先后顺序进行排序，可连接的设备优先排序，有local name的设备优先排序。此外，还支持按MAC地址和RSSI强度重新排列设备列表，支持按名称/MAC地址过滤设备。</p>
            <p>对设备列表显示的设备信息有怀疑时，比如怀疑设备在范围内但未显示或者希望设备列表显示附近BLE设备的最新信息，可以点击“停止扫描”按钮，然后手动点击“开始扫描”按钮以达到手动刷新设备列表的效果。</p>
            <h4>广播数据窗口</h4>
            <p>在列表中点击某条目，会在列表下方弹出该条目对应的广播数据，如下图所示。</p> 
                <img src="document.files/广播数据信息.png" alt="广播数据信息" width="300"/>
                <p>广播数据以表格的形式分类型进行展示，如果有多种类型的广播包，则会显示多张表格信息。
                    以上图为例，从中可以看出该设备发送了 CoonnectableUndirected 和 ScanResponse 两种类型的广播包，以及不同广播包中包含的具体数据。</p>
            
            <h4>已绑定设备查看</h4>
            <p>点击列表上方的 Bonded 可以查看已绑定的设备。</p>

            <h3 id="features_3">4.3 服务/特征调试</h3>
            <p>连接设备后，在成功连接到蓝牙设备之后，将会自动枚举 Service 与每个 Service 包含的 Characteristics 信息。
                在服务/特征调试窗口中展示 GATT 信息。用户可以读取、写入和订阅不同特征的通知或指示，从而实现全面的设备分析和交互。</p>
            <img src="document.files/特征调试.png" alt="特征调试" width="600" />
            <p>对于 BLE 设备，每个设备可以有若干个服务，每个服务下又有若干个特征。
                可以把特征看成是一个带有属性的参数，有的参数可以被读取，有的可以被写入，有的可以同时被读写，还有一些支持推送服务。</p>
            <p>当读取并选中设备对应的服务和特征后，可以根据特征类型进行访问，如果是特征是可读的，则可以点击Read按钮，
                如果该服务特征支持 Notify ，在订阅之后，若该特征对应的参数的值发生变化，收到的数据最终同主动读取的数据一样都会被显示在数据区。</p>
            <p>在写入特征时，一般有两种属性，分别为“Write”与“WriteWithoutResponse"，前者需要设备确认收到数据，后者无需确认，所以写入更快，但在写入时需要勾选”Write No Response“选项。</p>
            <p>在服务/特征的最右侧，有一个星形⭐图标，点击该图标可以标记相应的服务/特征。
                在 GATT 树形信息上方的复选框中勾选“Only favourite”选项后，可以快速查看到标记过的服务/特征。</p>
            <h3 id="features_4">4.4 自动连接管理</h3>
            <p> BLEDebug 支持设备自动连接功能，在菜单栏的设备中选择自动连接管理，如下图所示。
                在自动连接管理窗口中，支持将指定的 MAC 地址设置为自动连接，并查看已设置为自动连接的设备的 MAC 地址。
                将 MAC 地址设置为自动连接，也可以在广播设备列表中通过右键添加。
            </p>
            <p> 如果软件在扫描 BLE 设备期间发现指定设备的广播，将会自动发起连接。</p>
            <img src="document.files/自动连接.png" alt="自动连接" width="300" />
            <h3 id="features_5">4.5 输出窗口</h3>
            <img src="document.files/输出窗口.png" alt="输出窗口" width="600" />
            <p>输出窗口用来记录和查看操作日志和输出相关信息，包括用户操作，设备状态，以及收发的数据。</p>
            <p>输出窗口支持导出、清空和自动滚屏功能，可以通过窗口上方的按钮实现。</p>
            <p>通过输出窗口上方有下拉框对，可以对输出信息的类型进行筛选，默认情况下是显示全部(All)。
                比如在只关注数据收发结果的情况下，可以切换到 DATA 模式，此时，操作相关的信息将不会输出。</p>
        </section>

        <section id="faq">
            <h2>5. 常见问题解答</h2>
            <p>以下是一些常见问题及解答：</p>
            <ul>
                <li><strong>问题 1: 扫描不到 BLE 设备</strong>
                        <br>解答: 可能有一下原因导致扫描不到设备：
                        <ol><li>蓝牙适配器未启用或驱动过旧。</li>
                        <li>目标设备未进入广播模式。</li>
                        <li>Windows蓝牙服务异常。</li>
                        </ol>
                解决办法:
                        <ol>
                            <li>检查蓝牙状态：进入设置 —— 蓝牙和其他设备，确保蓝牙开关为“开”。 在设备管理器中确认蓝牙适配器无感叹号或错误提示。</li>
                            <li>重启蓝牙服务：按 Win + R 输入 services.msc，找到蓝牙支持服务，右键选择“重启”。</li>
                            <li>验证设备广播：使用其他平台的BLE工具，确认目标设备是否可见。</li>
                        </ol>
                </li>
                <li><strong>问题 2: 扫描不到 BLE Extended 广播</strong>
                    <br>解答: Windows 系统从 Windows 10 Build 19041 版本开始支持 BLE 5.0 扩展广播格式。如果您的系统版本低于 19041 则不支持扩展广播数据。
                </li>
                <li><strong>问题 3:为什么扫描到的很多设备没有连接按钮？</strong>
                    <br> 解答: BLEDebug 通过扫描到的广播包类型判断设备是否可连接，如果没有扫描到可连接类型的广播包，比如 ConnectableUndirected 类型，设备列表中不会显示连接按钮。
                </li>
            </ul>
        </section>
    </div>
</body>

</html>