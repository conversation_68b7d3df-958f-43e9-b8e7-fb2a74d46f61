@echo off
echo 正在复制STM32F4xx官方库文件...

REM 设置源路径
set SRC_CMSIS=..\..\..\..\STM32F4xx_DSP_StdPeriph_Lib_V1.9.0\Libraries\CMSIS
set SRC_STDPERIPH=..\..\..\..\STM32F4xx_DSP_StdPeriph_Lib_V1.9.0\Libraries\STM32F4xx_StdPeriph_Driver

REM 创建目标目录
if not exist "Library" mkdir Library
if not exist "System" mkdir System
if not exist "Startup" mkdir Startup

echo 1. 复制CMSIS核心文件...
copy "%SRC_CMSIS%\Include\core_cm4.h" "Library\"
copy "%SRC_CMSIS%\Include\core_cmFunc.h" "Library\"
copy "%SRC_CMSIS%\Include\core_cmInstr.h" "Library\"
copy "%SRC_CMSIS%\Include\core_cmSimd.h" "Library\"

echo 2. 复制STM32F4xx设备文件...
copy "%SRC_CMSIS%\Device\ST\STM32F4xx\Include\stm32f4xx.h" "Library\"
copy "%SRC_CMSIS%\Device\ST\STM32F4xx\Include\system_stm32f4xx.h" "System\"
copy "%SRC_CMSIS%\Device\ST\STM32F4xx\Source\Templates\system_stm32f4xx.c" "System\"

echo 3. 复制启动文件...
copy "%SRC_CMSIS%\Device\ST\STM32F4xx\Source\Templates\arm\startup_stm32f40_41xxx.s" "Startup\"

echo 4. 复制标准外设库头文件...
copy "%SRC_STDPERIPH%\inc\stm32f4xx_rcc.h" "Library\"
copy "%SRC_STDPERIPH%\inc\stm32f4xx_gpio.h" "Library\"
copy "%SRC_STDPERIPH%\inc\stm32f4xx_usart.h" "Library\"
copy "%SRC_STDPERIPH%\inc\stm32f4xx_flash.h" "Library\"
copy "%SRC_STDPERIPH%\inc\misc.h" "Library\"

echo 5. 复制标准外设库源文件...
copy "%SRC_STDPERIPH%\src\stm32f4xx_rcc.c" "Library\"
copy "%SRC_STDPERIPH%\src\stm32f4xx_gpio.c" "Library\"
copy "%SRC_STDPERIPH%\src\stm32f4xx_usart.c" "Library\"
copy "%SRC_STDPERIPH%\src\stm32f4xx_flash.c" "Library\"
copy "%SRC_STDPERIPH%\src\misc.c" "Library\"

echo 文件复制完成！
pause
