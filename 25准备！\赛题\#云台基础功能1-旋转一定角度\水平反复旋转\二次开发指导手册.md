# STM32F407双路步进电机控制系统 - 二次开发指导手册

## 快速开始

### 开发环境要求
- **IDE**: Keil µVision 5 (推荐最新版本)
- **编译器**: ARM Compiler 5/6
- **调试器**: ST-Link V2/V3 或 J-Link
- **硬件**: STM32F407ZGT6开发板 + D36A驱动器 + 42步进电机×2

### 项目导入步骤
1. **打开项目**: 双击 `USER/Template.uvprojx`
2. **配置目标**: 确认芯片型号为 `STM32F407ZGT6`
3. **编译项目**: 按 `F7` 或点击编译按钮
4. **下载程序**: 按 `F8` 或点击下载按钮
5. **查看输出**: 串口助手连接PA9/PA10，115200波特率

### 5分钟验证
```bash
# 1. 连接硬件（参考双路电机接线说明.md）
# 2. 编译下载程序
# 3. 打开串口助手，应该看到：
"STM32F407 Dual Stepper Motor Control System Started"
"Current Voltage: 24.XX V (ADC: XXXX)"
```

## 代码架构导览

### 目录结构说明
```
项目根目录/
├── USER/              # 用户应用层
│   ├── main.c         # 主程序入口 ⭐
│   ├── stm32f4xx_it.c # 中断服务程序
│   └── *.uvprojx      # Keil项目文件
│
├── HARDWARE/          # 硬件驱动层 ⭐
│   ├── ATD5984/       # 步进电机驱动 (核心)
│   ├── ADC/           # 电压监测
│   ├── KEY/           # 按键输入
│   └── TIM/           # 定时器PWM
│
├── SYSTEM/            # 系统服务层
│   ├── delay/         # 延时函数
│   ├── sys/           # 系统配置
│   └── usart/         # 串口通信
│
└── FWLIB/             # STM32标准库
    ├── inc/           # 头文件
    └── src/           # 源文件
```

### 核心文件解读

#### 1. main.c - 主程序流程
```c
int main(void)
{
    /* ===== 系统初始化 ===== */
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    delay_init(168);
    
    /* ===== 外设初始化 ===== */
    Key_Init();                  // 按键
    ADC1_Init();                // ADC电压检测
    uart_init(115200);          // 串口通信
    
    /* ===== 电机系统初始化 ===== */
    ATD5984_Init();             // 驱动器控制引脚
    STEP12_PWM_Init(7199, 6);   // PWM脉冲生成
    
    /* ===== 定时任务 ===== */
    TIM2_Init(199, 7199);       // 10ms周期中断
    
    /* ===== 主循环 ===== */
    while(1) {
        // ADC采集 → 电压计算 → 串口输出
        adc_val = Get_adc_Average(10, 5);
        voltage = (float)(adc_val * 3.3 * 11 / 4096);
        printf("Current Voltage: %.2f V\\r\\n", voltage);
        delay_ms(100);
    }
}
```

#### 2. ATD5984.c - 电机控制核心
```c
// 初始化函数 - 配置控制引脚
void ATD5984_Init(void);

// PWM频率配置
void STEP12_PWM_Init(u16 arr, u16 psc);

// 电机控制宏定义 (预留扩展)
#define MOTOR_A_STEP_HIGH()  // 电机A步进脉冲
#define MOTOR_A_DIR_CW()     // 电机A顺时针
#define MOTOR_A_ENABLE()     // 电机A使能
```

## 二次开发指南

### Level 1: 基础功能扩展

#### 1.1 添加电机控制函数
```c
// 在ATD5984.c中添加
void Motor_A_Step(u32 steps, u8 direction) {
    PCout(13) = direction;  // 设置方向
    PDout(2) = 0;           // 使能电机
    
    for(u32 i = 0; i < steps; i++) {
        // PWM已自动产生脉冲
        delay_us(300);  // 控制转速
    }
    
    PDout(2) = 1;  // 关闭电机
}

void Motor_B_Step(u32 steps, u8 direction) {
    PBout(12) = direction; // 设置方向
    PCout(12) = 0;         // 使能电机
    
    for(u32 i = 0; i < steps; i++) {
        delay_us(300);
    }
    
    PCout(12) = 1; // 关闭电机
}
```

#### 1.2 添加按键控制逻辑
```c
// 在main.c主循环中添加
if(PBin(14) == 0) {  // 按键按下
    delay_ms(20);    // 消抖
    if(PBin(14) == 0) {
        Motor_A_Step(3200, 1);  // 电机A转一圈
        while(PBin(14) == 0);   // 等待释放
    }
}
```

### Level 2: 中级功能开发

#### 2.1 PID控制算法集成
```c
// PID控制结构体
typedef struct {
    float Kp, Ki, Kd;
    float error, last_error, integral;
    float output;
} PID_TypeDef;

// PID计算函数
float PID_Calculate(PID_TypeDef *pid, float target, float current) {
    pid->error = target - current;
    pid->integral += pid->error;
    
    pid->output = pid->Kp * pid->error + 
                  pid->Ki * pid->integral + 
                  pid->Kd * (pid->error - pid->last_error);
    
    pid->last_error = pid->error;
    return pid->output;
}
```

#### 2.2 坐标控制系统
```c
// 坐标结构体
typedef struct {
    float x, y;     // 目标坐标
    u32 steps_x, steps_y;  // 对应步数
} Coordinate_TypeDef;

// 坐标转步数函数
void Coordinate_To_Steps(Coordinate_TypeDef *coord) {
    // 根据机械结构计算
    coord->steps_x = (u32)(coord->x * STEPS_PER_MM);
    coord->steps_y = (u32)(coord->y * STEPS_PER_MM);
}
```

### Level 3: 高级系统集成

#### 3.1 K230视觉模块接口
```c
// 串口数据解析结构体
typedef struct {
    u8 header[2];   // 0x55, 0xAA
    s16 x_offset;   // X轴偏移
    s16 y_offset;   // Y轴偏移
    u8 checksum;    // 校验和
} Vision_Data_TypeDef;

// 视觉数据处理函数
void Process_Vision_Data(Vision_Data_TypeDef *data) {
    if(data->header[0] == 0x55 && data->header[1] == 0xAA) {
        // 计算需要的步数
        s32 steps_x = data->x_offset * PIXEL_TO_STEPS;
        s32 steps_y = data->y_offset * PIXEL_TO_STEPS;
        
        // 执行电机控制
        if(steps_x > 0) Motor_A_Step(abs(steps_x), 1);
        else Motor_A_Step(abs(steps_x), 0);
        
        if(steps_y > 0) Motor_B_Step(abs(steps_y), 1);
        else Motor_B_Step(abs(steps_y), 0);
    }
}
```

#### 3.2 状态机控制框架
```c
// 系统状态枚举
typedef enum {
    STATE_IDLE,         // 空闲状态
    STATE_MANUAL,       // 手动控制
    STATE_AUTO_TRACK,   // 自动追踪
    STATE_CALIBRATION   // 校准模式
} System_State_TypeDef;

// 状态机处理函数
void State_Machine_Process(void) {
    static System_State_TypeDef current_state = STATE_IDLE;
    
    switch(current_state) {
        case STATE_IDLE:
            // 等待指令
            break;
        case STATE_MANUAL:
            // 手动控制逻辑
            break;
        case STATE_AUTO_TRACK:
            // 自动追踪逻辑
            break;
        case STATE_CALIBRATION:
            // 校准逻辑
            break;
    }
}
```

## 常用开发技巧

### 调试技巧

#### 1. 串口调试信息
```c
// 添加调试宏
#define DEBUG_PRINT(fmt, ...) printf("[DEBUG] " fmt "\\r\\n", ##__VA_ARGS__)
#define ERROR_PRINT(fmt, ...) printf("[ERROR] " fmt "\\r\\n", ##__VA_ARGS__)

// 使用示例
DEBUG_PRINT("Motor A steps: %d", steps);
ERROR_PRINT("ADC read failed: %d", error_code);
```

#### 2. GPIO状态监测
```c
// 添加状态读取函数
void Print_GPIO_Status(void) {
    printf("Motor A: DIR=%d, SLEEP=%d\\r\\n", PCin(13), PDin(2));
    printf("Motor B: DIR=%d, SLEEP=%d\\r\\n", PBin(12), PCin(12));
}
```

#### 3. PWM参数调试
```c
// PWM频率在线调整
void Adjust_PWM_Frequency(u16 new_arr, u16 new_psc) {
    TIM8->ARR = new_arr;
    TIM8->PSC = new_psc;
    printf("PWM updated: ARR=%d, PSC=%d\\r\\n", new_arr, new_psc);
}
```

### 性能优化

#### 1. 中断优化
```c
// TIM2中断处理 - 系统任务调度
void TIM2_IRQHandler(void) {
    if(TIM_GetITStatus(TIM2, TIM_IT_Update) != RESET) {
        // 10ms任务
        static u8 task_counter = 0;
        task_counter++;
        
        if(task_counter >= 10) {  // 100ms任务
            task_counter = 0;
            // 执行低频任务
        }
        
        TIM_ClearITPendingBit(TIM2, TIM_IT_Update);
    }
}
```

#### 2. 内存管理
```c
// 使用静态内存避免碎片化
static u8 uart_buffer[256];
static Vision_Data_TypeDef vision_data;
static PID_TypeDef pid_x, pid_y;
```

## 扩展模块集成

### 1. 激光器控制模块
```c
// 激光器控制引脚定义
#define LASER_PIN    PAout(1)
#define LASER_ON()   LASER_PIN = 1
#define LASER_OFF()  LASER_PIN = 0

// PWM调光控制
void Laser_PWM_Init(void) {
    // 配置TIM3_CH2 (PA7) 用于激光器PWM调光
}
```

### 2. 串口屏显示模块  
```c
// TJC串口屏指令格式
void TJC_Send_Command(char *cmd) {
    printf("%s", cmd);
    printf("%c%c%c", 0xFF, 0xFF, 0xFF);  // 结束符
}

// 更新显示数据
void Update_Display(float voltage, u32 steps_x, u32 steps_y) {
    char cmd[64];
    sprintf(cmd, "t0.txt=\"Voltage:%.2fV\"", voltage);
    TJC_Send_Command(cmd);
}
```

### 3. 限位开关检测
```c
// 限位开关引脚定义
#define LIMIT_X_MIN  PEin(0)
#define LIMIT_X_MAX  PEin(1)
#define LIMIT_Y_MIN  PEin(2)  
#define LIMIT_Y_MAX  PEin(3)

// 安全检查函数
u8 Check_Limits(u8 motor, u8 direction) {
    if(motor == MOTOR_A) {
        if(direction == CW && LIMIT_X_MAX == 0) return 0;
        if(direction == CCW && LIMIT_X_MIN == 0) return 0;
    }
    return 1;  // 安全
}
```

## 常见问题解决

### 问题1: 电机不转或抖动
**检查清单**:
- ✅ 供电电压 (推荐24V)
- ✅ 电机线序 (A+红, A-蓝, B+绿, B-黑)
- ✅ PWM频率和占空比
- ✅ SLEEP引脚使能逻辑

### 问题2: 串口无输出
**解决步骤**:
```c
// 1. 检查波特率匹配 (115200)
// 2. 确认引脚连接 (PA9-TX, PA10-RX)
// 3. 验证printf重定向
int fputc(int ch, FILE *f) {
    while((USART1->SR&0X40)==0);
    USART1->DR = (u8) ch;
    return ch;
}
```

### 问题3: 编译错误
**常见问题**:
- 头文件路径配置
- 宏定义冲突
- 库版本不匹配

## 项目扩展建议

### 短期目标 (1-2周)
1. **手动控制界面**: 按键或串口指令控制电机
2. **限位保护**: 添加限位开关检测
3. **速度控制**: 可调PWM频率实现变速

### 中期目标 (1个月)
1. **K230集成**: 视觉追踪功能
2. **PID控制**: 激光点精确回中
3. **显示界面**: 串口屏实时状态

### 长期目标 (竞赛前)
1. **自动瞄准**: 完整的追踪瞄准系统
2. **多目标**: 目标切换和优先级
3. **用户界面**: 友好的操作界面

## 开发资源

### 参考文档
- `项目技术总结.md` - 详细技术分析
- `双路电机接线说明.md` - 硬件连接指南
- STM32F4xx中文参考手册
- D36A驱动器用户手册

### 有用的调试工具
- **串口助手**: SSCOM、SecureCRT
- **逻辑分析仪**: 分析PWM和通信信号
- **示波器**: 检查PWM波形质量
- **万用表**: 电压和电流测试

### 在线资源
- STM32官方文档中心
- 正点原子开发板例程
- K230技术文档
- 电子设计竞赛资料

---

**编写者**: 基于实际开发经验整理  
**适用版本**: v1.0 成功驱动版本  
**更新时间**: 2025-08-01  
**难度等级**: 初级 → 中级 → 高级  

**祝您开发顺利！如有问题，可参考项目技术总结文档或联系原开发团队。**