# 水平电机校准与控制系统 v2.0

## 功能概述
本程序实现水平电机(电机A)的完整校准和控制系统：

### 🔧 校准功能
1. **方向控制测试**：验证DIR信号极性，确保顺时针/逆时针正确
2. **步数校准测试**：360°旋转测试，校准STEPS_PER_DEGREE参数
3. **细分模式检测**：验证驱动器实际细分设置

### 🎯 目标动作序列
1. **顺时针旋转90°**
2. **逆时针旋转188°**  
3. **顺时针旋转90°**
4. **停止**

## 主要修改内容

### 1. ATD5984.h头文件修改
- 简化为只支持水平电机A
- 添加角度控制宏定义：
  - `STEPS_PER_DEGREE = 8.89f` (步/度)
  - `DIR_CW = 0` (顺时针)
  - `DIR_CCW = 1` (逆时针)
- 新增函数声明：`Motor_A_Rotate(float angle)`

### 2. ATD5984.c驱动文件修改
- 简化初始化函数，只配置电机A相关引脚
- 实现`Motor_A_Rotate()`函数，支持角度控制：
  - 自动计算步数和方向
  - 内置延时确保动作完成
  - PWM频率1600Hz (中速平衡)
  - 完整的使能/禁用序列

### 3. main.c主程序大幅修改
- **删除功能**：ADC电压监测、按键检测、定时器中断、测试状态机
- **保留功能**：基础系统初始化、串口调试输出
- **新增功能**：线性执行固定动作序列
  - 系统稳定等待(2秒)
  - 三步旋转序列执行
  - 动作间隔(1秒)
  - 完成后进入空闲循环

### 4. sys.h系统头文件简化
- 删除不必要的头文件包含：adc.h, KEY.h, TIM.h
- 只保留核心功能：delay.h, usart.h, ATD5984.h

## 技术参数

### 步进控制参数
- **电机类型**: 42步进电机 + D36A驱动器
- **细分设置**: 1/16细分
- **步进精度**: 0.1125°/步 (8.89步/度)
- **PWM频率**: 1600Hz (平衡精度和速度)
- **占空比**: 50%

### 动作序列参数 (修正后)
- **STEPS_PER_DEGREE**: 13.1步/度 (基于实测校准)
- **动作1**: 顺时针90° = 1179步 ≈ 737ms
- **动作2**: 逆时针188° = 2463步 ≈ 1539ms  
- **动作3**: 顺时针90° = 1179步 ≈ 737ms
- **总耗时**: 约9秒(含校准测试和间隔)

### 🚨 问题修复
1. **方向控制修复**：
   - 原问题：三段都是顺时针旋转
   - 修复：DIR_CW=1, DIR_CCW=0 (信号极性修正)
   
2. **步数精度修复**：
   - 原问题：实测250°，期望368°，精度偏差32%
   - 修复：STEPS_PER_DEGREE从8.89调整为13.1 (增加47%)
   
3. **校准功能增加**：
   - 方向测试：Motor_A_DirectionTest()
   - 步数校准：Motor_A_StepsCalibration()
   - 细分模式参考表

### 引脚分配
- **STEP信号**: PC8 (TIM8_CH3 PWM输出)
- **DIR信号**: PC13 (GPIO输出，方向控制)
- **SLEEP信号**: PD2 (GPIO输出，使能控制)

## 编译和使用

### 编译环境
- **IDE**: Keil µVision 5
- **工程文件**: `USER/Template.uvprojx`
- **清理命令**: 运行`keilkilll.bat`
- **输出文件**: `OBJ/Template.hex`

### 使用方法
1. 打开Keil工程文件
2. 编译生成hex文件
3. 烧录到STM32F407
4. 上电后自动执行动作序列
5. 通过串口(115200bps)观察调试信息

### 预期输出
```
STM32F407 Horizontal Motor Rotation Test
========================================
Action Sequence:
1. Clockwise 90°
2. Counter-Clockwise 188°
3. Clockwise 90°
4. Stop
========================================

System initializing...
Starting motor rotation sequence...

[Step 1/3] Motor A: CW rotation 90.0° (800 steps)
Motor A: Rotating for 500ms...
Motor A: Rotation completed

[Step 2/3] Motor A: CCW rotation 188.0° (1672 steps)
Motor A: Rotating for 1045ms...
Motor A: Rotation completed

[Step 3/3] Motor A: CW rotation 90.0° (800 steps)
Motor A: Rotating for 500ms...
Motor A: Rotation completed

========================================
Motor rotation sequence completed!
System entering idle state...
========================================
System idle - Motor stopped
```

## 注意事项
- 确保D36A驱动器正确配置：1/16细分，0.77A电流
- 步进电机接线按标准：A+红，A-蓝，B+绿，B-黑
- 供电电压建议24V，确保驱动力充足
- 程序上电即自动执行，无需外部触发