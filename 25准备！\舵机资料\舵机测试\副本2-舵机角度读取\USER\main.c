#include "sys.h"
#include "delay.h"
#include "usart.h"
#include "led.h"


//ALIENTEK 探索者STM32F407开发板 实验4
//串口屏控制舵机系统
//技术支持：www.openedv.com
//淘宝店铺：http://eboard.taobao.com
//广州市星翼电子科技有限公司  
//作者：正点原子 @ALIENTEK


int main(void)
{ 
	u16 refresh_counter = 0;  // 定时刷新计数器
	
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);//设置系统中断优先级分组2
	delay_init(168);		//延时初始化 
	uart_init(115200);		//串口1初始化，与舵机通信
	uart2_init(115200);		//串口2初始化，与串口屏通信
	LED_Init();		  		//初始化与LED连接的硬件接口  
	
	delay_ms(1000);			//等待系统稳定
	
	// 初始化舵机到中位状态
	Servo_LoadMotor(1);		//装载ID为1的舵机
	delay_ms(50);
	Servo_LoadMotor(2);		//装载ID为2的舵机  
	delay_ms(50);
	
	// 设置初始位置为中位 (500对应120度)
	Servo_MoveToPosition(1, 500, 1000);
	delay_ms(50);
	Servo_MoveToPosition(2, 500, 1000);
	delay_ms(1000);			//等待移动完成
	
	// 初始角度显示
	Screen_UpdateAngleDisplay();
	
	while(1)
	{
		// 处理串口屏命令
		Screen_ProcessCommand();
		
		// 定时刷新角度显示 - 每500ms刷新一次
		refresh_counter++;
		if(refresh_counter >= 500 && auto_refresh_enabled) {
			Screen_UpdateAngleDisplay();
			refresh_counter = 0;
		}
		
		// LED闪烁，指示系统运行
		LED0=!LED0;
		delay_ms(1);  // 1ms延迟，实现500次循环约500ms刷新间隔
	}
}
