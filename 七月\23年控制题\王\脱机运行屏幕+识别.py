# bilibili搜索学不会电磁场看教程
# 第六课，在以往的课程中，我们都是链接电脑来运行代码，这节课我们学习如何离线运行代码，这种情况我们需要一块屏幕来
# 查看运行情况
#
# 新增功能：黑色边框四个顶点检测 + 红色激光点质心计算 + 坐标映射
# 纸张规格：宽度100mm × 高度150mm，黑色边框18mm
# 实现思路：
# 1. 获取黑色边框四个角点 pt1, pt2, pt3, pt4 (用矩形检测)
# 2. 获取红色激光点的质心坐标 cx, cy
# 3. 将像素坐标映射为纸的实际坐标 (毫米)
# 使用方法：
# - 黑色边框检测：修改 BLACK_THRESH，可直接从IDE阈值编辑器复制灰度阈值
# - 红色激光点：修改 RED_THRESH，可直接从IDE阈值编辑器复制LAB阈值

import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms
from machine import UART, FPIOA

# 坐标映射函数：将像素坐标映射为纸的实际坐标
def pixel_to_paper_coord(pixel_x, pixel_y, rect_points, paper_width_mm=100, paper_height_mm=150):
    """
    将像素坐标映射为纸的实际坐标 (毫米)
    参数:
        pixel_x, pixel_y: 像素坐标
        rect_points: 黑色边框四个顶点 [(x1,y1), (x2,y2), (x3,y3), (x4,y4)]
        paper_width_mm: 纸张宽度 (默认100mm)
        paper_height_mm: 纸张高度 (默认150mm)
    返回:
        (paper_x_mm, paper_y_mm): 纸上的实际坐标 (毫米)
    """
    if not rect_points or len(rect_points) != 4:
        return None, None

    try:
        # 简化的双线性插值映射 (假设矩形接近规则四边形)
        # 获取边框的边界
        x_coords = [p[0] for p in rect_points]
        y_coords = [p[1] for p in rect_points]

        min_x, max_x = min(x_coords), max(x_coords)
        min_y, max_y = min(y_coords), max(y_coords)

        # 计算相对位置 (0-1)
        if max_x > min_x and max_y > min_y:
            rel_x = (pixel_x - min_x) / (max_x - min_x)
            rel_y = (pixel_y - min_y) / (max_y - min_y)

            # 映射到纸的实际坐标
            paper_x_mm = rel_x * paper_width_mm
            paper_y_mm = rel_y * paper_height_mm

            return round(paper_x_mm, 1), round(paper_y_mm, 1)
    except:
        pass

    return None, None

# 串口通信处理函数
def process_uart_command(uart, clock):
    """
    处理串口命令
    参数:
        uart: UART对象
        clock: 时钟对象
    """
    try:
        if uart.any():  # 检查是否有数据
            data = uart.readline()  # 读取一行数据
            if data:
                cmd = data.decode().strip()  # 解码并去除换行符
                if cmd.lower() == 'now':  # 检查是否为'now'命令
                    fps = clock.fps()  # 获取当前帧率
                    response = f"FPS:{fps:.1f}\r\n"  # 格式化响应
                    uart.write(response.encode())  # 发送响应
                    print(f"收到命令: {cmd}, 回复帧率: {fps:.1f}")  # 调试信息
    except Exception as e:
        print(f"串口处理错误: {e}")

sensor = None
uart = None

try:
    print("camera_test")

    # 初始化串口通信 (使用GH1.25-4P串口座)
    print("初始化串口...")
    fpioa = FPIOA()
    fpioa.set_function(11, fpioa.UART2_TXD)  # 设置GPIO11为UART2发送 (T引脚)
    fpioa.set_function(12, fpioa.UART2_RXD)  # 设置GPIO12为UART2接收 (R引脚)

    uart = UART(UART.UART2, baudrate=115200,
               bits=UART.EIGHTBITS,
               parity=UART.PARITY_NONE,
               stop=UART.STOPBITS_ONE)
    print("串口初始化完成，波特率: 115200")

    sensor = Sensor(width=640, height=640)
    sensor.reset()

    # 鼠标悬停在函数上可以查看允许接收的参数
    sensor.set_framesize(width=640, height=640)
    sensor.set_pixformat(Sensor.RGB565)

    Display.init(Display.ST7701, width=800, height=480, to_ide=True)
    # 初始化媒体管理器
    MediaManager.init()
    # 启动 sensor
    sensor.run()
    clock = time.clock()

    while True:
        clock.tick()
        os.exitpoint()
        img = sensor.snapshot(chn=CAM_CHN_ID_0)

        # 处理串口命令 (新增功能)
        process_uart_command(uart, clock)
#        # 绘制字符串，参数依次为：x, y, 字符高度，字符串，字符颜色（RGB三元组）
#        img.draw_string_advanced(50, 50, 80, "hello k230\n学不会电磁场", color=(255, 0, 0))

#        # 绘制直线，参数依次为：x1, y1, x2, y2, 颜色，线宽
#        img.draw_line(50, 50, 300, 130, color=(0, 255, 0), thickness=2)

#        # 绘制方框，参数依次为：x, y, w, h, 颜色，线宽，是否填充
#        img.draw_rectangle(1000, 50, 300, 200, color=(0, 0, 255), thickness=4, fill=False)

#        # 绘制关键点, 列表[(x, y, 旋转角度)]
#        img.draw_keypoints([[960, 540, 200]], color=(255, 255, 0), size=30, thickness=2, fill=False)

#        # 绘制圆, x, y, r
#        img.draw_circle(640, 640, 50, color=(255, 0, 255), thickness=2, fill=True)
#        # 精细的像素级操作，虽然说其实一般用不上，这里利用像素填充的方式绘制一个非常奇怪的方块吧
#        for i in range(1200, 1250):
#            for j in range(700, 750):
#                color =((i + j)%256, (i*j)%256, abs(i-j)%256)
#                img.set_pixel(i, j, color)

        # ===== 1. 获取黑色边框四个顶点 (使用矩形检测) =====
        # 黑色边框检测阈值 (可直接从IDE阈值编辑器复制)
        # 纸张规格：100mm×150mm，黑色边框18mm
        BLACK_THRESH = [(0, 50)]  # 从IDE阈值编辑器复制的灰度阈值

        # 使用矩形检测替代轮廓检测 (更兼容)
        img_edge = img.to_grayscale(copy=True)
        img_edge = img_edge.binary(BLACK_THRESH)  # 提取黑色区域(边框)

        # 查找矩形
        rects = img_edge.find_rects(threshold=1000)  # 可调整阈值

        rect_points = None  # 存储黑色边框四个顶点

        if rects:
            # 找到最大的矩形(假设是黑色边框)
            max_rect = max(rects, key=lambda r: r.w() * r.h())

            # 获取矩形的四个角点
            corners = max_rect.corners()
            rect_points = [(corner[0], corner[1]) for corner in corners]

            # 绘制黑色边框的四个顶点
            for i, point in enumerate(rect_points):
                x, y = point
                # 绘制顶点圆圈
                img.draw_circle(x, y, 8, color=(0, 255, 0), thickness=3, fill=False)
                # 标注顶点编号
                img.draw_string_advanced(x+10, y-10, 25, f"pt{i+1}", color=(0, 255, 0))

            # 绘制黑色边框连线
            for i in range(4):
                x1, y1 = rect_points[i]
                x2, y2 = rect_points[(i+1) % 4]
                img.draw_line(x1, y1, x2, y2, color=(0, 255, 0), thickness=3)

                # 显示四个顶点坐标
                coord_text = f"pt1:{rect_points[0]} pt2:{rect_points[1]}"
                img.draw_string_advanced(10, 150, 20, coord_text, color=(0, 255, 0))
                coord_text2 = f"pt3:{rect_points[2]} pt4:{rect_points[3]}"
                img.draw_string_advanced(10, 180, 20, coord_text2, color=(0, 255, 0))

        # ===== 2. 获取红色激光点质心 =====
        # 红色激光点检测阈值 (可直接从IDE阈值编辑器复制)
        RED_THRESH = [(30, 100, 15, 127, 15, 127)]  # 从IDE阈值编辑器复制的LAB阈值

        # 查找红色区域
        red_blobs = img.find_blobs(RED_THRESH,
                                  pixels_threshold=50,    # 最小像素数 (可调整)
                                  area_threshold=20,      # 最小面积 (可调整)
                                  merge=True)

        laser_center = None  # 存储激光点质心坐标

        if red_blobs:
            # 找到最大的红色区域(激光点)
            max_blob = max(red_blobs, key=lambda b: b.pixels())

            # 计算质心坐标
            cx, cy = max_blob.cx(), max_blob.cy()
            laser_center = (cx, cy)

            # 绘制激光点边界框
            img.draw_rectangle(max_blob.rect(), color=(255, 0, 0), thickness=2, fill=False)

            # 绘制质心十字标记
            img.draw_line(cx-15, cy, cx+15, cy, color=(255, 255, 255), thickness=3)
            img.draw_line(cx, cy-15, cx, cy+15, color=(255, 255, 255), thickness=3)

            # 绘制质心圆点
            img.draw_circle(cx, cy, 5, color=(255, 255, 255), thickness=2, fill=True)

            # 显示激光点质心坐标
            img.draw_string_advanced(cx+20, cy-15, 25, f"cx:{cx}, cy:{cy}", color=(255, 255, 255))

            # 坐标映射：像素坐标 → 纸的实际坐标
            if rect_points:
                try:
                    paper_x, paper_y = pixel_to_paper_coord(cx, cy, rect_points)
                    if paper_x is not None and paper_y is not None:
                        # 显示纸上的实际坐标
                        img.draw_string_advanced(10, 210, 20, f"Paper: ({paper_x}, {paper_y})mm", color=(255, 255, 0))
                        # 在激光点旁边显示实际坐标
                        img.draw_string_advanced(cx+20, cy-40, 20, f"({paper_x},{paper_y})mm", color=(255, 255, 0))
                    else:
                        img.draw_string_advanced(10, 210, 20, f"Laser: ({cx}, {cy}) pixels", color=(255, 0, 0))
                except Exception as e:
                    img.draw_string_advanced(10, 210, 20, f"Laser: ({cx}, {cy}) pixels", color=(255, 0, 0))
            else:
                img.draw_string_advanced(10, 210, 20, f"Laser: ({cx}, {cy}) pixels", color=(255, 0, 0))

        # 显示检测状态
        status_y = 10
        if rect_points:
            img.draw_string_advanced(10, status_y, 18, f"Frame: Found", color=(0, 255, 0))
        else:
            img.draw_string_advanced(10, status_y, 18, f"Frame: Not Found", color=(255, 0, 0))

        status_y += 25
        if laser_center:
            img.draw_string_advanced(10, status_y, 18, f"Laser: Found", color=(0, 255, 0))
        else:
            img.draw_string_advanced(10, status_y, 18, f"Laser: Not Found", color=(255, 0, 0))

        img.draw_string_advanced(50, 50, 80, "fps: {}".format(clock.fps()), color=(255, 0, 0))
        img.midpoint_pool(2, 2)
        img.compressed_for_ide()
        Display.show_image(img, x=(800-320)//2, y=(480-320)//2)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    if isinstance(sensor, Sensor):
        sensor.stop()
    if uart:
        uart.deinit()  # 关闭串口
        print("串口已关闭")
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
