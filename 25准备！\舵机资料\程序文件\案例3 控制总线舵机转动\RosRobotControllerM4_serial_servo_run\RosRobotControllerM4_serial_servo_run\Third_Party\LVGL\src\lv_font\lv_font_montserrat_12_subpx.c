#include "../../lvgl.h"

/*******************************************************************************
 * Size: 12 px
 * Bpp: 4
 * Opts: --no-compress --no-prefilter --bpp 4 --size 12 --font <PERSON>-Medium.ttf -r 0x20-0x7F,0xB0,0x2022 --font <PERSON>ont<PERSON>wesome5-Solid+Brands+Regular.woff -r 61441,61448,61451,61452,61452,61453,61457,61459,61461,61465,61468,61473,61478,61479,61480,61502,61512,61515,61516,61517,61521,61522,61523,61524,61543,61544,61550,61552,61553,61556,61559,61560,61561,61563,61587,61589,61636,61637,61639,61671,61674,61683,61724,61732,61787,61931,62016,62017,62018,62019,62020,62087,62099,62212,62189,62810,63426,63650 --format lvgl -o lv_font_montserrat_12_subpx.c --force-fast-kern-format
 ******************************************************************************/

#ifndef LV_FONT_MONTSERRAT_12_SUBPX
#define LV_FONT_MONTSERRAT_12_SUBPX 1
#endif

#if LV_FONT_MONTSERRAT_12_SUBPX

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t gylph_bitmap[] = {
    /* U+20 " " */

    /* U+21 "!" */
    0xf, 0x40, 0xf3, 0xf, 0x30, 0xf2, 0xe, 0x20,
    0xd1, 0x3, 0x0, 0x81, 0x1e, 0x30,

    /* U+22 "\"" */
    0x3c, 0x1e, 0x3b, 0xe, 0x3b, 0xe, 0x15, 0x7,

    /* U+23 "#" */
    0x0, 0x48, 0x3, 0xa0, 0x0, 0x6, 0x60, 0x58,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0x10, 0xa, 0x20,
    0x84, 0x0, 0x0, 0xc1, 0xa, 0x30, 0x0, 0xd,
    0x0, 0xb1, 0x0, 0xaf, 0xff, 0xff, 0xfb, 0x0,
    0x1c, 0x0, 0xd0, 0x0, 0x3, 0xa0, 0x1c, 0x0,
    0x0,

    /* U+24 "$" */
    0x0, 0x9, 0x20, 0x0, 0x0, 0x92, 0x0, 0x3,
    0xcf, 0xfb, 0x31, 0xf7, 0xa5, 0x74, 0x4e, 0x9,
    0x20, 0x1, 0xf9, 0xb2, 0x0, 0x2, 0xbf, 0xe8,
    0x0, 0x0, 0x97, 0xda, 0x0, 0x9, 0x24, 0xe5,
    0xb4, 0xa5, 0xba, 0x8, 0xef, 0xfa, 0x10, 0x0,
    0x92, 0x0, 0x0, 0x4, 0x10, 0x0,

    /* U+25 "%" */
    0xa, 0xc8, 0x0, 0xc, 0x10, 0x66, 0xa, 0x20,
    0x76, 0x0, 0x83, 0x7, 0x42, 0xc0, 0x0, 0x57,
    0xa, 0x2b, 0x20, 0x0, 0x9, 0xc6, 0x68, 0x5c,
    0x90, 0x0, 0x1, 0xc1, 0xc0, 0x67, 0x0, 0xa,
    0x43, 0x90, 0x2a, 0x0, 0x49, 0x1, 0xb0, 0x47,
    0x0, 0xc1, 0x0, 0x7b, 0xb1,

    /* U+26 "&" */
    0x0, 0x9e, 0xd4, 0x0, 0x0, 0x5c, 0x3, 0xd0,
    0x0, 0x4, 0xc0, 0x5c, 0x0, 0x0, 0xc, 0xbd,
    0x20, 0x0, 0x3, 0xde, 0x80, 0x10, 0x1, 0xe3,
    0x1d, 0x78, 0x80, 0x6b, 0x0, 0x1d, 0xf2, 0x4,
    0xf4, 0x13, 0xcf, 0x60, 0x6, 0xdf, 0xd6, 0x2b,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+27 "'" */
    0x3c, 0x3b, 0x3b, 0x15,

    /* U+28 "(" */
    0xa, 0x71, 0xf1, 0x5c, 0x9, 0x80, 0xb6, 0xc,
    0x40, 0xd4, 0xc, 0x40, 0xb6, 0x9, 0x80, 0x5b,
    0x1, 0xf1, 0xa, 0x70,

    /* U+29 ")" */
    0x6b, 0x0, 0xf2, 0xb, 0x60, 0x7a, 0x5, 0xc0,
    0x4d, 0x3, 0xe0, 0x4d, 0x5, 0xc0, 0x7a, 0xb,
    0x60, 0xf1, 0x6b, 0x0,

    /* U+2A "*" */
    0x0, 0xb0, 0x8, 0x9c, 0xb5, 0xb, 0xf8, 0x8,
    0x7c, 0x95, 0x0, 0xa0, 0x0,

    /* U+2B "+" */
    0x0, 0xb, 0x0, 0x0, 0x0, 0xf0, 0x0, 0x0,
    0xf, 0x0, 0x2, 0xee, 0xfe, 0xe2, 0x1, 0x1f,
    0x11, 0x0, 0x0, 0xf0, 0x0,

    /* U+2C "," */
    0x18, 0x4, 0xf1, 0xd, 0x3, 0x80,

    /* U+2D "-" */
    0x4f, 0xfd, 0x2, 0x22,

    /* U+2E "." */
    0x2a, 0x4, 0xd0,

    /* U+2F "/" */
    0x0, 0x0, 0x34, 0x0, 0x0, 0xb5, 0x0, 0x0,
    0xf0, 0x0, 0x5, 0xb0, 0x0, 0xa, 0x60, 0x0,
    0xe, 0x10, 0x0, 0x4c, 0x0, 0x0, 0x97, 0x0,
    0x0, 0xe2, 0x0, 0x3, 0xd0, 0x0, 0x8, 0x70,
    0x0, 0xd, 0x20, 0x0, 0x2d, 0x0, 0x0,

    /* U+30 "0" */
    0x0, 0x9e, 0xe9, 0x0, 0xa, 0xd4, 0x4d, 0xa0,
    0x1f, 0x20, 0x2, 0xf1, 0x5e, 0x0, 0x0, 0xd5,
    0x6c, 0x0, 0x0, 0xc6, 0x5e, 0x0, 0x0, 0xd5,
    0x1f, 0x20, 0x2, 0xf1, 0xa, 0xd4, 0x4d, 0xa0,
    0x0, 0x9e, 0xe9, 0x0,

    /* U+31 "1" */
    0xef, 0xf3, 0x22, 0xf3, 0x0, 0xf3, 0x0, 0xf3,
    0x0, 0xf3, 0x0, 0xf3, 0x0, 0xf3, 0x0, 0xf3,
    0x0, 0xf3,

    /* U+32 "2" */
    0x19, 0xef, 0xc2, 0x8, 0xb4, 0x3a, 0xe0, 0x0,
    0x0, 0x2f, 0x10, 0x0, 0x5, 0xe0, 0x0, 0x2,
    0xe5, 0x0, 0x1, 0xd7, 0x0, 0x1, 0xd8, 0x0,
    0x1, 0xda, 0x22, 0x21, 0x8f, 0xff, 0xff, 0x70,

    /* U+33 "3" */
    0x9f, 0xff, 0xff, 0x1, 0x22, 0x2d, 0x80, 0x0,
    0x9, 0xb0, 0x0, 0x5, 0xf2, 0x0, 0x0, 0x7c,
    0xf8, 0x0, 0x0, 0x2, 0xf2, 0x0, 0x0, 0xe,
    0x4b, 0x94, 0x39, 0xf1, 0x3b, 0xff, 0xc3, 0x0,

    /* U+34 "4" */
    0x0, 0x0, 0x9b, 0x0, 0x0, 0x4, 0xe1, 0x0,
    0x0, 0x1e, 0x50, 0x0, 0x0, 0xaa, 0x0, 0x0,
    0x5, 0xe1, 0xd, 0x40, 0x1e, 0x40, 0xd, 0x40,
    0x8f, 0xff, 0xff, 0xfd, 0x12, 0x22, 0x2e, 0x62,
    0x0, 0x0, 0xe, 0x40,

    /* U+35 "5" */
    0xc, 0xff, 0xff, 0x0, 0xe5, 0x22, 0x20, 0xf,
    0x10, 0x0, 0x1, 0xff, 0xeb, 0x30, 0x2, 0x23,
    0x9f, 0x10, 0x0, 0x0, 0xd6, 0x0, 0x0, 0xd,
    0x69, 0xb4, 0x38, 0xf1, 0x2a, 0xef, 0xc4, 0x0,

    /* U+36 "6" */
    0x0, 0x6d, 0xfd, 0x50, 0x8, 0xd5, 0x23, 0x20,
    0x1f, 0x20, 0x0, 0x0, 0x4d, 0x6d, 0xea, 0x10,
    0x6f, 0xc4, 0x3c, 0xa0, 0x5f, 0x30, 0x2, 0xf0,
    0x2f, 0x20, 0x2, 0xf0, 0xa, 0xc3, 0x2b, 0xa0,
    0x1, 0xaf, 0xfa, 0x10,

    /* U+37 "7" */
    0xaf, 0xff, 0xff, 0xba, 0x92, 0x22, 0xd7, 0x76,
    0x0, 0x3f, 0x10, 0x0, 0xa, 0x90, 0x0, 0x1,
    0xf2, 0x0, 0x0, 0x7c, 0x0, 0x0, 0xe, 0x50,
    0x0, 0x5, 0xe0, 0x0, 0x0, 0xc8, 0x0, 0x0,

    /* U+38 "8" */
    0x3, 0xcf, 0xea, 0x10, 0xe, 0x81, 0x2c, 0xa0,
    0x2f, 0x10, 0x5, 0xd0, 0xe, 0x70, 0x1b, 0x90,
    0x6, 0xff, 0xff, 0x20, 0x3f, 0x50, 0x18, 0xe0,
    0x6c, 0x0, 0x0, 0xf2, 0x3f, 0x61, 0x29, 0xe0,
    0x5, 0xcf, 0xfb, 0x20,

    /* U+39 "9" */
    0x7, 0xef, 0xc3, 0x6, 0xe3, 0x15, 0xe1, 0x98,
    0x0, 0xb, 0x87, 0xd2, 0x3, 0xfb, 0xa, 0xff,
    0xd9, 0xc0, 0x0, 0x10, 0x8b, 0x0, 0x0, 0xd,
    0x70, 0x62, 0x4b, 0xd0, 0x1c, 0xfe, 0xa1, 0x0,

    /* U+3A ":" */
    0x4e, 0x2, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xa0, 0x4d, 0x0,

    /* U+3B ";" */
    0x4e, 0x2, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x80, 0x4f, 0x10, 0xd0, 0x38, 0x0,

    /* U+3C "<" */
    0x0, 0x0, 0x2, 0x10, 0x0, 0x4b, 0xe1, 0x7,
    0xdc, 0x50, 0x3, 0xf8, 0x0, 0x0, 0x4, 0xbe,
    0x71, 0x0, 0x0, 0x29, 0xe2, 0x0, 0x0, 0x0,
    0x0,

    /* U+3D "=" */
    0x3f, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xee, 0xee, 0xe2, 0x1, 0x11,
    0x11, 0x0,

    /* U+3E ">" */
    0x12, 0x0, 0x0, 0x2, 0xeb, 0x40, 0x0, 0x0,
    0x5c, 0xd6, 0x0, 0x0, 0x8, 0xf2, 0x1, 0x7e,
    0xb4, 0x2, 0xe9, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+3F "?" */
    0x1a, 0xef, 0xc3, 0x9, 0xa3, 0x2a, 0xe0, 0x0,
    0x0, 0x3f, 0x0, 0x0, 0xa, 0xa0, 0x0, 0x9,
    0xc0, 0x0, 0x2, 0xf1, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x2, 0x80, 0x0, 0x0, 0x4d, 0x0, 0x0,

    /* U+40 "@" */
    0x0, 0x4, 0xbd, 0xdd, 0x81, 0x0, 0x0, 0x9b,
    0x30, 0x0, 0x6d, 0x30, 0x7, 0xa0, 0x8e, 0xe8,
    0xd5, 0xd1, 0xd, 0x7, 0xd2, 0x19, 0xf3, 0x77,
    0x4a, 0xd, 0x40, 0x0, 0xf3, 0x1b, 0x58, 0xf,
    0x20, 0x0, 0xd3, 0xc, 0x58, 0xd, 0x40, 0x0,
    0xf3, 0x1b, 0x3a, 0x7, 0xd2, 0x1a, 0xf5, 0x77,
    0xd, 0x0, 0x8e, 0xe8, 0x5f, 0xb0, 0x6, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9b, 0x30, 0x2,
    0x40, 0x0, 0x0, 0x5, 0xbd, 0xed, 0x60, 0x0,

    /* U+41 "A" */
    0x0, 0x0, 0x6f, 0x30, 0x0, 0x0, 0x0, 0xdd,
    0x90, 0x0, 0x0, 0x4, 0xe3, 0xf1, 0x0, 0x0,
    0xb, 0x80, 0xc7, 0x0, 0x0, 0x1f, 0x20, 0x6e,
    0x0, 0x0, 0x8c, 0x0, 0x1f, 0x50, 0x0, 0xef,
    0xee, 0xef, 0xb0, 0x6, 0xe2, 0x11, 0x14, 0xf2,
    0xc, 0x70, 0x0, 0x0, 0xb9,

    /* U+42 "B" */
    0xbf, 0xff, 0xfb, 0x20, 0xb7, 0x11, 0x2a, 0xd0,
    0xb7, 0x0, 0x3, 0xf0, 0xb7, 0x0, 0x8, 0xc0,
    0xbf, 0xff, 0xff, 0x50, 0xb8, 0x22, 0x26, 0xf2,
    0xb7, 0x0, 0x0, 0xc7, 0xb7, 0x11, 0x15, 0xf4,
    0xbf, 0xff, 0xfd, 0x60,

    /* U+43 "C" */
    0x0, 0x3b, 0xef, 0xb3, 0x0, 0x5f, 0x93, 0x38,
    0xe0, 0xe, 0x60, 0x0, 0x0, 0x4, 0xe0, 0x0,
    0x0, 0x0, 0x6c, 0x0, 0x0, 0x0, 0x4, 0xe0,
    0x0, 0x0, 0x0, 0xe, 0x60, 0x0, 0x0, 0x0,
    0x5f, 0x93, 0x38, 0xe0, 0x0, 0x3b, 0xff, 0xb3,
    0x0,

    /* U+44 "D" */
    0xbf, 0xff, 0xea, 0x30, 0xb, 0x82, 0x23, 0x9f,
    0x40, 0xb7, 0x0, 0x0, 0x7e, 0xb, 0x70, 0x0,
    0x0, 0xf3, 0xb7, 0x0, 0x0, 0xe, 0x5b, 0x70,
    0x0, 0x0, 0xf3, 0xb7, 0x0, 0x0, 0x7e, 0xb,
    0x82, 0x23, 0x9f, 0x40, 0xbf, 0xff, 0xeb, 0x30,
    0x0,

    /* U+45 "E" */
    0xbf, 0xff, 0xff, 0x3b, 0x82, 0x22, 0x20, 0xb7,
    0x0, 0x0, 0xb, 0x70, 0x0, 0x0, 0xbf, 0xff,
    0xfa, 0xb, 0x82, 0x22, 0x10, 0xb7, 0x0, 0x0,
    0xb, 0x82, 0x22, 0x20, 0xbf, 0xff, 0xff, 0x50,

    /* U+46 "F" */
    0xbf, 0xff, 0xff, 0x3b, 0x82, 0x22, 0x20, 0xb7,
    0x0, 0x0, 0xb, 0x70, 0x0, 0x0, 0xbf, 0xff,
    0xfa, 0xb, 0x82, 0x22, 0x10, 0xb7, 0x0, 0x0,
    0xb, 0x70, 0x0, 0x0, 0xb7, 0x0, 0x0, 0x0,

    /* U+47 "G" */
    0x0, 0x3b, 0xef, 0xc4, 0x0, 0x5f, 0x94, 0x38,
    0xe1, 0xe, 0x70, 0x0, 0x0, 0x4, 0xe0, 0x0,
    0x0, 0x0, 0x6c, 0x0, 0x0, 0x8, 0x24, 0xe0,
    0x0, 0x0, 0xe3, 0xe, 0x60, 0x0, 0xe, 0x30,
    0x5f, 0x93, 0x37, 0xf3, 0x0, 0x3b, 0xef, 0xc4,
    0x0,

    /* U+48 "H" */
    0xb7, 0x0, 0x0, 0xb7, 0xb7, 0x0, 0x0, 0xb7,
    0xb7, 0x0, 0x0, 0xb7, 0xb7, 0x0, 0x0, 0xb7,
    0xbf, 0xff, 0xff, 0xf7, 0xb8, 0x22, 0x22, 0xc7,
    0xb7, 0x0, 0x0, 0xb7, 0xb7, 0x0, 0x0, 0xb7,
    0xb7, 0x0, 0x0, 0xb7,

    /* U+49 "I" */
    0xb7, 0xb7, 0xb7, 0xb7, 0xb7, 0xb7, 0xb7, 0xb7,
    0xb7,

    /* U+4A "J" */
    0x4, 0xff, 0xff, 0x0, 0x22, 0x5f, 0x0, 0x0,
    0x3f, 0x0, 0x0, 0x3f, 0x0, 0x0, 0x3f, 0x0,
    0x0, 0x3f, 0x0, 0x0, 0x4e, 0xd, 0x52, 0xba,
    0x5, 0xdf, 0xb2,

    /* U+4B "K" */
    0xb7, 0x0, 0x7, 0xd1, 0xb7, 0x0, 0x5e, 0x20,
    0xb7, 0x4, 0xe3, 0x0, 0xb7, 0x3e, 0x40, 0x0,
    0xb9, 0xef, 0x20, 0x0, 0xbf, 0x89, 0xd0, 0x0,
    0xba, 0x0, 0xca, 0x0, 0xb7, 0x0, 0x1e, 0x70,
    0xb7, 0x0, 0x3, 0xf3,

    /* U+4C "L" */
    0xb7, 0x0, 0x0, 0xb, 0x70, 0x0, 0x0, 0xb7,
    0x0, 0x0, 0xb, 0x70, 0x0, 0x0, 0xb7, 0x0,
    0x0, 0xb, 0x70, 0x0, 0x0, 0xb7, 0x0, 0x0,
    0xb, 0x82, 0x22, 0x20, 0xbf, 0xff, 0xff, 0x0,

    /* U+4D "M" */
    0xb8, 0x0, 0x0, 0x1, 0xf3, 0xbf, 0x10, 0x0,
    0x9, 0xf3, 0xbe, 0xa0, 0x0, 0x2e, 0xf3, 0xb7,
    0xe3, 0x0, 0xb7, 0xf3, 0xb6, 0x7b, 0x4, 0xd0,
    0xf3, 0xb6, 0xd, 0x4c, 0x50, 0xf3, 0xb6, 0x5,
    0xfc, 0x0, 0xf3, 0xb6, 0x0, 0xb3, 0x0, 0xf3,
    0xb6, 0x0, 0x0, 0x0, 0xf3,

    /* U+4E "N" */
    0xb9, 0x0, 0x0, 0xb7, 0xbf, 0x50, 0x0, 0xb7,
    0xbc, 0xf2, 0x0, 0xb7, 0xb7, 0xad, 0x0, 0xb7,
    0xb7, 0xd, 0x90, 0xb7, 0xb7, 0x2, 0xf5, 0xb7,
    0xb7, 0x0, 0x6f, 0xd7, 0xb7, 0x0, 0xa, 0xf7,
    0xb7, 0x0, 0x0, 0xd7,

    /* U+4F "O" */
    0x0, 0x3b, 0xef, 0xb4, 0x0, 0x5, 0xf9, 0x33,
    0x8f, 0x60, 0xe, 0x60, 0x0, 0x5, 0xf1, 0x4e,
    0x0, 0x0, 0x0, 0xd5, 0x6c, 0x0, 0x0, 0x0,
    0xb7, 0x4e, 0x0, 0x0, 0x0, 0xd5, 0xe, 0x60,
    0x0, 0x5, 0xf1, 0x5, 0xf9, 0x33, 0x8f, 0x60,
    0x0, 0x3b, 0xef, 0xb4, 0x0,

    /* U+50 "P" */
    0xbf, 0xff, 0xd8, 0x0, 0xb8, 0x22, 0x5d, 0x90,
    0xb7, 0x0, 0x4, 0xe0, 0xb7, 0x0, 0x3, 0xf0,
    0xb7, 0x0, 0x2c, 0xa0, 0xbf, 0xff, 0xfa, 0x10,
    0xb8, 0x22, 0x0, 0x0, 0xb7, 0x0, 0x0, 0x0,
    0xb7, 0x0, 0x0, 0x0,

    /* U+51 "Q" */
    0x0, 0x3b, 0xef, 0xb4, 0x0, 0x4, 0xf9, 0x33,
    0x8f, 0x60, 0xe, 0x60, 0x0, 0x5, 0xf1, 0x4e,
    0x0, 0x0, 0x0, 0xd5, 0x6c, 0x0, 0x0, 0x0,
    0xb7, 0x4e, 0x0, 0x0, 0x0, 0xd6, 0xf, 0x60,
    0x0, 0x5, 0xf1, 0x5, 0xf8, 0x32, 0x7f, 0x60,
    0x0, 0x4c, 0xff, 0xc5, 0x0, 0x0, 0x0, 0xc,
    0xb0, 0x28, 0x0, 0x0, 0x1, 0xbf, 0xe5, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+52 "R" */
    0xbf, 0xff, 0xd8, 0x0, 0xb8, 0x22, 0x5d, 0x90,
    0xb7, 0x0, 0x4, 0xe0, 0xb7, 0x0, 0x3, 0xf0,
    0xb7, 0x0, 0x1b, 0xb0, 0xbf, 0xff, 0xfb, 0x10,
    0xb8, 0x22, 0xb9, 0x0, 0xb7, 0x0, 0x1f, 0x30,
    0xb7, 0x0, 0x7, 0xd0,

    /* U+53 "S" */
    0x3, 0xcf, 0xeb, 0x31, 0xf7, 0x23, 0x74, 0x4e,
    0x0, 0x0, 0x1, 0xf9, 0x20, 0x0, 0x2, 0xbf,
    0xd7, 0x0, 0x0, 0x4, 0xca, 0x0, 0x0, 0x4,
    0xe5, 0xb4, 0x23, 0xbb, 0x8, 0xdf, 0xea, 0x10,

    /* U+54 "T" */
    0xff, 0xff, 0xff, 0xf2, 0x23, 0xf3, 0x22, 0x0,
    0x1f, 0x10, 0x0, 0x1, 0xf1, 0x0, 0x0, 0x1f,
    0x10, 0x0, 0x1, 0xf1, 0x0, 0x0, 0x1f, 0x10,
    0x0, 0x1, 0xf1, 0x0, 0x0, 0x1f, 0x10, 0x0,

    /* U+55 "U" */
    0xd6, 0x0, 0x0, 0xe4, 0xd6, 0x0, 0x0, 0xe4,
    0xd6, 0x0, 0x0, 0xe4, 0xd6, 0x0, 0x0, 0xe4,
    0xd6, 0x0, 0x0, 0xe4, 0xc7, 0x0, 0x0, 0xf3,
    0x9a, 0x0, 0x2, 0xf1, 0x2f, 0x83, 0x5d, 0xa0,
    0x4, 0xcf, 0xd8, 0x0,

    /* U+56 "V" */
    0xc, 0x70, 0x0, 0x0, 0xd5, 0x6, 0xe0, 0x0,
    0x4, 0xe0, 0x0, 0xf4, 0x0, 0xa, 0x80, 0x0,
    0x9b, 0x0, 0x1f, 0x20, 0x0, 0x2f, 0x20, 0x7b,
    0x0, 0x0, 0xc, 0x80, 0xe4, 0x0, 0x0, 0x5,
    0xe5, 0xe0, 0x0, 0x0, 0x0, 0xee, 0x70, 0x0,
    0x0, 0x0, 0x8f, 0x10, 0x0,

    /* U+57 "W" */
    0x7c, 0x0, 0x0, 0xe8, 0x0, 0x2, 0xf0, 0x2f,
    0x10, 0x3, 0xfd, 0x0, 0x7, 0xa0, 0xd, 0x60,
    0x8, 0x9f, 0x20, 0xc, 0x50, 0x8, 0xb0, 0xe,
    0x3b, 0x70, 0x1f, 0x0, 0x3, 0xf0, 0x3e, 0x6,
    0xc0, 0x6b, 0x0, 0x0, 0xe5, 0x89, 0x1, 0xf1,
    0xb6, 0x0, 0x0, 0x9a, 0xd4, 0x0, 0xb7, 0xf1,
    0x0, 0x0, 0x4f, 0xe0, 0x0, 0x6f, 0xc0, 0x0,
    0x0, 0xf, 0xa0, 0x0, 0x1f, 0x70, 0x0,

    /* U+58 "X" */
    0x5f, 0x10, 0x0, 0xe5, 0xa, 0xb0, 0x9, 0xa0,
    0x1, 0xe6, 0x4e, 0x10, 0x0, 0x4f, 0xe4, 0x0,
    0x0, 0xd, 0xe0, 0x0, 0x0, 0x7d, 0xd8, 0x0,
    0x2, 0xf3, 0x2f, 0x30, 0xc, 0x80, 0x7, 0xd0,
    0x8d, 0x0, 0x0, 0xc9,

    /* U+59 "Y" */
    0xc, 0x80, 0x0, 0xa, 0x80, 0x3f, 0x10, 0x3,
    0xe0, 0x0, 0xaa, 0x0, 0xc6, 0x0, 0x1, 0xf3,
    0x5d, 0x0, 0x0, 0x7, 0xce, 0x40, 0x0, 0x0,
    0xe, 0xb0, 0x0, 0x0, 0x0, 0xb7, 0x0, 0x0,
    0x0, 0xb, 0x70, 0x0, 0x0, 0x0, 0xb7, 0x0,
    0x0,

    /* U+5A "Z" */
    0x6f, 0xff, 0xff, 0xf5, 0x2, 0x22, 0x29, 0xd0,
    0x0, 0x0, 0x3f, 0x30, 0x0, 0x1, 0xe6, 0x0,
    0x0, 0xb, 0xa0, 0x0, 0x0, 0x8d, 0x0, 0x0,
    0x4, 0xf2, 0x0, 0x0, 0x1e, 0x82, 0x22, 0x21,
    0x7f, 0xff, 0xff, 0xf8,

    /* U+5B "[" */
    0xbf, 0xcb, 0x60, 0xb6, 0xb, 0x60, 0xb6, 0xb,
    0x60, 0xb6, 0xb, 0x60, 0xb6, 0xb, 0x60, 0xb6,
    0xb, 0x60, 0xbf, 0xc0,

    /* U+5C "\\" */
    0x35, 0x0, 0x0, 0x2e, 0x0, 0x0, 0xd, 0x30,
    0x0, 0x8, 0x80, 0x0, 0x3, 0xd0, 0x0, 0x0,
    0xd2, 0x0, 0x0, 0x87, 0x0, 0x0, 0x3c, 0x0,
    0x0, 0xe, 0x10, 0x0, 0x9, 0x70, 0x0, 0x4,
    0xc0, 0x0, 0x0, 0xe1, 0x0, 0x0, 0xa6,

    /* U+5D "]" */
    0xcf, 0xb0, 0x7b, 0x6, 0xb0, 0x6b, 0x6, 0xb0,
    0x6b, 0x6, 0xb0, 0x6b, 0x6, 0xb0, 0x6b, 0x6,
    0xb0, 0x7b, 0xcf, 0xb0,

    /* U+5E "^" */
    0x0, 0x7, 0x0, 0x0, 0x5, 0xe5, 0x0, 0x0,
    0xb4, 0xb0, 0x0, 0x2c, 0xc, 0x20, 0x8, 0x60,
    0x68, 0x0, 0xd0, 0x0, 0xd0,

    /* U+5F "_" */
    0xdd, 0xdd, 0xdd,

    /* U+60 "`" */
    0x27, 0x10, 0x5, 0xc1,

    /* U+61 "a" */
    0x8, 0xdf, 0xc3, 0x0, 0xa4, 0x29, 0xd0, 0x0,
    0x0, 0x1f, 0x10, 0x8d, 0xee, 0xf2, 0x4e, 0x10,
    0xf, 0x24, 0xe0, 0x7, 0xf2, 0x9, 0xed, 0x8f,
    0x20,

    /* U+62 "b" */
    0xe4, 0x0, 0x0, 0xe, 0x40, 0x0, 0x0, 0xe4,
    0x0, 0x0, 0xe, 0x7c, 0xfc, 0x40, 0xef, 0x52,
    0x8f, 0x2e, 0x60, 0x0, 0xb8, 0xe4, 0x0, 0x8,
    0xae, 0x60, 0x0, 0xb8, 0xef, 0x52, 0x8f, 0x2e,
    0x6d, 0xfc, 0x40,

    /* U+63 "c" */
    0x2, 0xbf, 0xe8, 0x0, 0xda, 0x24, 0xc3, 0x5d,
    0x0, 0x0, 0x7, 0xb0, 0x0, 0x0, 0x5d, 0x0,
    0x0, 0x0, 0xda, 0x24, 0xd3, 0x2, 0xbf, 0xe8,
    0x0,

    /* U+64 "d" */
    0x0, 0x0, 0x1, 0xf1, 0x0, 0x0, 0x1, 0xf1,
    0x0, 0x0, 0x1, 0xf1, 0x2, 0xbf, 0xd6, 0xf1,
    0xe, 0x92, 0x3d, 0xf1, 0x5d, 0x0, 0x4, 0xf1,
    0x7b, 0x0, 0x1, 0xf1, 0x5d, 0x0, 0x3, 0xf1,
    0xe, 0x91, 0x2d, 0xf1, 0x2, 0xbf, 0xe6, 0xf1,

    /* U+65 "e" */
    0x2, 0xbf, 0xd5, 0x0, 0xe8, 0x14, 0xe4, 0x5c,
    0x0, 0x6, 0xb7, 0xfe, 0xee, 0xec, 0x5d, 0x0,
    0x0, 0x0, 0xe9, 0x23, 0xa2, 0x2, 0xbf, 0xe9,
    0x0,

    /* U+66 "f" */
    0x1, 0xcf, 0x60, 0x9a, 0x11, 0xb, 0x60, 0xd,
    0xff, 0xf3, 0xb, 0x60, 0x0, 0xb6, 0x0, 0xb,
    0x60, 0x0, 0xb6, 0x0, 0xb, 0x60, 0x0, 0xb6,
    0x0,

    /* U+67 "g" */
    0x2, 0xbf, 0xe6, 0xe2, 0xe, 0xa2, 0x3c, 0xf2,
    0x5d, 0x0, 0x2, 0xf2, 0x7b, 0x0, 0x0, 0xf2,
    0x5d, 0x0, 0x2, 0xf2, 0xe, 0xa2, 0x3d, 0xf2,
    0x2, 0xbf, 0xe5, 0xf2, 0x0, 0x0, 0x2, 0xf0,
    0xc, 0x62, 0x3b, 0xa0, 0x6, 0xdf, 0xea, 0x10,

    /* U+68 "h" */
    0xe4, 0x0, 0x0, 0xe, 0x40, 0x0, 0x0, 0xe4,
    0x0, 0x0, 0xe, 0x7d, 0xfc, 0x20, 0xee, 0x42,
    0xac, 0xe, 0x60, 0x2, 0xf0, 0xe4, 0x0, 0xf,
    0x1e, 0x40, 0x0, 0xf2, 0xe4, 0x0, 0xf, 0x2e,
    0x40, 0x0, 0xf2,

    /* U+69 "i" */
    0xd, 0x40, 0x82, 0x0, 0x0, 0xe4, 0xe, 0x40,
    0xe4, 0xe, 0x40, 0xe4, 0xe, 0x40, 0xe4,

    /* U+6A "j" */
    0x0, 0xd, 0x50, 0x0, 0x72, 0x0, 0x0, 0x0,
    0x0, 0xd4, 0x0, 0xd, 0x40, 0x0, 0xd4, 0x0,
    0xd, 0x40, 0x0, 0xd4, 0x0, 0xd, 0x40, 0x0,
    0xd4, 0x0, 0xd, 0x40, 0x22, 0xf2, 0xd, 0xf8,
    0x0,

    /* U+6B "k" */
    0xe4, 0x0, 0x0, 0xe, 0x40, 0x0, 0x0, 0xe4,
    0x0, 0x0, 0xe, 0x40, 0xb, 0xa0, 0xe4, 0xb,
    0xb0, 0xe, 0x4b, 0xc0, 0x0, 0xee, 0xfd, 0x0,
    0xe, 0xc1, 0xd9, 0x0, 0xe4, 0x2, 0xf4, 0xe,
    0x40, 0x6, 0xe1,

    /* U+6C "l" */
    0xe4, 0xe4, 0xe4, 0xe4, 0xe4, 0xe4, 0xe4, 0xe4,
    0xe4, 0xe4,

    /* U+6D "m" */
    0xe7, 0xdf, 0xb2, 0x9e, 0xe8, 0xe, 0xd3, 0x2c,
    0xfb, 0x23, 0xe5, 0xe6, 0x0, 0x4f, 0x10, 0x9,
    0x9e, 0x40, 0x3, 0xf0, 0x0, 0x8a, 0xe4, 0x0,
    0x3f, 0x0, 0x8, 0xae, 0x40, 0x3, 0xf0, 0x0,
    0x8a, 0xe4, 0x0, 0x3f, 0x0, 0x8, 0xa0,

    /* U+6E "n" */
    0xe6, 0xdf, 0xc2, 0xe, 0xe4, 0x1a, 0xc0, 0xe6,
    0x0, 0x1f, 0xe, 0x40, 0x0, 0xf1, 0xe4, 0x0,
    0xf, 0x2e, 0x40, 0x0, 0xf2, 0xe4, 0x0, 0xf,
    0x20,

    /* U+6F "o" */
    0x2, 0xbf, 0xe8, 0x0, 0xe, 0xa2, 0x3e, 0x80,
    0x5d, 0x0, 0x4, 0xf0, 0x7b, 0x0, 0x1, 0xf1,
    0x5d, 0x0, 0x4, 0xf0, 0xd, 0xa2, 0x3e, 0x80,
    0x2, 0xbf, 0xe8, 0x0,

    /* U+70 "p" */
    0xe7, 0xdf, 0xc4, 0xe, 0xf4, 0x16, 0xf2, 0xe6,
    0x0, 0xa, 0x8e, 0x40, 0x0, 0x8a, 0xe7, 0x0,
    0xb, 0x8e, 0xf5, 0x28, 0xf2, 0xe6, 0xcf, 0xc4,
    0xe, 0x40, 0x0, 0x0, 0xe4, 0x0, 0x0, 0xe,
    0x40, 0x0, 0x0,

    /* U+71 "q" */
    0x2, 0xbf, 0xd5, 0xf1, 0xe, 0xa2, 0x3e, 0xf1,
    0x5d, 0x0, 0x4, 0xf1, 0x7b, 0x0, 0x1, 0xf1,
    0x5d, 0x0, 0x4, 0xf1, 0xe, 0xa2, 0x3e, 0xf1,
    0x2, 0xbf, 0xd5, 0xf1, 0x0, 0x0, 0x1, 0xf1,
    0x0, 0x0, 0x1, 0xf1, 0x0, 0x0, 0x1, 0xf1,

    /* U+72 "r" */
    0xe6, 0xd8, 0xee, 0x61, 0xe7, 0x0, 0xe4, 0x0,
    0xe4, 0x0, 0xe4, 0x0, 0xe4, 0x0,

    /* U+73 "s" */
    0x9, 0xef, 0xc2, 0x6d, 0x22, 0x61, 0x7d, 0x20,
    0x0, 0x9, 0xfe, 0x91, 0x0, 0x2, 0xc9, 0x56,
    0x22, 0xb8, 0x4c, 0xfe, 0xa0,

    /* U+74 "t" */
    0x5, 0x30, 0x0, 0xb6, 0x0, 0xdf, 0xff, 0x30,
    0xb6, 0x0, 0xb, 0x60, 0x0, 0xb6, 0x0, 0xb,
    0x60, 0x0, 0xaa, 0x11, 0x2, 0xdf, 0x60,

    /* U+75 "u" */
    0xf3, 0x0, 0x2f, 0xf, 0x30, 0x2, 0xf0, 0xf3,
    0x0, 0x2f, 0xf, 0x30, 0x2, 0xf0, 0xe4, 0x0,
    0x4f, 0xa, 0xb2, 0x2c, 0xf0, 0x1b, 0xfe, 0x6f,
    0x0,

    /* U+76 "v" */
    0xd, 0x50, 0x0, 0x98, 0x6, 0xc0, 0x0, 0xf2,
    0x1, 0xf2, 0x6, 0xb0, 0x0, 0xa8, 0xc, 0x50,
    0x0, 0x3e, 0x3e, 0x0, 0x0, 0xd, 0xd8, 0x0,
    0x0, 0x6, 0xf2, 0x0,

    /* U+77 "w" */
    0xc5, 0x0, 0x3f, 0x10, 0x7, 0x86, 0xa0, 0x9,
    0xf6, 0x0, 0xd3, 0x1f, 0x0, 0xe7, 0xb0, 0x2d,
    0x0, 0xb5, 0x4c, 0xe, 0x18, 0x80, 0x6, 0xa9,
    0x60, 0xa6, 0xd3, 0x0, 0x1f, 0xe1, 0x4, 0xed,
    0x0, 0x0, 0xbb, 0x0, 0xe, 0x80, 0x0,

    /* U+78 "x" */
    0x5d, 0x0, 0x4e, 0x10, 0xa9, 0x1e, 0x40, 0x1,
    0xed, 0x90, 0x0, 0x8, 0xf1, 0x0, 0x2, 0xeb,
    0xa0, 0x0, 0xc7, 0xd, 0x60, 0x7c, 0x0, 0x3f,
    0x20,

    /* U+79 "y" */
    0xd, 0x50, 0x0, 0x98, 0x7, 0xb0, 0x0, 0xe2,
    0x1, 0xf2, 0x5, 0xc0, 0x0, 0xa7, 0xb, 0x50,
    0x0, 0x4d, 0x1e, 0x0, 0x0, 0xe, 0xb9, 0x0,
    0x0, 0x8, 0xf3, 0x0, 0x0, 0x5, 0xd0, 0x0,
    0x5, 0x2c, 0x60, 0x0, 0x1c, 0xf9, 0x0, 0x0,

    /* U+7A "z" */
    0x7f, 0xff, 0xfb, 0x0, 0x2, 0xf3, 0x0, 0xc,
    0x70, 0x0, 0x9b, 0x0, 0x4, 0xe1, 0x0, 0x1e,
    0x50, 0x0, 0x8f, 0xff, 0xfd,

    /* U+7B "{" */
    0x0, 0xbf, 0x4, 0xe1, 0x5, 0xc0, 0x5, 0xc0,
    0x5, 0xc0, 0x6, 0xc0, 0x4f, 0x60, 0x8, 0xc0,
    0x5, 0xc0, 0x5, 0xc0, 0x5, 0xc0, 0x4, 0xe1,
    0x0, 0xbf,

    /* U+7C "|" */
    0xb5, 0xb5, 0xb5, 0xb5, 0xb5, 0xb5, 0xb5, 0xb5,
    0xb5, 0xb5, 0xb5, 0xb5, 0xb5,

    /* U+7D "}" */
    0xcd, 0x10, 0xc, 0x70, 0x9, 0x90, 0x9, 0x90,
    0x9, 0x90, 0x8, 0xa0, 0x3, 0xf7, 0x8, 0xb0,
    0x9, 0x90, 0x9, 0x90, 0x9, 0x90, 0xc, 0x80,
    0xcd, 0x20,

    /* U+7E "~" */
    0xb, 0xe8, 0xa, 0x33, 0x91, 0x8d, 0xa0,

    /* U+B0 "°" */
    0x6, 0xb7, 0x3, 0x80, 0x84, 0x64, 0x3, 0x73,
    0x80, 0x84, 0x6, 0xb7, 0x0,

    /* U+2022 "•" */
    0x4, 0x22, 0xfe, 0xd, 0xa0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x0, 0x0,
    0x0, 0x3, 0x7c, 0xff, 0x0, 0x0, 0x59, 0xef,
    0xff, 0xff, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xf, 0xff, 0xfd, 0x84, 0x8f, 0x0, 0xf,
    0xd7, 0x20, 0x0, 0x8f, 0x0, 0xf, 0x80, 0x0,
    0x0, 0x8f, 0x0, 0xf, 0x80, 0x0, 0x0, 0x8f,
    0x0, 0xf, 0x80, 0x0, 0x7b, 0xdf, 0x2, 0x3f,
    0x80, 0x6, 0xff, 0xff, 0xaf, 0xff, 0x80, 0x2,
    0xef, 0xf9, 0xef, 0xff, 0x60, 0x0, 0x2, 0x10,
    0x29, 0xa7, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0xb4, 0xdf, 0xff, 0xff, 0xfd, 0x4b, 0xe8, 0xe7,
    0x22, 0x22, 0x7e, 0x8e, 0xc0, 0xc5, 0x0, 0x0,
    0x6c, 0xc, 0xfc, 0xf6, 0x11, 0x11, 0x7f, 0xcf,
    0xc0, 0xcf, 0xff, 0xff, 0xfb, 0xc, 0xfc, 0xf6,
    0x11, 0x11, 0x7f, 0xcf, 0xc0, 0xc5, 0x0, 0x0,
    0x6c, 0xc, 0xe8, 0xe7, 0x22, 0x22, 0x7e, 0x8e,
    0xb4, 0xdf, 0xff, 0xff, 0xfd, 0x4b,

    /* U+F00B "" */
    0xdf, 0xf6, 0x9f, 0xff, 0xff, 0xfd, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xef, 0xf6, 0xaf, 0xff,
    0xff, 0xfe, 0x13, 0x20, 0x3, 0x33, 0x33, 0x31,
    0xff, 0xf7, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xbf, 0xff,
    0xff, 0xff, 0x13, 0x20, 0x3, 0x33, 0x33, 0x31,
    0xef, 0xf6, 0xaf, 0xff, 0xff, 0xfe, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xdf, 0xf6, 0xaf, 0xff,
    0xff, 0xfd,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf4, 0x4d, 0x30, 0x0, 0x3f, 0xff, 0x40,
    0xef, 0xf3, 0x3, 0xff, 0xf4, 0x0, 0x4f, 0xff,
    0x6f, 0xff, 0x40, 0x0, 0x4, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x3, 0xd3, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x14, 0x0, 0x0, 0x22, 0xd, 0xf7, 0x0, 0x4f,
    0xf1, 0x9f, 0xf7, 0x4f, 0xfd, 0x0, 0xaf, 0xff,
    0xfd, 0x10, 0x0, 0xbf, 0xfe, 0x10, 0x0, 0x4f,
    0xff, 0xf7, 0x0, 0x4f, 0xfd, 0xaf, 0xf7, 0xe,
    0xfd, 0x10, 0xaf, 0xf2, 0x5b, 0x10, 0x0, 0x99,
    0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x7, 0x70, 0x0, 0x0, 0x0, 0x32,
    0xf, 0xf0, 0x24, 0x0, 0x5, 0xfc, 0xf, 0xf0,
    0xcf, 0x50, 0x1f, 0xf4, 0xf, 0xf0, 0x5f, 0xf1,
    0x7f, 0x80, 0xf, 0xf0, 0x8, 0xf7, 0xbf, 0x20,
    0xf, 0xf0, 0x2, 0xfb, 0xcf, 0x10, 0xe, 0xe0,
    0x1, 0xfc, 0xaf, 0x40, 0x1, 0x10, 0x4, 0xfa,
    0x5f, 0xb0, 0x0, 0x0, 0xb, 0xf6, 0xd, 0xfa,
    0x10, 0x1, 0xaf, 0xd0, 0x2, 0xdf, 0xfc, 0xcf,
    0xfd, 0x20, 0x0, 0x8, 0xef, 0xfe, 0x91, 0x0,
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x14, 0x41, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf7, 0x0, 0x0, 0x3, 0x43, 0xdf, 0xfd,
    0x34, 0x30, 0xe, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x6f, 0xff, 0xfb, 0xbf, 0xff, 0xf6, 0x1b, 0xff,
    0x70, 0x7, 0xff, 0xb1, 0x7, 0xff, 0x20, 0x2,
    0xff, 0x70, 0x1b, 0xff, 0x70, 0x7, 0xff, 0xb1,
    0x6f, 0xff, 0xfb, 0xbf, 0xff, 0xf6, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x3, 0x42, 0xcf, 0xfc,
    0x23, 0x30, 0x0, 0x0, 0x7f, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x41, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x73, 0x3, 0x83, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0x67, 0xf7, 0x0, 0x0, 0x3,
    0xee, 0x5a, 0xfe, 0xf7, 0x0, 0x0, 0x6f, 0xd3,
    0xb5, 0x7f, 0xf7, 0x0, 0x9, 0xfb, 0x3d, 0xff,
    0x85, 0xfe, 0x30, 0xbf, 0x95, 0xff, 0xff, 0xfb,
    0x3e, 0xf4, 0x76, 0x6f, 0xff, 0xff, 0xff, 0xd2,
    0xa1, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xfa, 0x2, 0xff, 0xf4, 0x0, 0x0,
    0xcf, 0xfa, 0x2, 0xff, 0xf4, 0x0, 0x0, 0xaf,
    0xf8, 0x1, 0xff, 0xf3, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x27, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x4, 0xff, 0xff, 0x40, 0x0,
    0x23, 0x33, 0x5f, 0xf5, 0x33, 0x32, 0xff, 0xff,
    0xa4, 0x4a, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5c, 0x8f,
    0x9a, 0xaa, 0xaa, 0xaa, 0xaa, 0xa8,

    /* U+F01C "" */
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x1,
    0xed, 0x88, 0x88, 0x89, 0xf8, 0x0, 0xa, 0xf2,
    0x0, 0x0, 0x0, 0xaf, 0x30, 0x5f, 0x70, 0x0,
    0x0, 0x0, 0x1e, 0xc0, 0xef, 0x88, 0x60, 0x0,
    0x28, 0x8b, 0xf6, 0xff, 0xff, 0xf3, 0x0, 0xbf,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,

    /* U+F021 "" */
    0x0, 0x0, 0x1, 0x10, 0x0, 0x59, 0x0, 0x19,
    0xef, 0xfd, 0x70, 0x9f, 0x3, 0xef, 0xda, 0x9d,
    0xfe, 0xbf, 0xe, 0xf6, 0x0, 0x0, 0x5f, 0xff,
    0x7f, 0x70, 0x0, 0x3f, 0xff, 0xff, 0x69, 0x0,
    0x0, 0x2a, 0xaa, 0xa9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaa, 0xaa, 0xa2, 0x0, 0x0, 0xa6,
    0xff, 0xfe, 0xf3, 0x0, 0x7, 0xf7, 0xff, 0xf5,
    0x0, 0x0, 0x7f, 0xe0, 0xfb, 0xef, 0xd9, 0xad,
    0xfe, 0x30, 0xfa, 0x8, 0xef, 0xfe, 0x91, 0x0,
    0x95, 0x0, 0x1, 0x10, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x2a, 0x0, 0x2, 0xef, 0x78, 0x8e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xdf, 0xff, 0xff, 0x0, 0x7, 0xff,
    0x0, 0x0, 0x7f, 0x0, 0x0, 0x1,

    /* U+F027 "" */
    0x0, 0x0, 0x2a, 0x0, 0x0, 0x0, 0x2e, 0xf0,
    0x0, 0x78, 0x8e, 0xff, 0x3, 0xf, 0xff, 0xff,
    0xf0, 0xba, 0xff, 0xff, 0xff, 0x3, 0xff, 0xff,
    0xff, 0xf0, 0xaa, 0xdf, 0xff, 0xff, 0x4, 0x0,
    0x0, 0x8f, 0xf0, 0x0, 0x0, 0x0, 0x8f, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xd2, 0x0, 0x0, 0x0,
    0x2a, 0x0, 0x11, 0x8e, 0x10, 0x0, 0x2, 0xef,
    0x0, 0x7d, 0x2b, 0x90, 0x78, 0x8e, 0xff, 0x3,
    0xa, 0xb3, 0xf0, 0xff, 0xff, 0xff, 0xb, 0xa1,
    0xf1, 0xe3, 0xff, 0xff, 0xff, 0x3, 0xf0, 0xe3,
    0xc5, 0xff, 0xff, 0xff, 0xb, 0xa1, 0xf1, 0xe3,
    0xdf, 0xff, 0xff, 0x3, 0xa, 0xb3, 0xf0, 0x0,
    0x7, 0xff, 0x0, 0x7d, 0x2b, 0x90, 0x0, 0x0,
    0x7f, 0x0, 0x11, 0x9e, 0x10, 0x0, 0x0, 0x1,
    0x0, 0x6, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F03E "" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xfd, 0x5b,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x1, 0xff, 0xff,
    0xef, 0xff, 0xfb, 0x18, 0xff, 0xf6, 0x1c, 0xff,
    0xff, 0xfc, 0xff, 0x60, 0x1, 0xdf, 0xff, 0x60,
    0x96, 0x0, 0x0, 0x8f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xfc, 0x88, 0x88, 0x88, 0x88, 0xcf,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F048 "" */
    0x58, 0x0, 0x0, 0x35, 0x9f, 0x10, 0x5, 0xfe,
    0x9f, 0x10, 0x6f, 0xfe, 0x9f, 0x17, 0xff, 0xfe,
    0x9f, 0x9f, 0xff, 0xfe, 0x9f, 0xff, 0xff, 0xfe,
    0x9f, 0xef, 0xff, 0xfe, 0x9f, 0x2d, 0xff, 0xfe,
    0x9f, 0x10, 0xcf, 0xfe, 0x9f, 0x10, 0xb, 0xfe,
    0x8f, 0x0, 0x0, 0x9b, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x46, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0x40,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xa1, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xfd, 0x50, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xb1, 0xff, 0xff, 0xff, 0xff, 0xff, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xb1, 0xff, 0xff, 0xff, 0xfd,
    0x40, 0xf, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xff,
    0xff, 0xa1, 0x0, 0x0, 0xf, 0xfd, 0x40, 0x0,
    0x0, 0x0, 0x36, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0xaf, 0xfe, 0x30, 0xaf, 0xfe, 0x3f, 0xff, 0xf7,
    0xf, 0xff, 0xf7, 0xff, 0xff, 0x80, 0xff, 0xff,
    0x8f, 0xff, 0xf8, 0xf, 0xff, 0xf8, 0xff, 0xff,
    0x80, 0xff, 0xff, 0x8f, 0xff, 0xf8, 0xf, 0xff,
    0xf8, 0xff, 0xff, 0x80, 0xff, 0xff, 0x8f, 0xff,
    0xf8, 0xf, 0xff, 0xf8, 0xff, 0xff, 0x80, 0xff,
    0xff, 0x8f, 0xff, 0xf7, 0xf, 0xff, 0xf7, 0x48,
    0x98, 0x10, 0x48, 0x98, 0x10,

    /* U+F04D "" */
    0x48, 0x88, 0x88, 0x88, 0x88, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xaf,
    0xff, 0xff, 0xff, 0xfe, 0x30,

    /* U+F051 "" */
    0x26, 0x0, 0x0, 0x58, 0x7f, 0xa0, 0x0, 0xbf,
    0x8f, 0xfb, 0x0, 0xbf, 0x8f, 0xff, 0xc1, 0xbf,
    0x8f, 0xff, 0xfd, 0xcf, 0x8f, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xef, 0x8f, 0xff, 0xf4, 0xbf,
    0x8f, 0xff, 0x40, 0xbf, 0x8f, 0xe3, 0x0, 0xbf,
    0x5d, 0x20, 0x0, 0xae, 0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x3, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xfa, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0x90, 0x0, 0x0, 0x1e, 0xff, 0xff, 0xf8, 0x0,
    0x1, 0xdf, 0xff, 0xff, 0xff, 0x70, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x1, 0x34, 0x44, 0x44, 0x44, 0x30,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xf5,

    /* U+F053 "" */
    0x0, 0x0, 0x3, 0x10, 0x0, 0x5, 0xfb, 0x0,
    0x5, 0xff, 0x40, 0x5, 0xff, 0x40, 0x5, 0xff,
    0x50, 0x3, 0xff, 0x50, 0x0, 0xb, 0xfc, 0x10,
    0x0, 0xb, 0xfc, 0x10, 0x0, 0xc, 0xfc, 0x10,
    0x0, 0xc, 0xfb, 0x0, 0x0, 0xa, 0x50,

    /* U+F054 "" */
    0x3, 0x10, 0x0, 0x3, 0xfc, 0x10, 0x0, 0xb,
    0xfc, 0x10, 0x0, 0xb, 0xfc, 0x10, 0x0, 0xb,
    0xfc, 0x10, 0x0, 0xd, 0xfb, 0x0, 0x5, 0xff,
    0x50, 0x5, 0xff, 0x50, 0x5, 0xff, 0x50, 0x3,
    0xff, 0x50, 0x0, 0xa, 0x50, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x69, 0x10, 0x0, 0x0, 0x0, 0xd,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0xef, 0x60, 0x0,
    0x0, 0x0, 0xe, 0xf6, 0x0, 0x0, 0x58, 0x88,
    0xff, 0xb8, 0x88, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x9b, 0xbb, 0xff, 0xdb, 0xbb, 0x30, 0x0,
    0xe, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xef, 0x60,
    0x0, 0x0, 0x0, 0xe, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x9d, 0x20, 0x0, 0x0,

    /* U+F068 "" */
    0x46, 0x66, 0x66, 0x66, 0x66, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xad, 0xdd, 0xdd, 0xdd, 0xdd,
    0x40,

    /* U+F06E "" */
    0x0, 0x3, 0xad, 0xff, 0xc7, 0x0, 0x0, 0x0,
    0x9f, 0xe6, 0x24, 0xaf, 0xe3, 0x0, 0xb, 0xff,
    0x20, 0x77, 0x9, 0xff, 0x40, 0x7f, 0xf9, 0x0,
    0xcf, 0xa1, 0xff, 0xe1, 0xef, 0xf6, 0x7f, 0xff,
    0xf0, 0xef, 0xf7, 0x8f, 0xf9, 0x3f, 0xff, 0xc1,
    0xff, 0xe1, 0xb, 0xff, 0x26, 0xca, 0x19, 0xff,
    0x40, 0x0, 0x9f, 0xe6, 0x24, 0xaf, 0xe3, 0x0,
    0x0, 0x3, 0x9d, 0xff, 0xc7, 0x0, 0x0,

    /* U+F070 "" */
    0x32, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xf8, 0x4a, 0xef, 0xeb, 0x50, 0x0, 0x0,
    0x0, 0x9f, 0xfd, 0x52, 0x5d, 0xfc, 0x10, 0x0,
    0x0, 0x5, 0xfe, 0x4a, 0x70, 0xcf, 0xe1, 0x0,
    0xb, 0x80, 0x2d, 0xff, 0xf7, 0x4f, 0xfb, 0x0,
    0x2f, 0xfb, 0x0, 0xaf, 0xfb, 0x2f, 0xff, 0x30,
    0xb, 0xff, 0x50, 0x7, 0xfe, 0x7f, 0xfb, 0x0,
    0x1, 0xdf, 0xc0, 0x0, 0x3e, 0xff, 0xe1, 0x0,
    0x0, 0x1b, 0xfc, 0x42, 0x1, 0xbf, 0xa0, 0x0,
    0x0, 0x0, 0x5b, 0xef, 0xb0, 0x8, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x40,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x3, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xfd, 0xef, 0xa0, 0x0, 0x0, 0x0, 0xb,
    0xfb, 0x3, 0xff, 0x30, 0x0, 0x0, 0x4, 0xff,
    0xc0, 0x4f, 0xfc, 0x0, 0x0, 0x0, 0xdf, 0xfd,
    0x5, 0xff, 0xf6, 0x0, 0x0, 0x7f, 0xff, 0xf8,
    0xcf, 0xff, 0xe1, 0x0, 0x1f, 0xff, 0xfc, 0x4,
    0xff, 0xff, 0x90, 0xa, 0xff, 0xff, 0xd2, 0x7f,
    0xff, 0xff, 0x20, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x4, 0x78, 0x88, 0x88, 0x88, 0x88,
    0x87, 0x0,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x6, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xc1, 0xff, 0xf8, 0x0, 0x2e,
    0xff, 0xfc, 0xcd, 0xff, 0x62, 0xef, 0xdf, 0xf9,
    0x0, 0x2c, 0x4e, 0xf9, 0xf, 0x90, 0x0, 0x2,
    0xef, 0x90, 0x7, 0x0, 0x0, 0x2e, 0xf8, 0x88,
    0xf, 0xa0, 0xcd, 0xff, 0x80, 0xdf, 0xdf, 0xf9,
    0xff, 0xf8, 0x0, 0x1e, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0x10,

    /* U+F077 "" */
    0x0, 0x0, 0x27, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xf9, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xf9, 0x0,
    0x0, 0x2e, 0xf9, 0x2e, 0xf9, 0x0, 0x2e, 0xf9,
    0x0, 0x2e, 0xf9, 0xb, 0xf9, 0x0, 0x0, 0x2e,
    0xf4, 0x27, 0x0, 0x0, 0x0, 0x27, 0x0,

    /* U+F078 "" */
    0x27, 0x0, 0x0, 0x0, 0x27, 0xb, 0xf9, 0x0,
    0x0, 0x2e, 0xf4, 0x2e, 0xf9, 0x0, 0x2e, 0xf9,
    0x0, 0x2e, 0xf9, 0x2e, 0xf9, 0x0, 0x0, 0x2e,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x2e, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x26, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xc0, 0x7, 0x77, 0x77, 0x72, 0x0,
    0x3, 0xff, 0xfc, 0x2e, 0xff, 0xff, 0xf9, 0x0,
    0xf, 0xcf, 0xcf, 0xa0, 0x0, 0x0, 0xe9, 0x0,
    0x4, 0x1e, 0x93, 0x20, 0x0, 0x0, 0xe9, 0x0,
    0x0, 0xe, 0x90, 0x0, 0x0, 0x0, 0xe9, 0x0,
    0x0, 0xe, 0x90, 0x0, 0x0, 0xb5, 0xe9, 0x97,
    0x0, 0xe, 0xc7, 0x77, 0x73, 0xbf, 0xff, 0xf6,
    0x0, 0xd, 0xff, 0xff, 0xfd, 0xb, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa6, 0x0,

    /* U+F07B "" */
    0xbf, 0xff, 0xf6, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0x98, 0x88, 0x74, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F093 "" */
    0x0, 0x0, 0x2, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xe3, 0x0, 0x0, 0x0, 0x3, 0xef, 0xfe,
    0x30, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xe3, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x8f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0,
    0x23, 0x32, 0x8f, 0xf8, 0x23, 0x32, 0xff, 0xfe,
    0x39, 0x93, 0xef, 0xff, 0xff, 0xff, 0xc9, 0x9c,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5c, 0x8f,
    0x9a, 0xaa, 0xaa, 0xaa, 0xaa, 0xa8,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0x62, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xfd, 0x0, 0x0, 0x1,
    0x0, 0x9, 0xff, 0x40, 0x1, 0x8e, 0xe1, 0x1a,
    0xff, 0x70, 0x0, 0xef, 0xff, 0xde, 0xff, 0x90,
    0x0, 0xc, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x8f, 0xff, 0xe9, 0x10, 0x0, 0x0, 0x2, 0x76,
    0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x7, 0x93, 0x0, 0x0, 0x22, 0xa, 0xff, 0xf2,
    0x0, 0x8f, 0xf5, 0xf9, 0x1f, 0x70, 0x8f, 0xf9,
    0xc, 0xfc, 0xf8, 0x8f, 0xf9, 0x0, 0x1a, 0xef,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0xef, 0xfc, 0x0,
    0x0, 0x7, 0xbf, 0xff, 0xf6, 0x0, 0xa, 0xff,
    0xfa, 0xbf, 0xf6, 0x0, 0xf9, 0x1f, 0x70, 0xbf,
    0xf6, 0xc, 0xfc, 0xf4, 0x0, 0xbf, 0xf4, 0x1a,
    0xc6, 0x0, 0x0, 0x56, 0x0,

    /* U+F0C5 "" */
    0x0, 0x3, 0x44, 0x41, 0x20, 0x0, 0x0, 0xff,
    0xff, 0x5e, 0x40, 0x24, 0x1f, 0xff, 0xf5, 0xee,
    0x2f, 0xf4, 0xff, 0xff, 0xc8, 0x82, 0xff, 0x4f,
    0xff, 0xff, 0xff, 0x5f, 0xf4, 0xff, 0xff, 0xff,
    0xf5, 0xff, 0x4f, 0xff, 0xff, 0xff, 0x5f, 0xf4,
    0xff, 0xff, 0xff, 0xf5, 0xff, 0x4f, 0xff, 0xff,
    0xff, 0x5f, 0xf4, 0xff, 0xff, 0xff, 0xf4, 0xff,
    0x93, 0x44, 0x44, 0x43, 0xf, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x68, 0x88, 0x88, 0x71, 0x0, 0x0,

    /* U+F0C7 "" */
    0x48, 0x88, 0x88, 0x87, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0xf8, 0x0, 0x0, 0xb, 0xfb,
    0xf, 0x80, 0x0, 0x0, 0xbf, 0xf3, 0xfb, 0x77,
    0x77, 0x7d, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0x42, 0xdf, 0xff, 0x4f, 0xff,
    0xc0, 0x8, 0xff, 0xf4, 0xff, 0xfe, 0x0, 0xaf,
    0xff, 0x4f, 0xff, 0xfc, 0xaf, 0xff, 0xf4, 0xaf,
    0xff, 0xff, 0xff, 0xfd, 0x10,

    /* U+F0E7 "" */
    0x1, 0xbb, 0xba, 0x10, 0x0, 0x5f, 0xff, 0xf1,
    0x0, 0x7, 0xff, 0xfb, 0x0, 0x0, 0x9f, 0xff,
    0x60, 0x0, 0xb, 0xff, 0xff, 0xff, 0x60, 0xef,
    0xff, 0xff, 0xf1, 0xe, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xc, 0xfe, 0x0, 0x0, 0x0, 0xff, 0x50,
    0x0, 0x0, 0x3f, 0xc0, 0x0, 0x0, 0x7, 0xf3,
    0x0, 0x0, 0x0, 0xa9, 0x0, 0x0, 0x0, 0x2,
    0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x2a, 0x50, 0x0, 0x0, 0xe, 0xff, 0x8f,
    0xff, 0x20, 0x0, 0xff, 0xf8, 0xff, 0xf4, 0x0,
    0xf, 0xff, 0xeb, 0xbb, 0x30, 0x0, 0xff, 0xf4,
    0x99, 0x92, 0x60, 0xf, 0xff, 0x5f, 0xff, 0x4f,
    0xa0, 0xff, 0xf5, 0xff, 0xf5, 0x56, 0x1f, 0xff,
    0x5f, 0xff, 0xff, 0xf4, 0xff, 0xf5, 0xff, 0xff,
    0xff, 0x4e, 0xff, 0x5f, 0xff, 0xff, 0xf4, 0x0,
    0x5, 0xff, 0xff, 0xff, 0x40, 0x0, 0x5f, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x44, 0x44, 0x44, 0x0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x15, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xf1, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xf9, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xf7, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xf2, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0x50, 0x6f, 0xff, 0xff, 0xff,
    0xfd, 0xe, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x24,
    0x44, 0x44, 0x44, 0x43, 0x0, 0x0, 0x2f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x46, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xfc,
    0x8e, 0x8e, 0x8e, 0x88, 0xe8, 0xf7, 0xf8, 0xc,
    0xc, 0xb, 0x0, 0xb0, 0xf8, 0xff, 0xec, 0xfc,
    0xec, 0xee, 0xcf, 0xf8, 0xff, 0xa0, 0xc0, 0xa0,
    0x77, 0x2f, 0xf8, 0xff, 0xec, 0xfc, 0xec, 0xee,
    0xcf, 0xf8, 0xf8, 0xc, 0x0, 0x0, 0x0, 0xb0,
    0xf8, 0xfc, 0x8e, 0x88, 0x88, 0x88, 0xe8, 0xf7,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x18, 0xef, 0xe0, 0x0, 0x0,
    0x0, 0x29, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x3a,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x4c, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x1, 0x34, 0x44, 0xdf, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x26,
    0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x9b, 0xbb, 0xb2, 0x70, 0xf, 0xff, 0xff, 0x4f,
    0x90, 0xff, 0xff, 0xf4, 0xff, 0x9f, 0xff, 0xff,
    0x54, 0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x34, 0x44,
    0x44, 0x44, 0x30,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x9b, 0xcb, 0x95, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0x80, 0x3, 0xef,
    0xfa, 0x53, 0x23, 0x5a, 0xff, 0xe3, 0xdf, 0xa1,
    0x0, 0x0, 0x0, 0x1, 0xaf, 0xd2, 0x60, 0x5,
    0xbe, 0xfe, 0xb5, 0x0, 0x52, 0x0, 0x1c, 0xff,
    0xfe, 0xff, 0xfc, 0x10, 0x0, 0x2, 0xec, 0x40,
    0x0, 0x4c, 0xe2, 0x0, 0x0, 0x1, 0x0, 0x1,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xd6, 0x0,
    0x0, 0x0,

    /* U+F240 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x44, 0x44, 0x44, 0x44, 0x4f, 0xdf, 0x8c,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0xcf, 0xf8, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0x8, 0xff, 0x89, 0xcc,
    0xcc, 0xcc, 0xcc, 0xc3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F241 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x44, 0x44, 0x43, 0x0, 0x4f, 0xdf, 0x8c,
    0xff, 0xff, 0xff, 0xc0, 0x2, 0xcf, 0xf8, 0xcf,
    0xff, 0xff, 0xfc, 0x0, 0x8, 0xff, 0x89, 0xcc,
    0xcc, 0xcc, 0x90, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F242 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x44, 0x42, 0x0, 0x0, 0x4f, 0xdf, 0x8c,
    0xff, 0xff, 0x80, 0x0, 0x2, 0xcf, 0xf8, 0xcf,
    0xff, 0xf8, 0x0, 0x0, 0x8, 0xff, 0x89, 0xcc,
    0xcc, 0x60, 0x0, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F243 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x41, 0x0, 0x0, 0x0, 0x4f, 0xdf, 0x8c,
    0xff, 0x40, 0x0, 0x0, 0x2, 0xcf, 0xf8, 0xcf,
    0xf4, 0x0, 0x0, 0x0, 0x8, 0xff, 0x89, 0xcc,
    0x30, 0x0, 0x0, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F244 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xdf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xcf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x25, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xcb, 0xfe, 0x0, 0x0, 0x0,
    0x1, 0x0, 0xd, 0x10, 0x42, 0x0, 0x0, 0x0,
    0x9f, 0xd1, 0x68, 0x0, 0x0, 0x0, 0x68, 0x0,
    0xff, 0xfe, 0xee, 0xed, 0xdd, 0xdd, 0xef, 0xc0,
    0x9f, 0xd1, 0x0, 0xb3, 0x0, 0x0, 0x68, 0x0,
    0x1, 0x0, 0x0, 0x3b, 0x5, 0x74, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xbe, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2d, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x34, 0x20, 0x0, 0x0, 0x6e, 0xfe,
    0xfd, 0x20, 0x4, 0xff, 0xf3, 0xff, 0xd0, 0xc,
    0xff, 0xf0, 0x4f, 0xf5, 0xf, 0xd5, 0xf2, 0x95,
    0xf8, 0x2f, 0xf7, 0x41, 0x3c, 0xfa, 0x3f, 0xff,
    0x60, 0xaf, 0xfb, 0x3f, 0xfe, 0x20, 0x4f, 0xfb,
    0x2f, 0xe2, 0x92, 0x75, 0xfa, 0xf, 0xeb, 0xf1,
    0x49, 0xf8, 0x9, 0xff, 0xf0, 0x9f, 0xf2, 0x1,
    0xdf, 0xf9, 0xff, 0x90, 0x0, 0x6, 0xab, 0x95,
    0x0,

    /* U+F2ED "" */
    0x0, 0x4, 0x88, 0x70, 0x0, 0xb, 0xcc, 0xff,
    0xff, 0xdc, 0xc5, 0xbc, 0xcc, 0xcc, 0xcc, 0xcc,
    0x52, 0x88, 0x88, 0x88, 0x88, 0x60, 0x4f, 0xff,
    0xff, 0xff, 0xfc, 0x4, 0xfa, 0xae, 0x6f, 0x5f,
    0xc0, 0x4f, 0xaa, 0xe6, 0xf4, 0xfc, 0x4, 0xfa,
    0xae, 0x6f, 0x4f, 0xc0, 0x4f, 0xaa, 0xe6, 0xf4,
    0xfc, 0x4, 0xfa, 0xae, 0x6f, 0x4f, 0xc0, 0x4f,
    0xaa, 0xe6, 0xf5, 0xfc, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x6, 0x88, 0x88, 0x88, 0x72, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x71, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xd1, 0x0, 0x0, 0x0,
    0x1, 0x5f, 0xff, 0xc0, 0x0, 0x0, 0x2, 0xea,
    0x5f, 0xfd, 0x0, 0x0, 0x2, 0xef, 0xfa, 0x5d,
    0x20, 0x0, 0x2, 0xef, 0xff, 0xf8, 0x0, 0x0,
    0x2, 0xef, 0xff, 0xfe, 0x20, 0x0, 0x2, 0xef,
    0xff, 0xfe, 0x20, 0x0, 0x2, 0xef, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0xbf, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0xd, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x6, 0x64,
    0x10, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x5, 0xef, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5,
    0xff, 0xff, 0x91, 0xdd, 0x19, 0xff, 0xf5, 0xff,
    0xff, 0xfd, 0x11, 0x11, 0xdf, 0xff, 0xef, 0xff,
    0xff, 0xfb, 0x0, 0xbf, 0xff, 0xf5, 0xff, 0xff,
    0xfd, 0x11, 0x11, 0xdf, 0xff, 0x5, 0xff, 0xff,
    0x91, 0xdd, 0x19, 0xff, 0xf0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x4, 0xef, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F7C2 "" */
    0x0, 0x17, 0x88, 0x87, 0x20, 0x2d, 0xff, 0xff,
    0xfd, 0x2e, 0xa0, 0xb3, 0x78, 0xfe, 0xfa, 0xb,
    0x37, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xfc, 0x4, 0x44,
    0x44, 0x44, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xf0, 0x0, 0x69, 0x0,
    0x0, 0x0, 0xdf, 0x0, 0x7f, 0xc0, 0x0, 0x0,
    0xd, 0xf0, 0x8f, 0xff, 0xdd, 0xdd, 0xdd, 0xff,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xb,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0
};

/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 52, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 51, .box_w = 3, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14, .adv_w = 75, .box_w = 4, .box_h = 4, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 22, .adv_w = 135, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 63, .adv_w = 119, .box_w = 7, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 109, .adv_w = 162, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 154, .adv_w = 132, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 199, .adv_w = 40, .box_w = 2, .box_h = 4, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 203, .adv_w = 65, .box_w = 3, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 223, .adv_w = 65, .box_w = 3, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 243, .adv_w = 77, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 256, .adv_w = 112, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 277, .adv_w = 44, .box_w = 3, .box_h = 4, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 283, .adv_w = 74, .box_w = 4, .box_h = 2, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 287, .adv_w = 44, .box_w = 3, .box_h = 2, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 290, .adv_w = 68, .box_w = 6, .box_h = 13, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 329, .adv_w = 128, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 365, .adv_w = 71, .box_w = 4, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 383, .adv_w = 110, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 415, .adv_w = 110, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 447, .adv_w = 128, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 483, .adv_w = 110, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 515, .adv_w = 118, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 551, .adv_w = 115, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 583, .adv_w = 124, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 619, .adv_w = 118, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 651, .adv_w = 44, .box_w = 3, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 662, .adv_w = 44, .box_w = 3, .box_h = 9, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 676, .adv_w = 112, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 701, .adv_w = 112, .box_w = 7, .box_h = 5, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 719, .adv_w = 112, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 744, .adv_w = 110, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 776, .adv_w = 199, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 848, .adv_w = 141, .box_w = 10, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 893, .adv_w = 145, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 929, .adv_w = 139, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 970, .adv_w = 159, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1011, .adv_w = 129, .box_w = 7, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1043, .adv_w = 122, .box_w = 7, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1075, .adv_w = 148, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1116, .adv_w = 156, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1152, .adv_w = 60, .box_w = 2, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1161, .adv_w = 98, .box_w = 6, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1188, .adv_w = 138, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1224, .adv_w = 114, .box_w = 7, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1256, .adv_w = 183, .box_w = 10, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1301, .adv_w = 156, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1337, .adv_w = 161, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1382, .adv_w = 139, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1418, .adv_w = 161, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1478, .adv_w = 140, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1514, .adv_w = 119, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1546, .adv_w = 113, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1578, .adv_w = 152, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1614, .adv_w = 137, .box_w = 10, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1659, .adv_w = 216, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1722, .adv_w = 129, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1758, .adv_w = 124, .box_w = 9, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1799, .adv_w = 126, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1835, .adv_w = 64, .box_w = 3, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 1855, .adv_w = 68, .box_w = 6, .box_h = 13, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 1894, .adv_w = 64, .box_w = 3, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1914, .adv_w = 112, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 1935, .adv_w = 96, .box_w = 6, .box_h = 1, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1938, .adv_w = 115, .box_w = 4, .box_h = 2, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 1942, .adv_w = 115, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1967, .adv_w = 131, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2002, .adv_w = 110, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2027, .adv_w = 131, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2067, .adv_w = 118, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2092, .adv_w = 68, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2117, .adv_w = 132, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2157, .adv_w = 131, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2192, .adv_w = 54, .box_w = 3, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2207, .adv_w = 55, .box_w = 5, .box_h = 13, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 2240, .adv_w = 118, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2275, .adv_w = 54, .box_w = 2, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2285, .adv_w = 203, .box_w = 11, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2324, .adv_w = 131, .box_w = 7, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2349, .adv_w = 122, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2377, .adv_w = 131, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2412, .adv_w = 131, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2452, .adv_w = 79, .box_w = 4, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2466, .adv_w = 96, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2487, .adv_w = 79, .box_w = 5, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2510, .adv_w = 130, .box_w = 7, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2535, .adv_w = 107, .box_w = 8, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2563, .adv_w = 173, .box_w = 11, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2602, .adv_w = 106, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2627, .adv_w = 107, .box_w = 8, .box_h = 10, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 2667, .adv_w = 100, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2688, .adv_w = 67, .box_w = 4, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2714, .adv_w = 57, .box_w = 2, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2727, .adv_w = 67, .box_w = 4, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2753, .adv_w = 112, .box_w = 7, .box_h = 2, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 2760, .adv_w = 80, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 2773, .adv_w = 60, .box_w = 3, .box_h = 3, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 2778, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2856, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2910, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2976, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3030, .adv_w = 132, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3071, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3149, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3227, .adv_w = 216, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3304, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3382, .adv_w = 216, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3445, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3523, .adv_w = 96, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3553, .adv_w = 144, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3598, .adv_w = 216, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3689, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3743, .adv_w = 168, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 3791, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3863, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3924, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3985, .adv_w = 168, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 4033, .adv_w = 168, .box_w = 12, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 4099, .adv_w = 120, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4138, .adv_w = 120, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4177, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4238, .adv_w = 168, .box_w = 11, .box_h = 3, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 4255, .adv_w = 216, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4318, .adv_w = 240, .box_w = 16, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4422, .adv_w = 216, .box_w = 15, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 4520, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4586, .adv_w = 168, .box_w = 11, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 4625, .adv_w = 168, .box_w = 11, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 4664, .adv_w = 240, .box_w = 16, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 4744, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4798, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4876, .adv_w = 192, .box_w = 13, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 4961, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5022, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5094, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5155, .adv_w = 120, .box_w = 9, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5214, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5286, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5358, .adv_w = 216, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5421, .adv_w = 192, .box_w = 14, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5512, .adv_w = 144, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5571, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5661, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5729, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5797, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5865, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5933, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6001, .adv_w = 240, .box_w = 16, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6089, .adv_w = 168, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6154, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6226, .adv_w = 192, .box_w = 13, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6311, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6379, .adv_w = 144, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6438, .adv_w = 193, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x1f72, 0xef51, 0xef58, 0xef5b, 0xef5c, 0xef5d, 0xef61,
    0xef63, 0xef65, 0xef69, 0xef6c, 0xef71, 0xef76, 0xef77, 0xef78,
    0xef8e, 0xef98, 0xef9b, 0xef9c, 0xef9d, 0xefa1, 0xefa2, 0xefa3,
    0xefa4, 0xefb7, 0xefb8, 0xefbe, 0xefc0, 0xefc1, 0xefc4, 0xefc7,
    0xefc8, 0xefc9, 0xefcb, 0xefe3, 0xefe5, 0xf014, 0xf015, 0xf017,
    0xf037, 0xf03a, 0xf043, 0xf06c, 0xf074, 0xf0ab, 0xf13b, 0xf190,
    0xf191, 0xf192, 0xf193, 0xf194, 0xf1d7, 0xf1e3, 0xf23d, 0xf254,
    0xf4aa, 0xf712, 0xf7f2
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 176, .range_length = 63475, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 59, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/

/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 0, 13, 14, 15, 16, 17,
    18, 19, 12, 20, 20, 0, 0, 0,
    21, 22, 23, 24, 25, 22, 26, 27,
    28, 29, 29, 30, 31, 32, 29, 29,
    22, 33, 34, 35, 3, 36, 30, 37,
    37, 38, 39, 40, 41, 42, 43, 0,
    44, 0, 45, 46, 47, 48, 49, 50,
    51, 45, 52, 52, 53, 48, 45, 45,
    46, 46, 54, 55, 56, 57, 51, 58,
    58, 59, 58, 60, 41, 0, 0, 9,
    61, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 12,
    18, 19, 20, 21, 21, 0, 0, 0,
    22, 23, 24, 25, 23, 25, 25, 25,
    23, 25, 25, 26, 25, 25, 25, 25,
    23, 25, 23, 25, 3, 27, 28, 29,
    29, 30, 31, 32, 33, 34, 35, 0,
    36, 0, 37, 38, 39, 39, 39, 0,
    39, 38, 40, 41, 38, 38, 42, 42,
    39, 42, 39, 42, 43, 44, 45, 46,
    46, 47, 46, 48, 0, 0, 35, 9,
    49, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 2, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 9, 0, 5, -4, 0, 0,
    0, 0, -11, -12, 1, 9, 4, 3,
    -8, 1, 9, 1, 8, 2, 6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 12, 2, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 4, 0, -6, 0, 0, 0, 0,
    0, -4, 3, 4, 0, 0, -2, 0,
    -1, 2, 0, -2, 0, -2, -1, -4,
    0, 0, 0, 0, -2, 0, 0, -2,
    -3, 0, 0, -2, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    -2, 0, -3, 0, -5, 0, -23, 0,
    0, -4, 0, 4, 6, 0, 0, -4,
    2, 2, 6, 4, -3, 4, 0, 0,
    -11, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -5, -2, -9, 0, -8,
    -1, 0, 0, 0, 0, 0, 7, 0,
    -6, -2, -1, 1, 0, -3, 0, 0,
    -1, -14, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -15, -2, 7,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -8, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 6,
    0, 2, 0, 0, -4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 7, 2,
    1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -7, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 1,
    4, 2, 6, -2, 0, 0, 4, -2,
    -6, -26, 1, 5, 4, 0, -2, 0,
    7, 0, 6, 0, 6, 0, -18, 0,
    -2, 6, 0, 6, -2, 4, 2, 0,
    0, 1, -2, 0, 0, -3, 15, 0,
    15, 0, 6, 0, 8, 2, 3, 6,
    0, 0, 0, -7, 0, 0, 0, 0,
    1, -1, 0, 1, -3, -2, -4, 1,
    0, -2, 0, 0, 0, -8, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -12, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, -11, 0, -12, 0, 0, 0,
    0, -1, 0, 19, -2, -2, 2, 2,
    -2, 0, -2, 2, 0, 0, -10, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -19, 0, 2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -12, 0, 12, 0, 0, -7, 0,
    6, 0, -13, -19, -13, -4, 6, 0,
    0, -13, 0, 2, -4, 0, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 5, 6, -23, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 9, 0, 1, 0, 0, 0,
    0, 0, 1, 1, -2, -4, 0, -1,
    -1, -2, 0, 0, -1, 0, 0, 0,
    -4, 0, -2, 0, -4, -4, 0, -5,
    -6, -6, -4, 0, -4, 0, -4, 0,
    0, 0, 0, -2, 0, 0, 2, 0,
    1, -2, 0, 1, 0, 0, 0, 2,
    -1, 0, 0, 0, -1, 2, 2, -1,
    0, 0, 0, -4, 0, -1, 0, 0,
    0, 0, 0, 1, 0, 2, -1, 0,
    -2, 0, -3, 0, 0, -1, 0, 6,
    0, 0, -2, 0, 0, 0, 0, 0,
    -1, 1, -1, -1, 0, 0, -2, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, -1, 0, -2, -2, 0,
    0, 0, 0, 0, 1, 0, 0, -1,
    0, -2, -2, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -1, 0, 0,
    0, 0, -1, -2, 0, -3, 0, -6,
    -1, -6, 4, 0, 0, -4, 2, 4,
    5, 0, -5, -1, -2, 0, -1, -9,
    2, -1, 1, -10, 2, 0, 0, 1,
    -10, 0, -10, -2, -17, -1, 0, -10,
    0, 4, 5, 0, 2, 0, 0, 0,
    0, 0, 0, -3, -2, 0, -6, 0,
    0, 0, -2, 0, 0, 0, -2, 0,
    0, 0, 0, 0, -1, -1, 0, -1,
    -2, 0, 0, 0, 0, 0, 0, 0,
    -2, -2, 0, -1, -2, -2, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, -2, 0, -2,
    0, -1, 0, -4, 2, 0, 0, -2,
    1, 2, 2, 0, 0, 0, 0, 0,
    0, -1, 0, 0, 0, 0, 0, 1,
    0, 0, -2, 0, -2, -1, -2, 0,
    0, 0, 0, 0, 0, 0, 2, 0,
    -2, 0, 0, 0, 0, -2, -3, 0,
    -4, 0, 6, -1, 1, -6, 0, 0,
    5, -10, -10, -8, -4, 2, 0, -2,
    -12, -3, 0, -3, 0, -4, 3, -3,
    -12, 0, -5, 0, 0, 1, -1, 2,
    -1, 0, 2, 0, -6, -7, 0, -10,
    -5, -4, -5, -6, -2, -5, 0, -4,
    -5, 1, 0, 1, 0, -2, 0, 0,
    0, 1, 0, 2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    0, -1, 0, -1, -2, 0, -3, -4,
    -4, -1, 0, -6, 0, 0, 0, 0,
    0, 0, -2, 0, 0, 0, 0, 1,
    -1, 0, 0, 0, 2, 0, 0, 0,
    0, 0, 0, 0, 0, 9, 0, 0,
    0, 0, 0, 0, 1, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    -4, 0, 0, 0, 0, -10, -6, 0,
    0, 0, -3, -10, 0, 0, -2, 2,
    0, -5, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 2, 0, -3, 0,
    0, 0, 0, 2, 0, 1, -4, -4,
    0, -2, -2, -2, 0, 0, 0, 0,
    0, 0, -6, 0, -2, 0, -3, -2,
    0, -4, -5, -6, -2, 0, -4, 0,
    -6, 0, 0, 0, 0, 15, 0, 0,
    1, 0, 0, -2, 0, 2, 0, -8,
    0, 0, 0, 0, 0, -18, -3, 6,
    6, -2, -8, 0, 2, -3, 0, -10,
    -1, -2, 2, -13, -2, 2, 0, 3,
    -7, -3, -7, -6, -8, 0, 0, -12,
    0, 11, 0, 0, -1, 0, 0, 0,
    -1, -1, -2, -5, -6, 0, -18, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, -1, -2, -3, 0, 0,
    -4, 0, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -4, 0, 0, 4,
    -1, 2, 0, -4, 2, -1, -1, -5,
    -2, 0, -2, -2, -1, 0, -3, -3,
    0, 0, -2, -1, -1, -3, -2, 0,
    0, -2, 0, 2, -1, 0, -4, 0,
    0, 0, -4, 0, -3, 0, -3, -3,
    2, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 2, 0, -3, 0, -1, -2,
    -6, -1, -1, -1, -1, -1, -2, -1,
    0, 0, 0, 0, 0, -2, -2, -2,
    0, 0, 0, 0, 2, -1, 0, -1,
    0, 0, 0, -1, -2, -1, -2, -2,
    -2, 0, 2, 8, -1, 0, -5, 0,
    -1, 4, 0, -2, -8, -2, 3, 0,
    0, -9, -3, 2, -3, 1, 0, -1,
    -2, -6, 0, -3, 1, 0, 0, -3,
    0, 0, 0, 2, 2, -4, -4, 0,
    -3, -2, -3, -2, -2, 0, -3, 1,
    -4, -3, 6, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, -2,
    0, 0, -2, -2, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, -1, 0, 0,
    0, 0, 0, -1, 0, 0, 0, 0,
    -3, 0, -4, 0, 0, 0, -6, 0,
    1, -4, 4, 0, -1, -9, 0, 0,
    -4, -2, 0, -8, -5, -5, 0, 0,
    -8, -2, -8, -7, -9, 0, -5, 0,
    2, 13, -2, 0, -4, -2, -1, -2,
    -3, -5, -3, -7, -8, -4, -2, 0,
    0, -1, 0, 1, 0, 0, -13, -2,
    6, 4, -4, -7, 0, 1, -6, 0,
    -10, -1, -2, 4, -18, -2, 1, 0,
    0, -12, -2, -10, -2, -14, 0, 0,
    -13, 0, 11, 1, 0, -1, 0, 0,
    0, 0, -1, -1, -7, -1, 0, -12,
    0, 0, 0, 0, -6, 0, -2, 0,
    -1, -5, -9, 0, 0, -1, -3, -6,
    -2, 0, -1, 0, 0, 0, 0, -9,
    -2, -6, -6, -2, -3, -5, -2, -3,
    0, -4, -2, -6, -3, 0, -2, -4,
    -2, -4, 0, 1, 0, -1, -6, 0,
    4, 0, -3, 0, 0, 0, 0, 2,
    0, 1, -4, 8, 0, -2, -2, -2,
    0, 0, 0, 0, 0, 0, -6, 0,
    -2, 0, -3, -2, 0, -4, -5, -6,
    -2, 0, -4, 2, 8, 0, 0, 0,
    0, 15, 0, 0, 1, 0, 0, -2,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -1, -4, 0, 0, 0, 0, 0, -1,
    0, 0, 0, -2, -2, 0, 0, -4,
    -2, 0, 0, -4, 0, 3, -1, 0,
    0, 0, 0, 0, 0, 1, 0, 0,
    0, 0, 3, 4, 2, -2, 0, -6,
    -3, 0, 6, -6, -6, -4, -4, 8,
    3, 2, -17, -1, 4, -2, 0, -2,
    2, -2, -7, 0, -2, 2, -2, -2,
    -6, -2, 0, 0, 6, 4, 0, -5,
    0, -11, -2, 6, -2, -7, 1, -2,
    -6, -6, -2, 8, 2, 0, -3, 0,
    -5, 0, 2, 6, -4, -7, -8, -5,
    6, 0, 1, -14, -2, 2, -3, -1,
    -4, 0, -4, -7, -3, -3, -2, 0,
    0, -4, -4, -2, 0, 6, 4, -2,
    -11, 0, -11, -3, 0, -7, -11, -1,
    -6, -3, -6, -5, 5, 0, 0, -2,
    0, -4, -2, 0, -2, -3, 0, 3,
    -6, 2, 0, 0, -10, 0, -2, -4,
    -3, -1, -6, -5, -6, -4, 0, -6,
    -2, -4, -4, -6, -2, 0, 0, 1,
    9, -3, 0, -6, -2, 0, -2, -4,
    -4, -5, -5, -7, -2, -4, 4, 0,
    -3, 0, -10, -2, 1, 4, -6, -7,
    -4, -6, 6, -2, 1, -18, -3, 4,
    -4, -3, -7, 0, -6, -8, -2, -2,
    -2, -2, -4, -6, -1, 0, 0, 6,
    5, -1, -12, 0, -12, -4, 5, -7,
    -13, -4, -7, -8, -10, -6, 4, 0,
    0, 0, 0, -2, 0, 0, 2, -2,
    4, 1, -4, 4, 0, 0, -6, -1,
    0, -1, 0, 1, 1, -2, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 2, 6, 0, 0, -2, 0, 0,
    0, 0, -1, -1, -2, 0, 0, 0,
    1, 2, 0, 0, 0, 0, 2, 0,
    -2, 0, 7, 0, 3, 1, 1, -2,
    0, 4, 0, 0, 0, 2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 6, 0, 5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -12, 0, -2, 3, 0, 6,
    0, 0, 19, 2, -4, -4, 2, 2,
    -1, 1, -10, 0, 0, 9, -12, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -13, 7, 27, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -12, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, -4,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, -5, 0,
    0, 1, 0, 0, 2, 25, -4, -2,
    6, 5, -5, 2, 0, 0, 2, 2,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -25, 5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    0, 0, 0, -5, 0, 0, 0, 0,
    -4, -1, 0, 0, 0, -4, 0, -2,
    0, -9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -13, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, -2, 0, 0, -4, 0, -3, 0,
    -5, 0, 0, 0, -3, 2, -2, 0,
    0, -5, -2, -4, 0, 0, -5, 0,
    -2, 0, -9, 0, -2, 0, 0, -16,
    -4, -8, -2, -7, 0, 0, -13, 0,
    -5, -1, 0, 0, 0, 0, 0, 0,
    0, 0, -3, -3, -2, -3, 0, 0,
    0, 0, -4, 0, -4, 2, -2, 4,
    0, -1, -4, -1, -3, -4, 0, -2,
    -1, -1, 1, -5, -1, 0, 0, 0,
    -17, -2, -3, 0, -4, 0, -1, -9,
    -2, 0, 0, -1, -2, 0, 0, 0,
    0, 1, 0, -1, -3, -1, 3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 2, 0, 0, 0, 0, 0,
    0, -4, 0, -1, 0, 0, 0, -4,
    2, 0, 0, 0, -5, -2, -4, 0,
    0, -5, 0, -2, 0, -9, 0, 0,
    0, 0, -19, 0, -4, -7, -10, 0,
    0, -13, 0, -1, -3, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -3, -1,
    -3, 1, 0, 0, 3, -2, 0, 6,
    9, -2, -2, -6, 2, 9, 3, 4,
    -5, 2, 8, 2, 6, 4, 5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 12, 9, -3, -2, 0, -2,
    15, 8, 15, 0, 0, 0, 2, 0,
    0, 7, 0, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -1, 0,
    0, 0, 0, 0, 0, 0, 0, 3,
    0, 0, 0, 0, -16, -2, -2, -8,
    -9, 0, 0, -13, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    3, 0, 0, 0, 0, -16, -2, -2,
    -8, -9, 0, 0, -8, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, -4, 2, 0, -2,
    2, 3, 2, -6, 0, 0, -2, 2,
    0, 2, 0, 0, 0, 0, -5, 0,
    -2, -1, -4, 0, -2, -8, 0, 12,
    -2, 0, -4, -1, 0, -1, -3, 0,
    -2, -5, -4, -2, 0, 0, 0, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -1, 0, 0, 0, 0, 0, 0,
    0, 0, 3, 0, 0, 0, 0, -16,
    -2, -2, -8, -9, 0, 0, -13, 0,
    0, 0, 0, 0, 0, 10, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -3, 0, -6, -2, -2, 6, -2, -2,
    -8, 1, -1, 1, -1, -5, 0, 4,
    0, 2, 1, 2, -5, -8, -2, 0,
    -7, -4, -5, -8, -7, 0, -3, -4,
    -2, -2, -2, -1, -2, -1, 0, -1,
    -1, 3, 0, 3, -1, 0, 6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -1, -2, -2, 0, 0,
    -5, 0, -1, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -12, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, -2, 0, -2,
    0, 0, 0, 0, -2, 0, 0, -3,
    -2, 2, 0, -3, -4, -1, 0, -6,
    -1, -4, -1, -2, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -13, 0, 6, 0, 0, -3, 0,
    0, 0, 0, -2, 0, -2, 0, 0,
    -1, 0, 0, -1, 0, -4, 0, 0,
    8, -2, -6, -6, 1, 2, 2, 0,
    -5, 1, 3, 1, 6, 1, 6, -1,
    -5, 0, 0, -8, 0, 0, -6, -5,
    0, 0, -4, 0, -2, -3, 0, -3,
    0, -3, 0, -1, 3, 0, -2, -6,
    -2, 7, 0, 0, -2, 0, -4, 0,
    0, 2, -4, 0, 2, -2, 2, 0,
    0, -6, 0, -1, -1, 0, -2, 2,
    -2, 0, 0, 0, -8, -2, -4, 0,
    -6, 0, 0, -9, 0, 7, -2, 0,
    -3, 0, 1, 0, -2, 0, -2, -6,
    0, -2, 2, 0, 0, 0, 0, -1,
    0, 0, 2, -2, 1, 0, 0, -2,
    -1, 0, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -12, 0, 4, 0,
    0, -2, 0, 0, 0, 0, 0, 0,
    -2, -2, 0, 0, 0, 4, 0, 4,
    0, 0, 0, 0, 0, -12, -11, 1,
    8, 6, 3, -8, 1, 8, 0, 7,
    0, 4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 10, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0
};

/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 61,
    .right_class_cnt     = 49,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

/*Store all the custom data of the font*/
static lv_font_fmt_txt_dsc_t font_dsc = {
    .glyph_bitmap = gylph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0
};

/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
lv_font_t lv_font_montserrat_12_subpx = {
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 15,          /*The maximum line height required by the font*/
    .base_line = 3,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0)
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};

#endif /*#if LV_FONT_MONTSERRAT_12_SUBPX*/
