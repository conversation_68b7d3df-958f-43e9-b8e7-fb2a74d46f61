--cpu Cortex-M4.fp
".\obj\main.o"
".\obj\stm32f4xx_it.o"
".\obj\system_stm32f4xx.o"
".\obj\stm32f4xx_hal_msp.o"
".\obj\startup_stm32f407xx.o"
".\obj\led.o"
".\obj\key.o"
".\obj\lcd.o"
".\obj\usart1.o"
".\obj\malloc.o"
".\obj\spi.o"
".\obj\w25qxx.o"
".\obj\tfcard_sdio.o"
".\obj\rtc.o"
".\obj\lunar_calendar.o"
".\obj\stm32f4xx_hal.o"
".\obj\stm32f4xx_hal_cortex.o"
".\obj\stm32f4xx_hal_gpio.o"
".\obj\stm32f4xx_hal_pwr.o"
".\obj\stm32f4xx_hal_pwr_ex.o"
".\obj\stm32f4xx_hal_rcc.o"
".\obj\stm32f4xx_hal_rcc_ex.o"
".\obj\stm32f4xx_hal_dma_ex.o"
".\obj\stm32f4xx_hal_dma.o"
".\obj\stm32f4xx_ll_fsmc.o"
".\obj\stm32f4xx_hal_sram.o"
".\obj\stm32f4xx_hal_usart.o"
".\obj\stm32f4xx_hal_uart.o"
".\obj\stm32f4xx_hal_sd.o"
".\obj\stm32f4xx_ll_sdmmc.o"
".\obj\stm32f4xx_hal_spi.o"
".\obj\stm32f4xx_hal_tim.o"
".\obj\stm32f4xx_hal_tim_ex.o"
".\obj\stm32f4xx_hal_rtc.o"
".\obj\stm32f4xx_hal_rtc_ex.o"
".\obj\diskio.o"
".\obj\ff.o"
".\obj\cc936.o"
"..\HanZi\HanZiUse.lib"
".\obj\common.o"
--library_type=microlib --strict --scatter ".\OBJ\RTC.sct"
--summary_stderr --info summarysizes --map --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\LIST\RTC.map" -o .\OBJ\RTC.axf