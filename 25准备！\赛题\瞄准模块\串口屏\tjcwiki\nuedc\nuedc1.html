<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>电赛快速入门 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="上位机使用入门" href="nuedc2.html" />
    <link rel="prev" title="电赛专题" href="index.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">入门</a><ul class="current">
<li class="toctree-l2 current"><a class="current reference internal" href="#">电赛快速入门</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#id2">1、串口屏</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id3">2、调试工具</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id4">3、连接电脑</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id5">4、上电</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id6">5、串口屏资料下载</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id7">6、驱动安装</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id9">7、开发软件安装</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="nuedc2.html">上位机使用入门</a></li>
<li class="toctree-l2"><a class="reference internal" href="nuedc3.html">串口屏调试入门</a></li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">电赛专题</a> &raquo;</li>
      <li>电赛快速入门</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>电赛快速入门<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<section id="id2">
<h2>1、串口屏<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>屏幕:推荐购买3.5寸及以上的尺寸</p>
</div>
<img alt="../_images/tjc1.jpg" src="../_images/tjc1.jpg" />
</section>
<section id="id3">
<h2>2、调试工具<a class="headerlink" href="#id3" title="此标题的永久链接"></a></h2>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>调试工具:官方调试工具的芯片为cp2102,推荐使用cp2102或者ft232芯片的USB转TTL工具</p>
</div>
<img alt="../_images/tool1.jpg" src="../_images/tool1.jpg" />
</section>
<section id="id4">
<h2>3、连接电脑<a class="headerlink" href="#id4" title="此标题的永久链接"></a></h2>
<img alt="../_images/tool2.jpg" src="../_images/tool2.jpg" />
</section>
<section id="id5">
<h2>4、上电<a class="headerlink" href="#id5" title="此标题的永久链接"></a></h2>
<p>正常上电后显示如下,如果是屏幕比较大的屏在开机时闪烁则是因为供电不足,则需要在DC供电口接上供电</p>
<img alt="../_images/tool3.jpg" src="../_images/tool3.jpg" />
</section>
<section id="id6">
<h2>5、串口屏资料下载<a class="headerlink" href="#id6" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="../download/moreProject/tjcwiki_resource_collection.html#id1"><span class="std std-ref">淘晶驰资料合集</span></a></p>
</section>
<section id="id7">
<h2>6、驱动安装<a class="headerlink" href="#id7" title="此标题的永久链接"></a></h2>
<p>右键此电脑（我的电脑）-&gt;设备-&gt;设备管理器-&gt;端口，如果已经装了驱动，会显示对应的端口号(如下图的com7)，如果没有显示，则需要安装对应的驱动，<a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tools/serial_port_driver.zip">常见串口驱动下载</a></p>
<img alt="../_images/port1.jpg" src="../_images/port1.jpg" />
</section>
<section id="id9">
<h2>7、开发软件安装<a class="headerlink" href="#id9" title="此标题的永久链接"></a></h2>
<p><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/USARTHMIsetup_latest.zip">点击此处下载上位机开发软件</a></p>
<p>解压后安装即可</p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="电赛专题" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="nuedc2.html" class="btn btn-neutral float-right" title="上位机使用入门" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>