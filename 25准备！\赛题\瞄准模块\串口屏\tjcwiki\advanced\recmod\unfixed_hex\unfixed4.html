<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>解析不定长hex协议-海陵科LD-2410生命存在感应模组 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../../../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../../../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../../../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../../../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../../../" id="documentation_options" src="../../../_static/documentation_options.js"></script>
        <script src="../../../_static/jquery.js"></script>
        <script src="../../../_static/underscore.js"></script>
        <script src="../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../../../_static/doctools.js"></script>
        <script src="../../../_static/translations.js"></script>
        <script src="../../../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../../../_static/custom.js"></script>
    <script src="../../../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../../../genindex.html" />
    <link rel="search" title="搜索" href="../../../search.html" />
    <link rel="next" title="解析字符串格式指令" href="../recmod_ascii/index.html" />
    <link rel="prev" title="传输不定长的字符串" href="unfixed3.html" />
    <link href="../../../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../../../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../../../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../../../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="../../index.html">串口屏高级应用详解</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../../keyboard/index.html">系统键盘</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="../index.html">主动解析模式应用详解</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="../recmod_base.html">主动解析基本知识</a></li>
<li class="toctree-l3"><a class="reference internal" href="../fixed_hex/index.html">解析定长hex格式指令-自定义协议</a></li>
<li class="toctree-l3 current"><a class="reference internal" href="index.html">解析不定长hex格式指令-自定义协议</a><ul class="current">
<li class="toctree-l4"><a class="reference internal" href="unfixed1.html">不定长-帧头为0x55 ，帧尾为3个0xff</a></li>
<li class="toctree-l4"><a class="reference internal" href="unfixed2.html">帧头为0xFF ，帧尾为0d 0a</a></li>
<li class="toctree-l4"><a class="reference internal" href="unfixed3.html">传输不定长的字符串</a></li>
<li class="toctree-l4 current"><a class="current reference internal" href="#">解析不定长hex协议-海陵科LD-2410生命存在感应模组</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../recmod_ascii/index.html">解析字符串格式指令</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../hmi_download_protocol.html">HMI下载协议详解/OTA升级</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../crc.html">串口指令增加CRC校验</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../crc2.html">程序中使用CRC校验数据</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../transmit_data.html">运行中串口传输文件到内存或SD卡</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../SDcard.html">SD卡读写文件流程</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../expandIO.html">拓展IO资料</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../get_property.html">获取控件自身的属性</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../wepo_repo.html">读写用户存储区</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../getv.html">获取串口屏设备唯一序列号</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="../../index.html">串口屏高级应用详解</a> &raquo;</li>
          <li><a href="../index.html">主动解析模式应用详解</a> &raquo;</li>
          <li><a href="index.html">解析不定长hex格式指令-自定义协议</a> &raquo;</li>
      <li>解析不定长hex协议-海陵科LD-2410生命存在感应模组</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="hex-ld-2410">
<h1>解析不定长hex协议-海陵科LD-2410生命存在感应模组<a class="headerlink" href="#hex-ld-2410" title="此标题的永久链接"></a></h1>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>每个页面仅建议在一个定时器中读取串口缓冲区的数据,在多个定时器中读取串口缓冲区的数据容易照成逻辑混乱。</p>
</div>
<p>海陵科LD-2410生命存在感应模组的协议如下所示</p>
<img alt="../../../_images/recmod_hex_1.jpg" src="../../../_images/recmod_hex_1.jpg" />
<p>program.s中的配置如图所示</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="o">//</span><span class="n">以下代码只在上电时运行一次</span><span class="p">,</span><span class="n">一般用于全局变量定义和上电初始化数据</span>
<span class="linenos"> 2</span><span class="o">//</span><span class="n">全局变量定义目前仅支持4字节有符号整形</span><span class="p">(</span><span class="nb">int</span><span class="p">),</span><span class="n">不支持其他类型的全局变量声明</span><span class="p">,</span><span class="n">如需使用字符串类型可以在页面中使用变量控件来实现</span>
<span class="linenos"> 3</span><span class="nb">int</span> <span class="n">sys0</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span><span class="n">sys1</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span><span class="n">sys2</span><span class="o">=</span><span class="mi">0</span>
<span class="linenos"> 4</span>
<span class="linenos"> 5</span><span class="nb">int</span> <span class="n">length</span><span class="p">,</span><span class="n">totalLength</span><span class="p">,</span><span class="n">getFrameHead</span>
<span class="linenos"> 6</span>
<span class="linenos"> 7</span><span class="o">//</span><span class="n">波特率256000</span>
<span class="linenos"> 8</span><span class="n">bauds</span><span class="o">=</span><span class="mi">256000</span>
<span class="linenos"> 9</span>
<span class="linenos">10</span><span class="o">//</span><span class="n">打开主动解析</span>
<span class="linenos">11</span><span class="n">recmod</span><span class="o">=</span><span class="mi">1</span>
<span class="linenos">12</span><span class="n">page</span> <span class="mi">0</span>                       <span class="o">//</span><span class="n">上电刷新第0页</span>
</pre></div>
</div>
<p>界面布局如下图所示</p>
<img alt="../../../_images/recmod_hex_2.jpg" src="../../../_images/recmod_hex_2.jpg" />
<hr class="docutils" />
<p>解析定时器（tim为50）中的代码如下图所示</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span>while(usize&gt;=10&amp;&amp;getFrameHead==0)
<span class="linenos"> 2</span>{
<span class="linenos"> 3</span>  if(u[0]==0xF4&amp;&amp;u[1]==0xF3&amp;&amp;u[2]==0xF2&amp;&amp;u[3]==0xF1)
<span class="linenos"> 4</span>  {
<span class="linenos"> 5</span>    //找到帧头,退出循环
<span class="linenos"> 6</span>    getFrameHead=1
<span class="linenos"> 7</span>    length=0
<span class="linenos"> 8</span>    ucopy length,4,2,0
<span class="linenos"> 9</span>    totalLength=length+10
<span class="linenos">10</span>  }else
<span class="linenos">11</span>  {
<span class="linenos">12</span>    //如果帧头不对，就一直删除1个字节，直到不满足条件退出循环
<span class="linenos">13</span>    udelete 1
<span class="linenos">14</span>  }
<span class="linenos">15</span>}
<span class="linenos">16</span>if(getFrameHead==1)
<span class="linenos">17</span>{
<span class="linenos">18</span>  if(usize&gt;=totalLength)
<span class="linenos">19</span>  {
<span class="linenos">20</span>    if(u[totalLength-4]==0xF8&amp;&amp;u[totalLength-3]==0xF7&amp;&amp;u[totalLength-2]==0xF6&amp;&amp;u[totalLength-1]==0xF5)
<span class="linenos">21</span>    {
<span class="linenos">22</span>      if(u[6]==0x02&amp;&amp;u[7]==0xAA&amp;&amp;u[17]==0x55&amp;&amp;u[18]==0x00)
<span class="linenos">23</span>      {
<span class="linenos">24</span>        if(u[8]==0x00)
<span class="linenos">25</span>        {
<span class="linenos">26</span>          t0.txt=&quot;无目标&quot;
<span class="linenos">27</span>        }else if(u[8]==0x01)
<span class="linenos">28</span>        {
<span class="linenos">29</span>          t0.txt=&quot;运动目标&quot;
<span class="linenos">30</span>          ucopy distance.val,9,2,0
<span class="linenos">31</span>          ucopy energy.val,11,1,0
<span class="linenos">32</span>        }else if(u[8]==0x02)
<span class="linenos">33</span>        {
<span class="linenos">34</span>          t0.txt=&quot;静止目标&quot;
<span class="linenos">35</span>          ucopy distance.val,12,2,0
<span class="linenos">36</span>          ucopy energy.val,14,1,0
<span class="linenos">37</span>        }else if(u[8]==0x03)
<span class="linenos">38</span>        {
<span class="linenos">39</span>          t0.txt=&quot;运动&amp;静止目标&quot;
<span class="linenos">40</span>          ucopy distance.val,15,2,0
<span class="linenos">41</span>        }
<span class="linenos">42</span>      }
<span class="linenos">43</span>      udelete totalLength
<span class="linenos">44</span>    }else
<span class="linenos">45</span>    {
<span class="linenos">46</span>      udelete 4
<span class="linenos">47</span>    }
<span class="linenos">48</span>    getFrameHead=0
<span class="linenos">49</span>  }
<span class="linenos">50</span>}
</pre></div>
</div>
<hr class="docutils" />
<p>下面对代码进行逐行讲解</p>
<p>program.s中定义的变量</p>
<p>length：获取帧内数据长度</p>
<p>totalLength：通过length计算出一整帧的数据长度</p>
<p>getFrameHead：是否获取到帧头的标志位，1为获取到帧头，0为未获取到帧头</p>
<p>第1行为什么是判断usize&gt;=10</p>
<img alt="../../../_images/recmod_hex_3.jpg" src="../../../_images/recmod_hex_3.jpg" />
<p>当帧内数据长度为 00 00 时，理论上这是长度是最短的一帧,此时数据变成了</p>
<p>F4 F3 F2 F1 00 00 F8 F7 F6 F5</p>
<p>此时共有10个字节，因此要求进入解析时应该最少大于等于最短的一帧数据</p>
<p>第3-14行是判断帧头是否符合协议中的要求，如果不是，则删除串口缓冲区最前面1字节，直到找到帧头或者usize小于10</p>
<p>如何退出循环：当getFrameHead!=0或者usize&lt;10,不满足while的条件，就会退出循环</p>
<p>第16行，判断是否获取到帧头，获取到了才会进入解析</p>
<p>第18行，判断当前串口缓冲区的数据是否大于完整1帧的长度</p>
<p>第20行，判断帧尾是否符合</p>
<p>第22行，判断02、0A、55、00这四个标志位是否符合</p>
<img alt="../../../_images/recmod_hex_4.jpg" src="../../../_images/recmod_hex_4.jpg" />
<p>第24行，通过第8位判断目标状态，随后将数据一一提取出来放入对应的控件中</p>
<img alt="../../../_images/recmod_hex_5.jpg" src="../../../_images/recmod_hex_5.jpg" />
<p>第43行，解析完毕，删除1帧的数据长度</p>
<p>第46行，帧头符合但是其他数据不符合，删除串口缓冲区前4字节（之前已经确认过是帧头），等待进入下一轮判断</p>
<p>第48行，解析完毕或者帧头已经被删除，将获取帧头标志清除</p>
<section id="hex">
<h2>海陵科生命存在感应模组hex协议解析下载<a class="headerlink" href="#hex" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/串口屏高级应用/主动解析/海陵科生命存在感应模组hex协议解析.HMI">《海陵科生命存在感应模组hex协议解析》演示工程下载</a></p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="unfixed3.html" class="btn btn-neutral float-left" title="传输不定长的字符串" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="../recmod_ascii/index.html" class="btn btn-neutral float-right" title="解析字符串格式指令" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>