# STM32F407双定时器独立控制引脚连接表

## 系统概述
- **主控**: STM32F407ZGT6
- **驱动器**: D36A双路步进电机驱动器  
- **电机**: 42步进电机 × 2
- **特色**: 双定时器独立频率控制（TIM8 + TIM1）

## 🔄 关键变更说明

### 唯一硬件改动
- **电机B的STEP信号**: 从 `PC9 (TIM8_CH4)` 改接到 `PA8 (TIM1_CH1)`
- **改动原因**: 实现真正的双电机独立频率控制，支持并行运动

## 📋 完整引脚连接表

### 电机A（水平轴）- 使用TIM8

| STM32F407引脚 | 功能说明 | D36A接口 | 备注 |
|---------------|----------|----------|------|
| PC8 | STEP-A (脉冲信号) | STEP-A | TIM8_CH3 PWM输出 |
| PD3 | DIR-A (方向控制) | DIR-A | GPIO输出，高/低电平控制方向 |
| PD2 | SLEEP-A (使能控制) | SLEEP-A | GPIO输出，高电平使能，低电平禁用 |
| GND | 地线 | GND | 共地连接 |

### 电机B（垂直轴）- 使用TIM1

| STM32F407引脚 | 功能说明 | D36A接口 | 备注 |
|---------------|----------|----------|------|
| **PA8** ✨ | STEP-B (脉冲信号) | STEP-B | **TIM1_CH1 PWM输出（新）** |
| PB12 | DIR-B (方向控制) | DIR-B | GPIO输出，高/低电平控制方向 |
| PC12 | SLEEP-B (使能控制) | SLEEP-B | GPIO输出，高电平使能，低电平禁用 |
| GND | 地线 | GND | 共地连接 |

### 系统外设

| STM32F407引脚 | 功能说明 | 连接目标 | 备注 |
|---------------|----------|----------|------|
| PA9 | USART1_TX | 串口调试器 | 115200bps |
| PA10 | USART1_RX | 串口调试器 | 115200bps |
| PC0 | ADC1_IN10 | 电压监测 | 11倍分压，监测12-24V |
| PB14 | KEY | 按键输入 | 下拉输入模式 |

### 供电接线

| 电源引脚 | 连接说明 | 电压范围 |
|----------|----------|----------|
| 12-24V电源正极 | D36A VCC | 推荐24V |
| 12-24V电源负极 | D36A GND | 共地 |
| STM32 3.3V | 由开发板提供 | 3.3V |

## 🛠️ D36A驱动器设置

### 拨码开关配置（推荐）
- **SW1-SW3 (细分设置)**: 000 = 1/16细分
- **SW4-SW6 (电流设置)**: 100 = 0.77A

### 电机接线（42步进电机）

| 电机线色 | D36A接口 | 说明 |
|----------|----------|------|
| 红色 | A+ | A相正极 |
| 蓝色 | A- | A相负极 |
| 绿色 | B+ | B相正极 |
| 黑色 | B- | B相负极 |

## 🎯 技术优势

### 双定时器独立控制优势
1. **完全独立的频率控制**
   - 电机A: TIM8控制，400-1500Hz中低速范围
   - 电机B: TIM1控制，50-200Hz超低速范围

2. **真正的并行运动**
   - 两个电机可以同时以不同速度运行
   - 无需时分复用，提高系统效率

3. **为竞赛优化**
   - 满足E题第三问4秒时限要求
   - 水平快速搜索 + 垂直精密调整

## 📊 验证测试点

### 示波器测量点
1. **PC8 (TIM8_CH3)**: 测量电机A的PWM频率
2. **PA8 (TIM1_CH1)**: 测量电机B的PWM频率
3. **PD2/PC12**: 验证SLEEP信号高电平使能逻辑

### 万用表测量点
1. **D36A VCC-GND**: 确认供电电压（12-24V）
2. **DIR引脚**: 测量方向控制电平
3. **SLEEP引脚**: 确认使能状态

## ⚠️ 注意事项

1. **接线顺序**：先断电，完成所有接线后再上电
2. **引脚确认**：PA8是STM32F407的TIM1_CH1默认复用引脚
3. **共地连接**：确保STM32和D36A驱动器共地
4. **电机线序**：严格按照颜色对应，错误会导致电机抖动
5. **调试建议**：先单独测试每个电机，再进行并行控制

## 🚀 快速验证步骤

1. **硬件连接**：将电机B的STEP线从PC9改接到PA8
2. **编译烧录**：使用Keil编译并烧录程序
3. **上电观察**：
   - 2秒后：电机A快速旋转，电机B慢速旋转（速度差明显）
   - 20秒后：速度交换，观察控制灵活性
   - 25秒后：同速运行，验证独立控制成功

---
**创建时间**: 2025-08-01  
**版本**: V2.0 - 双定时器独立控制版本