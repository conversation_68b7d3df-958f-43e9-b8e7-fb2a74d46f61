#include "Key.h"
#include "Delay.h"

static uint8_t key_pa5_last_state = KEY_RELEASED;

/**
  * Function: Key initialization
  * Parameter: None
  * Return: None
  */
void Key_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;

    /*Enable clocks*/
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE);     // Enable GPIOB clock
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);     // Enable GPIOA clock

    /*GPIO initialization for original key*/
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_1;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOB, &GPIO_InitStructure);                    // Initialize PB1 as pull-up input

    /*GPIO initialization for PA5 key*/
    GPIO_InitStructure.GPIO_Pin = KEY_PA5_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;             // Input with pull-up
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(KEY_PA5_PORT, &GPIO_InitStructure);
}

uint8_t Key_PA5_Read(void)
{
    return GPIO_ReadInputDataBit(KEY_PA5_PORT, KEY_PA5_PIN);
}

uint8_t Key_PA5_GetPressed(void)
{
    uint8_t current_state = Key_PA5_Read();
    uint8_t key_pressed = 0;

    // Detect falling edge (pressed) then rising edge (released)
    if (key_pa5_last_state == KEY_RELEASED && current_state == KEY_PRESSED) {
        Delay_ms(20);  // Simple debounce
        current_state = Key_PA5_Read();
        if (current_state == KEY_PRESSED) {
            // Wait for key release
            while (Key_PA5_Read() == KEY_PRESSED) {
                Delay_ms(10);
            }
            key_pressed = 1;
        }
    }

    key_pa5_last_state = current_state;
    return key_pressed;
}

/**
  * Function: Get key number
  * Parameter: None
  * Return: Key code value, range: 0~2, return 0 means no key pressed
  * Note: This function is blocking operation
  */
uint8_t Key_GetNum(void)
{
    uint8_t KeyNum = 0;     // Define variable, default key code is 0

    if (GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_1) == 0)         // Read PB1 input register status
    {
        Delay_ms(20);                                           // Delay for debounce
        while (GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_1) == 0); // Wait for key release
        Delay_ms(20);                                           // Delay for debounce
        KeyNum = 1;                                             // Set key code to 1
    }

    return KeyNum;          // Return key code value
}
