Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    startup_stm32f10x_md.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(.text) for Reset_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to bluetooth.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f10x_md.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_md.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(HEAP) for Heap_Mem
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(STACK) for Stack_Mem
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    delay.o(i.Delay_ms) refers to delay.o(i.Delay_us) for Delay_us
    delay.o(i.Delay_s) refers to delay.o(i.Delay_ms) for Delay_ms
    led.o(i.LED_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED_OFF) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED_ON) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    led.o(i.LED_Turn) refers to stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit) for GPIO_ReadOutputDataBit
    led.o(i.LED_Turn) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED_Turn) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    key.o(i.Key_ClearEvent) refers to key.o(.bss) for key_states
    key.o(i.Key_GetEvent) refers to key.o(.bss) for key_states
    key.o(i.Key_GetNum) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    key.o(i.Key_GetNum) refers to delay.o(i.Delay_ms) for Delay_ms
    key.o(i.Key_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    key.o(i.Key_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    key.o(i.Key_Init) refers to key.o(.bss) for key_states
    key.o(i.Key_IsClicked) refers to key.o(.bss) for key_states
    key.o(i.Key_IsPressed) refers to key.o(.bss) for key_states
    key.o(i.Key_IsReleased) refers to key.o(.bss) for key_states
    key.o(i.Key_ReadPin) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    key.o(i.Key_Scan) refers to key.o(i.Key_UpdateState) for Key_UpdateState
    key.o(i.Key_UpdateState) refers to key.o(i.Key_ReadPin) for Key_ReadPin
    key.o(i.Key_UpdateState) refers to key.o(.bss) for key_states
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_SendByte) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Start) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Stop) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_I2C_Init) for OLED_I2C_Init
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_SetCursor) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for OLED_F8x16
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    servo.o(i.Servo_AngleToPosition) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    servo.o(i.Servo_AngleToPosition) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    servo.o(i.Servo_AngleToPosition) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    servo.o(i.Servo_AngleToPosition) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    servo.o(i.Servo_AngleToPosition) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    servo.o(i.Servo_GetStatus) refers to servo.o(i.Servo_ReadPosition) for Servo_ReadPosition
    servo.o(i.Servo_GetStatus) refers to servo.o(i.Servo_AngleToPosition) for Servo_AngleToPosition
    servo.o(i.Servo_GetStatus) refers to servo.o(i.Servo_ReadTemperature) for Servo_ReadTemperature
    servo.o(i.Servo_GetStatus) refers to servo.o(i.Servo_ReadVoltage) for Servo_ReadVoltage
    servo.o(i.Servo_GetStatus) refers to servo.o(i.Servo_SendCommand) for Servo_SendCommand
    servo.o(i.Servo_GetStatus) refers to delay.o(i.Delay_ms) for Delay_ms
    servo.o(i.Servo_GetStatus) refers to servo.o(i.Servo_ReceiveResponse) for Servo_ReceiveResponse
    servo.o(i.Servo_Init) refers to usart.o(i.USART1_Init) for USART1_Init
    servo.o(i.Servo_Init) refers to delay.o(i.Delay_ms) for Delay_ms
    servo.o(i.Servo_PositionToAngle) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    servo.o(i.Servo_PositionToAngle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    servo.o(i.Servo_PositionToAngle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    servo.o(i.Servo_ReadPosition) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    servo.o(i.Servo_ReadPosition) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    servo.o(i.Servo_ReadPosition) refers to servo.o(i.Servo_SendCommand) for Servo_SendCommand
    servo.o(i.Servo_ReadPosition) refers to delay.o(i.Delay_ms) for Delay_ms
    servo.o(i.Servo_ReadPosition) refers to servo.o(i.Servo_ReceiveResponse) for Servo_ReceiveResponse
    servo.o(i.Servo_ReadPosition) refers to servo.o(i.Servo_PositionToAngle) for Servo_PositionToAngle
    servo.o(i.Servo_ReadTemperature) refers to servo.o(i.Servo_SendCommand) for Servo_SendCommand
    servo.o(i.Servo_ReadTemperature) refers to delay.o(i.Delay_ms) for Delay_ms
    servo.o(i.Servo_ReadTemperature) refers to servo.o(i.Servo_ReceiveResponse) for Servo_ReceiveResponse
    servo.o(i.Servo_ReadVoltage) refers to servo.o(i.Servo_SendCommand) for Servo_SendCommand
    servo.o(i.Servo_ReadVoltage) refers to delay.o(i.Delay_ms) for Delay_ms
    servo.o(i.Servo_ReadVoltage) refers to servo.o(i.Servo_ReceiveResponse) for Servo_ReceiveResponse
    servo.o(i.Servo_ReceiveResponse) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    servo.o(i.Servo_ReceiveResponse) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    servo.o(i.Servo_ReceiveResponse) refers to servo.o(i.Servo_CalculateChecksum) for Servo_CalculateChecksum
    servo.o(i.Servo_SendCommand) refers to servo.o(i.Servo_CalculateChecksum) for Servo_CalculateChecksum
    servo.o(i.Servo_SendCommand) refers to usart.o(i.USART1_SendBuffer) for USART1_SendBuffer
    servo.o(i.Servo_SendCommand) refers to delay.o(i.Delay_ms) for Delay_ms
    servo.o(i.Servo_SetPosition) refers to servo.o(i.Servo_SetPositionWithTime) for Servo_SetPositionWithTime
    servo.o(i.Servo_SetPositionWithTime) refers to servo.o(i.Servo_AngleToPosition) for Servo_AngleToPosition
    servo.o(i.Servo_SetPositionWithTime) refers to servo.o(i.Servo_SendCommand) for Servo_SendCommand
    servo.o(i.Servo_SetTorqueEnable) refers to servo.o(i.Servo_SendCommand) for Servo_SendCommand
    servo.o(i.Servo_SetTorqueEnable) refers to delay.o(i.Delay_ms) for Delay_ms
    geometry.o(i.Geometry_CalculateAngleBetweenPoints) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    geometry.o(i.Geometry_CalculateAngleBetweenPoints) refers to atan2f.o(i.atan2f) for atan2f
    geometry.o(i.Geometry_CalculateDistance) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    geometry.o(i.Geometry_CalculateDistance) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    geometry.o(i.Geometry_CalculateDistance) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    geometry.o(i.Geometry_CalculateDistance) refers to sqrtf.o(i.sqrtf) for sqrtf
    geometry.o(i.Geometry_ClampServoAngle) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    geometry.o(i.Geometry_ClampServoAngle) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    geometry.o(i.Geometry_ClampWallPoint) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    geometry.o(i.Geometry_ClampWallPoint) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    geometry.o(i.Geometry_IsServoAngleValid) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    geometry.o(i.Geometry_IsServoAngleValid) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    geometry.o(i.Geometry_IsWallPointValid) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    geometry.o(i.Geometry_IsWallPointValid) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    geometry.o(i.Geometry_NormalizeAngle) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    geometry.o(i.Geometry_NormalizeAngle) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    geometry.o(i.Geometry_NormalizeAngle) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    geometry.o(i.Geometry_NormalizeAngle) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    geometry.o(i.Geometry_ServoToWall) refers to geometry.o(i.Geometry_IsServoAngleValid) for Geometry_IsServoAngleValid
    geometry.o(i.Geometry_ServoToWall) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    geometry.o(i.Geometry_ServoToWall) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    geometry.o(i.Geometry_ServoToWall) refers to tanf.o(i.tanf) for tanf
    geometry.o(i.Geometry_ServoToWall) refers to geometry.o(i.__ARM_isnanf) for __ARM_isnanf
    geometry.o(i.Geometry_ServoToWall) refers to geometry.o(i.__ARM_isinff) for __ARM_isinff
    geometry.o(i.Geometry_TestConversion) refers to geometry.o(i.Geometry_ServoToWall) for Geometry_ServoToWall
    geometry.o(i.Geometry_TestConversion) refers to geometry.o(i.Geometry_WallToServo) for Geometry_WallToServo
    geometry.o(i.Geometry_TestConversion) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    geometry.o(i.Geometry_TestConversion) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    geometry.o(i.Geometry_TestConversion) refers to geometry.o(.constdata) for .constdata
    geometry.o(i.Geometry_WallToServo) refers to geometry.o(i.Geometry_IsWallPointValid) for Geometry_IsWallPointValid
    geometry.o(i.Geometry_WallToServo) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    geometry.o(i.Geometry_WallToServo) refers to atanf.o(i.atanf) for atanf
    geometry.o(i.Geometry_WallToServo) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    geometry.o(i.Geometry_WallToServo) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    geometry.o(i.Geometry_WallToServo) refers to geometry.o(i.Geometry_ClampServoAngle) for Geometry_ClampServoAngle
    geometry.o(i.Path_CalculateOptimalSteps) refers to geometry.o(i.Path_CalculatePathLength) for Path_CalculatePathLength
    geometry.o(i.Path_CalculateOptimalSteps) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    geometry.o(i.Path_CalculateOptimalSteps) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    geometry.o(i.Path_CalculatePathLength) refers to geometry.o(i.Geometry_CalculateDistance) for Geometry_CalculateDistance
    geometry.o(i.Path_GetNextStep) refers to geometry.o(i.Path_IsComplete) for Path_IsComplete
    geometry.o(i.Path_GetNextStep) refers to geometry.o(i.Path_LinearInterpolate) for Path_LinearInterpolate
    geometry.o(i.Path_GetNextStep) refers to geometry.o(i.Geometry_WallToServo) for Geometry_WallToServo
    geometry.o(i.Path_Initialize) refers to geometry.o(i.Path_ValidatePath) for Path_ValidatePath
    geometry.o(i.Path_Initialize) refers to geometry.o(i.Geometry_WallToServo) for Geometry_WallToServo
    geometry.o(i.Path_LinearInterpolate) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    geometry.o(i.Path_LinearInterpolate) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    geometry.o(i.Path_LinearInterpolate) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    geometry.o(i.Path_LinearInterpolate) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    geometry.o(i.Path_LinearInterpolate) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    geometry.o(i.Path_LinearInterpolate) refers to geometry.o(i.Geometry_IsWallPointValid) for Geometry_IsWallPointValid
    geometry.o(i.Path_LinearInterpolate) refers to geometry.o(i.Geometry_ClampWallPoint) for Geometry_ClampWallPoint
    geometry.o(i.Path_Reset) refers to geometry.o(i.Geometry_WallToServo) for Geometry_WallToServo
    geometry.o(i.Path_SetDirection) refers to geometry.o(i.Geometry_WallToServo) for Geometry_WallToServo
    geometry.o(i.Path_TestInterpolation) refers to geometry.o(i.Path_Initialize) for Path_Initialize
    geometry.o(i.Path_TestInterpolation) refers to geometry.o(i.Path_GetNextStep) for Path_GetNextStep
    geometry.o(i.Path_TestInterpolation) refers to geometry.o(i.Path_LinearInterpolate) for Path_LinearInterpolate
    geometry.o(i.Path_TestInterpolation) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    geometry.o(i.Path_TestInterpolation) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    geometry.o(i.Path_TestInterpolation) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    geometry.o(i.Path_TestInterpolation) refers to sqrtf.o(i.sqrtf) for sqrtf
    geometry.o(i.Path_TestInterpolation) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    geometry.o(i.Path_TestInterpolation) refers to geometry.o(.constdata) for .constdata
    geometry.o(i.Path_ValidatePath) refers to geometry.o(i.Geometry_IsWallPointValid) for Geometry_IsWallPointValid
    geometry.o(i.Path_ValidatePath) refers to geometry.o(i.Geometry_CalculateDistance) for Geometry_CalculateDistance
    geometry.o(i.Path_ValidatePath) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    geometry.o(i.Path_ValidatePath) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    statemachine.o(i.StateMachine_GetMoveProgress) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    statemachine.o(i.StateMachine_GetMoveProgress) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    statemachine.o(i.StateMachine_GetTick) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    statemachine.o(i.StateMachine_HandleRecordKey) refers to statemachine.o(i.StateMachine_RecordPointA) for StateMachine_RecordPointA
    statemachine.o(i.StateMachine_HandleRecordKey) refers to statemachine.o(i.StateMachine_RecordPointB) for StateMachine_RecordPointB
    statemachine.o(i.StateMachine_HandleRecordKey) refers to statemachine.o(i.StateMachine_StopAutoMovement) for StateMachine_StopAutoMovement
    statemachine.o(i.StateMachine_HandleTriggerKey) refers to statemachine.o(i.StateMachine_ToggleDirection) for StateMachine_ToggleDirection
    statemachine.o(i.StateMachine_Init) refers to geometry.o(i.Path_Initialize) for Path_Initialize
    statemachine.o(i.StateMachine_Init) refers to timer.o(i.Timer_Init) for Timer_Init
    statemachine.o(i.StateMachine_Init) refers to timer.o(i.Timer_Start) for Timer_Start
    statemachine.o(i.StateMachine_Init) refers to statemachine.o(.constdata) for .constdata
    statemachine.o(i.StateMachine_IsTimeout) refers to timer.o(i.Timer_IsTimeout) for Timer_IsTimeout
    statemachine.o(i.StateMachine_MoveServoToPosition) refers to servo.o(i.Servo_SetPositionWithTime) for Servo_SetPositionWithTime
    statemachine.o(i.StateMachine_ReadCurrentPosition) refers to servo.o(i.Servo_ReadPosition) for Servo_ReadPosition
    statemachine.o(i.StateMachine_ReadCurrentPosition) refers to geometry.o(i.Geometry_ServoToWall) for Geometry_ServoToWall
    statemachine.o(i.StateMachine_RecordPointA) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    statemachine.o(i.StateMachine_RecordPointA) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    statemachine.o(i.StateMachine_RecordPointA) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    statemachine.o(i.StateMachine_RecordPointA) refers to servo.o(i.Servo_SetTorqueEnable) for Servo_SetTorqueEnable
    statemachine.o(i.StateMachine_RecordPointA) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    statemachine.o(i.StateMachine_RecordPointA) refers to bluetooth.o(i.Bluetooth_SendKeyAction) for Bluetooth_SendKeyAction
    statemachine.o(i.StateMachine_RecordPointA) refers to timer.o(i.Timer_IsTimeout) for Timer_IsTimeout
    statemachine.o(i.StateMachine_RecordPointA) refers to delay.o(i.Delay_ms) for Delay_ms
    statemachine.o(i.StateMachine_RecordPointA) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    statemachine.o(i.StateMachine_RecordPointA) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    statemachine.o(i.StateMachine_RecordPointA) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    statemachine.o(i.StateMachine_RecordPointA) refers to noretval__2sprintf.o(.text) for __2sprintf
    statemachine.o(i.StateMachine_RecordPointA) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    statemachine.o(i.StateMachine_RecordPointA) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    statemachine.o(i.StateMachine_RecordPointA) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    statemachine.o(i.StateMachine_RecordPointA) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    statemachine.o(i.StateMachine_RecordPointA) refers to tan.o(i.tan) for tan
    statemachine.o(i.StateMachine_RecordPointA) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    statemachine.o(i.StateMachine_RecordPointA) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    statemachine.o(i.StateMachine_RecordPointA) refers to statemachine.o(i.StateMachine_TransitionTo) for StateMachine_TransitionTo
    statemachine.o(i.StateMachine_RecordPointA) refers to statemachine.o(.data) for record_state
    statemachine.o(i.StateMachine_RecordPointA) refers to statemachine.o(.conststring) for .conststring
    statemachine.o(i.StateMachine_RecordPointB) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    statemachine.o(i.StateMachine_RecordPointB) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    statemachine.o(i.StateMachine_RecordPointB) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    statemachine.o(i.StateMachine_RecordPointB) refers to servo.o(i.Servo_SetTorqueEnable) for Servo_SetTorqueEnable
    statemachine.o(i.StateMachine_RecordPointB) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    statemachine.o(i.StateMachine_RecordPointB) refers to bluetooth.o(i.Bluetooth_SendKeyAction) for Bluetooth_SendKeyAction
    statemachine.o(i.StateMachine_RecordPointB) refers to timer.o(i.Timer_IsTimeout) for Timer_IsTimeout
    statemachine.o(i.StateMachine_RecordPointB) refers to delay.o(i.Delay_ms) for Delay_ms
    statemachine.o(i.StateMachine_RecordPointB) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    statemachine.o(i.StateMachine_RecordPointB) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    statemachine.o(i.StateMachine_RecordPointB) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    statemachine.o(i.StateMachine_RecordPointB) refers to noretval__2sprintf.o(.text) for __2sprintf
    statemachine.o(i.StateMachine_RecordPointB) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    statemachine.o(i.StateMachine_RecordPointB) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    statemachine.o(i.StateMachine_RecordPointB) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    statemachine.o(i.StateMachine_RecordPointB) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    statemachine.o(i.StateMachine_RecordPointB) refers to tan.o(i.tan) for tan
    statemachine.o(i.StateMachine_RecordPointB) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    statemachine.o(i.StateMachine_RecordPointB) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    statemachine.o(i.StateMachine_RecordPointB) refers to statemachine.o(i.StateMachine_TransitionTo) for StateMachine_TransitionTo
    statemachine.o(i.StateMachine_RecordPointB) refers to statemachine.o(.data) for record_state
    statemachine.o(i.StateMachine_RecordPointB) refers to statemachine.o(.conststring) for .conststring
    statemachine.o(i.StateMachine_SetServoTorque) refers to servo.o(i.Servo_SetTorqueEnable) for Servo_SetTorqueEnable
    statemachine.o(i.StateMachine_StartAutoMovement) refers to geometry.o(i.Path_Initialize) for Path_Initialize
    statemachine.o(i.StateMachine_StartAutoMovement) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    statemachine.o(i.StateMachine_StartAutoMovement) refers to bluetooth.o(i.Bluetooth_SendKeyAction) for Bluetooth_SendKeyAction
    statemachine.o(i.StateMachine_StartAutoMovement) refers to timer.o(i.Timer_StartPathMovement) for Timer_StartPathMovement
    statemachine.o(i.StateMachine_StartAutoMovement) refers to statemachine.o(i.StateMachine_MoveServoToPosition) for StateMachine_MoveServoToPosition
    statemachine.o(i.StateMachine_StopAutoMovement) refers to bluetooth.o(i.Bluetooth_SendKeyAction) for Bluetooth_SendKeyAction
    statemachine.o(i.StateMachine_StopAutoMovement) refers to timer.o(i.Timer_StopPathMovement) for Timer_StopPathMovement
    statemachine.o(i.StateMachine_StopAutoMovement) refers to statemachine.o(i.StateMachine_TransitionTo) for StateMachine_TransitionTo
    statemachine.o(i.StateMachine_ToggleDirection) refers to geometry.o(i.Path_SetDirection) for Path_SetDirection
    statemachine.o(i.StateMachine_ToggleDirection) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    statemachine.o(i.StateMachine_ToggleDirection) refers to timer.o(i.Timer_StartPathMovement) for Timer_StartPathMovement
    statemachine.o(i.StateMachine_TransitionTo) refers to statemachine.o(i.StateMachine_StartAutoMovement) for StateMachine_StartAutoMovement
    statemachine.o(i.StateMachine_Update) refers to key.o(i.Key_Scan) for Key_Scan
    statemachine.o(i.StateMachine_Update) refers to key.o(i.Key_IsClicked) for Key_IsClicked
    statemachine.o(i.StateMachine_Update) refers to statemachine.o(i.StateMachine_HandleRecordKey) for StateMachine_HandleRecordKey
    statemachine.o(i.StateMachine_Update) refers to key.o(i.Key_ClearEvent) for Key_ClearEvent
    statemachine.o(i.StateMachine_Update) refers to statemachine.o(i.StateMachine_HandleTriggerKey) for StateMachine_HandleTriggerKey
    statemachine.o(i.StateMachine_Update) refers to statemachine.o(i.StateMachine_UpdateAutoMovement) for StateMachine_UpdateAutoMovement
    statemachine.o(i.StateMachine_UpdateAutoMovement) refers to timer.o(i.Timer_IsPathStepReady) for Timer_IsPathStepReady
    statemachine.o(i.StateMachine_UpdateAutoMovement) refers to geometry.o(i.Path_GetNextStep) for Path_GetNextStep
    statemachine.o(i.StateMachine_UpdateAutoMovement) refers to statemachine.o(i.StateMachine_MoveServoToPosition) for StateMachine_MoveServoToPosition
    statemachine.o(i.StateMachine_UpdateAutoMovement) refers to timer.o(i.Timer_ResetPathStep) for Timer_ResetPathStep
    statemachine.o(i.StateMachine_UpdateAutoMovement) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    statemachine.o(i.StateMachine_UpdateAutoMovement) refers to statemachine.o(i.StateMachine_ToggleDirection) for StateMachine_ToggleDirection
    timer.o(i.Timer_ConfigureHardware) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer.o(i.Timer_ConfigureHardware) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.Timer_ConfigureHardware) refers to misc.o(i.NVIC_Init) for NVIC_Init
    timer.o(i.Timer_ConfigureHardware) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    timer.o(i.Timer_ConfigureHardware) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    timer.o(i.Timer_DelayMs) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    timer.o(i.Timer_DelayMs) refers to timer.o(i.Timer_UpdateCpuUsage) for Timer_UpdateCpuUsage
    timer.o(i.Timer_DelayMs) refers to timer.o(i.Timer_IsTimeout) for Timer_IsTimeout
    timer.o(i.Timer_DisableCallback) refers to timer.o(.bss) for timer_state
    timer.o(i.Timer_EnableCallback) refers to timer.o(.bss) for timer_state
    timer.o(i.Timer_GetCpuUsage) refers to timer.o(.data) for cpu_usage_percent
    timer.o(i.Timer_GetTick) refers to timer.o(.bss) for timer_state
    timer.o(i.Timer_GetTimeMs) refers to timer.o(.bss) for timer_state
    timer.o(i.Timer_IRQ_Handler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    timer.o(i.Timer_IRQ_Handler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    timer.o(i.Timer_IRQ_Handler) refers to timer.o(.bss) for timer_state
    timer.o(i.Timer_IRQ_Handler) refers to timer.o(.data) for path_step_counter
    timer.o(i.Timer_Init) refers to timer.o(i.Timer_ConfigureHardware) for Timer_ConfigureHardware
    timer.o(i.Timer_Init) refers to timer.o(.bss) for timer_state
    timer.o(i.Timer_Init) refers to timer.o(.data) for path_step_counter
    timer.o(i.Timer_IsDelayComplete) refers to timer.o(i.Timer_IsTimeout) for Timer_IsTimeout
    timer.o(i.Timer_IsPathStepReady) refers to timer.o(.data) for path_step_ready
    timer.o(i.Timer_IsRunning) refers to timer.o(.bss) for timer_state
    timer.o(i.Timer_IsTimeout) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    timer.o(i.Timer_Reset) refers to stm32f10x_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    timer.o(i.Timer_Reset) refers to timer.o(.bss) for timer_state
    timer.o(i.Timer_Reset) refers to timer.o(.data) for path_step_counter
    timer.o(i.Timer_ResetPathStep) refers to timer.o(.data) for path_step_counter
    timer.o(i.Timer_SetCallback) refers to timer.o(.bss) for timer_state
    timer.o(i.Timer_Start) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    timer.o(i.Timer_Start) refers to timer.o(.bss) for timer_state
    timer.o(i.Timer_StartPathMovement) refers to timer.o(.data) for path_step_counter
    timer.o(i.Timer_Stop) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    timer.o(i.Timer_Stop) refers to timer.o(.bss) for timer_state
    timer.o(i.Timer_StopPathMovement) refers to timer.o(.data) for path_step_counter
    timer.o(i.Timer_UpdateCpuUsage) refers to timer.o(.data) for cpu_idle_count
    manualrecord.o(i.ManualRecord_Cancel) refers to manualrecord.o(i.ManualRecord_LoadServos) for ManualRecord_LoadServos
    manualrecord.o(i.ManualRecord_Cancel) refers to manualrecord.o(i.ManualRecord_TransitionTo) for ManualRecord_TransitionTo
    manualrecord.o(i.ManualRecord_GetResult) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    manualrecord.o(i.ManualRecord_GetResult) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    manualrecord.o(i.ManualRecord_IsPointSaved) refers to manualrecord.o(.data) for point_saved_flags
    manualrecord.o(i.ManualRecord_IsPositionReasonable) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    manualrecord.o(i.ManualRecord_IsPositionReasonable) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    manualrecord.o(i.ManualRecord_IsPositionReasonable) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    manualrecord.o(i.ManualRecord_IsPositionReasonable) refers to sqrtf.o(i.sqrtf) for sqrtf
    manualrecord.o(i.ManualRecord_IsPositionReasonable) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    manualrecord.o(i.ManualRecord_LoadPointData) refers to manualrecord.o(.data) for point_saved_flags
    manualrecord.o(i.ManualRecord_LoadPointData) refers to manualrecord.o(.bss) for saved_points
    manualrecord.o(i.ManualRecord_LoadServos) refers to servo.o(i.Servo_SetTorqueEnable) for Servo_SetTorqueEnable
    manualrecord.o(i.ManualRecord_LoadServos) refers to delay.o(i.Delay_ms) for Delay_ms
    manualrecord.o(i.ManualRecord_ReadCurrentPosition) refers to servo.o(i.Servo_ReadPosition) for Servo_ReadPosition
    manualrecord.o(i.ManualRecord_ReadCurrentPosition) refers to geometry.o(i.Geometry_ServoToWall) for Geometry_ServoToWall
    manualrecord.o(i.ManualRecord_SavePointData) refers to manualrecord.o(.bss) for saved_points
    manualrecord.o(i.ManualRecord_SavePointData) refers to manualrecord.o(.data) for point_saved_flags
    manualrecord.o(i.ManualRecord_ShowInstructions) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    manualrecord.o(i.ManualRecord_ShowInstructions) refers to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    manualrecord.o(i.ManualRecord_ShowInstructions) refers to _printf_str.o(.text) for _printf_str
    manualrecord.o(i.ManualRecord_ShowInstructions) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    manualrecord.o(i.ManualRecord_ShowInstructions) refers to noretval__2sprintf.o(.text) for __2sprintf
    manualrecord.o(i.ManualRecord_ShowProgress) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    manualrecord.o(i.ManualRecord_ShowProgress) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    manualrecord.o(i.ManualRecord_ShowProgress) refers to _printf_str.o(.text) for _printf_str
    manualrecord.o(i.ManualRecord_ShowProgress) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    manualrecord.o(i.ManualRecord_ShowProgress) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    manualrecord.o(i.ManualRecord_ShowProgress) refers to manualrecord.o(i.ManualRecord_GetStateString) for ManualRecord_GetStateString
    manualrecord.o(i.ManualRecord_ShowProgress) refers to noretval__2sprintf.o(.text) for __2sprintf
    manualrecord.o(i.ManualRecord_ShowProgress) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    manualrecord.o(i.ManualRecord_ShowProgress) refers to manualrecord.o(i.ManualRecord_ReadCurrentPosition) for ManualRecord_ReadCurrentPosition
    manualrecord.o(i.ManualRecord_ShowProgress) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    manualrecord.o(i.ManualRecord_Start) refers to manualrecord.o(i.ManualRecord_Init) for ManualRecord_Init
    manualrecord.o(i.ManualRecord_Start) refers to manualrecord.o(i.ManualRecord_ShowInstructions) for ManualRecord_ShowInstructions
    manualrecord.o(i.ManualRecord_Start) refers to manualrecord.o(i.ManualRecord_TransitionTo) for ManualRecord_TransitionTo
    manualrecord.o(i.ManualRecord_TransitionTo) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    manualrecord.o(i.ManualRecord_TransitionTo) refers to led.o(i.LED_ON) for LED_ON
    manualrecord.o(i.ManualRecord_TransitionTo) refers to led.o(i.LED_OFF) for LED_OFF
    manualrecord.o(i.ManualRecord_TransitionTo) refers to delay.o(i.Delay_ms) for Delay_ms
    manualrecord.o(i.ManualRecord_UnloadServos) refers to servo.o(i.Servo_SetTorqueEnable) for Servo_SetTorqueEnable
    manualrecord.o(i.ManualRecord_Update) refers to timer.o(i.Timer_IsTimeout) for Timer_IsTimeout
    manualrecord.o(i.ManualRecord_Update) refers to manualrecord.o(i.ManualRecord_UnloadServos) for ManualRecord_UnloadServos
    manualrecord.o(i.ManualRecord_Update) refers to manualrecord.o(i.ManualRecord_TransitionTo) for ManualRecord_TransitionTo
    manualrecord.o(i.ManualRecord_Update) refers to manualrecord.o(i.ManualRecord_WaitUserAdjustment) for ManualRecord_WaitUserAdjustment
    manualrecord.o(i.ManualRecord_Update) refers to manualrecord.o(i.ManualRecord_ReadCurrentPosition) for ManualRecord_ReadCurrentPosition
    manualrecord.o(i.ManualRecord_Update) refers to manualrecord.o(i.ManualRecord_ValidatePosition) for ManualRecord_ValidatePosition
    manualrecord.o(i.ManualRecord_Update) refers to manualrecord.o(i.ManualRecord_LoadServos) for ManualRecord_LoadServos
    manualrecord.o(i.ManualRecord_Update) refers to manualrecord.o(i.ManualRecord_SavePointData) for ManualRecord_SavePointData
    manualrecord.o(i.ManualRecord_Update) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    manualrecord.o(i.ManualRecord_Update) refers to manualrecord.o(i.ManualRecord_ShowProgress) for ManualRecord_ShowProgress
    manualrecord.o(i.ManualRecord_ValidatePosition) refers to geometry.o(i.Geometry_IsWallPointValid) for Geometry_IsWallPointValid
    manualrecord.o(i.ManualRecord_ValidatePosition) refers to manualrecord.o(i.ManualRecord_IsPositionReasonable) for ManualRecord_IsPositionReasonable
    manualrecord.o(i.ManualRecord_WaitUserAdjustment) refers to key.o(i.Key_IsClicked) for Key_IsClicked
    manualrecord.o(i.ManualRecord_WaitUserAdjustment) refers to key.o(i.Key_ClearEvent) for Key_ClearEvent
    automovement.o(i.AutoMovement_ArePointsValid) refers to geometry.o(i.Geometry_IsWallPointValid) for Geometry_IsWallPointValid
    automovement.o(i.AutoMovement_ArePointsValid) refers to geometry.o(i.Geometry_CalculateDistance) for Geometry_CalculateDistance
    automovement.o(i.AutoMovement_ArePointsValid) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    automovement.o(i.AutoMovement_ArePointsValid) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    automovement.o(i.AutoMovement_CheckSafety) refers to automovement.o(i.AutoMovement_ArePointsValid) for AutoMovement_ArePointsValid
    automovement.o(i.AutoMovement_EmergencyStop) refers to timer.o(i.Timer_StopPathMovement) for Timer_StopPathMovement
    automovement.o(i.AutoMovement_EmergencyStop) refers to automovement.o(i.AutoMovement_TransitionTo) for AutoMovement_TransitionTo
    automovement.o(i.AutoMovement_ExecuteNextStep) refers to geometry.o(i.Path_GetNextStep) for Path_GetNextStep
    automovement.o(i.AutoMovement_ExecuteNextStep) refers to automovement.o(i.AutoMovement_MoveToPosition) for AutoMovement_MoveToPosition
    automovement.o(i.AutoMovement_ExecuteNextStep) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    automovement.o(i.AutoMovement_GetProgress) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    automovement.o(i.AutoMovement_GetProgress) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    automovement.o(i.AutoMovement_HandleDirectionChange) refers to timer.o(i.Timer_IsTimeout) for Timer_IsTimeout
    automovement.o(i.AutoMovement_HandleDirectionChange) refers to geometry.o(i.Path_SetDirection) for Path_SetDirection
    automovement.o(i.AutoMovement_HandleDirectionChange) refers to automovement.o(i.AutoMovement_Stop) for AutoMovement_Stop
    automovement.o(i.AutoMovement_HandleDirectionChange) refers to automovement.o(i.AutoMovement_TransitionTo) for AutoMovement_TransitionTo
    automovement.o(i.AutoMovement_HandleError) refers to timer.o(i.Timer_StopPathMovement) for Timer_StopPathMovement
    automovement.o(i.AutoMovement_HandleError) refers to automovement.o(i.AutoMovement_TransitionTo) for AutoMovement_TransitionTo
    automovement.o(i.AutoMovement_HandleMovingToStart) refers to automovement.o(i.AutoMovement_MoveToPosition) for AutoMovement_MoveToPosition
    automovement.o(i.AutoMovement_HandleMovingToStart) refers to timer.o(i.Timer_IsTimeout) for Timer_IsTimeout
    automovement.o(i.AutoMovement_HandleMovingToStart) refers to automovement.o(i.AutoMovement_TransitionTo) for AutoMovement_TransitionTo
    automovement.o(i.AutoMovement_HandlePathMoving) refers to timer.o(i.Timer_IsPathStepReady) for Timer_IsPathStepReady
    automovement.o(i.AutoMovement_HandlePathMoving) refers to automovement.o(i.AutoMovement_ExecuteNextStep) for AutoMovement_ExecuteNextStep
    automovement.o(i.AutoMovement_HandlePathMoving) refers to timer.o(i.Timer_ResetPathStep) for Timer_ResetPathStep
    automovement.o(i.AutoMovement_HandlePathMoving) refers to geometry.o(i.Path_IsComplete) for Path_IsComplete
    automovement.o(i.AutoMovement_HandlePathMoving) refers to automovement.o(i.AutoMovement_TransitionTo) for AutoMovement_TransitionTo
    automovement.o(i.AutoMovement_HandlePreparing) refers to timer.o(i.Timer_IsTimeout) for Timer_IsTimeout
    automovement.o(i.AutoMovement_HandlePreparing) refers to geometry.o(i.Path_Initialize) for Path_Initialize
    automovement.o(i.AutoMovement_HandlePreparing) refers to geometry.o(i.Path_SetDirection) for Path_SetDirection
    automovement.o(i.AutoMovement_HandlePreparing) refers to automovement.o(i.AutoMovement_TransitionTo) for AutoMovement_TransitionTo
    automovement.o(i.AutoMovement_LoadPointsFromRecord) refers to manualrecord.o(i.ManualRecord_IsPointSaved) for ManualRecord_IsPointSaved
    automovement.o(i.AutoMovement_LoadPointsFromRecord) refers to manualrecord.o(i.ManualRecord_LoadPointData) for ManualRecord_LoadPointData
    automovement.o(i.AutoMovement_MoveToPosition) refers to servo.o(i.Servo_SetPositionWithTime) for Servo_SetPositionWithTime
    automovement.o(i.AutoMovement_ShowStatus) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    automovement.o(i.AutoMovement_ShowStatus) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    automovement.o(i.AutoMovement_ShowStatus) refers to _printf_str.o(.text) for _printf_str
    automovement.o(i.AutoMovement_ShowStatus) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    automovement.o(i.AutoMovement_ShowStatus) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    automovement.o(i.AutoMovement_ShowStatus) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    automovement.o(i.AutoMovement_ShowStatus) refers to _printf_dec.o(.text) for _printf_int_dec
    automovement.o(i.AutoMovement_ShowStatus) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    automovement.o(i.AutoMovement_ShowStatus) refers to automovement.o(i.AutoMovement_GetStateString) for AutoMovement_GetStateString
    automovement.o(i.AutoMovement_ShowStatus) refers to noretval__2sprintf.o(.text) for __2sprintf
    automovement.o(i.AutoMovement_ShowStatus) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    automovement.o(i.AutoMovement_ShowStatus) refers to automovement.o(i.AutoMovement_GetProgress) for AutoMovement_GetProgress
    automovement.o(i.AutoMovement_ShowStatus) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    automovement.o(i.AutoMovement_ShowStatus) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    automovement.o(i.AutoMovement_ShowStatus) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    automovement.o(i.AutoMovement_Start) refers to automovement.o(i.AutoMovement_LoadPointsFromRecord) for AutoMovement_LoadPointsFromRecord
    automovement.o(i.AutoMovement_Start) refers to automovement.o(i.AutoMovement_ArePointsValid) for AutoMovement_ArePointsValid
    automovement.o(i.AutoMovement_Start) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    automovement.o(i.AutoMovement_Start) refers to automovement.o(i.AutoMovement_TransitionTo) for AutoMovement_TransitionTo
    automovement.o(i.AutoMovement_Stop) refers to automovement.o(i.AutoMovement_TransitionTo) for AutoMovement_TransitionTo
    automovement.o(i.AutoMovement_TransitionTo) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    automovement.o(i.AutoMovement_TransitionTo) refers to led.o(i.LED_OFF) for LED_OFF
    automovement.o(i.AutoMovement_TransitionTo) refers to led.o(i.LED_ON) for LED_ON
    automovement.o(i.AutoMovement_TransitionTo) refers to timer.o(i.Timer_StartPathMovement) for Timer_StartPathMovement
    automovement.o(i.AutoMovement_TransitionTo) refers to timer.o(i.Timer_StopPathMovement) for Timer_StopPathMovement
    automovement.o(i.AutoMovement_Update) refers to automovement.o(i.AutoMovement_EmergencyStop) for AutoMovement_EmergencyStop
    automovement.o(i.AutoMovement_Update) refers to automovement.o(i.AutoMovement_Stop) for AutoMovement_Stop
    automovement.o(i.AutoMovement_Update) refers to automovement.o(i.AutoMovement_CheckSafety) for AutoMovement_CheckSafety
    automovement.o(i.AutoMovement_Update) refers to automovement.o(i.AutoMovement_HandleError) for AutoMovement_HandleError
    automovement.o(i.AutoMovement_Update) refers to automovement.o(i.AutoMovement_HandlePreparing) for AutoMovement_HandlePreparing
    automovement.o(i.AutoMovement_Update) refers to automovement.o(i.AutoMovement_HandleMovingToStart) for AutoMovement_HandleMovingToStart
    automovement.o(i.AutoMovement_Update) refers to automovement.o(i.AutoMovement_HandlePathMoving) for AutoMovement_HandlePathMoving
    automovement.o(i.AutoMovement_Update) refers to automovement.o(i.AutoMovement_HandleDirectionChange) for AutoMovement_HandleDirectionChange
    automovement.o(i.AutoMovement_Update) refers to timer.o(i.Timer_IsTimeout) for Timer_IsTimeout
    automovement.o(i.AutoMovement_Update) refers to automovement.o(i.AutoMovement_TransitionTo) for AutoMovement_TransitionTo
    automovement.o(i.AutoMovement_Update) refers to automovement.o(i.AutoMovement_ShowStatus) for AutoMovement_ShowStatus
    systemdiagnostics.o(i.SystemDiag_BackupConfiguration) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    systemdiagnostics.o(i.SystemDiag_BackupConfiguration) refers to systemdiagnostics.o(i.SystemDiag_LogError) for SystemDiag_LogError
    systemdiagnostics.o(i.SystemDiag_CalibrateServos) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    systemdiagnostics.o(i.SystemDiag_CalibrateServos) refers to servo.o(i.Servo_SetPositionWithTime) for Servo_SetPositionWithTime
    systemdiagnostics.o(i.SystemDiag_CalibrateServos) refers to delay.o(i.Delay_ms) for Delay_ms
    systemdiagnostics.o(i.SystemDiag_CalibrateServos) refers to servo.o(i.Servo_ReadPosition) for Servo_ReadPosition
    systemdiagnostics.o(i.SystemDiag_CalibrateServos) refers to systemdiagnostics.o(i.SystemDiag_LogError) for SystemDiag_LogError
    systemdiagnostics.o(i.SystemDiag_CalibrateServos) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    systemdiagnostics.o(i.SystemDiag_CalibrateServos) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    systemdiagnostics.o(i.SystemDiag_CheckGeometry) refers to geometry.o(i.Geometry_ServoToWall) for Geometry_ServoToWall
    systemdiagnostics.o(i.SystemDiag_CheckGeometry) refers to geometry.o(i.Geometry_WallToServo) for Geometry_WallToServo
    systemdiagnostics.o(i.SystemDiag_CheckGeometry) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    systemdiagnostics.o(i.SystemDiag_CheckGeometry) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    systemdiagnostics.o(i.SystemDiag_CheckGeometry) refers to systemdiagnostics.o(i.SystemDiag_LogError) for SystemDiag_LogError
    systemdiagnostics.o(i.SystemDiag_CheckGeometry) refers to systemdiagnostics.o(.constdata) for .constdata
    systemdiagnostics.o(i.SystemDiag_CheckServos) refers to servo.o(i.Servo_ReadPosition) for Servo_ReadPosition
    systemdiagnostics.o(i.SystemDiag_CheckServos) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    systemdiagnostics.o(i.SystemDiag_CheckServos) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    systemdiagnostics.o(i.SystemDiag_CheckServos) refers to systemdiagnostics.o(i.SystemDiag_LogError) for SystemDiag_LogError
    systemdiagnostics.o(i.SystemDiag_CheckServos) refers to servo.o(i.Servo_GetStatus) for Servo_GetStatus
    systemdiagnostics.o(i.SystemDiag_CheckTimer) refers to timer.o(i.Timer_IsRunning) for Timer_IsRunning
    systemdiagnostics.o(i.SystemDiag_CheckTimer) refers to timer.o(i.Timer_GetFrequency) for Timer_GetFrequency
    systemdiagnostics.o(i.SystemDiag_CheckTimer) refers to systemdiagnostics.o(i.SystemDiag_LogError) for SystemDiag_LogError
    systemdiagnostics.o(i.SystemDiag_ClearErrorLog) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to _printf_str.o(.text) for _printf_str
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to _printf_dec.o(.text) for _printf_int_dec
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to systemdiagnostics.o(i.SystemDiag_QuickCheck) for SystemDiag_QuickCheck
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to systemdiagnostics.o(i.SystemDiag_GetLevelString) for SystemDiag_GetLevelString
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to noretval__2sprintf.o(.text) for __2sprintf
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to delay.o(i.Delay_ms) for Delay_ms
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to systemdiagnostics.o(i.SystemDiag_ShowPerformance) for SystemDiag_ShowPerformance
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to systemdiagnostics.o(i.SystemDiag_ShowErrors) for SystemDiag_ShowErrors
    systemdiagnostics.o(i.SystemDiag_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    systemdiagnostics.o(i.SystemDiag_Init) refers to systemdiagnostics.o(i.SystemDiag_LoadDefaultConfig) for SystemDiag_LoadDefaultConfig
    systemdiagnostics.o(i.SystemDiag_Init) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    systemdiagnostics.o(i.SystemDiag_LogError) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    systemdiagnostics.o(i.SystemDiag_LogError) refers to strncpy.o(.text) for strncpy
    systemdiagnostics.o(i.SystemDiag_OptimizePerformance) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    systemdiagnostics.o(i.SystemDiag_OptimizePerformance) refers to systemdiagnostics.o(i.SystemDiag_LogError) for SystemDiag_LogError
    systemdiagnostics.o(i.SystemDiag_OptimizePerformance) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    systemdiagnostics.o(i.SystemDiag_PrintDiagnostic) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    systemdiagnostics.o(i.SystemDiag_PrintDiagnostic) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    systemdiagnostics.o(i.SystemDiag_PrintDiagnostic) refers to _printf_str.o(.text) for _printf_str
    systemdiagnostics.o(i.SystemDiag_PrintDiagnostic) refers to noretval__2sprintf.o(.text) for __2sprintf
    systemdiagnostics.o(i.SystemDiag_PrintDiagnostic) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    systemdiagnostics.o(i.SystemDiag_QuickCheck) refers to systemdiagnostics.o(i.SystemDiag_LogError) for SystemDiag_LogError
    systemdiagnostics.o(i.SystemDiag_QuickCheck) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    systemdiagnostics.o(i.SystemDiag_RecordPathStep) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    systemdiagnostics.o(i.SystemDiag_RecordPathStep) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    systemdiagnostics.o(i.SystemDiag_RecordPathStep) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    systemdiagnostics.o(i.SystemDiag_RecordPathStep) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    systemdiagnostics.o(i.SystemDiag_RecordServoError) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    systemdiagnostics.o(i.SystemDiag_RecordServoError) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    systemdiagnostics.o(i.SystemDiag_RecordServoError) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    systemdiagnostics.o(i.SystemDiag_RecordServoError) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    systemdiagnostics.o(i.SystemDiag_ResetStatistics) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    systemdiagnostics.o(i.SystemDiag_ResetStatistics) refers to systemdiagnostics.o(i.SystemDiag_ClearErrorLog) for SystemDiag_ClearErrorLog
    systemdiagnostics.o(i.SystemDiag_RestoreConfiguration) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    systemdiagnostics.o(i.SystemDiag_RestoreConfiguration) refers to systemdiagnostics.o(i.SystemDiag_LoadDefaultConfig) for SystemDiag_LoadDefaultConfig
    systemdiagnostics.o(i.SystemDiag_RestoreConfiguration) refers to systemdiagnostics.o(i.SystemDiag_LogError) for SystemDiag_LogError
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to _printf_str.o(.text) for _printf_str
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to systemdiagnostics.o(i.SystemDiag_CheckServos) for SystemDiag_CheckServos
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to delay.o(i.Delay_ms) for Delay_ms
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to systemdiagnostics.o(i.SystemDiag_CheckTimer) for SystemDiag_CheckTimer
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to systemdiagnostics.o(i.SystemDiag_CheckKeys) for SystemDiag_CheckKeys
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to systemdiagnostics.o(i.SystemDiag_CheckGeometry) for SystemDiag_CheckGeometry
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to systemdiagnostics.o(i.SystemDiag_CheckMemory) for SystemDiag_CheckMemory
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to systemdiagnostics.o(i.SystemDiag_GetLevelString) for SystemDiag_GetLevelString
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to noretval__2sprintf.o(.text) for __2sprintf
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to led.o(i.LED_ON) for LED_ON
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to led.o(i.LED_OFF) for LED_OFF
    systemdiagnostics.o(i.SystemDiag_ShowErrors) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    systemdiagnostics.o(i.SystemDiag_ShowErrors) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    systemdiagnostics.o(i.SystemDiag_ShowErrors) refers to _printf_dec.o(.text) for _printf_int_dec
    systemdiagnostics.o(i.SystemDiag_ShowErrors) refers to oled.o(i.OLED_Clear) for OLED_Clear
    systemdiagnostics.o(i.SystemDiag_ShowErrors) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    systemdiagnostics.o(i.SystemDiag_ShowErrors) refers to noretval__2sprintf.o(.text) for __2sprintf
    systemdiagnostics.o(i.SystemDiag_ShowErrors) refers to strncpy.o(.text) for strncpy
    systemdiagnostics.o(i.SystemDiag_ShowPerformance) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    systemdiagnostics.o(i.SystemDiag_ShowPerformance) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    systemdiagnostics.o(i.SystemDiag_ShowPerformance) refers to _printf_dec.o(.text) for _printf_int_dec
    systemdiagnostics.o(i.SystemDiag_ShowPerformance) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    systemdiagnostics.o(i.SystemDiag_ShowPerformance) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    systemdiagnostics.o(i.SystemDiag_ShowPerformance) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    systemdiagnostics.o(i.SystemDiag_ShowPerformance) refers to noretval__2sprintf.o(.text) for __2sprintf
    systemdiagnostics.o(i.SystemDiag_ShowPerformance) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    systemdiagnostics.o(i.SystemDiag_ShowStatus) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    systemdiagnostics.o(i.SystemDiag_ShowStatus) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    systemdiagnostics.o(i.SystemDiag_ShowStatus) refers to _printf_dec.o(.text) for _printf_int_dec
    systemdiagnostics.o(i.SystemDiag_ShowStatus) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    systemdiagnostics.o(i.SystemDiag_ShowStatus) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    systemdiagnostics.o(i.SystemDiag_ShowStatus) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    systemdiagnostics.o(i.SystemDiag_ShowStatus) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    systemdiagnostics.o(i.SystemDiag_ShowStatus) refers to noretval__2sprintf.o(.text) for __2sprintf
    systemdiagnostics.o(i.SystemDiag_ShowStatus) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    systemdiagnostics.o(i.SystemDiag_Update) refers to timer.o(i.Timer_GetTimeMs) for Timer_GetTimeMs
    systemdiagnostics.o(i.SystemDiag_Update) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    systemdiagnostics.o(i.SystemDiag_Update) refers to systemdiagnostics.o(i.SystemDiag_QuickCheck) for SystemDiag_QuickCheck
    systemdiagnostics.o(i.SystemDiag_ValidateAccuracy) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    systemdiagnostics.o(i.SystemDiag_ValidateAccuracy) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    systemdiagnostics.o(i.SystemDiag_ValidateAccuracy) refers to geometry.o(i.Geometry_ServoToWall) for Geometry_ServoToWall
    systemdiagnostics.o(i.SystemDiag_ValidateAccuracy) refers to geometry.o(i.Geometry_WallToServo) for Geometry_WallToServo
    systemdiagnostics.o(i.SystemDiag_ValidateAccuracy) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    systemdiagnostics.o(i.SystemDiag_ValidateAccuracy) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    systemdiagnostics.o(i.SystemDiag_ValidateAccuracy) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    systemdiagnostics.o(i.SystemDiag_ValidateAccuracy) refers to systemdiagnostics.o(i.SystemDiag_LogError) for SystemDiag_LogError
    systemdiagnostics.o(i.SystemDiag_ValidateAccuracy) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    systemdiagnostics.o(i.SystemDiag_ValidateAccuracy) refers to systemdiagnostics.o(.constdata) for .constdata
    usart.o(i.USART1_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.USART1_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.USART1_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart.o(i.USART1_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.USART1_ReceiveBuffer) refers to usart.o(i.USART1_ReceiveByte) for USART1_ReceiveByte
    usart.o(i.USART1_ReceiveByte) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.USART1_ReceiveByte) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART1_SendBuffer) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.USART1_SendBuffer) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    usart.o(i.USART1_SendByte) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.USART1_SendByte) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    usart.o(i.USART1_WaitTransmitComplete) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    bluetooth.o(i.Bluetooth_ConfigureModule) refers to bluetooth.o(i.Bluetooth_SetName) for Bluetooth_SetName
    bluetooth.o(i.Bluetooth_ConfigureModule) refers to delay.o(i.Delay_ms) for Delay_ms
    bluetooth.o(i.Bluetooth_ConfigureModule) refers to bluetooth.o(i.Bluetooth_SetPin) for Bluetooth_SetPin
    bluetooth.o(i.Bluetooth_ConfigureModule) refers to bluetooth.o(i.Bluetooth_TestConnection) for Bluetooth_TestConnection
    bluetooth.o(i.Bluetooth_GetStatus) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.Bluetooth_Init) refers to bluetooth.o(i.USART2_Init) for USART2_Init
    bluetooth.o(i.Bluetooth_Init) refers to delay.o(i.Delay_ms) for Delay_ms
    bluetooth.o(i.Bluetooth_Init) refers to bluetooth.o(i.Bluetooth_ConfigureModule) for Bluetooth_ConfigureModule
    bluetooth.o(i.Bluetooth_Init) refers to bluetooth.o(i.Bluetooth_SendStartupInfo) for Bluetooth_SendStartupInfo
    bluetooth.o(i.Bluetooth_Init) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.Bluetooth_ParseCommand) refers to strstr.o(.text) for strstr
    bluetooth.o(i.Bluetooth_ParseCommand) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    bluetooth.o(i.Bluetooth_ParseCommand) refers to bluetooth.o(i.Bluetooth_PrintConnectionInfo) for Bluetooth_PrintConnectionInfo
    bluetooth.o(i.Bluetooth_PrintConnectionInfo) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bluetooth.o(i.Bluetooth_PrintConnectionInfo) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bluetooth.o(i.Bluetooth_PrintConnectionInfo) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    bluetooth.o(i.Bluetooth_PrintConnectionInfo) refers to _printf_dec.o(.text) for _printf_int_dec
    bluetooth.o(i.Bluetooth_PrintConnectionInfo) refers to _printf_str.o(.text) for _printf_str
    bluetooth.o(i.Bluetooth_PrintConnectionInfo) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    bluetooth.o(i.Bluetooth_PrintConnectionInfo) refers to noretval__2sprintf.o(.text) for __2sprintf
    bluetooth.o(i.Bluetooth_PrintConnectionInfo) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    bluetooth.o(i.Bluetooth_PrintConnectionInfo) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.Bluetooth_ProcessReceivedData) refers to bluetooth.o(i.Bluetooth_ParseCommand) for Bluetooth_ParseCommand
    bluetooth.o(i.Bluetooth_ProcessReceivedData) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.Bluetooth_SendByte) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    bluetooth.o(i.Bluetooth_SendByte) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    bluetooth.o(i.Bluetooth_SendDiagnosticInfo) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bluetooth.o(i.Bluetooth_SendDiagnosticInfo) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bluetooth.o(i.Bluetooth_SendDiagnosticInfo) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    bluetooth.o(i.Bluetooth_SendDiagnosticInfo) refers to _printf_dec.o(.text) for _printf_int_dec
    bluetooth.o(i.Bluetooth_SendDiagnosticInfo) refers to _printf_str.o(.text) for _printf_str
    bluetooth.o(i.Bluetooth_SendDiagnosticInfo) refers to noretval__2sprintf.o(.text) for __2sprintf
    bluetooth.o(i.Bluetooth_SendDiagnosticInfo) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    bluetooth.o(i.Bluetooth_SendDiagnosticInfo) refers to bluetooth.o(.conststring) for .conststring
    bluetooth.o(i.Bluetooth_SendDiagnosticInfo) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.Bluetooth_SendErrorReport) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bluetooth.o(i.Bluetooth_SendErrorReport) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bluetooth.o(i.Bluetooth_SendErrorReport) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    bluetooth.o(i.Bluetooth_SendErrorReport) refers to _printf_dec.o(.text) for _printf_int_dec
    bluetooth.o(i.Bluetooth_SendErrorReport) refers to _printf_str.o(.text) for _printf_str
    bluetooth.o(i.Bluetooth_SendErrorReport) refers to noretval__2sprintf.o(.text) for __2sprintf
    bluetooth.o(i.Bluetooth_SendErrorReport) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    bluetooth.o(i.Bluetooth_SendErrorReport) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.Bluetooth_SendKeyAction) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bluetooth.o(i.Bluetooth_SendKeyAction) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    bluetooth.o(i.Bluetooth_SendKeyAction) refers to _printf_str.o(.text) for _printf_str
    bluetooth.o(i.Bluetooth_SendKeyAction) refers to noretval__2sprintf.o(.text) for __2sprintf
    bluetooth.o(i.Bluetooth_SendKeyAction) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    bluetooth.o(i.Bluetooth_SendKeyAction) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.Bluetooth_SendPerformanceData) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bluetooth.o(i.Bluetooth_SendPerformanceData) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    bluetooth.o(i.Bluetooth_SendPerformanceData) refers to _printf_dec.o(.text) for _printf_int_dec
    bluetooth.o(i.Bluetooth_SendPerformanceData) refers to noretval__2sprintf.o(.text) for __2sprintf
    bluetooth.o(i.Bluetooth_SendPerformanceData) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    bluetooth.o(i.Bluetooth_SendPerformanceData) refers to bluetooth.o(.conststring) for .conststring
    bluetooth.o(i.Bluetooth_SendPerformanceData) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.Bluetooth_SendPositionInfo) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bluetooth.o(i.Bluetooth_SendPositionInfo) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    bluetooth.o(i.Bluetooth_SendPositionInfo) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    bluetooth.o(i.Bluetooth_SendPositionInfo) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    bluetooth.o(i.Bluetooth_SendPositionInfo) refers to noretval__2sprintf.o(.text) for __2sprintf
    bluetooth.o(i.Bluetooth_SendPositionInfo) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    bluetooth.o(i.Bluetooth_SendPositionInfo) refers to bluetooth.o(.conststring) for .conststring
    bluetooth.o(i.Bluetooth_SendPositionInfo) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.Bluetooth_SendStartupInfo) refers to noretval__2sprintf.o(.text) for __2sprintf
    bluetooth.o(i.Bluetooth_SendStartupInfo) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    bluetooth.o(i.Bluetooth_SendStartupInfo) refers to bluetooth.o(.conststring) for .conststring
    bluetooth.o(i.Bluetooth_SendStartupInfo) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.Bluetooth_SendString) refers to bluetooth.o(i.Bluetooth_SendByte) for Bluetooth_SendByte
    bluetooth.o(i.Bluetooth_SetName) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bluetooth.o(i.Bluetooth_SetName) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    bluetooth.o(i.Bluetooth_SetName) refers to _printf_str.o(.text) for _printf_str
    bluetooth.o(i.Bluetooth_SetName) refers to noretval__2sprintf.o(.text) for __2sprintf
    bluetooth.o(i.Bluetooth_SetName) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    bluetooth.o(i.Bluetooth_SetPin) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bluetooth.o(i.Bluetooth_SetPin) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    bluetooth.o(i.Bluetooth_SetPin) refers to _printf_str.o(.text) for _printf_str
    bluetooth.o(i.Bluetooth_SetPin) refers to noretval__2sprintf.o(.text) for __2sprintf
    bluetooth.o(i.Bluetooth_SetPin) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    bluetooth.o(i.Bluetooth_TestConnection) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    bluetooth.o(i.Bluetooth_TestConnection) refers to delay.o(i.Delay_ms) for Delay_ms
    bluetooth.o(i.Bluetooth_Update) refers to bluetooth.o(i.Bluetooth_ProcessReceivedData) for Bluetooth_ProcessReceivedData
    bluetooth.o(i.Bluetooth_Update) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    bluetooth.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    bluetooth.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    bluetooth.o(i.USART2_IRQHandler) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.USART2_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    bluetooth.o(i.USART2_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    bluetooth.o(i.USART2_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    bluetooth.o(i.USART2_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    bluetooth.o(i.USART2_Init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    bluetooth.o(i.USART2_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    bluetooth.o(i.USART2_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    main.o(i.main) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.main) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main.o(i.main) refers to _printf_str.o(.text) for _printf_str
    main.o(i.main) refers to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    main.o(i.main) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(i.main) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.main) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(i.main) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to led.o(i.LED_Init) for LED_Init
    main.o(i.main) refers to servo.o(i.Servo_Init) for Servo_Init
    main.o(i.main) refers to key.o(i.Key_Init) for Key_Init
    main.o(i.main) refers to bluetooth.o(i.Bluetooth_Init) for Bluetooth_Init
    main.o(i.main) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.main) refers to delay.o(i.Delay_ms) for Delay_ms
    main.o(i.main) refers to servo.o(i.Servo_SetTorqueEnable) for Servo_SetTorqueEnable
    main.o(i.main) refers to servo.o(i.Servo_SetPosition) for Servo_SetPosition
    main.o(i.main) refers to statemachine.o(i.StateMachine_Init) for StateMachine_Init
    main.o(i.main) refers to automovement.o(i.AutoMovement_Init) for AutoMovement_Init
    main.o(i.main) refers to systemdiagnostics.o(i.SystemDiag_Init) for SystemDiag_Init
    main.o(i.main) refers to led.o(i.LED_ON) for LED_ON
    main.o(i.main) refers to systemdiagnostics.o(i.SystemDiag_RunFullCheck) for SystemDiag_RunFullCheck
    main.o(i.main) refers to systemdiagnostics.o(i.SystemDiag_ShowErrors) for SystemDiag_ShowErrors
    main.o(i.main) refers to led.o(i.LED_OFF) for LED_OFF
    main.o(i.main) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    main.o(i.main) refers to key.o(i.Key_IsPressed) for Key_IsPressed
    main.o(i.main) refers to timer.o(i.Timer_IsTimeout) for Timer_IsTimeout
    main.o(i.main) refers to systemdiagnostics.o(i.SystemDiag_ShowStatus) for SystemDiag_ShowStatus
    main.o(i.main) refers to key.o(i.Key_ClearEvent) for Key_ClearEvent
    main.o(i.main) refers to key.o(i.Key_IsClicked) for Key_IsClicked
    main.o(i.main) refers to systemdiagnostics.o(i.SystemDiag_ShowPerformance) for SystemDiag_ShowPerformance
    main.o(i.main) refers to systemdiagnostics.o(i.SystemDiag_QuickCheck) for SystemDiag_QuickCheck
    main.o(i.main) refers to statemachine.o(i.StateMachine_Update) for StateMachine_Update
    main.o(i.main) refers to systemdiagnostics.o(i.SystemDiag_RecordStateTransition) for SystemDiag_RecordStateTransition
    main.o(i.main) refers to automovement.o(i.AutoMovement_IsActive) for AutoMovement_IsActive
    main.o(i.main) refers to automovement.o(i.AutoMovement_Start) for AutoMovement_Start
    main.o(i.main) refers to systemdiagnostics.o(i.SystemDiag_LogError) for SystemDiag_LogError
    main.o(i.main) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    main.o(i.main) refers to timer.o(i.Timer_GetTimeMs) for Timer_GetTimeMs
    main.o(i.main) refers to strcpy.o(.text) for strcpy
    main.o(i.main) refers to bluetooth.o(i.Bluetooth_SendErrorReport) for Bluetooth_SendErrorReport
    main.o(i.main) refers to statemachine.o(i.StateMachine_TransitionTo) for StateMachine_TransitionTo
    main.o(i.main) refers to automovement.o(i.AutoMovement_Update) for AutoMovement_Update
    main.o(i.main) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    main.o(i.main) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    main.o(i.main) refers to systemdiagnostics.o(i.SystemDiag_RecordPathStep) for SystemDiag_RecordPathStep
    main.o(i.main) refers to systemdiagnostics.o(i.SystemDiag_RecordKeyPress) for SystemDiag_RecordKeyPress
    main.o(i.main) refers to automovement.o(i.AutoMovement_Stop) for AutoMovement_Stop
    main.o(i.main) refers to automovement.o(i.AutoMovement_ShowStatus) for AutoMovement_ShowStatus
    main.o(i.main) refers to statemachine.o(i.StateMachine_GetStateString) for StateMachine_GetStateString
    main.o(i.main) refers to noretval__2sprintf.o(.text) for __2sprintf
    main.o(i.main) refers to statemachine.o(i.StateMachine_IsPointRecorded) for StateMachine_IsPointRecorded
    main.o(i.main) refers to main.o(.data) for both_pressed_time
    main.o(i.main) refers to main.o(.constdata) for .constdata
    main.o(i.main) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.main) refers to bluetooth.o(i.Bluetooth_Update) for Bluetooth_Update
    main.o(i.main) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    main.o(i.main) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    main.o(i.main) refers to servo.o(i.Servo_ReadPosition) for Servo_ReadPosition
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to timer.o(i.Timer_IRQ_Handler) for Timer_IRQ_Handler
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    strncpy.o(.text) refers to rt_memclr.o(.text) for __aeabi_memclr
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixu) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixur) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fleqf.o(x$fpl$fleqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    frleqf.o(x$fpl$frleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frleqf.o(x$fpl$frleqf) refers to fleqf.o(x$fpl$fleqf) for __fpl_fcmple_InfNaN
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    atan2f.o(i.__softfp_atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.atan2f) for atan2f
    atan2f.o(i.atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.atan2f) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    atan2f.o(i.atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.atan2f) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    atan2f.o(i.atan2f) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    atan2f.o(i.atan2f) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    atan2f.o(i.atan2f) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    atan2f.o(i.atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f_x.o(i.____softfp_atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.____softfp_atan2f$lsc) refers to atan2f_x.o(i.__atan2f$lsc) for __atan2f$lsc
    atan2f_x.o(i.__atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.__atan2f$lsc) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    atan2f_x.o(i.__atan2f$lsc) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    atan2f_x.o(i.__atan2f$lsc) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    atan2f_x.o(i.__atan2f$lsc) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    atan2f_x.o(i.__atan2f$lsc) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    atan2f_x.o(i.__atan2f$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f_x.o(i.__atan2f$lsc) refers to _rserrno.o(.text) for __set_errno
    atanf.o(i.__softfp_atanf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atanf.o(i.__softfp_atanf) refers to atanf.o(i.atanf) for atanf
    atanf.o(i.atanf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atanf.o(i.atanf) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    atanf.o(i.atanf) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    atanf.o(i.atanf) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    atanf.o(i.atanf) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    atanf.o(i.atanf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atanf.o(i.atanf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atanf.o(i.atanf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    atanf_x.o(i.____softfp_atanf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atanf_x.o(i.____softfp_atanf$lsc) refers to atanf_x.o(i.__atanf$lsc) for __atanf$lsc
    atanf_x.o(i.__atanf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atanf_x.o(i.__atanf$lsc) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    atanf_x.o(i.__atanf$lsc) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    atanf_x.o(i.__atanf$lsc) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    atanf_x.o(i.__atanf$lsc) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    atanf_x.o(i.__atanf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to fsqrt.o(x$fpl$fsqrt) for _fsqrt
    sqrtf.o(i.__softfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to fsqrt.o(x$fpl$fsqrt) for _fsqrt
    sqrtf.o(i.sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to fsqrt.o(x$fpl$fsqrt) for _fsqrt
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers to fsqrt.o(x$fpl$fsqrt) for _fsqrt
    tan.o(i.__softfp_tan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan.o(i.__softfp_tan) refers to _rserrno.o(.text) for __set_errno
    tan.o(i.__softfp_tan) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    tan.o(i.__softfp_tan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan.o(i.__softfp_tan) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan.o(i.__softfp_tan) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan.o(i.tan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan.o(i.tan) refers to _rserrno.o(.text) for __set_errno
    tan.o(i.tan) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    tan.o(i.tan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan.o(i.tan) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan.o(i.tan) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan_x.o(i.____softfp_tan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_x.o(i.____softfp_tan$lsc) refers to _rserrno.o(.text) for __set_errno
    tan_x.o(i.____softfp_tan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan_x.o(i.____softfp_tan$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan_x.o(i.____softfp_tan$lsc) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan_x.o(i.__tan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_x.o(i.__tan$lsc) refers to _rserrno.o(.text) for __set_errno
    tan_x.o(i.__tan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan_x.o(i.__tan$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan_x.o(i.__tan$lsc) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tanf.o(i.__softfp_tanf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tanf.o(i.__softfp_tanf) refers to tanf.o(i.tanf) for tanf
    tanf.o(i.tanf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tanf.o(i.tanf) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    tanf.o(i.tanf) refers to frnd.o(x$fpl$frnd) for _frnd
    tanf.o(i.tanf) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    tanf.o(i.tanf) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    tanf.o(i.tanf) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    tanf.o(i.tanf) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    tanf.o(i.tanf) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    tanf.o(i.tanf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    tanf.o(i.tanf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    tanf.o(i.tanf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    tanf.o(i.tanf) refers to _rserrno.o(.text) for __set_errno
    tanf.o(i.tanf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    tanf.o(i.tanf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    tanf_x.o(i.____softfp_tanf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tanf_x.o(i.____softfp_tanf$lsc) refers to tanf_x.o(i.__tanf$lsc) for __tanf$lsc
    tanf_x.o(i.__tanf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tanf_x.o(i.__tanf$lsc) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    tanf_x.o(i.__tanf$lsc) refers to frnd.o(x$fpl$frnd) for _frnd
    tanf_x.o(i.__tanf$lsc) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    tanf_x.o(i.__tanf$lsc) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    tanf_x.o(i.__tanf$lsc) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    tanf_x.o(i.__tanf$lsc) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    tanf_x.o(i.__tanf$lsc) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    tanf_x.o(i.__tanf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    tanf_x.o(i.__tanf$lsc) refers to _rserrno.o(.text) for __set_errno
    tanf_x.o(i.__tanf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_char_common.o(.text) refers to __printf_ss_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmpi.o(x$fpl$fcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffix) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffixr) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frnd.o(x$fpl$frnd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frnd.o(x$fpl$frnd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fsqrt.o(x$fpl$fsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fsqrt.o(x$fpl$fsqrt) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    funder.o(i.__mathlib_flt_divzero) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    funder.o(i.__mathlib_flt_infnan) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    funder.o(i.__mathlib_flt_infnan2) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    funder.o(i.__mathlib_flt_invalid) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    funder.o(i.__mathlib_flt_overflow) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    funder.o(i.__mathlib_flt_posinfnan) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    funder.o(i.__mathlib_flt_underflow) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    rred.o(i.__ieee754_rem_pio2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    rredf.o(i.__mathlib_rredf2) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    rredf.o(i.__mathlib_rredf2) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    rredf.o(i.__mathlib_rredf2) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    rredf.o(i.__mathlib_rredf2) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    rredf.o(i.__mathlib_rredf2) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    rredf.o(i.__mathlib_rredf2) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_i.o(i.__kernel_tan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_i.o(i.__kernel_tan) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    tan_i.o(i.__kernel_tan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    tan_i.o(i.__kernel_tan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    tan_i.o(i.__kernel_tan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    tan_i.o(i.__kernel_tan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    tan_i.o(i.__kernel_tan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    tan_i.o(i.__kernel_tan) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    tan_i.o(i.__kernel_tan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    tan_i.o(i.__kernel_tan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    tan_i.o(i.__kernel_tan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    tan_i.o(i.__kernel_tan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    tan_i.o(i.__kernel_tan) refers to tan_i.o(.constdata) for .constdata
    tan_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_i_x.o(i.____kernel_tan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_i_x.o(i.____kernel_tan$lsc) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    tan_i_x.o(i.____kernel_tan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    tan_i_x.o(i.____kernel_tan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    tan_i_x.o(i.____kernel_tan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    tan_i_x.o(i.____kernel_tan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    tan_i_x.o(i.____kernel_tan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    tan_i_x.o(i.____kernel_tan$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    tan_i_x.o(i.____kernel_tan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    tan_i_x.o(i.____kernel_tan$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    tan_i_x.o(i.____kernel_tan$lsc) refers to tan_i_x.o(.constdata) for .constdata
    tan_i_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    scalbnf.o(x$fpl$scalbnf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbnf.o(x$fpl$scalbnf) refers to fcheck1.o(x$fpl$fcheck1) for __fpl_fcheck_NaN1
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_md.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    fcheck1.o(x$fpl$fcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcheck1.o(x$fpl$fcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing system_stm32f10x.o(.data), (20 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (40 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetITStatus), (40 bytes).
    Removing stm32f10x_exti.o(i.EXTI_Init), (148 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (144 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (42 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (56 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (58 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (38 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (26 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (236 bytes).
    Removing stm32f10x_i2c.o(i.I2C_NACKPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (128 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (144 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_ARRPreloadConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CtrlPWMOutputs), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (488 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (172 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (160 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing delay.o(i.Delay_s), (24 bytes).
    Removing led.o(i.LED_Turn), (36 bytes).
    Removing key.o(i.Key_GetEvent), (56 bytes).
    Removing key.o(i.Key_GetNum), (92 bytes).
    Removing key.o(i.Key_IsReleased), (40 bytes).
    Removing oled.o(i.OLED_Pow), (20 bytes).
    Removing oled.o(i.OLED_ShowBinNum), (62 bytes).
    Removing oled.o(i.OLED_ShowHexNum), (84 bytes).
    Removing oled.o(i.OLED_ShowNum), (68 bytes).
    Removing oled.o(i.OLED_ShowSignedNum), (102 bytes).
    Removing geometry.o(i.Geometry_CalculateAngleBetweenPoints), (34 bytes).
    Removing geometry.o(i.Geometry_NormalizeAngle), (56 bytes).
    Removing geometry.o(i.Geometry_TestConversion), (112 bytes).
    Removing geometry.o(i.Path_CalculateOptimalSteps), (52 bytes).
    Removing geometry.o(i.Path_CalculatePathLength), (14 bytes).
    Removing geometry.o(i.Path_Reset), (58 bytes).
    Removing geometry.o(i.Path_TestInterpolation), (192 bytes).
    Removing geometry.o(.constdata), (24 bytes).
    Removing statemachine.o(i.StateMachine_GetCurrentState), (12 bytes).
    Removing statemachine.o(i.StateMachine_GetDirectionString), (28 bytes).
    Removing statemachine.o(i.StateMachine_GetMoveProgress), (44 bytes).
    Removing statemachine.o(i.StateMachine_GetTick), (8 bytes).
    Removing statemachine.o(i.StateMachine_IsMoving), (14 bytes).
    Removing statemachine.o(i.StateMachine_IsSystemReady), (14 bytes).
    Removing statemachine.o(i.StateMachine_IsTimeout), (16 bytes).
    Removing statemachine.o(i.StateMachine_ReadCurrentPosition), (80 bytes).
    Removing statemachine.o(i.StateMachine_SetServoTorque), (26 bytes).
    Removing timer.o(i.Timer_DelayMs), (30 bytes).
    Removing timer.o(i.Timer_DisableCallback), (12 bytes).
    Removing timer.o(i.Timer_EnableCallback), (12 bytes).
    Removing timer.o(i.Timer_GetCpuUsage), (12 bytes).
    Removing timer.o(i.Timer_GetPeriodMs), (4 bytes).
    Removing timer.o(i.Timer_IsDelayComplete), (16 bytes).
    Removing timer.o(i.Timer_Reset), (44 bytes).
    Removing timer.o(i.Timer_SetCallback), (12 bytes).
    Removing timer.o(i.Timer_Stop), (24 bytes).
    Removing timer.o(i.Timer_UpdateCpuUsage), (32 bytes).
    Removing manualrecord.o(i.ManualRecord_Cancel), (36 bytes).
    Removing manualrecord.o(i.ManualRecord_GetResult), (48 bytes).
    Removing manualrecord.o(i.ManualRecord_GetStateString), (116 bytes).
    Removing manualrecord.o(i.ManualRecord_Init), (52 bytes).
    Removing manualrecord.o(i.ManualRecord_IsComplete), (28 bytes).
    Removing manualrecord.o(i.ManualRecord_IsPositionReasonable), (104 bytes).
    Removing manualrecord.o(i.ManualRecord_LoadServos), (40 bytes).
    Removing manualrecord.o(i.ManualRecord_ReadCurrentPosition), (72 bytes).
    Removing manualrecord.o(i.ManualRecord_SavePointData), (68 bytes).
    Removing manualrecord.o(i.ManualRecord_ShowInstructions), (152 bytes).
    Removing manualrecord.o(i.ManualRecord_ShowProgress), (200 bytes).
    Removing manualrecord.o(i.ManualRecord_Start), (56 bytes).
    Removing manualrecord.o(i.ManualRecord_TransitionTo), (136 bytes).
    Removing manualrecord.o(i.ManualRecord_UnloadServos), (34 bytes).
    Removing manualrecord.o(i.ManualRecord_Update), (268 bytes).
    Removing manualrecord.o(i.ManualRecord_ValidatePosition), (34 bytes).
    Removing manualrecord.o(i.ManualRecord_WaitUserAdjustment), (54 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_BackupConfiguration), (92 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_CalibrateServos), (364 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_ClearErrorLog), (30 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_GenerateReport), (204 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_GetConfig), (14 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_GetErrorCount), (14 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_GetModuleName), (136 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_OptimizePerformance), (200 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_PrintDiagnostic), (160 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_RecordGeometryCalc), (14 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_RecordServoCommand), (14 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_RecordServoError), (76 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_ResetStatistics), (44 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_RestoreConfiguration), (96 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_SetConfig), (22 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_Update), (50 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_ValidateAccuracy), (392 bytes).
    Removing usart.o(i.USART1_ReceiveBuffer), (26 bytes).
    Removing usart.o(i.USART1_ReceiveByte), (32 bytes).
    Removing usart.o(i.USART1_SendByte), (48 bytes).
    Removing usart.o(i.USART1_WaitTransmitComplete), (24 bytes).
    Removing bluetooth.o(i.Bluetooth_GetStatus), (12 bytes).
    Removing bluetooth.o(i.Bluetooth_SendDiagnosticInfo), (148 bytes).
    Removing bluetooth.o(i.Bluetooth_SendPerformanceData), (64 bytes).
    Removing bluetooth.o(i.Bluetooth_SendPositionInfo), (100 bytes).

276 unused section(s) (total 12568 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fcheck1.s                       0x00000000   Number         0  fcheck1.o ABSOLUTE
    ../fplib/fcmpi.s                         0x00000000   Number         0  fcmpi.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/ffix.s                          0x00000000   Number         0  ffix.o ABSOLUTE
    ../fplib/ffixu.s                         0x00000000   Number         0  ffixu.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fleqf.s                         0x00000000   Number         0  fleqf.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/frleqf.s                        0x00000000   Number         0  frleqf.o ABSOLUTE
    ../fplib/frnd.s                          0x00000000   Number         0  frnd.o ABSOLUTE
    ../fplib/fsqrt.s                         0x00000000   Number         0  fsqrt.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/scalbnf.s                       0x00000000   Number         0  scalbnf.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f_x.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f.o ABSOLUTE
    ../mathlib/atanf.c                       0x00000000   Number         0  atanf.o ABSOLUTE
    ../mathlib/atanf.c                       0x00000000   Number         0  atanf_x.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/rred.c                        0x00000000   Number         0  rred.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_x.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    ../mathlib/tan.c                         0x00000000   Number         0  tan.o ABSOLUTE
    ../mathlib/tan.c                         0x00000000   Number         0  tan_x.o ABSOLUTE
    ../mathlib/tan_i.c                       0x00000000   Number         0  tan_i_x.o ABSOLUTE
    ../mathlib/tan_i.c                       0x00000000   Number         0  tan_i.o ABSOLUTE
    ../mathlib/tanf.c                        0x00000000   Number         0  tanf.o ABSOLUTE
    ../mathlib/tanf.c                        0x00000000   Number         0  tanf_x.o ABSOLUTE
    Hardware\AutoMovement.c                  0x00000000   Number         0  automovement.o ABSOLUTE
    Hardware\Bluetooth.c                     0x00000000   Number         0  bluetooth.o ABSOLUTE
    Hardware\Geometry.c                      0x00000000   Number         0  geometry.o ABSOLUTE
    Hardware\Key.c                           0x00000000   Number         0  key.o ABSOLUTE
    Hardware\LED.c                           0x00000000   Number         0  led.o ABSOLUTE
    Hardware\ManualRecord.c                  0x00000000   Number         0  manualrecord.o ABSOLUTE
    Hardware\OLED.c                          0x00000000   Number         0  oled.o ABSOLUTE
    Hardware\Servo.c                         0x00000000   Number         0  servo.o ABSOLUTE
    Hardware\StateMachine.c                  0x00000000   Number         0  statemachine.o ABSOLUTE
    Hardware\SystemDiagnostics.c             0x00000000   Number         0  systemdiagnostics.o ABSOLUTE
    Hardware\Timer.c                         0x00000000   Number         0  timer.o ABSOLUTE
    Hardware\usart.c                         0x00000000   Number         0  usart.o ABSOLUTE
    Library\misc.c                           0x00000000   Number         0  misc.o ABSOLUTE
    Library\stm32f10x_exti.c                 0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    Library\stm32f10x_gpio.c                 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    Library\stm32f10x_i2c.c                  0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    Library\stm32f10x_rcc.c                  0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    Library\stm32f10x_tim.c                  0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    Library\stm32f10x_usart.c                0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    Start\\core_cm3.c                        0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\core_cm3.c                         0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\startup_stm32f10x_md.s             0x00000000   Number         0  startup_stm32f10x_md.o ABSOLUTE
    Start\system_stm32f10x.c                 0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    System\Delay.c                           0x00000000   Number         0  delay.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\stm32f10x_it.c                      0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f10x_md.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000128   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000144   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x08000160   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x08000160   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000009  0x08000166   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x0800016c   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$00000013  0x08000172   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x08000178   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x0800017e   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000182   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x08000184   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x08000184   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000184   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000184   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000184   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000184   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x0800018a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x0800018a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x0800018a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x0800018a   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000194   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000196   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000198   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000198   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000198   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000198   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000198   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000198   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000198   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x0800019a   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0800019a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0800019a   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001a0   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001a0   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001a4   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001a4   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001ac   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001ae   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001ae   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001b2   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001b8   Section       64  startup_stm32f10x_md.o(.text)
    .text                                    0x080001f8   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x08000220   Section        0  _printf_str.o(.text)
    .text                                    0x08000274   Section        0  _printf_dec.o(.text)
    .text                                    0x080002ec   Section        0  __printf_ss_wp.o(.text)
    .text                                    0x0800044c   Section        0  strstr.o(.text)
    .text                                    0x08000470   Section        0  strcpy.o(.text)
    .text                                    0x080004b8   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x0800051c   Section       78  rt_memclr_w.o(.text)
    .text                                    0x0800056a   Section       86  strncpy.o(.text)
    .text                                    0x080005c0   Section        0  heapauxi.o(.text)
    .text                                    0x080005c6   Section        0  _rserrno.o(.text)
    .text                                    0x080005dc   Section        0  _printf_intcommon.o(.text)
    .text                                    0x0800068e   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000691   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08000aac   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000aad   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000adc   Section        0  _sputc.o(.text)
    .text                                    0x08000ae6   Section        0  _printf_char.o(.text)
    .text                                    0x08000b12   Section       68  rt_memclr.o(.text)
    .text                                    0x08000b58   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08000b60   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08000b68   Section      138  lludiv10.o(.text)
    .text                                    0x08000bf4   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08000c74   Section        0  bigflt0.o(.text)
    .text                                    0x08000d58   Section        8  libspace.o(.text)
    .text                                    0x08000d60   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000daa   Section        0  exit.o(.text)
    .text                                    0x08000dbc   Section      128  strcmpv7m.o(.text)
    .text                                    0x08000e3c   Section        0  sys_exit.o(.text)
    .text                                    0x08000e48   Section        2  use_no_semi.o(.text)
    .text                                    0x08000e4a   Section        0  indicate_semi.o(.text)
    CL$$btod_d2e                             0x08000e4a   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08000e88   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08000ece   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08000f2e   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08001266   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08001342   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x0800136c   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08001396   Section      580  btod.o(CL$$btod_mult_common)
    i.AutoMovement_ArePointsValid            0x080015dc   Section        0  automovement.o(i.AutoMovement_ArePointsValid)
    i.AutoMovement_CheckSafety               0x08001638   Section        0  automovement.o(i.AutoMovement_CheckSafety)
    i.AutoMovement_EmergencyStop             0x08001652   Section        0  automovement.o(i.AutoMovement_EmergencyStop)
    i.AutoMovement_ExecuteNextStep           0x08001678   Section        0  automovement.o(i.AutoMovement_ExecuteNextStep)
    i.AutoMovement_GetProgress               0x080016b8   Section        0  automovement.o(i.AutoMovement_GetProgress)
    i.AutoMovement_GetStateString            0x080016f0   Section        0  automovement.o(i.AutoMovement_GetStateString)
    i.AutoMovement_HandleDirectionChange     0x08001770   Section        0  automovement.o(i.AutoMovement_HandleDirectionChange)
    i.AutoMovement_HandleError               0x080017dc   Section        0  automovement.o(i.AutoMovement_HandleError)
    i.AutoMovement_HandleMovingToStart       0x0800184a   Section        0  automovement.o(i.AutoMovement_HandleMovingToStart)
    i.AutoMovement_HandlePathMoving          0x0800189a   Section        0  automovement.o(i.AutoMovement_HandlePathMoving)
    i.AutoMovement_HandlePreparing           0x080018d6   Section        0  automovement.o(i.AutoMovement_HandlePreparing)
    i.AutoMovement_Init                      0x08001920   Section        0  automovement.o(i.AutoMovement_Init)
    i.AutoMovement_IsActive                  0x0800197c   Section        0  automovement.o(i.AutoMovement_IsActive)
    i.AutoMovement_LoadPointsFromRecord      0x0800198a   Section        0  automovement.o(i.AutoMovement_LoadPointsFromRecord)
    i.AutoMovement_MoveToPosition            0x080019dc   Section        0  automovement.o(i.AutoMovement_MoveToPosition)
    i.AutoMovement_ShowStatus                0x080019f8   Section        0  automovement.o(i.AutoMovement_ShowStatus)
    i.AutoMovement_Start                     0x08001b24   Section        0  automovement.o(i.AutoMovement_Start)
    i.AutoMovement_Stop                      0x08001b7c   Section        0  automovement.o(i.AutoMovement_Stop)
    i.AutoMovement_TransitionTo              0x08001b96   Section        0  automovement.o(i.AutoMovement_TransitionTo)
    i.AutoMovement_Update                    0x08001bf8   Section        0  automovement.o(i.AutoMovement_Update)
    i.Bluetooth_ConfigureModule              0x08001ca4   Section        0  bluetooth.o(i.Bluetooth_ConfigureModule)
    i.Bluetooth_Init                         0x08001ce0   Section        0  bluetooth.o(i.Bluetooth_Init)
    i.Bluetooth_ParseCommand                 0x08001d30   Section        0  bluetooth.o(i.Bluetooth_ParseCommand)
    i.Bluetooth_PrintConnectionInfo          0x08001df4   Section        0  bluetooth.o(i.Bluetooth_PrintConnectionInfo)
    i.Bluetooth_ProcessReceivedData          0x08001ec0   Section        0  bluetooth.o(i.Bluetooth_ProcessReceivedData)
    i.Bluetooth_SendByte                     0x08001f40   Section        0  bluetooth.o(i.Bluetooth_SendByte)
    i.Bluetooth_SendErrorReport              0x08001f70   Section        0  bluetooth.o(i.Bluetooth_SendErrorReport)
    i.Bluetooth_SendKeyAction                0x08002024   Section        0  bluetooth.o(i.Bluetooth_SendKeyAction)
    i.Bluetooth_SendStartupInfo              0x08002118   Section        0  bluetooth.o(i.Bluetooth_SendStartupInfo)
    i.Bluetooth_SendString                   0x08002140   Section        0  bluetooth.o(i.Bluetooth_SendString)
    i.Bluetooth_SetName                      0x08002158   Section        0  bluetooth.o(i.Bluetooth_SetName)
    i.Bluetooth_SetPin                       0x08002180   Section        0  bluetooth.o(i.Bluetooth_SetPin)
    i.Bluetooth_TestConnection               0x080021a8   Section        0  bluetooth.o(i.Bluetooth_TestConnection)
    i.Bluetooth_Update                       0x080021c0   Section        0  bluetooth.o(i.Bluetooth_Update)
    i.BusFault_Handler                       0x080021dc   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x080021e0   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.Delay_ms                               0x080021e2   Section        0  delay.o(i.Delay_ms)
    i.Delay_us                               0x080021fa   Section        0  delay.o(i.Delay_us)
    i.GPIO_Init                              0x08002228   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_ReadInputDataBit                  0x0800233e   Section        0  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x08002350   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08002354   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.GPIO_WriteBit                          0x08002358   Section        0  stm32f10x_gpio.o(i.GPIO_WriteBit)
    i.Geometry_CalculateDistance             0x08002362   Section        0  geometry.o(i.Geometry_CalculateDistance)
    i.Geometry_ClampServoAngle               0x080023a0   Section        0  geometry.o(i.Geometry_ClampServoAngle)
    i.Geometry_ClampWallPoint                0x08002404   Section        0  geometry.o(i.Geometry_ClampWallPoint)
    i.Geometry_IsServoAngleValid             0x08002468   Section        0  geometry.o(i.Geometry_IsServoAngleValid)
    i.Geometry_IsWallPointValid              0x080024a4   Section        0  geometry.o(i.Geometry_IsWallPointValid)
    i.Geometry_ServoToWall                   0x080024e0   Section        0  geometry.o(i.Geometry_ServoToWall)
    i.Geometry_WallToServo                   0x08002578   Section        0  geometry.o(i.Geometry_WallToServo)
    i.HardFault_Handler                      0x08002604   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.Key_ClearEvent                         0x08002608   Section        0  key.o(i.Key_ClearEvent)
    i.Key_Init                               0x0800262c   Section        0  key.o(i.Key_Init)
    i.Key_IsClicked                          0x080026ac   Section        0  key.o(i.Key_IsClicked)
    i.Key_IsPressed                          0x080026cc   Section        0  key.o(i.Key_IsPressed)
    i.Key_ReadPin                            0x080026f4   Section        0  key.o(i.Key_ReadPin)
    i.Key_Scan                               0x08002720   Section        0  key.o(i.Key_Scan)
    i.Key_UpdateState                        0x08002730   Section        0  key.o(i.Key_UpdateState)
    i.LED_Init                               0x08002798   Section        0  led.o(i.LED_Init)
    i.LED_OFF                                0x080027cc   Section        0  led.o(i.LED_OFF)
    i.LED_ON                                 0x080027dc   Section        0  led.o(i.LED_ON)
    i.ManualRecord_IsPointSaved              0x080027ec   Section        0  manualrecord.o(i.ManualRecord_IsPointSaved)
    i.ManualRecord_LoadPointData             0x08002804   Section        0  manualrecord.o(i.ManualRecord_LoadPointData)
    i.MemManage_Handler                      0x0800284c   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08002850   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08002854   Section        0  misc.o(i.NVIC_Init)
    i.OLED_Clear                             0x080028c4   Section        0  oled.o(i.OLED_Clear)
    i.OLED_I2C_Init                          0x080028f0   Section        0  oled.o(i.OLED_I2C_Init)
    i.OLED_I2C_SendByte                      0x08002940   Section        0  oled.o(i.OLED_I2C_SendByte)
    i.OLED_I2C_Start                         0x0800299c   Section        0  oled.o(i.OLED_I2C_Start)
    i.OLED_I2C_Stop                          0x080029d0   Section        0  oled.o(i.OLED_I2C_Stop)
    i.OLED_Init                              0x080029f8   Section        0  oled.o(i.OLED_Init)
    i.OLED_SetCursor                         0x08002aa6   Section        0  oled.o(i.OLED_SetCursor)
    i.OLED_ShowChar                          0x08002ac8   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowString                        0x08002b3c   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_WriteCommand                      0x08002b64   Section        0  oled.o(i.OLED_WriteCommand)
    i.OLED_WriteData                         0x08002b84   Section        0  oled.o(i.OLED_WriteData)
    i.Path_GetNextStep                       0x08002ba4   Section        0  geometry.o(i.Path_GetNextStep)
    i.Path_Initialize                        0x08002c40   Section        0  geometry.o(i.Path_Initialize)
    i.Path_IsComplete                        0x08002caa   Section        0  geometry.o(i.Path_IsComplete)
    i.Path_LinearInterpolate                 0x08002cc2   Section        0  geometry.o(i.Path_LinearInterpolate)
    i.Path_SetDirection                      0x08002d4e   Section        0  geometry.o(i.Path_SetDirection)
    i.Path_ValidatePath                      0x08002d84   Section        0  geometry.o(i.Path_ValidatePath)
    i.PendSV_Handler                         0x08002dd4   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_APB1PeriphClockCmd                 0x08002dd8   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08002df8   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x08002e18   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x08002eec   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.Servo_AngleToPosition                  0x08002ef0   Section        0  servo.o(i.Servo_AngleToPosition)
    i.Servo_CalculateChecksum                0x08002f30   Section        0  servo.o(i.Servo_CalculateChecksum)
    i.Servo_GetStatus                        0x08002f4e   Section        0  servo.o(i.Servo_GetStatus)
    i.Servo_Init                             0x08002fc4   Section        0  servo.o(i.Servo_Init)
    i.Servo_PositionToAngle                  0x08002fd4   Section        0  servo.o(i.Servo_PositionToAngle)
    i.Servo_ReadPosition                     0x08003004   Section        0  servo.o(i.Servo_ReadPosition)
    i.Servo_ReadTemperature                  0x08003088   Section        0  servo.o(i.Servo_ReadTemperature)
    i.Servo_ReadVoltage                      0x080030c6   Section        0  servo.o(i.Servo_ReadVoltage)
    i.Servo_ReceiveResponse                  0x0800310c   Section        0  servo.o(i.Servo_ReceiveResponse)
    i.Servo_SendCommand                      0x08003224   Section        0  servo.o(i.Servo_SendCommand)
    i.Servo_SetPosition                      0x080032a4   Section        0  servo.o(i.Servo_SetPosition)
    i.Servo_SetPositionWithTime              0x080032b8   Section        0  servo.o(i.Servo_SetPositionWithTime)
    i.Servo_SetTorqueEnable                  0x080032ee   Section        0  servo.o(i.Servo_SetTorqueEnable)
    i.SetSysClock                            0x08003314   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x08003315   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x0800331c   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x0800331d   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.StateMachine_GetStateString            0x080033fc   Section        0  statemachine.o(i.StateMachine_GetStateString)
    i.StateMachine_HandleRecordKey           0x0800343c   Section        0  statemachine.o(i.StateMachine_HandleRecordKey)
    i.StateMachine_HandleTriggerKey          0x08003470   Section        0  statemachine.o(i.StateMachine_HandleTriggerKey)
    i.StateMachine_Init                      0x080034a0   Section        0  statemachine.o(i.StateMachine_Init)
    i.StateMachine_IsPointRecorded           0x08003528   Section        0  statemachine.o(i.StateMachine_IsPointRecorded)
    i.StateMachine_MoveServoToPosition       0x08003540   Section        0  statemachine.o(i.StateMachine_MoveServoToPosition)
    i.StateMachine_RecordPointA              0x0800355c   Section        0  statemachine.o(i.StateMachine_RecordPointA)
    i.StateMachine_RecordPointB              0x08003874   Section        0  statemachine.o(i.StateMachine_RecordPointB)
    i.StateMachine_StartAutoMovement         0x08003b98   Section        0  statemachine.o(i.StateMachine_StartAutoMovement)
    i.StateMachine_StopAutoMovement          0x08003c14   Section        0  statemachine.o(i.StateMachine_StopAutoMovement)
    i.StateMachine_ToggleDirection           0x08003c58   Section        0  statemachine.o(i.StateMachine_ToggleDirection)
    i.StateMachine_TransitionTo              0x08003c98   Section        0  statemachine.o(i.StateMachine_TransitionTo)
    i.StateMachine_Update                    0x08003cdc   Section        0  statemachine.o(i.StateMachine_Update)
    i.StateMachine_UpdateAutoMovement        0x08003d50   Section        0  statemachine.o(i.StateMachine_UpdateAutoMovement)
    i.SysTick_Handler                        0x08003da0   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemDiag_CheckGeometry               0x08003da4   Section        0  systemdiagnostics.o(i.SystemDiag_CheckGeometry)
    i.SystemDiag_CheckKeys                   0x08003e6c   Section        0  systemdiagnostics.o(i.SystemDiag_CheckKeys)
    i.SystemDiag_CheckMemory                 0x08003e7c   Section        0  systemdiagnostics.o(i.SystemDiag_CheckMemory)
    i.SystemDiag_CheckServos                 0x08003e8c   Section        0  systemdiagnostics.o(i.SystemDiag_CheckServos)
    i.SystemDiag_CheckTimer                  0x08004004   Section        0  systemdiagnostics.o(i.SystemDiag_CheckTimer)
    i.SystemDiag_GetLevelString              0x0800407c   Section        0  systemdiagnostics.o(i.SystemDiag_GetLevelString)
    i.SystemDiag_Init                        0x080040c8   Section        0  systemdiagnostics.o(i.SystemDiag_Init)
    i.SystemDiag_LoadDefaultConfig           0x0800411c   Section        0  systemdiagnostics.o(i.SystemDiag_LoadDefaultConfig)
    i.SystemDiag_LogError                    0x08004142   Section        0  systemdiagnostics.o(i.SystemDiag_LogError)
    i.SystemDiag_QuickCheck                  0x08004194   Section        0  systemdiagnostics.o(i.SystemDiag_QuickCheck)
    i.SystemDiag_RecordKeyPress              0x08004264   Section        0  systemdiagnostics.o(i.SystemDiag_RecordKeyPress)
    i.SystemDiag_RecordPathStep              0x08004272   Section        0  systemdiagnostics.o(i.SystemDiag_RecordPathStep)
    i.SystemDiag_RecordStateTransition       0x080042b6   Section        0  systemdiagnostics.o(i.SystemDiag_RecordStateTransition)
    i.SystemDiag_RunFullCheck                0x080042c4   Section        0  systemdiagnostics.o(i.SystemDiag_RunFullCheck)
    i.SystemDiag_ShowErrors                  0x08004474   Section        0  systemdiagnostics.o(i.SystemDiag_ShowErrors)
    i.SystemDiag_ShowPerformance             0x08004554   Section        0  systemdiagnostics.o(i.SystemDiag_ShowPerformance)
    i.SystemDiag_ShowStatus                  0x080045e0   Section        0  systemdiagnostics.o(i.SystemDiag_ShowStatus)
    i.SystemInit                             0x08004694   Section        0  system_stm32f10x.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x080046f4   Section        0  stm32f10x_it.o(i.TIM2_IRQHandler)
    i.TIM_ClearITPendingBit                  0x080046fc   Section        0  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x08004702   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_GetITStatus                        0x0800471a   Section        0  stm32f10x_tim.o(i.TIM_GetITStatus)
    i.TIM_ITConfig                           0x0800473c   Section        0  stm32f10x_tim.o(i.TIM_ITConfig)
    i.TIM_TimeBaseInit                       0x08004750   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.Timer_ConfigureHardware                0x080047f4   Section        0  timer.o(i.Timer_ConfigureHardware)
    i.Timer_GetFrequency                     0x08004852   Section        0  timer.o(i.Timer_GetFrequency)
    i.Timer_GetTick                          0x08004858   Section        0  timer.o(i.Timer_GetTick)
    i.Timer_GetTimeMs                        0x08004864   Section        0  timer.o(i.Timer_GetTimeMs)
    i.Timer_IRQ_Handler                      0x08004874   Section        0  timer.o(i.Timer_IRQ_Handler)
    i.Timer_Init                             0x08004924   Section        0  timer.o(i.Timer_Init)
    i.Timer_IsPathStepReady                  0x08004950   Section        0  timer.o(i.Timer_IsPathStepReady)
    i.Timer_IsRunning                        0x0800495c   Section        0  timer.o(i.Timer_IsRunning)
    i.Timer_IsTimeout                        0x08004968   Section        0  timer.o(i.Timer_IsTimeout)
    i.Timer_ResetPathStep                    0x08004984   Section        0  timer.o(i.Timer_ResetPathStep)
    i.Timer_Start                            0x08004998   Section        0  timer.o(i.Timer_Start)
    i.Timer_StartPathMovement                0x080049b0   Section        0  timer.o(i.Timer_StartPathMovement)
    i.Timer_StopPathMovement                 0x080049c4   Section        0  timer.o(i.Timer_StopPathMovement)
    i.USART1_Init                            0x080049d8   Section        0  usart.o(i.USART1_Init)
    i.USART1_SendBuffer                      0x08004a58   Section        0  usart.o(i.USART1_SendBuffer)
    i.USART2_IRQHandler                      0x08004aac   Section        0  bluetooth.o(i.USART2_IRQHandler)
    i.USART2_Init                            0x08004b08   Section        0  bluetooth.o(i.USART2_Init)
    USART2_Init                              0x08004b09   Thumb Code   158  bluetooth.o(i.USART2_Init)
    i.USART_ClearITPendingBit                0x08004bb0   Section        0  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    i.USART_Cmd                              0x08004bce   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetFlagStatus                    0x08004be6   Section        0  stm32f10x_usart.o(i.USART_GetFlagStatus)
    i.USART_GetITStatus                      0x08004c00   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x08004c54   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08004ca0   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08004d78   Section        0  stm32f10x_usart.o(i.USART_ReceiveData)
    i.USART_SendData                         0x08004d82   Section        0  stm32f10x_usart.o(i.USART_SendData)
    i.UsageFault_Handler                     0x08004d8a   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x08004d8e   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__ARM_fpclassifyf                      0x08004db6   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__ARM_isinff                           0x08004ddc   Section        0  geometry.o(i.__ARM_isinff)
    __ARM_isinff                             0x08004ddd   Thumb Code    20  geometry.o(i.__ARM_isinff)
    i.__ARM_isnanf                           0x08004df0   Section        0  geometry.o(i.__ARM_isnanf)
    __ARM_isnanf                             0x08004df1   Thumb Code    14  geometry.o(i.__ARM_isnanf)
    i.__ieee754_rem_pio2                     0x08004e00   Section        0  rred.o(i.__ieee754_rem_pio2)
    i.__kernel_poly                          0x08005188   Section        0  poly.o(i.__kernel_poly)
    i.__kernel_tan                           0x08005234   Section        0  tan_i.o(i.__kernel_tan)
    i.__mathlib_dbl_infnan                   0x080054b0   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_invalid                  0x080054b6   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_underflow                0x080054c4   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__mathlib_flt_infnan                   0x080054d4   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_invalid                  0x080054da   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__mathlib_flt_underflow                0x080054e2   Section        0  funder.o(i.__mathlib_flt_underflow)
    i.__mathlib_rredf2                       0x080054ec   Section        0  rredf.o(i.__mathlib_rredf2)
    i._is_digit                              0x08005654   Section        0  __printf_wp.o(i._is_digit)
    i.atanf                                  0x08005664   Section        0  atanf.o(i.atanf)
    i.main                                   0x080057e0   Section        0  main.o(i.main)
    i.sqrtf                                  0x08006010   Section        0  sqrtf.o(i.sqrtf)
    i.tan                                    0x0800603c   Section        0  tan.o(i.tan)
    i.tanf                                   0x080060a0   Section        0  tanf.o(i.tanf)
    locale$$code                             0x08006214   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$d2f                                0x08006240   Section       98  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x080062a4   Section      336  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x080062b5   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcheck1                            0x080063f4   Section       16  dcheck1.o(x$fpl$dcheck1)
    x$fpl$ddiv                               0x08006404   Section      688  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x0800640b   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dfix                               0x080066b4   Section       94  dfix.o(x$fpl$dfix)
    x$fpl$dflt                               0x08006712   Section       46  dflt_clz.o(x$fpl$dflt)
    x$fpl$dfltu                              0x08006740   Section       38  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dmul                               0x08006768   Section      340  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x080068bc   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08006958   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$drsb                               0x08006964   Section       22  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsub                               0x0800697c   Section      468  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x0800698d   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x08006b50   Section       86  f2d.o(x$fpl$f2d)
    x$fpl$fadd                               0x08006ba8   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x08006bb7   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$fcheck1                            0x08006c6c   Section       12  fcheck1.o(x$fpl$fcheck1)
    x$fpl$fcmpinf                            0x08006c78   Section       24  fcmpi.o(x$fpl$fcmpinf)
    x$fpl$fdiv                               0x08006c90   Section      388  fdiv.o(x$fpl$fdiv)
    _fdiv1                                   0x08006c91   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    x$fpl$ffix                               0x08006e14   Section       54  ffix.o(x$fpl$ffix)
    x$fpl$ffixu                              0x08006e4c   Section       62  ffixu.o(x$fpl$ffixu)
    x$fpl$fflt                               0x08006e8c   Section       48  fflt_clz.o(x$fpl$fflt)
    x$fpl$ffltu                              0x08006ebc   Section       38  fflt_clz.o(x$fpl$ffltu)
    x$fpl$fleqf                              0x08006ee4   Section      104  fleqf.o(x$fpl$fleqf)
    x$fpl$fmul                               0x08006f4c   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x0800704e   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x080070da   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$frleqf                             0x080070e4   Section       98  frleqf.o(x$fpl$frleqf)
    x$fpl$frnd                               0x08007148   Section       96  frnd.o(x$fpl$frnd)
    x$fpl$frsb                               0x080071a8   Section       20  faddsub_clz.o(x$fpl$frsb)
    x$fpl$fsqrt                              0x080071bc   Section      272  fsqrt.o(x$fpl$fsqrt)
    x$fpl$fsub                               0x080072cc   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x080072db   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    x$fpl$printf1                            0x080073b6   Section        4  printf1.o(x$fpl$printf1)
    x$fpl$retnan                             0x080073ba   Section      100  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x0800741e   Section       92  scalbn.o(x$fpl$scalbn)
    x$fpl$scalbnf                            0x0800747a   Section       76  scalbnf.o(x$fpl$scalbnf)
    x$fpl$trapveneer                         0x080074c6   Section       48  trapv.o(x$fpl$trapveneer)
    .constdata                               0x080074f6   Section     1520  oled.o(.constdata)
    x$fpl$usenofp                            0x080074f6   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x08007ae8   Section       16  statemachine.o(.constdata)
    .constdata                               0x08007af8   Section       32  systemdiagnostics.o(.constdata)
    .constdata                               0x08007b18   Section      144  main.o(.constdata)
    .constdata                               0x08007ba8   Section      200  rred.o(.constdata)
    pio2s                                    0x08007ba8   Data          48  rred.o(.constdata)
    twooverpi                                0x08007bd8   Data         152  rred.o(.constdata)
    .constdata                               0x08007c70   Section       32  rredf.o(.constdata)
    twooverpi                                0x08007c70   Data          32  rredf.o(.constdata)
    .constdata                               0x08007c90   Section       96  tan_i.o(.constdata)
    Todd                                     0x08007c90   Data          48  tan_i.o(.constdata)
    Teven                                    0x08007cc0   Data          48  tan_i.o(.constdata)
    .constdata                               0x08007cf0   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x08007cf0   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x08007d2c   Data          64  bigflt0.o(.constdata)
    .conststring                             0x08007d84   Section      143  statemachine.o(.conststring)
    .conststring                             0x08007e14   Section      175  bluetooth.o(.conststring)
    locale$$data                             0x08007ee4   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x08007ee8   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08007ef0   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08007efc   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x08007efe   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x08007eff   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x08007f00   Data           0  lc_numeric_c.o(locale$$data)
    .data                                    0x20000000   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000000   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000010   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x20000014   Section       16  statemachine.o(.data)
    record_state                             0x20000014   Data           1  statemachine.o(.data)
    unload_time                              0x20000018   Data           4  statemachine.o(.data)
    record_state                             0x2000001c   Data           1  statemachine.o(.data)
    unload_time                              0x20000020   Data           4  statemachine.o(.data)
    .data                                    0x20000024   Section       20  timer.o(.data)
    path_step_counter                        0x20000024   Data           4  timer.o(.data)
    path_step_ready                          0x20000028   Data           1  timer.o(.data)
    cpu_idle_count                           0x2000002c   Data           4  timer.o(.data)
    cpu_total_count                          0x20000030   Data           4  timer.o(.data)
    cpu_usage_percent                        0x20000034   Data           4  timer.o(.data)
    .data                                    0x20000038   Section        3  manualrecord.o(.data)
    point_saved_flags                        0x20000038   Data           3  manualrecord.o(.data)
    .data                                    0x2000003c   Section       22  main.o(.data)
    both_pressed_time                        0x2000003c   Data           4  main.o(.data)
    last_state                               0x20000040   Data           1  main.o(.data)
    last_key_test                            0x20000044   Data           4  main.o(.data)
    last_servo_test                          0x20000048   Data           4  main.o(.data)
    led_toggle_time                          0x2000004c   Data           4  main.o(.data)
    led_state                                0x20000050   Data           1  main.o(.data)
    last_state                               0x20000051   Data           1  main.o(.data)
    .bss                                     0x20000054   Section       30  key.o(.bss)
    key_states                               0x20000054   Data          30  key.o(.bss)
    .bss                                     0x20000074   Section       20  timer.o(.bss)
    timer_state                              0x20000074   Data          20  timer.o(.bss)
    .bss                                     0x20000088   Section       48  manualrecord.o(.bss)
    saved_points                             0x20000088   Data          24  manualrecord.o(.bss)
    saved_servos                             0x200000a0   Data          24  manualrecord.o(.bss)
    .bss                                     0x200000b8   Section      932  bluetooth.o(.bss)
    .bss                                     0x2000045c   Section       96  libspace.o(.bss)
    HEAP                                     0x200004c0   Section      512  startup_stm32f10x_md.o(HEAP)
    Heap_Mem                                 0x200004c0   Data         512  startup_stm32f10x_md.o(HEAP)
    STACK                                    0x200006c0   Section     1024  startup_stm32f10x_md.o(STACK)
    Stack_Mem                                0x200006c0   Data        1024  startup_stm32f10x_md.o(STACK)
    __initial_sp                             0x20000ac0   Data           0  startup_stm32f10x_md.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f10x_md.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_md.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f10x_md.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000103   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000129   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000145   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x08000161   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x08000161   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x08000167   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x0800016d   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_c                                0x08000173   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x08000179   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x0800017f   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000183   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000185   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_heap_1                     0x08000185   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000185   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x08000185   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000185   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000185   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x0800018b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x0800018b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x0800018b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x0800018b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000197   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000199   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000199   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000199   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000199   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000199   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000199   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000199   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x0800019b   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0800019b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0800019b   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001a1   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001a1   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001a5   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001a5   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001ad   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001af   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001af   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001b3   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080001b9   Thumb Code     8  startup_stm32f10x_md.o(.text)
    ADC1_2_IRQHandler                        0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_RX1_IRQHandler                      0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_SCE_IRQHandler                      0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI0_IRQHandler                         0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI15_10_IRQHandler                     0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI1_IRQHandler                         0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI2_IRQHandler                         0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI3_IRQHandler                         0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI4_IRQHandler                         0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI9_5_IRQHandler                       0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    FLASH_IRQHandler                         0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_ER_IRQHandler                       0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_EV_IRQHandler                       0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_ER_IRQHandler                       0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_EV_IRQHandler                       0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    PVD_IRQHandler                           0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RCC_IRQHandler                           0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTCAlarm_IRQHandler                      0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTC_IRQHandler                           0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI1_IRQHandler                          0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI2_IRQHandler                          0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TAMPER_IRQHandler                        0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_BRK_IRQHandler                      0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_CC_IRQHandler                       0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_UP_IRQHandler                       0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM3_IRQHandler                          0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM4_IRQHandler                          0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART1_IRQHandler                        0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART3_IRQHandler                        0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USBWakeUp_IRQHandler                     0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    WWDG_IRQHandler                          0x080001d3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __user_initial_stackheap                 0x080001d5   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __2sprintf                               0x080001f9   Thumb Code    34  noretval__2sprintf.o(.text)
    _printf_str                              0x08000221   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08000275   Thumb Code   104  _printf_dec.o(.text)
    __printf                                 0x080002ed   Thumb Code   352  __printf_ss_wp.o(.text)
    strstr                                   0x0800044d   Thumb Code    36  strstr.o(.text)
    strcpy                                   0x08000471   Thumb Code    72  strcpy.o(.text)
    __aeabi_memcpy4                          0x080004b9   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x080004b9   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x080004b9   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08000501   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr4                          0x0800051d   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x0800051d   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x0800051d   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x08000521   Thumb Code     0  rt_memclr_w.o(.text)
    strncpy                                  0x0800056b   Thumb Code    86  strncpy.o(.text)
    __use_two_region_memory                  0x080005c1   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080005c3   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080005c5   Thumb Code     2  heapauxi.o(.text)
    __read_errno                             0x080005c7   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x080005d1   Thumb Code    12  _rserrno.o(.text)
    _printf_int_common                       0x080005dd   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x0800068f   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000841   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08000ab7   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000add   Thumb Code    10  _sputc.o(.text)
    _printf_cs_common                        0x08000ae7   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08000afb   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08000b0b   Thumb Code     8  _printf_char.o(.text)
    __aeabi_memclr                           0x08000b13   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x08000b13   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x08000b17   Thumb Code     0  rt_memclr.o(.text)
    __rt_locale                              0x08000b59   Thumb Code     8  rt_locale_intlibspace.o(.text)
    __aeabi_errno_addr                       0x08000b61   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000b61   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000b61   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _ll_udiv10                               0x08000b69   Thumb Code   138  lludiv10.o(.text)
    _printf_fp_infnan                        0x08000bf5   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08000c75   Thumb Code   224  bigflt0.o(.text)
    __user_libspace                          0x08000d59   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000d59   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000d59   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08000d61   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000dab   Thumb Code    18  exit.o(.text)
    strcmp                                   0x08000dbd   Thumb Code   128  strcmpv7m.o(.text)
    _sys_exit                                0x08000e3d   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08000e49   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000e49   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08000e4b   Thumb Code     0  indicate_semi.o(.text)
    _btod_d2e                                0x08000e4b   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08000e89   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08000ecf   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08000f2f   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08001267   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08001343   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x0800136d   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08001397   Thumb Code   580  btod.o(CL$$btod_mult_common)
    AutoMovement_ArePointsValid              0x080015dd   Thumb Code    82  automovement.o(i.AutoMovement_ArePointsValid)
    AutoMovement_CheckSafety                 0x08001639   Thumb Code    26  automovement.o(i.AutoMovement_CheckSafety)
    AutoMovement_EmergencyStop               0x08001653   Thumb Code    38  automovement.o(i.AutoMovement_EmergencyStop)
    AutoMovement_ExecuteNextStep             0x08001679   Thumb Code    64  automovement.o(i.AutoMovement_ExecuteNextStep)
    AutoMovement_GetProgress                 0x080016b9   Thumb Code    54  automovement.o(i.AutoMovement_GetProgress)
    AutoMovement_GetStateString              0x080016f1   Thumb Code    54  automovement.o(i.AutoMovement_GetStateString)
    AutoMovement_HandleDirectionChange       0x08001771   Thumb Code   108  automovement.o(i.AutoMovement_HandleDirectionChange)
    AutoMovement_HandleError                 0x080017dd   Thumb Code   110  automovement.o(i.AutoMovement_HandleError)
    AutoMovement_HandleMovingToStart         0x0800184b   Thumb Code    80  automovement.o(i.AutoMovement_HandleMovingToStart)
    AutoMovement_HandlePathMoving            0x0800189b   Thumb Code    60  automovement.o(i.AutoMovement_HandlePathMoving)
    AutoMovement_HandlePreparing             0x080018d7   Thumb Code    72  automovement.o(i.AutoMovement_HandlePreparing)
    AutoMovement_Init                        0x08001921   Thumb Code    88  automovement.o(i.AutoMovement_Init)
    AutoMovement_IsActive                    0x0800197d   Thumb Code    14  automovement.o(i.AutoMovement_IsActive)
    AutoMovement_LoadPointsFromRecord        0x0800198b   Thumb Code    82  automovement.o(i.AutoMovement_LoadPointsFromRecord)
    AutoMovement_MoveToPosition              0x080019dd   Thumb Code    28  automovement.o(i.AutoMovement_MoveToPosition)
    AutoMovement_ShowStatus                  0x080019f9   Thumb Code   208  automovement.o(i.AutoMovement_ShowStatus)
    AutoMovement_Start                       0x08001b25   Thumb Code    88  automovement.o(i.AutoMovement_Start)
    AutoMovement_Stop                        0x08001b7d   Thumb Code    26  automovement.o(i.AutoMovement_Stop)
    AutoMovement_TransitionTo                0x08001b97   Thumb Code    98  automovement.o(i.AutoMovement_TransitionTo)
    AutoMovement_Update                      0x08001bf9   Thumb Code   172  automovement.o(i.AutoMovement_Update)
    Bluetooth_ConfigureModule                0x08001ca5   Thumb Code    36  bluetooth.o(i.Bluetooth_ConfigureModule)
    Bluetooth_Init                           0x08001ce1   Thumb Code    74  bluetooth.o(i.Bluetooth_Init)
    Bluetooth_ParseCommand                   0x08001d31   Thumb Code    64  bluetooth.o(i.Bluetooth_ParseCommand)
    Bluetooth_PrintConnectionInfo            0x08001df5   Thumb Code    58  bluetooth.o(i.Bluetooth_PrintConnectionInfo)
    Bluetooth_ProcessReceivedData            0x08001ec1   Thumb Code   120  bluetooth.o(i.Bluetooth_ProcessReceivedData)
    Bluetooth_SendByte                       0x08001f41   Thumb Code    42  bluetooth.o(i.Bluetooth_SendByte)
    Bluetooth_SendErrorReport                0x08001f71   Thumb Code    82  bluetooth.o(i.Bluetooth_SendErrorReport)
    Bluetooth_SendKeyAction                  0x08002025   Thumb Code   102  bluetooth.o(i.Bluetooth_SendKeyAction)
    Bluetooth_SendStartupInfo                0x08002119   Thumb Code    32  bluetooth.o(i.Bluetooth_SendStartupInfo)
    Bluetooth_SendString                     0x08002141   Thumb Code    22  bluetooth.o(i.Bluetooth_SendString)
    Bluetooth_SetName                        0x08002159   Thumb Code    26  bluetooth.o(i.Bluetooth_SetName)
    Bluetooth_SetPin                         0x08002181   Thumb Code    26  bluetooth.o(i.Bluetooth_SetPin)
    Bluetooth_TestConnection                 0x080021a9   Thumb Code    16  bluetooth.o(i.Bluetooth_TestConnection)
    Bluetooth_Update                         0x080021c1   Thumb Code    24  bluetooth.o(i.Bluetooth_Update)
    BusFault_Handler                         0x080021dd   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x080021e1   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    Delay_ms                                 0x080021e3   Thumb Code    24  delay.o(i.Delay_ms)
    Delay_us                                 0x080021fb   Thumb Code    46  delay.o(i.Delay_us)
    GPIO_Init                                0x08002229   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_ReadInputDataBit                    0x0800233f   Thumb Code    18  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x08002351   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08002355   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    GPIO_WriteBit                            0x08002359   Thumb Code    10  stm32f10x_gpio.o(i.GPIO_WriteBit)
    Geometry_CalculateDistance               0x08002363   Thumb Code    62  geometry.o(i.Geometry_CalculateDistance)
    Geometry_ClampServoAngle                 0x080023a1   Thumb Code    90  geometry.o(i.Geometry_ClampServoAngle)
    Geometry_ClampWallPoint                  0x08002405   Thumb Code    90  geometry.o(i.Geometry_ClampWallPoint)
    Geometry_IsServoAngleValid               0x08002469   Thumb Code    50  geometry.o(i.Geometry_IsServoAngleValid)
    Geometry_IsWallPointValid                0x080024a5   Thumb Code    50  geometry.o(i.Geometry_IsWallPointValid)
    Geometry_ServoToWall                     0x080024e1   Thumb Code   140  geometry.o(i.Geometry_ServoToWall)
    Geometry_WallToServo                     0x08002579   Thumb Code   126  geometry.o(i.Geometry_WallToServo)
    HardFault_Handler                        0x08002605   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    Key_ClearEvent                           0x08002609   Thumb Code    32  key.o(i.Key_ClearEvent)
    Key_Init                                 0x0800262d   Thumb Code   120  key.o(i.Key_Init)
    Key_IsClicked                            0x080026ad   Thumb Code    28  key.o(i.Key_IsClicked)
    Key_IsPressed                            0x080026cd   Thumb Code    36  key.o(i.Key_IsPressed)
    Key_ReadPin                              0x080026f5   Thumb Code    38  key.o(i.Key_ReadPin)
    Key_Scan                                 0x08002721   Thumb Code    16  key.o(i.Key_Scan)
    Key_UpdateState                          0x08002731   Thumb Code   100  key.o(i.Key_UpdateState)
    LED_Init                                 0x08002799   Thumb Code    46  led.o(i.LED_Init)
    LED_OFF                                  0x080027cd   Thumb Code    12  led.o(i.LED_OFF)
    LED_ON                                   0x080027dd   Thumb Code    12  led.o(i.LED_ON)
    ManualRecord_IsPointSaved                0x080027ed   Thumb Code    20  manualrecord.o(i.ManualRecord_IsPointSaved)
    ManualRecord_LoadPointData               0x08002805   Thumb Code    58  manualrecord.o(i.ManualRecord_LoadPointData)
    MemManage_Handler                        0x0800284d   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08002851   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x08002855   Thumb Code   100  misc.o(i.NVIC_Init)
    OLED_Clear                               0x080028c5   Thumb Code    42  oled.o(i.OLED_Clear)
    OLED_I2C_Init                            0x080028f1   Thumb Code    76  oled.o(i.OLED_I2C_Init)
    OLED_I2C_SendByte                        0x08002941   Thumb Code    88  oled.o(i.OLED_I2C_SendByte)
    OLED_I2C_Start                           0x0800299d   Thumb Code    48  oled.o(i.OLED_I2C_Start)
    OLED_I2C_Stop                            0x080029d1   Thumb Code    36  oled.o(i.OLED_I2C_Stop)
    OLED_Init                                0x080029f9   Thumb Code   174  oled.o(i.OLED_Init)
    OLED_SetCursor                           0x08002aa7   Thumb Code    34  oled.o(i.OLED_SetCursor)
    OLED_ShowChar                            0x08002ac9   Thumb Code   110  oled.o(i.OLED_ShowChar)
    OLED_ShowString                          0x08002b3d   Thumb Code    40  oled.o(i.OLED_ShowString)
    OLED_WriteCommand                        0x08002b65   Thumb Code    32  oled.o(i.OLED_WriteCommand)
    OLED_WriteData                           0x08002b85   Thumb Code    32  oled.o(i.OLED_WriteData)
    Path_GetNextStep                         0x08002ba5   Thumb Code   156  geometry.o(i.Path_GetNextStep)
    Path_Initialize                          0x08002c41   Thumb Code   106  geometry.o(i.Path_Initialize)
    Path_IsComplete                          0x08002cab   Thumb Code    24  geometry.o(i.Path_IsComplete)
    Path_LinearInterpolate                   0x08002cc3   Thumb Code   140  geometry.o(i.Path_LinearInterpolate)
    Path_SetDirection                        0x08002d4f   Thumb Code    54  geometry.o(i.Path_SetDirection)
    Path_ValidatePath                        0x08002d85   Thumb Code    70  geometry.o(i.Path_ValidatePath)
    PendSV_Handler                           0x08002dd5   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_APB1PeriphClockCmd                   0x08002dd9   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08002df9   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x08002e19   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x08002eed   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    Servo_AngleToPosition                    0x08002ef1   Thumb Code    54  servo.o(i.Servo_AngleToPosition)
    Servo_CalculateChecksum                  0x08002f31   Thumb Code    30  servo.o(i.Servo_CalculateChecksum)
    Servo_GetStatus                          0x08002f4f   Thumb Code   118  servo.o(i.Servo_GetStatus)
    Servo_Init                               0x08002fc5   Thumb Code    14  servo.o(i.Servo_Init)
    Servo_PositionToAngle                    0x08002fd5   Thumb Code    38  servo.o(i.Servo_PositionToAngle)
    Servo_ReadPosition                       0x08003005   Thumb Code   126  servo.o(i.Servo_ReadPosition)
    Servo_ReadTemperature                    0x08003089   Thumb Code    62  servo.o(i.Servo_ReadTemperature)
    Servo_ReadVoltage                        0x080030c7   Thumb Code    70  servo.o(i.Servo_ReadVoltage)
    Servo_ReceiveResponse                    0x0800310d   Thumb Code   272  servo.o(i.Servo_ReceiveResponse)
    Servo_SendCommand                        0x08003225   Thumb Code   128  servo.o(i.Servo_SendCommand)
    Servo_SetPosition                        0x080032a5   Thumb Code    20  servo.o(i.Servo_SetPosition)
    Servo_SetPositionWithTime                0x080032b9   Thumb Code    54  servo.o(i.Servo_SetPositionWithTime)
    Servo_SetTorqueEnable                    0x080032ef   Thumb Code    38  servo.o(i.Servo_SetTorqueEnable)
    StateMachine_GetStateString              0x080033fd   Thumb Code    30  statemachine.o(i.StateMachine_GetStateString)
    StateMachine_HandleRecordKey             0x0800343d   Thumb Code    52  statemachine.o(i.StateMachine_HandleRecordKey)
    StateMachine_HandleTriggerKey            0x08003471   Thumb Code    46  statemachine.o(i.StateMachine_HandleTriggerKey)
    StateMachine_Init                        0x080034a1   Thumb Code   126  statemachine.o(i.StateMachine_Init)
    StateMachine_IsPointRecorded             0x08003529   Thumb Code    24  statemachine.o(i.StateMachine_IsPointRecorded)
    StateMachine_MoveServoToPosition         0x08003541   Thumb Code    28  statemachine.o(i.StateMachine_MoveServoToPosition)
    StateMachine_RecordPointA                0x0800355d   Thumb Code   512  statemachine.o(i.StateMachine_RecordPointA)
    StateMachine_RecordPointB                0x08003875   Thumb Code   512  statemachine.o(i.StateMachine_RecordPointB)
    StateMachine_StartAutoMovement           0x08003b99   Thumb Code    98  statemachine.o(i.StateMachine_StartAutoMovement)
    StateMachine_StopAutoMovement            0x08003c15   Thumb Code    44  statemachine.o(i.StateMachine_StopAutoMovement)
    StateMachine_ToggleDirection             0x08003c59   Thumb Code    64  statemachine.o(i.StateMachine_ToggleDirection)
    StateMachine_TransitionTo                0x08003c99   Thumb Code    68  statemachine.o(i.StateMachine_TransitionTo)
    StateMachine_Update                      0x08003cdd   Thumb Code   116  statemachine.o(i.StateMachine_Update)
    StateMachine_UpdateAutoMovement          0x08003d51   Thumb Code    80  statemachine.o(i.StateMachine_UpdateAutoMovement)
    SysTick_Handler                          0x08003da1   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemDiag_CheckGeometry                 0x08003da5   Thumb Code   142  systemdiagnostics.o(i.SystemDiag_CheckGeometry)
    SystemDiag_CheckKeys                     0x08003e6d   Thumb Code    16  systemdiagnostics.o(i.SystemDiag_CheckKeys)
    SystemDiag_CheckMemory                   0x08003e7d   Thumb Code    16  systemdiagnostics.o(i.SystemDiag_CheckMemory)
    SystemDiag_CheckServos                   0x08003e8d   Thumb Code   246  systemdiagnostics.o(i.SystemDiag_CheckServos)
    SystemDiag_CheckTimer                    0x08004005   Thumb Code    70  systemdiagnostics.o(i.SystemDiag_CheckTimer)
    SystemDiag_GetLevelString                0x0800407d   Thumb Code    38  systemdiagnostics.o(i.SystemDiag_GetLevelString)
    SystemDiag_Init                          0x080040c9   Thumb Code    76  systemdiagnostics.o(i.SystemDiag_Init)
    SystemDiag_LoadDefaultConfig             0x0800411d   Thumb Code    38  systemdiagnostics.o(i.SystemDiag_LoadDefaultConfig)
    SystemDiag_LogError                      0x08004143   Thumb Code    82  systemdiagnostics.o(i.SystemDiag_LogError)
    SystemDiag_QuickCheck                    0x08004195   Thumb Code   108  systemdiagnostics.o(i.SystemDiag_QuickCheck)
    SystemDiag_RecordKeyPress                0x08004265   Thumb Code    14  systemdiagnostics.o(i.SystemDiag_RecordKeyPress)
    SystemDiag_RecordPathStep                0x08004273   Thumb Code    68  systemdiagnostics.o(i.SystemDiag_RecordPathStep)
    SystemDiag_RecordStateTransition         0x080042b7   Thumb Code    14  systemdiagnostics.o(i.SystemDiag_RecordStateTransition)
    SystemDiag_RunFullCheck                  0x080042c5   Thumb Code   324  systemdiagnostics.o(i.SystemDiag_RunFullCheck)
    SystemDiag_ShowErrors                    0x08004475   Thumb Code   158  systemdiagnostics.o(i.SystemDiag_ShowErrors)
    SystemDiag_ShowPerformance               0x08004555   Thumb Code    92  systemdiagnostics.o(i.SystemDiag_ShowPerformance)
    SystemDiag_ShowStatus                    0x080045e1   Thumb Code   118  systemdiagnostics.o(i.SystemDiag_ShowStatus)
    SystemInit                               0x08004695   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    TIM2_IRQHandler                          0x080046f5   Thumb Code     8  stm32f10x_it.o(i.TIM2_IRQHandler)
    TIM_ClearITPendingBit                    0x080046fd   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x08004703   Thumb Code    24  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_GetITStatus                          0x0800471b   Thumb Code    34  stm32f10x_tim.o(i.TIM_GetITStatus)
    TIM_ITConfig                             0x0800473d   Thumb Code    18  stm32f10x_tim.o(i.TIM_ITConfig)
    TIM_TimeBaseInit                         0x08004751   Thumb Code   122  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    Timer_ConfigureHardware                  0x080047f5   Thumb Code    94  timer.o(i.Timer_ConfigureHardware)
    Timer_GetFrequency                       0x08004853   Thumb Code     4  timer.o(i.Timer_GetFrequency)
    Timer_GetTick                            0x08004859   Thumb Code     6  timer.o(i.Timer_GetTick)
    Timer_GetTimeMs                          0x08004865   Thumb Code    12  timer.o(i.Timer_GetTimeMs)
    Timer_IRQ_Handler                        0x08004875   Thumb Code   150  timer.o(i.Timer_IRQ_Handler)
    Timer_Init                               0x08004925   Thumb Code    30  timer.o(i.Timer_Init)
    Timer_IsPathStepReady                    0x08004951   Thumb Code     6  timer.o(i.Timer_IsPathStepReady)
    Timer_IsRunning                          0x0800495d   Thumb Code     6  timer.o(i.Timer_IsRunning)
    Timer_IsTimeout                          0x08004969   Thumb Code    28  timer.o(i.Timer_IsTimeout)
    Timer_ResetPathStep                      0x08004985   Thumb Code    12  timer.o(i.Timer_ResetPathStep)
    Timer_Start                              0x08004999   Thumb Code    18  timer.o(i.Timer_Start)
    Timer_StartPathMovement                  0x080049b1   Thumb Code    12  timer.o(i.Timer_StartPathMovement)
    Timer_StopPathMovement                   0x080049c5   Thumb Code    12  timer.o(i.Timer_StopPathMovement)
    USART1_Init                              0x080049d9   Thumb Code   120  usart.o(i.USART1_Init)
    USART1_SendBuffer                        0x08004a59   Thumb Code    80  usart.o(i.USART1_SendBuffer)
    USART2_IRQHandler                        0x08004aad   Thumb Code    84  bluetooth.o(i.USART2_IRQHandler)
    USART_ClearITPendingBit                  0x08004bb1   Thumb Code    30  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    USART_Cmd                                0x08004bcf   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetFlagStatus                      0x08004be7   Thumb Code    26  stm32f10x_usart.o(i.USART_GetFlagStatus)
    USART_GetITStatus                        0x08004c01   Thumb Code    84  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x08004c55   Thumb Code    74  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x08004ca1   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08004d79   Thumb Code    10  stm32f10x_usart.o(i.USART_ReceiveData)
    USART_SendData                           0x08004d83   Thumb Code     8  stm32f10x_usart.o(i.USART_SendData)
    UsageFault_Handler                       0x08004d8b   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x08004d8f   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    __ARM_fpclassifyf                        0x08004db7   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __ieee754_rem_pio2                       0x08004e01   Thumb Code   828  rred.o(i.__ieee754_rem_pio2)
    __kernel_poly                            0x08005189   Thumb Code   170  poly.o(i.__kernel_poly)
    __kernel_tan                             0x08005235   Thumb Code   586  tan_i.o(i.__kernel_tan)
    __mathlib_dbl_infnan                     0x080054b1   Thumb Code     6  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_invalid                    0x080054b7   Thumb Code    12  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_underflow                  0x080054c5   Thumb Code    10  dunder.o(i.__mathlib_dbl_underflow)
    __mathlib_flt_infnan                     0x080054d5   Thumb Code     6  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_invalid                    0x080054db   Thumb Code     8  funder.o(i.__mathlib_flt_invalid)
    __mathlib_flt_underflow                  0x080054e3   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    __mathlib_rredf2                         0x080054ed   Thumb Code   344  rredf.o(i.__mathlib_rredf2)
    _is_digit                                0x08005655   Thumb Code    14  __printf_wp.o(i._is_digit)
    atanf                                    0x08005665   Thumb Code   324  atanf.o(i.atanf)
    main                                     0x080057e1   Thumb Code  1868  main.o(i.main)
    sqrtf                                    0x08006011   Thumb Code    44  sqrtf.o(i.sqrtf)
    tan                                      0x0800603d   Thumb Code    90  tan.o(i.tan)
    tanf                                     0x080060a1   Thumb Code   314  tanf.o(i.tanf)
    _get_lc_numeric                          0x08006215   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __aeabi_d2f                              0x08006241   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08006241   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x080062a5   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x080062a5   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcheck_NaN1                        0x080063f5   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __aeabi_ddiv                             0x08006405   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08006405   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_d2iz                             0x080066b5   Thumb Code     0  dfix.o(x$fpl$dfix)
    _dfix                                    0x080066b5   Thumb Code    94  dfix.o(x$fpl$dfix)
    __aeabi_i2d                              0x08006713   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x08006713   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_ui2d                             0x08006741   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x08006741   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_dmul                             0x08006769   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08006769   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x080068bd   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08006959   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_drsub                            0x08006965   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x08006965   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    __aeabi_dsub                             0x0800697d   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x0800697d   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x08006b51   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08006b51   Thumb Code    86  f2d.o(x$fpl$f2d)
    __aeabi_fadd                             0x08006ba9   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x08006ba9   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __fpl_fcheck_NaN1                        0x08006c6d   Thumb Code     6  fcheck1.o(x$fpl$fcheck1)
    __fpl_fcmp_Inf                           0x08006c79   Thumb Code    24  fcmpi.o(x$fpl$fcmpinf)
    __aeabi_fdiv                             0x08006c91   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    _fdiv                                    0x08006c91   Thumb Code   384  fdiv.o(x$fpl$fdiv)
    __aeabi_f2iz                             0x08006e15   Thumb Code     0  ffix.o(x$fpl$ffix)
    _ffix                                    0x08006e15   Thumb Code    54  ffix.o(x$fpl$ffix)
    __aeabi_f2uiz                            0x08006e4d   Thumb Code     0  ffixu.o(x$fpl$ffixu)
    _ffixu                                   0x08006e4d   Thumb Code    62  ffixu.o(x$fpl$ffixu)
    __aeabi_i2f                              0x08006e8d   Thumb Code     0  fflt_clz.o(x$fpl$fflt)
    _fflt                                    0x08006e8d   Thumb Code    48  fflt_clz.o(x$fpl$fflt)
    __aeabi_ui2f                             0x08006ebd   Thumb Code     0  fflt_clz.o(x$fpl$ffltu)
    _ffltu                                   0x08006ebd   Thumb Code    38  fflt_clz.o(x$fpl$ffltu)
    __aeabi_cfcmple                          0x08006ee5   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    _fcmple                                  0x08006ee5   Thumb Code   104  fleqf.o(x$fpl$fleqf)
    __fpl_fcmple_InfNaN                      0x08006f37   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    __aeabi_fmul                             0x08006f4d   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x08006f4d   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x0800704f   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x080070db   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_cfrcmple                         0x080070e5   Thumb Code     0  frleqf.o(x$fpl$frleqf)
    _frcmple                                 0x080070e5   Thumb Code    98  frleqf.o(x$fpl$frleqf)
    _frnd                                    0x08007149   Thumb Code    96  frnd.o(x$fpl$frnd)
    __aeabi_frsub                            0x080071a9   Thumb Code     0  faddsub_clz.o(x$fpl$frsb)
    _frsb                                    0x080071a9   Thumb Code    20  faddsub_clz.o(x$fpl$frsb)
    _fsqrt                                   0x080071bd   Thumb Code   272  fsqrt.o(x$fpl$fsqrt)
    __aeabi_fsub                             0x080072cd   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x080072cd   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    _printf_fp_dec                           0x080073b7   Thumb Code     4  printf1.o(x$fpl$printf1)
    __fpl_return_NaN                         0x080073bb   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x0800741f   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    __ARM_scalbnf                            0x0800747b   Thumb Code    76  scalbnf.o(x$fpl$scalbnf)
    __fpl_cmpreturn                          0x080074c7   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    OLED_F8x16                               0x080074f6   Data        1520  oled.o(.constdata)
    __I$use$fp                               0x080074f6   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x08007ec4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08007ee4   Number         0  anon$$obj.o(Region$$Table)
    bluetooth_ctrl                           0x200000b8   Data         932  bluetooth.o(.bss)
    __libspace_start                         0x2000045c   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200004bc   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00007f54, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00007f00, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO            3    RESET               startup_stm32f10x_md.o
    0x080000ec   0x080000ec   0x00000008   Code   RO         3084  * !!!main             c_w.l(__main.o)
    0x080000f4   0x080000f4   0x00000034   Code   RO         3477    !!!scatter          c_w.l(__scatter.o)
    0x08000128   0x08000128   0x0000001a   Code   RO         3479    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000142   0x08000142   0x00000002   PAD
    0x08000144   0x08000144   0x0000001c   Code   RO         3481    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000160   0x08000160   0x00000000   Code   RO         3071    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x08000160   0x08000160   0x00000006   Code   RO         3070    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000166   0x08000166   0x00000006   Code   RO         3068    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x0800016c   0x0800016c   0x00000006   Code   RO         3069    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x08000172   0x08000172   0x00000006   Code   RO         3066    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x08000178   0x08000178   0x00000006   Code   RO         3067    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x0800017e   0x0800017e   0x00000004   Code   RO         3173    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000182   0x08000182   0x00000002   Code   RO         3342    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000184   0x08000184   0x00000000   Code   RO         3344    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x08000184   0x08000184   0x00000000   Code   RO         3346    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000184   0x08000184   0x00000000   Code   RO         3349    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000184   0x08000184   0x00000000   Code   RO         3351    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000184   0x08000184   0x00000000   Code   RO         3353    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000184   0x08000184   0x00000006   Code   RO         3354    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x0800018a   0x0800018a   0x00000000   Code   RO         3356    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x0800018a   0x0800018a   0x00000000   Code   RO         3358    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x0800018a   0x0800018a   0x00000000   Code   RO         3360    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x0800018a   0x0800018a   0x0000000a   Code   RO         3361    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         3362    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         3364    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         3366    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         3368    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         3370    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         3372    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         3374    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         3376    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         3380    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         3382    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         3384    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         3386    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000002   Code   RO         3387    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000196   0x08000196   0x00000002   Code   RO         3425    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000198   0x08000198   0x00000000   Code   RO         3436    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000198   0x08000198   0x00000000   Code   RO         3438    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000198   0x08000198   0x00000000   Code   RO         3441    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000198   0x08000198   0x00000000   Code   RO         3444    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000198   0x08000198   0x00000000   Code   RO         3446    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000198   0x08000198   0x00000000   Code   RO         3449    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000198   0x08000198   0x00000002   Code   RO         3450    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x0800019a   0x0800019a   0x00000000   Code   RO         3160    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x0800019a   0x0800019a   0x00000000   Code   RO         3240    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x0800019a   0x0800019a   0x00000006   Code   RO         3252    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001a0   0x080001a0   0x00000000   Code   RO         3242    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001a0   0x080001a0   0x00000004   Code   RO         3243    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001a4   0x080001a4   0x00000000   Code   RO         3245    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001a4   0x080001a4   0x00000008   Code   RO         3246    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001ac   0x080001ac   0x00000002   Code   RO         3392    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001ae   0x080001ae   0x00000000   Code   RO         3401    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001ae   0x080001ae   0x00000004   Code   RO         3402    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001b2   0x080001b2   0x00000006   Code   RO         3403    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001b8   0x080001b8   0x00000040   Code   RO            4    .text               startup_stm32f10x_md.o
    0x080001f8   0x080001f8   0x00000028   Code   RO         3038    .text               c_w.l(noretval__2sprintf.o)
    0x08000220   0x08000220   0x00000052   Code   RO         3042    .text               c_w.l(_printf_str.o)
    0x08000272   0x08000272   0x00000002   PAD
    0x08000274   0x08000274   0x00000078   Code   RO         3044    .text               c_w.l(_printf_dec.o)
    0x080002ec   0x080002ec   0x00000160   Code   RO         3061    .text               c_w.l(__printf_ss_wp.o)
    0x0800044c   0x0800044c   0x00000024   Code   RO         3072    .text               c_w.l(strstr.o)
    0x08000470   0x08000470   0x00000048   Code   RO         3074    .text               c_w.l(strcpy.o)
    0x080004b8   0x080004b8   0x00000064   Code   RO         3076    .text               c_w.l(rt_memcpy_w.o)
    0x0800051c   0x0800051c   0x0000004e   Code   RO         3078    .text               c_w.l(rt_memclr_w.o)
    0x0800056a   0x0800056a   0x00000056   Code   RO         3080    .text               c_w.l(strncpy.o)
    0x080005c0   0x080005c0   0x00000006   Code   RO         3082    .text               c_w.l(heapauxi.o)
    0x080005c6   0x080005c6   0x00000016   Code   RO         3161    .text               c_w.l(_rserrno.o)
    0x080005dc   0x080005dc   0x000000b2   Code   RO         3163    .text               c_w.l(_printf_intcommon.o)
    0x0800068e   0x0800068e   0x0000041e   Code   RO         3165    .text               c_w.l(_printf_fp_dec.o)
    0x08000aac   0x08000aac   0x00000030   Code   RO         3167    .text               c_w.l(_printf_char_common.o)
    0x08000adc   0x08000adc   0x0000000a   Code   RO         3169    .text               c_w.l(_sputc.o)
    0x08000ae6   0x08000ae6   0x0000002c   Code   RO         3171    .text               c_w.l(_printf_char.o)
    0x08000b12   0x08000b12   0x00000044   Code   RO         3174    .text               c_w.l(rt_memclr.o)
    0x08000b56   0x08000b56   0x00000002   PAD
    0x08000b58   0x08000b58   0x00000008   Code   RO         3257    .text               c_w.l(rt_locale_intlibspace.o)
    0x08000b60   0x08000b60   0x00000008   Code   RO         3262    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08000b68   0x08000b68   0x0000008a   Code   RO         3264    .text               c_w.l(lludiv10.o)
    0x08000bf2   0x08000bf2   0x00000002   PAD
    0x08000bf4   0x08000bf4   0x00000080   Code   RO         3266    .text               c_w.l(_printf_fp_infnan.o)
    0x08000c74   0x08000c74   0x000000e4   Code   RO         3270    .text               c_w.l(bigflt0.o)
    0x08000d58   0x08000d58   0x00000008   Code   RO         3328    .text               c_w.l(libspace.o)
    0x08000d60   0x08000d60   0x0000004a   Code   RO         3331    .text               c_w.l(sys_stackheap_outer.o)
    0x08000daa   0x08000daa   0x00000012   Code   RO         3333    .text               c_w.l(exit.o)
    0x08000dbc   0x08000dbc   0x00000080   Code   RO         3335    .text               c_w.l(strcmpv7m.o)
    0x08000e3c   0x08000e3c   0x0000000c   Code   RO         3415    .text               c_w.l(sys_exit.o)
    0x08000e48   0x08000e48   0x00000002   Code   RO         3426    .text               c_w.l(use_no_semi.o)
    0x08000e4a   0x08000e4a   0x00000000   Code   RO         3428    .text               c_w.l(indicate_semi.o)
    0x08000e4a   0x08000e4a   0x0000003e   Code   RO         3273    CL$$btod_d2e        c_w.l(btod.o)
    0x08000e88   0x08000e88   0x00000046   Code   RO         3275    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08000ece   0x08000ece   0x00000060   Code   RO         3274    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08000f2e   0x08000f2e   0x00000338   Code   RO         3283    CL$$btod_div_common  c_w.l(btod.o)
    0x08001266   0x08001266   0x000000dc   Code   RO         3280    CL$$btod_e2e        c_w.l(btod.o)
    0x08001342   0x08001342   0x0000002a   Code   RO         3277    CL$$btod_ediv       c_w.l(btod.o)
    0x0800136c   0x0800136c   0x0000002a   Code   RO         3276    CL$$btod_emul       c_w.l(btod.o)
    0x08001396   0x08001396   0x00000244   Code   RO         3282    CL$$btod_mult_common  c_w.l(btod.o)
    0x080015da   0x080015da   0x00000002   PAD
    0x080015dc   0x080015dc   0x0000005c   Code   RO         2399    i.AutoMovement_ArePointsValid  automovement.o
    0x08001638   0x08001638   0x0000001a   Code   RO         2400    i.AutoMovement_CheckSafety  automovement.o
    0x08001652   0x08001652   0x00000026   Code   RO         2401    i.AutoMovement_EmergencyStop  automovement.o
    0x08001678   0x08001678   0x00000040   Code   RO         2402    i.AutoMovement_ExecuteNextStep  automovement.o
    0x080016b8   0x080016b8   0x00000036   Code   RO         2403    i.AutoMovement_GetProgress  automovement.o
    0x080016ee   0x080016ee   0x00000002   PAD
    0x080016f0   0x080016f0   0x00000080   Code   RO         2404    i.AutoMovement_GetStateString  automovement.o
    0x08001770   0x08001770   0x0000006c   Code   RO         2405    i.AutoMovement_HandleDirectionChange  automovement.o
    0x080017dc   0x080017dc   0x0000006e   Code   RO         2406    i.AutoMovement_HandleError  automovement.o
    0x0800184a   0x0800184a   0x00000050   Code   RO         2407    i.AutoMovement_HandleMovingToStart  automovement.o
    0x0800189a   0x0800189a   0x0000003c   Code   RO         2408    i.AutoMovement_HandlePathMoving  automovement.o
    0x080018d6   0x080018d6   0x00000048   Code   RO         2409    i.AutoMovement_HandlePreparing  automovement.o
    0x0800191e   0x0800191e   0x00000002   PAD
    0x08001920   0x08001920   0x0000005c   Code   RO         2410    i.AutoMovement_Init  automovement.o
    0x0800197c   0x0800197c   0x0000000e   Code   RO         2411    i.AutoMovement_IsActive  automovement.o
    0x0800198a   0x0800198a   0x00000052   Code   RO         2412    i.AutoMovement_LoadPointsFromRecord  automovement.o
    0x080019dc   0x080019dc   0x0000001c   Code   RO         2413    i.AutoMovement_MoveToPosition  automovement.o
    0x080019f8   0x080019f8   0x0000012c   Code   RO         2414    i.AutoMovement_ShowStatus  automovement.o
    0x08001b24   0x08001b24   0x00000058   Code   RO         2415    i.AutoMovement_Start  automovement.o
    0x08001b7c   0x08001b7c   0x0000001a   Code   RO         2416    i.AutoMovement_Stop  automovement.o
    0x08001b96   0x08001b96   0x00000062   Code   RO         2417    i.AutoMovement_TransitionTo  automovement.o
    0x08001bf8   0x08001bf8   0x000000ac   Code   RO         2418    i.AutoMovement_Update  automovement.o
    0x08001ca4   0x08001ca4   0x0000003c   Code   RO         2781    i.Bluetooth_ConfigureModule  bluetooth.o
    0x08001ce0   0x08001ce0   0x00000050   Code   RO         2783    i.Bluetooth_Init    bluetooth.o
    0x08001d30   0x08001d30   0x000000c4   Code   RO         2784    i.Bluetooth_ParseCommand  bluetooth.o
    0x08001df4   0x08001df4   0x000000cc   Code   RO         2785    i.Bluetooth_PrintConnectionInfo  bluetooth.o
    0x08001ec0   0x08001ec0   0x00000080   Code   RO         2786    i.Bluetooth_ProcessReceivedData  bluetooth.o
    0x08001f40   0x08001f40   0x00000030   Code   RO         2787    i.Bluetooth_SendByte  bluetooth.o
    0x08001f70   0x08001f70   0x000000b4   Code   RO         2789    i.Bluetooth_SendErrorReport  bluetooth.o
    0x08002024   0x08002024   0x000000f4   Code   RO         2790    i.Bluetooth_SendKeyAction  bluetooth.o
    0x08002118   0x08002118   0x00000028   Code   RO         2793    i.Bluetooth_SendStartupInfo  bluetooth.o
    0x08002140   0x08002140   0x00000016   Code   RO         2794    i.Bluetooth_SendString  bluetooth.o
    0x08002156   0x08002156   0x00000002   PAD
    0x08002158   0x08002158   0x00000028   Code   RO         2795    i.Bluetooth_SetName  bluetooth.o
    0x08002180   0x08002180   0x00000028   Code   RO         2796    i.Bluetooth_SetPin  bluetooth.o
    0x080021a8   0x080021a8   0x00000018   Code   RO         2797    i.Bluetooth_TestConnection  bluetooth.o
    0x080021c0   0x080021c0   0x0000001c   Code   RO         2798    i.Bluetooth_Update  bluetooth.o
    0x080021dc   0x080021dc   0x00000004   Code   RO         2961    i.BusFault_Handler  stm32f10x_it.o
    0x080021e0   0x080021e0   0x00000002   Code   RO         2962    i.DebugMon_Handler  stm32f10x_it.o
    0x080021e2   0x080021e2   0x00000018   Code   RO         1463    i.Delay_ms          delay.o
    0x080021fa   0x080021fa   0x0000002e   Code   RO         1465    i.Delay_us          delay.o
    0x08002228   0x08002228   0x00000116   Code   RO          208    i.GPIO_Init         stm32f10x_gpio.o
    0x0800233e   0x0800233e   0x00000012   Code   RO          212    i.GPIO_ReadInputDataBit  stm32f10x_gpio.o
    0x08002350   0x08002350   0x00000004   Code   RO          215    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x08002354   0x08002354   0x00000004   Code   RO          216    i.GPIO_SetBits      stm32f10x_gpio.o
    0x08002358   0x08002358   0x0000000a   Code   RO          219    i.GPIO_WriteBit     stm32f10x_gpio.o
    0x08002362   0x08002362   0x0000003e   Code   RO         1793    i.Geometry_CalculateDistance  geometry.o
    0x080023a0   0x080023a0   0x00000064   Code   RO         1794    i.Geometry_ClampServoAngle  geometry.o
    0x08002404   0x08002404   0x00000064   Code   RO         1795    i.Geometry_ClampWallPoint  geometry.o
    0x08002468   0x08002468   0x0000003c   Code   RO         1796    i.Geometry_IsServoAngleValid  geometry.o
    0x080024a4   0x080024a4   0x0000003c   Code   RO         1797    i.Geometry_IsWallPointValid  geometry.o
    0x080024e0   0x080024e0   0x00000098   Code   RO         1799    i.Geometry_ServoToWall  geometry.o
    0x08002578   0x08002578   0x0000008c   Code   RO         1801    i.Geometry_WallToServo  geometry.o
    0x08002604   0x08002604   0x00000004   Code   RO         2963    i.HardFault_Handler  stm32f10x_it.o
    0x08002608   0x08002608   0x00000024   Code   RO         1517    i.Key_ClearEvent    key.o
    0x0800262c   0x0800262c   0x00000080   Code   RO         1520    i.Key_Init          key.o
    0x080026ac   0x080026ac   0x00000020   Code   RO         1521    i.Key_IsClicked     key.o
    0x080026cc   0x080026cc   0x00000028   Code   RO         1522    i.Key_IsPressed     key.o
    0x080026f4   0x080026f4   0x0000002c   Code   RO         1524    i.Key_ReadPin       key.o
    0x08002720   0x08002720   0x00000010   Code   RO         1525    i.Key_Scan          key.o
    0x08002730   0x08002730   0x00000068   Code   RO         1526    i.Key_UpdateState   key.o
    0x08002798   0x08002798   0x00000034   Code   RO         1487    i.LED_Init          led.o
    0x080027cc   0x080027cc   0x00000010   Code   RO         1488    i.LED_OFF           led.o
    0x080027dc   0x080027dc   0x00000010   Code   RO         1489    i.LED_ON            led.o
    0x080027ec   0x080027ec   0x00000018   Code   RO         2271    i.ManualRecord_IsPointSaved  manualrecord.o
    0x08002804   0x08002804   0x00000048   Code   RO         2273    i.ManualRecord_LoadPointData  manualrecord.o
    0x0800284c   0x0800284c   0x00000004   Code   RO         2964    i.MemManage_Handler  stm32f10x_it.o
    0x08002850   0x08002850   0x00000002   Code   RO         2965    i.NMI_Handler       stm32f10x_it.o
    0x08002852   0x08002852   0x00000002   PAD
    0x08002854   0x08002854   0x00000070   Code   RO           89    i.NVIC_Init         misc.o
    0x080028c4   0x080028c4   0x0000002a   Code   RO         1591    i.OLED_Clear        oled.o
    0x080028ee   0x080028ee   0x00000002   PAD
    0x080028f0   0x080028f0   0x00000050   Code   RO         1592    i.OLED_I2C_Init     oled.o
    0x08002940   0x08002940   0x0000005c   Code   RO         1593    i.OLED_I2C_SendByte  oled.o
    0x0800299c   0x0800299c   0x00000034   Code   RO         1594    i.OLED_I2C_Start    oled.o
    0x080029d0   0x080029d0   0x00000028   Code   RO         1595    i.OLED_I2C_Stop     oled.o
    0x080029f8   0x080029f8   0x000000ae   Code   RO         1596    i.OLED_Init         oled.o
    0x08002aa6   0x08002aa6   0x00000022   Code   RO         1598    i.OLED_SetCursor    oled.o
    0x08002ac8   0x08002ac8   0x00000074   Code   RO         1600    i.OLED_ShowChar     oled.o
    0x08002b3c   0x08002b3c   0x00000028   Code   RO         1604    i.OLED_ShowString   oled.o
    0x08002b64   0x08002b64   0x00000020   Code   RO         1605    i.OLED_WriteCommand  oled.o
    0x08002b84   0x08002b84   0x00000020   Code   RO         1606    i.OLED_WriteData    oled.o
    0x08002ba4   0x08002ba4   0x0000009c   Code   RO         1804    i.Path_GetNextStep  geometry.o
    0x08002c40   0x08002c40   0x0000006a   Code   RO         1805    i.Path_Initialize   geometry.o
    0x08002caa   0x08002caa   0x00000018   Code   RO         1806    i.Path_IsComplete   geometry.o
    0x08002cc2   0x08002cc2   0x0000008c   Code   RO         1807    i.Path_LinearInterpolate  geometry.o
    0x08002d4e   0x08002d4e   0x00000036   Code   RO         1809    i.Path_SetDirection  geometry.o
    0x08002d84   0x08002d84   0x00000050   Code   RO         1811    i.Path_ValidatePath  geometry.o
    0x08002dd4   0x08002dd4   0x00000002   Code   RO         2966    i.PendSV_Handler    stm32f10x_it.o
    0x08002dd6   0x08002dd6   0x00000002   PAD
    0x08002dd8   0x08002dd8   0x00000020   Code   RO          528    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08002df8   0x08002df8   0x00000020   Code   RO          530    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08002e18   0x08002e18   0x000000d4   Code   RO          538    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x08002eec   0x08002eec   0x00000002   Code   RO         2967    i.SVC_Handler       stm32f10x_it.o
    0x08002eee   0x08002eee   0x00000002   PAD
    0x08002ef0   0x08002ef0   0x00000040   Code   RO         1699    i.Servo_AngleToPosition  servo.o
    0x08002f30   0x08002f30   0x0000001e   Code   RO         1700    i.Servo_CalculateChecksum  servo.o
    0x08002f4e   0x08002f4e   0x00000076   Code   RO         1701    i.Servo_GetStatus   servo.o
    0x08002fc4   0x08002fc4   0x0000000e   Code   RO         1702    i.Servo_Init        servo.o
    0x08002fd2   0x08002fd2   0x00000002   PAD
    0x08002fd4   0x08002fd4   0x00000030   Code   RO         1703    i.Servo_PositionToAngle  servo.o
    0x08003004   0x08003004   0x00000084   Code   RO         1704    i.Servo_ReadPosition  servo.o
    0x08003088   0x08003088   0x0000003e   Code   RO         1705    i.Servo_ReadTemperature  servo.o
    0x080030c6   0x080030c6   0x00000046   Code   RO         1706    i.Servo_ReadVoltage  servo.o
    0x0800310c   0x0800310c   0x00000118   Code   RO         1707    i.Servo_ReceiveResponse  servo.o
    0x08003224   0x08003224   0x00000080   Code   RO         1708    i.Servo_SendCommand  servo.o
    0x080032a4   0x080032a4   0x00000014   Code   RO         1709    i.Servo_SetPosition  servo.o
    0x080032b8   0x080032b8   0x00000036   Code   RO         1710    i.Servo_SetPositionWithTime  servo.o
    0x080032ee   0x080032ee   0x00000026   Code   RO         1711    i.Servo_SetTorqueEnable  servo.o
    0x08003314   0x08003314   0x00000008   Code   RO           24    i.SetSysClock       system_stm32f10x.o
    0x0800331c   0x0800331c   0x000000e0   Code   RO           25    i.SetSysClockTo72   system_stm32f10x.o
    0x080033fc   0x080033fc   0x00000040   Code   RO         1949    i.StateMachine_GetStateString  statemachine.o
    0x0800343c   0x0800343c   0x00000034   Code   RO         1951    i.StateMachine_HandleRecordKey  statemachine.o
    0x08003470   0x08003470   0x0000002e   Code   RO         1952    i.StateMachine_HandleTriggerKey  statemachine.o
    0x0800349e   0x0800349e   0x00000002   PAD
    0x080034a0   0x080034a0   0x00000088   Code   RO         1953    i.StateMachine_Init  statemachine.o
    0x08003528   0x08003528   0x00000018   Code   RO         1955    i.StateMachine_IsPointRecorded  statemachine.o
    0x08003540   0x08003540   0x0000001c   Code   RO         1958    i.StateMachine_MoveServoToPosition  statemachine.o
    0x0800355c   0x0800355c   0x00000318   Code   RO         1960    i.StateMachine_RecordPointA  statemachine.o
    0x08003874   0x08003874   0x00000324   Code   RO         1961    i.StateMachine_RecordPointB  statemachine.o
    0x08003b98   0x08003b98   0x0000007c   Code   RO         1963    i.StateMachine_StartAutoMovement  statemachine.o
    0x08003c14   0x08003c14   0x00000044   Code   RO         1964    i.StateMachine_StopAutoMovement  statemachine.o
    0x08003c58   0x08003c58   0x00000040   Code   RO         1965    i.StateMachine_ToggleDirection  statemachine.o
    0x08003c98   0x08003c98   0x00000044   Code   RO         1966    i.StateMachine_TransitionTo  statemachine.o
    0x08003cdc   0x08003cdc   0x00000074   Code   RO         1967    i.StateMachine_Update  statemachine.o
    0x08003d50   0x08003d50   0x00000050   Code   RO         1968    i.StateMachine_UpdateAutoMovement  statemachine.o
    0x08003da0   0x08003da0   0x00000002   Code   RO         2968    i.SysTick_Handler   stm32f10x_it.o
    0x08003da2   0x08003da2   0x00000002   PAD
    0x08003da4   0x08003da4   0x000000c8   Code   RO         2530    i.SystemDiag_CheckGeometry  systemdiagnostics.o
    0x08003e6c   0x08003e6c   0x00000010   Code   RO         2531    i.SystemDiag_CheckKeys  systemdiagnostics.o
    0x08003e7c   0x08003e7c   0x00000010   Code   RO         2532    i.SystemDiag_CheckMemory  systemdiagnostics.o
    0x08003e8c   0x08003e8c   0x00000178   Code   RO         2533    i.SystemDiag_CheckServos  systemdiagnostics.o
    0x08004004   0x08004004   0x00000078   Code   RO         2534    i.SystemDiag_CheckTimer  systemdiagnostics.o
    0x0800407c   0x0800407c   0x0000004c   Code   RO         2539    i.SystemDiag_GetLevelString  systemdiagnostics.o
    0x080040c8   0x080040c8   0x00000054   Code   RO         2541    i.SystemDiag_Init   systemdiagnostics.o
    0x0800411c   0x0800411c   0x00000026   Code   RO         2542    i.SystemDiag_LoadDefaultConfig  systemdiagnostics.o
    0x08004142   0x08004142   0x00000052   Code   RO         2543    i.SystemDiag_LogError  systemdiagnostics.o
    0x08004194   0x08004194   0x000000d0   Code   RO         2546    i.SystemDiag_QuickCheck  systemdiagnostics.o
    0x08004264   0x08004264   0x0000000e   Code   RO         2548    i.SystemDiag_RecordKeyPress  systemdiagnostics.o
    0x08004272   0x08004272   0x00000044   Code   RO         2549    i.SystemDiag_RecordPathStep  systemdiagnostics.o
    0x080042b6   0x080042b6   0x0000000e   Code   RO         2552    i.SystemDiag_RecordStateTransition  systemdiagnostics.o
    0x080042c4   0x080042c4   0x000001b0   Code   RO         2555    i.SystemDiag_RunFullCheck  systemdiagnostics.o
    0x08004474   0x08004474   0x000000e0   Code   RO         2557    i.SystemDiag_ShowErrors  systemdiagnostics.o
    0x08004554   0x08004554   0x0000008c   Code   RO         2558    i.SystemDiag_ShowPerformance  systemdiagnostics.o
    0x080045e0   0x080045e0   0x000000b4   Code   RO         2559    i.SystemDiag_ShowStatus  systemdiagnostics.o
    0x08004694   0x08004694   0x00000060   Code   RO           27    i.SystemInit        system_stm32f10x.o
    0x080046f4   0x080046f4   0x00000008   Code   RO         2969    i.TIM2_IRQHandler   stm32f10x_it.o
    0x080046fc   0x080046fc   0x00000006   Code   RO          740    i.TIM_ClearITPendingBit  stm32f10x_tim.o
    0x08004702   0x08004702   0x00000018   Code   RO          745    i.TIM_Cmd           stm32f10x_tim.o
    0x0800471a   0x0800471a   0x00000022   Code   RO          766    i.TIM_GetITStatus   stm32f10x_tim.o
    0x0800473c   0x0800473c   0x00000012   Code   RO          770    i.TIM_ITConfig      stm32f10x_tim.o
    0x0800474e   0x0800474e   0x00000002   PAD
    0x08004750   0x08004750   0x000000a4   Code   RO          816    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x080047f4   0x080047f4   0x0000005e   Code   RO         2119    i.Timer_ConfigureHardware  timer.o
    0x08004852   0x08004852   0x00000004   Code   RO         2124    i.Timer_GetFrequency  timer.o
    0x08004856   0x08004856   0x00000002   PAD
    0x08004858   0x08004858   0x0000000c   Code   RO         2126    i.Timer_GetTick     timer.o
    0x08004864   0x08004864   0x00000010   Code   RO         2127    i.Timer_GetTimeMs   timer.o
    0x08004874   0x08004874   0x000000b0   Code   RO         2128    i.Timer_IRQ_Handler  timer.o
    0x08004924   0x08004924   0x0000002c   Code   RO         2129    i.Timer_Init        timer.o
    0x08004950   0x08004950   0x0000000c   Code   RO         2131    i.Timer_IsPathStepReady  timer.o
    0x0800495c   0x0800495c   0x0000000c   Code   RO         2132    i.Timer_IsRunning   timer.o
    0x08004968   0x08004968   0x0000001c   Code   RO         2133    i.Timer_IsTimeout   timer.o
    0x08004984   0x08004984   0x00000014   Code   RO         2135    i.Timer_ResetPathStep  timer.o
    0x08004998   0x08004998   0x00000018   Code   RO         2137    i.Timer_Start       timer.o
    0x080049b0   0x080049b0   0x00000014   Code   RO         2138    i.Timer_StartPathMovement  timer.o
    0x080049c4   0x080049c4   0x00000014   Code   RO         2140    i.Timer_StopPathMovement  timer.o
    0x080049d8   0x080049d8   0x00000080   Code   RO         2739    i.USART1_Init       usart.o
    0x08004a58   0x08004a58   0x00000054   Code   RO         2742    i.USART1_SendBuffer  usart.o
    0x08004aac   0x08004aac   0x0000005c   Code   RO         2799    i.USART2_IRQHandler  bluetooth.o
    0x08004b08   0x08004b08   0x000000a8   Code   RO         2800    i.USART2_Init       bluetooth.o
    0x08004bb0   0x08004bb0   0x0000001e   Code   RO         1281    i.USART_ClearITPendingBit  stm32f10x_usart.o
    0x08004bce   0x08004bce   0x00000018   Code   RO         1284    i.USART_Cmd         stm32f10x_usart.o
    0x08004be6   0x08004be6   0x0000001a   Code   RO         1287    i.USART_GetFlagStatus  stm32f10x_usart.o
    0x08004c00   0x08004c00   0x00000054   Code   RO         1288    i.USART_GetITStatus  stm32f10x_usart.o
    0x08004c54   0x08004c54   0x0000004a   Code   RO         1290    i.USART_ITConfig    stm32f10x_usart.o
    0x08004c9e   0x08004c9e   0x00000002   PAD
    0x08004ca0   0x08004ca0   0x000000d8   Code   RO         1291    i.USART_Init        stm32f10x_usart.o
    0x08004d78   0x08004d78   0x0000000a   Code   RO         1298    i.USART_ReceiveData  stm32f10x_usart.o
    0x08004d82   0x08004d82   0x00000008   Code   RO         1301    i.USART_SendData    stm32f10x_usart.o
    0x08004d8a   0x08004d8a   0x00000004   Code   RO         2970    i.UsageFault_Handler  stm32f10x_it.o
    0x08004d8e   0x08004d8e   0x00000028   Code   RO         3324    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x08004db6   0x08004db6   0x00000026   Code   RO         3209    i.__ARM_fpclassifyf  m_ws.l(fpclassifyf.o)
    0x08004ddc   0x08004ddc   0x00000014   Code   RO         1812    i.__ARM_isinff      geometry.o
    0x08004df0   0x08004df0   0x0000000e   Code   RO         1813    i.__ARM_isnanf      geometry.o
    0x08004dfe   0x08004dfe   0x00000002   PAD
    0x08004e00   0x08004e00   0x00000388   Code   RO         3225    i.__ieee754_rem_pio2  m_ws.l(rred.o)
    0x08005188   0x08005188   0x000000aa   Code   RO         3326    i.__kernel_poly     m_ws.l(poly.o)
    0x08005232   0x08005232   0x00000002   PAD
    0x08005234   0x08005234   0x0000027c   Code   RO         3233    i.__kernel_tan      m_ws.l(tan_i.o)
    0x080054b0   0x080054b0   0x00000006   Code   RO         3196    i.__mathlib_dbl_infnan  m_ws.l(dunder.o)
    0x080054b6   0x080054b6   0x0000000c   Code   RO         3198    i.__mathlib_dbl_invalid  m_ws.l(dunder.o)
    0x080054c2   0x080054c2   0x00000002   PAD
    0x080054c4   0x080054c4   0x00000010   Code   RO         3201    i.__mathlib_dbl_underflow  m_ws.l(dunder.o)
    0x080054d4   0x080054d4   0x00000006   Code   RO         3212    i.__mathlib_flt_infnan  m_ws.l(funder.o)
    0x080054da   0x080054da   0x00000008   Code   RO         3214    i.__mathlib_flt_invalid  m_ws.l(funder.o)
    0x080054e2   0x080054e2   0x0000000a   Code   RO         3217    i.__mathlib_flt_underflow  m_ws.l(funder.o)
    0x080054ec   0x080054ec   0x00000168   Code   RO         3230    i.__mathlib_rredf2  m_ws.l(rredf.o)
    0x08005654   0x08005654   0x0000000e   Code   RO         3056    i._is_digit         c_w.l(__printf_wp.o)
    0x08005662   0x08005662   0x00000002   PAD
    0x08005664   0x08005664   0x0000017c   Code   RO         3129    i.atanf             m_ws.l(atanf.o)
    0x080057e0   0x080057e0   0x00000830   Code   RO         2913    i.main              main.o
    0x08006010   0x08006010   0x0000002c   Code   RO         3137    i.sqrtf             m_ws.l(sqrtf.o)
    0x0800603c   0x0800603c   0x00000064   Code   RO         3145    i.tan               m_ws.l(tan.o)
    0x080060a0   0x080060a0   0x00000174   Code   RO         3153    i.tanf              m_ws.l(tanf.o)
    0x08006214   0x08006214   0x0000002c   Code   RO         3296    locale$$code        c_w.l(lc_numeric_c.o)
    0x08006240   0x08006240   0x00000062   Code   RO         3086    x$fpl$d2f           fz_ws.l(d2f.o)
    0x080062a2   0x080062a2   0x00000002   PAD
    0x080062a4   0x080062a4   0x00000150   Code   RO         3298    x$fpl$dadd          fz_ws.l(daddsub_clz.o)
    0x080063f4   0x080063f4   0x00000010   Code   RO         3388    x$fpl$dcheck1       fz_ws.l(dcheck1.o)
    0x08006404   0x08006404   0x000002b0   Code   RO         3305    x$fpl$ddiv          fz_ws.l(ddiv.o)
    0x080066b4   0x080066b4   0x0000005e   Code   RO         3308    x$fpl$dfix          fz_ws.l(dfix.o)
    0x08006712   0x08006712   0x0000002e   Code   RO         3313    x$fpl$dflt          fz_ws.l(dflt_clz.o)
    0x08006740   0x08006740   0x00000026   Code   RO         3312    x$fpl$dfltu         fz_ws.l(dflt_clz.o)
    0x08006766   0x08006766   0x00000002   PAD
    0x08006768   0x08006768   0x00000154   Code   RO         3088    x$fpl$dmul          fz_ws.l(dmul.o)
    0x080068bc   0x080068bc   0x0000009c   Code   RO         3176    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x08006958   0x08006958   0x0000000c   Code   RO         3178    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x08006964   0x08006964   0x00000016   Code   RO         3299    x$fpl$drsb          fz_ws.l(daddsub_clz.o)
    0x0800697a   0x0800697a   0x00000002   PAD
    0x0800697c   0x0800697c   0x000001d4   Code   RO         3300    x$fpl$dsub          fz_ws.l(daddsub_clz.o)
    0x08006b50   0x08006b50   0x00000056   Code   RO         3090    x$fpl$f2d           fz_ws.l(f2d.o)
    0x08006ba6   0x08006ba6   0x00000002   PAD
    0x08006ba8   0x08006ba8   0x000000c4   Code   RO         3092    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x08006c6c   0x08006c6c   0x0000000c   Code   RO         3390    x$fpl$fcheck1       fz_ws.l(fcheck1.o)
    0x08006c78   0x08006c78   0x00000018   Code   RO         3180    x$fpl$fcmpinf       fz_ws.l(fcmpi.o)
    0x08006c90   0x08006c90   0x00000184   Code   RO         3099    x$fpl$fdiv          fz_ws.l(fdiv.o)
    0x08006e14   0x08006e14   0x00000036   Code   RO         3182    x$fpl$ffix          fz_ws.l(ffix.o)
    0x08006e4a   0x08006e4a   0x00000002   PAD
    0x08006e4c   0x08006e4c   0x0000003e   Code   RO         3102    x$fpl$ffixu         fz_ws.l(ffixu.o)
    0x08006e8a   0x08006e8a   0x00000002   PAD
    0x08006e8c   0x08006e8c   0x00000030   Code   RO         3107    x$fpl$fflt          fz_ws.l(fflt_clz.o)
    0x08006ebc   0x08006ebc   0x00000026   Code   RO         3106    x$fpl$ffltu         fz_ws.l(fflt_clz.o)
    0x08006ee2   0x08006ee2   0x00000002   PAD
    0x08006ee4   0x08006ee4   0x00000068   Code   RO         3112    x$fpl$fleqf         fz_ws.l(fleqf.o)
    0x08006f4c   0x08006f4c   0x00000102   Code   RO         3114    x$fpl$fmul          fz_ws.l(fmul.o)
    0x0800704e   0x0800704e   0x0000008c   Code   RO         3186    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x080070da   0x080070da   0x0000000a   Code   RO         3188    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x080070e4   0x080070e4   0x00000062   Code   RO         3116    x$fpl$frleqf        fz_ws.l(frleqf.o)
    0x08007146   0x08007146   0x00000002   PAD
    0x08007148   0x08007148   0x00000060   Code   RO         3190    x$fpl$frnd          fz_ws.l(frnd.o)
    0x080071a8   0x080071a8   0x00000014   Code   RO         3093    x$fpl$frsb          fz_ws.l(faddsub_clz.o)
    0x080071bc   0x080071bc   0x00000110   Code   RO         3192    x$fpl$fsqrt         fz_ws.l(fsqrt.o)
    0x080072cc   0x080072cc   0x000000ea   Code   RO         3094    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x080073b6   0x080073b6   0x00000004   Code   RO         3118    x$fpl$printf1       fz_ws.l(printf1.o)
    0x080073ba   0x080073ba   0x00000064   Code   RO         3398    x$fpl$retnan        fz_ws.l(retnan.o)
    0x0800741e   0x0800741e   0x0000005c   Code   RO         3320    x$fpl$scalbn        fz_ws.l(scalbn.o)
    0x0800747a   0x0800747a   0x0000004c   Code   RO         3322    x$fpl$scalbnf       fz_ws.l(scalbnf.o)
    0x080074c6   0x080074c6   0x00000030   Code   RO         3411    x$fpl$trapveneer    fz_ws.l(trapv.o)
    0x080074f6   0x080074f6   0x00000000   Code   RO         3194    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x080074f6   0x080074f6   0x000005f0   Data   RO         1607    .constdata          oled.o
    0x08007ae6   0x08007ae6   0x00000002   PAD
    0x08007ae8   0x08007ae8   0x00000010   Data   RO         1969    .constdata          statemachine.o
    0x08007af8   0x08007af8   0x00000020   Data   RO         2562    .constdata          systemdiagnostics.o
    0x08007b18   0x08007b18   0x00000090   Data   RO         2914    .constdata          main.o
    0x08007ba8   0x08007ba8   0x000000c8   Data   RO         3227    .constdata          m_ws.l(rred.o)
    0x08007c70   0x08007c70   0x00000020   Data   RO         3231    .constdata          m_ws.l(rredf.o)
    0x08007c90   0x08007c90   0x00000060   Data   RO         3234    .constdata          m_ws.l(tan_i.o)
    0x08007cf0   0x08007cf0   0x00000094   Data   RO         3271    .constdata          c_w.l(bigflt0.o)
    0x08007d84   0x08007d84   0x0000008f   Data   RO         1970    .conststring        statemachine.o
    0x08007e13   0x08007e13   0x00000001   PAD
    0x08007e14   0x08007e14   0x000000af   Data   RO         2802    .conststring        bluetooth.o
    0x08007ec3   0x08007ec3   0x00000001   PAD
    0x08007ec4   0x08007ec4   0x00000020   Data   RO         3475    Region$$Table       anon$$obj.o
    0x08007ee4   0x08007ee4   0x0000001c   Data   RO         3295    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08007f00, Size: 0x00000ac0, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08007f00   0x00000014   Data   RW          558    .data               stm32f10x_rcc.o
    0x20000014   0x08007f14   0x00000010   Data   RW         1971    .data               statemachine.o
    0x20000024   0x08007f24   0x00000014   Data   RW         2143    .data               timer.o
    0x20000038   0x08007f38   0x00000003   Data   RW         2286    .data               manualrecord.o
    0x2000003b   0x08007f3b   0x00000001   PAD
    0x2000003c   0x08007f3c   0x00000016   Data   RW         2915    .data               main.o
    0x20000052   0x08007f52   0x00000002   PAD
    0x20000054        -       0x0000001e   Zero   RW         1527    .bss                key.o
    0x20000072   0x08007f52   0x00000002   PAD
    0x20000074        -       0x00000014   Zero   RW         2142    .bss                timer.o
    0x20000088        -       0x00000030   Zero   RW         2285    .bss                manualrecord.o
    0x200000b8        -       0x000003a4   Zero   RW         2801    .bss                bluetooth.o
    0x2000045c        -       0x00000060   Zero   RW         3329    .bss                c_w.l(libspace.o)
    0x200004bc   0x08007f52   0x00000004   PAD
    0x200004c0        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f10x_md.o
    0x200006c0        -       0x00000400   Zero   RW            1    STACK               startup_stm32f10x_md.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      1732        210          0          0          0      15647   automovement.o
      1594        628        175          0        932      11694   bluetooth.o
         0          0          0          0          0       4516   core_cm3.o
        70          0          0          0          0        962   delay.o
      1268         76          0          0          0      16174   geometry.o
       400         30          0          0         30       5191   key.o
        84         14          0          0          0       1354   led.o
      2096        614        144         22          0      12272   main.o
        96         18          0          3         48       2113   manualrecord.o
       112         12          0          0          0     204352   misc.o
       734         22       1520          0          0       6583   oled.o
      1058         34          0          0          0      11498   servo.o
        64         26        236          0       1536        828   startup_stm32f10x_md.o
      2466        666        159         16          0      17451   statemachine.o
         0          0          0          0          0       1676   stm32f10x_exti.o
       314          0          0          0          0      12468   stm32f10x_gpio.o
        34          0          0          0          0       4380   stm32f10x_it.o
       276         32          0         20          0      13090   stm32f10x_rcc.o
       246         42          0          0          0      23774   stm32f10x_tim.o
       472          6          0          0          0      12667   stm32f10x_usart.o
       328         28          0          0          0      45365   system_stm32f10x.o
      2288        668         32          0          0      12485   systemdiagnostics.o
       482         92          0         20         20       6713   timer.o
       212         12          0          0          0       1606   usart.o

    ----------------------------------------------------------------------
     16454       <USER>       <GROUP>         84       2568     444859   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        28          0          4          3          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       352          0          0          0          0         88   __printf_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
         6          0          0          0          0          0   _printf_u.o
        22          0          0          0          0        100   _rserrno.o
        10          0          0          0          0         68   _sputc.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        18          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
        40          6          0          0          0         84   noretval__2sprintf.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        72          0          0          0          0         80   strcpy.o
        86          0          0          0          0         76   strncpy.o
        36          0          0          0          0         80   strstr.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        98          4          0          0          0         92   d2f.o
       826         16          0          0          0        348   daddsub_clz.o
        16          4          0          0          0         68   dcheck1.o
       688        140          0          0          0        208   ddiv.o
        94          4          0          0          0         92   dfix.o
        84          0          0          0          0        136   dflt_clz.o
       340         12          0          0          0        104   dmul.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
        86          4          0          0          0         84   f2d.o
       450          8          0          0          0        236   faddsub_clz.o
        12          4          0          0          0         68   fcheck1.o
        24          0          0          0          0         68   fcmpi.o
       388         76          0          0          0         96   fdiv.o
        54          4          0          0          0         84   ffix.o
        62          4          0          0          0         84   ffixu.o
        86          0          0          0          0        136   fflt_clz.o
       104          4          0          0          0         84   fleqf.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
        98          0          0          0          0         68   frleqf.o
        96          4          0          0          0         76   frnd.o
       272        100          0          0          0         88   fsqrt.o
         4          0          0          0          0         68   printf1.o
       100          0          0          0          0         68   retnan.o
        92          0          0          0          0         68   scalbn.o
        76          0          0          0          0         68   scalbnf.o
        48          0          0          0          0         68   trapv.o
         0          0          0          0          0          0   usenofp.o
       380         56          0          0          0        108   atanf.o
        34          6          0          0          0        204   dunder.o
        40          0          0          0          0         68   fpclassify.o
        38          0          0          0          0         68   fpclassifyf.o
        24          0          0          0          0        204   funder.o
       170          0          0          0          0         96   poly.o
       904         76        200          0          0        140   rred.o
       360         16         32          0          0         88   rredf.o
        44          0          0          0          0         80   sqrtf.o
       100         10          0          0          0         96   tan.o
       636         50         96          0          0        168   tan_i.o
       372         58          0          0          0        124   tanf.o

    ----------------------------------------------------------------------
     13252        <USER>        <GROUP>          0        100       7748   Library Totals
        32          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      5344        210        176          0         96       3348   c_w.l
      4774        400          0          0          0       2956   fz_ws.l
      3102        272        328          0          0       1444   m_ws.l

    ----------------------------------------------------------------------
     13252        <USER>        <GROUP>          0        100       7748   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     29706       4112       2806         84       2668     439447   Grand Totals
     29706       4112       2806         84       2668     439447   ELF Image Totals
     29706       4112       2806         84          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                32512 (  31.75kB)
    Total RW  Size (RW Data + ZI Data)              2752 (   2.69kB)
    Total ROM Size (Code + RO Data + RW Data)      32596 (  31.83kB)

==============================================================================

