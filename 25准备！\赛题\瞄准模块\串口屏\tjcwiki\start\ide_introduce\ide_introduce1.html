<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>1.菜单栏 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
        <script src="../../_static/jquery.js"></script>
        <script src="../../_static/underscore.js"></script>
        <script src="../../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../../_static/doctools.js"></script>
        <script src="../../_static/translations.js"></script>
        <script src="../../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../../_static/custom.js"></script>
    <script src="../../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../../genindex.html" />
    <link rel="search" title="搜索" href="../../search.html" />
    <link rel="next" title="2.工具栏" href="ide_introduce2.html" />
    <link rel="prev" title="上位机基本功能介绍" href="index.html" />
    <link href="../../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="../index.html">快速入门</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../download_ide.html">下载和安装上位机软件</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html">上位机基本功能介绍</a><ul class="current">
<li class="toctree-l3 current"><a class="current reference internal" href="#">1.菜单栏</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id2">文件菜单</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id3">工具菜单</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id4">设置菜单</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id5">帮助菜单</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id6">关于菜单</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce2.html">2.工具栏</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce3.html">3.工具箱</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce4.html">4.资源文件窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce5.html">5.界面</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce6.html">6.特殊控件窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce7.html">7.输出窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce8.html">8.事件编辑窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce9.html">9.页面窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce10.html">10.属性窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce11.html">11.调试窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce12.html">12.下载窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce13.html">13.设备</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../first_test.html">到手测试</a></li>
<li class="toctree-l2"><a class="reference internal" href="../create_project/index.html">创建工程</a></li>
<li class="toctree-l2"><a class="reference internal" href="../codeSpace.html">代码编写</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="../index.html">快速入门</a> &raquo;</li>
          <li><a href="index.html">上位机基本功能介绍</a> &raquo;</li>
      <li>1.菜单栏</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>1.菜单栏<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<img alt="../../_images/menuBar1.png" src="../../_images/menuBar1.png" />
<section id="id2">
<h2>文件菜单<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<img alt="../../_images/menuBar2.png" src="../../_images/menuBar2.png" />
<p>1.输出生产文件</p>
<p>该菜单用于输出编译生成的.tft类型文件。.tft文件用于做TF卡升级或外部设备串口升级使用。</p>
<img alt="../../_images/menuBar3.png" src="../../_images/menuBar3.png" />
<p>1.1 生产输出的.tft文件，与项目工程文件同名。</p>
<p>1.2 在弹出的《输出生产文件》窗口窗口中，我们可以选择一个指定的目录用于存放输出的.tft文件。</p>
<p>1.3 选择《输出》按钮。将对项目进行编译后，生成新的.tft文件。并打开《输出文件夹》。</p>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>如果《输出文件夹》已经存在同名的.tft文件，新生成的文件会覆盖旧文件。</p>
</div>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>如果项目编译失败，将不会打开《输出文件夹》。请留意输出窗口的错误提示。</p>
</div>
<p>1.4 《仅打开输出文件夹》。不编译当前项目工程，直接打开《输出文件夹》。</p>
<div class="admonition tip">
<p class="admonition-title">小技巧</p>
<p>技巧：如果仅仅是想打开上次编译生成的文件，可以执行该操作。</p>
</div>
<dl class="simple">
<dt>2.导出控件名称宏定义</dt><dd><p>导出页面编号和控件编号的宏定义(.h文件,用于导入单片机工程中使用)</p>
</dd>
<dt>3.打开</dt><dd><p>打开一个.HMI工程文件。</p>
</dd>
<dt>4.新建</dt><dd><p>新建一个.HMI工程文件。</p>
</dd>
<dt>5.保存</dt><dd><p>保存当前正在编辑的工程文件。</p>
</dd>
<dt>6.另存为</dt><dd><p>将当前编辑的工程文件，另存为一个副本。</p>
</dd>
<dt>7.版本备份目录</dt><dd><p>打开backup文件夹。 backup文件夹下保存的工程文件为使用新版本USART HMI软件第一次打开旧版本工程文件之前自动备份的工程文件。</p>
</dd>
<dt>8.虚拟eeprom文件夹</dt><dd><p>打开eeprom文件夹。eeprom文件夹保存的文件是USART HMI软件在模拟运行工程中生成的虚拟eeprom文件。</p>
</dd>
<dt>9.虚拟SD卡文件夹</dt><dd><p>打开虚拟SD卡文件夹。虚拟SD卡文件夹保存的文件是USART HMI软件在模拟运行工程中使用到SD卡内的文件。</p>
</dd>
<dt>10.导入工程</dt><dd><p>将会导入另一个工程的所有页面。</p>
</dd>
<dt>11.关闭工程</dt><dd><p>关闭当前正在编辑的工程文件。</p>
</dd>
<dt>12.退出</dt><dd><p>退出USART HMI集成开发环境。</p>
</dd>
</dl>
</section>
<section id="id3">
<h2>工具菜单<a class="headerlink" href="#id3" title="此标题的永久链接"></a></h2>
<img alt="../../_images/menuBar4.png" src="../../_images/menuBar4.png" />
<dl>
<dt>2.1字库制作</dt><dd><p>《字库制作》工具是USART HMI开发环境使用的专用字库制作工具。</p>
<p>参考：</p>
<p><a class="reference internal" href="../create_project/create_project_5.html#id1"><span class="std std-ref">创建字库和导入字库</span></a></p>
<p><a class="reference internal" href="../../QA/QA8.html#id1"><span class="std std-ref">指定字库</span></a></p>
<p><a class="reference internal" href="../../QA/QA29.html#id1"><span class="std std-ref">在做字库的时候有部分字体无法选择</span></a></p>
<p><a class="reference internal" href="../../QA/QA9.html#xxx"><span class="std std-ref">编译报错：XXX初始值无效</span></a></p>
<p><a class="reference internal" href="../../QA/QA76.html#utf8"><span class="std std-ref">utf8字库下如何只选择汉字</span></a></p>
<p><a class="reference internal" href="../../QA/QA44.html#file-is-too-large-for-destination-device"><span class="std std-ref">报错:file is too large for destination device</span></a></p>
</dd>
<dt>2.2动画制作(GmovMaker)</dt><dd><p>《动画制作》工具是USART HMI开发环境使用的专用动画制作工具。</p>
<p>参考：</p>
<p><a class="reference internal" href="../../widgets/Gmov.html#id1"><span class="std std-ref">动画控件</span></a></p>
</dd>
<dt>2.3视频/音频转换(VideoBox)</dt><dd><p>《视频/音频转换》工具是USART HMI开发环境使用的专用视频/音频制作工具。</p>
<p>参考：</p>
<p><a class="reference internal" href="../../widgets/Video.html#id1"><span class="std std-ref">视频控件</span></a></p>
</dd>
<dt>2.4图片转换</dt><dd><p>《图片转换》工具是USART HMI开发环境使用的用于外部图片控件的工具</p>
<p>参考：</p>
<p><a class="reference internal" href="../../widgets/ExPicture.html#id1"><span class="std std-ref">外部图片控件</span></a></p>
</dd>
</dl>
</section>
<section id="id4">
<h2>设置菜单<a class="headerlink" href="#id4" title="此标题的永久链接"></a></h2>
<img alt="../../_images/menuBar5.png" src="../../_images/menuBar5.png" />
<dl class="simple">
<dt>3.1软件设置</dt><dd><p>在该选项打开的窗口中，可以根据个人喜好对开发环境的各种参数进行配置。</p>
</dd>
<dt>3.2重置窗口布局</dt><dd><p>将开发环境的各项配置参数，窗口布局恢复到默认状态。</p>
</dd>
</dl>
</section>
<section id="id5">
<h2>帮助菜单<a class="headerlink" href="#id5" title="此标题的永久链接"></a></h2>
<img alt="../../_images/menuBar6.png" src="../../_images/menuBar6.png" />
<dl class="simple">
<dt>4.1指令集</dt><dd><p>该菜单可以打开一个本地文档，该文档包含串口屏的关键文档描述。方便无法连接互联网的客户查阅资料。</p>
</dd>
</dl>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>强烈建议客户使用在线版本资料中心的资料。因为离线版本并没有包含本资料中心的所有资源文档，同时可能由于资料更新不及时出现部分功能描述与软件功能不一致。</p>
</div>
<dl class="simple">
<dt>4.2 USART HMI 资料中心</dt><dd><p>该菜单会链接到在线网页版本的USART HMI 资料中心。</p>
</dd>
</dl>
</section>
<section id="id6">
<h2>关于菜单<a class="headerlink" href="#id6" title="此标题的永久链接"></a></h2>
<img alt="../../_images/menuBar7.png" src="../../_images/menuBar7.png" />
<dl class="simple">
<dt>1.5.1关于USART HMI</dt><dd><p>该菜单用于查看软件版本信息。</p>
</dd>
<dt>1.5.2检测版本更新</dt><dd><p>该菜单用于连接服务器，检测软件是否有新版本。</p>
</dd>
</dl>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="上位机基本功能介绍" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="ide_introduce2.html" class="btn btn-neutral float-right" title="2.工具栏" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>