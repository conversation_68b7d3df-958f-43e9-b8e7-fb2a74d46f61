/**
  ******************************************************************************
  * @file    app_servo.h
  * <AUTHOR> Analysis Expert
  * @version V1.0
  * @date    2025-07-17
  * @brief   舵机应用层头文件
  ******************************************************************************
  */

#ifndef __APP_SERVO_H
#define __APP_SERVO_H

#include "stm32f4xx.h"

/* 舵机参数定义 */
#define SERVO_ID                1       // 舵机ID
#define SERVO_MOVE_TIME         600     // 运动时间 600ms
#define SERVO_WAIT_TIME         700     // 等待时间 700ms (600+100)
#define SERVO_STEP_ANGLE        30.0f   // 步进角度 30度

/* 应用状态定义 */
typedef enum
{
    SERVO_STATE_IDLE = 0,       // 空闲状态
    SERVO_STATE_MOVING,         // 运动状态
    SERVO_STATE_WAITING         // 等待状态
} ServoState_t;

/* 全局变量声明 */
extern volatile uint32_t g_systick_count;

/* 函数声明 */
void App_Servo_Init(void);
void App_Servo_Task(void);
void App_Servo_SysTick(void);

#endif /* __APP_SERVO_H */
