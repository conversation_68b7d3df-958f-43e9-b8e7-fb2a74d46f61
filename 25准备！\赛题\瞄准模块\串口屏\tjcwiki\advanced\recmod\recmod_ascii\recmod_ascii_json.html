<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>接收json数据字符串 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../../../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../../../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../../../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../../../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../../../" id="documentation_options" src="../../../_static/documentation_options.js"></script>
        <script src="../../../_static/jquery.js"></script>
        <script src="../../../_static/underscore.js"></script>
        <script src="../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../../../_static/doctools.js"></script>
        <script src="../../../_static/translations.js"></script>
        <script src="../../../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../../../_static/custom.js"></script>
    <script src="../../../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../../../genindex.html" />
    <link rel="search" title="搜索" href="../../../search.html" />
    <link rel="next" title="解析不定长字符串(以回车换行结尾)" href="recmod_ascii_2.html" />
    <link rel="prev" title="解析AT指令" href="recmod_ascii_attention.html" />
    <link href="../../../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../../../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../../../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../../../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="../../index.html">串口屏高级应用详解</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../../keyboard/index.html">系统键盘</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="../index.html">主动解析模式应用详解</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="../recmod_base.html">主动解析基本知识</a></li>
<li class="toctree-l3"><a class="reference internal" href="../fixed_hex/index.html">解析定长hex格式指令-自定义协议</a></li>
<li class="toctree-l3"><a class="reference internal" href="../unfixed_hex/index.html">解析不定长hex格式指令-自定义协议</a></li>
<li class="toctree-l3 current"><a class="reference internal" href="index.html">解析字符串格式指令</a><ul class="current">
<li class="toctree-l4"><a class="reference internal" href="recmod_ascii_attention.html">解析AT指令</a></li>
<li class="toctree-l4 current"><a class="current reference internal" href="#">接收json数据字符串</a></li>
<li class="toctree-l4"><a class="reference internal" href="recmod_ascii_2.html">解析不定长字符串(以回车换行结尾)</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../hmi_download_protocol.html">HMI下载协议详解/OTA升级</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../crc.html">串口指令增加CRC校验</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../crc2.html">程序中使用CRC校验数据</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../transmit_data.html">运行中串口传输文件到内存或SD卡</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../SDcard.html">SD卡读写文件流程</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../expandIO.html">拓展IO资料</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../get_property.html">获取控件自身的属性</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../wepo_repo.html">读写用户存储区</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../getv.html">获取串口屏设备唯一序列号</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="../../index.html">串口屏高级应用详解</a> &raquo;</li>
          <li><a href="../index.html">主动解析模式应用详解</a> &raquo;</li>
          <li><a href="index.html">解析字符串格式指令</a> &raquo;</li>
      <li>接收json数据字符串</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="json">
<h1>接收json数据字符串<a class="headerlink" href="#json" title="此标题的永久链接"></a></h1>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>每个页面仅建议在一个定时器中读取串口缓冲区的数据,在多个定时器中读取串口缓冲区的数据容易照成逻辑混乱。</p>
</div>
<p>以下面的json为例</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="linenos">2</span><span class="w">     </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;test&quot;</span><span class="p">,</span><span class="w"></span>
<span class="linenos">3</span><span class="w">     </span><span class="nt">&quot;info&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="linenos">4</span><span class="w">         </span><span class="nt">&quot;age&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">16</span><span class="p">,</span><span class="w"></span>
<span class="linenos">5</span><span class="w">         </span><span class="nt">&quot;pass&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="w"></span>
<span class="linenos">6</span><span class="w">     </span><span class="p">}</span><span class="w"></span>
<span class="linenos">7</span><span class="w"> </span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<p>json数据中，左括号和右括号是成对出现的，因此我们可以通过这个特性，将第一个左括号当做帧头，最后一个右括号当做帧尾</p>
<p>新建一个空白工程</p>
<p>在工程中新建一个定时器tmDecode，tim设置为50，en设置为1，用于定时解析串口数据</p>
<p>新建数字控件n1,n2,n3,n4，用于显示解析出来的数据</p>
<p>program.s中的配置如图所示</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span>//以下代码只在上电时运行一次,一般用于全局变量定义和上电初始化数据
<span class="linenos"> 2</span>//全局变量定义目前仅支持4字节有符号整形(int),不支持其他类型的全局变量声明,如需使用字符串类型可以在页面中使用变量控件来实现
<span class="linenos"> 3</span>int sys0=0,sys1=0,sys2=0
<span class="linenos"> 4</span>int jsonIndex=1
<span class="linenos"> 5</span>//frameLength：每帧数据长度
<span class="linenos"> 6</span>//getFullJson：是否找到完整的json
<span class="linenos"> 7</span>//getFrameFlag：帧头的数量
<span class="linenos"> 8</span>//jsonLength：json数据实际长度
<span class="linenos"> 9</span>int frameLength=2,getFullJson=0,getFrameFlag,jsonLength
<span class="linenos">10</span>//breakFlag：防止解析卡死在while中
<span class="linenos">11</span>int breakFlag
<span class="linenos">12</span>//键和值是否存在
<span class="linenos">13</span>int isKeyExist,isValueExist
<span class="linenos">14</span>bauds=115200 //波特率115200
<span class="linenos">15</span>recmod=1    //打开主动解析
<span class="linenos">16</span>page 0                       //上电刷新第0页
</pre></div>
</div>
<p>解析定时器（tim为50）中的代码如下图所示</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span>breakFlag=0
<span class="linenos"> 2</span>while(usize&gt;=frameLength&amp;&amp;getFullJson==0&amp;&amp;breakFlag==0)
<span class="linenos"> 3</span>{
<span class="linenos"> 4</span>  if(u[0]!=0x7B)
<span class="linenos"> 5</span>  {
<span class="linenos"> 6</span>    //如果帧头不对，就一直删除1个字节，直到不满足条件退出循环
<span class="linenos"> 7</span>    //0x7B是左括号{对应的ascii码值
<span class="linenos"> 8</span>    udelete 1
<span class="linenos"> 9</span>  }else
<span class="linenos">10</span>  {
<span class="linenos">11</span>    getFrameFlag=0
<span class="linenos">12</span>    for(sys0=0;sys0&lt;usize;sys0++)
<span class="linenos">13</span>    {
<span class="linenos">14</span>      if(u[sys0]==0x7B)
<span class="linenos">15</span>      {
<span class="linenos">16</span>        //找到左括号，帧头数量自增
<span class="linenos">17</span>        getFrameFlag++
<span class="linenos">18</span>      }else if(u[sys0]==0x7D)
<span class="linenos">19</span>      {
<span class="linenos">20</span>        //找到右括号，帧头数量自减
<span class="linenos">21</span>        getFrameFlag--
<span class="linenos">22</span>      }
<span class="linenos">23</span>      //当getFrameFlag==0时，说明左右括号数量相等，接收到了完整数据
<span class="linenos">24</span>      if(getFrameFlag==0)
<span class="linenos">25</span>      {
<span class="linenos">26</span>        getFullJson=1
<span class="linenos">27</span>        jsonLength=sys0
<span class="linenos">28</span>        //让for循环不满足条件退出
<span class="linenos">29</span>        sys0=usize+1
<span class="linenos">30</span>      }
<span class="linenos">31</span>    }
<span class="linenos">32</span>    //已经解析过整个数据，退出while循环
<span class="linenos">33</span>    breakFlag=1
<span class="linenos">34</span>  }
<span class="linenos">35</span>}
<span class="linenos">36</span>if(getFullJson==1)
<span class="linenos">37</span>{
<span class="linenos">38</span>  //拷贝指定长度到myJson.txt
<span class="linenos">39</span>  ucopy myJson.txt,0,jsonLength,0
<span class="linenos">40</span>  //删除指定长度的串口缓冲区
<span class="linenos">41</span>  udelete jsonLength
<span class="linenos">42</span>  getFullJson=0
<span class="linenos">43</span>}
</pre></div>
</div>
<p>以上代码仅演示如何接收json。代码本身的健壮性存在问题，请勿直接使用</p>
<p>建议参考以下方法发送和接收json字符串，可以降低接收json字符串的难度 <a class="reference internal" href="../unfixed_hex/unfixed3.html#id1"><span class="std std-ref">传输不定长的字符串</span></a></p>
<section id="id1">
<h2>json解析-样例工程下载<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/串口屏高级应用/主动解析/json解析.HMI">《json解析》演示工程下载</a></p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="recmod_ascii_attention.html" class="btn btn-neutral float-left" title="解析AT指令" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="recmod_ascii_2.html" class="btn btn-neutral float-right" title="解析不定长字符串(以回车换行结尾)" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>