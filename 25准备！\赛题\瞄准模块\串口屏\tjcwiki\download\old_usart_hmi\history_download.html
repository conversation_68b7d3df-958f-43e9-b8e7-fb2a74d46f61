<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>旧版本上位机下载 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
        <script src="../../_static/jquery.js"></script>
        <script src="../../_static/underscore.js"></script>
        <script src="../../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../../_static/doctools.js"></script>
        <script src="../../_static/translations.js"></script>
        <script src="../../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../../_static/custom.js"></script>
    <script src="../../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../../genindex.html" />
    <link rel="search" title="搜索" href="../../search.html" />
    <link rel="next" title="开发文档下载" href="../development_doc.html" />
    <link rel="prev" title="上位机下载" href="../usart_hmi.html" />
    <link href="../../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="../index.html">资料下载</a><ul class="current">
<li class="toctree-l2 current"><a class="reference internal" href="../usart_hmi.html">上位机下载</a><ul class="current">
<li class="toctree-l3 current"><a class="current reference internal" href="#">旧版本上位机下载</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id2">下载V1.67.5 USART HMI软件（最新）</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id4">下载V1.67.5 USART HMI软件(免安装版)</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id5">下载V1.67.3 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id7">下载V1.67.3 USART HMI软件(免安装版)</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id8">下载V1.67.2 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id10">下载V1.67.2 USART HMI软件(免安装版)</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id11">下载V1.67.1 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id13">下载V1.67.1 USART HMI软件(免安装版)</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id14">下载V1.66.3 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id16">下载V1.66.3 USART HMI软件(免安装版)</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id17">下载V1.66.2 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id18">下载V1.66.1 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id19">下载V1.65 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id20">下载V1.64 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id21">下载V1.63 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id22">下载V1.61 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id23">下载V1.60 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id24">下载V0.59 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id25">下载V0.57 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id26">下载V0.56 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id27">下载V0.55 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id28">下载V0.54 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id29">下载V0.52 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id30">下载V0.51 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id31">下载V0.50 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id32">下载V0.49 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id33">下载V0.47 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id34">下载V0.46 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id35">下载V0.45 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id36">下载V0.44 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id37">下载V0.42 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id38">下载V0.41 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id39">下载V0.40 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id40">下载V0.39 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id41">下载V0.37 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id42">下载V0.36 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id43">下载V0.35 USART HMI软件</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id44">下载V0.34 USART HMI软件</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development_doc.html">开发文档下载</a></li>
<li class="toctree-l2"><a class="reference internal" href="../tools_download.html">常用工具下载</a></li>
<li class="toctree-l2"><a class="reference internal" href="../default_project.html">标准出厂工程样例</a></li>
<li class="toctree-l2"><a class="reference internal" href="../moreProject/index.html">官方样例工程</a></li>
<li class="toctree-l2"><a class="reference internal" href="../ui_demo.html">UI样例工程</a></li>
<li class="toctree-l2"><a class="reference internal" href="../other_project.html">网友提供应用样例</a></li>
<li class="toctree-l2"><a class="reference internal" href="../tjc_game.html">游戏工程</a></li>
<li class="toctree-l2"><a class="reference internal" href="../font_download.html">免费字体下载</a></li>
<li class="toctree-l2"><a class="reference internal" href="../voice_download.html">声音资源下载</a></li>
<li class="toctree-l2"><a class="reference internal" href="../scheme_download.html">原理图下载</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="../index.html">资料下载</a> &raquo;</li>
          <li><a href="../usart_hmi.html">上位机下载</a> &raquo;</li>
      <li>旧版本上位机下载</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>旧版本上位机下载<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<section id="id2">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/USARTHMIsetup_1.67.5.zip">下载V1.67.5 USART HMI软件（最新）</a><a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<p>1.优化串口通讯底层驱动。</p>
</section>
<section id="id4">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/USARTHMIsetup_1.67.5.7z">下载V1.67.5 USART HMI软件(免安装版)</a><a class="headerlink" href="#id4" title="此标题的永久链接"></a></h2>
<p>某些用户安装时会报错，可以使用免安装版</p>
</section>
<section id="id5">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/USARTHMIsetup_1.67.3.zip">下载V1.67.3 USART HMI软件</a><a class="headerlink" href="#id5" title="此标题的永久链接"></a></h2>
<p>1.增加2.1寸,2.8寸圆屏驱动。</p>
<p>2.修复已知bug。</p>
</section>
<section id="id7">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/USARTHMIsetup_1.67.3.7z">下载V1.67.3 USART HMI软件(免安装版)</a><a class="headerlink" href="#id7" title="此标题的永久链接"></a></h2>
<p>某些用户安装时会报错，可以使用免安装版</p>
</section>
<section id="id8">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/USARTHMIsetup_1.67.2.zip">下载V1.67.2 USART HMI软件</a><a class="headerlink" href="#id8" title="此标题的永久链接"></a></h2>
<p>1.优化X系列电容触摸滑动翻页流畅度。</p>
</section>
<section id="id10">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/USARTHMIsetup_1.67.2.7z">下载V1.67.2 USART HMI软件(免安装版)</a><a class="headerlink" href="#id10" title="此标题的永久链接"></a></h2>
<p>某些用户安装时会报错，可以使用免安装版</p>
</section>
<section id="id11">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/USARTHMIsetup_1.67.1.zip">下载V1.67.1 USART HMI软件</a><a class="headerlink" href="#id11" title="此标题的永久链接"></a></h2>
<p>1.优化4.3高分竖屏底层驱动。</p>
<p>2.优化X5系列NAND FLASH底层驱动。</p>
<p>3.增加T1系列2.0寸产品驱动。</p>
</section>
<section id="id13">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/USARTHMIsetup_1.67.1.7z">下载V1.67.1 USART HMI软件(免安装版)</a><a class="headerlink" href="#id13" title="此标题的永久链接"></a></h2>
<p>某些用户安装时会报错，可以使用免安装版</p>
</section>
<section id="id14">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/USARTHMIsetup_1.66.3.zip">下载V1.66.3 USART HMI软件</a><a class="headerlink" href="#id14" title="此标题的永久链接"></a></h2>
<p>1.优化4.3高分竖屏底层驱动。</p>
<p>2.优化X5系列NAND FLASH底层驱动。</p>
<p>3.增加T1系列2.0寸产品驱动。</p>
</section>
<section id="id16">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/USARTHMIsetup_1.66.3.7z">下载V1.66.3 USART HMI软件(免安装版)</a><a class="headerlink" href="#id16" title="此标题的永久链接"></a></h2>
<p>某些用户安装时会报错，可以使用免安装版</p>
</section>
<section id="id17">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/USARTHMIsetup_1.66.2.zip">下载V1.66.2 USART HMI软件</a><a class="headerlink" href="#id17" title="此标题的永久链接"></a></h2>
<p>1.优化T1系列产品电容触摸性能。</p>
<p>2.增加X2系列6.2寸产品型号。</p>
</section>
<section id="id18">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/USARTHMIsetup_1.66.1.zip">下载V1.66.1 USART HMI软件</a><a class="headerlink" href="#id18" title="此标题的永久链接"></a></h2>
<p>1.66更新说明:</p>
<p>1.优化新品T1系列4.3寸屏显示效果。</p>
<p>2.修复已知bug。</p>
<p>1.66.1:第一次发布</p>
</section>
<section id="id19">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/1.65.zip">下载V1.65 USART HMI软件</a><a class="headerlink" href="#id19" title="此标题的永久链接"></a></h2>
<p>1.65更新说明：</p>
<p>1.增加T1系列4.3寸产品型号。</p>
<p>2.X系列优化图片无损压缩算法。</p>
<p>3.取消设备开机固定发送开机信息，转为Program.s中由代码发送开机信息（用户可自行删除）。</p>
<p>1.65.5</p>
<p>增加T1系列5寸屏驱动。</p>
<p>1.65.4</p>
<p>优化T1系列4.3寸玻璃驱动。</p>
<p>1.65.3</p>
<p>优化上位机定时器的计时准确度。</p>
<p>修复HMI文件小于7M时部分文件受损的bug。</p>
<p>1.65.2</p>
<p>修复部分型号触摸长按异常。</p>
<p>新品T1 4.3寸增加电容触摸驱动。</p>
<p>1.65.1更新</p>
<p>修复大于2G的HMI文件打开异常的问题。</p>
<p>优化X2系列小图片（小于33*33分辨率的图片）的无损压缩效率。</p>
<p>1.65.0更新</p>
<p>第一次发布</p>
</section>
<section id="id20">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/1.64.zip">下载V1.64 USART HMI软件</a><a class="headerlink" href="#id20" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>1.增加X2系列产品支持。</p>
<p>2.X系列增加图片缓冲。</p>
<p>3.增加X2系列的beep指令。</p>
<p>4.优化x系列性能。</p>
<p>5.修复X系列部分产品随机数异常。</p>
<p>6.修复k0系列屏幕前初始化时间过长会导致屏闪。</p>
<p>7.修复T0/K0在极端电源情况下触摸失效的bug。</p>
<p>8.修复定时器定时不准确。</p>
<p>9.修复其他已知bug。</p>
<p>1.64.1</p>
<p>第一次发布</p>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id21">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/1.63.zip">下载V1.63 USART HMI软件</a><a class="headerlink" href="#id21" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>1.增加触摸捕捉控件。</p>
<p>2.优化X系列电阻触摸精度。</p>
<p>3.增加新主控MCU支持。</p>
<p>4.修复已知bug。</p>
<p>1.63.1</p>
<p>第一次发布</p>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id22">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/1.61.zip">下载V1.61 USART HMI软件</a><a class="headerlink" href="#id22" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>1.增加字符串分割指令:spstr(用于按指定关键字分割字符串)。</p>
<p>2.增加音频控件(FLASH内部音频和SD卡外部音频均可播放)。</p>
<p>3.增加1.8寸产品线型号。</p>
<p>4.优化屏幕联机时的波特率识别准确度。</p>
<p>5.修复big5编码下部分字符和转义字符冲突问题。</p>
<p>6.修复T0系列3.5寸以下在电源不稳定的情况下可能导致Flash数据损坏的风险。</p>
<p>子版本修复记录：</p>
<p>1.61.6</p>
<p>修复X系列电阻触摸屏长时间工作偶尔出现触摸失效的BUG。</p>
<p>1.61.5</p>
<p>1.滑动翻页的时候，控件按下事件和滑动事件两种操作屏幕自动二选一，不再同时执行。</p>
<p>2.系统变量bkcmd=3的时候，get,prints,printh指令只返回相应的变量/常量内容，不会再返回执行成功的回应，其他指令不变。</p>
<p>3.修复波形控件宽度小于50时显示出现的bug。</p>
<p>4.covx转换HEX数据的时候,手动输入转换字节数量为基数时将会报错。</p>
<p>5.资源文件ID无效的赋值操作不再执行。</p>
<p>1.61.3</p>
<p>修复动画控件被隐藏掉vid属性的bug。</p>
<p>1.61.2</p>
<p>修复T0系列工程下载BUG。</p>
<p>1.61.1</p>
<p>第一次发布。</p>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id23">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/1.60.zip">下载V1.60 USART HMI软件</a><a class="headerlink" href="#id23" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>1.视频控件增加播放SD卡视频功能(设置视频控件的from属性即可)</p>
<p>2.增加启动文件概念:可以在启动文件中定义任意数量的全局变量或写入各种初始化数据代码</p>
<p>3.所有字库增加指定字符功能</p>
<p>4.增加数据CRC校验功能,此功能涉及到以下五个对象：</p>
<p>系统变量:crcval crc校验结果（只可获取不可设置,使用前请先用crcrest指令复位初始值）</p>
<p>指令：crcrest 复位crc初始值(复位之后，可使用crcputs或crcputh或crcputu校验指定数据,检验完毕读取系统变量crcval获得校验结果)</p>
<p>指令：crcputs crc校验一个变量/常量</p>
<p>指令：crcputh crc校验一组Hex</p>
<p>指令：crcputu crc校验一段串口缓冲区数据(recmod=1时有效)</p>
<p>5.版本定义规则从原来的两位版本号更新为3位版本号,分别表示:主版本.子版本.修订号。如：1.60.8 【主版本1，子版本60，修订号8】</p>
<p>主版本变更：一般表示底层做了重大修改</p>
<p>子版本变更：一般代表有新增功能(如新增控件或指令)</p>
<p>修订号变更：一般代表bug修复,没有任何功能变化或底层改动</p>
<p>子版本修复记录：</p>
<p>1.60.2</p>
<p>修复单字节编码字库显示不全的bug</p>
<p>修复X系列跨页面设置坐标导致触摸出现问题的bug</p>
<p>1.60.1</p>
<p>第一次发布</p>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id24">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/0.59.zip">下载V0.59 USART HMI软件</a><a class="headerlink" href="#id24" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>1.增加T1系列支持</p>
<p>2.数据记录控件增加delete方法(用于删除一条或多条记录)</p>
<p>3.上位软件增加图片尺寸编辑</p>
<p>4.X系列提高图片压缩质量</p>
<p>5.编辑界面增加控件组合功能</p>
<p>6.工程下载界面增加下载波特率记忆功能</p>
<p>7.T1系列增加睡眠模式设定,支持普通睡眠和极低电流的深度睡眠( 目前仅T1系列支持深度睡眠，其他系列仅支持普通睡眠)</p>
<p>8.修复部分电脑兼容性问题导致退出软件时系统报错的情况</p>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id25">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/0.57.zip">下载V0.57 USART HMI软件</a><a class="headerlink" href="#id25" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>升级X3系列4.3寸屏驱动。</p>
<p>优化指令CRC校验速度。</p>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id26">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/0.56.zip">下载V0.56 USART HMI软件</a><a class="headerlink" href="#id26" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>优化X3,X5系列电阻触摸屏的触摸灵敏度。</p>
<p>曲线控件增加背景透明选项(仅X系列支持)。</p>
<p>菜单栏增加工程导入选项。</p>
<p>属性栏文本属性编辑增加多行输入功能。</p>
<p>增加指令CRC校验。点击此处查看说明文档。</p>
<p>SD卡图片播放支持(仅X3,X5系列)。</p>
<p>新增控件”外部图片”,用于显示内存中或SD卡中的图片(仅X3,X5系列)。</p>
<p>运行中串口传输图片文件到内存或SD卡(仅X3,X5系列)。,点击此处查看说明文档。</p>
<p>新增指令:(仅X3,X5系列支持)：</p>
<blockquote>
<div><p>wrfile      串口透传文件</p>
<p>delfile   删除文件</p>
<p>refile   重命名文件</p>
<p>findfile    查找文件</p>
<p>rdfile   透传读文件</p>
<p>点击此处查看说明文档</p>
</div></blockquote>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id27">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/0.55.zip">下载V0.55 USART HMI软件</a><a class="headerlink" href="#id27" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>1.全系列升级抗锯齿字库(包括字体抗锯齿，非等宽字符，字符占位延伸)</p>
<p>2.上位编辑界面鼠标拖拽改变控件尺寸增加6种方式(上拖高度，左拖宽度，左上角拖宽&amp;&amp;高，左下角拖宽&amp;&amp;高，右上角拖宽&amp;&amp;高,右下角拖宽&amp;&amp;高)</p>
<p>3.增加控件锁定功能(锁定后鼠标不能拖动控件位置)</p>
<p>4.软件启动界面的项目打开历史记录增加右键菜单(允许删除指定的记录或失效记录或全部历史记录)</p>
<p>5.增加虚拟浮点数控件</p>
<p>6.编辑界面鼠标选中控件增加防抖算法</p>
<p>7.修复新产品X系列的一些小BUG</p>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id28">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/0.54.zip">下载V0.54 USART HMI软件</a><a class="headerlink" href="#id28" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>1、if/while 语句增加 &amp;&amp; 和 || 支持啦!!!</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="k">if</span><span class="p">(</span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="o">==</span><span class="mi">3</span><span class="o">||</span><span class="n">n1</span><span class="p">.</span><span class="n">val</span><span class="o">==</span><span class="mi">4</span><span class="o">||</span><span class="n">n2</span><span class="p">.</span><span class="n">val</span><span class="o">==</span><span class="mi">5</span><span class="p">)</span><span class="w"></span>
<span class="linenos">2</span><span class="p">{</span><span class="w"></span>
<span class="linenos">3</span><span class="w">    </span><span class="k">if</span><span class="p">(</span><span class="n">t0</span><span class="p">.</span><span class="n">txt</span><span class="o">==</span><span class="s">&quot;abc&quot;</span><span class="o">&amp;&amp;</span><span class="n">t1</span><span class="p">.</span><span class="n">txt</span><span class="o">!==</span><span class="s">&quot;dd&quot;</span><span class="o">&amp;&amp;</span><span class="n">n3</span><span class="p">.</span><span class="n">val</span><span class="o">==</span><span class="mi">12</span><span class="p">)</span><span class="w"></span>
<span class="linenos">4</span><span class="w">    </span><span class="p">{</span><span class="w"></span>
<span class="linenos">5</span><span class="w">        </span><span class="n">page</span><span class="w"> </span><span class="mi">1</span><span class="w"></span>
<span class="linenos">6</span><span class="w">    </span><span class="p">}</span><span class="w"></span>
<span class="linenos">7</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<p>注意：if语句内部不支持括号，比如:if((t0.txt==”a”||t0.txt==”b”)&amp;&amp;t1.txt==”1”)这样是不支持的！</p>
<p>还有就是不要出现多余的空格。</p>
<p>2、串口波特率增加到912600</p>
<p>3、优化flash读数据效率</p>
<p>4、新增页面编辑区缩放功能</p>
<p>5、页面编辑区控件允许拖出显示区域外</p>
<p>6、串口下载资源文件时自动跳过相同资源文件</p>
<p>7、曲线控件支持全局属性，实现离开页面后曲线数据不丢失</p>
<p>【X系列智能串口屏 为动而生】</p>
<p>X3,X5 系列产品新增功能</p>
<p>1、运行中控件图层保持（每个控件是一个图层，运行中永远保持各自的层级关系）</p>
<p>2、图片压缩存储（压缩质量可设置）</p>
<p>3、带透明信息的png图片支持</p>
<p>4、多种页面切换特效</p>
<p>5、滑动翻页(上下左右4个方向滑动可分别设置不同目标页面)</p>
<p>6、控件加载特效支持(多种飞入方式和优先级)</p>
<p>7、运行中可通过指令随意修改控件坐标(允许负数坐标，允许超出显示区域的坐标)</p>
<p>8、增加move指令，指定起始坐标和结束坐标实现控件自动移动</p>
<p>9、增加drag属性实现运行中控件可以触摸拖动位置</p>
<p>10、所有控件支持半透明设置(128级，可运行中随意改变)</p>
<p>11、2路音频播放通道（2路通道可同时播放不同音乐）</p>
<p>12、每个页面可以最多有6个视频控件同时播放不同视频内容</p>
<p>13、每个页面最多可以有16个动画控件</p>
<p>14、增加状态开关控件</p>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id29">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/0.52.zip">下载V0.52 USART HMI软件</a><a class="headerlink" href="#id29" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>1、修复0.51版本新增功能的部分bug</p>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id30">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/0.51.zip">下载V0.51 USART HMI软件</a><a class="headerlink" href="#id30" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>1、增加二维码控件。</p>
<p>2、增加实时触摸坐标读取系统变量:tch0-tch3。</p>
<p>3、增加屏幕地址设置功能（操作方法请参看系统变量:addr）。</p>
<p>4、增加数值类型的变量赋值可以使用Hex的写法(比如dim=100,dim=0x64,两句表达的是同一个意思，前一句是十进制写法，</p>
<blockquote>
<div><p>后一句是十六进制写法)</p>
</div></blockquote>
<p>5、数字控件增加格式化类型属性:可以选择数字格式，货币格式，Hex格式，3种显示格式。</p>
<p>6、增加串口输出指令prints(用于替换原来的print指令，功能更强,可以指定发送变量的数据字节数，详情请参阅指令集)。</p>
<p>7、增加变量类型转换指令covx(用于替换原来的cov指令,功能更强,可以申明数字类型，详情请参阅指令集)。</p>
<p>8、增加全行业唯一支持的高级串口通讯功能-串口主动数据解析(此功能可以实现自定义指令协议与屏幕通讯，也可以实现跟PLC的通讯，</p>
<blockquote>
<div><p>可以实现跟任何特殊协议的串口设备通讯，具体请参看”HMI高级应用功能与特殊指令集”文档)。</p>
</div></blockquote>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id31">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/0.50.zip">下载V0.50 USART HMI软件</a><a class="headerlink" href="#id31" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>1、修复部分老产品出现”非法型号”的bug。</p>
<p>2、升级系统固件加密方式。</p>
<p>3、升级新款3.2寸屏幕驱动。</p>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id32">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/0.49.zip">下载V0.49 USART HMI软件</a><a class="headerlink" href="#id32" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>1、增加串口数据自动唤醒功能(详情请参看系统变量usup)。</p>
<p>2、增加工程文件设置打开密码功能（打开工程后，点击“设备”，在点击“工程”即可设置）。</p>
<p>3、上位软件模拟器增加掉电存储空间用户存储功能。</p>
<p>4、修复切图失败的时候导致的花屏问题。</p>
<p>5、上位软件模拟器界面增加16进制数据输入功能。</p>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id33">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/0.47.zip">下载V0.47 USART HMI软件</a><a class="headerlink" href="#id33" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>1、修复图片数量过多以后(200多张)无法浏览所有图片的bug。</p>
<p>2、修复软件联机信息中无法识别电容触摸的bug。</p>
<p>3、优化电容触摸灵敏度。</p>
<p>4、增加串口缓冲区溢出报错数据:0x24(详情请参看数据返回格式说明文档)</p>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id34">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/0.46.zip">下载V0.46 USART HMI软件</a><a class="headerlink" href="#id34" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>1、修复部分底层驱动bug</p>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id35">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/0.45.zip">下载V0.45 USART HMI软件</a><a class="headerlink" href="#id35" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>1、增加曲线控件数据添加时使用变量的功能。</p>
<p>2、增加繁体字库编码支持(即big5编码)。</p>
<p>3、增加字符串截取指令(即substr指令,详情请参看最新指令集)</p>
<p>4、上位编辑界面增加更多对齐按钮（使水平间距相同，使垂直间距相同，增加水平间距等…）。</p>
<p>5、修复文本控件最后一个字符为换行符(即:r)时显示异常的bug。</p>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id36">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/0.44.zip">下载V0.44 USART HMI软件</a><a class="headerlink" href="#id36" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>1、增加电容触摸驱动。</p>
<p>2、优化上位软件内存占用。</p>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id37">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/0.42.zip">下载V0.42 USART HMI软件</a><a class="headerlink" href="#id37" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>1、增加模拟调试界面的代码提示功能</p>
<p>2、增加控件颜色属性直接输入颜色值</p>
<p>3、增加属性编辑界面中颜色属性的颜色预览功能</p>
<p>4、增加系统内置虚拟键盘(文本控件和数字控件可以在属性区直接设置键盘绑定)</p>
<p>5、增加页面导入导出功能</p>
<p>6、增加页面锁定功能</p>
<p>7、修复电脑无网络时软件占用CPU资源过多的bug</p>
<p>8、修复颜色选择对话框不能保存自定义颜色的bug</p>
<p>9、修复数值为负数时强制转换出来的字符串中多出一个”0”的bug</p>
<p>10、修复软件重置窗体布局时出现的界面异常(工具条被拖到较远处时)</p>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id38">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/0.41.zip">下载V0.41 USART HMI软件</a><a class="headerlink" href="#id38" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>1、增加代码提示功能</p>
<p>2、软件设计界面窗口布局自动保存</p>
<p>3、增加系统变量:dp(表示当前页面ID)</p>
<p>4、修复工程属性总数超过65535时出现的bug</p>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id39">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/0.40.zip">下载V0.40 USART HMI软件</a><a class="headerlink" href="#id39" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>1、修复页面初始化代码中出现delay时的黑屏bug</p>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id40">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/0.39.zip">下载V0.39 USART HMI软件</a><a class="headerlink" href="#id40" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>1、增加文本控件显示密码功能(即显示为*)</p>
<p>2、增加文本控件的风格选择（3D，平面，带边框）</p>
<p>3、增加按钮控件的风格选择（3D，平面，带边框）</p>
<p>4、增加数字控件的风格选择（3D，平面，带边框）</p>
<p>5、增加休眠唤醒后刷新页面的设置(请参看系统变量wup含义：wup=255(上电默认)：唤醒后刷新休眠前的页面,</p>
<blockquote>
<div><p>wup=0-254：唤醒后刷新指定页面(wup的值为页面序号)</p>
</div></blockquote>
<p>6、休眠状态下可以执行如下指令：get,print,printh 除以上三条指令外，休眠状态下还可以执行sleep=1，wup=XXX的赋值语句，</p>
<blockquote>
<div><p>并且支持上位软件联机</p>
</div></blockquote>
<p>7、从此版本开始，在运行中修改控件的任何属性都将自动刷新，不再需要使用手动刷新指令。(原来的版本在修改非加粗属性时需要手动刷新)</p>
<p>8、优化指令解析速度，解析速度提升20%</p>
<p>9、增加代码编辑区指令和系统变量的高亮显示</p>
<p>10、修复文本控件背景色改变时偶尔出现未知色块的bug</p>
<p>11、修复控件触摸禁用后依然会响应发送键值的bug(弹起发送键值)</p>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id41">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/0.37.zip">下载V0.37 USART HMI软件</a><a class="headerlink" href="#id41" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>1、双态按钮增加文本显示</p>
<p>2、增加控件事件激活指令(click)</p>
<p>3、备份老版本文件时文件名中增加当前版本号</p>
<p>4、优化控件隐藏/显示性能</p>
<p>5、优化字符显示速度</p>
<p>6、修复picq指令异常的bug</p>
<p>7、修复显示方向为270度时文本控件背景切图的bug</p>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id42">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/0.36.zip">下载V0.36 USART HMI软件</a><a class="headerlink" href="#id42" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>1、优化USART HMI上位软件图片显示速度和页面刷新速度（上位模拟图片及页面刷新速度提升50倍）</p>
<p>2、增加滚动文本控件</p>
<p>3、数字控件和变量控件增加负数支持</p>
<p>4、增加字符串变量的字符数检测指令strlen</p>
<p>5、增加xpic指令(高级切图)</p>
<p>6、文本控件增加是否需要自动换行的设置</p>
<p>7、修复增强型系列不能触摸自动唤醒的bug</p>
<p>8、修复部分屏幕按键在触摸边缘时触摸卡死的bug</p>
<p>9、修复老版本在电源出问题时偶尔可能出现的型号非法的bug</p>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id43">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/0.35.zip">下载V0.35 USART HMI软件</a><a class="headerlink" href="#id43" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>1、增加增强型大屏驱动(3.5寸及以上尺寸)</p>
<p>2、优化3.2寸屏的显示色彩</p>
<p>3、增加RTC的”星期”系统变量:rtc6</p>
<p>4、修复内存使用过多的时候大字体出现乱码的bug</p>
<p>5、修复上位模拟器运行不支持控件暂停刷新功能的bug</p>
<p>6、ref_stop指令修改了用法，执行ref_stop后，ref指令也不会立刻刷新，直到执行ref_star指令的时候统一刷新</p>
</div></blockquote>
<hr class="docutils" />
</section>
<section id="id44">
<h2><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/oldapp/0.34.zip">下载V0.34 USART HMI软件</a><a class="headerlink" href="#id44" title="此标题的永久链接"></a></h2>
<blockquote>
<div><p>本次升级内容:</p>
<p>1、优化用户代码区的代码复制粘贴操作</p>
<p>2、修复初始化事件中休眠后不能退出的bug</p>
<p>3、修复图片不能全删的bug</p>
<p>4、修复滑块不能设置hig属性的bug</p>
<p>5、修复弹起/按下发送键值动作被交换的bug</p>
<p>6、串口下载速度提示更精确</p>
<p>7、修复else内部嵌入if时的冲突bug</p>
<p>8、调试页面增加校准设备RTC功能</p>
<p>9、模拟器支持虚拟RTC设置，并且不影响电脑系统时间</p>
<p>10、修复增强型系列用户存储数据透传的bug</p>
<p>11、优化资源文件合法性检测以避开电源有问题时出现的data error提示</p>
</div></blockquote>
<hr class="docutils" />
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="../usart_hmi.html" class="btn btn-neutral float-left" title="上位机下载" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="../development_doc.html" class="btn btn-neutral float-right" title="开发文档下载" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>