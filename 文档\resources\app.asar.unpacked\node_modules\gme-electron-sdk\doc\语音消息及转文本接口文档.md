﻿为方便 Electron 开发者调试和接入腾讯云游戏多媒体引擎客户端 API，本文为您介绍适用于 Electron 语音消息服务及转文本服务的接入技术文档。

## 使用 GME 重要事项

GME 提供实时语音服务、语音消息服务及转文本服务，使用 GME 服务都依赖 Init 和 Poll 等核心接口。

#### 重点提示

- 已完成 GME 应用创建，并获取 SDK AppID 和 Key。请参见 [服务开通指引]( https://cloud.tencent.com/document/product/607/10782)。
- 已开通 **GME 实时语音服务、语音消息服务以及转文本服务**。请参见 [服务开通指引](https://cloud.tencent.com/document/product/607/10782)。
- GME 使用前请对工程进行配置，否则 SDK 不生效。
- GME 的接口调用成功后返回值为 GmeError.AV_OK，数值为 0。
- GME 的接口调用要在同一个线程下。
- GME 需要周期性的调用 Poll 接口触发事件回调。
- 错误码详情可参见 <dx-tag-link link="https://cloud.tencent.com/document/product/607/15173" tag="ErrorCode">错误码</dx-tag-link>。

<dx-alert infotype="notice" title="">
语音转文本相关接口有默认频率限制，限额范围内计费方式请参见 [计费文档](https://cloud.tencent.com/document/product/607/17808#.E8.AF.AD.E9.9F.B3.E8.BD.AC.E6.96.87.E6.9C.AC.E6.9C.8D.E5.8A.A1)；若需提升接口频率限额或了解超额计费方式，请联系商务或 [提交工单咨询](https://console.cloud.tencent.com/workorder/category?level1_id=438&level2_id=445&source=0&data_title=%E6%B8%B8%E6%88%8F%E5%A4%9A%E5%AA%92%E4%BD%93%E5%BC%95%E6%93%8EGME&step=1)。
- 语音消息非流式转文本接口 ***PttSpeechToText()*** ：默认单账号限制并发数为10路
- 语音消息流式转文本接口 ***PttStartRecordingWithStreamingRecognition()***：默认单账号限制并发数为50路
</dx-alert>

## 接入 SDK

### 重要步骤

接入 SDK 重要流程如下：

<img src="https://qcloudimg.tencent-cloud.cn/raw/5e05e6ffe1749725a6ba077926286f16.png"  width="70%" /></img>

<dx-steps>
-<dx-tag-link link="#Init" tag="接口：Init">初始化 GME</dx-tag-link>
-<dx-tag-link link="#Poll" tag="接口：Poll">周期性调用 Poll 触发回调</dx-tag-link>
-<dx-tag-link link="#ApplyPtt" tag="接口：ApplyPTTAuthbuffer">鉴权初始化</dx-tag-link>
-<dx-tag-link link="#StartRWSR" tag="接口：PttStartRecordingWithStreamingRecognition">启动流式语音识别</dx-tag-link>
-<dx-tag-link link="#Stop" tag="接口：PttStopRecording">停止录制</dx-tag-link>
-<dx-tag-link link="#UnInit" tag="接口：UnInit">反初始化 GME</dx-tag-link>
</dx-steps>

### ts 类

```
GmeContext Gme业务实现接口
GmeError Gme 错误码定义类
```

## 核心接口

| 接口   |   接口含义   |
| ------ | :----------: |
| Init   |  初始化 GME  |
| Poll   | 触发事件回调 |
| Uninit | 反初始化 GME |

### 导入Gme模块

```
const { GmeContext } = require('gme-electron-sdk');
```

### 获取实例

var m_context = new GmeContext();

[](id:Init)
### 初始化 SDK

未初始化前，SDK 处于未初始化阶段，**需要通过接口 Init 初始化 SDK**，才可以使用实时语音服务、语音消息服务及转文本服务。调用 Init 接口的线程必须于其他接口在同一线程,建议都在主线程调用接口。

#### 接口原型

```
Init(appid: string, openid: string): number;
```

| 参数     |  类型  | 含义                                                         |
| -------- | :----: | ------------------------------------------------------------ |
| sdkAppId | string | 来自 [腾讯云控制台](https://console.cloud.tencent.com/gamegme) 的 GME 服务提供的 AppID，获取请参见 [语音服务开通指引](https://cloud.tencent.com/document/product/607/10782#.E9.87.8D.E7.82.B9.E5.8F.82.E6.95.B0)。 |
| openID   | string | openID 只支持 Int64 类型（转为 string 传入），规则由 App 开发者自行制定，App 内不重复即可。如需使用字符串作为 Openid 传入，可 [提交工单](https://console.cloud.tencent.com/workorder/category?level1_id=438&level2_id=445&source=0&data_title=%E6%B8%B8%E6%88%8F%E5%A4%9A%E5%AA%92%E4%BD%93%E5%BC%95%E6%93%8EGME&step=1) 联系开发者。|

#### 返回值

| 返回值                          | 处理                                          |
| ------------------------------- | --------------------------------------------- |
| GmeError.AV_OK= 0                  | 初始化 SDK 成功                               |
| AV_ERR_SDK_NOT_FULL_UPDATE=7015 | 检查 SDK 文件是否完整，建议删除后重新导入 SDK |

<dx-alert infotype="notice" title="关于7015错误提示">

- 7015错误码是通过 md5 进行判断，在接入过程中若出现此错误，请根据提示检查 SDK 文件是否完整、SDK 文件版本是否一致。
- 出现返回值 AV_ERR_SDK_NOT_FULL_UPDATE 时，此返回值**只有提示作用**，并不会造成初始化失败。
  </dx-alert>

#### 示例代码 

```
number ret = m_context.Init(sdkAppId, openID);
//通过返回值判断是否初始化成功
if (ret != GmeError.AV_OK)
{
	 console.log("SDK初始化失败:");
		return;
}
```

[](id:Poll)
### 触发事件回调

通过在定时器调用 Poll 可以触发事件回调。Poll 是 GME 的消息泵，GME 需要周期性的调用 Poll 接口触发事件回调。如果没有调用 Poll ，将会导致整个 SDK 服务运行异常。详情请参见 [Sample Project](https://cloud.tencent.com/document/product/607/18521)  中的 EnginePollHelper 文件。

<dx-alert infotype="notice" title="">
务必周期性调用 Poll 接口且在主线程调用，以免接口回调异常。
</dx-alert>

#### 接口原型

```
Poll();
```

#### 示例代码

```
setInterval(function () {
      m_context.Poll();
    }, 50);
```

[](id:UnInit)
### 反初始化 SDK

反初始化 SDK，进入未初始化状态。**如果游戏业务侧账号与 openid 是绑定的，那切换游戏账号需要反初始化 GME，再用新的 openid 初始化**。

#### 接口原型

```
Uninit() : number
```

## 语音消息服务及转文本服务

<dx-alert infotype="explain" title="">

- 转文本服务分录音文件极速转文本以及语音消息流式转文本。
- 使用语音消息服务不需要进入实时语音房间。
- 语音消息最大录制时长默认为58秒，最短不能小于1秒。如果需要再加以限制，例如限制为最大录制时长为10秒，请在初始化之后调用 SetMaxMessageLength 接口进行设置。
  </dx-alert>

### 语音消息服务使用流程

<img src="https://qcloudimg.tencent-cloud.cn/raw/481e7e65e5c35510b64176716b095ac8.jpg" width="80%">

### 转文本服务使用流程

<img src="https://qcloudimg.tencent-cloud.cn/raw/746ef979b0f606588c010a6d4c7453f0.jpg" width="80%">

| 接口                |       接口含义       |
| ------------------- | :------------------: |
|GenAuthBuffer|获取鉴权信息
| SetMaxMessageLength | 限制最大语音信息时长 |




### 生成本地鉴权

生成 AuthBuffer，用于相关功能的加密和鉴权，如正式发布请使用后台部署密钥，后台部署请参见 [鉴权密钥](https://cloud.tencent.com/document/product/607/12218)。    

#### 接口原型

```
GenAuthBuffer(appId: string,roomId: string, openId:string, appKey: string) :string
```

| 参数   |  类型  | 含义                                                         |
| ------ | :----: | ------------------------------------------------------------ |
| appId  |  string   | 来自腾讯云控制台的 AppId 号码。                              |
| roomId | string | 填 null 或者空字符串。                                      |
| openId | string | 用户标识。与 Init 时候的 OpenId 相同。                        |
| key    | string | 来自腾讯云 [控制台](https://console.cloud.tencent.com/gamegme) 的权限密钥。 |

### 应用鉴权

生成鉴权信息后，将鉴权赋值到 SDK 中。  

#### 接口原型  

```
ApplyPTTAuthbuffer(authBuffer: string) :number
```

| 参数   | 类型 | 含义                                            |
| ------ | :--: | ----------------------------------------------- |
| authBuffer | string  | 鉴权 |

#### 示例代码

```
var authBuffer = m_context.GetAuthBuffer(UserConfig.GetAppID(), UserConfig.GetUserID(), null,UserConfig.GetAuthKey());
m_context.ApplyPTTAuthbuffer(authBuffer);
```

### 限制最大语音信息时长

限制最大语音消息的长度，最大支持58秒。

#### 接口原型

```
PttSetMaxMessageLength(msTime: number) :number
```

| 参数   | 类型 | 含义                                            |
| ------ | :--: | ----------------------------------------------- |
| msTime | number  | 语音时长，单位 ms，区间为 1000 < msTime < = 58000 |

#### 示例代码  

```
m_context.PttSetMaxMessageLength(58000); 
```

## 流式语音识别



### 语音消息及转文字相关接口

| 接口                                   |   接口含义   |
| -------------------------------------- | :----------: |
| PttStartRecordingWithStreamingRecognition | 启动流式录音 |
| PttStopRecording                          |   停止录音   |


[](id:StartRWSR)
### 启动流式语音识别

此接口用于启动流式语音识别，同时在回调中会有实时的语音转文字返回，可以指定语言进行识别，也可以将语音中识别到的信息翻译成指定的语言返回。**停止录音调用 <dx-tag-link link="#Stop" tag="接口：PttStopRecording">停止录制</dx-tag-link>**。

#### 接口原型  

```
PttStartRecordingWithStreamingRecognition(filePath: string, speechLanguage: string, translateLanguage: string) :number
```

| 参数              |  类型  | 含义                                                         |
| ----------------- | :----: | ------------------------------------------------------------ |
| filePath          | string | 存放的语音路径                                               |
| speechLanguage    | string | 识别成指定文字的语言参数，参数请参见 [语言参数参考列表](https://cloud.tencent.com/document/product/607/30282) |
| translateLanguage | string | 翻译成指定文字的语言参数，参数请参见 [语言参数参考列表](https://cloud.tencent.com/document/product/607/30282) |

#### 示例代码  

```
string filePath = "xx/xxx/xxx.silk"
var ret = m_context.StartRecordingWithStreamingRecognition(filePath,"cmn-Hans-CN","cmn-Hans-CN");
if (ret == 0) {
    this.currentStatus = "开始流式录音";
} else {
    this.currentStatus = "开始流式录音失败";
}
```

> ! 翻译会收取额外费用，请参见 [购买指南](https://cloud.tencent.com/document/product/607/17808)。
### 流式语音识别的回调

启动流式语音识别后，需要在 OnEvent 通知中监听回调消息，事件消息分为以下两个：

ITMG_MAIN_EVNET_TYPE_PTT_STREAMINGRECOGNITION_COMPLETE 是在停止录制并完成识别后才返回文字，相当于一段话说完才会返回识别的文字。
ITMG_MAIN_EVNET_TYPE_PTT_STREAMINGRECOGNITION_IS_RUNNING 是在录音过程中就会实时返回识别到的文字，相当于边说话边返回识别到的文字。

根据需求在 回调 通知中对相应事件消息进行判断。传递的参数包含以下四个信息。

| 消息名称  |                      含义                       |
| --------- | :---------------------------------------------: |
| result    |      用于判断流式语音识别是否成功的返回码       |
| text      |              语音转文字识别的文本               |
| file_path |               录音存放的本地地址                |
| file_id   | 录音在后台的 url 地址，录音在服务器存放90天 |



<dx-alert infotype="notice" title="">
监听 `ITMG_MAIN_EVNET_TYPE_PTT_STREAMINGRECOGNITION_IS_RUNNING` 消息时，file_id 为空。
</dx-alert>

#### 错误码

| 错误码 | 含义 | 处理方式 |
| ------ | :--: | :------: |
|32775	|流式语音转文本失败，但是录音成功	|调用 UploadRecordedFile 接口上传录音，再调用 SpeechToText 接口进行语音转文字操作
|32777	|流式语音转文本失败，但是录音成功，上传成功	|返回的信息中有上传成功的后台 url 地址，调用 SpeechToText 接口进行语音转文字操作
|32786  |流式语音转文本失败|在流式录制状态当中，请等待流式录制接口执行结果返回|
| 32787     | 转文本成功，文本翻译服务未开通         | 需要在控制台开通文本翻译服务                                 |
| 32788     | 转文本成功，文本翻译语言参数不支持         | 重新检查传入参数                                 |

如果出现 4098 错误码，请参见 [常见问题文档](https://cloud.tencent.com/document/product/607/51466#.E5.BC.80.E5.A7.8B.E5.BD.95.E9.9F.B3.E4.B9.8B.E5.90.8E.EF.BC.8C.E6.98.BE.E7.A4.BA-4098-.E7.9A.84.E9.94.99.E8.AF.AF.E7.A0.81.EF.BC.8C.E5.BA.94.E8.AF.A5.E6.80.8E.E4.B9.88.E8.A7.A3.E5.86.B3.EF.BC.9F) 进行解决。

#### 示例代码  

```
m_context.setTMGDelegate(function(eventId, msg){
   switch (eventType) {
                    case ITMG_MAIN_EVNET_TYPE_PTT_STREAMINGRECOGNITION_COMPLETE:
                    {
                        HandleSTREAM2TEXTComplete(data,true);
                        break;
                        }
                    ...
                            case ITMG_MAIN_EVNET_TYPE_PTT_STREAMINGRECOGNITION_IS_RUNNING:
                    {
                        HandleSTREAM2TEXTComplete(data, false);
                        break;
                    }
                }
});

```

## 语音消息录制

**录制的流程为：录音 > 停止录音 > 录音回调返回 > 启动下一次录音。**

### 语音消息及转文字相关接口

| 接口            | 接口含义 |
| --------------- | :------: |
| PttStartRecording  | 启动录音 |
| PttPauseRecording  | 暂停录音 |
| PttResumeRecording | 恢复录音 |
| PttStopRecording   | 停止录音 |
| PttCancelRecording | 取消录音 |



### 启动录音

此接口用于启动录音。

#### 接口原型  

```
PttStartRecording(filePath: string) : number;
```

| 参数    |  类型  | 含义           |
| ------- | :----: | -------------- |
| filePath | string | 存放的语音路径 |

#### 示例代码  

```
string filepath = "xxxx/xxx.silk";
var ret = m_context.PttStartRecording(filepath);
```

[](id:Stop)
### 停止录音

此接口用于停止录音。此接口为异步接口，停止录音后会有录音完成回调，成功之后录音文件才可用。

#### 接口原型  

```
PttStopRecording() :number;
```

#### 示例代码  

```
m_context.PttStopRecording();
```

### 启动录音的回调

录音完成的回调，通过委托传递消息。

**停止录音调用StopRecording**。停止录音后才有启动录音的回调。


| 参数     |  类型  | 含义                                                         |
| -------- | :----: | ------------------------------------------------------------ |
| code     | string | 当 code 为 0 时，录制完成                                    |
| filepath | string | 录制的存放地址，必须是可以访问到的路径，不可将 fileid 作为路径 |

#### 错误码

| 错误码值 | 原因                     | 建议方案                                                     |
| -------- | ------------------------ | ------------------------------------------------------------ |
| 4097     | 参数为空                 | 检查代码中接口参数是否正确                                   |
| 4098     | 初始化错误               | 检查设备是否被占用，或者权限是否正常，是否初始化正常         |
| 4099     | 正在录制中               | 确保在正确的时机使用 SDK 录制功能                            |
| 4100     | 没有采集到音频数据       | 检查麦克风设备是否正常                                       |
| 4101     | 录音时，录制文件访问错误 | 确保文件存在，文件路径的合法性                               |
| 4102     | 麦克风未授权错误         | 使用 SDK 需要麦克风权限，添加权限请参考对应引擎或平台的 SDK 工程配置文档 |
| 4103     | 录音时间太短错误         | 首先，限制录音时长的单位为毫秒，检查参数是否正确；其次，录音时长要1000毫秒以上才能成功录制 |
| 4104     | 没有启动录音操作         | 检查是否已经调用启动录音接口                                 |

#### 示例代码  

```
m_context.setTMGDelegate(function(eventId, msg){
   switch (eventType) {
                    case ITMG_MAIN_EVENT_TYPE_ENTER_ROOM:
                    {
                    //进行处理
                    break;
                        }
                    ...
                            case ITMG_MAIN_EVNET_TYPE_PTT_RECORD_COMPLETE:
                    {
                    //进行处理
                    break;
                    }
                }
});


```

### 暂停录音

此接口用于暂停录音。如需恢复录音请调用接口 PttResumeRecording。

#### 接口原型  

```
PttPauseRecording() : number
```

#### 示例代码  

```
number ret = m_context.PttPauseRecording();
```

### 恢复录音

此接口用于恢复录音。

#### 接口原型  

```
PttResumeRecording() : number;
```

#### 示例代码  

```
number ret = m_context.PttResumeRecording();
```



### 取消录音

调用此接口取消录音。**取消之后没有回调**。

#### 接口原型  

```
PttCancelRecording() : number
```

#### 示例代码  

```
m_context.PttCancelRecording();
```


## 语音消息上传、下载及播放

| 接口                 |    接口含义    |
| -------------------- | :------------: |
| PttUploadRecordedFile   |  上传语音文件  |
| PttDownloadRecordedFile |  下载语音文件  |
| PttPlayRecordedFile     |    播放语音    |
| PttStopPlayFile         |  停止播放语音  |
|  PttGetFileSize          | 语音文件的大小 |
|  PttGetVoiceFileDuration | 语音文件的时长 |

### 上传语音文件

此接口用于上传语音文件。

#### 接口原型  

```
PttUploadRecordedFile(filePath: string) : number
```

| 参数     |  类型  | 含义                             |
| -------- | :----: | -------------------------------- |
| filePath | String | 上传的语音路径，此路径为本地路径 |

#### 示例代码  

```
var ret = m_context.PttUploadRecordedFile(filePath);
```

### 上传语音完成的回调

上传语音完成后，事件消息为 ITMG_MAIN_EVNET_TYPE_PTT_UPLOAD_COMPLETE， 在 OnEvent 函数中对事件消息进行判断。
传递的参数包含三个信息，result，file_path 和 file_id。

| 参数              |  类型  | 含义                                                         |
| ----------------- | :----: | ------------------------------------------------------------ |
| result          | number | 当 code 为0时，录制完成                                            |
| filepath          | string | 录制的存放地址                                            |
| fileid          | string | 文件的 url 路径                                      |

#### 错误码

| 错误码值 | 原因                           | 建议方案                                               |
| -------- | ------------------------------ | ------------------------------------------------------ |
| 8193     | 上传文件时，文件访问错误       | 确保文件存在，文件路径的合法性                         |
| 8194     | 签名校验失败错误               | 检查鉴权密钥是否正确，检查是否有初始化离线语音         |
| 8195     | 网络错误                       | 检查设备网络是否可以正常访问外网环境                   |
| 8196     | 获取上传参数过程中网络失败     | 检查鉴权是否正确，检查设备网络是否可以正常访问外网环境 |
| 8197     | 获取上传参数过程中回包数据为空 | 检查鉴权是否正确，检查设备网络是否可以正常访问外网环境 |
| 8198     | 获取上传参数过程中回包解包失败 | 检查鉴权是否正确，检查设备网络是否可以正常访问外网环境 |
| 8200     | 没有设置 appinfo               | 检查 apply 接口是否有调用，或者入参是否为空            |

#### 示例代码

```
m_context.setTMGDelegate(function(eventId, msg){
 switch (eventType) {
            case ITMG_MAIN_EVENT_TYPE_ENTER_ROOM:
            {
            //进行处理
            break;
                }
            ...
                    case ITMG_MAIN_EVNET_TYPE_PTT_UPLOAD_COMPLETE:
            {
            //进行处理
            break;
            }
        }
});
```

### 下载语音文件

此接口用于下载语音文件。

#### 接口原型  

```
PttDownloadRecordedFile(fileId: string, filePath: string) : number
```

| 参数             |  类型  | 含义                                                         |
| ---------------- | :----: | ------------------------------------------------------------ |
| fileId           | string | 文件的 url 路径                                              |
| filePath | string | 文件的本地保存路径，必须是可以访问到的路径，不可将 fileid 作为路径 |

#### 示例代码  

```
var ret = m_context.PttDownloadRecordedFile(fileID,filePath);
```

### 下载语音文件完成回调

下载语音完成后，事件消息为 ITMG_MAIN_EVNET_TYPE_PTT_DOWNLOAD_COMPLETE， 在 OnEvent 函数中对事件消息进行判断。
传递的参数包含三个信息，result、file_path 和 file_id。

| 参数              |  类型  | 含义                                                         |
| ----------------- | :----: | ------------------------------------------------------------ |
| result          | number | 当 code 为0时，下载完成                                            |
| filepath          | string | 录制的存放地址                                            |
| fileid          | string | 文件的 url 路径，录音在服务器存放 90 天                                      |

#### 错误码

| 错误码值 | 原因                              | 建议方案                                                     |
| -------- | --------------------------------- | ------------------------------------------------------------ |
| 12289    | 下载文件时，文件访问错误          | 检查文件路径是否合法                                         |
| 12290    | 签名校验失败                      | 检查鉴权密钥是否正确，检查是否有初始化离线语音               |
| 12291    | 网络存储系统异常                  | 服务器获取语音文件失败，检查接口参数 fileid 是否正确，检查网络是否正常，检查 COS 文件存不存在 |
| 12292    | 服务器文件系统错误                | 检查设备网络是否可以正常访问外网环境，检查服务器上是否有此文件 |
| 12293    | 获取下载参数过程中，HTTP 网络失败 | 检查设备网络是否可以正常访问外网环境                         |
| 12294    | 获取下载参数过程中，回包数据为空  | 检查设备网络是否可以正常访问外网环境                         |
| 12295    | 获取下载参数过程中，回包解包失败  | 检查设备网络是否可以正常访问外网环境                         |
| 12297    | 没有设置 appinfo                  | 检查鉴权密钥是否正确，检查是否有初始化离线语音               |

#### 示例代码

```
m_context.setTMGDelegate(function(eventId, msg){
 switch (eventType) {
           case ITMG_MAIN_EVENT_TYPE_ENTER_ROOM:
            {
            //进行处理
            break;
             }
        }
});
```

### 播放语音

此接口用于播放语音。

#### 接口原型  

```
PttPlayRecordedFile(filePath: string, voiceType: ITMG_VOICE_TYPE) : number
```

| 参数      |  类型  | 含义                                                         |
| --------- | :----: | ------------------------------------------------------------ |
| filePath  | string | 本地语音文件的路径                                           |
| voicetype |  ITMG_VOICE_TYPE | 变声类型，请参见 [变声接入文档](https://cloud.tencent.com/document/product/607/67312#.E8.AF.AD.E9.9F.B3.E6.B6.88.E6.81.AF.E5.8F.98.E5.A3.B0) |

#### 错误码

| 错误码值 | 原因       | 建议方案                       |
| -------- | ---------- | ------------------------------ |
| 20485    | 播放未开始 | 确保文件存在，文件路径的合法性 |

#### 示例代码  

```
m_context.PlayRecordedFile(filePath); 
```

### 播放语音的回调

播放语音的回调，事件消息为 ITMG_MAIN_EVNET_TYPE_PTT_PLAY_COMPLETE， 在 OnEvent 函数中对事件消息进行判断。
传递的参数包含两个信息，一个是 result，另一个是 file_path。

| 参数              |  类型  | 含义                                                         |
| ----------------- | :----: | ------------------------------------------------------------ |
| code          | number | 当 code 为0时，播放完成                                            |
| filepath          | string | 录制的存放地址                                            |

#### 错误码

| 错误码值 | 原因                                                       | 建议方案                                                     |
| -------- | ---------------------------------------------------------- | ------------------------------------------------------------ |
| 20481    | 初始化错误                                                 | 检查设备是否被占用，或者权限是否正常，是否初始化正常         |
| 20482    | 正在播放中，试图打断并播放下一个失败了（正常是可以打断的） | 检查代码逻辑是否正确                                         |
| 20483    | 参数为空                                                   | 检查代码中接口参数是否正确                                   |
| 20484    | 内部错误                                                   | 初始化播放器错误，解码失败等问题产生此错误码，需要结合日志定位问题 |

#### 示例代码

```
m_context.setTMGDelegate(function(eventId, msg){
 switch (eventType) {
		case ITMG_MAIN_EVNET_TYPE_PTT_PLAY_COMPLETE:
		{
		//进行处理
		break;
		 }
		}
});
```



### 停止播放语音

此接口用于停止播放语音。停止播放语音也会有播放完成的回调。

#### 接口原型  

```
PttStopPlayFile() : number
```

#### 示例代码  

```
m_context.PttStopPlayFile();
```


### 获取语音文件的大小

通过此接口，获取语音文件的大小。

#### 接口原型  

```
PttGetFileSize(filePath: string) : number
```

| 参数     |  类型  | 含义                             |
| -------- | :----: | -------------------------------- |
| filePath | string | 语音文件的路径，此路径为本地路径 |

#### 示例代码  

```
m_context.PttGetFileSize(filePath);
```

### 获取语音文件的时长

此接口用于获取语音文件的时长，单位毫秒。

#### 接口原型  

```
PttGetVoiceFileDuration(filePath: string) : number
```

| 参数     |  类型  | 含义                             |
| -------- | :----: | -------------------------------- |
| filePath | string | 语音文件的路径，此路径为本地路径 |

#### 示例代码  

```
number fileDuration = m_context.PttGetVoiceFileDuration(filePath);
```


## 录音文件极速转文

### 将指定的语音文件翻译成文字（指定语言）

此接口可以指定语言进行识别，也可以将语音中识别到的信息翻译成指定的语言返回。

>!翻译会收取额外费用，请参见 [购买指南](https://cloud.tencent.com/document/product/607/17808)。
#### 接口原型  

```
PttSpeechToText(fileID: string, speechLanguage: string, translateLanguage: string) : number
```

| 参数              |  类型  | 含义                                                         |
| ----------------- | :----: | ------------------------------------------------------------ |
| fileID            | string | 语音文件 url，录音在服务器存放90天                           |
| speechLanguage    | string | 识别出指定文字的语言参数，参数参见 [语言参数参考列表](https://cloud.tencent.com/document/product/607/30282) |
| translatelanguage | string | 翻译成指定文字的语言参数，参数参见 [语言参数参考列表](https://cloud.tencent.com/document/product/607/30282) 中的翻译语言参数 |

#### 示例代码  

```
m_context.PttSpeechToText(filePath,"cmn-Hans-CN","cmn-Hans-CN");
```



### 识别回调

将指定的语音文件识别成文字的回调，事件消息为 ITMG_MAIN_EVNET_TYPE_PTT_SPEECH2TEXT_COMPLETE， 在 OnEvent 函数中对事件消息进行判断。
传递的参数包含三个信息，result、file_path 和 text，其中 text 为识别的文本。


| 参数              |  类型  | 含义                                                         |
| ----------------- | :----: | ------------------------------------------------------------ |
| result          | number | 当 code 为0时，录制完成                                         |
| fileid          | string | 语音文件 url，录音在服务器存放90天                                           |
| text          | string | 转换的文本结果                                          |

#### 错误码

| 错误码值 | 原因                               | 建议方案                                                     |
| -------- | ---------------------------------- | ------------------------------------------------------------ |
| 32769    | 内部错误                           | 分析日志，获取后台返回给客户端的真正错误码，并联系后台同事协助解决 |
| 32770    | 网络失败                           | 检查设备网络是否可以正常访问外网环境                         |
| 32772    | 回包解包失败                       | 分析日志，获取后台返回给客户端的真正错误码，并联系后台同事协助解决 |
| 32774    | 没有设置 appinfo                   | 检查鉴权密钥是否正确，检查是否有初始化离线语音               |
| 32776    | authbuffer 校验失败                | 检查 authbuffer 是否正确                                     |
| 32784    | 语音转文本参数错误                 | 检查代码中接口参数 fileid 是否为空                           |
| 32785    | 语音转文本翻译返回错误             | 离线语音后台错误，请分析日志，获取后台返回给客户端的真正错误码，并联系后台同事协助解决 |
| 32787    | 转文本成功，文本翻译服务未开通     | 需要在控制台开通文本翻译服务                                 |
| 32788    | 转文本成功，文本翻译语言参数不支持 | 重新检查传入参数                                             |

#### 示例代码

```
m_context.setTMGDelegate(function(eventId, msg){
 switch (eventType) {
         case ITMG_MAIN_EVENT_TYPE_ENTER_ROOM:
            {
            //进行处理
            break;
                }
            ...
                    case ITMG_MAIN_EVNET_TYPE_PTT_SPEECH2TEXT_COMPLETE:
            {
            //进行处理
            break;
            }
});
```

## 语音消息音量相关接口

| 接口             |      接口含义      |
| ---------------- | :----------------: |
| PttGetMicLevel      | 获取实时麦克风音量 |
| PttSetMicVolume     |    设置录制音量    |
| PttGetMicVolume     |    获取录制音量    |
| PttGetSpeakerLevel  | 获取实时扬声器音量 |
| PttSetSpeakerVolume |    设置播放音量    |
| PttGetSpeakerVolume |    获取播放音量    |

### 获取语音消息麦克风实时音量

此接口用于获取麦克风实时音量，返回值为 number 类型，值域为0 - 200。

#### 接口原型  

```
PttGetMicLevel():number
```

#### 示例代码  

```
m_context.PttGetMicLevel();
```

### 设置语音消息录制音量

此接口用于设置离线语音录制音量，值域为0 - 200。

#### 接口原型  

```
PttSetMicVolume(vol:number) :number
```

| 参数     |  类型  | 含义                                                         |
| -------- | :----: | ------------------------------------------------------------ |
| vol | number | 取值范围为 0-200，数值为0的时候表示静音，当数值为100的时候表示音量不增不减，默认数值为100。 |

#### 示例代码  

```
m_context.PttSetMicVolume(vol);
```

### 获取语音消息录制音量

此接口用于获取离线语音录制音量。返回值为 number 类型，值域为0 - 200。

#### 接口原型  

```
PttGetMicVolume() : number
```

#### 示例代码  

```
m_context.PttGetMicVolume();
```

### 获取语音消息扬声器实时音量

此接口用于获取扬声器实时音量。返回值为 number 类型，值域为0 - 200。

#### 接口原型  

```
PttGetSpeakerLevel() : number;
```

#### 示例代码  

```
m_context.PttGetSpeakerLevel();
```

### 设置语音消息播放音量

此接口用于设置离线语音播放音量，值域为0 - 200。

#### 接口原型  

```
PttSetSpeakerVolume(vol: number) : number
```

#### 示例代码  

```
m_context.PttSetSpeakerVolume(100);
```

### 获取语音消息播放音量

此接口用于获取离线语音播放音量。返回值为 number 类型，值域为0 - 200。

#### 接口原型  

```
PttGetSpeakerVolume() : number
```

#### 示例代码  

```
m_context.PttGetSpeakerVolume();
```

## 高级 API

### 获取版本号

获取 SDK 版本号，用于分析。

#### 接口原型

```
GetSDKVersion() :string
```

#### 示例代码  

```
string sdkVersion = m_context.GetSDKVersion()；
```


### 设置打印日志等级

用于设置打印日志等级。建议保持默认等级。需要在 Init 之前调用。

#### 接口原型

```
SetLogLevel(level: number) : number
```

#### 参数含义

| 参数       | 类型           | 含义                                                         |
| ---------- | -------------- | ------------------------------------------------------------ |
| level | ITMG_LOG_LEVEL | 设置写入日志的等级，TMG_LOG_LEVEL_NONE 表示不写入，默认为 TMG_LOG_LEVEL_INFO |

level 说明如下：

| level        | 含义                 |
| --------------------- | -------------------- |
| TMG_LOG_LEVEL_NONE    | 不打印日志           |
| TMG_LOG_LEVEL_ERROR   | 打印错误日志（默认） |
| TMG_LOG_LEVEL_INFO    | 打印提示日志         |
| TMG_LOG_LEVEL_DEBUG   | 打印开发调试日志     |
| TMG_LOG_LEVEL_VERBOSE | 打印高频日志         |

#### 示例代码  

```
m_context.SetLogLevel(TMG_LOG_LEVEL_INFO);
```



### 设置打印日志路径

用于设置打印日志路径。默认路径如下。需要在 Init 之前调用。

| 平台    | 路径                                                         |
| ------- | ------------------------------------------------------------ |
| Windows | %appdata%\GMEGLOBAL\GME\ProcessName                           |

#### 接口原型

```
SetLogPath(logPath: string)
```

| 参数   |  类型  | 含义 |
| ------ | :----: | ---- |
| logPath | string | 路径 |

#### 示例代码  

```
string logDir = ""//自行设置路径
m_context.SetLogPath(logDir);
```