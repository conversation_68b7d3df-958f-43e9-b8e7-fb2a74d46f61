--cpu=Cortex-M4.fp.sp
"..\obj\main.o"
"..\obj\stm32f4xx_it.o"
"..\obj\system_stm32f4xx.o"
"..\obj\led.o"
"..\obj\beep.o"
"..\obj\key.o"
"..\obj\delay.o"
"..\obj\sys.o"
"..\obj\usart.o"
"..\obj\startup_stm32f40_41xxx.o"
"..\obj\misc.o"
"..\obj\stm32f4xx_gpio.o"
"..\obj\stm32f4xx_rcc.o"
"..\obj\stm32f4xx_syscfg.o"
"..\obj\stm32f4xx_usart.o"
--strict --scatter "..\OBJ\USART.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "..\OBJ\USART.map" -o ..\OBJ\USART.axf