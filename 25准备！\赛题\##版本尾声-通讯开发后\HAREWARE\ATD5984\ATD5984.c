#include "../../SYSTEM/sys/sys.h"
#include "ATD5984.h"

/* 全局变量：记录当前PWM频率 */
static uint16_t current_pwm_frequency = 2286;  // 默认频率
static uint16_t current_pwm_frequency_motorB = 100;  // 电机B默认频率

/*
 * 水平电机A引脚分配 (STM32F407ZGT6) - 简化版
 * 
 * 水平电机A (ATD5984-A):
 * - STEP信号: PC8 (TIM8_CH3 PWM输出)
 * - DIR信号:  PD3 (GPIO输出, 方向控制)
 * - SLEEP信号: PD2 (GPIO输出, 使能控制)
 */

/**
 * @brief  水平电机A初始化
 * @param  None
 * @retval None
 * @note   配置水平电机控制引脚为输出模式，并设置初始状态
 */
void ATD5984_Init(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	
	/* 使能相关GPIO时钟 */
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOD, ENABLE);  // PD2,PD3时钟
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOC, ENABLE);  // PC12时钟 (电机B使能)
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOB, ENABLE);  // PB12时钟 (电机B方向)
	
	/* 配置GPIOD引脚 (PD2-电机A使能, PD3-电机A方向) */
	GPIO_InitStructure.GPIO_Mode = 	GPIO_Mode_OUT;     // 输出模式
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;     // 推挽输出
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2 | GPIO_Pin_3;  // PD2和PD3同时配置
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;  // 50MHz翻转速度
	GPIO_Init(GPIOD, &GPIO_InitStructure);
	
	/* 配置GPIOC引脚 (PC12-电机B使能) */
	GPIO_InitStructure.GPIO_Mode = 	GPIO_Mode_OUT;     // 输出模式
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;     // 推挽输出
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_12;         // PC12
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;  // 50MHz翻转速度
	GPIO_Init(GPIOC, &GPIO_InitStructure);
	
	/* 配置GPIOB引脚 (PB12-电机B方向) */
	GPIO_InitStructure.GPIO_Mode = 	GPIO_Mode_OUT;     // 输出模式
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;     // 推挽输出
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_12;         // PB12
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;  // 50MHz翻转速度
	GPIO_Init(GPIOB, &GPIO_InitStructure);
	
	/* 设置初始状态 */
	GPIO_ResetBits(GPIOD, GPIO_Pin_3);    // 水平电机A正向 (DIR=LOW)
	GPIO_ResetBits(GPIOD, GPIO_Pin_2);    // 水平电机A初始禁用 (SLEEP=LOW)
	GPIO_ResetBits(GPIOB, GPIO_Pin_12);   // 垂直电机B正向 (DIR=LOW)
	GPIO_ResetBits(GPIOC, GPIO_Pin_12);   // 垂直电机B初始禁用 (SLEEP=LOW)
	
	/* 根据配置设置竖直电机B默认状态 */
	#if MOTOR_B_DEFAULT_STATE_DISABLE
		Motor_B_Disable();  // 第二问：默认卸载状态
	#else
		Motor_B_Enable();   // 第三问：默认上载状态
	#endif
	
	printf("Dual Motor System initialized\r\n");
	printf("Motor A: PC8(TIM8_CH3), DIR=PD3, SLEEP=PD2\r\n");
	printf("Motor B: PA8(TIM1_CH1), DIR=PB12, SLEEP=PC12\r\n");
}

/**
 * @brief  初始化水平电机PWM信号发生器
 * @param  arr: 自动重装载值 (ARR)，决定PWM周期
 * @param  psc: 预分频系数 (PSC)，决定计数频率
 * @retval None
 * @note   使用TIM8的CH3输出PWM，连接到PC8水平电机A
 *         PWM频率计算: f = 168MHz / ((psc+1) * (arr+1))
 *         占空比固定为50%，初始状态PWM通道禁用
 */
void STEP12_PWM_Init(u16 arr, u16 psc)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStructure;
	TIM_OCInitTypeDef TIM_OCInitStructure;
	
	/* 使能GPIOC和TIM8时钟 */
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOC, ENABLE);  // PC8时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM8, ENABLE);   // TIM8时钟
	
	/* 配置PWM输出引脚 PC8(TIM8_CH3) */
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;       // 复用功能模式
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;     // 推挽输出
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8;          // 只配置PC8
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;  // 高速翻转
	GPIO_Init(GPIOC, &GPIO_InitStructure);
	
	/* 配置引脚复用功能为TIM8 */
	GPIO_PinAFConfig(GPIOC, GPIO_PinSource8, GPIO_AF_TIM8);  // PC8->TIM8_CH3
	
	/* 配置TIM8基本参数 */
	TIM_TimeBaseInitStructure.TIM_Period = arr;                    // 自动重装载值(决定周期)
	TIM_TimeBaseInitStructure.TIM_Prescaler = psc;                 // 预分频值(168MHz分频)
	TIM_TimeBaseInitStructure.TIM_ClockDivision = 0;               // 时钟分割系数
	TIM_TimeBaseInitStructure.TIM_CounterMode = TIM_CounterMode_Up; // 向上计数模式
	TIM_TimeBaseInit(TIM8, &TIM_TimeBaseInitStructure);
	
	/* 配置PWM输出参数 */
	TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM1;              // PWM模式1
	TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_High;      // 输出极性为高
	TIM_OCInitStructure.TIM_OutputState = TIM_OutputState_Enable;  // 输出使能
	TIM_OCInitStructure.TIM_Pulse = arr / 2;                       // 比较值(50%占空比)
	
	/* 初始化TIM8通道3 (PC8 - 水平电机A的STEP信号) */
	TIM_OC3Init(TIM8, &TIM_OCInitStructure);
	TIM_OC3PreloadConfig(TIM8, TIM_OCPreload_Enable);   // 使能CH3预装载
	
	/* 使能ARR预装载寄存器 */
	TIM_ARRPreloadConfig(TIM8, ENABLE);
	
	/* 使能TIM8主输出 (高级定时器特有) */
	TIM_CtrlPWMOutputs(TIM8, ENABLE);
	
	/* 启动TIM8定时器，但初始状态禁用PWM输出 */
	TIM_Cmd(TIM8, ENABLE);
	TIM_CCxCmd(TIM8, TIM_Channel_3, DISABLE);  // 初始禁用电机A PWM输出
}

/**
 * @brief  水平电机A旋转指定角度
 * @param  angle: 旋转角度 (度，正数为顺时针，负数为逆时针)
 * @retval None
 * @note   根据角度自动计算步数和方向，执行旋转动作
 *         内部包含适当延时确保动作完成
 */
void Motor_A_Rotate(float angle)
{
	u32 steps;
	u32 rotation_time_ms;
	u8 direction;
	
	/* 参数检查 */
	if (angle == 0.0f) {
		printf("Motor A: No rotation (angle = 0)\r\n");
		return;
	}
	
	/* 计算步数和方向 */
	if (angle > 0) {
		direction = DIR_CW;  // 顺时针
		steps = (u32)(angle * STEPS_PER_DEGREE);
		printf("Motor A: CW rotation %.1f deg (%d steps)\r\n", (double)angle, (int)steps);
	} else {
		direction = DIR_CCW;  // 逆时针
		steps = (u32)((-angle) * STEPS_PER_DEGREE);
		printf("Motor A: CCW rotation %.1f deg (%d steps)\r\n", (double)(-angle), (int)steps);
	}
	
	/* 设置方向 */
	if (direction == DIR_CW) {
		if (DIR_CW == 1) {
			GPIO_SetBits(GPIOD, GPIO_Pin_3);    // DIR=HIGH (顺时针) *改为PD3*
			printf("Direction: CW (DIR=HIGH)\r\n");
		} else {
			GPIO_ResetBits(GPIOD, GPIO_Pin_3);  // DIR=LOW (顺时针) *改为PD3*
			printf("Direction: CW (DIR=LOW)\r\n");
		}
	} else {
		if (DIR_CCW == 1) {
			GPIO_SetBits(GPIOD, GPIO_Pin_3);    // DIR=HIGH (逆时针) *改为PD3*
			printf("Direction: CCW (DIR=HIGH)\r\n");
		} else {
			GPIO_ResetBits(GPIOD, GPIO_Pin_3);  // DIR=LOW (逆时针) *改为PD3*
			printf("Direction: CCW (DIR=LOW)\r\n");
		}
	}
	
	/* 使能电机 */
	GPIO_SetBits(GPIOD, GPIO_Pin_2);  // SLEEP=HIGH (使能)
	delay_ms(2);  // 等待驱动器稳定
	
	/* 启动PWM输出 */
	TIM_CCxCmd(TIM8, TIM_Channel_3, ENABLE);
	
	/* 计算旋转时间 (ms) = 步数 × 1000 / 当前频率 */
	/* 使用当前动态频率进行计算 */
	rotation_time_ms = steps * 1000 / current_pwm_frequency;
	
	printf("Motor A: Rotating for %dms...\r\n", (int)rotation_time_ms);
	delay_ms(rotation_time_ms);
	
	/* 停止PWM输出 */
	TIM_CCxCmd(TIM8, TIM_Channel_3, DISABLE);
	
	/* 禁用电机 */
	GPIO_ResetBits(GPIOD, GPIO_Pin_2);  // SLEEP=LOW (禁用)
	
	printf("Motor A: Rotation completed\r\n");
}

/**
 * @brief  方向控制测试函数
 * @param  None
 * @retval None
 * @note   执行简单的左右旋转测试，验证方向控制正确性
 *         用于校准DIR信号极性
 */
void Motor_A_DirectionTest(void)
{
	printf("\r\n=== Direction Control Test ===\r\n");
	
	/* 测试1: DIR=LOW (应该是某个方向) */
	printf("Test 1: DIR=LOW (Check rotation direction)\r\n");
	GPIO_ResetBits(GPIOD, GPIO_Pin_3);  // DIR=LOW *改为PD3*
	GPIO_SetBits(GPIOD, GPIO_Pin_2);     // 使能电机
	delay_ms(10);
	
	TIM_CCxCmd(TIM8, TIM_Channel_3, ENABLE);   // 启动PWM
	delay_ms(1000);  // 旋转1秒
	TIM_CCxCmd(TIM8, TIM_Channel_3, DISABLE);  // 停止PWM
	GPIO_ResetBits(GPIOD, GPIO_Pin_2);         // 禁用电机
	
	printf("Wait 2 seconds...\r\n");
	delay_ms(2000);
	
	/* 测试2: DIR=HIGH (应该是相反方向) */
	printf("Test 2: DIR=HIGH (Check rotation direction)\r\n");
	GPIO_SetBits(GPIOD, GPIO_Pin_3);    // DIR=HIGH *改为PD3*
	GPIO_SetBits(GPIOD, GPIO_Pin_2);     // 使能电机
	delay_ms(10);
	
	TIM_CCxCmd(TIM8, TIM_Channel_3, ENABLE);   // 启动PWM
	delay_ms(1000);  // 旋转1秒
	TIM_CCxCmd(TIM8, TIM_Channel_3, DISABLE);  // 停止PWM
	GPIO_ResetBits(GPIOD, GPIO_Pin_2);         // 禁用电机
	
	printf("=== Direction Test Complete ===\r\n");
	printf("Observe motor rotation and confirm:\r\n");
	printf("- Test 1 (DIR=LOW): Which direction?\r\n");
	printf("- Test 2 (DIR=HIGH): Which direction?\r\n");
	printf("- Are they opposite directions?\r\n");
	printf("===============================\r\n\r\n");
}

/**
 * @brief  步数校准测试函数
 * @param  None
 * @retval None
 * @note   执行360deg旋转测试，用于校准STEPS_PER_DEGREE值
 *         通过实际测量验证步数计算的准确性
 */
void Motor_A_StepsCalibration(void)
{
	u32 steps_360;
	u32 rotation_time_ms;
	
	printf("\r\n=== Steps Calibration Test ===\r\n");
	printf("This test will rotate 360 deg to calibrate step count\r\n");
	printf("Please observe and measure actual rotation angle\r\n");
	
	/* 计算360deg的步数 (使用当前STEPS_PER_DEGREE值) */
	steps_360 = (u32)(360.0f * STEPS_PER_DEGREE);
	rotation_time_ms = steps_360 * 1000 / 2286;  // 2286Hz PWM频率
	
	printf("Calculated steps for 360 deg: %d steps\r\n", (int)steps_360);
	printf("Estimated time: %d ms\r\n", (int)rotation_time_ms);
	printf("Starting calibration rotation...\r\n");
	
	/* 设置顺时针方向 */
	if (DIR_CW == 1) {
		GPIO_SetBits(GPIOD, GPIO_Pin_3);    // DIR=HIGH *改为PD3*
	} else {
		GPIO_ResetBits(GPIOD, GPIO_Pin_3);  // DIR=LOW *改为PD3*
	}
	
	/* 使能电机 */
	GPIO_SetBits(GPIOD, GPIO_Pin_2);  // SLEEP=HIGH
	delay_ms(10);
	
	/* 执行360deg旋转 */
	TIM_CCxCmd(TIM8, TIM_Channel_3, ENABLE);
	delay_ms(rotation_time_ms);
	TIM_CCxCmd(TIM8, TIM_Channel_3, DISABLE);
	
	/* 禁用电机 */
	GPIO_ResetBits(GPIOD, GPIO_Pin_2);  // SLEEP=LOW
	
	printf("Calibration rotation completed!\r\n");
	printf("Please measure actual rotation angle:\r\n");
	printf("- If actual angle < 360 deg: Increase STEPS_PER_DEGREE\r\n");
	printf("- If actual angle > 360 deg: Decrease STEPS_PER_DEGREE\r\n"); 
	printf("- Current value: %.1f steps/degree\r\n", STEPS_PER_DEGREE);
	printf("==============================\r\n\r\n");
}

/**
 * @brief  初始化垂直电机B的PWM信号发生器
 * @param  arr: 自动重装载值 (ARR)，决定PWM周期
 * @param  psc: 预分频系数 (PSC)，决定计数频率
 * @retval None
 * @note   使用TIM1的CH1输出PWM，连接到PA8垂直电机B
 *         PWM频率计算: f = 168MHz / ((psc+1) * (arr+1))
 *         占空比固定为50%，初始状态PWM通道禁用
 */
void STEP_B_PWM_Init(u16 arr, u16 psc)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStructure;
	TIM_OCInitTypeDef TIM_OCInitStructure;
	
	/* 使能GPIOA和TIM1时钟 */
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);  // PA8时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM1, ENABLE);   // TIM1时钟
	
	/* 配置PWM输出引脚 PA8(TIM1_CH1) */
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;       // 复用功能模式
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;     // 推挽输出
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8;          // PA8引脚
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;  // 高速翻转
	GPIO_Init(GPIOA, &GPIO_InitStructure);
	
	/* 配置引脚复用功能为TIM1 */
	GPIO_PinAFConfig(GPIOA, GPIO_PinSource8, GPIO_AF_TIM1);  // PA8->TIM1_CH1
	
	/* 配置TIM1基本参数 */
	TIM_TimeBaseInitStructure.TIM_Period = arr;                    // 自动重装载值(决定周期)
	TIM_TimeBaseInitStructure.TIM_Prescaler = psc;                 // 预分频值(168MHz分频)
	TIM_TimeBaseInitStructure.TIM_ClockDivision = 0;               // 时钟分割系数
	TIM_TimeBaseInitStructure.TIM_CounterMode = TIM_CounterMode_Up; // 向上计数模式
	TIM_TimeBaseInitStructure.TIM_RepetitionCounter = 0;           // 高级定时器特有
	TIM_TimeBaseInit(TIM1, &TIM_TimeBaseInitStructure);
	
	/* 配置PWM输出参数 */
	TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM1;              // PWM模式1
	TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_High;      // 输出极性为高
	TIM_OCInitStructure.TIM_OutputState = TIM_OutputState_Enable;  // 输出使能
	TIM_OCInitStructure.TIM_OutputNState = TIM_OutputNState_Disable; // 互补输出禁用
	TIM_OCInitStructure.TIM_Pulse = arr / 2;                       // 比较值(50%占空比)
	TIM_OCInitStructure.TIM_OCIdleState = TIM_OCIdleState_Reset;   // 空闲状态为低
	TIM_OCInitStructure.TIM_OCNIdleState = TIM_OCNIdleState_Reset; // 互补空闲状态
	
	/* 初始化TIM1通道1 (PA8 - 垂直电机B的STEP信号) */
	TIM_OC1Init(TIM1, &TIM_OCInitStructure);
	TIM_OC1PreloadConfig(TIM1, TIM_OCPreload_Enable);   // 使能CH1预装载
	
	/* 使能ARR预装载寄存器 */
	TIM_ARRPreloadConfig(TIM1, ENABLE);
	
	/* 使能TIM1主输出 (高级定时器特有) */
	TIM_CtrlPWMOutputs(TIM1, ENABLE);
	
	/* 启动TIM1定时器，但初始状态禁用PWM输出 */
	TIM_Cmd(TIM1, ENABLE);
	TIM_CCxCmd(TIM1, TIM_Channel_1, DISABLE);  // 初始禁用电机B PWM输出
}

/**
 * @brief  动态设置TIM8 PWM频率
 * @param  target_freq: 目标频率 (Hz)，有效范围800-3000Hz
 * @retval None
 * @note   运行时动态调节PWM频率，实现变速控制
 *         基于168MHz系统时钟自动计算最优ARR和PSC组合
 *         保持50%占空比，支持无缝切换
 */
void TIM8_SetFrequency(uint16_t target_freq)
{
	uint16_t psc, arr;
	uint32_t temp_calc;
	uint32_t actual_freq;  // 提前声明所有变量
	
	/* 参数范围检查 */
	if (target_freq < 800) {
		target_freq = 800;
		printf("Warning: Frequency too low, set to 800Hz\r\n");
	} else if (target_freq > 3000) {
		target_freq = 3000;
		printf("Warning: Frequency too high, set to 3000Hz\r\n");
	}
	
	/* 计算最优PSC和ARR组合
	 * 目标：PWM_freq = 168MHz / ((PSC+1) * (ARR+1))
	 * 策略：优先使用较小的PSC值，确保ARR在合理范围内
	 */
	
	for (psc = 0; psc < 65535; psc++) {
		temp_calc = 168000000 / ((psc + 1) * target_freq);
		
		if (temp_calc > 1 && temp_calc <= 65535) {
			arr = (uint16_t)(temp_calc - 1);
			break;
		}
	}
	
	/* 验证计算结果 */
	actual_freq = 168000000 / ((psc + 1) * (arr + 1));
	
	printf("Frequency Update: Target=%dHz, Actual=%dHz, PSC=%d, ARR=%d\r\n", 
		   target_freq, (int)actual_freq, psc, arr);
	
	/* 更新TIM8寄存器 */
	TIM8->PSC = psc;                    // 设置预分频值
	TIM8->ARR = arr;                    // 设置自动重装载值
	TIM8->CCR3 = arr / 2;               // 设置比较值(50%占空比)
	
	/* 生成更新事件，立即生效 */
	TIM_GenerateEvent(TIM8, TIM_EventSource_Update);
	
	/* 记录当前频率 */
	current_pwm_frequency = (uint16_t)actual_freq;
	
	printf("TIM8 Frequency updated successfully!\r\n");
}

/**
 * @brief  获取当前TIM8 PWM频率
 * @param  None
 * @retval 当前PWM频率值 (Hz)
 * @note   返回最后一次设置的频率值
 */
uint16_t TIM8_GetCurrentFrequency(void)
{
	return current_pwm_frequency;
}

/**
 * @brief  设置TIM1 PWM频率（电机B专用）
 * @param  target_freq: 目标频率 (Hz)，有效范围50-200Hz
 * @retval None
 * @note   电机B超低速范围，适用于90°范围内精密控制
 *         独立于TIM8频率，实现真正的并行控制
 */
void TIM1_SetFrequency(uint16_t target_freq)
{
	uint16_t psc, arr;
	uint32_t temp_calc;
	uint32_t actual_freq;
	
	/* 参数范围检查 */
	if (target_freq < 50) {
		target_freq = 50;
		printf("Motor B: Frequency too low, set to 50Hz\r\n");
	} else if (target_freq > 200) {
		target_freq = 200;
		printf("Motor B: Frequency too high, set to 200Hz\r\n");
	}
	
	/* 计算最优PSC和ARR组合
	 * 目标：PWM_freq = 168MHz / ((PSC+1) * (ARR+1))
	 * 策略：优先使用较大的PSC值确保低频稳定性
	 */
	
	/* 对于超低频率，从较大的预分频值开始 */
	for (psc = 167; psc < 65535; psc++) {
		temp_calc = 168000000 / ((psc + 1) * target_freq);
		
		if (temp_calc > 1 && temp_calc <= 65535) {
			arr = (uint16_t)(temp_calc - 1);
			break;
		}
	}
	
	/* 验证计算结果 */
	actual_freq = 168000000 / ((psc + 1) * (arr + 1));
	
	printf("Motor B Frequency: Target=%dHz, Actual=%dHz, PSC=%d, ARR=%d\r\n", 
		   target_freq, (int)actual_freq, psc, arr);
	
	/* 更新TIM1寄存器 */
	TIM1->PSC = psc;                    // 设置预分频值
	TIM1->ARR = arr;                    // 设置自动重装载值
	TIM1->CCR1 = arr / 2;               // 设置比较值(50%占空比)
	
	/* 生成更新事件，立即生效 */
	TIM_GenerateEvent(TIM1, TIM_EventSource_Update);
	
	/* 记录当前频率 */
	current_pwm_frequency_motorB = (uint16_t)actual_freq;
	
	printf("TIM1 Frequency updated successfully!\r\n");
}

/**
 * @brief  获取当前TIM1 PWM频率
 * @param  None
 * @retval 当前PWM频率值 (Hz)
 * @note   返回TIM1最后一次设置的频率值
 */
uint16_t TIM1_GetCurrentFrequency(void)
{
	return current_pwm_frequency_motorB;
}


/**
 * @brief  垂直电机B旋转指定角度
 * @param  angle: 旋转角度 (度，正数为顺时针，负数为逆时针)
 * @retval None
 * @note   电机B专用超低速精密旋转控制
 *         引脚控制: DIR=PB12, SLEEP=PC12, STEP=PA8(TIM1_CH1)
 */
void Motor_B_Rotate(float angle)
{
	u32 steps;
	u32 rotation_time_ms;
	u8 direction;
	
	/* 参数检查 */
	if (angle == 0.0f) {
		printf("Motor B: No rotation (angle = 0)\r\n");
		return;
	}
	
	/* 计算步数和方向 */
	if (angle > 0) {
		direction = DIR_CW;  // 顺时针
		steps = (u32)(angle * STEPS_PER_DEGREE);
		printf("Motor B: CW rotation %.1f deg (%d steps)\r\n", (double)angle, (int)steps);
	} else {
		direction = DIR_CCW;  // 逆时针
		steps = (u32)((-angle) * STEPS_PER_DEGREE);
		printf("Motor B: CCW rotation %.1f deg (%d steps)\r\n", (double)(-angle), (int)steps);
	}
	
	/* 设置方向 */
	if (direction == DIR_CW) {
		if (DIR_CW == 1) {
			GPIO_SetBits(GPIOB, GPIO_Pin_12);    // DIR=HIGH (顺时针)
			printf("Motor B Direction: CW (DIR=HIGH)\r\n");
		} else {
			GPIO_ResetBits(GPIOB, GPIO_Pin_12);  // DIR=LOW (顺时针)
			printf("Motor B Direction: CW (DIR=LOW)\r\n");
		}
	} else {
		if (DIR_CCW == 1) {
			GPIO_SetBits(GPIOB, GPIO_Pin_12);    // DIR=HIGH (逆时针)
			printf("Motor B Direction: CCW (DIR=HIGH)\r\n");
		} else {
			GPIO_ResetBits(GPIOB, GPIO_Pin_12);  // DIR=LOW (逆时针)
			printf("Motor B Direction: CCW (DIR=LOW)\r\n");
		}
	}
	
	/* 使能电机 */
	GPIO_SetBits(GPIOC, GPIO_Pin_12);  // SLEEP=HIGH (使能)
	delay_ms(2);  // 等待驱动器稳定
	
	/* 启动PWM输出 - 使用TIM1通道1 */
	TIM_CCxCmd(TIM1, TIM_Channel_1, ENABLE);
	
	/* 计算旋转时间 (ms) = 步数 × 1000 / 当前频率 */
	/* 使用电机B专用频率进行计算 */
	rotation_time_ms = steps * 1000 / current_pwm_frequency_motorB;
	
	printf("Motor B: Rotating for %dms at %dHz...\r\n", (int)rotation_time_ms, current_pwm_frequency_motorB);
	delay_ms(rotation_time_ms);
	
	/* 停止PWM输出 */
	TIM_CCxCmd(TIM1, TIM_Channel_1, DISABLE);
	
	/* 禁用电机 */
	GPIO_ResetBits(GPIOC, GPIO_Pin_12);  // SLEEP=LOW (禁用)
	
	printf("Motor B: Rotation completed\r\n");
}

/**
 * @brief  竖直电机B上载使能
 * @param  None
 * @retval None
 * @note   设置PC12=HIGH，使能竖直电机B (D36A SLEEP高电平有效)
 *         用于第三问需要竖直电机工作的场景
 */
void Motor_B_Enable(void)
{
	GPIO_SetBits(GPIOC, GPIO_Pin_12);  // SLEEP=HIGH (使能)
}

/**
 * @brief  竖直电机B卸载禁用
 * @param  None
 * @retval None
 * @note   设置PC12=LOW，禁用竖直电机B (D36A SLEEP高电平有效)
 *         用于第二问竖直电机不需要工作的场景
 */
void Motor_B_Disable(void)
{
	GPIO_ResetBits(GPIOC, GPIO_Pin_12);   // SLEEP=LOW (禁用)
}

/**
 * @brief  水平电机A上载使能
 * @param  None
 * @retval None
 * @note   设置PD2=HIGH，使能水平电机A (D36A SLEEP高电平有效)
 *         用于需要水平电机工作的场景
 */
void Motor_A_Enable(void)
{
	GPIO_SetBits(GPIOD, GPIO_Pin_2);  // SLEEP=HIGH (使能)
}

/**
 * @brief  水平电机A卸载禁用
 * @param  None
 * @retval None
 * @note   设置PD2=LOW，禁用水平电机A (D36A SLEEP高电平有效)
 *         用于水平电机不需要工作的场景
 */
void Motor_A_Disable(void)
{
	GPIO_ResetBits(GPIOD, GPIO_Pin_2);   // SLEEP=LOW (禁用)
}
