Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    startup_stm32f10x_md.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(.text) for Reset_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to bluetooth.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f10x_md.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_md.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(HEAP) for Heap_Mem
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(STACK) for Stack_Mem
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    delay.o(i.Delay_ms) refers to delay.o(i.Delay_us) for Delay_us
    delay.o(i.Delay_s) refers to delay.o(i.Delay_ms) for Delay_ms
    led.o(i.LED_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED_OFF) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED_ON) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    led.o(i.LED_Turn) refers to stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit) for GPIO_ReadOutputDataBit
    led.o(i.LED_Turn) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED_Turn) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    key.o(i.Key_ClearEvent) refers to key.o(.bss) for key_states
    key.o(i.Key_GetEvent) refers to key.o(.bss) for key_states
    key.o(i.Key_GetNum) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    key.o(i.Key_GetNum) refers to delay.o(i.Delay_ms) for Delay_ms
    key.o(i.Key_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    key.o(i.Key_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    key.o(i.Key_Init) refers to key.o(.bss) for key_states
    key.o(i.Key_IsClicked) refers to key.o(.bss) for key_states
    key.o(i.Key_IsPressed) refers to key.o(.bss) for key_states
    key.o(i.Key_IsReleased) refers to key.o(.bss) for key_states
    key.o(i.Key_ReadPin) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    key.o(i.Key_Scan) refers to key.o(i.Key_UpdateState) for Key_UpdateState
    key.o(i.Key_UpdateState) refers to key.o(i.Key_ReadPin) for Key_ReadPin
    key.o(i.Key_UpdateState) refers to key.o(.bss) for key_states
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_SendByte) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Start) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Stop) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_I2C_Init) for OLED_I2C_Init
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_SetCursor) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for OLED_F8x16
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    servo.o(i.Servo_AngleToPosition) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    servo.o(i.Servo_AngleToPosition) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    servo.o(i.Servo_AngleToPosition) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    servo.o(i.Servo_AngleToPosition) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    servo.o(i.Servo_AngleToPosition) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    servo.o(i.Servo_GetStatus) refers to servo.o(i.Servo_ReadPosition) for Servo_ReadPosition
    servo.o(i.Servo_GetStatus) refers to servo.o(i.Servo_AngleToPosition) for Servo_AngleToPosition
    servo.o(i.Servo_GetStatus) refers to servo.o(i.Servo_ReadTemperature) for Servo_ReadTemperature
    servo.o(i.Servo_GetStatus) refers to servo.o(i.Servo_ReadVoltage) for Servo_ReadVoltage
    servo.o(i.Servo_GetStatus) refers to servo.o(i.Servo_SendCommand) for Servo_SendCommand
    servo.o(i.Servo_GetStatus) refers to servo.o(i.Servo_ReceiveResponse) for Servo_ReceiveResponse
    servo.o(i.Servo_Init) refers to usart.o(i.USART1_Init) for USART1_Init
    servo.o(i.Servo_Init) refers to delay.o(i.Delay_ms) for Delay_ms
    servo.o(i.Servo_PositionToAngle) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    servo.o(i.Servo_PositionToAngle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    servo.o(i.Servo_PositionToAngle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    servo.o(i.Servo_ReadPosition) refers to servo.o(i.Servo_SendCommand) for Servo_SendCommand
    servo.o(i.Servo_ReadPosition) refers to servo.o(i.Servo_ReceiveResponse) for Servo_ReceiveResponse
    servo.o(i.Servo_ReadPosition) refers to servo.o(i.Servo_PositionToAngle) for Servo_PositionToAngle
    servo.o(i.Servo_ReadTemperature) refers to servo.o(i.Servo_SendCommand) for Servo_SendCommand
    servo.o(i.Servo_ReadTemperature) refers to servo.o(i.Servo_ReceiveResponse) for Servo_ReceiveResponse
    servo.o(i.Servo_ReadVoltage) refers to servo.o(i.Servo_SendCommand) for Servo_SendCommand
    servo.o(i.Servo_ReadVoltage) refers to servo.o(i.Servo_ReceiveResponse) for Servo_ReceiveResponse
    servo.o(i.Servo_ReceiveResponse) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    servo.o(i.Servo_ReceiveResponse) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    servo.o(i.Servo_ReceiveResponse) refers to servo.o(i.Servo_CalculateChecksum) for Servo_CalculateChecksum
    servo.o(i.Servo_SendCommand) refers to servo.o(i.Servo_CalculateChecksum) for Servo_CalculateChecksum
    servo.o(i.Servo_SendCommand) refers to usart.o(i.USART1_SendBuffer) for USART1_SendBuffer
    servo.o(i.Servo_SendCommand) refers to delay.o(i.Delay_ms) for Delay_ms
    servo.o(i.Servo_SetPosition) refers to servo.o(i.Servo_SetPositionWithTime) for Servo_SetPositionWithTime
    servo.o(i.Servo_SetPositionWithTime) refers to servo.o(i.Servo_AngleToPosition) for Servo_AngleToPosition
    servo.o(i.Servo_SetPositionWithTime) refers to servo.o(i.Servo_SendCommand) for Servo_SendCommand
    servo.o(i.Servo_SetTorqueEnable) refers to servo.o(i.Servo_SendCommand) for Servo_SendCommand
    servo.o(i.Servo_SetTorqueEnable) refers to delay.o(i.Delay_ms) for Delay_ms
    geometry.o(i.Geometry_CalculateAngleBetweenPoints) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    geometry.o(i.Geometry_CalculateAngleBetweenPoints) refers to atan2f.o(i.atan2f) for atan2f
    geometry.o(i.Geometry_CalculateDistance) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    geometry.o(i.Geometry_CalculateDistance) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    geometry.o(i.Geometry_CalculateDistance) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    geometry.o(i.Geometry_CalculateDistance) refers to sqrtf.o(i.sqrtf) for sqrtf
    geometry.o(i.Geometry_ClampServoAngle) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    geometry.o(i.Geometry_ClampServoAngle) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    geometry.o(i.Geometry_ClampWallPoint) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    geometry.o(i.Geometry_ClampWallPoint) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    geometry.o(i.Geometry_IsServoAngleValid) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    geometry.o(i.Geometry_IsServoAngleValid) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    geometry.o(i.Geometry_IsWallPointValid) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    geometry.o(i.Geometry_IsWallPointValid) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    geometry.o(i.Geometry_NormalizeAngle) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    geometry.o(i.Geometry_NormalizeAngle) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    geometry.o(i.Geometry_NormalizeAngle) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    geometry.o(i.Geometry_NormalizeAngle) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    geometry.o(i.Geometry_ServoToWall) refers to geometry.o(i.Geometry_IsServoAngleValid) for Geometry_IsServoAngleValid
    geometry.o(i.Geometry_ServoToWall) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    geometry.o(i.Geometry_ServoToWall) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    geometry.o(i.Geometry_ServoToWall) refers to tanf.o(i.tanf) for tanf
    geometry.o(i.Geometry_ServoToWall) refers to geometry.o(i.__ARM_isnanf) for __ARM_isnanf
    geometry.o(i.Geometry_ServoToWall) refers to geometry.o(i.__ARM_isinff) for __ARM_isinff
    geometry.o(i.Geometry_TestConversion) refers to geometry.o(i.Geometry_ServoToWall) for Geometry_ServoToWall
    geometry.o(i.Geometry_TestConversion) refers to geometry.o(i.Geometry_WallToServo) for Geometry_WallToServo
    geometry.o(i.Geometry_TestConversion) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    geometry.o(i.Geometry_TestConversion) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    geometry.o(i.Geometry_TestConversion) refers to geometry.o(.constdata) for .constdata
    geometry.o(i.Geometry_WallToServo) refers to geometry.o(i.Geometry_IsWallPointValid) for Geometry_IsWallPointValid
    geometry.o(i.Geometry_WallToServo) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    geometry.o(i.Geometry_WallToServo) refers to atanf.o(i.atanf) for atanf
    geometry.o(i.Geometry_WallToServo) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    geometry.o(i.Geometry_WallToServo) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    geometry.o(i.Geometry_WallToServo) refers to geometry.o(i.Geometry_ClampServoAngle) for Geometry_ClampServoAngle
    geometry.o(i.Path_CalculateOptimalSteps) refers to geometry.o(i.Path_CalculatePathLength) for Path_CalculatePathLength
    geometry.o(i.Path_CalculateOptimalSteps) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    geometry.o(i.Path_CalculateOptimalSteps) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    geometry.o(i.Path_CalculatePathLength) refers to geometry.o(i.Geometry_CalculateDistance) for Geometry_CalculateDistance
    geometry.o(i.Path_GetNextStep) refers to geometry.o(i.Path_IsComplete) for Path_IsComplete
    geometry.o(i.Path_GetNextStep) refers to geometry.o(i.Path_LinearInterpolate) for Path_LinearInterpolate
    geometry.o(i.Path_GetNextStep) refers to geometry.o(i.Geometry_WallToServo) for Geometry_WallToServo
    geometry.o(i.Path_Initialize) refers to geometry.o(i.Path_ValidatePath) for Path_ValidatePath
    geometry.o(i.Path_Initialize) refers to geometry.o(i.Geometry_WallToServo) for Geometry_WallToServo
    geometry.o(i.Path_LinearInterpolate) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    geometry.o(i.Path_LinearInterpolate) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    geometry.o(i.Path_LinearInterpolate) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    geometry.o(i.Path_LinearInterpolate) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    geometry.o(i.Path_LinearInterpolate) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    geometry.o(i.Path_LinearInterpolate) refers to geometry.o(i.Geometry_IsWallPointValid) for Geometry_IsWallPointValid
    geometry.o(i.Path_LinearInterpolate) refers to geometry.o(i.Geometry_ClampWallPoint) for Geometry_ClampWallPoint
    geometry.o(i.Path_Reset) refers to geometry.o(i.Geometry_WallToServo) for Geometry_WallToServo
    geometry.o(i.Path_SetDirection) refers to geometry.o(i.Geometry_WallToServo) for Geometry_WallToServo
    geometry.o(i.Path_TestInterpolation) refers to geometry.o(i.Path_Initialize) for Path_Initialize
    geometry.o(i.Path_TestInterpolation) refers to geometry.o(i.Path_GetNextStep) for Path_GetNextStep
    geometry.o(i.Path_TestInterpolation) refers to geometry.o(i.Path_LinearInterpolate) for Path_LinearInterpolate
    geometry.o(i.Path_TestInterpolation) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    geometry.o(i.Path_TestInterpolation) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    geometry.o(i.Path_TestInterpolation) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    geometry.o(i.Path_TestInterpolation) refers to sqrtf.o(i.sqrtf) for sqrtf
    geometry.o(i.Path_TestInterpolation) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    geometry.o(i.Path_TestInterpolation) refers to geometry.o(.constdata) for .constdata
    geometry.o(i.Path_ValidatePath) refers to geometry.o(i.Geometry_IsWallPointValid) for Geometry_IsWallPointValid
    geometry.o(i.Path_ValidatePath) refers to geometry.o(i.Geometry_CalculateDistance) for Geometry_CalculateDistance
    geometry.o(i.Path_ValidatePath) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    geometry.o(i.Path_ValidatePath) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    statemachine.o(i.StateMachine_GetMoveProgress) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    statemachine.o(i.StateMachine_GetMoveProgress) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    statemachine.o(i.StateMachine_GetTick) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    statemachine.o(i.StateMachine_HandleRecordKey) refers to statemachine.o(i.StateMachine_RecordPointA) for StateMachine_RecordPointA
    statemachine.o(i.StateMachine_HandleRecordKey) refers to statemachine.o(i.StateMachine_RecordPointB) for StateMachine_RecordPointB
    statemachine.o(i.StateMachine_HandleRecordKey) refers to statemachine.o(i.StateMachine_StopAutoMovement) for StateMachine_StopAutoMovement
    statemachine.o(i.StateMachine_HandleTriggerKey) refers to statemachine.o(i.StateMachine_ToggleDirection) for StateMachine_ToggleDirection
    statemachine.o(i.StateMachine_Init) refers to geometry.o(i.Path_Initialize) for Path_Initialize
    statemachine.o(i.StateMachine_Init) refers to timer.o(i.Timer_Init) for Timer_Init
    statemachine.o(i.StateMachine_Init) refers to timer.o(i.Timer_Start) for Timer_Start
    statemachine.o(i.StateMachine_Init) refers to statemachine.o(.constdata) for .constdata
    statemachine.o(i.StateMachine_IsTimeout) refers to timer.o(i.Timer_IsTimeout) for Timer_IsTimeout
    statemachine.o(i.StateMachine_MoveServoToPosition) refers to servo.o(i.Servo_SetPositionWithTime) for Servo_SetPositionWithTime
    statemachine.o(i.StateMachine_ReadCurrentPosition) refers to servo.o(i.Servo_ReadPosition) for Servo_ReadPosition
    statemachine.o(i.StateMachine_ReadCurrentPosition) refers to geometry.o(i.Geometry_ServoToWall) for Geometry_ServoToWall
    statemachine.o(i.StateMachine_RecordPointA) refers to manualrecord.o(i.ManualRecord_Start) for ManualRecord_Start
    statemachine.o(i.StateMachine_RecordPointA) refers to manualrecord.o(i.ManualRecord_Update) for ManualRecord_Update
    statemachine.o(i.StateMachine_RecordPointA) refers to manualrecord.o(i.ManualRecord_IsComplete) for ManualRecord_IsComplete
    statemachine.o(i.StateMachine_RecordPointA) refers to manualrecord.o(i.ManualRecord_GetResult) for ManualRecord_GetResult
    statemachine.o(i.StateMachine_RecordPointA) refers to bluetooth.o(i.Bluetooth_SendKeyAction) for Bluetooth_SendKeyAction
    statemachine.o(i.StateMachine_RecordPointA) refers to statemachine.o(i.StateMachine_TransitionTo) for StateMachine_TransitionTo
    statemachine.o(i.StateMachine_RecordPointA) refers to statemachine.o(.data) for record_started
    statemachine.o(i.StateMachine_RecordPointA) refers to statemachine.o(.bss) for record_process
    statemachine.o(i.StateMachine_RecordPointB) refers to manualrecord.o(i.ManualRecord_Start) for ManualRecord_Start
    statemachine.o(i.StateMachine_RecordPointB) refers to manualrecord.o(i.ManualRecord_Update) for ManualRecord_Update
    statemachine.o(i.StateMachine_RecordPointB) refers to manualrecord.o(i.ManualRecord_IsComplete) for ManualRecord_IsComplete
    statemachine.o(i.StateMachine_RecordPointB) refers to manualrecord.o(i.ManualRecord_GetResult) for ManualRecord_GetResult
    statemachine.o(i.StateMachine_RecordPointB) refers to bluetooth.o(i.Bluetooth_SendKeyAction) for Bluetooth_SendKeyAction
    statemachine.o(i.StateMachine_RecordPointB) refers to statemachine.o(i.StateMachine_TransitionTo) for StateMachine_TransitionTo
    statemachine.o(i.StateMachine_RecordPointB) refers to statemachine.o(.data) for record_started
    statemachine.o(i.StateMachine_RecordPointB) refers to statemachine.o(.bss) for record_process
    statemachine.o(i.StateMachine_SetServoTorque) refers to servo.o(i.Servo_SetTorqueEnable) for Servo_SetTorqueEnable
    statemachine.o(i.StateMachine_StartAutoMovement) refers to geometry.o(i.Path_Initialize) for Path_Initialize
    statemachine.o(i.StateMachine_StartAutoMovement) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    statemachine.o(i.StateMachine_StartAutoMovement) refers to bluetooth.o(i.Bluetooth_SendKeyAction) for Bluetooth_SendKeyAction
    statemachine.o(i.StateMachine_StartAutoMovement) refers to timer.o(i.Timer_StartPathMovement) for Timer_StartPathMovement
    statemachine.o(i.StateMachine_StartAutoMovement) refers to statemachine.o(i.StateMachine_MoveServoToPosition) for StateMachine_MoveServoToPosition
    statemachine.o(i.StateMachine_StopAutoMovement) refers to bluetooth.o(i.Bluetooth_SendKeyAction) for Bluetooth_SendKeyAction
    statemachine.o(i.StateMachine_StopAutoMovement) refers to timer.o(i.Timer_StopPathMovement) for Timer_StopPathMovement
    statemachine.o(i.StateMachine_StopAutoMovement) refers to statemachine.o(i.StateMachine_TransitionTo) for StateMachine_TransitionTo
    statemachine.o(i.StateMachine_ToggleDirection) refers to geometry.o(i.Path_SetDirection) for Path_SetDirection
    statemachine.o(i.StateMachine_ToggleDirection) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    statemachine.o(i.StateMachine_ToggleDirection) refers to timer.o(i.Timer_StartPathMovement) for Timer_StartPathMovement
    statemachine.o(i.StateMachine_TransitionTo) refers to statemachine.o(i.StateMachine_StartAutoMovement) for StateMachine_StartAutoMovement
    statemachine.o(i.StateMachine_Update) refers to key.o(i.Key_Scan) for Key_Scan
    statemachine.o(i.StateMachine_Update) refers to key.o(i.Key_IsClicked) for Key_IsClicked
    statemachine.o(i.StateMachine_Update) refers to statemachine.o(i.StateMachine_HandleRecordKey) for StateMachine_HandleRecordKey
    statemachine.o(i.StateMachine_Update) refers to key.o(i.Key_ClearEvent) for Key_ClearEvent
    statemachine.o(i.StateMachine_Update) refers to statemachine.o(i.StateMachine_HandleTriggerKey) for StateMachine_HandleTriggerKey
    statemachine.o(i.StateMachine_Update) refers to statemachine.o(i.StateMachine_UpdateAutoMovement) for StateMachine_UpdateAutoMovement
    statemachine.o(i.StateMachine_UpdateAutoMovement) refers to timer.o(i.Timer_IsPathStepReady) for Timer_IsPathStepReady
    statemachine.o(i.StateMachine_UpdateAutoMovement) refers to geometry.o(i.Path_GetNextStep) for Path_GetNextStep
    statemachine.o(i.StateMachine_UpdateAutoMovement) refers to statemachine.o(i.StateMachine_MoveServoToPosition) for StateMachine_MoveServoToPosition
    statemachine.o(i.StateMachine_UpdateAutoMovement) refers to timer.o(i.Timer_ResetPathStep) for Timer_ResetPathStep
    statemachine.o(i.StateMachine_UpdateAutoMovement) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    statemachine.o(i.StateMachine_UpdateAutoMovement) refers to statemachine.o(i.StateMachine_ToggleDirection) for StateMachine_ToggleDirection
    timer.o(i.Timer_ConfigureHardware) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer.o(i.Timer_ConfigureHardware) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.Timer_ConfigureHardware) refers to misc.o(i.NVIC_Init) for NVIC_Init
    timer.o(i.Timer_ConfigureHardware) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    timer.o(i.Timer_ConfigureHardware) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    timer.o(i.Timer_DelayMs) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    timer.o(i.Timer_DelayMs) refers to timer.o(i.Timer_UpdateCpuUsage) for Timer_UpdateCpuUsage
    timer.o(i.Timer_DelayMs) refers to timer.o(i.Timer_IsTimeout) for Timer_IsTimeout
    timer.o(i.Timer_DisableCallback) refers to timer.o(.bss) for timer_state
    timer.o(i.Timer_EnableCallback) refers to timer.o(.bss) for timer_state
    timer.o(i.Timer_GetCpuUsage) refers to timer.o(.data) for cpu_usage_percent
    timer.o(i.Timer_GetTick) refers to timer.o(.bss) for timer_state
    timer.o(i.Timer_GetTimeMs) refers to timer.o(.bss) for timer_state
    timer.o(i.Timer_IRQ_Handler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    timer.o(i.Timer_IRQ_Handler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    timer.o(i.Timer_IRQ_Handler) refers to timer.o(.bss) for timer_state
    timer.o(i.Timer_IRQ_Handler) refers to timer.o(.data) for path_step_counter
    timer.o(i.Timer_Init) refers to timer.o(i.Timer_ConfigureHardware) for Timer_ConfigureHardware
    timer.o(i.Timer_Init) refers to timer.o(.bss) for timer_state
    timer.o(i.Timer_Init) refers to timer.o(.data) for path_step_counter
    timer.o(i.Timer_IsDelayComplete) refers to timer.o(i.Timer_IsTimeout) for Timer_IsTimeout
    timer.o(i.Timer_IsPathStepReady) refers to timer.o(.data) for path_step_ready
    timer.o(i.Timer_IsRunning) refers to timer.o(.bss) for timer_state
    timer.o(i.Timer_IsTimeout) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    timer.o(i.Timer_Reset) refers to stm32f10x_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    timer.o(i.Timer_Reset) refers to timer.o(.bss) for timer_state
    timer.o(i.Timer_Reset) refers to timer.o(.data) for path_step_counter
    timer.o(i.Timer_ResetPathStep) refers to timer.o(.data) for path_step_counter
    timer.o(i.Timer_SetCallback) refers to timer.o(.bss) for timer_state
    timer.o(i.Timer_Start) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    timer.o(i.Timer_Start) refers to timer.o(.bss) for timer_state
    timer.o(i.Timer_StartPathMovement) refers to timer.o(.data) for path_step_counter
    timer.o(i.Timer_Stop) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    timer.o(i.Timer_Stop) refers to timer.o(.bss) for timer_state
    timer.o(i.Timer_StopPathMovement) refers to timer.o(.data) for path_step_counter
    timer.o(i.Timer_UpdateCpuUsage) refers to timer.o(.data) for cpu_idle_count
    manualrecord.o(i.ManualRecord_Cancel) refers to manualrecord.o(i.ManualRecord_LoadServos) for ManualRecord_LoadServos
    manualrecord.o(i.ManualRecord_Cancel) refers to manualrecord.o(i.ManualRecord_TransitionTo) for ManualRecord_TransitionTo
    manualrecord.o(i.ManualRecord_GetResult) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    manualrecord.o(i.ManualRecord_GetResult) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    manualrecord.o(i.ManualRecord_IsPointSaved) refers to manualrecord.o(.data) for point_saved_flags
    manualrecord.o(i.ManualRecord_IsPositionReasonable) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    manualrecord.o(i.ManualRecord_IsPositionReasonable) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    manualrecord.o(i.ManualRecord_IsPositionReasonable) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    manualrecord.o(i.ManualRecord_IsPositionReasonable) refers to sqrtf.o(i.sqrtf) for sqrtf
    manualrecord.o(i.ManualRecord_IsPositionReasonable) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    manualrecord.o(i.ManualRecord_LoadPointData) refers to manualrecord.o(.data) for point_saved_flags
    manualrecord.o(i.ManualRecord_LoadPointData) refers to manualrecord.o(.bss) for saved_points
    manualrecord.o(i.ManualRecord_LoadServos) refers to servo.o(i.Servo_SetTorqueEnable) for Servo_SetTorqueEnable
    manualrecord.o(i.ManualRecord_LoadServos) refers to delay.o(i.Delay_ms) for Delay_ms
    manualrecord.o(i.ManualRecord_ReadCurrentPosition) refers to servo.o(i.Servo_ReadPosition) for Servo_ReadPosition
    manualrecord.o(i.ManualRecord_ReadCurrentPosition) refers to geometry.o(i.Geometry_ServoToWall) for Geometry_ServoToWall
    manualrecord.o(i.ManualRecord_SavePointData) refers to manualrecord.o(.bss) for saved_points
    manualrecord.o(i.ManualRecord_SavePointData) refers to manualrecord.o(.data) for point_saved_flags
    manualrecord.o(i.ManualRecord_ShowInstructions) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    manualrecord.o(i.ManualRecord_ShowInstructions) refers to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    manualrecord.o(i.ManualRecord_ShowInstructions) refers to _printf_str.o(.text) for _printf_str
    manualrecord.o(i.ManualRecord_ShowInstructions) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    manualrecord.o(i.ManualRecord_ShowInstructions) refers to noretval__2sprintf.o(.text) for __2sprintf
    manualrecord.o(i.ManualRecord_ShowProgress) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    manualrecord.o(i.ManualRecord_ShowProgress) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    manualrecord.o(i.ManualRecord_ShowProgress) refers to _printf_str.o(.text) for _printf_str
    manualrecord.o(i.ManualRecord_ShowProgress) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    manualrecord.o(i.ManualRecord_ShowProgress) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    manualrecord.o(i.ManualRecord_ShowProgress) refers to manualrecord.o(i.ManualRecord_GetStateString) for ManualRecord_GetStateString
    manualrecord.o(i.ManualRecord_ShowProgress) refers to noretval__2sprintf.o(.text) for __2sprintf
    manualrecord.o(i.ManualRecord_ShowProgress) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    manualrecord.o(i.ManualRecord_ShowProgress) refers to manualrecord.o(i.ManualRecord_ReadCurrentPosition) for ManualRecord_ReadCurrentPosition
    manualrecord.o(i.ManualRecord_ShowProgress) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    manualrecord.o(i.ManualRecord_Start) refers to manualrecord.o(i.ManualRecord_Init) for ManualRecord_Init
    manualrecord.o(i.ManualRecord_Start) refers to manualrecord.o(i.ManualRecord_ShowInstructions) for ManualRecord_ShowInstructions
    manualrecord.o(i.ManualRecord_Start) refers to manualrecord.o(i.ManualRecord_TransitionTo) for ManualRecord_TransitionTo
    manualrecord.o(i.ManualRecord_TransitionTo) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    manualrecord.o(i.ManualRecord_TransitionTo) refers to led.o(i.LED_ON) for LED_ON
    manualrecord.o(i.ManualRecord_TransitionTo) refers to led.o(i.LED_OFF) for LED_OFF
    manualrecord.o(i.ManualRecord_TransitionTo) refers to delay.o(i.Delay_ms) for Delay_ms
    manualrecord.o(i.ManualRecord_UnloadServos) refers to servo.o(i.Servo_SetTorqueEnable) for Servo_SetTorqueEnable
    manualrecord.o(i.ManualRecord_Update) refers to timer.o(i.Timer_IsTimeout) for Timer_IsTimeout
    manualrecord.o(i.ManualRecord_Update) refers to manualrecord.o(i.ManualRecord_UnloadServos) for ManualRecord_UnloadServos
    manualrecord.o(i.ManualRecord_Update) refers to manualrecord.o(i.ManualRecord_TransitionTo) for ManualRecord_TransitionTo
    manualrecord.o(i.ManualRecord_Update) refers to manualrecord.o(i.ManualRecord_WaitUserAdjustment) for ManualRecord_WaitUserAdjustment
    manualrecord.o(i.ManualRecord_Update) refers to manualrecord.o(i.ManualRecord_ReadCurrentPosition) for ManualRecord_ReadCurrentPosition
    manualrecord.o(i.ManualRecord_Update) refers to manualrecord.o(i.ManualRecord_ValidatePosition) for ManualRecord_ValidatePosition
    manualrecord.o(i.ManualRecord_Update) refers to manualrecord.o(i.ManualRecord_LoadServos) for ManualRecord_LoadServos
    manualrecord.o(i.ManualRecord_Update) refers to manualrecord.o(i.ManualRecord_SavePointData) for ManualRecord_SavePointData
    manualrecord.o(i.ManualRecord_Update) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    manualrecord.o(i.ManualRecord_Update) refers to manualrecord.o(i.ManualRecord_ShowProgress) for ManualRecord_ShowProgress
    manualrecord.o(i.ManualRecord_ValidatePosition) refers to geometry.o(i.Geometry_IsWallPointValid) for Geometry_IsWallPointValid
    manualrecord.o(i.ManualRecord_ValidatePosition) refers to manualrecord.o(i.ManualRecord_IsPositionReasonable) for ManualRecord_IsPositionReasonable
    manualrecord.o(i.ManualRecord_WaitUserAdjustment) refers to key.o(i.Key_IsClicked) for Key_IsClicked
    manualrecord.o(i.ManualRecord_WaitUserAdjustment) refers to key.o(i.Key_ClearEvent) for Key_ClearEvent
    automovement.o(i.AutoMovement_ArePointsValid) refers to geometry.o(i.Geometry_IsWallPointValid) for Geometry_IsWallPointValid
    automovement.o(i.AutoMovement_ArePointsValid) refers to geometry.o(i.Geometry_CalculateDistance) for Geometry_CalculateDistance
    automovement.o(i.AutoMovement_ArePointsValid) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    automovement.o(i.AutoMovement_ArePointsValid) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    automovement.o(i.AutoMovement_CheckSafety) refers to automovement.o(i.AutoMovement_ArePointsValid) for AutoMovement_ArePointsValid
    automovement.o(i.AutoMovement_EmergencyStop) refers to timer.o(i.Timer_StopPathMovement) for Timer_StopPathMovement
    automovement.o(i.AutoMovement_EmergencyStop) refers to automovement.o(i.AutoMovement_TransitionTo) for AutoMovement_TransitionTo
    automovement.o(i.AutoMovement_ExecuteNextStep) refers to geometry.o(i.Path_GetNextStep) for Path_GetNextStep
    automovement.o(i.AutoMovement_ExecuteNextStep) refers to automovement.o(i.AutoMovement_MoveToPosition) for AutoMovement_MoveToPosition
    automovement.o(i.AutoMovement_ExecuteNextStep) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    automovement.o(i.AutoMovement_GetProgress) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    automovement.o(i.AutoMovement_GetProgress) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    automovement.o(i.AutoMovement_HandleDirectionChange) refers to timer.o(i.Timer_IsTimeout) for Timer_IsTimeout
    automovement.o(i.AutoMovement_HandleDirectionChange) refers to geometry.o(i.Path_SetDirection) for Path_SetDirection
    automovement.o(i.AutoMovement_HandleDirectionChange) refers to automovement.o(i.AutoMovement_Stop) for AutoMovement_Stop
    automovement.o(i.AutoMovement_HandleDirectionChange) refers to automovement.o(i.AutoMovement_TransitionTo) for AutoMovement_TransitionTo
    automovement.o(i.AutoMovement_HandleError) refers to timer.o(i.Timer_StopPathMovement) for Timer_StopPathMovement
    automovement.o(i.AutoMovement_HandleError) refers to automovement.o(i.AutoMovement_TransitionTo) for AutoMovement_TransitionTo
    automovement.o(i.AutoMovement_HandleMovingToStart) refers to automovement.o(i.AutoMovement_MoveToPosition) for AutoMovement_MoveToPosition
    automovement.o(i.AutoMovement_HandleMovingToStart) refers to timer.o(i.Timer_IsTimeout) for Timer_IsTimeout
    automovement.o(i.AutoMovement_HandleMovingToStart) refers to automovement.o(i.AutoMovement_TransitionTo) for AutoMovement_TransitionTo
    automovement.o(i.AutoMovement_HandlePathMoving) refers to timer.o(i.Timer_IsPathStepReady) for Timer_IsPathStepReady
    automovement.o(i.AutoMovement_HandlePathMoving) refers to automovement.o(i.AutoMovement_ExecuteNextStep) for AutoMovement_ExecuteNextStep
    automovement.o(i.AutoMovement_HandlePathMoving) refers to timer.o(i.Timer_ResetPathStep) for Timer_ResetPathStep
    automovement.o(i.AutoMovement_HandlePathMoving) refers to geometry.o(i.Path_IsComplete) for Path_IsComplete
    automovement.o(i.AutoMovement_HandlePathMoving) refers to automovement.o(i.AutoMovement_TransitionTo) for AutoMovement_TransitionTo
    automovement.o(i.AutoMovement_HandlePreparing) refers to timer.o(i.Timer_IsTimeout) for Timer_IsTimeout
    automovement.o(i.AutoMovement_HandlePreparing) refers to geometry.o(i.Path_Initialize) for Path_Initialize
    automovement.o(i.AutoMovement_HandlePreparing) refers to geometry.o(i.Path_SetDirection) for Path_SetDirection
    automovement.o(i.AutoMovement_HandlePreparing) refers to automovement.o(i.AutoMovement_TransitionTo) for AutoMovement_TransitionTo
    automovement.o(i.AutoMovement_LoadPointsFromRecord) refers to manualrecord.o(i.ManualRecord_IsPointSaved) for ManualRecord_IsPointSaved
    automovement.o(i.AutoMovement_LoadPointsFromRecord) refers to manualrecord.o(i.ManualRecord_LoadPointData) for ManualRecord_LoadPointData
    automovement.o(i.AutoMovement_MoveToPosition) refers to servo.o(i.Servo_SetPositionWithTime) for Servo_SetPositionWithTime
    automovement.o(i.AutoMovement_ShowStatus) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    automovement.o(i.AutoMovement_ShowStatus) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    automovement.o(i.AutoMovement_ShowStatus) refers to _printf_str.o(.text) for _printf_str
    automovement.o(i.AutoMovement_ShowStatus) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    automovement.o(i.AutoMovement_ShowStatus) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    automovement.o(i.AutoMovement_ShowStatus) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    automovement.o(i.AutoMovement_ShowStatus) refers to _printf_dec.o(.text) for _printf_int_dec
    automovement.o(i.AutoMovement_ShowStatus) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    automovement.o(i.AutoMovement_ShowStatus) refers to automovement.o(i.AutoMovement_GetStateString) for AutoMovement_GetStateString
    automovement.o(i.AutoMovement_ShowStatus) refers to noretval__2sprintf.o(.text) for __2sprintf
    automovement.o(i.AutoMovement_ShowStatus) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    automovement.o(i.AutoMovement_ShowStatus) refers to automovement.o(i.AutoMovement_GetProgress) for AutoMovement_GetProgress
    automovement.o(i.AutoMovement_ShowStatus) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    automovement.o(i.AutoMovement_ShowStatus) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    automovement.o(i.AutoMovement_ShowStatus) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    automovement.o(i.AutoMovement_Start) refers to automovement.o(i.AutoMovement_LoadPointsFromRecord) for AutoMovement_LoadPointsFromRecord
    automovement.o(i.AutoMovement_Start) refers to automovement.o(i.AutoMovement_ArePointsValid) for AutoMovement_ArePointsValid
    automovement.o(i.AutoMovement_Start) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    automovement.o(i.AutoMovement_Start) refers to automovement.o(i.AutoMovement_TransitionTo) for AutoMovement_TransitionTo
    automovement.o(i.AutoMovement_Stop) refers to automovement.o(i.AutoMovement_TransitionTo) for AutoMovement_TransitionTo
    automovement.o(i.AutoMovement_TransitionTo) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    automovement.o(i.AutoMovement_TransitionTo) refers to led.o(i.LED_OFF) for LED_OFF
    automovement.o(i.AutoMovement_TransitionTo) refers to led.o(i.LED_ON) for LED_ON
    automovement.o(i.AutoMovement_TransitionTo) refers to timer.o(i.Timer_StartPathMovement) for Timer_StartPathMovement
    automovement.o(i.AutoMovement_TransitionTo) refers to timer.o(i.Timer_StopPathMovement) for Timer_StopPathMovement
    automovement.o(i.AutoMovement_Update) refers to automovement.o(i.AutoMovement_EmergencyStop) for AutoMovement_EmergencyStop
    automovement.o(i.AutoMovement_Update) refers to automovement.o(i.AutoMovement_Stop) for AutoMovement_Stop
    automovement.o(i.AutoMovement_Update) refers to automovement.o(i.AutoMovement_CheckSafety) for AutoMovement_CheckSafety
    automovement.o(i.AutoMovement_Update) refers to automovement.o(i.AutoMovement_HandleError) for AutoMovement_HandleError
    automovement.o(i.AutoMovement_Update) refers to automovement.o(i.AutoMovement_HandlePreparing) for AutoMovement_HandlePreparing
    automovement.o(i.AutoMovement_Update) refers to automovement.o(i.AutoMovement_HandleMovingToStart) for AutoMovement_HandleMovingToStart
    automovement.o(i.AutoMovement_Update) refers to automovement.o(i.AutoMovement_HandlePathMoving) for AutoMovement_HandlePathMoving
    automovement.o(i.AutoMovement_Update) refers to automovement.o(i.AutoMovement_HandleDirectionChange) for AutoMovement_HandleDirectionChange
    automovement.o(i.AutoMovement_Update) refers to timer.o(i.Timer_IsTimeout) for Timer_IsTimeout
    automovement.o(i.AutoMovement_Update) refers to automovement.o(i.AutoMovement_TransitionTo) for AutoMovement_TransitionTo
    automovement.o(i.AutoMovement_Update) refers to automovement.o(i.AutoMovement_ShowStatus) for AutoMovement_ShowStatus
    systemdiagnostics.o(i.SystemDiag_BackupConfiguration) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    systemdiagnostics.o(i.SystemDiag_BackupConfiguration) refers to systemdiagnostics.o(i.SystemDiag_LogError) for SystemDiag_LogError
    systemdiagnostics.o(i.SystemDiag_CalibrateServos) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    systemdiagnostics.o(i.SystemDiag_CalibrateServos) refers to servo.o(i.Servo_SetPositionWithTime) for Servo_SetPositionWithTime
    systemdiagnostics.o(i.SystemDiag_CalibrateServos) refers to delay.o(i.Delay_ms) for Delay_ms
    systemdiagnostics.o(i.SystemDiag_CalibrateServos) refers to servo.o(i.Servo_ReadPosition) for Servo_ReadPosition
    systemdiagnostics.o(i.SystemDiag_CalibrateServos) refers to systemdiagnostics.o(i.SystemDiag_LogError) for SystemDiag_LogError
    systemdiagnostics.o(i.SystemDiag_CalibrateServos) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    systemdiagnostics.o(i.SystemDiag_CalibrateServos) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    systemdiagnostics.o(i.SystemDiag_CheckGeometry) refers to geometry.o(i.Geometry_ServoToWall) for Geometry_ServoToWall
    systemdiagnostics.o(i.SystemDiag_CheckGeometry) refers to geometry.o(i.Geometry_WallToServo) for Geometry_WallToServo
    systemdiagnostics.o(i.SystemDiag_CheckGeometry) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    systemdiagnostics.o(i.SystemDiag_CheckGeometry) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    systemdiagnostics.o(i.SystemDiag_CheckGeometry) refers to systemdiagnostics.o(i.SystemDiag_LogError) for SystemDiag_LogError
    systemdiagnostics.o(i.SystemDiag_CheckGeometry) refers to systemdiagnostics.o(.constdata) for .constdata
    systemdiagnostics.o(i.SystemDiag_CheckServos) refers to servo.o(i.Servo_ReadPosition) for Servo_ReadPosition
    systemdiagnostics.o(i.SystemDiag_CheckServos) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    systemdiagnostics.o(i.SystemDiag_CheckServos) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    systemdiagnostics.o(i.SystemDiag_CheckServos) refers to systemdiagnostics.o(i.SystemDiag_LogError) for SystemDiag_LogError
    systemdiagnostics.o(i.SystemDiag_CheckServos) refers to servo.o(i.Servo_GetStatus) for Servo_GetStatus
    systemdiagnostics.o(i.SystemDiag_CheckTimer) refers to timer.o(i.Timer_IsRunning) for Timer_IsRunning
    systemdiagnostics.o(i.SystemDiag_CheckTimer) refers to timer.o(i.Timer_GetFrequency) for Timer_GetFrequency
    systemdiagnostics.o(i.SystemDiag_CheckTimer) refers to systemdiagnostics.o(i.SystemDiag_LogError) for SystemDiag_LogError
    systemdiagnostics.o(i.SystemDiag_ClearErrorLog) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to _printf_str.o(.text) for _printf_str
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to _printf_dec.o(.text) for _printf_int_dec
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to systemdiagnostics.o(i.SystemDiag_QuickCheck) for SystemDiag_QuickCheck
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to systemdiagnostics.o(i.SystemDiag_GetLevelString) for SystemDiag_GetLevelString
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to noretval__2sprintf.o(.text) for __2sprintf
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to delay.o(i.Delay_ms) for Delay_ms
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to systemdiagnostics.o(i.SystemDiag_ShowPerformance) for SystemDiag_ShowPerformance
    systemdiagnostics.o(i.SystemDiag_GenerateReport) refers to systemdiagnostics.o(i.SystemDiag_ShowErrors) for SystemDiag_ShowErrors
    systemdiagnostics.o(i.SystemDiag_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    systemdiagnostics.o(i.SystemDiag_Init) refers to systemdiagnostics.o(i.SystemDiag_LoadDefaultConfig) for SystemDiag_LoadDefaultConfig
    systemdiagnostics.o(i.SystemDiag_Init) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    systemdiagnostics.o(i.SystemDiag_LogError) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    systemdiagnostics.o(i.SystemDiag_LogError) refers to strncpy.o(.text) for strncpy
    systemdiagnostics.o(i.SystemDiag_OptimizePerformance) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    systemdiagnostics.o(i.SystemDiag_OptimizePerformance) refers to systemdiagnostics.o(i.SystemDiag_LogError) for SystemDiag_LogError
    systemdiagnostics.o(i.SystemDiag_OptimizePerformance) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    systemdiagnostics.o(i.SystemDiag_PrintDiagnostic) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    systemdiagnostics.o(i.SystemDiag_PrintDiagnostic) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    systemdiagnostics.o(i.SystemDiag_PrintDiagnostic) refers to _printf_str.o(.text) for _printf_str
    systemdiagnostics.o(i.SystemDiag_PrintDiagnostic) refers to noretval__2sprintf.o(.text) for __2sprintf
    systemdiagnostics.o(i.SystemDiag_PrintDiagnostic) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    systemdiagnostics.o(i.SystemDiag_QuickCheck) refers to systemdiagnostics.o(i.SystemDiag_LogError) for SystemDiag_LogError
    systemdiagnostics.o(i.SystemDiag_QuickCheck) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    systemdiagnostics.o(i.SystemDiag_RecordPathStep) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    systemdiagnostics.o(i.SystemDiag_RecordPathStep) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    systemdiagnostics.o(i.SystemDiag_RecordPathStep) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    systemdiagnostics.o(i.SystemDiag_RecordPathStep) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    systemdiagnostics.o(i.SystemDiag_RecordServoError) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    systemdiagnostics.o(i.SystemDiag_RecordServoError) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    systemdiagnostics.o(i.SystemDiag_RecordServoError) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    systemdiagnostics.o(i.SystemDiag_RecordServoError) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    systemdiagnostics.o(i.SystemDiag_ResetStatistics) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    systemdiagnostics.o(i.SystemDiag_ResetStatistics) refers to systemdiagnostics.o(i.SystemDiag_ClearErrorLog) for SystemDiag_ClearErrorLog
    systemdiagnostics.o(i.SystemDiag_RestoreConfiguration) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    systemdiagnostics.o(i.SystemDiag_RestoreConfiguration) refers to systemdiagnostics.o(i.SystemDiag_LoadDefaultConfig) for SystemDiag_LoadDefaultConfig
    systemdiagnostics.o(i.SystemDiag_RestoreConfiguration) refers to systemdiagnostics.o(i.SystemDiag_LogError) for SystemDiag_LogError
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to _printf_str.o(.text) for _printf_str
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to systemdiagnostics.o(i.SystemDiag_CheckServos) for SystemDiag_CheckServos
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to delay.o(i.Delay_ms) for Delay_ms
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to systemdiagnostics.o(i.SystemDiag_CheckTimer) for SystemDiag_CheckTimer
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to systemdiagnostics.o(i.SystemDiag_CheckKeys) for SystemDiag_CheckKeys
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to systemdiagnostics.o(i.SystemDiag_CheckGeometry) for SystemDiag_CheckGeometry
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to systemdiagnostics.o(i.SystemDiag_CheckMemory) for SystemDiag_CheckMemory
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to systemdiagnostics.o(i.SystemDiag_GetLevelString) for SystemDiag_GetLevelString
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to noretval__2sprintf.o(.text) for __2sprintf
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to led.o(i.LED_ON) for LED_ON
    systemdiagnostics.o(i.SystemDiag_RunFullCheck) refers to led.o(i.LED_OFF) for LED_OFF
    systemdiagnostics.o(i.SystemDiag_ShowErrors) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    systemdiagnostics.o(i.SystemDiag_ShowErrors) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    systemdiagnostics.o(i.SystemDiag_ShowErrors) refers to _printf_dec.o(.text) for _printf_int_dec
    systemdiagnostics.o(i.SystemDiag_ShowErrors) refers to oled.o(i.OLED_Clear) for OLED_Clear
    systemdiagnostics.o(i.SystemDiag_ShowErrors) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    systemdiagnostics.o(i.SystemDiag_ShowErrors) refers to noretval__2sprintf.o(.text) for __2sprintf
    systemdiagnostics.o(i.SystemDiag_ShowErrors) refers to strncpy.o(.text) for strncpy
    systemdiagnostics.o(i.SystemDiag_ShowPerformance) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    systemdiagnostics.o(i.SystemDiag_ShowPerformance) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    systemdiagnostics.o(i.SystemDiag_ShowPerformance) refers to _printf_dec.o(.text) for _printf_int_dec
    systemdiagnostics.o(i.SystemDiag_ShowPerformance) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    systemdiagnostics.o(i.SystemDiag_ShowPerformance) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    systemdiagnostics.o(i.SystemDiag_ShowPerformance) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    systemdiagnostics.o(i.SystemDiag_ShowPerformance) refers to noretval__2sprintf.o(.text) for __2sprintf
    systemdiagnostics.o(i.SystemDiag_ShowPerformance) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    systemdiagnostics.o(i.SystemDiag_ShowStatus) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    systemdiagnostics.o(i.SystemDiag_ShowStatus) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    systemdiagnostics.o(i.SystemDiag_ShowStatus) refers to _printf_dec.o(.text) for _printf_int_dec
    systemdiagnostics.o(i.SystemDiag_ShowStatus) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    systemdiagnostics.o(i.SystemDiag_ShowStatus) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    systemdiagnostics.o(i.SystemDiag_ShowStatus) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    systemdiagnostics.o(i.SystemDiag_ShowStatus) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    systemdiagnostics.o(i.SystemDiag_ShowStatus) refers to noretval__2sprintf.o(.text) for __2sprintf
    systemdiagnostics.o(i.SystemDiag_ShowStatus) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    systemdiagnostics.o(i.SystemDiag_Update) refers to timer.o(i.Timer_GetTimeMs) for Timer_GetTimeMs
    systemdiagnostics.o(i.SystemDiag_Update) refers to timer.o(i.Timer_GetTick) for Timer_GetTick
    systemdiagnostics.o(i.SystemDiag_Update) refers to systemdiagnostics.o(i.SystemDiag_QuickCheck) for SystemDiag_QuickCheck
    systemdiagnostics.o(i.SystemDiag_ValidateAccuracy) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    systemdiagnostics.o(i.SystemDiag_ValidateAccuracy) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    systemdiagnostics.o(i.SystemDiag_ValidateAccuracy) refers to geometry.o(i.Geometry_ServoToWall) for Geometry_ServoToWall
    systemdiagnostics.o(i.SystemDiag_ValidateAccuracy) refers to geometry.o(i.Geometry_WallToServo) for Geometry_WallToServo
    systemdiagnostics.o(i.SystemDiag_ValidateAccuracy) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    systemdiagnostics.o(i.SystemDiag_ValidateAccuracy) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    systemdiagnostics.o(i.SystemDiag_ValidateAccuracy) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    systemdiagnostics.o(i.SystemDiag_ValidateAccuracy) refers to systemdiagnostics.o(i.SystemDiag_LogError) for SystemDiag_LogError
    systemdiagnostics.o(i.SystemDiag_ValidateAccuracy) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    systemdiagnostics.o(i.SystemDiag_ValidateAccuracy) refers to systemdiagnostics.o(.constdata) for .constdata
    usart.o(i.USART1_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.USART1_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.USART1_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart.o(i.USART1_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.USART1_ReceiveBuffer) refers to usart.o(i.USART1_ReceiveByte) for USART1_ReceiveByte
    usart.o(i.USART1_ReceiveByte) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.USART1_ReceiveByte) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART1_SendBuffer) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.USART1_SendBuffer) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    usart.o(i.USART1_SendByte) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.USART1_SendByte) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    usart.o(i.USART1_WaitTransmitComplete) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    bluetooth.o(i.Bluetooth_ConfigureModule) refers to bluetooth.o(i.Bluetooth_SetName) for Bluetooth_SetName
    bluetooth.o(i.Bluetooth_ConfigureModule) refers to delay.o(i.Delay_ms) for Delay_ms
    bluetooth.o(i.Bluetooth_ConfigureModule) refers to bluetooth.o(i.Bluetooth_SetPin) for Bluetooth_SetPin
    bluetooth.o(i.Bluetooth_ConfigureModule) refers to bluetooth.o(i.Bluetooth_TestConnection) for Bluetooth_TestConnection
    bluetooth.o(i.Bluetooth_GetStatus) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.Bluetooth_Init) refers to bluetooth.o(i.USART2_Init) for USART2_Init
    bluetooth.o(i.Bluetooth_Init) refers to delay.o(i.Delay_ms) for Delay_ms
    bluetooth.o(i.Bluetooth_Init) refers to bluetooth.o(i.Bluetooth_ConfigureModule) for Bluetooth_ConfigureModule
    bluetooth.o(i.Bluetooth_Init) refers to bluetooth.o(i.Bluetooth_SendStartupInfo) for Bluetooth_SendStartupInfo
    bluetooth.o(i.Bluetooth_Init) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.Bluetooth_ParseCommand) refers to strstr.o(.text) for strstr
    bluetooth.o(i.Bluetooth_ParseCommand) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    bluetooth.o(i.Bluetooth_ParseCommand) refers to bluetooth.o(i.Bluetooth_PrintConnectionInfo) for Bluetooth_PrintConnectionInfo
    bluetooth.o(i.Bluetooth_PrintConnectionInfo) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bluetooth.o(i.Bluetooth_PrintConnectionInfo) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bluetooth.o(i.Bluetooth_PrintConnectionInfo) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    bluetooth.o(i.Bluetooth_PrintConnectionInfo) refers to _printf_dec.o(.text) for _printf_int_dec
    bluetooth.o(i.Bluetooth_PrintConnectionInfo) refers to _printf_str.o(.text) for _printf_str
    bluetooth.o(i.Bluetooth_PrintConnectionInfo) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    bluetooth.o(i.Bluetooth_PrintConnectionInfo) refers to noretval__2sprintf.o(.text) for __2sprintf
    bluetooth.o(i.Bluetooth_PrintConnectionInfo) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    bluetooth.o(i.Bluetooth_PrintConnectionInfo) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.Bluetooth_ProcessReceivedData) refers to bluetooth.o(i.Bluetooth_ParseCommand) for Bluetooth_ParseCommand
    bluetooth.o(i.Bluetooth_ProcessReceivedData) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.Bluetooth_SendByte) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    bluetooth.o(i.Bluetooth_SendByte) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    bluetooth.o(i.Bluetooth_SendDiagnosticInfo) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bluetooth.o(i.Bluetooth_SendDiagnosticInfo) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bluetooth.o(i.Bluetooth_SendDiagnosticInfo) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    bluetooth.o(i.Bluetooth_SendDiagnosticInfo) refers to _printf_dec.o(.text) for _printf_int_dec
    bluetooth.o(i.Bluetooth_SendDiagnosticInfo) refers to _printf_str.o(.text) for _printf_str
    bluetooth.o(i.Bluetooth_SendDiagnosticInfo) refers to noretval__2sprintf.o(.text) for __2sprintf
    bluetooth.o(i.Bluetooth_SendDiagnosticInfo) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    bluetooth.o(i.Bluetooth_SendDiagnosticInfo) refers to bluetooth.o(.conststring) for .conststring
    bluetooth.o(i.Bluetooth_SendDiagnosticInfo) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.Bluetooth_SendErrorReport) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bluetooth.o(i.Bluetooth_SendErrorReport) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bluetooth.o(i.Bluetooth_SendErrorReport) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    bluetooth.o(i.Bluetooth_SendErrorReport) refers to _printf_dec.o(.text) for _printf_int_dec
    bluetooth.o(i.Bluetooth_SendErrorReport) refers to _printf_str.o(.text) for _printf_str
    bluetooth.o(i.Bluetooth_SendErrorReport) refers to noretval__2sprintf.o(.text) for __2sprintf
    bluetooth.o(i.Bluetooth_SendErrorReport) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    bluetooth.o(i.Bluetooth_SendErrorReport) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.Bluetooth_SendKeyAction) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bluetooth.o(i.Bluetooth_SendKeyAction) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    bluetooth.o(i.Bluetooth_SendKeyAction) refers to _printf_str.o(.text) for _printf_str
    bluetooth.o(i.Bluetooth_SendKeyAction) refers to noretval__2sprintf.o(.text) for __2sprintf
    bluetooth.o(i.Bluetooth_SendKeyAction) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    bluetooth.o(i.Bluetooth_SendKeyAction) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.Bluetooth_SendPerformanceData) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bluetooth.o(i.Bluetooth_SendPerformanceData) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    bluetooth.o(i.Bluetooth_SendPerformanceData) refers to _printf_dec.o(.text) for _printf_int_dec
    bluetooth.o(i.Bluetooth_SendPerformanceData) refers to noretval__2sprintf.o(.text) for __2sprintf
    bluetooth.o(i.Bluetooth_SendPerformanceData) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    bluetooth.o(i.Bluetooth_SendPerformanceData) refers to bluetooth.o(.conststring) for .conststring
    bluetooth.o(i.Bluetooth_SendPerformanceData) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.Bluetooth_SendPositionInfo) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bluetooth.o(i.Bluetooth_SendPositionInfo) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    bluetooth.o(i.Bluetooth_SendPositionInfo) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    bluetooth.o(i.Bluetooth_SendPositionInfo) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    bluetooth.o(i.Bluetooth_SendPositionInfo) refers to noretval__2sprintf.o(.text) for __2sprintf
    bluetooth.o(i.Bluetooth_SendPositionInfo) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    bluetooth.o(i.Bluetooth_SendPositionInfo) refers to bluetooth.o(.conststring) for .conststring
    bluetooth.o(i.Bluetooth_SendPositionInfo) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.Bluetooth_SendStartupInfo) refers to noretval__2sprintf.o(.text) for __2sprintf
    bluetooth.o(i.Bluetooth_SendStartupInfo) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    bluetooth.o(i.Bluetooth_SendStartupInfo) refers to bluetooth.o(.conststring) for .conststring
    bluetooth.o(i.Bluetooth_SendStartupInfo) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.Bluetooth_SendString) refers to bluetooth.o(i.Bluetooth_SendByte) for Bluetooth_SendByte
    bluetooth.o(i.Bluetooth_SetName) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bluetooth.o(i.Bluetooth_SetName) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    bluetooth.o(i.Bluetooth_SetName) refers to _printf_str.o(.text) for _printf_str
    bluetooth.o(i.Bluetooth_SetName) refers to noretval__2sprintf.o(.text) for __2sprintf
    bluetooth.o(i.Bluetooth_SetName) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    bluetooth.o(i.Bluetooth_SetPin) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bluetooth.o(i.Bluetooth_SetPin) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    bluetooth.o(i.Bluetooth_SetPin) refers to _printf_str.o(.text) for _printf_str
    bluetooth.o(i.Bluetooth_SetPin) refers to noretval__2sprintf.o(.text) for __2sprintf
    bluetooth.o(i.Bluetooth_SetPin) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    bluetooth.o(i.Bluetooth_TestConnection) refers to bluetooth.o(i.Bluetooth_SendString) for Bluetooth_SendString
    bluetooth.o(i.Bluetooth_TestConnection) refers to delay.o(i.Delay_ms) for Delay_ms
    bluetooth.o(i.Bluetooth_Update) refers to bluetooth.o(i.Bluetooth_ProcessReceivedData) for Bluetooth_ProcessReceivedData
    bluetooth.o(i.Bluetooth_Update) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    bluetooth.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    bluetooth.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    bluetooth.o(i.USART2_IRQHandler) refers to bluetooth.o(.bss) for bluetooth_ctrl
    bluetooth.o(i.USART2_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    bluetooth.o(i.USART2_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    bluetooth.o(i.USART2_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    bluetooth.o(i.USART2_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    bluetooth.o(i.USART2_Init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    bluetooth.o(i.USART2_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    bluetooth.o(i.USART2_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    main.o(i.main) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.main) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(i.main) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to led.o(i.LED_Init) for LED_Init
    main.o(i.main) refers to servo.o(i.Servo_Init) for Servo_Init
    main.o(i.main) refers to key.o(i.Key_Init) for Key_Init
    main.o(i.main) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.main) refers to delay.o(i.Delay_ms) for Delay_ms
    main.o(i.main) refers to servo.o(i.Servo_SetPosition) for Servo_SetPosition
    main.o(i.main) refers to led.o(i.LED_OFF) for LED_OFF
    main.o(i.main) refers to key.o(i.Key_Scan) for Key_Scan
    main.o(i.main) refers to key.o(i.Key_IsClicked) for Key_IsClicked
    main.o(i.main) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    main.o(i.main) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    main.o(i.main) refers to led.o(i.LED_ON) for LED_ON
    main.o(i.main) refers to key.o(i.Key_ClearEvent) for Key_ClearEvent
    main.o(i.main) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.main) refers to noretval__2sprintf.o(.text) for __2sprintf
    main.o(i.main) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    main.o(i.main) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    main.o(i.main) refers to main.o(.data) for servo1_angle
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to timer.o(i.Timer_IRQ_Handler) for Timer_IRQ_Handler
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    strncpy.o(.text) refers to rt_memclr.o(.text) for __aeabi_memclr
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixu) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixur) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fleqf.o(x$fpl$fleqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    frleqf.o(x$fpl$frleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frleqf.o(x$fpl$frleqf) refers to fleqf.o(x$fpl$fleqf) for __fpl_fcmple_InfNaN
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    atan2f.o(i.__softfp_atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.atan2f) for atan2f
    atan2f.o(i.atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.atan2f) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    atan2f.o(i.atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.atan2f) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    atan2f.o(i.atan2f) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    atan2f.o(i.atan2f) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    atan2f.o(i.atan2f) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    atan2f.o(i.atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f_x.o(i.____softfp_atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.____softfp_atan2f$lsc) refers to atan2f_x.o(i.__atan2f$lsc) for __atan2f$lsc
    atan2f_x.o(i.__atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.__atan2f$lsc) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    atan2f_x.o(i.__atan2f$lsc) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    atan2f_x.o(i.__atan2f$lsc) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    atan2f_x.o(i.__atan2f$lsc) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    atan2f_x.o(i.__atan2f$lsc) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    atan2f_x.o(i.__atan2f$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f_x.o(i.__atan2f$lsc) refers to _rserrno.o(.text) for __set_errno
    atanf.o(i.__softfp_atanf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atanf.o(i.__softfp_atanf) refers to atanf.o(i.atanf) for atanf
    atanf.o(i.atanf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atanf.o(i.atanf) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    atanf.o(i.atanf) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    atanf.o(i.atanf) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    atanf.o(i.atanf) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    atanf.o(i.atanf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atanf.o(i.atanf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atanf.o(i.atanf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    atanf_x.o(i.____softfp_atanf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atanf_x.o(i.____softfp_atanf$lsc) refers to atanf_x.o(i.__atanf$lsc) for __atanf$lsc
    atanf_x.o(i.__atanf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atanf_x.o(i.__atanf$lsc) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    atanf_x.o(i.__atanf$lsc) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    atanf_x.o(i.__atanf$lsc) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    atanf_x.o(i.__atanf$lsc) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    atanf_x.o(i.__atanf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to fsqrt.o(x$fpl$fsqrt) for _fsqrt
    sqrtf.o(i.__softfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to fsqrt.o(x$fpl$fsqrt) for _fsqrt
    sqrtf.o(i.sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to fsqrt.o(x$fpl$fsqrt) for _fsqrt
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers to fsqrt.o(x$fpl$fsqrt) for _fsqrt
    tanf.o(i.__softfp_tanf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tanf.o(i.__softfp_tanf) refers to tanf.o(i.tanf) for tanf
    tanf.o(i.tanf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tanf.o(i.tanf) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    tanf.o(i.tanf) refers to frnd.o(x$fpl$frnd) for _frnd
    tanf.o(i.tanf) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    tanf.o(i.tanf) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    tanf.o(i.tanf) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    tanf.o(i.tanf) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    tanf.o(i.tanf) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    tanf.o(i.tanf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    tanf.o(i.tanf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    tanf.o(i.tanf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    tanf.o(i.tanf) refers to _rserrno.o(.text) for __set_errno
    tanf.o(i.tanf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    tanf.o(i.tanf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    tanf_x.o(i.____softfp_tanf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tanf_x.o(i.____softfp_tanf$lsc) refers to tanf_x.o(i.__tanf$lsc) for __tanf$lsc
    tanf_x.o(i.__tanf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tanf_x.o(i.__tanf$lsc) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    tanf_x.o(i.__tanf$lsc) refers to frnd.o(x$fpl$frnd) for _frnd
    tanf_x.o(i.__tanf$lsc) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    tanf_x.o(i.__tanf$lsc) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    tanf_x.o(i.__tanf$lsc) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    tanf_x.o(i.__tanf$lsc) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    tanf_x.o(i.__tanf$lsc) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    tanf_x.o(i.__tanf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    tanf_x.o(i.__tanf$lsc) refers to _rserrno.o(.text) for __set_errno
    tanf_x.o(i.__tanf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_char_common.o(.text) refers to __printf_ss_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmpi.o(x$fpl$fcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffix) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffixr) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frnd.o(x$fpl$frnd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frnd.o(x$fpl$frnd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fsqrt.o(x$fpl$fsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fsqrt.o(x$fpl$fsqrt) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    funder.o(i.__mathlib_flt_divzero) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    funder.o(i.__mathlib_flt_infnan) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    funder.o(i.__mathlib_flt_infnan2) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    funder.o(i.__mathlib_flt_invalid) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    funder.o(i.__mathlib_flt_overflow) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    funder.o(i.__mathlib_flt_posinfnan) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    funder.o(i.__mathlib_flt_underflow) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    rredf.o(i.__mathlib_rredf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    rredf.o(i.__mathlib_rredf2) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    rredf.o(i.__mathlib_rredf2) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    rredf.o(i.__mathlib_rredf2) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    rredf.o(i.__mathlib_rredf2) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    rredf.o(i.__mathlib_rredf2) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    rredf.o(i.__mathlib_rredf2) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbnf.o(x$fpl$scalbnf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbnf.o(x$fpl$scalbnf) refers to fcheck1.o(x$fpl$fcheck1) for __fpl_fcheck_NaN1
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_md.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    fcheck1.o(x$fpl$fcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcheck1.o(x$fpl$fcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing system_stm32f10x.o(.data), (20 bytes).
    Removing misc.o(i.NVIC_Init), (112 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (40 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetITStatus), (40 bytes).
    Removing stm32f10x_exti.o(i.EXTI_Init), (148 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (144 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (42 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (56 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (58 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (38 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (26 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (236 bytes).
    Removing stm32f10x_i2c.o(i.I2C_NACKPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (128 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (144 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_ARRPreloadConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_Cmd), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CtrlPWMOutputs), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (488 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (172 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (160 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseInit), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ITConfig), (74 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing delay.o(i.Delay_s), (24 bytes).
    Removing led.o(i.LED_Turn), (36 bytes).
    Removing key.o(i.Key_GetEvent), (56 bytes).
    Removing key.o(i.Key_GetNum), (92 bytes).
    Removing key.o(i.Key_IsPressed), (40 bytes).
    Removing key.o(i.Key_IsReleased), (40 bytes).
    Removing oled.o(i.OLED_Pow), (20 bytes).
    Removing oled.o(i.OLED_ShowBinNum), (62 bytes).
    Removing oled.o(i.OLED_ShowHexNum), (84 bytes).
    Removing oled.o(i.OLED_ShowNum), (68 bytes).
    Removing oled.o(i.OLED_ShowSignedNum), (102 bytes).
    Removing servo.o(i.Servo_GetStatus), (112 bytes).
    Removing servo.o(i.Servo_PositionToAngle), (48 bytes).
    Removing servo.o(i.Servo_ReadPosition), (70 bytes).
    Removing servo.o(i.Servo_ReadTemperature), (56 bytes).
    Removing servo.o(i.Servo_ReadVoltage), (64 bytes).
    Removing servo.o(i.Servo_ReceiveResponse), (280 bytes).
    Removing servo.o(i.Servo_SetTorqueEnable), (38 bytes).
    Removing geometry.o(i.Geometry_CalculateAngleBetweenPoints), (34 bytes).
    Removing geometry.o(i.Geometry_CalculateDistance), (62 bytes).
    Removing geometry.o(i.Geometry_ClampServoAngle), (100 bytes).
    Removing geometry.o(i.Geometry_ClampWallPoint), (100 bytes).
    Removing geometry.o(i.Geometry_IsServoAngleValid), (60 bytes).
    Removing geometry.o(i.Geometry_IsWallPointValid), (60 bytes).
    Removing geometry.o(i.Geometry_NormalizeAngle), (56 bytes).
    Removing geometry.o(i.Geometry_ServoToWall), (152 bytes).
    Removing geometry.o(i.Geometry_TestConversion), (112 bytes).
    Removing geometry.o(i.Geometry_WallToServo), (140 bytes).
    Removing geometry.o(i.Path_CalculateOptimalSteps), (52 bytes).
    Removing geometry.o(i.Path_CalculatePathLength), (14 bytes).
    Removing geometry.o(i.Path_GetNextStep), (156 bytes).
    Removing geometry.o(i.Path_Initialize), (106 bytes).
    Removing geometry.o(i.Path_IsComplete), (24 bytes).
    Removing geometry.o(i.Path_LinearInterpolate), (140 bytes).
    Removing geometry.o(i.Path_Reset), (58 bytes).
    Removing geometry.o(i.Path_SetDirection), (54 bytes).
    Removing geometry.o(i.Path_TestInterpolation), (192 bytes).
    Removing geometry.o(i.Path_ValidatePath), (80 bytes).
    Removing geometry.o(i.__ARM_isinff), (20 bytes).
    Removing geometry.o(i.__ARM_isnanf), (14 bytes).
    Removing geometry.o(.constdata), (24 bytes).
    Removing statemachine.o(i.StateMachine_GetCurrentState), (12 bytes).
    Removing statemachine.o(i.StateMachine_GetDirectionString), (28 bytes).
    Removing statemachine.o(i.StateMachine_GetMoveProgress), (44 bytes).
    Removing statemachine.o(i.StateMachine_GetStateString), (64 bytes).
    Removing statemachine.o(i.StateMachine_GetTick), (8 bytes).
    Removing statemachine.o(i.StateMachine_HandleRecordKey), (52 bytes).
    Removing statemachine.o(i.StateMachine_HandleTriggerKey), (46 bytes).
    Removing statemachine.o(i.StateMachine_Init), (136 bytes).
    Removing statemachine.o(i.StateMachine_IsMoving), (14 bytes).
    Removing statemachine.o(i.StateMachine_IsPointRecorded), (24 bytes).
    Removing statemachine.o(i.StateMachine_IsSystemReady), (14 bytes).
    Removing statemachine.o(i.StateMachine_IsTimeout), (16 bytes).
    Removing statemachine.o(i.StateMachine_MoveServoToPosition), (28 bytes).
    Removing statemachine.o(i.StateMachine_ReadCurrentPosition), (80 bytes).
    Removing statemachine.o(i.StateMachine_RecordPointA), (208 bytes).
    Removing statemachine.o(i.StateMachine_RecordPointB), (220 bytes).
    Removing statemachine.o(i.StateMachine_SetServoTorque), (26 bytes).
    Removing statemachine.o(i.StateMachine_StartAutoMovement), (124 bytes).
    Removing statemachine.o(i.StateMachine_StopAutoMovement), (68 bytes).
    Removing statemachine.o(i.StateMachine_ToggleDirection), (64 bytes).
    Removing statemachine.o(i.StateMachine_TransitionTo), (68 bytes).
    Removing statemachine.o(i.StateMachine_Update), (116 bytes).
    Removing statemachine.o(i.StateMachine_UpdateAutoMovement), (80 bytes).
    Removing statemachine.o(.bss), (88 bytes).
    Removing statemachine.o(.constdata), (16 bytes).
    Removing statemachine.o(.data), (2 bytes).
    Removing timer.o(i.Timer_ConfigureHardware), (94 bytes).
    Removing timer.o(i.Timer_DelayMs), (30 bytes).
    Removing timer.o(i.Timer_DisableCallback), (12 bytes).
    Removing timer.o(i.Timer_EnableCallback), (12 bytes).
    Removing timer.o(i.Timer_GetCpuUsage), (12 bytes).
    Removing timer.o(i.Timer_GetFrequency), (4 bytes).
    Removing timer.o(i.Timer_GetPeriodMs), (4 bytes).
    Removing timer.o(i.Timer_GetTick), (12 bytes).
    Removing timer.o(i.Timer_GetTimeMs), (16 bytes).
    Removing timer.o(i.Timer_Init), (44 bytes).
    Removing timer.o(i.Timer_IsDelayComplete), (16 bytes).
    Removing timer.o(i.Timer_IsPathStepReady), (12 bytes).
    Removing timer.o(i.Timer_IsRunning), (12 bytes).
    Removing timer.o(i.Timer_IsTimeout), (28 bytes).
    Removing timer.o(i.Timer_Reset), (44 bytes).
    Removing timer.o(i.Timer_ResetPathStep), (20 bytes).
    Removing timer.o(i.Timer_SetCallback), (12 bytes).
    Removing timer.o(i.Timer_Start), (24 bytes).
    Removing timer.o(i.Timer_StartPathMovement), (20 bytes).
    Removing timer.o(i.Timer_Stop), (24 bytes).
    Removing timer.o(i.Timer_StopPathMovement), (20 bytes).
    Removing timer.o(i.Timer_UpdateCpuUsage), (32 bytes).
    Removing manualrecord.o(i.ManualRecord_Cancel), (36 bytes).
    Removing manualrecord.o(i.ManualRecord_GetResult), (48 bytes).
    Removing manualrecord.o(i.ManualRecord_GetStateString), (116 bytes).
    Removing manualrecord.o(i.ManualRecord_Init), (52 bytes).
    Removing manualrecord.o(i.ManualRecord_IsComplete), (28 bytes).
    Removing manualrecord.o(i.ManualRecord_IsPointSaved), (24 bytes).
    Removing manualrecord.o(i.ManualRecord_IsPositionReasonable), (104 bytes).
    Removing manualrecord.o(i.ManualRecord_LoadPointData), (72 bytes).
    Removing manualrecord.o(i.ManualRecord_LoadServos), (40 bytes).
    Removing manualrecord.o(i.ManualRecord_ReadCurrentPosition), (72 bytes).
    Removing manualrecord.o(i.ManualRecord_SavePointData), (68 bytes).
    Removing manualrecord.o(i.ManualRecord_ShowInstructions), (152 bytes).
    Removing manualrecord.o(i.ManualRecord_ShowProgress), (200 bytes).
    Removing manualrecord.o(i.ManualRecord_Start), (56 bytes).
    Removing manualrecord.o(i.ManualRecord_TransitionTo), (136 bytes).
    Removing manualrecord.o(i.ManualRecord_UnloadServos), (34 bytes).
    Removing manualrecord.o(i.ManualRecord_Update), (268 bytes).
    Removing manualrecord.o(i.ManualRecord_ValidatePosition), (34 bytes).
    Removing manualrecord.o(i.ManualRecord_WaitUserAdjustment), (54 bytes).
    Removing manualrecord.o(.bss), (48 bytes).
    Removing manualrecord.o(.data), (3 bytes).
    Removing automovement.o(i.AutoMovement_ArePointsValid), (92 bytes).
    Removing automovement.o(i.AutoMovement_CheckSafety), (26 bytes).
    Removing automovement.o(i.AutoMovement_EmergencyStop), (38 bytes).
    Removing automovement.o(i.AutoMovement_ExecuteNextStep), (64 bytes).
    Removing automovement.o(i.AutoMovement_GetProgress), (54 bytes).
    Removing automovement.o(i.AutoMovement_GetStateString), (128 bytes).
    Removing automovement.o(i.AutoMovement_HandleDirectionChange), (108 bytes).
    Removing automovement.o(i.AutoMovement_HandleError), (110 bytes).
    Removing automovement.o(i.AutoMovement_HandleMovingToStart), (80 bytes).
    Removing automovement.o(i.AutoMovement_HandlePathMoving), (60 bytes).
    Removing automovement.o(i.AutoMovement_HandlePreparing), (72 bytes).
    Removing automovement.o(i.AutoMovement_Init), (92 bytes).
    Removing automovement.o(i.AutoMovement_IsActive), (14 bytes).
    Removing automovement.o(i.AutoMovement_LoadPointsFromRecord), (82 bytes).
    Removing automovement.o(i.AutoMovement_MoveToPosition), (28 bytes).
    Removing automovement.o(i.AutoMovement_ShowStatus), (300 bytes).
    Removing automovement.o(i.AutoMovement_Start), (88 bytes).
    Removing automovement.o(i.AutoMovement_Stop), (26 bytes).
    Removing automovement.o(i.AutoMovement_TransitionTo), (98 bytes).
    Removing automovement.o(i.AutoMovement_Update), (172 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_BackupConfiguration), (92 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_CalibrateServos), (364 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_CheckGeometry), (200 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_CheckKeys), (16 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_CheckMemory), (16 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_CheckServos), (376 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_CheckTimer), (120 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_ClearErrorLog), (30 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_GenerateReport), (204 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_GetConfig), (14 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_GetErrorCount), (14 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_GetLevelString), (76 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_GetModuleName), (136 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_Init), (84 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_LoadDefaultConfig), (38 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_LogError), (82 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_OptimizePerformance), (200 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_PrintDiagnostic), (160 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_QuickCheck), (208 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_RecordGeometryCalc), (14 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_RecordKeyPress), (14 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_RecordPathStep), (68 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_RecordServoCommand), (14 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_RecordServoError), (76 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_RecordStateTransition), (14 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_ResetStatistics), (44 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_RestoreConfiguration), (96 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_RunFullCheck), (432 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_SetConfig), (22 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_ShowErrors), (224 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_ShowPerformance), (140 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_ShowStatus), (180 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_Update), (50 bytes).
    Removing systemdiagnostics.o(i.SystemDiag_ValidateAccuracy), (392 bytes).
    Removing systemdiagnostics.o(.constdata), (32 bytes).
    Removing usart.o(i.USART1_ReceiveBuffer), (26 bytes).
    Removing usart.o(i.USART1_ReceiveByte), (32 bytes).
    Removing usart.o(i.USART1_SendByte), (48 bytes).
    Removing usart.o(i.USART1_WaitTransmitComplete), (24 bytes).
    Removing bluetooth.o(i.Bluetooth_ConfigureModule), (60 bytes).
    Removing bluetooth.o(i.Bluetooth_GetStatus), (12 bytes).
    Removing bluetooth.o(i.Bluetooth_Init), (80 bytes).
    Removing bluetooth.o(i.Bluetooth_ParseCommand), (196 bytes).
    Removing bluetooth.o(i.Bluetooth_PrintConnectionInfo), (204 bytes).
    Removing bluetooth.o(i.Bluetooth_ProcessReceivedData), (128 bytes).
    Removing bluetooth.o(i.Bluetooth_SendByte), (48 bytes).
    Removing bluetooth.o(i.Bluetooth_SendDiagnosticInfo), (148 bytes).
    Removing bluetooth.o(i.Bluetooth_SendErrorReport), (180 bytes).
    Removing bluetooth.o(i.Bluetooth_SendKeyAction), (244 bytes).
    Removing bluetooth.o(i.Bluetooth_SendPerformanceData), (64 bytes).
    Removing bluetooth.o(i.Bluetooth_SendPositionInfo), (100 bytes).
    Removing bluetooth.o(i.Bluetooth_SendStartupInfo), (40 bytes).
    Removing bluetooth.o(i.Bluetooth_SendString), (22 bytes).
    Removing bluetooth.o(i.Bluetooth_SetName), (40 bytes).
    Removing bluetooth.o(i.Bluetooth_SetPin), (40 bytes).
    Removing bluetooth.o(i.Bluetooth_TestConnection), (24 bytes).
    Removing bluetooth.o(i.Bluetooth_Update), (28 bytes).
    Removing bluetooth.o(i.USART2_Init), (168 bytes).
    Removing bluetooth.o(.conststring), (623 bytes).

392 unused section(s) (total 23002 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strstr.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fcheck1.s                       0x00000000   Number         0  fcheck1.o ABSOLUTE
    ../fplib/fcmpi.s                         0x00000000   Number         0  fcmpi.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/ffix.s                          0x00000000   Number         0  ffix.o ABSOLUTE
    ../fplib/ffixu.s                         0x00000000   Number         0  ffixu.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fleqf.s                         0x00000000   Number         0  fleqf.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/frleqf.s                        0x00000000   Number         0  frleqf.o ABSOLUTE
    ../fplib/frnd.s                          0x00000000   Number         0  frnd.o ABSOLUTE
    ../fplib/fsqrt.s                         0x00000000   Number         0  fsqrt.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbnf.s                       0x00000000   Number         0  scalbnf.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f_x.o ABSOLUTE
    ../mathlib/atanf.c                       0x00000000   Number         0  atanf.o ABSOLUTE
    ../mathlib/atanf.c                       0x00000000   Number         0  atanf_x.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_x.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    ../mathlib/tanf.c                        0x00000000   Number         0  tanf.o ABSOLUTE
    ../mathlib/tanf.c                        0x00000000   Number         0  tanf_x.o ABSOLUTE
    Hardware\AutoMovement.c                  0x00000000   Number         0  automovement.o ABSOLUTE
    Hardware\Bluetooth.c                     0x00000000   Number         0  bluetooth.o ABSOLUTE
    Hardware\Geometry.c                      0x00000000   Number         0  geometry.o ABSOLUTE
    Hardware\Key.c                           0x00000000   Number         0  key.o ABSOLUTE
    Hardware\LED.c                           0x00000000   Number         0  led.o ABSOLUTE
    Hardware\ManualRecord.c                  0x00000000   Number         0  manualrecord.o ABSOLUTE
    Hardware\OLED.c                          0x00000000   Number         0  oled.o ABSOLUTE
    Hardware\Servo.c                         0x00000000   Number         0  servo.o ABSOLUTE
    Hardware\StateMachine.c                  0x00000000   Number         0  statemachine.o ABSOLUTE
    Hardware\SystemDiagnostics.c             0x00000000   Number         0  systemdiagnostics.o ABSOLUTE
    Hardware\Timer.c                         0x00000000   Number         0  timer.o ABSOLUTE
    Hardware\usart.c                         0x00000000   Number         0  usart.o ABSOLUTE
    Library\misc.c                           0x00000000   Number         0  misc.o ABSOLUTE
    Library\stm32f10x_exti.c                 0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    Library\stm32f10x_gpio.c                 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    Library\stm32f10x_i2c.c                  0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    Library\stm32f10x_rcc.c                  0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    Library\stm32f10x_tim.c                  0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    Library\stm32f10x_usart.c                0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    Start\\core_cm3.c                        0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\core_cm3.c                         0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\startup_stm32f10x_md.s             0x00000000   Number         0  startup_stm32f10x_md.o ABSOLUTE
    Start\system_stm32f10x.c                 0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    System\Delay.c                           0x00000000   Number         0  delay.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\stm32f10x_it.c                      0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f10x_md.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000128   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000144   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x08000160   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x08000160   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000017  0x08000166   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x0800016a   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x0800016c   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000172   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000172   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000172   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000172   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x0800017c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800017c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800017c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800017c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800017c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800017c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800017c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800017c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800017c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800017c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800017c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800017c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800017c   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x0800017e   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000180   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000180   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000180   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000180   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000180   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000180   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000180   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000182   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000182   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000182   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000188   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000188   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800018c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800018c   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000194   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000196   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000196   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800019a   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001a0   Section       64  startup_stm32f10x_md.o(.text)
    .text                                    0x080001e0   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x08000208   Section        0  __printf_ss_wp.o(.text)
    .text                                    0x08000368   Section        0  heapauxi.o(.text)
    .text                                    0x0800036e   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000371   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x0800078c   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x0800078d   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x080007bc   Section        0  _sputc.o(.text)
    .text                                    0x080007c8   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x080007d0   Section      138  lludiv10.o(.text)
    .text                                    0x0800085c   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x080008dc   Section        0  bigflt0.o(.text)
    .text                                    0x080009c0   Section        8  libspace.o(.text)
    .text                                    0x080009c8   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000a12   Section        0  exit.o(.text)
    .text                                    0x08000a24   Section      128  strcmpv7m.o(.text)
    .text                                    0x08000aa4   Section        0  sys_exit.o(.text)
    .text                                    0x08000ab0   Section        2  use_no_semi.o(.text)
    .text                                    0x08000ab2   Section        0  indicate_semi.o(.text)
    CL$$btod_d2e                             0x08000ab2   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08000af0   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08000b36   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08000b96   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08000ece   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08000faa   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08000fd4   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08000ffe   Section      580  btod.o(CL$$btod_mult_common)
    i.BusFault_Handler                       0x08001242   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x08001246   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.Delay_ms                               0x08001248   Section        0  delay.o(i.Delay_ms)
    i.Delay_us                               0x08001260   Section        0  delay.o(i.Delay_us)
    i.GPIO_Init                              0x0800128e   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_ReadInputDataBit                  0x080013a4   Section        0  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x080013b6   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x080013ba   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.GPIO_WriteBit                          0x080013be   Section        0  stm32f10x_gpio.o(i.GPIO_WriteBit)
    i.HardFault_Handler                      0x080013c8   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.Key_ClearEvent                         0x080013cc   Section        0  key.o(i.Key_ClearEvent)
    i.Key_Init                               0x080013f0   Section        0  key.o(i.Key_Init)
    i.Key_IsClicked                          0x08001470   Section        0  key.o(i.Key_IsClicked)
    i.Key_ReadPin                            0x08001490   Section        0  key.o(i.Key_ReadPin)
    i.Key_Scan                               0x080014bc   Section        0  key.o(i.Key_Scan)
    i.Key_UpdateState                        0x080014cc   Section        0  key.o(i.Key_UpdateState)
    i.LED_Init                               0x08001534   Section        0  led.o(i.LED_Init)
    i.LED_OFF                                0x08001568   Section        0  led.o(i.LED_OFF)
    i.LED_ON                                 0x08001578   Section        0  led.o(i.LED_ON)
    i.MemManage_Handler                      0x08001588   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x0800158c   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.OLED_Clear                             0x0800158e   Section        0  oled.o(i.OLED_Clear)
    i.OLED_I2C_Init                          0x080015b8   Section        0  oled.o(i.OLED_I2C_Init)
    i.OLED_I2C_SendByte                      0x08001608   Section        0  oled.o(i.OLED_I2C_SendByte)
    i.OLED_I2C_Start                         0x08001664   Section        0  oled.o(i.OLED_I2C_Start)
    i.OLED_I2C_Stop                          0x08001698   Section        0  oled.o(i.OLED_I2C_Stop)
    i.OLED_Init                              0x080016c0   Section        0  oled.o(i.OLED_Init)
    i.OLED_SetCursor                         0x0800176e   Section        0  oled.o(i.OLED_SetCursor)
    i.OLED_ShowChar                          0x08001790   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowString                        0x08001804   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_WriteCommand                      0x0800182c   Section        0  oled.o(i.OLED_WriteCommand)
    i.OLED_WriteData                         0x0800184c   Section        0  oled.o(i.OLED_WriteData)
    i.PendSV_Handler                         0x0800186c   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_APB2PeriphClockCmd                 0x08001870   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x08001890   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x08001964   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.Servo_AngleToPosition                  0x08001968   Section        0  servo.o(i.Servo_AngleToPosition)
    i.Servo_CalculateChecksum                0x080019a8   Section        0  servo.o(i.Servo_CalculateChecksum)
    i.Servo_Init                             0x080019c6   Section        0  servo.o(i.Servo_Init)
    i.Servo_SendCommand                      0x080019d4   Section        0  servo.o(i.Servo_SendCommand)
    i.Servo_SetPosition                      0x08001a54   Section        0  servo.o(i.Servo_SetPosition)
    i.Servo_SetPositionWithTime              0x08001a68   Section        0  servo.o(i.Servo_SetPositionWithTime)
    i.SetSysClock                            0x08001a9e   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x08001a9f   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x08001aa8   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x08001aa9   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.SysTick_Handler                        0x08001b88   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08001b8c   Section        0  system_stm32f10x.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x08001bec   Section        0  stm32f10x_it.o(i.TIM2_IRQHandler)
    i.TIM_ClearITPendingBit                  0x08001bf4   Section        0  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_GetITStatus                        0x08001bfa   Section        0  stm32f10x_tim.o(i.TIM_GetITStatus)
    i.Timer_IRQ_Handler                      0x08001c1c   Section        0  timer.o(i.Timer_IRQ_Handler)
    i.USART1_Init                            0x08001ccc   Section        0  usart.o(i.USART1_Init)
    i.USART1_SendBuffer                      0x08001d4c   Section        0  usart.o(i.USART1_SendBuffer)
    i.USART2_IRQHandler                      0x08001da0   Section        0  bluetooth.o(i.USART2_IRQHandler)
    i.USART_ClearITPendingBit                0x08001dfc   Section        0  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    i.USART_Cmd                              0x08001e1a   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetFlagStatus                    0x08001e32   Section        0  stm32f10x_usart.o(i.USART_GetFlagStatus)
    i.USART_GetITStatus                      0x08001e4c   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_Init                             0x08001ea0   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08001f78   Section        0  stm32f10x_usart.o(i.USART_ReceiveData)
    i.USART_SendData                         0x08001f82   Section        0  stm32f10x_usart.o(i.USART_SendData)
    i.UsageFault_Handler                     0x08001f8a   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x08001f8e   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i._is_digit                              0x08001fb6   Section        0  __printf_wp.o(i._is_digit)
    i.main                                   0x08001fc4   Section        0  main.o(i.main)
    locale$$code                             0x08002188   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$dretinf                            0x080021b4   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$f2d                                0x080021c0   Section       86  f2d.o(x$fpl$f2d)
    x$fpl$fadd                               0x08002218   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x08002227   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$fcmpinf                            0x080022dc   Section       24  fcmpi.o(x$fpl$fcmpinf)
    x$fpl$fdiv                               0x080022f4   Section      388  fdiv.o(x$fpl$fdiv)
    _fdiv1                                   0x080022f5   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    x$fpl$ffixu                              0x08002478   Section       62  ffixu.o(x$fpl$ffixu)
    x$fpl$fleqf                              0x080024b8   Section      104  fleqf.o(x$fpl$fleqf)
    x$fpl$fmul                               0x08002520   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x08002622   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x080026ae   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$frleqf                             0x080026b8   Section       98  frleqf.o(x$fpl$frleqf)
    x$fpl$fsub                               0x0800271c   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x0800272b   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    x$fpl$printf1                            0x08002806   Section        4  printf1.o(x$fpl$printf1)
    .constdata                               0x0800280a   Section     1520  oled.o(.constdata)
    x$fpl$usenofp                            0x0800280a   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x08002dfc   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x08002dfc   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x08002e38   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x08002eb0   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x08002eb4   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08002ebc   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08002ec8   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x08002eca   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x08002ecb   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x08002ecc   Data           0  lc_numeric_c.o(locale$$data)
    .data                                    0x20000000   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000000   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000010   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x20000014   Section       20  timer.o(.data)
    path_step_counter                        0x20000014   Data           4  timer.o(.data)
    path_step_ready                          0x20000018   Data           1  timer.o(.data)
    cpu_idle_count                           0x2000001c   Data           4  timer.o(.data)
    cpu_total_count                          0x20000020   Data           4  timer.o(.data)
    cpu_usage_percent                        0x20000024   Data           4  timer.o(.data)
    .data                                    0x20000028   Section        8  main.o(.data)
    .bss                                     0x20000030   Section       30  key.o(.bss)
    key_states                               0x20000030   Data          30  key.o(.bss)
    .bss                                     0x20000050   Section       20  timer.o(.bss)
    timer_state                              0x20000050   Data          20  timer.o(.bss)
    .bss                                     0x20000064   Section      932  bluetooth.o(.bss)
    .bss                                     0x20000408   Section       96  libspace.o(.bss)
    HEAP                                     0x20000468   Section      512  startup_stm32f10x_md.o(HEAP)
    Heap_Mem                                 0x20000468   Data         512  startup_stm32f10x_md.o(HEAP)
    STACK                                    0x20000668   Section     1024  startup_stm32f10x_md.o(STACK)
    Stack_Mem                                0x20000668   Data        1024  startup_stm32f10x_md.o(STACK)
    __initial_sp                             0x20000a68   Data           0  startup_stm32f10x_md.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f10x_md.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_md.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f10x_md.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000103   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000129   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000145   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x08000161   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x08000161   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_percent_end                      0x08000167   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x0800016b   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_heap_1                     0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000173   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000173   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000173   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000173   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x0800017d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800017d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800017d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800017d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800017d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800017d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x0800017d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800017d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x0800017d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800017d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x0800017d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800017d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800017d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x0800017f   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000181   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000181   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000181   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000181   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000181   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000181   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000181   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000183   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000183   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000183   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000189   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000189   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800018d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800018d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000195   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000197   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000197   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800019b   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080001a1   Thumb Code     8  startup_stm32f10x_md.o(.text)
    ADC1_2_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_RX1_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_SCE_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI0_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI15_10_IRQHandler                     0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI1_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI2_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI3_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI4_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI9_5_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    FLASH_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    PVD_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RCC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTCAlarm_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI1_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI2_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TAMPER_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_BRK_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_CC_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_UP_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM3_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM4_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART1_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART3_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USBWakeUp_IRQHandler                     0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    WWDG_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __user_initial_stackheap                 0x080001bd   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __2sprintf                               0x080001e1   Thumb Code    34  noretval__2sprintf.o(.text)
    __printf                                 0x08000209   Thumb Code   352  __printf_ss_wp.o(.text)
    __use_two_region_memory                  0x08000369   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800036b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800036d   Thumb Code     2  heapauxi.o(.text)
    __lib_sel_fp_printf                      0x0800036f   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000521   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08000797   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x080007bd   Thumb Code    10  _sputc.o(.text)
    __rt_locale                              0x080007c9   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _ll_udiv10                               0x080007d1   Thumb Code   138  lludiv10.o(.text)
    _printf_fp_infnan                        0x0800085d   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x080008dd   Thumb Code   224  bigflt0.o(.text)
    __user_libspace                          0x080009c1   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080009c1   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080009c1   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x080009c9   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000a13   Thumb Code    18  exit.o(.text)
    strcmp                                   0x08000a25   Thumb Code   128  strcmpv7m.o(.text)
    _sys_exit                                0x08000aa5   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08000ab1   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000ab1   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08000ab3   Thumb Code     0  indicate_semi.o(.text)
    _btod_d2e                                0x08000ab3   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08000af1   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08000b37   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08000b97   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08000ecf   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08000fab   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08000fd5   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08000fff   Thumb Code   580  btod.o(CL$$btod_mult_common)
    BusFault_Handler                         0x08001243   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x08001247   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    Delay_ms                                 0x08001249   Thumb Code    24  delay.o(i.Delay_ms)
    Delay_us                                 0x08001261   Thumb Code    46  delay.o(i.Delay_us)
    GPIO_Init                                0x0800128f   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_ReadInputDataBit                    0x080013a5   Thumb Code    18  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x080013b7   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x080013bb   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    GPIO_WriteBit                            0x080013bf   Thumb Code    10  stm32f10x_gpio.o(i.GPIO_WriteBit)
    HardFault_Handler                        0x080013c9   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    Key_ClearEvent                           0x080013cd   Thumb Code    32  key.o(i.Key_ClearEvent)
    Key_Init                                 0x080013f1   Thumb Code   120  key.o(i.Key_Init)
    Key_IsClicked                            0x08001471   Thumb Code    28  key.o(i.Key_IsClicked)
    Key_ReadPin                              0x08001491   Thumb Code    38  key.o(i.Key_ReadPin)
    Key_Scan                                 0x080014bd   Thumb Code    16  key.o(i.Key_Scan)
    Key_UpdateState                          0x080014cd   Thumb Code   100  key.o(i.Key_UpdateState)
    LED_Init                                 0x08001535   Thumb Code    46  led.o(i.LED_Init)
    LED_OFF                                  0x08001569   Thumb Code    12  led.o(i.LED_OFF)
    LED_ON                                   0x08001579   Thumb Code    12  led.o(i.LED_ON)
    MemManage_Handler                        0x08001589   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    NMI_Handler                              0x0800158d   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    OLED_Clear                               0x0800158f   Thumb Code    42  oled.o(i.OLED_Clear)
    OLED_I2C_Init                            0x080015b9   Thumb Code    76  oled.o(i.OLED_I2C_Init)
    OLED_I2C_SendByte                        0x08001609   Thumb Code    88  oled.o(i.OLED_I2C_SendByte)
    OLED_I2C_Start                           0x08001665   Thumb Code    48  oled.o(i.OLED_I2C_Start)
    OLED_I2C_Stop                            0x08001699   Thumb Code    36  oled.o(i.OLED_I2C_Stop)
    OLED_Init                                0x080016c1   Thumb Code   174  oled.o(i.OLED_Init)
    OLED_SetCursor                           0x0800176f   Thumb Code    34  oled.o(i.OLED_SetCursor)
    OLED_ShowChar                            0x08001791   Thumb Code   110  oled.o(i.OLED_ShowChar)
    OLED_ShowString                          0x08001805   Thumb Code    40  oled.o(i.OLED_ShowString)
    OLED_WriteCommand                        0x0800182d   Thumb Code    32  oled.o(i.OLED_WriteCommand)
    OLED_WriteData                           0x0800184d   Thumb Code    32  oled.o(i.OLED_WriteData)
    PendSV_Handler                           0x0800186d   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_APB2PeriphClockCmd                   0x08001871   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x08001891   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x08001965   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    Servo_AngleToPosition                    0x08001969   Thumb Code    54  servo.o(i.Servo_AngleToPosition)
    Servo_CalculateChecksum                  0x080019a9   Thumb Code    30  servo.o(i.Servo_CalculateChecksum)
    Servo_Init                               0x080019c7   Thumb Code    14  servo.o(i.Servo_Init)
    Servo_SendCommand                        0x080019d5   Thumb Code   128  servo.o(i.Servo_SendCommand)
    Servo_SetPosition                        0x08001a55   Thumb Code    20  servo.o(i.Servo_SetPosition)
    Servo_SetPositionWithTime                0x08001a69   Thumb Code    54  servo.o(i.Servo_SetPositionWithTime)
    SysTick_Handler                          0x08001b89   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x08001b8d   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    TIM2_IRQHandler                          0x08001bed   Thumb Code     8  stm32f10x_it.o(i.TIM2_IRQHandler)
    TIM_ClearITPendingBit                    0x08001bf5   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    TIM_GetITStatus                          0x08001bfb   Thumb Code    34  stm32f10x_tim.o(i.TIM_GetITStatus)
    Timer_IRQ_Handler                        0x08001c1d   Thumb Code   150  timer.o(i.Timer_IRQ_Handler)
    USART1_Init                              0x08001ccd   Thumb Code   120  usart.o(i.USART1_Init)
    USART1_SendBuffer                        0x08001d4d   Thumb Code    80  usart.o(i.USART1_SendBuffer)
    USART2_IRQHandler                        0x08001da1   Thumb Code    84  bluetooth.o(i.USART2_IRQHandler)
    USART_ClearITPendingBit                  0x08001dfd   Thumb Code    30  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    USART_Cmd                                0x08001e1b   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetFlagStatus                      0x08001e33   Thumb Code    26  stm32f10x_usart.o(i.USART_GetFlagStatus)
    USART_GetITStatus                        0x08001e4d   Thumb Code    84  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_Init                               0x08001ea1   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08001f79   Thumb Code    10  stm32f10x_usart.o(i.USART_ReceiveData)
    USART_SendData                           0x08001f83   Thumb Code     8  stm32f10x_usart.o(i.USART_SendData)
    UsageFault_Handler                       0x08001f8b   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x08001f8f   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    _is_digit                                0x08001fb7   Thumb Code    14  __printf_wp.o(i._is_digit)
    main                                     0x08001fc5   Thumb Code   348  main.o(i.main)
    _get_lc_numeric                          0x08002189   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __fpl_dretinf                            0x080021b5   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_f2d                              0x080021c1   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x080021c1   Thumb Code    86  f2d.o(x$fpl$f2d)
    __aeabi_fadd                             0x08002219   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x08002219   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __fpl_fcmp_Inf                           0x080022dd   Thumb Code    24  fcmpi.o(x$fpl$fcmpinf)
    __aeabi_fdiv                             0x080022f5   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    _fdiv                                    0x080022f5   Thumb Code   384  fdiv.o(x$fpl$fdiv)
    __aeabi_f2uiz                            0x08002479   Thumb Code     0  ffixu.o(x$fpl$ffixu)
    _ffixu                                   0x08002479   Thumb Code    62  ffixu.o(x$fpl$ffixu)
    __aeabi_cfcmple                          0x080024b9   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    _fcmple                                  0x080024b9   Thumb Code   104  fleqf.o(x$fpl$fleqf)
    __fpl_fcmple_InfNaN                      0x0800250b   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    __aeabi_fmul                             0x08002521   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x08002521   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x08002623   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x080026af   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_cfrcmple                         0x080026b9   Thumb Code     0  frleqf.o(x$fpl$frleqf)
    _frcmple                                 0x080026b9   Thumb Code    98  frleqf.o(x$fpl$frleqf)
    __aeabi_fsub                             0x0800271d   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x0800271d   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    _printf_fp_dec                           0x08002807   Thumb Code     4  printf1.o(x$fpl$printf1)
    OLED_F8x16                               0x0800280a   Data        1520  oled.o(.constdata)
    __I$use$fp                               0x0800280a   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x08002e90   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08002eb0   Number         0  anon$$obj.o(Region$$Table)
    servo1_angle                             0x20000028   Data           4  main.o(.data)
    servo2_angle                             0x2000002c   Data           4  main.o(.data)
    bluetooth_ctrl                           0x20000064   Data         932  bluetooth.o(.bss)
    __libspace_start                         0x20000408   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000468   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00002efc, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00002ecc, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO            3    RESET               startup_stm32f10x_md.o
    0x080000ec   0x080000ec   0x00000008   Code   RO         3073  * !!!main             c_w.l(__main.o)
    0x080000f4   0x080000f4   0x00000034   Code   RO         3401    !!!scatter          c_w.l(__scatter.o)
    0x08000128   0x08000128   0x0000001a   Code   RO         3403    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000142   0x08000142   0x00000002   PAD
    0x08000144   0x08000144   0x0000001c   Code   RO         3405    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000160   0x08000160   0x00000000   Code   RO         3062    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x08000160   0x08000160   0x00000006   Code   RO         3061    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000166   0x08000166   0x00000004   Code   RO         3150    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x0800016a   0x0800016a   0x00000002   Code   RO         3268    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800016c   0x0800016c   0x00000000   Code   RO         3270    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x0800016c   0x0800016c   0x00000000   Code   RO         3272    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x0800016c   0x0800016c   0x00000000   Code   RO         3275    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x0800016c   0x0800016c   0x00000000   Code   RO         3277    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x0800016c   0x0800016c   0x00000000   Code   RO         3279    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x0800016c   0x0800016c   0x00000006   Code   RO         3280    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000172   0x08000172   0x00000000   Code   RO         3282    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000172   0x08000172   0x00000000   Code   RO         3284    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000172   0x08000172   0x00000000   Code   RO         3286    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000172   0x08000172   0x0000000a   Code   RO         3287    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x0800017c   0x0800017c   0x00000000   Code   RO         3288    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800017c   0x0800017c   0x00000000   Code   RO         3290    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800017c   0x0800017c   0x00000000   Code   RO         3292    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800017c   0x0800017c   0x00000000   Code   RO         3294    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800017c   0x0800017c   0x00000000   Code   RO         3296    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800017c   0x0800017c   0x00000000   Code   RO         3298    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800017c   0x0800017c   0x00000000   Code   RO         3300    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800017c   0x0800017c   0x00000000   Code   RO         3302    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800017c   0x0800017c   0x00000000   Code   RO         3306    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800017c   0x0800017c   0x00000000   Code   RO         3308    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800017c   0x0800017c   0x00000000   Code   RO         3310    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800017c   0x0800017c   0x00000000   Code   RO         3312    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800017c   0x0800017c   0x00000002   Code   RO         3313    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x0800017e   0x0800017e   0x00000002   Code   RO         3349    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000180   0x08000180   0x00000000   Code   RO         3360    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000180   0x08000180   0x00000000   Code   RO         3362    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000180   0x08000180   0x00000000   Code   RO         3365    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000180   0x08000180   0x00000000   Code   RO         3368    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000180   0x08000180   0x00000000   Code   RO         3370    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000180   0x08000180   0x00000000   Code   RO         3373    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000180   0x08000180   0x00000002   Code   RO         3374    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000182   0x08000182   0x00000000   Code   RO         3137    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000182   0x08000182   0x00000000   Code   RO         3190    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000182   0x08000182   0x00000006   Code   RO         3202    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000188   0x08000188   0x00000000   Code   RO         3192    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000188   0x08000188   0x00000004   Code   RO         3193    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800018c   0x0800018c   0x00000000   Code   RO         3195    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800018c   0x0800018c   0x00000008   Code   RO         3196    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000194   0x08000194   0x00000002   Code   RO         3316    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000196   0x08000196   0x00000000   Code   RO         3325    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000196   0x08000196   0x00000004   Code   RO         3326    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800019a   0x0800019a   0x00000006   Code   RO         3327    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001a0   0x080001a0   0x00000040   Code   RO            4    .text               startup_stm32f10x_md.o
    0x080001e0   0x080001e0   0x00000028   Code   RO         3029    .text               c_w.l(noretval__2sprintf.o)
    0x08000208   0x08000208   0x00000160   Code   RO         3052    .text               c_w.l(__printf_ss_wp.o)
    0x08000368   0x08000368   0x00000006   Code   RO         3071    .text               c_w.l(heapauxi.o)
    0x0800036e   0x0800036e   0x0000041e   Code   RO         3142    .text               c_w.l(_printf_fp_dec.o)
    0x0800078c   0x0800078c   0x00000030   Code   RO         3144    .text               c_w.l(_printf_char_common.o)
    0x080007bc   0x080007bc   0x0000000a   Code   RO         3146    .text               c_w.l(_sputc.o)
    0x080007c6   0x080007c6   0x00000002   PAD
    0x080007c8   0x080007c8   0x00000008   Code   RO         3207    .text               c_w.l(rt_locale_intlibspace.o)
    0x080007d0   0x080007d0   0x0000008a   Code   RO         3214    .text               c_w.l(lludiv10.o)
    0x0800085a   0x0800085a   0x00000002   PAD
    0x0800085c   0x0800085c   0x00000080   Code   RO         3216    .text               c_w.l(_printf_fp_infnan.o)
    0x080008dc   0x080008dc   0x000000e4   Code   RO         3220    .text               c_w.l(bigflt0.o)
    0x080009c0   0x080009c0   0x00000008   Code   RO         3254    .text               c_w.l(libspace.o)
    0x080009c8   0x080009c8   0x0000004a   Code   RO         3257    .text               c_w.l(sys_stackheap_outer.o)
    0x08000a12   0x08000a12   0x00000012   Code   RO         3259    .text               c_w.l(exit.o)
    0x08000a24   0x08000a24   0x00000080   Code   RO         3261    .text               c_w.l(strcmpv7m.o)
    0x08000aa4   0x08000aa4   0x0000000c   Code   RO         3339    .text               c_w.l(sys_exit.o)
    0x08000ab0   0x08000ab0   0x00000002   Code   RO         3350    .text               c_w.l(use_no_semi.o)
    0x08000ab2   0x08000ab2   0x00000000   Code   RO         3352    .text               c_w.l(indicate_semi.o)
    0x08000ab2   0x08000ab2   0x0000003e   Code   RO         3223    CL$$btod_d2e        c_w.l(btod.o)
    0x08000af0   0x08000af0   0x00000046   Code   RO         3225    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08000b36   0x08000b36   0x00000060   Code   RO         3224    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08000b96   0x08000b96   0x00000338   Code   RO         3233    CL$$btod_div_common  c_w.l(btod.o)
    0x08000ece   0x08000ece   0x000000dc   Code   RO         3230    CL$$btod_e2e        c_w.l(btod.o)
    0x08000faa   0x08000faa   0x0000002a   Code   RO         3227    CL$$btod_ediv       c_w.l(btod.o)
    0x08000fd4   0x08000fd4   0x0000002a   Code   RO         3226    CL$$btod_emul       c_w.l(btod.o)
    0x08000ffe   0x08000ffe   0x00000244   Code   RO         3232    CL$$btod_mult_common  c_w.l(btod.o)
    0x08001242   0x08001242   0x00000004   Code   RO         2940    i.BusFault_Handler  stm32f10x_it.o
    0x08001246   0x08001246   0x00000002   Code   RO         2941    i.DebugMon_Handler  stm32f10x_it.o
    0x08001248   0x08001248   0x00000018   Code   RO         1463    i.Delay_ms          delay.o
    0x08001260   0x08001260   0x0000002e   Code   RO         1465    i.Delay_us          delay.o
    0x0800128e   0x0800128e   0x00000116   Code   RO          208    i.GPIO_Init         stm32f10x_gpio.o
    0x080013a4   0x080013a4   0x00000012   Code   RO          212    i.GPIO_ReadInputDataBit  stm32f10x_gpio.o
    0x080013b6   0x080013b6   0x00000004   Code   RO          215    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x080013ba   0x080013ba   0x00000004   Code   RO          216    i.GPIO_SetBits      stm32f10x_gpio.o
    0x080013be   0x080013be   0x0000000a   Code   RO          219    i.GPIO_WriteBit     stm32f10x_gpio.o
    0x080013c8   0x080013c8   0x00000004   Code   RO         2942    i.HardFault_Handler  stm32f10x_it.o
    0x080013cc   0x080013cc   0x00000024   Code   RO         1517    i.Key_ClearEvent    key.o
    0x080013f0   0x080013f0   0x00000080   Code   RO         1520    i.Key_Init          key.o
    0x08001470   0x08001470   0x00000020   Code   RO         1521    i.Key_IsClicked     key.o
    0x08001490   0x08001490   0x0000002c   Code   RO         1524    i.Key_ReadPin       key.o
    0x080014bc   0x080014bc   0x00000010   Code   RO         1525    i.Key_Scan          key.o
    0x080014cc   0x080014cc   0x00000068   Code   RO         1526    i.Key_UpdateState   key.o
    0x08001534   0x08001534   0x00000034   Code   RO         1487    i.LED_Init          led.o
    0x08001568   0x08001568   0x00000010   Code   RO         1488    i.LED_OFF           led.o
    0x08001578   0x08001578   0x00000010   Code   RO         1489    i.LED_ON            led.o
    0x08001588   0x08001588   0x00000004   Code   RO         2943    i.MemManage_Handler  stm32f10x_it.o
    0x0800158c   0x0800158c   0x00000002   Code   RO         2944    i.NMI_Handler       stm32f10x_it.o
    0x0800158e   0x0800158e   0x0000002a   Code   RO         1591    i.OLED_Clear        oled.o
    0x080015b8   0x080015b8   0x00000050   Code   RO         1592    i.OLED_I2C_Init     oled.o
    0x08001608   0x08001608   0x0000005c   Code   RO         1593    i.OLED_I2C_SendByte  oled.o
    0x08001664   0x08001664   0x00000034   Code   RO         1594    i.OLED_I2C_Start    oled.o
    0x08001698   0x08001698   0x00000028   Code   RO         1595    i.OLED_I2C_Stop     oled.o
    0x080016c0   0x080016c0   0x000000ae   Code   RO         1596    i.OLED_Init         oled.o
    0x0800176e   0x0800176e   0x00000022   Code   RO         1598    i.OLED_SetCursor    oled.o
    0x08001790   0x08001790   0x00000074   Code   RO         1600    i.OLED_ShowChar     oled.o
    0x08001804   0x08001804   0x00000028   Code   RO         1604    i.OLED_ShowString   oled.o
    0x0800182c   0x0800182c   0x00000020   Code   RO         1605    i.OLED_WriteCommand  oled.o
    0x0800184c   0x0800184c   0x00000020   Code   RO         1606    i.OLED_WriteData    oled.o
    0x0800186c   0x0800186c   0x00000002   Code   RO         2945    i.PendSV_Handler    stm32f10x_it.o
    0x0800186e   0x0800186e   0x00000002   PAD
    0x08001870   0x08001870   0x00000020   Code   RO          530    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08001890   0x08001890   0x000000d4   Code   RO          538    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x08001964   0x08001964   0x00000002   Code   RO         2946    i.SVC_Handler       stm32f10x_it.o
    0x08001966   0x08001966   0x00000002   PAD
    0x08001968   0x08001968   0x00000040   Code   RO         1699    i.Servo_AngleToPosition  servo.o
    0x080019a8   0x080019a8   0x0000001e   Code   RO         1700    i.Servo_CalculateChecksum  servo.o
    0x080019c6   0x080019c6   0x0000000e   Code   RO         1702    i.Servo_Init        servo.o
    0x080019d4   0x080019d4   0x00000080   Code   RO         1708    i.Servo_SendCommand  servo.o
    0x08001a54   0x08001a54   0x00000014   Code   RO         1709    i.Servo_SetPosition  servo.o
    0x08001a68   0x08001a68   0x00000036   Code   RO         1710    i.Servo_SetPositionWithTime  servo.o
    0x08001a9e   0x08001a9e   0x00000008   Code   RO           24    i.SetSysClock       system_stm32f10x.o
    0x08001aa6   0x08001aa6   0x00000002   PAD
    0x08001aa8   0x08001aa8   0x000000e0   Code   RO           25    i.SetSysClockTo72   system_stm32f10x.o
    0x08001b88   0x08001b88   0x00000002   Code   RO         2947    i.SysTick_Handler   stm32f10x_it.o
    0x08001b8a   0x08001b8a   0x00000002   PAD
    0x08001b8c   0x08001b8c   0x00000060   Code   RO           27    i.SystemInit        system_stm32f10x.o
    0x08001bec   0x08001bec   0x00000008   Code   RO         2948    i.TIM2_IRQHandler   stm32f10x_it.o
    0x08001bf4   0x08001bf4   0x00000006   Code   RO          740    i.TIM_ClearITPendingBit  stm32f10x_tim.o
    0x08001bfa   0x08001bfa   0x00000022   Code   RO          766    i.TIM_GetITStatus   stm32f10x_tim.o
    0x08001c1c   0x08001c1c   0x000000b0   Code   RO         2128    i.Timer_IRQ_Handler  timer.o
    0x08001ccc   0x08001ccc   0x00000080   Code   RO         2739    i.USART1_Init       usart.o
    0x08001d4c   0x08001d4c   0x00000054   Code   RO         2742    i.USART1_SendBuffer  usart.o
    0x08001da0   0x08001da0   0x0000005c   Code   RO         2799    i.USART2_IRQHandler  bluetooth.o
    0x08001dfc   0x08001dfc   0x0000001e   Code   RO         1281    i.USART_ClearITPendingBit  stm32f10x_usart.o
    0x08001e1a   0x08001e1a   0x00000018   Code   RO         1284    i.USART_Cmd         stm32f10x_usart.o
    0x08001e32   0x08001e32   0x0000001a   Code   RO         1287    i.USART_GetFlagStatus  stm32f10x_usart.o
    0x08001e4c   0x08001e4c   0x00000054   Code   RO         1288    i.USART_GetITStatus  stm32f10x_usart.o
    0x08001ea0   0x08001ea0   0x000000d8   Code   RO         1291    i.USART_Init        stm32f10x_usart.o
    0x08001f78   0x08001f78   0x0000000a   Code   RO         1298    i.USART_ReceiveData  stm32f10x_usart.o
    0x08001f82   0x08001f82   0x00000008   Code   RO         1301    i.USART_SendData    stm32f10x_usart.o
    0x08001f8a   0x08001f8a   0x00000004   Code   RO         2949    i.UsageFault_Handler  stm32f10x_it.o
    0x08001f8e   0x08001f8e   0x00000028   Code   RO         3252    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x08001fb6   0x08001fb6   0x0000000e   Code   RO         3047    i._is_digit         c_w.l(__printf_wp.o)
    0x08001fc4   0x08001fc4   0x000001c4   Code   RO         2913    i.main              main.o
    0x08002188   0x08002188   0x0000002c   Code   RO         3246    locale$$code        c_w.l(lc_numeric_c.o)
    0x080021b4   0x080021b4   0x0000000c   Code   RO         3153    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x080021c0   0x080021c0   0x00000056   Code   RO         3075    x$fpl$f2d           fz_ws.l(f2d.o)
    0x08002216   0x08002216   0x00000002   PAD
    0x08002218   0x08002218   0x000000c4   Code   RO         3077    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x080022dc   0x080022dc   0x00000018   Code   RO         3155    x$fpl$fcmpinf       fz_ws.l(fcmpi.o)
    0x080022f4   0x080022f4   0x00000184   Code   RO         3084    x$fpl$fdiv          fz_ws.l(fdiv.o)
    0x08002478   0x08002478   0x0000003e   Code   RO         3087    x$fpl$ffixu         fz_ws.l(ffixu.o)
    0x080024b6   0x080024b6   0x00000002   PAD
    0x080024b8   0x080024b8   0x00000068   Code   RO         3097    x$fpl$fleqf         fz_ws.l(fleqf.o)
    0x08002520   0x08002520   0x00000102   Code   RO         3099    x$fpl$fmul          fz_ws.l(fmul.o)
    0x08002622   0x08002622   0x0000008c   Code   RO         3161    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x080026ae   0x080026ae   0x0000000a   Code   RO         3163    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x080026b8   0x080026b8   0x00000062   Code   RO         3101    x$fpl$frleqf        fz_ws.l(frleqf.o)
    0x0800271a   0x0800271a   0x00000002   PAD
    0x0800271c   0x0800271c   0x000000ea   Code   RO         3079    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x08002806   0x08002806   0x00000004   Code   RO         3103    x$fpl$printf1       fz_ws.l(printf1.o)
    0x0800280a   0x0800280a   0x00000000   Code   RO         3169    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x0800280a   0x0800280a   0x000005f0   Data   RO         1607    .constdata          oled.o
    0x08002dfa   0x08002dfa   0x00000002   PAD
    0x08002dfc   0x08002dfc   0x00000094   Data   RO         3221    .constdata          c_w.l(bigflt0.o)
    0x08002e90   0x08002e90   0x00000020   Data   RO         3399    Region$$Table       anon$$obj.o
    0x08002eb0   0x08002eb0   0x0000001c   Data   RO         3245    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08002ecc, Size: 0x00000a68, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08002ecc   0x00000014   Data   RW          558    .data               stm32f10x_rcc.o
    0x20000014   0x08002ee0   0x00000014   Data   RW         2143    .data               timer.o
    0x20000028   0x08002ef4   0x00000008   Data   RW         2914    .data               main.o
    0x20000030        -       0x0000001e   Zero   RW         1527    .bss                key.o
    0x2000004e   0x08002efc   0x00000002   PAD
    0x20000050        -       0x00000014   Zero   RW         2142    .bss                timer.o
    0x20000064        -       0x000003a4   Zero   RW         2801    .bss                bluetooth.o
    0x20000408        -       0x00000060   Zero   RW         3255    .bss                c_w.l(libspace.o)
    0x20000468        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f10x_md.o
    0x20000668        -       0x00000400   Zero   RW            1    STACK               startup_stm32f10x_md.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        92          8          0          0        932       2713   bluetooth.o
         0          0          0          0          0       4516   core_cm3.o
        70          0          0          0          0        978   delay.o
         0          0          0          0          0       1216   geometry.o
       360         26          0          0         30       4727   key.o
        84         14          0          0          0       1382   led.o
       452        104          0          8          0       1019   main.o
         0          0          0          0          0     201720   misc.o
       734         22       1520          0          0       6703   oled.o
       310         10          0          0          0       5903   servo.o
        64         26        236          0       1536        832   startup_stm32f10x_md.o
         0          0          0          0          0       3456   statemachine.o
       314          0          0          0          0      12528   stm32f10x_gpio.o
        34          0          0          0          0       4464   stm32f10x_it.o
       244         26          0         20          0      12577   stm32f10x_rcc.o
        40          0          0          0          0      22058   stm32f10x_tim.o
       398          6          0          0          0      11918   stm32f10x_usart.o
       328         28          0          0          0      25085   system_stm32f10x.o
       176         26          0         20         20        916   timer.o
       212         12          0          0          0       1630   usart.o

    ----------------------------------------------------------------------
      3920        <USER>       <GROUP>         48       2520     326341   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         8          0          2          0          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       352          0          0          0          0         88   __printf_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        48          6          0          0          0         96   _printf_char_common.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
        10          0          0          0          0         68   _sputc.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        18          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
        40          6          0          0          0         84   noretval__2sprintf.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        12          0          0          0          0         68   dretinf.o
        86          4          0          0          0         84   f2d.o
       430          8          0          0          0        168   faddsub_clz.o
        24          0          0          0          0         68   fcmpi.o
       388         76          0          0          0         96   fdiv.o
        62          4          0          0          0         84   ffixu.o
       104          4          0          0          0         84   fleqf.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
        98          0          0          0          0         68   frleqf.o
         4          0          0          0          0         68   printf1.o
         0          0          0          0          0          0   usenofp.o
        40          0          0          0          0         68   fpclassify.o

    ----------------------------------------------------------------------
      6094        <USER>        <GROUP>          0         96       3440   Library Totals
        12          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      4426        190        176          0         96       2348   c_w.l
      1616        104          0          0          0       1024   fz_ws.l
        40          0          0          0          0         68   m_ws.l

    ----------------------------------------------------------------------
      6094        <USER>        <GROUP>          0         96       3440   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     10014        602       1966         48       2616     324785   Grand Totals
     10014        602       1966         48       2616     324785   ELF Image Totals
     10014        602       1966         48          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                11980 (  11.70kB)
    Total RW  Size (RW Data + ZI Data)              2664 (   2.60kB)
    Total ROM Size (Code + RO Data + RW Data)      12028 (  11.75kB)

==============================================================================

