<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>串口助手软件(sscom)和屏幕联调 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="串口助手软件和串口屏模拟器联调1" href="debug_with_vspd.html" />
    <link rel="prev" title="串口屏发送数据给单片机" href="usart_protocol/tjc2mcu.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">串口屏调试</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="simulator/index.html">模拟器调试基础知识</a></li>
<li class="toctree-l2"><a class="reference internal" href="connect_ttl.html">和USB转TTL的接线说明</a></li>
<li class="toctree-l2"><a class="reference internal" href="connect_mcu.html">和单片机的接线说明</a></li>
<li class="toctree-l2"><a class="reference internal" href="base/index.html">联机调试基础知识</a></li>
<li class="toctree-l2"><a class="reference internal" href="usart_protocol/index.html">串口屏通讯协议</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">串口助手软件(sscom)和屏幕联调</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#id1">sscom和屏幕联调-样例工程下载</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="debug_with_vspd.html">串口助手软件和串口屏模拟器联调1</a></li>
<li class="toctree-l2"><a class="reference internal" href="debug_with_vspd2.html">串口助手软件和串口屏模拟器联调2</a></li>
<li class="toctree-l2"><a class="reference internal" href="stm32/index.html">与stm32单片机联调</a></li>
<li class="toctree-l2"><a class="reference internal" href="timcu/index.html">与TI（德州仪器）单片机联调</a></li>
<li class="toctree-l2"><a class="reference internal" href="arduino/index.html">与arduino联调</a></li>
<li class="toctree-l2"><a class="reference internal" href="micropython/index.html">与MicroPython联调</a></li>
<li class="toctree-l2"><a class="reference internal" href="python/index.html">与python联调</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">串口屏调试</a> &raquo;</li>
      <li>串口助手软件(sscom)和屏幕联调</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="sscom">
<h1>串口助手软件(sscom)和屏幕联调<a class="headerlink" href="#sscom" title="此标题的永久链接"></a></h1>
<p>串口通讯工具sscom下载链接：</p>
<p><a class="reference internal" href="../download/tools_download.html#sscom"><span class="std std-ref">sscom串口调试助手下载</span></a></p>
<p>连接的接线图如下</p>
<img alt="../_images/howtoconnect0.png" src="../_images/howtoconnect0.png" />
<p>使用官方的USB转ttl连接串口屏如下所示</p>
<img alt="../_images/debugWithsscom1.jpg" src="../_images/debugWithsscom1.jpg" />
<p>1.串口屏通过电平转换模块和电脑正确连接，是否连接成功可以先用我们上位联机试一下，这样确保硬件连线没问题。</p>
<img alt="../_images/debugWithsscom2.jpg" src="../_images/debugWithsscom2.jpg" />
<p>联机成功后底部会显示联机信息，请记住红圈框起来的串口号和波特率，然后关闭模拟器界面</p>
<img alt="../_images/debugWithsscom3.jpg" src="../_images/debugWithsscom3.jpg" />
<p>2.串口助手如下配置，串口助手请使用SSCOM5.13.1版本，根据实际情况设置端口号和波特率，不要勾选“加回车换行”</p>
<img alt="../_images/debugWithsscom4.jpg" src="../_images/debugWithsscom4.jpg" />
<p>3.点击拓展</p>
<img alt="../_images/debugWithsscom5.jpg" src="../_images/debugWithsscom5.jpg" />
<p>4.可以看到提前配置好的一些命令</p>
<img alt="../_images/debugWithsscom6.jpg" src="../_images/debugWithsscom6.jpg" />
<p>5.点击右侧对应的按钮进行发送即可</p>
<img alt="../_images/debugWithsscom7.jpg" src="../_images/debugWithsscom7.jpg" />
<p>6.如有其它需求可以模仿提前配置好的命令进行编写</p>
<section id="id1">
<h2>sscom和屏幕联调-样例工程下载<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/串口屏调试/sscom和屏幕联调/sscom和屏幕联调.zip">《sscom和屏幕联调》演示工程下载</a></p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="usart_protocol/tjc2mcu.html" class="btn btn-neutral float-left" title="串口屏发送数据给单片机" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="debug_with_vspd.html" class="btn btn-neutral float-right" title="串口助手软件和串口屏模拟器联调1" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>