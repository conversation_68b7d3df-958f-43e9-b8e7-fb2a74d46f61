 =============================================================
 ================= AES using CCM mode ===================
 ============================================================
 ---------------------------------------
 Plain Data :
 ---------------------------------------
[0x20][0x21][0x22][0x23][0x24][0x25][0x26][0x27][0x28][0x29][0x2A][0x2B][0x2C][0x2D][0x2E][0x2F]  Block 0 

 =======================================
 Encrypted Data with AES 128  Mode  CCM
 ---------------------------------------
[0xD2][0xA1][0xF0][0xE0][0x51][0xEA][0x5F][0x62][0x08][0x1A][0x77][0x92][0x07][0x3D][0x59][0x3D] Block 0 

 =======================================
 Message Authentication Code (MAC):
  ---------------------------------------
[0x1F][0xC6][0x4F][0xBF][0xAC][0xCD]
 =======================================
 Decrypted Data with AES 128  Mode  CCM
 ---------------------------------------
[0x20][0x21][0x22][0x23][0x24][0x25][0x26][0x27][0x28][0x29][0x2A][0x2B][0x2C][0x2D][0x2E][0x2F] Block 0 

 =======================================
 Message Authentication Code (MAC):
  ---------------------------------------
[0x1F][0xC6][0x4F][0xBF][0xAC][0xCD]
 Press any key to continue...