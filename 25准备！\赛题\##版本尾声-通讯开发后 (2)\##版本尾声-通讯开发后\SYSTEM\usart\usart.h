/**
 ******************************************************************************
 * @file    usart.h
 * <AUTHOR> Team & E题开发团队
 * @version V1.6
 * @date    2025-08-02
 * @brief   STM32F407 USART1串口通信驱动头文件
 *          
 *          本文件定义了USART1串口的初始化和K230通信接口
 *          专门用于接收K230发送的6字节控制协议数据包
 * 
 * @note    功能特性:
 *          - USART1串口初始化 (PA9/PA10)
 *          - K230协议接收和解析
 *          - 6字节数据包状态机处理
 *          - 校验和验证机制
 *          
 *          硬件连接:
 *          - PA9:  USART1_TX (发送到K230 RX)
 *          - PA10: USART1_RX (接收K230 TX)
 *          - 波特率: 115200 (固定)
 ******************************************************************************
 * @attention
 * 本程序基于正点原子STM32开发板例程修改
 * 原作者: 正点原子@ALIENTEK (www.openedv.com)
 * 修改: 删除printf支持，专用于K230通信
 ******************************************************************************
 */

#ifndef __USART_H
#define __USART_H

#include "stdio.h"	
#include "stm32f4xx_conf.h"
#include "sys.h"

/* 串口配置宏定义 ------------------------------------------------------------*/

/**
 * @brief  串口接收缓存长度定义
 * @note   定义串口接收缓存区大小，单位：字节
 *         保留原有缓存以兼容旧代码
 */
#define USART_REC_LEN    200

/**
 * @brief  串口接收使能控制
 * @note   1: 使能USART1接收中断
 *         0: 禁用USART1接收中断
 */
#define EN_USART1_RX     1

/* K230通信协议常量定义 -----------------------------------------------------*/

/**
 * @brief  K230协议帧格式常量
 */
#define K230_FRAME_HEADER    0xAA    /**< 帧头标识 */
#define K230_FRAME_TAIL      0xFF    /**< 帧尾标识 */
#define K230_FRAME_LENGTH    6       /**< 数据包长度 */

/**
 * @brief  K230控制指令定义
 */
#define K230_CMD_LEFT        1       /**< 左转指令 */
#define K230_CMD_RIGHT       2       /**< 右转指令 */
#define K230_CMD_STOP        3       /**< 停止指令 */
#define K230_CMD_FIRE        4       /**< 发射指令 */

/* 全局变量声明 --------------------------------------------------------------*/

#if EN_USART1_RX
extern u8  USART_RX_BUF[USART_REC_LEN]; // 接收缓冲,最大USART_REC_LEN个字节
extern volatile u8 k230_str_ready;        // K230字符串就绪标志
extern char k230_str_buffer[32];           // K230字符串缓冲区
#endif

/* 函数声明 ------------------------------------------------------------------*/

/**
 * @brief  USART1串口初始化
 * @param  bound: 波特率设置
 * @retval None
 * @note   初始化USART1串口，配置GPIO和中断
 *         专用于K230通信，固定115200波特率
 */
void uart_init(u32 bound);

/**
 * @brief  K230字符串解析函数
 * @param  ch: 接收到的字符
 * @retval None
 * @note   由USART中断调用，实现字符串解析
 */
void K230_ParseString(char ch);

/**
 * @brief  获取K230字符串状态
 * @param  None
 * @retval u8: 1=字符串就绪, 0=无字符串
 * @note   主循环中检查是否有新的K230字符串
 */
__inline u8 K230_IsStringReady(void) {
    return k230_str_ready;
}

/**
 * @brief  清除K230字符串就绪标志
 * @param  None
 * @retval None
 * @note   处理完字符串后调用此函数
 */
__inline void K230_ClearStringFlag(void) {
    k230_str_ready = 0;
}

/**
 * @brief  获取K230接收到的字符串
 * @param  None
 * @retval char*: 字符串指针
 * @note   仅在K230_IsStringReady()返回1时调用有效
 */
__inline char* K230_GetString(void) {
    return k230_str_buffer;
}

#endif
