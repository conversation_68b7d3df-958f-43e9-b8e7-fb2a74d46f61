# HTS-25L总线舵机连接与调试说明

## 🔌 **硬件连接方案**

### **方案1: 使用RS485驱动板 (推荐)**
```
STM32F103C8T6    →    RS485驱动板    →    HTS-25L舵机
PA9 (USART1_TX)  →    TXD            
PA10(USART1_RX)  →    RXD            
PA8 (DE控制)     →    DE/RE          →    总线连接
GND              →    GND            →    GND (黑线)
5V               →    VCC            →    VCC (红线)
                      A+/A-          →    信号线 (黄线)
```

**配置**: 在usart.h中设置 `#define USE_RS485_DE_CONTROL  1`

### **方案2: TTL电平直连 (简单测试)**
```
STM32F103C8T6    →    HTS-25L舵机
PA9 (USART1_TX)  →    信号线 (黄线)
GND              →    GND (黑线)
5V               →    VCC (红线)
PA8              →    悬空 (不连接)
```

**配置**: 在usart.h中设置 `#define USE_RS485_DE_CONTROL  0`

## 📊 **HTS-25L协议格式 (已修正)**

### **标准命令格式**
```
| 字节位置 | 名称 | 值 | 说明 |
|----------|------|----|----- |
| 0 | Header1 | 0x55 | 帧头1 |
| 1 | Header2 | 0x55 | 帧头2 |
| 2 | ID | 0x01/0x02 | 舵机ID |
| 3 | Length | 0x06 | 数据长度 |
| 4 | Cmd | 0x03 | 写位置指令 |
| 5 | Prm1 | 位置低字节 | 位置值LSB |
| 6 | Prm2 | 位置高字节 | 位置值MSB |
| 7 | Prm3 | 时间低字节 | 时间LSB |
| 8 | Prm4 | 时间高字节 | 时间MSB |
| 9 | Checksum | 计算值 | 校验和 |
```

### **校验和计算**
```c
Checksum = ~(ID + Length + Cmd + Prm1 + Prm2 + Prm3 + Prm4) & 0xFF
```

### **示例命令**

#### **ID1舵机90度，1000ms**
```
位置值: 90 * 1000 / 240 = 375 (0x0177)
时间值: 1000ms (0x03E8)

命令: 55 55 01 06 03 77 01 E8 03 [校验和]
```

#### **ID2舵机120度，1000ms**
```
位置值: 120 * 1000 / 240 = 500 (0x01F4)
时间值: 1000ms (0x03E8)

命令: 55 55 02 06 03 F4 01 E8 03 [校验和]
```

## 🔧 **代码修正要点**

### **1. 协议格式修正**
- ✅ 帧头改为 0x55 0x55 (原来错误的0xFF 0xFF)
- ✅ 校验和算法修正
- ✅ 数据长度计算修正
- ✅ 小端格式数据排列

### **2. 时序控制优化**
- ✅ 添加字节间延时
- ✅ 发送完成等待
- ✅ 命令间10ms延时
- ✅ 可选的DE控制

### **3. 调试功能增强**
- ✅ 添加Debug模块
- ✅ 十六进制数据显示
- ✅ 舵机状态显示
- ✅ 系统状态监控

## 🔍 **调试步骤**

### **1. 硬件检查**
- [ ] 确认舵机ID设置 (ID1和ID2)
- [ ] 检查电源供电 (5V，足够电流)
- [ ] 验证信号线连接
- [ ] 测试串口波特率 (115200)

### **2. 软件配置**
- [ ] 设置正确的DE控制模式
- [ ] 确认协议格式 (0x55帧头)
- [ ] 检查校验和计算
- [ ] 验证时序参数

### **3. 信号测试**
使用示波器或逻辑分析仪检查PA9输出：
```
期望信号: 55 55 01 06 03 77 01 E8 03 [校验和]
波特率: 115200 bps
数据格式: 8N1
```

### **4. 舵机响应测试**
- [ ] 上电后舵机是否有初始化动作
- [ ] 发送命令后是否有运动
- [ ] LED指示是否正常
- [ ] OLED显示是否正确

## ⚠️ **常见问题排查**

### **问题1: 舵机完全不动**
**可能原因**:
- 协议格式错误 (帧头、校验和)
- 舵机ID不匹配
- 电源供电不足
- 信号线连接错误

**解决方法**:
- 使用示波器检查信号
- 确认舵机ID设置
- 检查电源电压和电流
- 重新检查连接

### **问题2: 舵机偶尔动作**
**可能原因**:
- 时序问题
- 信号干扰
- 校验和错误
- 电源不稳定

**解决方法**:
- 增加延时时间
- 使用屏蔽线
- 检查校验和算法
- 改善电源滤波

### **问题3: 舵机动作不准确**
**可能原因**:
- 位置值计算错误
- 时间参数不当
- 舵机机械问题

**解决方法**:
- 验证角度转换公式
- 调整运动时间
- 检查舵机机械状态

## 📈 **性能优化建议**

### **1. 通信可靠性**
- 使用RS485差分信号 (长距离)
- 添加信号滤波电路
- 实现命令重发机制

### **2. 系统稳定性**
- 增加错误检测
- 实现超时保护
- 添加状态反馈

### **3. 功能扩展**
- 支持更多舵机ID
- 实现位置读取
- 添加速度控制

## 🚀 **预期测试结果**

修正后的系统应该能够：
1. ✅ 正确发送0x55帧头的命令
2. ✅ ID1舵机转到90度位置
3. ✅ ID2舵机转到120度位置
4. ✅ 1000ms平滑运动时间
5. ✅ 稳定的循环测试
6. ✅ 准确的调试信息显示

---
**注意**: 如果仍有问题，请提供示波器截图或具体的错误现象描述。
