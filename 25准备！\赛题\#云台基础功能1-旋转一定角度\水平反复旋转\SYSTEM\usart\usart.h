/**
 ******************************************************************************
 * @file    usart.h
 * <AUTHOR> Team & [Your Name]
 * @version V1.5
 * @date    2025-01-31
 * @brief   STM32F407 USART1串口通信驱动头文件
 *          
 *          本文件定义了USART1串口的初始化和通信接口
 *          支持printf重定向、中断接收和数据处理
 * 
 * @note    功能特性:
 *          - USART1串口初始化 (PA9/PA10)
 *          - printf函数重定向到串口
 *          - 中断方式接收数据
 *          - 支持不定长数据接收
 *          - 兼容不同系统时钟频率
 *          
 *          硬件连接:
 *          - PA9:  USART1_TX (发送)
 *          - PA10: USART1_RX (接收)
 *          - 波特率: 115200 (默认)
 ******************************************************************************
 * @attention
 * 本程序基于正点原子STM32开发板例程修改
 * 原作者: 正点原子@ALIENTEK (www.openedv.com)
 * 修改: 适配STM32F407，添加详细注释
 ******************************************************************************
 */

#ifndef __USART_H
#define __USART_H

#include "stdio.h"	
#include "stm32f4xx_conf.h"
#include "sys.h"

/* 串口配置宏定义 ------------------------------------------------------------*/

/**
 * @brief  串口接收缓存长度定义
 * @note   定义串口接收缓存区大小，单位：字节
 *         可根据实际应用需求调整大小
 */
#define USART_REC_LEN    200

/**
 * @brief  串口接收使能控制
 * @note   1: 使能USART1接收中断
 *         0: 禁用USART1接收中断
 *         可通过此宏控制是否启用串口接收功能
 */
#define EN_USART1_RX     1

/* 全局变量声明 --------------------------------------------------------------*/

#if EN_USART1_RX
extern u8  USART_RX_BUF[USART_REC_LEN]; // 接收缓冲,最大USART_REC_LEN个字节
extern u16 USART_RX_STA;                 // 接收状态标记
#endif

/* 函数声明 ------------------------------------------------------------------*/

/**
 * @brief  USART1串口初始化
 * @param  bound: 波特率设置
 * @retval None
 * @note   初始化USART1串口，配置GPIO和中断
 *         常用波特率: 9600, 115200
 *         自动适配不同的系统时钟频率
 */
void uart_init(u32 bound);

#endif
