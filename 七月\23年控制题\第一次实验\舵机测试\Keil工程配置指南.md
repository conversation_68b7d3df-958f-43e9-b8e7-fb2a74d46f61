# Keil MDK-ARM 5 工程配置指南

> **适用工程**: HTS25L_ServoControl  
> **目标芯片**: STM32F407ZGT6  
> **库类型**: STM32 Standard Peripheral Library (SPL)  

---

## 🔧 必需文件准备

### 1. STM32标准外设库文件

**从STM32F4xx_StdPeriph_Lib_V1.8.0复制以下文件到`Library/`目录**：

#### 外设驱动文件
```
STM32F4xx_StdPeriph_Driver/src/
├── stm32f4xx_rcc.c      # RCC时钟控制
├── stm32f4xx_gpio.c     # GPIO控制
├── stm32f4xx_usart.c    # USART通信
├── stm32f4xx_flash.c    # Flash控制
└── misc.c               # 杂项功能

STM32F4xx_StdPeriph_Driver/inc/
├── stm32f4xx_rcc.h
├── stm32f4xx_gpio.h
├── stm32f4xx_usart.h
├── stm32f4xx_flash.h
└── misc.h
```

#### CMSIS核心文件
```
Libraries/CMSIS/Device/ST/STM32F4xx/Include/
├── stm32f4xx.h          # STM32F4xx总头文件
└── system_stm32f4xx.h   # 系统配置头文件

Libraries/CMSIS/Include/
├── core_cm4.h           # Cortex-M4核心定义
├── core_cmFunc.h        # 核心函数
├── core_cmInstr.h       # 核心指令
└── core_cmSimd.h        # SIMD指令
```

#### 启动文件
```
Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/arm/
└── startup_stm32f40_41xxx.s  # STM32F407启动文件
```

### 2. 文件放置位置

**目录结构**：
```
HTS25L_ServoControl/
├── Library/                    # 标准外设库
│   ├── stm32f4xx_rcc.c
│   ├── stm32f4xx_rcc.h
│   ├── stm32f4xx_gpio.c
│   ├── stm32f4xx_gpio.h
│   ├── stm32f4xx_usart.c
│   ├── stm32f4xx_usart.h
│   ├── stm32f4xx_flash.c
│   ├── stm32f4xx_flash.h
│   ├── misc.c
│   ├── misc.h
│   ├── stm32f4xx.h
│   ├── core_cm4.h
│   ├── core_cmFunc.h
│   ├── core_cmInstr.h
│   └── core_cmSimd.h
└── Startup/                    # 启动文件
    └── startup_stm32f40_41xxx.s
```

---

## ⚙️ Keil工程配置步骤

### 1. 打开工程
1. 启动Keil MDK-ARM 5
2. 打开工程文件：`HTS25L_ServoControl.uvprojx`
3. 确认工程加载成功

### 2. 添加库文件到工程

**在Project窗口中**：
1. 右键点击`Library`组 → Add Files to Group 'Library'
2. 选择`Library/`目录下的所有`.c`文件：
   - `stm32f4xx_rcc.c`
   - `stm32f4xx_gpio.c`
   - `stm32f4xx_usart.c`
   - `stm32f4xx_flash.c`
   - `misc.c`
3. 点击Add添加到工程

**添加启动文件**：
1. 右键点击`Startup`组 → Add Files to Group 'Startup'
2. 选择`Startup/startup_stm32f40_41xxx.s`
3. 点击Add添加到工程

### 3. 配置Target选项

**右键Target → Options for Target**

#### Device页面
- Device: `STM32F407ZGTx`
- Vendor: `STMicroelectronics`
- Pack: `Keil.STM32F4xx_DFP.2.14.0`

#### Target页面
- Code Generation: `ARM Compiler 5`
- Use MicroLIB: `不勾选`
- Cross-Module Optimization: `不勾选`

#### Output页面
- Select Folder for Objects: `.\Objects\`
- Name of Executable: `HTS25L_ServoControl`
- Create HEX File: `勾选`

#### Listing页面
- Select Folder for Listings: `.\Listings\`

### 4. 配置C/C++选项

#### C/C++页面
- Optimization: `Level 0 (-O0)` (调试时)
- One ELF Section per Function: `勾选`
- C99 Mode: `勾选`
- Warnings: `All Warnings`

**Define**:
```
STM32F40_41xxx,USE_STDPERIPH_DRIVER
```

**Include Paths**:
```
.\User
.\BSP
.\Driver
.\App
.\System
.\Library
```

### 5. 配置Asm选项

#### Assembler页面
- 保持默认设置即可

### 6. 配置Linker选项

#### Linker页面
- Use Memory Layout from Target Dialog: `勾选`
- Don't Search Standard Libraries: `不勾选`

### 7. 配置Debug选项

#### Debug页面
- Use: `ST-Link Debugger`
- Load Application at Startup: `勾选`
- Go till main(): `勾选`

**Settings按钮配置**：
- Port: `SW`
- Max Clock: `10 MHz`
- Connect & Reset Options: `Normal`
- Flash Download: 勾选所有选项

### 8. 配置Utilities选项

#### Utilities页面
- Use Target Driver for Flash Programming: `勾选`
- Update Target before Debugging: `勾选`

---

## 🔍 编译测试

### 1. 编译工程
1. 点击菜单 Project → Build Target (F7)
2. 检查Output窗口是否有错误
3. 确认编译成功：`0 Error(s), 0 Warning(s)`

### 2. 常见编译错误解决

**错误1**: `cannot open source input file "stm32f4xx.h"`
- **解决**: 检查Include Paths是否包含`.\Library`

**错误2**: `undefined symbol "RCC_AHB1PeriphClockCmd"`
- **解决**: 确认`stm32f4xx_rcc.c`已添加到工程

**错误3**: `L6218E: Undefined symbol SystemInit`
- **解决**: 确认启动文件`startup_stm32f40_41xxx.s`已添加

### 3. 下载测试
1. 连接ST-Link调试器到开发板
2. 点击菜单 Flash → Download (F8)
3. 确认下载成功
4. 复位开发板测试功能

---

## 📋 工程文件说明

### 自动生成文件
```
Objects/          # 编译生成的目标文件
Listings/         # 编译生成的列表文件
*.uvguix.*        # 用户界面配置文件
*.uvoptx          # 工程选项文件
```

### 重要配置文件
- `HTS25L_ServoControl.uvprojx`: 主工程文件
- `User/stm32f4xx_conf.h`: 外设库配置文件
- `System/system_stm32f4xx.c`: 系统时钟配置

---

## ⚠️ 注意事项

### 1. 库版本兼容性
- 使用STM32F4xx_StdPeriph_Lib_V1.8.0或更高版本
- 确保CMSIS版本与库版本匹配

### 2. 编译器设置
- 必须使用ARM Compiler 5，不支持ARM Compiler 6
- 调试时使用-O0优化，发布时可使用-O2

### 3. 内存配置
- IROM1: 0x08000000, Size: 0x100000 (1MB Flash)
- IRAM1: 0x20000000, Size: 0x20000 (128KB RAM)
- IRAM2: 0x10000000, Size: 0x10000 (64KB CCM)

### 4. 调试配置
- 使用SWD接口，速度设置为10MHz以下
- 确保ST-Link驱动正确安装

---

## 🚀 快速配置检查清单

### 编译前检查
- [ ] STM32标准外设库文件已复制到Library目录
- [ ] 启动文件已复制到Startup目录
- [ ] 工程中已添加所有必需的.c文件
- [ ] Include Paths配置正确
- [ ] Define宏定义正确

### 下载前检查
- [ ] ST-Link驱动已安装
- [ ] 调试器连接正常
- [ ] Target设备选择正确
- [ ] Flash Download选项已配置

### 运行前检查
- [ ] 硬件连接正确（舵机供电、共地、信号线）
- [ ] 舵机ID设置为1
- [ ] 按键和LED引脚连接正常

---

**按照此指南配置完成后，工程即可正常编译、下载和运行！** ✅
