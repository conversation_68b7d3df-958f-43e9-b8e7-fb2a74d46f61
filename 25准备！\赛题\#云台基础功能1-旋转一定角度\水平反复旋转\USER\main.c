/**
 ******************************************************************************
 * @file    main.c
 * <AUTHOR> Name]
 * @version V1.0
 * @date    2025-01-31
 * @brief   STM32F407水平电机反复旋转控制程序 - 简化版
 *          
 *          本程序实现水平电机固定动作序列：
 *          顺时针90deg -> 逆时针180deg -> 顺时针90deg -> 停止
 * 
 * @note    系统配置:
 *          - 主控: STM32F407ZGT6 @ 168MHz
 *          - 驱动: D36A步进电机驱动器
 *          - 通信: USART1 @ 115200bps (调试输出)
 *          
 *          引脚分配:
 *          - 水平电机A: STEP=PC8, DIR=PC13, SLEEP=PD2
 *          - 串口通信:   TX=PA9,  RX=PA10
 ******************************************************************************
 * @attention
 * 本程序用于2025年全国大学生电子设计竞赛E题：简易自行瞄准装置
 * 水平电机反复旋转测试版本
 ******************************************************************************
 */

#include "sys.h"

/**
 * @brief  主函数 - 水平电机反复旋转控制
 * @param  None
 * @retval None
 * @note   程序入口点，完成系统初始化后执行固定动作序列：
 *         顺时针90deg -> 逆时针180deg -> 顺时针90deg -> 停止
 */
int main(void)
{
	/* ===== 系统基础初始化 ===== */
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2); // 设置中断分组为2
	delay_init(168);                                 // 延时初始化(F407为168MHz)
	uart_init(115200);                               // 串口通信初始化(115200bps)
	
	/* ===== 水平电机系统初始化 ===== */
	ATD5984_Init();                   // 水平电机控制引脚初始化
	STEP12_PWM_Init(10499, 6);        // TIM8 PWM初始化(2286Hz中速, 50%占空比)
	
	/* ===== 系统启动提示 ===== */
	printf("\r\n========================================\r\n");
	printf("STM32F407 Motor Control System - Clean Mode\r\n");
	printf("========================================\r\n");
	printf("System Features:\r\n");
	printf("1. Pure Target Sequence (No Calibration Tests)\r\n");
	printf("2. Fixed Sequence: 90deg CW -> 180deg CCW -> 90deg CW\r\n");
	printf("3. Precise PWM Timing: 2286Hz\r\n");
	printf("Current Settings:\r\n");
	printf("- STEPS_PER_DEGREE: %.1f\r\n", STEPS_PER_DEGREE);
	printf("- DIR_CW: %d, DIR_CCW: %d\r\n", DIR_CW, DIR_CCW);
	printf("========================================\r\n\r\n");
	
	/* ===== 等待系统稳定 ===== */
	printf("System initializing...\r\n");
	delay_ms(2000);  // 等待2秒系统稳定
	
	/* ===== 校准测试已跳过，直接执行目标序列 ===== */
	printf("Skipping calibration tests, starting target sequence...\r\n");
	
	/* ===== 执行固定动作序列 ===== */
	printf("Starting motor rotation sequence...\r\n\r\n");
	
	/* 动作1: 顺时针旋转90deg */
	printf("[Step 1/3] ");
	Motor_A_Rotate(90.0f);
	delay_ms(1000);  // 动作间隔1秒
	
	/* 动作2: 逆时针旋转180deg */
	printf("\r\n[Step 2/3] ");
	Motor_A_Rotate(-180.0f);
	delay_ms(1000);  // 动作间隔1秒
	
	/* 动作3: 顺时针旋转90deg */
	printf("\r\n[Step 3/3] ");
	Motor_A_Rotate(90.0f);
	delay_ms(1000);  // 动作间隔1秒
	
	/* ===== 动作序列完成 ===== */
	printf("\r\n========================================\r\n");
	printf("Motor rotation sequence completed!\r\n");
	printf("System entering idle state...\r\n");
	printf("========================================\r\n");
	
	/* ===== 空闲循环 ===== */
	while(1)
	{
		delay_ms(1000);  // 空闲状态，每秒输出一次心跳
		printf("System idle - Motor stopped\r\n");
	}
}



