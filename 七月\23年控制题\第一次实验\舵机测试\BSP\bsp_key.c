/**
  ******************************************************************************
  * @file    bsp_key.c
  * <AUTHOR> Analysis Expert
  * @version V1.0
  * @date    2025-07-17
  * @brief   按键板级支持包实现
  ******************************************************************************
  */

#include "bsp_key.h"

/* 外部变量声明 */
extern volatile uint32_t g_systick_count;

/**
  * @brief  按键初始化
  * @param  None
  * @retval None
  */
void BSP_Key_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 使能GPIO时钟
    RCC_AHB1PeriphClockCmd(KEY_GPIO_CLK, ENABLE);
    
    // 配置按键引脚
    GPIO_InitStructure.GPIO_Pin = KEY_K1_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;  // 上拉输入
    GPIO_Init(KEY_GPIO_PORT, &GPIO_InitStructure);
}

/**
  * @brief  获取按键状态
  * @param  key: 按键编号
  * @retval 按键状态 (KEY_PRESSED/KEY_RELEASED)
  */
uint8_t BSP_Key_GetState(uint8_t key)
{
    switch(key)
    {
        case KEY_K1:
            // 按键按下为低电平
            return (GPIO_ReadInputDataBit(KEY_GPIO_PORT, KEY_K1_PIN) == RESET) ? KEY_PRESSED : KEY_RELEASED;
        default:
            return KEY_RELEASED;
    }
}

/**
  * @brief  按键扫描（带消抖）
  * @param  key: 按键编号
  * @retval 按键状态 (KEY_PRESSED/KEY_RELEASED)
  */
uint8_t BSP_Key_Scan(uint8_t key)
{
    static uint8_t key_state[1] = {KEY_RELEASED};  // 按键状态记录
    static uint32_t key_time[1] = {0};             // 按键时间记录
    
    uint8_t current_state = BSP_Key_GetState(key);
    
    switch(key)
    {
        case KEY_K1:
            // 检测按键按下
            if(current_state == KEY_PRESSED && key_state[KEY_K1] == KEY_RELEASED)
            {
                key_time[KEY_K1] = g_systick_count;  // 记录按下时间
                key_state[KEY_K1] = KEY_PRESSED;
            }
            // 检测按键释放
            else if(current_state == KEY_RELEASED && key_state[KEY_K1] == KEY_PRESSED)
            {
                // 检查是否满足消抖时间
                if((g_systick_count - key_time[KEY_K1]) >= KEY_DEBOUNCE_TIME)
                {
                    key_state[KEY_K1] = KEY_RELEASED;
                    return KEY_PRESSED;  // 返回有效按键
                }
                else
                {
                    key_state[KEY_K1] = KEY_RELEASED;  // 抖动，重置状态
                }
            }
            break;
        default:
            break;
    }
    
    return KEY_RELEASED;
}
