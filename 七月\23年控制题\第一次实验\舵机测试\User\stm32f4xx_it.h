/**
  ******************************************************************************
  * @file    stm32f4xx_it.h
  * <AUTHOR> Analysis Expert
  * @version V1.0
  * @date    2025-07-17
  * @brief   中断服务程序头文件
  ******************************************************************************
  */

#ifndef __STM32F4XX_IT_H
#define __STM32F4XX_IT_H

#ifdef __cplusplus
 extern "C" {
#endif 

/* 中断服务程序声明 */
void NMI_Handler(void);
void HardFault_Handler(void);
void MemManage_Handler(void);
void BusFault_Handler(void);
void UsageFault_Handler(void);
void SVC_Handler(void);
void DebugMon_Handler(void);
void PendSV_Handler(void);
void SysTick_Handler(void);

#ifdef __cplusplus
}
#endif

#endif /* __STM32F4XX_IT_H */
