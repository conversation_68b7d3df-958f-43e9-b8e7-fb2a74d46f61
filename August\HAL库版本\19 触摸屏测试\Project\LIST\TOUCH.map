Component: ARM Compiler 5.06 update 3 (build 300) Tool: armlink [4d35c9]

==============================================================================

Section Cross References

    main.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_Init
    main.o(.text) refers to common.o(.text) for Stm32_Clock_Init
    main.o(.text) refers to led.o(.text) for LED_Init
    main.o(.text) refers to key.o(.text) for KEY_Init
    main.o(.text) refers to lcd.o(.text) for LCD_Init
    main.o(.text) refers to touch.o(.text) for Touch_Init
    main.o(.text) refers to lcd.o(.data) for BRUSH_COLOR
    stm32f4xx_it.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_IncTick
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text) for NMI_Handler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(.text) for SystemInit
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    led.o(.text) refers to stm32f4xx_hal_gpio.o(.text) for HAL_GPIO_Init
    key.o(.text) refers to stm32f4xx_hal_gpio.o(.text) for HAL_GPIO_Init
    key.o(.text) refers to key.o(.data) for keyup_data
    lcd.o(.text) refers to common.o(.text) for delay_us
    lcd.o(.text) refers to lcd.o(.data) for write_gramcmd
    lcd.o(.text) refers to stm32f4xx_hal_gpio.o(.text) for HAL_GPIO_Init
    lcd.o(.text) refers to stm32f4xx_hal_sram.o(.text) for HAL_SRAM_Init
    lcd.o(.text) refers to lcd.o(.bss) for LCDSRAM_Handler
    lcd.o(.text) refers to lcd.o(.constdata) for char_1206
    ft5426.o(.text) refers to stm32f4xx_hal_gpio.o(.text) for HAL_GPIO_Init
    ft5426.o(.text) refers to common.o(.text) for delay_us
    ft5426.o(.text) refers to ft5426.o(.data) for Touch_num
    ft5426.o(.text) refers to ft5426.o(.constdata) for FT5426_Touch_Addr
    ft5426.o(.text) refers to lcd.o(.data) for dir_flag
    ft5426.o(.text) refers to xpt2046.o(.data) for x
    ft5426.o(.text) refers to touch.o(.text) for Draw_Point
    touch.o(.text) refers to xpt2046.o(.text) for XPT2046_Init
    touch.o(.text) refers to ft5426.o(.text) for FT5426_Init
    touch.o(.text) refers to lcd.o(.text) for LCD_Clear
    touch.o(.text) refers to lcd.o(.data) for lcd_id
    touch.o(.text) refers to xpt2046.o(.data) for Xdown
    xpt2046.o(.text) refers to common.o(.text) for delay_us
    xpt2046.o(.text) refers to stm32f4xx_hal_gpio.o(.text) for HAL_GPIO_Init
    xpt2046.o(.text) refers to xpt2046.o(.data) for CMD_RDX
    xpt2046.o(.text) refers to lcd.o(.data) for dir_flag
    stm32f4xx_hal.o(.text) refers to stm32f4xx_hal_cortex.o(.text) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(.text) refers to stm32f4xx_hal_msp.o(.text) for HAL_MspInit
    stm32f4xx_hal.o(.text) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_pwr_ex.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.text) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(.text) refers to stm32f4xx_hal_gpio.o(.text) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(.text) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_dma.o(.text) refers to stm32f4xx_hal_dma.o(.constdata) for flagBitshiftOffset
    stm32f4xx_hal_dma.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_ll_fsmc.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_sram.o(.text) refers to lcd.o(.text) for HAL_SRAM_MspInit
    stm32f4xx_hal_sram.o(.text) refers to stm32f4xx_ll_fsmc.o(.text) for FSMC_NORSRAM_Init
    stm32f4xx_hal_sram.o(.text) refers to stm32f4xx_hal_dma.o(.text) for HAL_DMA_Start_IT
    common.o(.text) refers to stm32f4xx_hal_rcc.o(.text) for HAL_RCC_OscConfig
    common.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetREVID
    common.o(.text) refers to stm32f4xx_hal_cortex.o(.text) for HAL_SYSTICK_CLKSourceConfig
    common.o(.text) refers to common.o(.data) for fac_us
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing led.o(.rrx_text), (6 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing lcd.o(.rev16_text), (4 bytes).
    Removing lcd.o(.revsh_text), (4 bytes).
    Removing lcd.o(.rrx_text), (6 bytes).
    Removing ft5426.o(.rev16_text), (4 bytes).
    Removing ft5426.o(.revsh_text), (4 bytes).
    Removing ft5426.o(.rrx_text), (6 bytes).
    Removing touch.o(.rev16_text), (4 bytes).
    Removing touch.o(.revsh_text), (4 bytes).
    Removing touch.o(.rrx_text), (6 bytes).
    Removing xpt2046.o(.rev16_text), (4 bytes).
    Removing xpt2046.o(.revsh_text), (4 bytes).
    Removing xpt2046.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(.text), (476 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text), (256 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text), (1140 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text), (4232 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ll_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ll_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ll_fsmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_sram.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.rrx_text), (6 bytes).
    Removing common.o(.rev16_text), (4 bytes).
    Removing common.o(.revsh_text), (4 bytes).
    Removing common.o(.rrx_text), (6 bytes).
    Removing common.o(.emb_text), (16 bytes).

71 unused section(s) (total 6428 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ..\Common\common.c                       0x00000000   Number         0  common.o ABSOLUTE
    ..\Main\main.c                           0x00000000   Number         0  main.o ABSOLUTE
    ..\Main\stm32f4xx_it.c                   0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_ll_fsmc.c 0x00000000   Number         0  stm32f4xx_ll_fsmc.o ABSOLUTE
    ..\Startup_config\startup_stm32f407xx.s  0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    ..\Startup_config\stm32f4xx_hal_msp.c    0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Startup_config\system_stm32f4xx.c     0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\USER\KEY\key.c                        0x00000000   Number         0  key.o ABSOLUTE
    ..\USER\LCD\lcd.c                        0x00000000   Number         0  lcd.o ABSOLUTE
    ..\USER\LED\led.c                        0x00000000   Number         0  led.o ABSOLUTE
    ..\USER\TOUCH\ft5426.c                   0x00000000   Number         0  ft5426.o ABSOLUTE
    ..\USER\TOUCH\touch.c                    0x00000000   Number         0  touch.o ABSOLUTE
    ..\USER\TOUCH\xpt2046.c                  0x00000000   Number         0  xpt2046.o ABSOLUTE
    ..\\Common\\common.c                     0x00000000   Number         0  common.o ABSOLUTE
    ..\\Main\\main.c                         0x00000000   Number         0  main.o ABSOLUTE
    ..\\Main\\stm32f4xx_it.c                 0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_ll_fsmc.c 0x00000000   Number         0  stm32f4xx_ll_fsmc.o ABSOLUTE
    ..\\Startup_config\\stm32f4xx_hal_msp.c  0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\\Startup_config\\system_stm32f4xx.c   0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\\USER\\KEY\\key.c                     0x00000000   Number         0  key.o ABSOLUTE
    ..\\USER\\LCD\\lcd.c                     0x00000000   Number         0  lcd.o ABSOLUTE
    ..\\USER\\LED\\led.c                     0x00000000   Number         0  led.o ABSOLUTE
    ..\\USER\\TOUCH\\ft5426.c                0x00000000   Number         0  ft5426.o ABSOLUTE
    ..\\USER\\TOUCH\\touch.c                 0x00000000   Number         0  touch.o ABSOLUTE
    ..\\USER\\TOUCH\\xpt2046.c               0x00000000   Number         0  xpt2046.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x080001fc   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x080001fe   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000202   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000204   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000206   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000208   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000208   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000208   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800020e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800020e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000212   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000212   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800021a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800021c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800021c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000220   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000228   Section        0  main.o(.text)
    .text                                    0x080002ec   Section        0  stm32f4xx_it.o(.text)
    .text                                    0x0800030c   Section        0  system_stm32f4xx.o(.text)
    .text                                    0x0800042c   Section        0  stm32f4xx_hal_msp.o(.text)
    .text                                    0x08000430   Section       64  startup_stm32f407xx.o(.text)
    $v0                                      0x08000430   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x08000470   Section        0  led.o(.text)
    .text                                    0x080004cc   Section        0  key.o(.text)
    .text                                    0x080005f0   Section        0  lcd.o(.text)
    .text                                    0x08001360   Section        0  ft5426.o(.text)
    .text                                    0x08001818   Section        0  touch.o(.text)
    .text                                    0x080019ac   Section        0  xpt2046.o(.text)
    .text                                    0x08001e34   Section        0  stm32f4xx_hal.o(.text)
    .text                                    0x08002028   Section        0  stm32f4xx_hal_cortex.o(.text)
    __NVIC_SetPriority                       0x08002029   Thumb Code    32  stm32f4xx_hal_cortex.o(.text)
    __NVIC_GetPriorityGrouping               0x080023d9   Thumb Code    12  stm32f4xx_hal_cortex.o(.text)
    .text                                    0x080023f8   Section        0  stm32f4xx_hal_gpio.o(.text)
    .text                                    0x080027ac   Section        0  stm32f4xx_hal_rcc.o(.text)
    .text                                    0x08003098   Section        0  stm32f4xx_hal_dma.o(.text)
    DMA_CalcBaseAndBitshift                  0x08003099   Thumb Code    46  stm32f4xx_hal_dma.o(.text)
    DMA_CheckFifoParam                       0x080030c7   Thumb Code   170  stm32f4xx_hal_dma.o(.text)
    DMA_SetConfig                            0x080032c9   Thumb Code    44  stm32f4xx_hal_dma.o(.text)
    .text                                    0x08003954   Section        0  stm32f4xx_ll_fsmc.o(.text)
    .text                                    0x08003cdc   Section        0  stm32f4xx_hal_sram.o(.text)
    .text                                    0x0800405c   Section        0  common.o(.text)
    .text                                    0x080043b8   Section      238  lludivv7m.o(.text)
    .text                                    0x080044a6   Section        0  heapauxi.o(.text)
    .text                                    0x080044ac   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080044f6   Section        0  exit.o(.text)
    .text                                    0x08004508   Section        8  libspace.o(.text)
    .text                                    0x08004510   Section        0  sys_exit.o(.text)
    .text                                    0x0800451c   Section        2  use_no_semi.o(.text)
    .text                                    0x0800451e   Section        0  indicate_semi.o(.text)
    x$fpl$fpinit                             0x0800451e   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800451e   Number         0  fpinit.o(x$fpl$fpinit)
    .constdata                               0x08004528   Section       24  system_stm32f4xx.o(.constdata)
    .constdata                               0x08004540   Section     7220  lcd.o(.constdata)
    .constdata                               0x08006174   Section       20  ft5426.o(.constdata)
    .constdata                               0x08006188   Section        8  stm32f4xx_hal_dma.o(.constdata)
    flagBitshiftOffset                       0x08006188   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .data                                    0x20000000   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000004   Section        6  key.o(.data)
    .data                                    0x2000000a   Section       18  lcd.o(.data)
    .data                                    0x2000001c   Section        1  ft5426.o(.data)
    .data                                    0x20000020   Section       29  xpt2046.o(.data)
    .data                                    0x20000040   Section        9  stm32f4xx_hal.o(.data)
    .data                                    0x20000049   Section        1  common.o(.data)
    fac_us                                   0x20000049   Data           1  common.o(.data)
    .bss                                     0x2000004c   Section       80  lcd.o(.bss)
    .bss                                     0x2000009c   Section       96  libspace.o(.bss)
    HEAP                                     0x20000100   Section      512  startup_stm32f407xx.o(HEAP)
    Heap_Mem                                 0x20000100   Data         512  startup_stm32f407xx.o(HEAP)
    STACK                                    0x20000300   Section     1024  startup_stm32f407xx.o(STACK)
    Stack_Mem                                0x20000300   Data        1024  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x20000700   Data           0  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x080001fd   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080001ff   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_alloca_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000205   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000209   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000209   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000209   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800020f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800020f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000213   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000213   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800021b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800021d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800021d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000221   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    main                                     0x08000229   Thumb Code   134  main.o(.text)
    NMI_Handler                              0x080002ed   Thumb Code     2  stm32f4xx_it.o(.text)
    HardFault_Handler                        0x080002ef   Thumb Code     4  stm32f4xx_it.o(.text)
    MemManage_Handler                        0x080002f3   Thumb Code     4  stm32f4xx_it.o(.text)
    BusFault_Handler                         0x080002f7   Thumb Code     4  stm32f4xx_it.o(.text)
    UsageFault_Handler                       0x080002fb   Thumb Code     4  stm32f4xx_it.o(.text)
    SVC_Handler                              0x080002ff   Thumb Code     2  stm32f4xx_it.o(.text)
    DebugMon_Handler                         0x08000301   Thumb Code     2  stm32f4xx_it.o(.text)
    PendSV_Handler                           0x08000303   Thumb Code     2  stm32f4xx_it.o(.text)
    SysTick_Handler                          0x08000305   Thumb Code     8  stm32f4xx_it.o(.text)
    SystemInit                               0x0800030d   Thumb Code    82  system_stm32f4xx.o(.text)
    SystemCoreClockUpdate                    0x0800035f   Thumb Code   174  system_stm32f4xx.o(.text)
    HAL_MspInit                              0x0800042d   Thumb Code     2  stm32f4xx_hal_msp.o(.text)
    HAL_MspDeInit                            0x0800042f   Thumb Code     2  stm32f4xx_hal_msp.o(.text)
    Reset_Handler                            0x08000431   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART1_IRQHandler                        0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART2_IRQHandler                        0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x0800044b   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x0800044d   Thumb Code     0  startup_stm32f407xx.o(.text)
    LED_Init                                 0x08000471   Thumb Code    84  led.o(.text)
    KEY_Init                                 0x080004cd   Thumb Code    66  key.o(.text)
    key_scan                                 0x0800050f   Thumb Code   192  key.o(.text)
    LCD_WriteReg                             0x080005f1   Thumb Code    10  lcd.o(.text)
    LCD_ReadReg                              0x080005fb   Thumb Code    22  lcd.o(.text)
    lcdm_delay                               0x08000611   Thumb Code    14  lcd.o(.text)
    LCD_WriteGRAM                            0x0800061f   Thumb Code    10  lcd.o(.text)
    LCD_DisplayOn                            0x08000629   Thumb Code     8  lcd.o(.text)
    LCD_DisplayOff                           0x08000631   Thumb Code     8  lcd.o(.text)
    LCD_Open_Window                          0x08000639   Thumb Code   166  lcd.o(.text)
    Set_Scan_Direction                       0x080006df   Thumb Code   204  lcd.o(.text)
    Set_Display_Mode                         0x080007ab   Thumb Code   214  lcd.o(.text)
    LCD_SetCursor                            0x08000881   Thumb Code   186  lcd.o(.text)
    LCD_GetPoint                             0x0800093b   Thumb Code   116  lcd.o(.text)
    LCD_DrawPoint                            0x080009af   Thumb Code    66  lcd.o(.text)
    LCD_Color_DrawPoint                      0x080009f1   Thumb Code    30  lcd.o(.text)
    Ssd1963_Set_BackLight                    0x08000a0f   Thumb Code    32  lcd.o(.text)
    LCD_Clear                                0x08000a2f   Thumb Code    50  lcd.o(.text)
    HAL_SRAM_MspInit                         0x08000a61   Thumb Code   238  lcd.o(.text)
    LCD_FSMC_Config                          0x08000b4f   Thumb Code   122  lcd.o(.text)
    ILI9341_Read_id                          0x08000bc9   Thumb Code    30  lcd.o(.text)
    SSD1963_Read_id                          0x08000be7   Thumb Code    28  lcd.o(.text)
    LCD_Init                                 0x08000c03   Thumb Code  1020  lcd.o(.text)
    LCD_Fill_onecolor                        0x08000fff   Thumb Code    82  lcd.o(.text)
    LCD_Draw_Picture                         0x08001051   Thumb Code    94  lcd.o(.text)
    LCD_DisplayChar                          0x080010af   Thumb Code   206  lcd.o(.text)
    LCD_DisplayString                        0x0800117d   Thumb Code    64  lcd.o(.text)
    LCD_DisplayString_color                  0x080011bd   Thumb Code    68  lcd.o(.text)
    Counter_Power                            0x08001201   Thumb Code    22  lcd.o(.text)
    LCD_DisplayNum                           0x08001217   Thumb Code   244  lcd.o(.text)
    LCD_DisplayNum_color                     0x0800130b   Thumb Code    78  lcd.o(.text)
    FT_IICSDA_OUT                            0x08001361   Thumb Code    56  ft5426.o(.text)
    FT_IIC_Stop                              0x08001399   Thumb Code    40  ft5426.o(.text)
    FT_IIC_Init                              0x080013c1   Thumb Code   108  ft5426.o(.text)
    FT_IICSDA_IN                             0x0800142d   Thumb Code    56  ft5426.o(.text)
    FT_IIC_Start                             0x08001465   Thumb Code    46  ft5426.o(.text)
    FT_MCU_WaitAck                           0x08001493   Thumb Code    56  ft5426.o(.text)
    FT_MCU_Send_Ack                          0x080014cb   Thumb Code    54  ft5426.o(.text)
    FT_MCU_NOAck                             0x08001501   Thumb Code    54  ft5426.o(.text)
    FT_IIC_write_OneByte                     0x08001537   Thumb Code    70  ft5426.o(.text)
    FT_IIC_Read_OneByte                      0x0800157d   Thumb Code    80  ft5426.o(.text)
    FT5426_WriteReg                          0x080015cd   Thumb Code    46  ft5426.o(.text)
    FT5426_ReadReg                           0x080015fb   Thumb Code    58  ft5426.o(.text)
    Read_Touch_Point                         0x08001635   Thumb Code    32  ft5426.o(.text)
    FT5426_Init                              0x08001655   Thumb Code   172  ft5426.o(.text)
    FT5426_Scan                              0x08001701   Thumb Code   266  ft5426.o(.text)
    Touch_Init                               0x08001819   Thumb Code    38  touch.o(.text)
    Clear_Screen                             0x0800183f   Thumb Code    50  touch.o(.text)
    Draw_Point                               0x08001871   Thumb Code    78  touch.o(.text)
    R_Touch_test                             0x080018bf   Thumb Code   118  touch.o(.text)
    C_Touch_test                             0x08001935   Thumb Code    74  touch.o(.text)
    SPI_Write_Byte                           0x080019ad   Thumb Code    64  xpt2046.o(.text)
    SPI_Read_AD                              0x080019ed   Thumb Code   136  xpt2046.o(.text)
    RTouch_Read_XorY                         0x08001a75   Thumb Code   120  xpt2046.o(.text)
    RTouch_Read_XY                           0x08001aed   Thumb Code    50  xpt2046.o(.text)
    RTouch_Read_XY2                          0x08001b1f   Thumb Code   192  xpt2046.o(.text)
    XPT2046_Scan                             0x08001bdf   Thumb Code   214  xpt2046.o(.text)
    XPT2046_Init                             0x08001cb5   Thumb Code   378  xpt2046.o(.text)
    HAL_InitTick                             0x08001e37   Thumb Code    64  stm32f4xx_hal.o(.text)
    HAL_Init                                 0x08001e77   Thumb Code    44  stm32f4xx_hal.o(.text)
    HAL_DeInit                               0x08001ea5   Thumb Code    64  stm32f4xx_hal.o(.text)
    HAL_IncTick                              0x08001ee5   Thumb Code    16  stm32f4xx_hal.o(.text)
    HAL_GetTick                              0x08001ef5   Thumb Code     6  stm32f4xx_hal.o(.text)
    HAL_GetTickPrio                          0x08001efb   Thumb Code     6  stm32f4xx_hal.o(.text)
    HAL_SetTickFreq                          0x08001f01   Thumb Code    32  stm32f4xx_hal.o(.text)
    HAL_GetTickFreq                          0x08001f21   Thumb Code     6  stm32f4xx_hal.o(.text)
    HAL_Delay                                0x08001f27   Thumb Code    36  stm32f4xx_hal.o(.text)
    HAL_SuspendTick                          0x08001f4b   Thumb Code    18  stm32f4xx_hal.o(.text)
    HAL_ResumeTick                           0x08001f5d   Thumb Code    18  stm32f4xx_hal.o(.text)
    HAL_GetHalVersion                        0x08001f6f   Thumb Code     4  stm32f4xx_hal.o(.text)
    HAL_GetREVID                             0x08001f73   Thumb Code     8  stm32f4xx_hal.o(.text)
    HAL_GetDEVID                             0x08001f7b   Thumb Code    10  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_EnableDBGSleepMode            0x08001f85   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_DisableDBGSleepMode           0x08001f93   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_EnableDBGStopMode             0x08001fa1   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_DisableDBGStopMode            0x08001faf   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_EnableDBGStandbyMode          0x08001fbd   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_DisableDBGStandbyMode         0x08001fcb   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_EnableCompensationCell               0x08001fd9   Thumb Code     8  stm32f4xx_hal.o(.text)
    HAL_DisableCompensationCell              0x08001fe1   Thumb Code     8  stm32f4xx_hal.o(.text)
    HAL_GetUIDw0                             0x08001fe9   Thumb Code     6  stm32f4xx_hal.o(.text)
    HAL_GetUIDw1                             0x08001fef   Thumb Code     8  stm32f4xx_hal.o(.text)
    HAL_GetUIDw2                             0x08001ff7   Thumb Code     8  stm32f4xx_hal.o(.text)
    HAL_NVIC_SetPriorityGrouping             0x08002049   Thumb Code    36  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_SetPriority                     0x0800206d   Thumb Code   124  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_EnableIRQ                       0x080020e9   Thumb Code    32  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_DisableIRQ                      0x08002109   Thumb Code    62  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_SystemReset                     0x08002147   Thumb Code    64  stm32f4xx_hal_cortex.o(.text)
    HAL_SYSTICK_Config                       0x08002187   Thumb Code    52  stm32f4xx_hal_cortex.o(.text)
    HAL_MPU_Disable                          0x080021bb   Thumb Code    42  stm32f4xx_hal_cortex.o(.text)
    HAL_MPU_Enable                           0x080021e5   Thumb Code    60  stm32f4xx_hal_cortex.o(.text)
    HAL_MPU_ConfigRegion                     0x08002221   Thumb Code    90  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_GetPriorityGrouping             0x0800227b   Thumb Code     8  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_GetPriority                     0x08002283   Thumb Code   138  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_SetPendingIRQ                   0x0800230d   Thumb Code    32  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_GetPendingIRQ                   0x0800232d   Thumb Code    46  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_ClearPendingIRQ                 0x0800235b   Thumb Code    30  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_GetActive                       0x08002379   Thumb Code    46  stm32f4xx_hal_cortex.o(.text)
    HAL_SYSTICK_CLKSourceConfig              0x080023a7   Thumb Code    40  stm32f4xx_hal_cortex.o(.text)
    HAL_SYSTICK_Callback                     0x080023cf   Thumb Code     2  stm32f4xx_hal_cortex.o(.text)
    HAL_SYSTICK_IRQHandler                   0x080023d1   Thumb Code     8  stm32f4xx_hal_cortex.o(.text)
    HAL_GPIO_Init                            0x080023f9   Thumb Code   466  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_DeInit                          0x080025cb   Thumb Code   316  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_ReadPin                         0x08002707   Thumb Code    16  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_WritePin                        0x08002717   Thumb Code    12  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_TogglePin                       0x08002723   Thumb Code    18  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_LockPin                         0x08002735   Thumb Code    46  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_EXTI_Callback                   0x08002763   Thumb Code     2  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_EXTI_IRQHandler                 0x08002765   Thumb Code    28  stm32f4xx_hal_gpio.o(.text)
    HAL_RCC_OscConfig                        0x080027b1   Thumb Code  1086  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetSysClockFreq                  0x08002bef   Thumb Code   164  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_ClockConfig                      0x08002c93   Thumb Code   388  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_MCOConfig                        0x08002e17   Thumb Code   186  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_EnableCSS                        0x08002ed1   Thumb Code     8  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_DisableCSS                       0x08002ed9   Thumb Code     8  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetHCLKFreq                      0x08002ee1   Thumb Code     6  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetPCLK1Freq                     0x08002ee7   Thumb Code    24  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetPCLK2Freq                     0x08002eff   Thumb Code    24  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetOscConfig                     0x08002f17   Thumb Code   278  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetClockConfig                   0x0800302d   Thumb Code    66  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_CSSCallback                      0x0800306f   Thumb Code     2  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_NMI_IRQHandler                   0x08003071   Thumb Code    30  stm32f4xx_hal_rcc.o(.text)
    HAL_DMA_Init                             0x08003171   Thumb Code   232  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_DeInit                           0x08003259   Thumb Code   112  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_Start                            0x080032f5   Thumb Code   102  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_Start_IT                         0x0800335b   Thumb Code   158  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_Abort                            0x080033f9   Thumb Code   180  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_Abort_IT                         0x080034ad   Thumb Code    40  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_PollForTransfer                  0x080034d5   Thumb Code   346  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_IRQHandler                       0x0800362f   Thumb Code   570  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_RegisterCallback                 0x08003869   Thumb Code    94  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_UnRegisterCallback               0x080038c7   Thumb Code   124  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_GetState                         0x08003943   Thumb Code     8  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_GetError                         0x0800394b   Thumb Code     6  stm32f4xx_hal_dma.o(.text)
    FSMC_NORSRAM_Init                        0x08003955   Thumb Code    88  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NORSRAM_DeInit                      0x080039ad   Thumb Code    56  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NORSRAM_Timing_Init                 0x080039e5   Thumb Code    68  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NORSRAM_Extended_Timing_Init        0x08003a29   Thumb Code    66  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NORSRAM_WriteOperation_Enable       0x08003a6b   Thumb Code    18  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NORSRAM_WriteOperation_Disable      0x08003a7d   Thumb Code    18  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NAND_Init                           0x08003a8f   Thumb Code    70  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NAND_CommonSpace_Timing_Init        0x08003ad5   Thumb Code    54  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NAND_AttributeSpace_Timing_Init     0x08003b0b   Thumb Code    54  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NAND_DeInit                         0x08003b41   Thumb Code    66  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NAND_ECC_Enable                     0x08003b83   Thumb Code    28  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NAND_ECC_Disable                    0x08003b9f   Thumb Code    28  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NAND_GetECC                         0x08003bbb   Thumb Code    88  stm32f4xx_ll_fsmc.o(.text)
    FSMC_PCCARD_Init                         0x08003c13   Thumb Code    38  stm32f4xx_ll_fsmc.o(.text)
    FSMC_PCCARD_CommonSpace_Timing_Init      0x08003c39   Thumb Code    38  stm32f4xx_ll_fsmc.o(.text)
    FSMC_PCCARD_AttributeSpace_Timing_Init   0x08003c5f   Thumb Code    38  stm32f4xx_ll_fsmc.o(.text)
    FSMC_PCCARD_IOSpace_Timing_Init          0x08003c85   Thumb Code    38  stm32f4xx_ll_fsmc.o(.text)
    FSMC_PCCARD_DeInit                       0x08003cab   Thumb Code    32  stm32f4xx_ll_fsmc.o(.text)
    HAL_SRAM_Init                            0x08003cdf   Thumb Code    88  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_MspDeInit                       0x08003d37   Thumb Code     2  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_DeInit                          0x08003d39   Thumb Code    34  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_DMA_XferCpltCallback            0x08003d5b   Thumb Code     2  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_DMA_XferErrorCallback           0x08003d5d   Thumb Code     2  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_Read_8b                         0x08003d5f   Thumb Code    70  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_Write_8b                        0x08003da5   Thumb Code    82  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_Read_16b                        0x08003df7   Thumb Code    70  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_Write_16b                       0x08003e3d   Thumb Code    82  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_Read_32b                        0x08003e8f   Thumb Code    68  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_Write_32b                       0x08003ed3   Thumb Code    80  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_Read_DMA                        0x08003f23   Thumb Code    86  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_Write_DMA                       0x08003f79   Thumb Code    98  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_WriteOperation_Enable           0x08003fdb   Thumb Code    54  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_WriteOperation_Disable          0x08004011   Thumb Code    60  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_GetState                        0x0800404d   Thumb Code     8  stm32f4xx_hal_sram.o(.text)
    GPIO_group_OUT                           0x0800405d   Thumb Code   454  common.o(.text)
    GPIO_bits_OUT                            0x08004223   Thumb Code    92  common.o(.text)
    Stm32_Clock_Init                         0x0800427f   Thumb Code   190  common.o(.text)
    delay_init                               0x0800433d   Thumb Code    16  common.o(.text)
    delay_us                                 0x0800434d   Thumb Code    68  common.o(.text)
    delay_ms                                 0x08004391   Thumb Code    24  common.o(.text)
    __aeabi_uldivmod                         0x080043b9   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x080043b9   Thumb Code   238  lludivv7m.o(.text)
    __use_two_region_memory                  0x080044a7   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080044a9   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080044ab   Thumb Code     2  heapauxi.o(.text)
    __user_setup_stackheap                   0x080044ad   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x080044f7   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x08004509   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08004509   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08004509   Thumb Code     0  libspace.o(.text)
    _sys_exit                                0x08004511   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x0800451d   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x0800451d   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x0800451f   Thumb Code     0  indicate_semi.o(.text)
    _fp_init                                 0x0800451f   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08004527   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08004527   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    AHBPrescTable                            0x08004528   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x08004538   Data           8  system_stm32f4xx.o(.constdata)
    char_1206                                0x08004540   Data        1140  lcd.o(.constdata)
    char_1608                                0x080049b4   Data        1520  lcd.o(.constdata)
    char_2412                                0x08004fa4   Data        4560  lcd.o(.constdata)
    FT5426_Touch_Addr                        0x08006174   Data          10  ft5426.o(.constdata)
    Touch_Point_COLOR                        0x0800617e   Data          10  ft5426.o(.constdata)
    Region$$Table$$Base                      0x08006190   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080061b0   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f4xx.o(.data)
    keydown_data                             0x20000004   Data           1  key.o(.data)
    keyup_data                               0x20000005   Data           1  key.o(.data)
    key_time                                 0x20000006   Data           2  key.o(.data)
    key_tem                                  0x20000008   Data           1  key.o(.data)
    key_bak                                  0x20000009   Data           1  key.o(.data)
    BRUSH_COLOR                              0x2000000a   Data           2  lcd.o(.data)
    BACK_COLOR                               0x2000000c   Data           2  lcd.o(.data)
    lcd_id                                   0x2000000e   Data           2  lcd.o(.data)
    dir_flag                                 0x20000010   Data           1  lcd.o(.data)
    lcd_width                                0x20000012   Data           2  lcd.o(.data)
    lcd_height                               0x20000014   Data           2  lcd.o(.data)
    write_gramcmd                            0x20000016   Data           2  lcd.o(.data)
    setxcmd                                  0x20000018   Data           2  lcd.o(.data)
    setycmd                                  0x2000001a   Data           2  lcd.o(.data)
    Touch_num                                0x2000001c   Data           1  ft5426.o(.data)
    Xdown                                    0x20000020   Data           2  xpt2046.o(.data)
    Ydown                                    0x20000022   Data           2  xpt2046.o(.data)
    Xup                                      0x20000024   Data           2  xpt2046.o(.data)
    Yup                                      0x20000026   Data           2  xpt2046.o(.data)
    CMD_RDX                                  0x20000028   Data           1  xpt2046.o(.data)
    CMD_RDY                                  0x20000029   Data           1  xpt2046.o(.data)
    xFactor                                  0x2000002c   Data           4  xpt2046.o(.data)
    yFactor                                  0x20000030   Data           4  xpt2046.o(.data)
    xOffset                                  0x20000034   Data           2  xpt2046.o(.data)
    yOffset                                  0x20000036   Data           2  xpt2046.o(.data)
    x                                        0x20000038   Data           2  xpt2046.o(.data)
    y                                        0x2000003a   Data           2  xpt2046.o(.data)
    time                                     0x2000003c   Data           1  xpt2046.o(.data)
    uwTick                                   0x20000040   Data           4  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000044   Data           4  stm32f4xx_hal.o(.data)
    uwTickFreq                               0x20000048   Data           1  stm32f4xx_hal.o(.data)
    LCDSRAM_Handler                          0x2000004c   Data          80  lcd.o(.bss)
    __libspace_start                         0x2000009c   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200000fc   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000061fc, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Base: 0x08000000, Size: 0x000061b0, Max: 0x00100000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x00000188   Data   RO          329    RESET               startup_stm32f407xx.o
    0x08000188   0x00000008   Code   RO          828  * !!!main             c_w.l(__main.o)
    0x08000190   0x00000034   Code   RO          995    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x0000001a   Code   RO          997    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x00000002   PAD
    0x080001e0   0x0000001c   Code   RO          999    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x00000002   Code   RO          865    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001fe   0x00000004   Code   RO          877    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          880    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          883    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          885    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          887    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          890    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          892    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          894    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          896    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          898    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          900    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          902    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          904    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          906    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          908    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          910    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          914    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          916    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          918    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          920    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000202   0x00000002   Code   RO          921    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000204   0x00000002   Code   RO          952    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000206   0x00000000   Code   RO          978    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000206   0x00000000   Code   RO          980    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000206   0x00000000   Code   RO          983    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000206   0x00000000   Code   RO          986    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000206   0x00000000   Code   RO          988    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000206   0x00000000   Code   RO          991    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000206   0x00000002   Code   RO          992    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000208   0x00000000   Code   RO          830    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000208   0x00000000   Code   RO          836    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000208   0x00000006   Code   RO          848    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800020e   0x00000000   Code   RO          838    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800020e   0x00000004   Code   RO          839    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000212   0x00000000   Code   RO          841    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000212   0x00000008   Code   RO          842    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800021a   0x00000002   Code   RO          869    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800021c   0x00000000   Code   RO          925    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800021c   0x00000004   Code   RO          926    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000220   0x00000006   Code   RO          927    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000226   0x00000002   PAD
    0x08000228   0x000000c4   Code   RO            4    .text               main.o
    0x080002ec   0x00000020   Code   RO          245    .text               stm32f4xx_it.o
    0x0800030c   0x00000120   Code   RO          275    .text               system_stm32f4xx.o
    0x0800042c   0x00000004   Code   RO          306    .text               stm32f4xx_hal_msp.o
    0x08000430   0x00000040   Code   RO          330    .text               startup_stm32f407xx.o
    0x08000470   0x0000005c   Code   RO          337    .text               led.o
    0x080004cc   0x00000124   Code   RO          361    .text               key.o
    0x080005f0   0x00000d70   Code   RO          391    .text               lcd.o
    0x08001360   0x000004b8   Code   RO          426    .text               ft5426.o
    0x08001818   0x00000194   Code   RO          465    .text               touch.o
    0x080019ac   0x00000488   Code   RO          493    .text               xpt2046.o
    0x08001e34   0x000001f4   Code   RO          523    .text               stm32f4xx_hal.o
    0x08002028   0x000003d0   Code   RO          550    .text               stm32f4xx_hal_cortex.o
    0x080023f8   0x000003b4   Code   RO          578    .text               stm32f4xx_hal_gpio.o
    0x080027ac   0x000008ec   Code   RO          650    .text               stm32f4xx_hal_rcc.o
    0x08003098   0x000008bc   Code   RO          722    .text               stm32f4xx_hal_dma.o
    0x08003954   0x00000388   Code   RO          747    .text               stm32f4xx_ll_fsmc.o
    0x08003cdc   0x00000380   Code   RO          771    .text               stm32f4xx_hal_sram.o
    0x0800405c   0x0000035c   Code   RO          796    .text               common.o
    0x080043b8   0x000000ee   Code   RO          824    .text               c_w.l(lludivv7m.o)
    0x080044a6   0x00000006   Code   RO          826    .text               c_w.l(heapauxi.o)
    0x080044ac   0x0000004a   Code   RO          852    .text               c_w.l(sys_stackheap_outer.o)
    0x080044f6   0x00000012   Code   RO          854    .text               c_w.l(exit.o)
    0x08004508   0x00000008   Code   RO          866    .text               c_w.l(libspace.o)
    0x08004510   0x0000000c   Code   RO          922    .text               c_w.l(sys_exit.o)
    0x0800451c   0x00000002   Code   RO          941    .text               c_w.l(use_no_semi.o)
    0x0800451e   0x00000000   Code   RO          943    .text               c_w.l(indicate_semi.o)
    0x0800451e   0x0000000a   Code   RO          937    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08004528   0x00000018   Data   RO          276    .constdata          system_stm32f4xx.o
    0x08004540   0x00001c34   Data   RO          393    .constdata          lcd.o
    0x08006174   0x00000014   Data   RO          427    .constdata          ft5426.o
    0x08006188   0x00000008   Data   RO          723    .constdata          stm32f4xx_hal_dma.o
    0x08006190   0x00000020   Data   RO          993    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Base: 0x20000000, Size: 0x00000700, Max: 0x00020000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x00000004   Data   RW          277    .data               system_stm32f4xx.o
    0x20000004   0x00000006   Data   RW          362    .data               key.o
    0x2000000a   0x00000012   Data   RW          394    .data               lcd.o
    0x2000001c   0x00000001   Data   RW          428    .data               ft5426.o
    0x2000001d   0x00000003   PAD
    0x20000020   0x0000001d   Data   RW          494    .data               xpt2046.o
    0x2000003d   0x00000003   PAD
    0x20000040   0x00000009   Data   RW          524    .data               stm32f4xx_hal.o
    0x20000049   0x00000001   Data   RW          797    .data               common.o
    0x2000004a   0x00000002   PAD
    0x2000004c   0x00000050   Zero   RW          392    .bss                lcd.o
    0x2000009c   0x00000060   Zero   RW          867    .bss                c_w.l(libspace.o)
    0x200000fc   0x00000004   PAD
    0x20000100   0x00000200   Zero   RW          328    HEAP                startup_stm32f407xx.o
    0x20000300   0x00000400   Zero   RW          327    STACK               startup_stm32f407xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       860         48          0          1          0       3309   common.o
      1208         72         20          1          0       4935   ft5426.o
       292         34          0          6          0       1815   key.o
      3440        154       7220         18         80      11557   lcd.o
        92          8          0          0          0       1011   led.o
       196         62          0          0          0     675087   main.o
        64         26        392          0       1536        908   startup_stm32f407xx.o
       500         42          0          9          0       6168   stm32f4xx_hal.o
       976         20          0          0          0      36410   stm32f4xx_hal_cortex.o
      2236         26          8          0          0       7289   stm32f4xx_hal_dma.o
       948         44          0          0          0       3775   stm32f4xx_hal_gpio.o
         4          0          0          0          0        660   stm32f4xx_hal_msp.o
      2284         66          0          0          0       5459   stm32f4xx_hal_rcc.o
       896          8          0          0          0       6552   stm32f4xx_hal_sram.o
        32          0          0          0          0       1462   stm32f4xx_it.o
       904         18          0          0          0       6668   stm32f4xx_ll_fsmc.o
       288         32         24          4          0       1853   system_stm32f4xx.o
       404         46          0          0          0       1347   touch.o
      1160        114          0         29          0       4054   xpt2046.o

    ----------------------------------------------------------------------
     16784        <USER>       <GROUP>         76       1616     780319   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         0          0          0          8          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         6          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       238          0          0          0          0        100   lludivv7m.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        10          0          0          0          0        116   fpinit.o

    ----------------------------------------------------------------------
       528         <USER>          <GROUP>          0        100        800   Library Totals
         4          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       514         16          0          0         96        684   c_w.l
        10          0          0          0          0        116   fz_wm.l

    ----------------------------------------------------------------------
       528         <USER>          <GROUP>          0        100        800   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     17312        836       7696         76       1716     777211   Grand Totals
     17312        836       7696         76       1716     777211   ELF Image Totals
     17312        836       7696         76          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                25008 (  24.42kB)
    Total RW  Size (RW Data + ZI Data)              1792 (   1.75kB)
    Total ROM Size (Code + RO Data + RW Data)      25084 (  24.50kB)

==============================================================================

