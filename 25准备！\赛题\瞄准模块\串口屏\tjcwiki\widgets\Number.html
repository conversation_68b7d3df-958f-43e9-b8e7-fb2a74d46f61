<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>数字控件 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="虚拟浮点数控件" href="Xfloat.html" />
    <link rel="prev" title="滚动文本控件" href="Scrolling_text.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">控件详解</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="component.html">认识控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Page.html">页面控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Text.html">文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Scrolling_text.html">滚动文本控件</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">数字控件</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#id2">数字控件-使用详解</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id3">给数字控件配置键盘</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id4">数字控件赋值</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id5">数字和文本之间相互转换</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id6">数字控件如何跨页面使用</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id7">修改数字控件显示文字的大小和样式</a></li>
<li class="toctree-l4"><a class="reference internal" href="#n0">通过名称组的方式来设置n0控件的属性</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id8">批量通过名称组的方式来设置数字控件的属性</a></li>
<li class="toctree-l4"><a class="reference internal" href="#rtc">使用数字控件显示RTC时钟数据</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id9">屏幕如何显示小数</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id10">单片机如何对数字控件赋值</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#c">数字控件-c语言示例</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id11">数字控件-样例工程下载</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id15">数字控件-相关链接</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id16">数字控件-属性详解</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="Xfloat.html">虚拟浮点数控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Button.html">按钮控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Progress_bar.html">进度条控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Picture.html">图片控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Crop.html">切图控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Hotspot.html">触摸热区控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="TouchCap.html">触摸捕捉控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Gauge.html">指针控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Waveform.html">曲线波形控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Slider.html">滑块控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Timer.html">定时器控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Variable.html">变量控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Dual-state_button.html">双态按钮控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Checkbox.html">复选框控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Radio.html">单选框控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="QRcode.html">二维码控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Switch.html">状态开关控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="ComboBox.html">下拉框控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="TextSelect.html">选择文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="SLText.html">滑动文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="DataRecord.html">数据记录控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="FileBrowser.html">文件浏览器控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="FileStream.html">文件流控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Gmov.html">动画控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Video.html">视频控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Audio.html">音频控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="ExPicture.html">外部图片控件</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">所有控件详解</a> &raquo;</li>
      <li>数字控件</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>数字控件<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<p>用于在串口屏上显示整形（int）类型的数据，使用前需要提前导入字库，制作字库请参考 <a class="reference internal" href="../start/create_project/create_project_5.html#id1"><span class="std std-ref">创建字库和导入字库</span></a></p>
<p>数字控件val赋值范围 -2147483648到2147483647</p>
<p>如何修改显示的字体大小：需要提前导入不同大小的字库，需要修改控件显示的字体大小时，通过上位机编辑或者通过指令修改控件的font属性即可，请参考 <a class="reference internal" href="../QA/QA45.html#id1"><span class="std std-ref">如何修改控件显示的字体</span></a></p>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>数字控件是可以显示负数的，如果负号没有显示，请检查当前调用的字库是否有”-”</p>
</div>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>导入字库请参考:  <a class="reference internal" href="../QA/QA115.html#id1"><span class="std std-ref">如何导入字库</span></a></p>
<p>导入图片请参考:  <a class="reference internal" href="../QA/QA116.html#id1"><span class="std std-ref">如何导入图片</span></a></p>
<p>导入动画请参考:  <a class="reference internal" href="../QA/QA117.html#id1"><span class="std std-ref">如何导入动画</span></a></p>
<p>导入视频请参考:  <a class="reference internal" href="../QA/QA118.html#id1"><span class="std std-ref">如何导入视频</span></a></p>
<p>导入音频请参考:  <a class="reference internal" href="../QA/QA119.html#id1"><span class="std std-ref">如何导入音频</span></a></p>
</div>
<section id="id2">
<h2>数字控件-使用详解<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<section id="id3">
<h3>给数字控件配置键盘<a class="headerlink" href="#id3" title="此标题的永久链接"></a></h3>
<img alt="../_images/number_0.jpg" src="../_images/number_0.jpg" />
<p>参考： <a class="reference internal" href="../advanced/keyboard/keyboard_base.html#keyboarduse"><span class="std std-ref">系统键盘的调用方式</span></a></p>
</section>
<section id="id4">
<h3>数字控件赋值<a class="headerlink" href="#id4" title="此标题的永久链接"></a></h3>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span> //n1赋值为100
<span class="linenos"> 2</span> n1.val<span class="o">=</span><span class="m">100</span>
<span class="linenos"> 3</span>
<span class="linenos"> 4</span> //n2赋值为-999
<span class="linenos"> 5</span> n2.val<span class="o">=</span>-999
<span class="linenos"> 6</span>
<span class="linenos"> 7</span> //n0赋值为n1.val
<span class="linenos"> 8</span> n0.val<span class="o">=</span>n1.val
<span class="linenos"> 9</span>
<span class="linenos">10</span> //加法
<span class="linenos">11</span> n0.val<span class="o">=</span>n1.val+n2.val
<span class="linenos">12</span>
<span class="linenos">13</span> //减法
<span class="linenos">14</span> n0.val<span class="o">=</span>n1.val-n2.val
<span class="linenos">15</span>
<span class="linenos">16</span> //乘法
<span class="linenos">17</span> n0.val<span class="o">=</span>n1.val*n2.val
<span class="linenos">18</span>
<span class="linenos">19</span> //除法
<span class="linenos">20</span> n0.val<span class="o">=</span>n1.val/n2.val
<span class="linenos">21</span>
<span class="linenos">22</span> //取余
<span class="linenos">23</span> n0.val<span class="o">=</span>n1.val%n2.val
<span class="linenos">24</span>
<span class="linenos">25</span> //跨页面赋值，前提是相关的变量vscope属性设置为全局
<span class="linenos">26</span> main.n0.val<span class="o">=</span>set.n1.val+set.n2.val
</pre></div>
</div>
</section>
<section id="id5">
<h3>数字和文本之间相互转换<a class="headerlink" href="#id5" title="此标题的永久链接"></a></h3>
<p><a class="reference internal" href="../commands/covx.html#covx"><span class="std std-ref">covx-变量类型转换</span></a></p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span> //文本控件赋值给数字控件
<span class="linenos">2</span> covx t0.txt,n0.val,0,0
<span class="linenos">3</span>
<span class="linenos">4</span> //数字控件赋值给文本控件
<span class="linenos">5</span> covx n0.val,t0.txt,0,0
</pre></div>
</div>
</section>
<section id="id6">
<h3>数字控件如何跨页面使用<a class="headerlink" href="#id6" title="此标题的永久链接"></a></h3>
<p>假设page0页面的n0数字控件，如果需要跨页面使用，需要将该控件的vscope设置为全局</p>
<p>在其他页面跨页面调用page0页面的n0数字控件</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span> //将page1页面的n0.val赋值为page0页面的n0.val
<span class="linenos">2</span> page1.n0.val<span class="o">=</span>page0.n0.val
<span class="linenos">3</span>
<span class="linenos">4</span> //将page0页面的n0.val发送出去
<span class="linenos">5</span> prints page0.n0.val,0
</pre></div>
</div>
</section>
<section id="id7">
<h3>修改数字控件显示文字的大小和样式<a class="headerlink" href="#id7" title="此标题的永久链接"></a></h3>
<img alt="../_images/number_2.jpg" src="../_images/number_2.jpg" />
<p>我们需要提前导入不同大小的字库，需要修改数字控件显示大小时，通过上位机进行编辑或者通过指令进行修改即可，请参考 <a class="reference internal" href="../QA/QA45.html#id1"><span class="std std-ref">如何修改控件显示的字体</span></a></p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span> //将n0控件的字体设置为1号字体
<span class="linenos">2</span> n0.font<span class="o">=</span><span class="m">1</span>
<span class="linenos">3</span>
<span class="linenos">4</span> //将main页面的n0控件的字体设置为1号字体，需要将n0控件的vscope属性设置为全局
<span class="linenos">5</span> main.n0.font<span class="o">=</span><span class="m">1</span>
</pre></div>
</div>
</section>
<section id="n0">
<h3>通过名称组的方式来设置n0控件的属性<a class="headerlink" href="#n0" title="此标题的永久链接"></a></h3>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span> <span class="nv">sys0</span><span class="o">=</span>n0.id
<span class="linenos">2</span> b<span class="o">[</span>sys0<span class="o">]</span>.val<span class="o">=</span><span class="m">123</span>
<span class="linenos">3</span> b<span class="o">[</span>sys0<span class="o">]</span>.font<span class="o">=</span><span class="m">1</span>
</pre></div>
</div>
</section>
<section id="id8">
<h3>批量通过名称组的方式来设置数字控件的属性<a class="headerlink" href="#id8" title="此标题的永久链接"></a></h3>
<p>假设n0存储的起始位置为100，n1存储位置104，n3存储位置108，以此类推，n9存储位置136</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span> <span class="nv">sys1</span><span class="o">=</span><span class="m">100</span>
<span class="linenos">2</span> <span class="k">for</span><span class="o">(</span><span class="nv">sys0</span><span class="o">=</span>n0.id<span class="p">;</span>sys0&lt;<span class="o">=</span>n9.id<span class="p">;</span>sys0++<span class="o">)</span>
<span class="linenos">3</span> <span class="o">{</span>
<span class="linenos">4</span>     repo b<span class="o">[</span>sys0<span class="o">]</span>.val,sys1
<span class="linenos">5</span>     <span class="nv">sys1</span><span class="o">+=</span><span class="m">4</span>
<span class="linenos">6</span> <span class="o">}</span>
</pre></div>
</div>
</section>
<section id="rtc">
<h3>使用数字控件显示RTC时钟数据<a class="headerlink" href="#rtc" title="此标题的永久链接"></a></h3>
<p>仅k0，x5系列支持，以下代码需要写在页面的前初始化事件以及定时器中定时执行，可以通过设置数字控件的length属性来设置显示的位数</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span> n0.val<span class="o">=</span>rtc0     //显示年份
<span class="linenos"> 2</span> n1.val<span class="o">=</span>rtc1     //显示月份
<span class="linenos"> 3</span> n2.val<span class="o">=</span>rtc2     //显示日期
<span class="linenos"> 4</span> n3.val<span class="o">=</span>rtc3     //显示小时
<span class="linenos"> 5</span> n4.val<span class="o">=</span>rtc4     //显示分钟
<span class="linenos"> 6</span> n5.val<span class="o">=</span>rtc5     //显示秒钟
<span class="linenos"> 7</span> //显示星期，需要使用文本控件
<span class="linenos"> 8</span> <span class="k">if</span><span class="o">(</span><span class="nv">rtc6</span><span class="o">==</span><span class="m">0</span><span class="o">)</span>
<span class="linenos"> 9</span> <span class="o">{</span>
<span class="linenos">10</span>     t0.txt<span class="o">=</span><span class="s2">&quot;星期日&quot;</span>
<span class="linenos">11</span> <span class="o">}</span><span class="k">else</span> <span class="k">if</span><span class="o">(</span><span class="nv">rtc6</span><span class="o">==</span><span class="m">1</span><span class="o">)</span>
<span class="linenos">12</span> <span class="o">{</span>
<span class="linenos">13</span>     t0.txt<span class="o">=</span><span class="s2">&quot;星期一&quot;</span>
<span class="linenos">14</span> <span class="o">}</span><span class="k">else</span> <span class="k">if</span><span class="o">(</span><span class="nv">rtc6</span><span class="o">==</span><span class="m">2</span><span class="o">)</span>
<span class="linenos">15</span> <span class="o">{</span>
<span class="linenos">16</span>     t0.txt<span class="o">=</span><span class="s2">&quot;星期二&quot;</span>
<span class="linenos">17</span> <span class="o">}</span><span class="k">else</span> <span class="k">if</span><span class="o">(</span><span class="nv">rtc6</span><span class="o">==</span><span class="m">3</span><span class="o">)</span>
<span class="linenos">18</span> <span class="o">{</span>
<span class="linenos">19</span>     t0.txt<span class="o">=</span><span class="s2">&quot;星期三&quot;</span>
<span class="linenos">20</span> <span class="o">}</span><span class="k">else</span> <span class="k">if</span><span class="o">(</span><span class="nv">rtc6</span><span class="o">==</span><span class="m">4</span><span class="o">)</span>
<span class="linenos">21</span> <span class="o">{</span>
<span class="linenos">22</span>     t0.txt<span class="o">=</span><span class="s2">&quot;星期四&quot;</span>
<span class="linenos">23</span> <span class="o">}</span><span class="k">else</span> <span class="k">if</span><span class="o">(</span><span class="nv">rtc6</span><span class="o">==</span><span class="m">5</span><span class="o">)</span>
<span class="linenos">24</span> <span class="o">{</span>
<span class="linenos">25</span>     t0.txt<span class="o">=</span><span class="s2">&quot;星期五&quot;</span>
<span class="linenos">26</span> <span class="o">}</span><span class="k">else</span> <span class="k">if</span><span class="o">(</span><span class="nv">rtc6</span><span class="o">==</span><span class="m">6</span><span class="o">)</span>
<span class="linenos">27</span> <span class="o">{</span>
<span class="linenos">28</span>     t0.txt<span class="o">=</span><span class="s2">&quot;星期六&quot;</span>
<span class="linenos">29</span> <span class="o">}</span>
</pre></div>
</div>
</section>
<section id="id9">
<h3>屏幕如何显示小数<a class="headerlink" href="#id9" title="此标题的永久链接"></a></h3>
<p>解决方法：使用虚拟浮点数控件或者文本控件。</p>
<p><a class="reference internal" href="Xfloat.html#id1"><span class="std std-ref">虚拟浮点数控件</span></a></p>
<p><a class="reference internal" href="Text.html#id1"><span class="std std-ref">文本控件</span></a></p>
<p>数字控件如何在一定情况不显示默认值</p>
<hr class="docutils" />
<p>解决方法一：数字控件默认值是一直有的，不能像文本控件为空。要实现数字控件默认值为空的话，可以先将数字控件属性font（字库）设置为不含数字的字库。赋值的时候再将font（字库）设置为包含数字的字库。</p>
<p>解决方法二：通过一个空白控件挡住，当数字控件不显示为0时，将空白控件通过vis指令隐藏。</p>
</section>
<section id="id10">
<h3>单片机如何对数字控件赋值<a class="headerlink" href="#id10" title="此标题的永久链接"></a></h3>
<p>1、在上位机工程新建一个数字控件，假设为n0，将程序下载到串口屏上，</p>
<p>2、串口屏串口与单片机串口连接，两者波特率应一致，单片机RX接串口屏TX，单片机TX接串口屏RX。</p>
<p>3、发送指令：单片机串口通过字符串模式发送n0.val=666</p>
<p>4、发送结束符：单片机通过HEX模式发送0xff 0xff 0xff</p>
<p>5、此时屏幕上的n0控件内的文字变为“666”</p>
</section>
</section>
<section id="c">
<h2>数字控件-c语言示例<a class="headerlink" href="#c" title="此标题的永久链接"></a></h2>
<p>单片机通过串口给数字控件赋值</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;n0.val=123</span><span class="se">\xff\xff\xff</span><span class="s">&quot;</span><span class="p">);</span><span class="w"></span>
<span class="linenos">2</span>
<span class="linenos">3</span><span class="w"> </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;n0.val=%d</span><span class="se">\xff\xff\xff</span><span class="s">&quot;</span><span class="p">,</span><span class="n">a</span><span class="p">);</span><span class="w"></span>
<span class="linenos">4</span>
<span class="linenos">5</span><span class="w"> </span><span class="c1">//赋值main页面的n0，前提是main页面的n0的vscope设置为全局</span>
<span class="linenos">6</span><span class="w"> </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;main.n0.val=%d</span><span class="se">\xff\xff\xff</span><span class="s">&quot;</span><span class="p">,</span><span class="n">a</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>给数字控件赋值必须是整形（int），否则会出错</p>
</div>
</section>
<section id="id11">
<h2>数字控件-样例工程下载<a class="headerlink" href="#id11" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/数字控件/数字控件赋值.HMI">《数字控件赋值》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/数字控件/数字控件显示RTC时钟数据.HMI">《数字控件显示RTC时钟数据》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/数字控件/数字控件写入和读取eeprom.HMI">《数字控件写入和读取eeprom》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/数字控件/通过名称组的方式来设置数字控件的属性.HMI">《通过名称组的方式来设置数字控件的属性》演示工程下载</a></p>
</section>
<section id="id15">
<h2>数字控件-相关链接<a class="headerlink" href="#id15" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="../QA/QA10.html#id1"><span class="std std-ref">哪些控件属性可以运行中修改，哪些不能运行中修改，绿色属性和黑色属性有什么区别？</span></a></p>
<p><a class="reference internal" href="../QA/QA11.html#txtval"><span class="std std-ref">txt属性和val属性有什么区别，为什么有些属性赋值要加双引号，有些不用加双引号</span></a></p>
<p><a class="reference internal" href="../grammar/global_variable.html#id1"><span class="std std-ref">跨页面赋值，全局变量操作</span></a></p>
</section>
<section id="id16">
<h2>数字控件-属性详解<a class="headerlink" href="#id16" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="component.html#id6"><span class="std std-ref">控件属性解析</span></a></p>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>绿色属性可以通过上位机或者串口屏指令进行修改，黑色属性只能在上位机中修改或者不可修改，可通过上位机进行修改指“选中控件后通过属性栏修改控件的属性”</p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">type属性</span></code> -控件类型，固定值，不同类型的控件type值不同，相同类型的控件type值相同，可读，不可通过上位机修改，不可通过指令修改。参考： <a class="reference internal" href="component.html#id10"><span class="std std-ref">控件属性-控件id对照表</span></a></p>
<p><code class="docutils literal notranslate"><span class="pre">id属性</span></code> -控件ID，可通过上位机左上角的上下箭头置顶或置底，可读，可通过上位机修改左上角的箭头置顶或置地间接修改，不可通过指令修改。参考： <a class="reference internal" href="../QA/QA28.html#id1"><span class="std std-ref">如何更改控件的前后图层关系</span></a></p>
<p><code class="docutils literal notranslate"><span class="pre">objname属性</span></code> -控件名称。不可读，可通过上位机进行修改，不可通过指令更改。</p>
<p><code class="docutils literal notranslate"><span class="pre">vscope属性</span></code> -内存占用(私有占用只能在当前页面被访问，全局占用可以在所有页面被访问)，当设置为私有时，跳转页面后，该控件占用的内存会被释放，重新返回该页面后该控件会恢复到最初的设置。可读，可通过上位机进行修改，不可通过指令更改。参考：<a class="reference internal" href="../grammar/global_variable.html#id1"><span class="std std-ref">跨页面赋值，全局变量操作</span></a></p>
<p><code class="docutils literal notranslate"><span class="pre">drag属性</span></code> -是否支持拖动:0-否;1-是。仅x系列支持。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">aph属性</span></code> -不透明度(0-127)，0为完全透明，127为完全不透明。仅x系列支持。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">effect属性</span></code> -加载特效:0-立即加载;1-上边飞入;2-下边飞入;3-左边飞入;4-右边飞入;5-左上角飞入;6-右上角飞入;7-左下角飞入;8-右下角飞入。仅x系列支持，在上位机中设置为立即加载时，无法通过指令变为其他特效，当在上位机中设置为非立即加载的特效时，可以变为立即加载，也可以再改为其他特效</p>
<p><code class="docutils literal notranslate"><span class="pre">sta属性</span></code> -背景填充方式:0-切图;1-单色;2-图片;3-透明（仅x系列支持透明）。可读，可通过上位机修改，不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">picc属性</span></code> -切图背景(必须是全屏图片)，sta为切图时才有这个属性。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">bco属性</span></code> -背景色，sta为单色时才有这个属性。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">style属性</span></code> -显示风格:0-平面;1-边框;2-3D_Down;3-3D_Up;4-3D_Auto，sta为单色时才有这个属性。可读，可通过上位机修改，不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">borderc属性</span></code> -边框颜色。当style设置为边框时可用。可读，可通过上位机修改，x系列可通过指令修改，其他系列不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">borderw属性</span></code> 边框粗细。当style设置为边框时可用。最大值:255。可读，可通过上位机修改，x系列可通过指令修改，其他系列不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">pic属性</span></code> -背景图片，sta为图片时才有这个属性。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">pco属性</span></code> -字体色。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">key属性</span></code> -绑定键盘。可读，可通过上位机修改，不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">font属性</span></code> -控件调用的字库id，调用不同的字库会显示不同的字体或字号。可读，可通过上位机修改，可通过指令修改。参考：1、 <a class="reference internal" href="../start/create_project/create_project_5.html#id1"><span class="std std-ref">创建字库和导入字库</span></a>  2、   <a class="reference internal" href="../QA/QA8.html#id1"><span class="std std-ref">指定字库</span></a></p>
<p><code class="docutils literal notranslate"><span class="pre">xcen属性</span></code> -水平对齐:0-靠左;1-居中;2-靠右。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">ycen属性</span></code> -垂直对齐:0-靠上;1-居中;2-靠下。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">val属性</span></code> -初始值(最小-2147483648,最大2147483647)。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">length属性</span></code> -显示位数(0为自动,最大15位)。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">format属性</span></code> -格式化类型:0-数字;1-货币;2-Hex。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">isbr属性</span></code> -是否自动换行:0-否;1-是。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">spax属性</span></code> -字符横向间距(最小0,最大255)。可读，可通过上位机修改，x系列可通过指令修改，其他系列不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">spay属性</span></code> -字符纵向间距(最小0,最大255)。可读，可通过上位机修改，x系列可通过指令修改，其他系列不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">x属性</span></code> -控件的X坐标。可读，可通过上位机修改，x系列可通过指令修改，其他系列不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">y属性</span></code> -控件的Y坐标。可读，可通过上位机修改，x系列可通过指令修改，其他系列不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">w属性</span></code> -控件的宽度。可读，可通过上位机修改，不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">h属性</span></code> -控件的高度。可读，可通过上位机修改，不可通过指令修改。</p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="Scrolling_text.html" class="btn btn-neutral float-left" title="滚动文本控件" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="Xfloat.html" class="btn btn-neutral float-right" title="虚拟浮点数控件" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>