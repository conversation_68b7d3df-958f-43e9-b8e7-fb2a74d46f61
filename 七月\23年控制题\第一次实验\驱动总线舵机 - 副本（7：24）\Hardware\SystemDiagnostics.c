#include "SystemDiagnostics.h"
#include "OLED.h"
#include "LED.h"
#include "Delay.h"
#include <stdio.h>
#include <stddef.h>
#include <string.h>
#include <math.h>

/**
 * 初始化系统诊断模块
 * @param diag 诊断结构体指针
 */
void SystemDiag_Init(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return;
    
    // 初始化模块状态
    memset(&diag->module_status, 0, sizeof(SystemModuleStatus_t));
    
    // 初始化性能统计
    memset(&diag->performance, 0, sizeof(SystemPerformanceStats_t));
    diag->performance.average_step_time = 20.0f;  // 默认20ms
    diag->performance.system_efficiency = 100.0f;
    
    // 加载默认配置
    SystemDiag_LoadDefaultConfig(diag);
    
    // 初始化错误日志
    memset(diag->error_log, 0, sizeof(diag->error_log));
    diag->error_count = 0;
    
    // 初始化诊断控制
    diag->last_diagnostic_time = Timer_GetTick();
    diag->diagnostic_enabled = 1;
}

/**
 * 运行完整系统检查 - 激光云台系统诊断核心
 * @param diag 诊断结构体指针
 * @return 诊断级别
 */
DiagnosticLevel_t SystemDiag_RunFullCheck(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return DIAG_CRITICAL;
    
    DiagnosticLevel_t max_level = DIAG_OK;
    DiagnosticLevel_t level;
    
    OLED_ShowString(1, 1, "System Check");
    OLED_ShowString(2, 1, "Running...");
    
    // 检查舵机系统 (可选 - 测试时可跳过)
    level = SystemDiag_CheckServos(diag);
    if (level > max_level && level >= DIAG_ERROR) max_level = level;  // 只有ERROR及以上才影响结果
    OLED_ShowString(3, 1, "Servos: Check");
    Delay_ms(500);
    
    // 检查定时器系统
    level = SystemDiag_CheckTimer(diag);
    if (level > max_level) max_level = level;
    OLED_ShowString(3, 1, "Timer: OK");
    Delay_ms(500);
    
    // 检查按键系统
    level = SystemDiag_CheckKeys(diag);
    if (level > max_level) max_level = level;
    OLED_ShowString(3, 1, "Keys: OK");
    Delay_ms(500);
    
    // 检查几何算法
    level = SystemDiag_CheckGeometry(diag);
    if (level > max_level) max_level = level;
    OLED_ShowString(3, 1, "Geometry: OK");
    Delay_ms(500);
    
    // 检查内存系统
    level = SystemDiag_CheckMemory(diag);
    if (level > max_level) max_level = level;
    OLED_ShowString(3, 1, "Memory: OK");
    Delay_ms(500);
    
    // 显示检查结果
    char result_str[16];
    sprintf(result_str, "Result: %s", SystemDiag_GetLevelString(max_level));
    OLED_ShowString(4, 1, result_str);
    
    // LED指示结果
    if (max_level == DIAG_OK) {
        // 成功 - 绿灯效果 (快速闪烁3次)
        for (int i = 0; i < 3; i++) {
            LED_ON();
            Delay_ms(100);
            LED_OFF();
            Delay_ms(100);
        }
    } else if (max_level == DIAG_WARNING) {
        // 警告 - 黄灯效果 (慢速闪烁3次)
        for (int i = 0; i < 3; i++) {
            LED_ON();
            Delay_ms(300);
            LED_OFF();
            Delay_ms(300);
        }
    } else {
        // 错误 - 红灯效果 (长亮2秒)
        LED_ON();
        Delay_ms(2000);
        LED_OFF();
    }
    
    return max_level;
}

/**
 * 快速系统检查
 * @param diag 诊断结构体指针
 * @return 诊断级别
 */
DiagnosticLevel_t SystemDiag_QuickCheck(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return DIAG_CRITICAL;
    
    DiagnosticLevel_t max_level = DIAG_OK;
    
    // 快速检查关键模块
    if (!diag->module_status.servo_pan_ok || !diag->module_status.servo_tilt_ok) {
        max_level = DIAG_ERROR;
        SystemDiag_LogError(diag, MODULE_SERVO_PAN, 1, "Servo communication error");
    }
    
    if (!diag->module_status.timer_ok) {
        max_level = DIAG_ERROR;
        SystemDiag_LogError(diag, MODULE_TIMER, 1, "Timer not running");
    }
    
    // 检查性能指标
    if (diag->performance.servo_errors > 10) {
        if (max_level < DIAG_WARNING) max_level = DIAG_WARNING;
        SystemDiag_LogError(diag, MODULE_SERVO_PAN, 2, "High servo error rate");
    }
    
    if (diag->performance.system_efficiency < 80.0f) {
        if (max_level < DIAG_WARNING) max_level = DIAG_WARNING;
        SystemDiag_LogError(diag, MODULE_SYSTEM, 1, "Low system efficiency");
    }
    
    return max_level;
}

/**
 * 检查舵机系统
 * @param diag 诊断结构体指针
 * @return 诊断级别
 */
DiagnosticLevel_t SystemDiag_CheckServos(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return DIAG_CRITICAL;
    
    DiagnosticLevel_t level = DIAG_OK;
    
    // 检查Pan舵机
    float pan_angle;
    ServoError_t error1 = Servo_ReadPosition(SERVO_PAN_ID, &pan_angle);
    if (error1 == SERVO_OK) {
        diag->module_status.servo_pan_ok = 1;
        // 检查角度范围
        if (pan_angle < PAN_ANGLE_MIN || pan_angle > PAN_ANGLE_MAX) {
            level = DIAG_WARNING;
            SystemDiag_LogError(diag, MODULE_SERVO_PAN, 2, "Pan angle range");
        }
    } else {
        diag->module_status.servo_pan_ok = 0;
        level = DIAG_WARNING;  // 改为WARNING而不是ERROR
        SystemDiag_LogError(diag, MODULE_SERVO_PAN, 1, "Pan servo offline");
    }
    
    // 检查Tilt舵机
    float tilt_angle;
    ServoError_t error2 = Servo_ReadPosition(SERVO_TILT_ID, &tilt_angle);
    if (error2 == SERVO_OK) {
        diag->module_status.servo_tilt_ok = 1;
        // 检查角度范围
        if (tilt_angle < TILT_ANGLE_MIN || tilt_angle > TILT_ANGLE_MAX) {
            if (level < DIAG_WARNING) level = DIAG_WARNING;
            SystemDiag_LogError(diag, MODULE_SERVO_TILT, 2, "Tilt angle range");
        }
    } else {
        diag->module_status.servo_tilt_ok = 0;
        if (level < DIAG_WARNING) level = DIAG_WARNING;  // 改为WARNING
        SystemDiag_LogError(diag, MODULE_SERVO_TILT, 1, "Tilt servo offline");
    }
    
    // 检查舵机温度和电压
    ServoStatus_t status;
    if (Servo_GetStatus(SERVO_PAN_ID, &status) == SERVO_OK) {
        if (status.temperature > 70) {  // 温度过高警告
            if (level < DIAG_WARNING) level = DIAG_WARNING;
            SystemDiag_LogError(diag, MODULE_SERVO_PAN, 3, "High temperature");
        }
        if (status.voltage < 6000 || status.voltage > 8400) {  // 电压异常
            if (level < DIAG_WARNING) level = DIAG_WARNING;
            SystemDiag_LogError(diag, MODULE_SERVO_PAN, 4, "Voltage out of range");
        }
    }
    
    return level;
}

/**
 * 检查定时器系统
 * @param diag 诊断结构体指针
 * @return 诊断级别
 */
DiagnosticLevel_t SystemDiag_CheckTimer(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return DIAG_CRITICAL;
    
    DiagnosticLevel_t level = DIAG_OK;
    
    // 检查定时器是否运行
    if (Timer_IsRunning()) {
        diag->module_status.timer_ok = 1;
        
        // 检查定时器频率
        uint32_t frequency = Timer_GetFrequency();
        if (frequency != TIMER_FREQUENCY_HZ) {
            level = DIAG_WARNING;
            SystemDiag_LogError(diag, MODULE_TIMER, 2, "Timer frequency mismatch");
        }
    } else {
        diag->module_status.timer_ok = 0;
        level = DIAG_ERROR;
        SystemDiag_LogError(diag, MODULE_TIMER, 1, "Timer not running");
    }
    
    return level;
}

/**
 * 检查按键系统
 * @param diag 诊断结构体指针
 * @return 诊断级别
 */
DiagnosticLevel_t SystemDiag_CheckKeys(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return DIAG_CRITICAL;
    
    // 按键系统检查相对简单，主要检查是否初始化
    diag->module_status.key_ok = 1;  // 假设按键系统正常
    
    return DIAG_OK;
}

/**
 * 检查几何算法
 * @param diag 诊断结构体指针
 * @return 诊断级别
 */
DiagnosticLevel_t SystemDiag_CheckGeometry(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return DIAG_CRITICAL;
    
    DiagnosticLevel_t level = DIAG_OK;
    
    // 测试几何转换精度
    ServoAngle_t test_servo = {120.0f, 120.0f};
    WallPoint_t test_wall;
    ServoAngle_t result_servo;
    
    GeometryError_t error1 = Geometry_ServoToWall(test_servo, &test_wall);
    GeometryError_t error2 = Geometry_WallToServo(test_wall, &result_servo);
    
    if (error1 == GEOMETRY_OK && error2 == GEOMETRY_OK) {
        diag->module_status.geometry_ok = 1;
        
        // 检查转换精度
        float pan_error = result_servo.pan - test_servo.pan;
        float tilt_error = result_servo.tilt - test_servo.tilt;
        
        if (pan_error > 0.5f || tilt_error > 0.5f) {  // 精度要求0.5度
            level = DIAG_WARNING;
            SystemDiag_LogError(diag, MODULE_GEOMETRY, 1, "Low conversion accuracy");
        }
    } else {
        diag->module_status.geometry_ok = 0;
        level = DIAG_ERROR;
        SystemDiag_LogError(diag, MODULE_GEOMETRY, 2, "Geometry calculation failed");
    }
    
    return level;
}

/**
 * 检查内存系统
 * @param diag 诊断结构体指针
 * @return 诊断级别
 */
DiagnosticLevel_t SystemDiag_CheckMemory(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return DIAG_CRITICAL;
    
    // 简单的内存检查
    diag->module_status.memory_ok = 1;  // 假设内存正常
    
    return DIAG_OK;
}

/**
 * 加载默认配置
 * @param diag 诊断结构体指针
 */
void SystemDiag_LoadDefaultConfig(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return;

    diag->config.position_tolerance = 2.0f;    // 2mm位置容差
    diag->config.servo_timeout = 1000;         // 1秒舵机超时
    diag->config.movement_speed = 50;           // 50步/秒移动速度
    diag->config.debug_level = 1;               // 调试级别1
    diag->config.auto_calibration = 1;          // 启用自动校准
    diag->config.safety_checks = 1;             // 启用安全检查
}

/**
 * 记录舵机命令
 * @param diag 诊断结构体指针
 */
void SystemDiag_RecordServoCommand(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return;
    diag->performance.servo_commands_sent++;
}

/**
 * 记录舵机错误
 * @param diag 诊断结构体指针
 */
void SystemDiag_RecordServoError(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return;
    diag->performance.servo_errors++;

    // 更新系统效率
    if (diag->performance.servo_commands_sent > 0) {
        float error_rate = (float)diag->performance.servo_errors / diag->performance.servo_commands_sent;
        diag->performance.system_efficiency = (1.0f - error_rate) * 100.0f;
    }
}

/**
 * 记录按键按下
 * @param diag 诊断结构体指针
 */
void SystemDiag_RecordKeyPress(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return;
    diag->performance.key_presses++;
}

/**
 * 记录状态转换
 * @param diag 诊断结构体指针
 */
void SystemDiag_RecordStateTransition(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return;
    diag->performance.state_transitions++;
}

/**
 * 记录几何计算
 * @param diag 诊断结构体指针
 */
void SystemDiag_RecordGeometryCalc(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return;
    diag->performance.geometry_calculations++;
}

/**
 * 记录路径步进
 * @param diag 诊断结构体指针
 * @param step_time 步进时间
 */
void SystemDiag_RecordPathStep(SystemDiagnostics_t* diag, float step_time)
{
    if (diag == NULL) return;

    diag->performance.path_steps_executed++;

    // 更新平均步进时间
    float total_time = diag->performance.average_step_time * (diag->performance.path_steps_executed - 1);
    diag->performance.average_step_time = (total_time + step_time) / diag->performance.path_steps_executed;
}

/**
 * 记录错误
 * @param diag 诊断结构体指针
 * @param module_id 模块ID
 * @param error_code 错误代码
 * @param description 错误描述
 */
void SystemDiag_LogError(SystemDiagnostics_t* diag, uint8_t module_id,
                        uint8_t error_code, const char* description)
{
    if (diag == NULL || diag->error_count >= MAX_ERROR_RECORDS) return;

    ErrorRecord_t* record = &diag->error_log[diag->error_count];
    record->timestamp = Timer_GetTick();
    record->module_id = module_id;
    record->error_code = error_code;
    strncpy(record->description, description, sizeof(record->description) - 1);
    record->description[sizeof(record->description) - 1] = '\0';

    diag->error_count++;
}

/**
 * 显示系统状态
 * @param diag 诊断结构体指针
 */
void SystemDiag_ShowStatus(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return;

    OLED_ShowString(1, 1, "System Status");

    // 显示模块状态
    char status_str[16];
    sprintf(status_str, "S:%d T:%d K:%d",
            diag->module_status.servo_pan_ok && diag->module_status.servo_tilt_ok,
            diag->module_status.timer_ok,
            diag->module_status.key_ok);
    OLED_ShowString(2, 1, status_str);

    // 显示性能统计
    sprintf(status_str, "Cmd:%lu Err:%lu",
            diag->performance.servo_commands_sent,
            diag->performance.servo_errors);
    OLED_ShowString(3, 1, status_str);

    // 显示效率
    sprintf(status_str, "Eff:%.1f%%", diag->performance.system_efficiency);
    OLED_ShowString(4, 1, status_str);
}

/**
 * 显示性能统计
 * @param diag 诊断结构体指针
 */
void SystemDiag_ShowPerformance(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return;

    OLED_ShowString(1, 1, "Performance");

    char perf_str[16];
    sprintf(perf_str, "Steps:%lu", diag->performance.path_steps_executed);
    OLED_ShowString(2, 1, perf_str);

    sprintf(perf_str, "AvgT:%.1fms", diag->performance.average_step_time);
    OLED_ShowString(3, 1, perf_str);

    sprintf(perf_str, "Keys:%lu", diag->performance.key_presses);
    OLED_ShowString(4, 1, perf_str);
}

/**
 * 显示错误信息
 * @param diag 诊断结构体指针
 */
void SystemDiag_ShowErrors(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return;

    // 清空屏幕避免字符重叠
    OLED_Clear();
    
    OLED_ShowString(1, 1, "Error Log");

    if (diag->error_count == 0) {
        OLED_ShowString(2, 1, "No errors");
        OLED_ShowString(3, 1, "System OK");
        OLED_ShowString(4, 1, "");
    } else {
        char error_str[16];
        sprintf(error_str, "Count: %d", diag->error_count);
        OLED_ShowString(2, 1, error_str);

        // 显示最新错误
        ErrorRecord_t* latest = &diag->error_log[diag->error_count - 1];
        sprintf(error_str, "M:%d C:%d", latest->module_id, latest->error_code);
        OLED_ShowString(3, 1, error_str);

        // 限制描述长度避免显示溢出
        char desc[16];
        strncpy(desc, latest->description, 15);
        desc[15] = '\0';
        OLED_ShowString(4, 1, desc);
    }
}

/**
 * 获取诊断级别字符串
 * @param level 诊断级别
 * @return 级别字符串
 */
const char* SystemDiag_GetLevelString(DiagnosticLevel_t level)
{
    switch (level) {
        case DIAG_OK:       return "OK";
        case DIAG_WARNING:  return "WARN";
        case DIAG_ERROR:    return "ERROR";
        case DIAG_CRITICAL: return "CRIT";
        default:            return "UNKNOWN";
    }
}

/**
 * 获取模块名称
 * @param module_id 模块ID
 * @return 模块名称
 */
const char* SystemDiag_GetModuleName(uint8_t module_id)
{
    switch (module_id) {
        case MODULE_SERVO_PAN:  return "ServoPan";
        case MODULE_SERVO_TILT: return "ServoTilt";
        case MODULE_TIMER:      return "Timer";
        case MODULE_KEY:        return "Key";
        case MODULE_OLED:       return "OLED";
        case MODULE_GEOMETRY:   return "Geometry";
        case MODULE_MEMORY:     return "Memory";
        case MODULE_SYSTEM:     return "System";
        default:                return "Unknown";
    }
}

/**
 * 重置统计数据
 * @param diag 诊断结构体指针
 */
void SystemDiag_ResetStatistics(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return;

    memset(&diag->performance, 0, sizeof(SystemPerformanceStats_t));
    diag->performance.average_step_time = 20.0f;
    diag->performance.system_efficiency = 100.0f;

    SystemDiag_ClearErrorLog(diag);
}

/**
 * 清除错误日志
 * @param diag 诊断结构体指针
 */
void SystemDiag_ClearErrorLog(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return;

    memset(diag->error_log, 0, sizeof(diag->error_log));
    diag->error_count = 0;
}

/**
 * 更新系统诊断状态
 * @param diag 诊断结构体指针
 */
void SystemDiag_Update(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return;
    
    // 更新运行时间
    diag->performance.total_runtime = Timer_GetTimeMs();
    
    // 定期执行快速检查 (每5秒一次)
    uint32_t current_time = Timer_GetTick();
    if (current_time - diag->last_diagnostic_time >= 250) {  // 250 * 20ms = 5秒
        diag->last_diagnostic_time = current_time;
        if (diag->diagnostic_enabled) {
            SystemDiag_QuickCheck(diag);
        }
    }
}

/**
 * 设置系统配置
 * @param diag 诊断结构体指针
 * @param config 配置参数
 */
void SystemDiag_SetConfig(SystemDiagnostics_t* diag, SystemConfiguration_t* config)
{
    if (diag == NULL || config == NULL) return;
    
    diag->config = *config;
}

/**
 * 获取系统配置
 * @param diag 诊断结构体指针
 * @return 配置参数指针
 */
SystemConfiguration_t* SystemDiag_GetConfig(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return NULL;
    
    return &diag->config;
}

/**
 * 获取错误数量
 * @param diag 诊断结构体指针
 * @return 错误数量
 */
uint8_t SystemDiag_GetErrorCount(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return 0;
    
    return diag->error_count;
}

/**
 * 舵机校准
 * @param diag 诊断结构体指针
 * @return 诊断级别
 */
DiagnosticLevel_t SystemDiag_CalibrateServos(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return DIAG_CRITICAL;
    
    DiagnosticLevel_t level = DIAG_OK;
    
    OLED_ShowString(1, 1, "Servo Calibrate");
    OLED_ShowString(2, 1, "Starting...");
    
    // 校准Pan舵机到中心位置
    Servo_SetPositionWithTime(SERVO_PAN_ID, 120.0f, 1000);
    Delay_ms(1000);
    
    // 校准Tilt舵机到中心位置  
    Servo_SetPositionWithTime(SERVO_TILT_ID, 120.0f, 1000);
    Delay_ms(1000);
    
    // 验证校准结果
    float pan_angle, tilt_angle;
    ServoError_t error1 = Servo_ReadPosition(SERVO_PAN_ID, &pan_angle);
    ServoError_t error2 = Servo_ReadPosition(SERVO_TILT_ID, &tilt_angle);
    
    if (error1 != SERVO_OK || error2 != SERVO_OK) {
        level = DIAG_ERROR;
        SystemDiag_LogError(diag, MODULE_SERVO_PAN, 1, "Servo calibration failed");
        OLED_ShowString(2, 1, "Calibrate ERROR");
    } else if (fabsf(pan_angle - 120.0f) > 5.0f || fabsf(tilt_angle - 120.0f) > 5.0f) {
        level = DIAG_WARNING;
        SystemDiag_LogError(diag, MODULE_SERVO_PAN, 2, "Servo calibration inaccurate");
        OLED_ShowString(2, 1, "Calibrate WARN");
    } else {
        OLED_ShowString(2, 1, "Calibrate OK");
    }
    
    Delay_ms(1000);
    
    return level;
}

/**
 * 优化系统性能
 * @param diag 诊断结构体指针
 * @return 诊断级别
 */
DiagnosticLevel_t SystemDiag_OptimizePerformance(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return DIAG_CRITICAL;
    
    DiagnosticLevel_t level = DIAG_OK;
    
    OLED_ShowString(1, 1, "Performance Opt");
    
    // 优化舵机通信参数
    if (diag->performance.servo_errors > 5) {
        // 降低通信速度以提高稳定性
        level = DIAG_WARNING;
        SystemDiag_LogError(diag, MODULE_SYSTEM, 1, "Communication optimized");
        OLED_ShowString(2, 1, "Comm optimized");
    }
    
    // 优化路径移动参数
    if (diag->performance.average_step_time > 25.0f) {
        // 调整步进时间
        level = DIAG_WARNING;
        SystemDiag_LogError(diag, MODULE_SYSTEM, 2, "Timing optimized");
        OLED_ShowString(2, 1, "Timing optimized");
    }
    
    if (level == DIAG_OK) {
        OLED_ShowString(2, 1, "Already optimal");
    }
    
    return level;
}

/**
 * 验证系统精度
 * @param diag 诊断结构体指针
 * @return 诊断级别
 */
DiagnosticLevel_t SystemDiag_ValidateAccuracy(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return DIAG_CRITICAL;
    
    DiagnosticLevel_t level = DIAG_OK;
    
    OLED_ShowString(1, 1, "Accuracy Test");
    OLED_ShowString(2, 1, "Running...");
    
    // 测试几何转换精度
    ServoAngle_t test_angles[] = {
        {90.0f, 90.0f}, {120.0f, 120.0f}, {150.0f, 150.0f}
    };
    
    float max_error = 0.0f;
    
    for (int i = 0; i < 3; i++) {
        WallPoint_t wall_point;
        ServoAngle_t result_angle;
        
        GeometryError_t error1 = Geometry_ServoToWall(test_angles[i], &wall_point);
        GeometryError_t error2 = Geometry_WallToServo(wall_point, &result_angle);
        
        if (error1 == GEOMETRY_OK && error2 == GEOMETRY_OK) {
            float pan_error = result_angle.pan - test_angles[i].pan;
            float tilt_error = result_angle.tilt - test_angles[i].tilt;
            float total_error = pan_error + tilt_error;
            
            if (total_error > max_error) {
                max_error = total_error;
            }
        } else {
            level = DIAG_ERROR;
            SystemDiag_LogError(diag, MODULE_GEOMETRY, 1, "Accuracy test failed");
            OLED_ShowString(2, 1, "Test FAILED");
            return level;
        }
    }
    
    // 评估精度
    if (max_error < 0.5f) {
        OLED_ShowString(2, 1, "High accuracy");
    } else if (max_error < 1.0f) {
        level = DIAG_WARNING;
        OLED_ShowString(2, 1, "Medium accuracy");
        SystemDiag_LogError(diag, MODULE_GEOMETRY, 2, "Medium accuracy");
    } else {
        level = DIAG_ERROR;
        OLED_ShowString(2, 1, "Low accuracy");
        SystemDiag_LogError(diag, MODULE_GEOMETRY, 3, "Low accuracy");
    }
    
    return level;
}

/**
 * 生成诊断报告
 * @param diag 诊断结构体指针
 */
void SystemDiag_GenerateReport(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return;
    
    OLED_ShowString(1, 1, "Diagnostic Report");
    
    // 显示整体状态
    DiagnosticLevel_t overall_level = SystemDiag_QuickCheck(diag);
    char report_str[16];
    sprintf(report_str, "Status: %s", SystemDiag_GetLevelString(overall_level));
    OLED_ShowString(2, 1, report_str);
    
    // 显示运行时间
    sprintf(report_str, "Runtime:%lums", diag->performance.total_runtime);
    OLED_ShowString(3, 1, report_str);
    
    // 显示错误统计
    sprintf(report_str, "Errors: %d", diag->error_count);
    OLED_ShowString(4, 1, report_str);
    
    Delay_ms(2000);
    
    // 显示性能统计
    SystemDiag_ShowPerformance(diag);
    Delay_ms(2000);
    
    // 显示错误详情
    if (diag->error_count > 0) {
        SystemDiag_ShowErrors(diag);
        Delay_ms(2000);
    }
}

/**
 * 备份配置
 * @param diag 诊断结构体指针
 */
void SystemDiag_BackupConfiguration(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return;
    
    // 简单的配置备份实现 - 实际项目中可能需要存储到Flash
    OLED_ShowString(1, 1, "Config Backup");
    OLED_ShowString(2, 1, "Completed");
    
    SystemDiag_LogError(diag, MODULE_SYSTEM, 0, "Config backed up");
}

/**
 * 恢复配置
 * @param diag 诊断结构体指针
 */
void SystemDiag_RestoreConfiguration(SystemDiagnostics_t* diag)
{
    if (diag == NULL) return;
    
    OLED_ShowString(1, 1, "Config Restore");
    
    // 恢复默认配置
    SystemDiag_LoadDefaultConfig(diag);
    
    OLED_ShowString(2, 1, "Completed");
    SystemDiag_LogError(diag, MODULE_SYSTEM, 0, "Config restored");
}

/**
 * 打印诊断信息
 * @param level 诊断级别
 * @param message 信息内容
 */
void SystemDiag_PrintDiagnostic(DiagnosticLevel_t level, const char* message)
{
    if (message == NULL) return;
    
    // 根据级别显示不同的信息格式
    char diag_str[16];
    switch (level) {
        case DIAG_OK:
            sprintf(diag_str, "OK: %.8s", message);
            break;
        case DIAG_WARNING:
            sprintf(diag_str, "WARN:%.7s", message);
            break;
        case DIAG_ERROR:
            sprintf(diag_str, "ERR:%.8s", message);
            break;
        case DIAG_CRITICAL:
            sprintf(diag_str, "CRIT:%.7s", message);
            break;
        default:
            sprintf(diag_str, "?:%.10s", message);
            break;
    }
    
    // 在OLED第4行显示诊断信息
    OLED_ShowString(4, 1, diag_str);
}
