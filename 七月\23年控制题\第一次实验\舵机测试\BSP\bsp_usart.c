/**
  ******************************************************************************
  * @file    bsp_usart.c
  * <AUTHOR> Analysis Expert
  * @version V1.0
  * @date    2025-07-17
  * @brief   USART板级支持包实现
  ******************************************************************************
  */

#include "bsp_usart.h"

/* 外部变量声明 */
extern volatile uint32_t g_systick_count;

/**
  * @brief  USART2初始化（半双工模式）
  * @param  None
  * @retval None
  */
void BSP_USART2_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;
    
    // 使能时钟
    RCC_AHB1PeriphClockCmd(BSP_USART2_GPIO_CLK, ENABLE);
    RCC_APB1PeriphClockCmd(BSP_USART2_CLK, ENABLE);
    
    // 配置PA2为复用功能
    GPIO_InitStructure.GPIO_Pin = BSP_USART2_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_OD;  // 开漏输出
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;   // 上拉
    GPIO_Init(BSP_USART2_GPIO_PORT, &GPIO_InitStructure);
    
    // 配置复用功能
    GPIO_PinAFConfig(BSP_USART2_GPIO_PORT, BSP_USART2_PINSOURCE, BSP_USART2_AF);
    
    // 配置USART2
    USART_InitStructure.USART_BaudRate = BSP_USART2_BAUDRATE;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;
    USART_Init(BSP_USART2, &USART_InitStructure);
    
    // 使能半双工模式
    USART_HalfDuplexCmd(BSP_USART2, ENABLE);
    
    // 使能USART2
    USART_Cmd(BSP_USART2, ENABLE);
}

/**
  * @brief  USART2发送数据（阻塞模式）
  * @param  data: 发送数据缓冲区
  * @param  len: 数据长度
  * @param  timeout: 超时时间(ms)
  * @retval 0:成功, -1:超时
  */
int BSP_USART2_SendData(uint8_t *data, uint16_t len, uint32_t timeout)
{
    uint32_t start_time = g_systick_count;
    uint16_t i;
    
    for(i = 0; i < len; i++)
    {
        // 等待发送缓冲区空
        while(USART_GetFlagStatus(BSP_USART2, USART_FLAG_TXE) == RESET)
        {
            if((g_systick_count - start_time) > timeout)
                return -1;  // 超时
        }
        
        // 发送数据
        USART_SendData(BSP_USART2, data[i]);
    }
    
    // 等待发送完成
    while(USART_GetFlagStatus(BSP_USART2, USART_FLAG_TC) == RESET)
    {
        if((g_systick_count - start_time) > timeout)
            return -1;  // 超时
    }
    
    return 0;  // 成功
}

/**
  * @brief  USART2接收数据（阻塞模式）
  * @param  data: 接收数据缓冲区
  * @param  len: 期望接收长度
  * @param  timeout: 超时时间(ms)
  * @retval 实际接收长度, -1:超时
  */
int BSP_USART2_ReceiveData(uint8_t *data, uint16_t len, uint32_t timeout)
{
    uint32_t start_time = g_systick_count;
    uint16_t i = 0;
    
    // 清除接收标志
    USART_ClearFlag(BSP_USART2, USART_FLAG_RXNE);
    
    while(i < len)
    {
        // 等待接收数据
        while(USART_GetFlagStatus(BSP_USART2, USART_FLAG_RXNE) == RESET)
        {
            if((g_systick_count - start_time) > timeout)
                return i;  // 返回已接收长度
        }
        
        // 读取数据
        data[i] = USART_ReceiveData(BSP_USART2);
        i++;
    }
    
    return i;  // 返回接收长度
}
