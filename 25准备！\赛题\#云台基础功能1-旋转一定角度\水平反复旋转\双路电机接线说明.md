# STM32F407双路步进电机接线说明

## 系统概述
- **主控**: STM32F407ZGT6
- **驱动器**: D36A双路步进电机驱动器  
- **电机**: 42步进电机 × 2

## 电机A（第一路）接线

### STM32F407 → D36A-A路
```
PC8  → STEP-A  (脉冲信号)
PC13 → DIR-A   (方向控制)
PD2  → SLEEP-A (使能控制，低电平使能)
GND  → GND
```

### 42步进电机 → D36A-A路输出
```
电机线序    D36A接口
1: A+ (红) → A+
2: 公共线  → (不接)
3: A- (蓝) → A-
4: B+ (绿) → B+
5: 公共线  → (不接)  
6: B- (黑) → B-
```

## 电机B（第二路）接线

### STM32F407 → D36A-B路
```
PC9  → STEP-B  (脉冲信号)
PB12 → DIR-B   (方向控制)
PC12 → SLEEP-B (使能控制，低电平使能)
GND  → GND
```

### 42步进电机 → D36A-B路输出
```
电机线序    D36A接口
1: A+ (红) → A+
2: 公共线  → (不接)
3: A- (蓝) → A-
4: B+ (绿) → B+
5: 公共线  → (不接)
6: B- (黑) → B-
```

## 供电接线
```
12-24V电源正极 → D36A VCC
12-24V电源负极 → D36A GND
```

## 重要提醒
1. **线序必须正确**：步进电机线序错误会导致抖动
2. **使能逻辑**：SLEEP引脚低电平使能，高电平关闭
3. **供电电压**：推荐使用24V供电以获得更好性能
4. **细分设置**：D36A默认1/16细分，提供高精度控制

## PWM参数
- **频率**: 3.33kHz (`STEP12_PWM_Init(7199, 6)`)
- **占空比**: 50% (`TIM_Pulse = 3600`)
- **步进分辨率**: 0.1125°/步 (1/16细分)