#include "Bluetooth.h"
#include "Delay.h"
#include "Timer.h"

// 全局蓝牙控制变量
BluetoothControl_t bluetooth_ctrl;

/**
 * @brief  发送按键操作信息 (简化版)
 * @param  gimbal_state: 云台状态指针
 * @param  key_action: 按键动作描述
 * @retval None
 */
void Bluetooth_SendKeyAction(LaserGimbalState_t* gimbal_state, char* key_action)
{
    char status_buffer[256];

    // 根据当前状态发送状态信息
    char* state_desc = "";
    switch(gimbal_state->current_state) {
        case STATE_WAIT_POINT_A:
            state_desc = "Wait Point A";
            break;
        case STATE_WAIT_POINT_B:
            state_desc = "Wait Point B";
            break;
        case STATE_AUTO_MOVING:
            state_desc = "Auto Moving";
            break;
        default:
            state_desc = "Unknown";
            break;
    }

    sprintf(status_buffer,
        "\r\n[Laser Gimbal] %s\r\n"
        "State: %s\r\n"
        "Point A: %s | Point B: %s\r\n",
        key_action,
        state_desc,
        gimbal_state->point_a_recorded ? "Recorded" : "Not Set",
        gimbal_state->point_b_recorded ? "Recorded" : "Not Set"
    );

    Bluetooth_SendString(status_buffer);
    bluetooth_ctrl.packet_sent++;
}

/**
 * @brief  发送错误报告 (简化版)
 * @param  error: 错误报告指针
 * @retval None
 */
void Bluetooth_SendErrorReport(ErrorReport_t* error)
{
    char error_buffer[256];

    // 错误级别描述
    char* severity_desc = "";
    switch(error->severity) {
        case DIAG_OK:
            severity_desc = "INFO";
            break;
        case DIAG_WARNING:
            severity_desc = "WARNING";
            break;
        case DIAG_ERROR:
            severity_desc = "ERROR";
            break;
        default:
            severity_desc = "CRITICAL";
            break;
    }

    sprintf(error_buffer,
        "\r\n[SYSTEM %s]\r\n"
        "Message: %s\r\n"
        "Module: %d | Code: %d\r\n",
        severity_desc,
        error->error_message,
        error->module_id,
        error->error_code
    );

    Bluetooth_SendString(error_buffer);
    bluetooth_ctrl.packet_sent++;
}

/**
 * @brief  发送性能数据报告
 * @param  perf: 性能数据指针
 * @retval None
 */
void Bluetooth_SendPerformanceData(SystemPerformanceStats_t* perf)
{
    char perf_buffer[512];

    sprintf(perf_buffer,
        "\r\n=== PERFORMANCE REPORT ===\r\n"
        "Total Runtime: %lu ms\r\n"
        "Servo Commands: %lu\r\n"
        "Servo Errors: %lu\r\n"
        "Key Presses: %lu\r\n"
        "State Transitions: %lu\r\n"
        "==========================\r\n",
        perf->total_runtime,
        perf->servo_commands_sent,
        perf->servo_errors,
        perf->key_presses,
        perf->state_transitions
    );

    Bluetooth_SendString(perf_buffer);
    bluetooth_ctrl.packet_sent++;
}

/**
 * @brief  发送位置信息报告
 * @param  servo_angle: 舵机角度
 * @param  wall_point: 墙面坐标
 * @retval None
 */
void Bluetooth_SendPositionInfo(ServoAngle_t servo_angle, WallPoint_t wall_point)
{
    char pos_buffer[256];

    sprintf(pos_buffer,
        "\r\n=== POSITION INFO ===\r\n"
        "Servo Pan: %.2f°\r\n"
        "Servo Tilt: %.2f°\r\n"
        "Wall X: %.1f mm\r\n"
        "Wall Y: %.1f mm\r\n"
        "=====================\r\n",
        servo_angle.pan,
        servo_angle.tilt,
        wall_point.x,
        wall_point.y
    );

    Bluetooth_SendString(pos_buffer);
    bluetooth_ctrl.packet_sent++;
}

/**
 * @brief  发送诊断信息报告
 * @param  diag: 系统诊断指针
 * @retval None
 */
void Bluetooth_SendDiagnosticInfo(SystemDiagnostics_t* diag)
{
    char diag_buffer[512];

    sprintf(diag_buffer,
        "\r\n=== DIAGNOSTIC INFO ===\r\n"
        "Pan Servo: %s\r\n"
        "Tilt Servo: %s\r\n"
        "Timer: %s\r\n"
        "Keys: %s\r\n"
        "OLED: %s\r\n"
        "Geometry: %s\r\n"
        "Memory: %s\r\n"
        "Error Count: %d\r\n"
        "=======================\r\n",
        diag->module_status.servo_pan_ok ? "OK" : "ERROR",
        diag->module_status.servo_tilt_ok ? "OK" : "ERROR",
        diag->module_status.timer_ok ? "OK" : "ERROR",
        diag->module_status.key_ok ? "OK" : "ERROR",
        diag->module_status.oled_ok ? "OK" : "ERROR",
        diag->module_status.geometry_ok ? "OK" : "ERROR",
        diag->module_status.memory_ok ? "OK" : "ERROR",
        diag->error_count
    );

    Bluetooth_SendString(diag_buffer);
    bluetooth_ctrl.packet_sent++;
}

/**
 * @brief  USART2初始化 (用于JDY-31蓝牙模块)
 * @param  None
 * @retval None
 */
static void USART2_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;
    
    // 使能时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART2, ENABLE);
    
    // 配置PA2 (USART2_TX)
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    // 配置PA3 (USART2_RX)
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_3;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING;
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    // 配置USART2参数
    USART_InitStructure.USART_BaudRate = BLUETOOTH_BAUDRATE;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
    USART_Init(USART2, &USART_InitStructure);
    
    // 使能接收中断
    USART_ITConfig(USART2, USART_IT_RXNE, ENABLE);
    
    // 配置NVIC中断
    NVIC_InitStructure.NVIC_IRQChannel = USART2_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 3;  // 较低优先级
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
    
    // 使能USART2
    USART_Cmd(USART2, ENABLE);
}

/**
 * @brief  蓝牙模块初始化
 * @param  None
 * @retval None
 */
void Bluetooth_Init(void)
{
    // 初始化USART2
    USART2_Init();
    
    // 初始化控制结构体
    bluetooth_ctrl.status = BT_DISCONNECTED;
    bluetooth_ctrl.last_status_report = 0;
    bluetooth_ctrl.last_heartbeat = 0;
    bluetooth_ctrl.error_count = 0;
    bluetooth_ctrl.packet_sent = 0;
    bluetooth_ctrl.packet_received = 0;
    
    bluetooth_ctrl.rx_head = 0;
    bluetooth_ctrl.rx_tail = 0;
    bluetooth_ctrl.rx_complete = 0;
    bluetooth_ctrl.tx_head = 0;
    bluetooth_ctrl.tx_tail = 0;
    bluetooth_ctrl.tx_busy = 0;
    bluetooth_ctrl.cmd_ready = 0;
    
    // 延时等待蓝牙模块稳定
    Delay_ms(1000);
    
    // 配置蓝牙模块
    Bluetooth_ConfigureModule();
    
    // 发送中文初始化完成消息
    Bluetooth_SendStartupInfo();
    
    bluetooth_ctrl.status = BT_CONNECTED;
}

/**
 * @brief  蓝牙模块配置
 * @param  None
 * @retval None
 */
void Bluetooth_ConfigureModule(void)
{
    // 设置蓝牙名称
    Bluetooth_SetName(BLUETOOTH_NAME);
    Delay_ms(500);
    
    // 设置蓝牙密码
    Bluetooth_SetPin(BLUETOOTH_PIN);
    Delay_ms(500);
    
    // 测试连接
    Bluetooth_TestConnection();
}

/**
 * @brief  设置蓝牙名称
 * @param  name: 蓝牙名称
 * @retval None
 */
void Bluetooth_SetName(char* name)
{
    char cmd[64];
    sprintf(cmd, "AT+NAME%s\r\n", name);
    Bluetooth_SendString(cmd);
}

/**
 * @brief  设置蓝牙密码
 * @param  pin: 蓝牙密码
 * @retval None
 */
void Bluetooth_SetPin(char* pin)
{
    char cmd[32];
    sprintf(cmd, "AT+PIN%s\r\n", pin);
    Bluetooth_SendString(cmd);
}

/**
 * @brief  测试蓝牙连接
 * @param  None
 * @retval None
 */
void Bluetooth_TestConnection(void)
{
    Bluetooth_SendString("AT\r\n");
    Delay_ms(100);
}

/**
 * @brief  发送字符串
 * @param  str: 要发送的字符串
 * @retval None
 */
void Bluetooth_SendString(char* str)
{
    while (*str) {
        Bluetooth_SendByte(*str++);
    }
}

/**
 * @brief  发送单个字节
 * @param  byte: 要发送的字节
 * @retval None
 */
void Bluetooth_SendByte(uint8_t byte)
{
    // 等待发送缓冲区为空
    while (USART_GetFlagStatus(USART2, USART_FLAG_TXE) == RESET);
    
    // 发送数据
    USART_SendData(USART2, byte);
    
    // 等待发送完成
    while (USART_GetFlagStatus(USART2, USART_FLAG_TC) == RESET);
}

/**
 * @brief  蓝牙更新函数 (在主循环中调用) - 简化版，只处理接收数据
 * @param  None
 * @retval None
 */
void Bluetooth_Update(void)
{
    // 只处理接收到的数据，不再定期发送心跳包
    if (bluetooth_ctrl.rx_complete) {
        Bluetooth_ProcessReceivedData();
        bluetooth_ctrl.rx_complete = 0;
    }
}

/**
 * @brief  发送系统启动信息 (简化版)
 * @param  None
 * @retval None
 */
void Bluetooth_SendStartupInfo(void)
{
    char startup_buffer[256];

    sprintf(startup_buffer,
        "\r\n=== Laser Gimbal System v1.1 ===\r\n"
        "Bluetooth: JDY-31 Ready\r\n"
        "Status: System Ready\r\n"
        "Controls:\r\n"
        "- PB0: Record Points\r\n"
        "- PB1: Start Auto Movement\r\n"
        "Commands: STATUS, HELP, INFO\r\n"
    );

    Bluetooth_SendString(startup_buffer);
    bluetooth_ctrl.packet_sent++;
}

/**
 * @brief  获取蓝牙状态
 * @param  None
 * @retval BluetoothStatus_t 蓝牙状态
 */
BluetoothStatus_t Bluetooth_GetStatus(void)
{
    return bluetooth_ctrl.status;
}

/**
 * @brief  USART2中断服务函数
 * @param  None
 * @retval None
 */
void USART2_IRQHandler(void)
{
    if (USART_GetITStatus(USART2, USART_IT_RXNE) != RESET) {
        uint8_t received_byte = USART_ReceiveData(USART2);
        
        // 将接收到的数据存入缓冲区
        bluetooth_ctrl.rx_buffer[bluetooth_ctrl.rx_head] = received_byte;
        bluetooth_ctrl.rx_head = (bluetooth_ctrl.rx_head + 1) % BT_RX_BUFFER_SIZE;
        
        // 检查是否接收到完整命令 (以\r\n结尾)
        if (received_byte == '\n') {
            bluetooth_ctrl.rx_complete = 1;
        }
        
        // 清除中断标志
        USART_ClearITPendingBit(USART2, USART_IT_RXNE);
    }
}

/**
 * @brief  处理接收到的数据
 * @param  None
 * @retval None
 */
void Bluetooth_ProcessReceivedData(void)
{
    // 提取完整的命令行
    uint16_t cmd_len = 0;
    uint16_t tail = bluetooth_ctrl.rx_tail;
    
    while (tail != bluetooth_ctrl.rx_head && cmd_len < BT_CMD_BUFFER_SIZE - 1) {
        bluetooth_ctrl.cmd_buffer[cmd_len] = bluetooth_ctrl.rx_buffer[tail];
        if (bluetooth_ctrl.rx_buffer[tail] == '\n') {
            break;
        }
        tail = (tail + 1) % BT_RX_BUFFER_SIZE;
        cmd_len++;
    }
    
    bluetooth_ctrl.cmd_buffer[cmd_len] = '\0';
    bluetooth_ctrl.rx_tail = (tail + 1) % BT_RX_BUFFER_SIZE;
    
    // 解析命令
    if (cmd_len > 0) {
        Bluetooth_ParseCommand((char*)bluetooth_ctrl.cmd_buffer);
        bluetooth_ctrl.packet_received++;
    }
}

/**
 * @brief  解析接收到的命令
 * @param  cmd: 命令字符串
 * @retval None
 */
void Bluetooth_ParseCommand(char* cmd)
{
    // 简单的命令解析示例
    if (strstr(cmd, "STATUS") != NULL) {
        Bluetooth_SendString("[CMD] Status request received\r\n");
    }
    else if (strstr(cmd, "HELP") != NULL) {
        Bluetooth_SendString("[HELP] Available commands: STATUS, HELP, INFO\r\n");
    }
    else if (strstr(cmd, "INFO") != NULL) {
        Bluetooth_PrintConnectionInfo();
    }
    else {
        Bluetooth_SendString("[ERROR] Unknown command\r\n");
    }
}

/**
 * @brief  打印连接信息
 * @param  None
 * @retval None
 */
void Bluetooth_PrintConnectionInfo(void)
{
    char info[256];
    sprintf(info, "[INFO] Module: JDY-31, Name: %s, PIN: %s, Baud: %d\r\n",
            BLUETOOTH_NAME, BLUETOOTH_PIN, BLUETOOTH_BAUDRATE);
    Bluetooth_SendString(info);
    
    sprintf(info, "[INFO] Packets - Sent: %lu, Received: %lu, Errors: %lu\r\n",
            bluetooth_ctrl.packet_sent, bluetooth_ctrl.packet_received, bluetooth_ctrl.error_count);
    Bluetooth_SendString(info);
}
