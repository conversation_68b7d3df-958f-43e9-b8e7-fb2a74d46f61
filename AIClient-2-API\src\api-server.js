#!/usr/bin/env node

const express = require('express');
const cors = require('cors');
const fs = require('fs-extra');
const path = require('path');
const yargs = require('yargs');
const winston = require('winston');

// 配置日志
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.printf(({ timestamp, level, message }) => {
      return `${timestamp} [${level.toUpperCase()}]: ${message}`;
    })
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'aiclient2api.log' })
  ]
});

// 解析命令行参数
const argv = yargs
  .option('model-provider', {
    alias: 'p',
    description: '模型提供商',
    type: 'string',
    default: 'kiro-api'
  })
  .option('host', {
    description: '服务器主机地址',
    type: 'string',
    default: 'localhost'
  })
  .option('port', {
    description: '服务器端口',
    type: 'number',
    default: 3000
  })
  .option('api-key', {
    description: 'API 密钥',
    type: 'string',
    default: '123456'
  })
  .option('config', {
    alias: 'c',
    description: '配置文件路径',
    type: 'string',
    default: './config.json'
  })
  .option('kiro-oauth-creds-file', {
    description: 'Kiro OAuth 凭据文件路径',
    type: 'string'
  })
  .option('kiro-oauth-creds-base64', {
    description: 'Kiro OAuth 凭据 Base64 编码',
    type: 'string'
  })
  .help()
  .alias('help', 'h')
  .argv;

// 加载配置
let config = {};
try {
  if (fs.existsSync(argv.config)) {
    config = JSON.parse(fs.readFileSync(argv.config, 'utf8'));
  } else {
    // 默认配置
    config = {
      "REQUIRED_API_KEY": "123456",
      "SERVER_PORT": 3000,
      "HOST": "localhost",
      "MODEL_PROVIDER": "kiro-api",
      "PROMPT_LOG_MODE": "console",
      "KIRO_OAUTH_CREDS_FILE": "./kiro-credentials.json",
      "KIRO_OAUTH_CREDS_BASE64": ""
    };
    logger.info(`配置文件不存在，使用默认配置`);
  }
} catch (error) {
  logger.error(`加载配置文件失败: ${error.message}`);
  process.exit(1);
}

// 命令行参数覆盖配置文件
if (argv.host) config.HOST = argv.host;
if (argv.port) config.SERVER_PORT = argv.port;
if (argv.apiKey) config.REQUIRED_API_KEY = argv.apiKey;
if (argv.modelProvider) config.MODEL_PROVIDER = argv.modelProvider;
if (argv.kiroOauthCredsFile) config.KIRO_OAUTH_CREDS_FILE = argv.kiroOauthCredsFile;
if (argv.kiroOauthCredsBase64) config.KIRO_OAUTH_CREDS_BASE64 = argv.kiroOauthCredsBase64;

// 创建 Express 应用
const app = express();

// 中间件
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// 请求日志中间件
app.use((req, res, next) => {
  if (config.PROMPT_LOG_MODE === 'console' || config.PROMPT_LOG_MODE === 'file') {
    logger.info(`${req.method} ${req.path} - ${req.ip}`);
  }
  next();
});

// API Key 验证中间件
const validateApiKey = (req, res, next) => {
  const authHeader = req.headers.authorization;
  const apiKey = req.headers['x-api-key'];
  
  if (config.REQUIRED_API_KEY) {
    const providedKey = authHeader?.replace('Bearer ', '') || apiKey;
    if (providedKey !== config.REQUIRED_API_KEY) {
      return res.status(401).json({ error: 'Invalid API key' });
    }
  }
  next();
};

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    provider: config.MODEL_PROVIDER,
    version: '1.0.0'
  });
});

// 模型列表端点
app.get('/v1/models', validateApiKey, (req, res) => {
  const models = {
    "object": "list",
    "data": []
  };

  if (config.MODEL_PROVIDER === 'kiro-api' || config.MODEL_PROVIDER === 'claude-kiro-oauth') {
    models.data = [
      {
        "id": "claude-sonnet-4",
        "object": "model",
        "created": Date.now(),
        "owned_by": "anthropic"
      },
      {
        "id": "claude-3.5-sonnet",
        "object": "model",
        "created": Date.now(),
        "owned_by": "anthropic"
      }
    ];
  } else if (config.MODEL_PROVIDER === 'gemini-cli') {
    models.data = [
      {
        "id": "gemini-2.5-pro",
        "object": "model",
        "created": Date.now(),
        "owned_by": "google"
      },
      {
        "id": "gemini-2.5-flash",
        "object": "model",
        "created": Date.now(),
        "owned_by": "google"
      }
    ];
  }

  res.json(models);
});

// 聊天完成端点
app.post('/v1/chat/completions', validateApiKey, async (req, res) => {
  try {
    logger.info(`收到聊天请求，模型: ${req.body.model || 'default'}`);
    
    // 根据配置的提供商处理请求
    if (config.MODEL_PROVIDER === 'kiro-api' || config.MODEL_PROVIDER === 'claude-kiro-oauth') {
      await handleKiroRequest(req, res);
    } else if (config.MODEL_PROVIDER === 'gemini-cli') {
      await handleGeminiRequest(req, res);
    } else {
      res.status(400).json({ error: 'Unsupported model provider' });
    }
  } catch (error) {
    logger.error(`处理聊天请求失败: ${error.message}`);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Kiro 请求处理器
async function handleKiroRequest(req, res) {
  try {
    // 加载 Kiro 提供商
    const KiroProvider = require('./providers/kiro-provider');
    const kiroProvider = new KiroProvider(config, logger);

    // 检查授权
    if (!kiroProvider.isAuthValid()) {
      return res.status(401).json({
        error: 'Kiro 授权无效或已过期，请检查 kiro-auth-token.json 文件'
      });
    }

    // 提取请求参数
    const messages = req.body.messages || [];
    const model = req.body.model || 'claude-sonnet-4';
    const stream = req.body.stream || false;
    const options = {
      max_tokens: req.body.max_tokens,
      temperature: req.body.temperature,
      stream: stream
    };

    // 记录请求
    if (config.PROMPT_LOG_MODE === 'console' || config.PROMPT_LOG_MODE === 'file') {
      logger.info(`Kiro 请求 - 模型: ${model}, 消息数: ${messages.length}`);
    }

    // 调用 Kiro API
    const response = await kiroProvider.callKiroAPI(messages, model, options);

    // 返回响应
    res.json(response);

  } catch (error) {
    logger.error(`Kiro 请求处理失败: ${error.message}`);
    res.status(500).json({
      error: 'Kiro API 调用失败',
      details: error.message
    });
  }
}

// Gemini 请求处理器
async function handleGeminiRequest(req, res) {
  // 这里需要实现 Gemini CLI 的调用逻辑
  // 暂时返回模拟响应
  const response = {
    "id": "chatcmpl-" + Date.now(),
    "object": "chat.completion", 
    "created": Math.floor(Date.now() / 1000),
    "model": req.body.model || "gemini-2.5-pro",
    "choices": [{
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "这是来自 Gemini CLI OAuth 的响应。请注意：这是一个模拟响应，实际的 Gemini 集成正在开发中。"
      },
      "finish_reason": "stop"
    }],
    "usage": {
      "prompt_tokens": 10,
      "completion_tokens": 20,
      "total_tokens": 30
    }
  };

  res.json(response);
}

// 启动服务器
const PORT = config.SERVER_PORT || 3000;
const HOST = config.HOST || 'localhost';

app.listen(PORT, HOST, () => {
  logger.info(`AIClient-2-API 服务器已启动`);
  logger.info(`地址: http://${HOST}:${PORT}`);
  logger.info(`模型提供商: ${config.MODEL_PROVIDER}`);
  logger.info(`健康检查: http://${HOST}:${PORT}/health`);
  logger.info(`模型列表: http://${HOST}:${PORT}/v1/models`);
});

// 优雅关闭
process.on('SIGINT', () => {
  logger.info('收到 SIGINT 信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  logger.info('收到 SIGTERM 信号，正在关闭服务器...');
  process.exit(0);
});
