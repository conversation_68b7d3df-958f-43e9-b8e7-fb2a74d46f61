<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>dp-当前页面ID &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="volume-系统音量" href="volume.html" />
    <link rel="prev" title="sys0-sys2默认变量" href="sys0-sys2.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../commands/index.html">基本指令集</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">系统变量</a><ul class="current">
<li class="toctree-l2 current"><a class="reference internal" href="index.html#id2">常用变量</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="sys0-sys2.html">sys0-sys2默认变量</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">dp-当前页面ID</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#dp-1">dp-示例1，跳转页面</a></li>
<li class="toctree-l4"><a class="reference internal" href="#dp-2">dp-示例2，发送当面页面编号到串口</a></li>
<li class="toctree-l4"><a class="reference internal" href="#dp-3">dp-示例3，获取当前页面编号</a></li>
<li class="toctree-l4"><a class="reference internal" href="#dp-4">dp-示例4:返回上一页</a></li>
<li class="toctree-l4"><a class="reference internal" href="#dp-5">dp-示例5:刷新当前页面</a></li>
<li class="toctree-l4"><a class="reference internal" href="#dp-c">dp-c语言示例</a></li>
<li class="toctree-l4"><a class="reference internal" href="#dp">dp-相关链接</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id1">dp-样例工程下载</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="volume.html">volume-系统音量</a></li>
<li class="toctree-l3"><a class="reference internal" href="dims.html">dims-上电默认背光亮度值</a></li>
<li class="toctree-l3"><a class="reference internal" href="dim.html">dim-当前背光亮度值</a></li>
<li class="toctree-l3"><a class="reference internal" href="bauds.html">bauds-上电默认波特率值</a></li>
<li class="toctree-l3"><a class="reference internal" href="baud.html">baud-当前波特率值</a></li>
<li class="toctree-l3"><a class="reference internal" href="ussp.html">ussp-无串口数据自动睡眠时间</a></li>
<li class="toctree-l3"><a class="reference internal" href="thsp.html">thsp-无触摸操作自动睡眠时间</a></li>
<li class="toctree-l3"><a class="reference internal" href="thup.html">thup-睡眠模式下触摸自动唤醒开关</a></li>
<li class="toctree-l3"><a class="reference internal" href="usup.html">usup-睡眠模式下串口数据自动唤醒开关</a></li>
<li class="toctree-l3"><a class="reference internal" href="wup.html">wup-睡眠唤醒后刷新页面设置</a></li>
<li class="toctree-l3"><a class="reference internal" href="sleep.html">sleep-睡眠</a></li>
<li class="toctree-l3"><a class="reference internal" href="lowpower.html">lowpower-睡眠模式设定</a></li>
<li class="toctree-l3"><a class="reference internal" href="bkcmd.html">bkcmd-串口指令执行状态数据返回</a></li>
<li class="toctree-l3"><a class="reference internal" href="delay.html">delay-延时</a></li>
<li class="toctree-l3"><a class="reference internal" href="rand.html">rand-随机数</a></li>
<li class="toctree-l3"><a class="reference internal" href="crcval.html">crcval-crc校验结果</a></li>
<li class="toctree-l3"><a class="reference internal" href="rtc0-rtc6.html">rtc0~rtc6-RTC时钟变量</a></li>
<li class="toctree-l3"><a class="reference internal" href="pio0-pio7.html">pio0~pio7-扩展IO端口</a></li>
<li class="toctree-l3"><a class="reference internal" href="pwm4-pwm7.html">pwm4~pwm7-扩展IO占空比</a></li>
<li class="toctree-l3"><a class="reference internal" href="pwmf.html">pwmf-PWM输出的频率</a></li>
<li class="toctree-l3"><a class="reference internal" href="audio0-audio1.html">audio0~audio1-音频通道控制</a></li>
<li class="toctree-l3"><a class="reference internal" href="hmi_color.html">串口HMI颜色代号表</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id3">不常用变量</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">系统变量</a> &raquo;</li>
      <li>dp-当前页面ID</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="dp-id">
<h1>dp-当前页面ID<a class="headerlink" href="#dp-id" title="此标题的永久链接"></a></h1>
<section id="dp-1">
<h2>dp-示例1，跳转页面<a class="headerlink" href="#dp-1" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//设置当前页面为1,等同于page 1</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">dp</span><span class="o">=</span><span class="mi">1</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/dp_1.jpg" src="../_images/dp_1.jpg" />
</section>
<section id="dp-2">
<h2>dp-示例2，发送当面页面编号到串口<a class="headerlink" href="#dp-2" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//发送当面页面ID到串口</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">prints</span><span class="w"> </span><span class="n">dp</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/dp_2.jpg" src="../_images/dp_2.jpg" />
</section>
<section id="dp-3">
<h2>dp-示例3，获取当前页面编号<a class="headerlink" href="#dp-3" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//当前页面ID赋值给n0.val</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="o">=</span><span class="n">dp</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/dp_3.jpg" src="../_images/dp_3.jpg" />
</section>
<section id="dp-4">
<h2>dp-示例4:返回上一页<a class="headerlink" href="#dp-4" title="此标题的永久链接"></a></h2>
<p>某些情况下,可能从多个不同的页面跳转到同一个页面,这时如果在”返回”按钮中直接跳转到某一个页面,就会造成逻辑混乱</p>
<p>此时可以使用以下方法解决</p>
<p>在program.s中新建一个lastpage变量</p>
<img alt="../_images/dp_4.jpg" src="../_images/dp_4.jpg" />
<p>在每一个页面的离开事件中添加以下代码，将dp的值赋值给 lastpage 变量</p>
<img alt="../_images/dp_5.jpg" src="../_images/dp_5.jpg" />
<p>添加一个“返回上一页”按钮，在按钮中跳转到 lastpage 所保存的值</p>
<img alt="../_images/dp_6.jpg" src="../_images/dp_6.jpg" />
</section>
<section id="dp-5">
<h2>dp-示例5:刷新当前页面<a class="headerlink" href="#dp-5" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//重新进入到当前页面</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">page</span><span class="w"> </span><span class="n">dp</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="dp-c">
<h2>dp-c语言示例<a class="headerlink" href="#dp-c" title="此标题的永久链接"></a></h2>
<p>单片机通过串口跳转到ID为1的页面</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//设置当前页面为1,等同于page 1</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;dp=1</span><span class="se">\xff\xff\xff</span><span class="s">&quot;</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
<p>单片机通过串口获取当前页面号</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//单片机通过串口获取当前页面号</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;prints dp,0</span><span class="se">\xff\xff\xff</span><span class="s">&quot;</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="dp">
<h2>dp-相关链接<a class="headerlink" href="#dp" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="../commands/page.html#page"><span class="std std-ref">page-页面跳转指令</span></a></p>
<p><a class="reference internal" href="../commands/sendme.html#sendme-id"><span class="std std-ref">sendme-发送当前页面ID号到串口</span></a></p>
</section>
<section id="id1">
<h2>dp-样例工程下载<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/系统变量/dp/dp-当前页面id.HMI">《dp-当前页面id》演示工程下载</a></p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="sys0-sys2.html" class="btn btn-neutral float-left" title="sys0-sys2默认变量" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="volume.html" class="btn btn-neutral float-right" title="volume-系统音量" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>