/**
  ******************************************************************************
  * @file    Project/STM32F10x_StdPeriph_Template/stm32f10x_conf.h 
  * <AUTHOR> Application Team
  * @version V3.5.0
  * @date    08-April-2011
  * @brief   Library configuration file.
  ******************************************************************************
  * @attention
  *
  * THE PRESENT FIRMWARE WHICH IS FOR GUIDANCE ONLY AIMS AT PROVIDING CUSTOMERS
  * WITH CODING INFORMATION REGARDING THEIR PRODUCTS IN ORDER FOR THEM TO SAVE
  * TIME. AS A RESULT, STMICROELECTRONICS SHALL NOT BE HELD LIABLE FOR ANY
  * DIRECT, INDIRECT OR CONSEQUENTIAL DAMAGES WITH RESPECT TO ANY CLAIMS ARISING
  * FROM THE CONTENT OF SUCH FIRMWARE AND/OR THE USE MADE BY CUSTOMERS OF THE
  * CODING INFORMATION CONTAINED HEREIN IN CONNECTION WITH THEIR PRODUCTS.
  *
  * <h2><center>&copy; COPYRIGHT 2011 STMicroelectronics</center></h2>
  ******************************************************************************
  */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __STM32F10x_CONF_H
#define __STM32F10x_CONF_H

/* Includes ------------------------------------------------------------------*/
/* Uncomment/Comment the line below to enable/disable peripheral header file inclusion */
/* 激光云台项目只包含必要的库文件 */
//#include "stm32f10x_adc.h"     // 删除 - 不需要ADC
//#include "stm32f10x_bkp.h"     // 删除 - 不需要备份寄存器
//#include "stm32f10x_can.h"     // 删除 - 不需要CAN
//#include "stm32f10x_cec.h"     // 删除 - 不需要CEC
//#include "stm32f10x_crc.h"     // 删除 - 不需要CRC
//#include "stm32f10x_dac.h"     // 删除 - 不需要DAC
//#include "stm32f10x_dbgmcu.h"  // 删除 - 不需要调试MCU
//#include "stm32f10x_dma.h"     // 删除 - 暂不需要DMA
#include "stm32f10x_exti.h"      // 保留 - 按键中断需要
//#include "stm32f10x_flash.h"   // 删除 - 不需要Flash操作
//#include "stm32f10x_fsmc.h"    // 删除 - 不需要FSMC
#include "stm32f10x_gpio.h"      // 保留 - GPIO控制必需
#include "stm32f10x_i2c.h"       // 保留 - OLED显示需要
//#include "stm32f10x_iwdg.h"    // 删除 - 不需要看门狗
//#include "stm32f10x_pwr.h"     // 删除 - 不需要电源管理
#include "stm32f10x_rcc.h"       // 保留 - 时钟配置必需
//#include "stm32f10x_rtc.h"     // 删除 - 不需要RTC
//#include "stm32f10x_sdio.h"    // 删除 - 不需要SDIO
//#include "stm32f10x_spi.h"     // 删除 - 不需要SPI
#include "stm32f10x_tim.h"       // 保留 - 定时器控制需要
#include "stm32f10x_usart.h"     // 保留 - 舵机通信必需
//#include "stm32f10x_wwdg.h"    // 删除 - 不需要窗口看门狗
#include "misc.h" /* High level functions for NVIC and SysTick (add-on to CMSIS functions) */

/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
/* Uncomment the line below to expanse the "assert_param" macro in the 
   Standard Peripheral Library drivers code */
/* #define USE_FULL_ASSERT    1 */

/* Exported macro ------------------------------------------------------------*/
#ifdef  USE_FULL_ASSERT

/**
  * @brief  The assert_param macro is used for function's parameters check.
  * @param  expr: If expr is false, it calls assert_failed function which reports 
  *         the name of the source file and the source line number of the call 
  *         that failed. If expr is true, it returns no value.
  * @retval None
  */
  #define assert_param(expr) ((expr) ? (void)0 : assert_failed((uint8_t *)__FILE__, __LINE__))
/* Exported functions ------------------------------------------------------- */
  void assert_failed(uint8_t* file, uint32_t line);
#else
  #define assert_param(expr) ((void)0)
#endif /* USE_FULL_ASSERT */

#endif /* __STM32F10x_CONF_H */

/******************* (C) COPYRIGHT 2011 STMicroelectronics *****END OF FILE****/
