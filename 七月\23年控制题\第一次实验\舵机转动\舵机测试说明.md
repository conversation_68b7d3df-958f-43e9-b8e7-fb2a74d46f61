# 舵机测试程序使用说明

## 程序功能
这是一个简化的舵机测试程序，用于验证STM32F103C8T6单片机能否正常控制总线舵机。

## 硬件连接
- **按键连接**：
  - PB0：控制舵机1（水平舵机，ID=1）
  - PB1：控制舵机2（垂直舵机，ID=2）
  
- **舵机连接**：
  - 舵机1（ID=1）：水平Pan舵机
  - 舵机2（ID=2）：垂直Tilt舵机
  - 通过USART1与STM32通信

- **显示**：
  - OLED显示屏显示当前状态和角度信息
  - LED指示按键操作

## 操作说明

### 初始化
1. 上电后，程序自动初始化所有模块
2. OLED显示"Servo Test"启动信息
3. 两个舵机自动移动到中位（120度）
4. LED熄灭，系统准备就绪

### 按键操作
- **PB0按键**：
  - 每按一次，舵机1顺时针旋转30度
  - 角度范围：0-240度
  - 超过240度时自动回到0度
  - OLED第1行显示：S1:xxx（当前角度）

- **PB1按键**：
  - 每按一次，舵机2逆时针旋转30度
  - 角度范围：0-240度  
  - 小于0度时自动回到240度
  - OLED第2行显示：S2:xxx（当前角度）

### 显示信息
- **OLED第1行**：S1:xxx - 舵机1当前角度
- **OLED第2行**：S2:xxx - 舵机2当前角度  
- **OLED第3行**：PB0:S1+30 - 操作提示
- **OLED第4行**：PB1:S2-30 - 操作提示

### LED指示
- 每次按键操作时，LED会短暂点亮100ms作为反馈

## 测试目的
1. 验证STM32能否正常控制总线舵机
2. 测试按键响应是否正常
3. 检查舵机运动是否平稳
4. 确认通信协议是否正确

## 预期效果
- 按下PB0，舵机1应该顺时针旋转30度
- 按下PB1，舵机2应该逆时针旋转30度
- OLED实时显示当前角度
- LED提供按键反馈

## 故障排除
1. **舵机不动**：检查电源和通信线连接
2. **按键无响应**：检查按键连接和消抖设置
3. **OLED无显示**：检查I2C连接
4. **角度显示异常**：检查舵机反馈数据

## 代码特点
- 简化的主循环，易于理解和调试
- 完整的按键消抖处理
- 角度限制和循环处理
- 实时状态显示
- 模块化设计，便于扩展

## 编译信息
- 编译器：Keil MDK-ARM V5.06
- 目标芯片：STM32F103C8T6
- 代码大小：约25KB
- 编译状态：✅ 无错误，无警告
