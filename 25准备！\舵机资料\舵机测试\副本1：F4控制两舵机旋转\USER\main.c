#include "sys.h"
#include "delay.h"
#include "usart.h"
#include "led.h"


//ALIENTEK 探索者STM32F407开发板 实验4
//舵机连续旋转测试
//技术支持：www.openedv.com
//淘宝店铺：http://eboard.taobao.com
//广州市星翼电子科技有限公司  
//作者：正点原子 @ALIENTEK


int main(void)
{ 
	u8 servo1_pos = 0;  // 舵机1位置状态: 0=最小位置, 1=最大位置
	u8 servo2_pos = 0;  // 舵机2位置状态: 0=最小位置, 1=最大位置
	
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);//设置系统中断优先级分组2
	delay_init(168);		//延时初始化 
	uart_init(115200);	//串口初始化波特率为115200
	LED_Init();		  		//初始化与LED连接的硬件接口  
	
	delay_ms(1000);			//等待系统稳定
	
	// 先装载舵机电机(上电状态)
	Servo_LoadMotor(1);		//装载ID为1的舵机
	delay_ms(100);			//等待命令执行
	
	Servo_LoadMotor(2);		//装载ID为2的舵机  
	delay_ms(100);			//等待命令执行
	
	// 初始位置设置
	Servo_MoveToPosition(1, 100, 1000);  // ID=1舵机移动到位置100，用时1秒
	delay_ms(100);
	Servo_MoveToPosition(2, 100, 1000);  // ID=2舵机移动到位置100，用时1秒
	delay_ms(1000);			//等待移动完成
	
	while(1)
	{
		LED0=!LED0;		//LED闪烁，指示系统运行
		
		// 舵机1来回摆动 (水平轴)
		if(servo1_pos == 0) {
			Servo_MoveToPosition(1, 900, 2000);  // 移动到最大位置900，用时2秒
			servo1_pos = 1;
		} else {
			Servo_MoveToPosition(1, 100, 2000);  // 移动到最小位置100，用时2秒  
			servo1_pos = 0;
		}
		
		delay_ms(200);  // 命令间隔
		
		// 舵机2来回摆动 (垂直轴)
		if(servo2_pos == 0) {
			Servo_MoveToPosition(2, 900, 2000);  // 移动到最大位置900，用时2秒
			servo2_pos = 1;
		} else {
			Servo_MoveToPosition(2, 100, 2000);  // 移动到最小位置100，用时2秒
			servo2_pos = 0;
		}
		
		delay_ms(2200);  // 等待舵机运动完成(2秒运动时间+200ms余量)
	}
}
