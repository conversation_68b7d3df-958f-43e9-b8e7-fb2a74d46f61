# JDY-31蓝牙模块集成指导

## 📋 硬件连接

### JDY-31蓝牙模块连接图
```
JDY-31蓝牙模块 (6引脚版)    →    STM32F103C8T6
VCC (5V)                   →    5V (外部电源)
GND                        →    GND
TXD                        →    PA3 (USART2_RX)
RXD                        →    PA2 (USART2_TX)
STATE (可选)               →    未连接
EN (可选)                  →    未连接
```

### ⚠️ 重要注意事项
1. **电源要求**：JDY-31需要5V供电，电流约30-50mA
2. **交叉连接**：蓝牙TXD接STM32的RX，蓝牙RXD接STM32的TX
3. **串口分配**：
   - USART1 (PA9/PA10) → 总线舵机通信 (115200bps)
   - USART2 (PA2/PA3) → 蓝牙模块通信 (9600bps)

## 🔧 软件配置

### 蓝牙模块默认参数
```
设备名称：LaserGimbal_v1
PIN码：1234
波特率：9600
工作模式：从机模式
```

### 系统功能
1. **自动状态报告**：每5秒发送系统状态
2. **实时错误报告**：检测到错误立即发送
3. **心跳包**：每10秒发送一次心跳
4. **命令响应**：支持简单的调试命令

## 📱 电脑端连接步骤

### 1. 蓝牙配对
1. 打开电脑蓝牙设置
2. 搜索设备，找到"LaserGimbal_v1"
3. 配对时输入PIN码：1234

### 2. 串口连接
1. 配对成功后，系统会创建虚拟串口
2. 查看设备管理器，找到"标准串行通过蓝牙链接"
3. 记录COM端口号（如COM5）

### 3. 串口调试工具
推荐使用以下工具：
- **串口调试助手**
- **PuTTY**
- **Tera Term**
- **Arduino IDE串口监视器**

配置参数：
```
波特率：9600
数据位：8
停止位：1
奇偶校验：无
流控制：无
```

## 📊 数据报告格式

### 系统状态报告 (每5秒)
```
=== SYSTEM STATUS REPORT ===
Timestamp: 12345 ms
System State: STATE_AUTO_MOVING
Point A Recorded: YES
Point B Recorded: YES
Is Moving: YES
Pan Angle: 120.50°
Tilt Angle: 90.25°
System Efficiency: 95.2%
Runtime: 12345 ms
Diagnostic Level: OK
============================
```

### 错误报告 (实时)
```
!!! ERROR REPORT !!!
Timestamp: 12345 ms
Module ID: 1
Error Code: 2
Message: Auto movement execution error
Severity: WARNING
!!!!!!!!!!!!!!!!!!!
```

### 心跳包 (每10秒)
```
[HEARTBEAT] Runtime: 12345 ms, Status: OK
```

## 🎮 支持的调试命令

发送以下命令到蓝牙串口：

### 基本命令
- `STATUS` - 请求当前系统状态
- `HELP` - 显示帮助信息
- `INFO` - 显示蓝牙模块信息

### 命令示例
```
发送: STATUS
回复: [CMD] Status request received

发送: HELP
回复: [HELP] Available commands: STATUS, HELP, INFO

发送: INFO
回复: [INFO] Module: JDY-31, Name: LaserGimbal_v1, PIN: 1234, Baud: 9600
      [INFO] Packets - Sent: 123, Received: 45, Errors: 0
```

## 🔍 故障排除

### 连接问题
1. **无法发现设备**
   - 检查蓝牙模块供电
   - 确认STM32程序正常运行
   - 重启蓝牙模块

2. **配对失败**
   - 确认PIN码正确 (1234)
   - 删除已配对设备重新配对
   - 检查电脑蓝牙兼容性

3. **数据接收异常**
   - 检查串口参数设置
   - 确认COM端口号正确
   - 重新连接蓝牙

### 数据传输问题
1. **无数据接收**
   - 检查USART2初始化
   - 确认中断配置正确
   - 检查蓝牙模块状态

2. **数据乱码**
   - 确认波特率匹配 (9600)
   - 检查数据位、停止位设置
   - 排除电磁干扰

## 📈 性能监控

### 蓝牙通信统计
- 发送包计数
- 接收包计数
- 错误计数
- 连接状态

### 系统资源占用
- USART2中断频率
- 内存使用情况
- CPU占用率影响

## 🚀 扩展功能建议

### 1. 远程控制
- 通过蓝牙发送控制命令
- 远程参数调整
- 紧急停止功能

### 2. 数据记录
- 运动轨迹记录
- 性能数据日志
- 错误历史统计

### 3. 移动端APP
- Android/iOS应用开发
- 图形化界面显示
- 实时数据可视化

## 📝 开发注意事项

1. **中断优先级**：USART2中断优先级设为3，低于系统关键中断
2. **缓冲区管理**：合理设置接收和发送缓冲区大小
3. **错误处理**：完善的超时和重试机制
4. **内存优化**：避免频繁的动态内存分配

---
**版本**：v1.1  
**更新时间**：2025年7月18日  
**适用系统**：激光云台控制系统 + JDY-31蓝牙模块
