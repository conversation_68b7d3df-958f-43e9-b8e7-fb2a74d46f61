/**
 ******************************************************************************
 * @file    KEY.h
 * <AUTHOR> Name]
 * @version V1.1
 * @date    2025-08-02
 * @brief   按键输入检测和LED指示驱动头文件
 *          
 *          本文件定义了按键输入检测的初始化和扫描接口
 *          支持单个按键的状态检测和去抖处理
 *          增加LED0指示功能
 * 
 * @note    硬件连接:
 *          PE4 -> K0按键输入 (内部上拉，按下时为低电平)
 *          PF9 -> LED0输出 (低电平点亮)
 *          
 *          按键特性:
 *          - 内部上拉电阻使能
 *          - 按下时引脚为低电平(0)
 *          - 释放时引脚为高电平(1)
 *          - 无硬件去抖，依靠软件处理
 *          
 *          LED特性:
 *          - 低电平点亮，高电平熄灭
 *          - 作为按键状态指示
 ******************************************************************************
 */

#ifndef __KEY_H
#define __KEY_H

#include "sys.h"

/* 函数声明 ------------------------------------------------------------------*/

/**
 * @brief  按键和LED初始化
 * @param  None
 * @retval None
 * @note   配置PE4为输入模式，使能内部上拉电阻
 *         配置PF9为输出模式，用于LED0控制
 *         按键连接到地，按下时引脚读取为低电平
 *         LED0低电平点亮，高电平熄灭
 */
void Key_Init(void);

/**
 * @brief  LED0控制
 * @param  state: 1=点亮, 0=熄灭
 * @retval None
 * @note   控制PF9引脚输出，低电平点亮LED，高电平熄灭LED
 */
void LED0_Set(u8 state);

/**
 * @brief  LED0切换状态
 * @param  None
 * @retval None
 * @note   切换LED0当前状态，点亮变熄灭，熄灭变点亮
 */
void LED0_Toggle(void);

/**
 * @brief  按键状态扫描
 * @param  None
 * @retval 按键状态: 1=按下, 0=释放
 * @note   实时读取按键状态，无去抖处理
 *         返回值: 1表示按键被按下，0表示按键未按下
 *         如需去抖，调用方应添加延时或状态确认
 */
u8 Key_Scan(void);

/**
 * @brief  按键状态扫描(带防抖)
 * @param  None
 * @retval 按键事件: 1=按下事件, 0=无事件
 * @note   带防抖处理的按键检测，只在按下瞬间返回1
 *         适合用于状态切换和事件触发
 */
u8 Key_Scan_Debounce(void);

#endif
