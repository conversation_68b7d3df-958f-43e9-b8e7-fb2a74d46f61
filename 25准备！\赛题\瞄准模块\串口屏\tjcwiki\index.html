<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>淘晶驰资料中心 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
        <script src="_static/jquery.js"></script>
        <script src="_static/underscore.js"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="_static/doctools.js"></script>
        <script src="_static/translations.js"></script>
        <script src="_static/js\rtd_sphinx_search.min.js"></script>
        <script src="_static/custom.js"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="索引" href="genindex.html" />
    <link rel="search" title="搜索" href="search.html" />
    <link rel="next" title="快速入门" href="start/index.html" />
    <link href="_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="#" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="#">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="#" class="icon icon-home"></a> &raquo;</li>
      <li>淘晶驰资料中心</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>淘晶驰资料中心<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>左侧搜索框可以搜索资料，如果搜索结果为空建议尝试清除浏览器cookie后重新搜索。</p>
</div>
<div class="admonition note">
<p class="admonition-title">备注</p>
<p>本文档适用于淘晶驰所有的串口屏系列，包括X5，X3，X2，K0，T1，T0系列。</p>
<p><a class="reference internal" href="download/development_doc.html#id1"><span class="std std-ref">开发文档下载</span></a></p>
<p><a class="reference internal" href="download/usart_hmi.html#id1"><span class="std std-ref">上位机下载</span></a></p>
<p>本次资料中心更新时间： 2025-07-24 10:19:55   ， 版本号：1.1.0-2025-07-24 10:19:55</p>
</div>
<table class="docutils align-default">
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p><a class="reference external" href="start/create_project/create_project_1.html"><img alt="img1" src="_images/1.png" /></a></p></td>
<td><p><a class="reference external" href="commands/index.html"><img alt="img2" src="_images/2.png" /></a></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference external" href="variables/index.html"><img alt="img3" src="_images/33.png" /></a></p></td>
<td><p><a class="reference external" href="return/index.html"><img alt="img4" src="_images/4.png" /></a></p></td>
</tr>
</tbody>
</table>
<p><strong>常用链接</strong></p>
<p><a class="reference internal" href="QA/baudrate.html#id1"><span class="std std-ref">如何配置亮度,主动解析,波特率</span></a></p>
<p><a class="reference internal" href="start/create_project/create_project_5.html#id1"><span class="std std-ref">创建字库和导入字库</span></a></p>
<p><a class="reference internal" href="start/create_project/create_project_8.html#id1"><span class="std std-ref">安装串口驱动</span></a></p>
<p><a class="reference internal" href="start/create_project/create_project_9.html#id1"><span class="std std-ref">通过串口下载工程到串口屏</span></a>  / <a class="reference internal" href="start/create_project/create_project_10.html#sd"><span class="std std-ref">通过SD卡下载工程到串口屏</span></a></p>
<p><a class="reference internal" href="QA/QA57.html#id1"><span class="std std-ref">使用串口下载工程速度慢怎么办</span></a></p>
<p><a class="reference internal" href="start/create_project/create_project_12.html#id1"><span class="std std-ref">校准RTC时钟</span></a> / <a class="reference internal" href="start/create_project/create_project_12.html#id2"><span class="std std-ref">RTC电池型号</span></a></p>
<p><a class="reference internal" href="QA/QA72.html#id1"><span class="std std-ref">如何修改设备型号</span></a></p>
<p><a class="reference internal" href="product/new_datasheet/index.html#id1"><span class="std std-ref">新版规格书和出厂工程下载</span></a></p>
<p><a class="reference internal" href="product/NamingRules.html#id1"><span class="std std-ref">产品命名规则</span></a></p>
<hr class="docutils" />
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="start/index.html" class="btn btn-neutral float-right" title="快速入门" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>