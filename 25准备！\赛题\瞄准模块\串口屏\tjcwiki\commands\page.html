<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>page-页面跳转指令 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="prints-从串口打印一个变量/常量" href="prints.html" />
    <link rel="prev" title="指令集" href="index.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../widgets/index.html">控件详解</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">基本指令集</a><ul class="current">
<li class="toctree-l2 current"><a class="reference internal" href="index.html#id2">常用指令集</a><ul class="current">
<li class="toctree-l3 current"><a class="current reference internal" href="#">page-页面跳转指令</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#page-1">page-示例1</a></li>
<li class="toctree-l4"><a class="reference internal" href="#page-2">page-示例2 返回上一个页面</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id1">page-应用实例</a></li>
<li class="toctree-l4"><a class="reference internal" href="#page-c">page-c语言示例</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id2">page指令-相关链接</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id3">page指令-样例工程下载</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="prints.html">prints-从串口打印一个变量/常量</a></li>
<li class="toctree-l3"><a class="reference internal" href="printh.html">printh-从串口打印16进制</a></li>
<li class="toctree-l3"><a class="reference internal" href="click.html">click-激活控件的按下/弹起事件</a></li>
<li class="toctree-l3"><a class="reference internal" href="vis.html">vis-隐藏/显示控件</a></li>
<li class="toctree-l3"><a class="reference internal" href="tsw.html">tsw-控件触摸使能</a></li>
<li class="toctree-l3"><a class="reference internal" href="randset.html">randset-随机数范围设置</a></li>
<li class="toctree-l3"><a class="reference internal" href="covx.html">covx-变量类型转换</a></li>
<li class="toctree-l3"><a class="reference internal" href="strlen.html">strlen-字符串变量字符长度测试</a></li>
<li class="toctree-l3"><a class="reference internal" href="btlen.html">btlen-字符串变量字节长度测试</a></li>
<li class="toctree-l3"><a class="reference internal" href="substr.html">substr-字符串截取</a></li>
<li class="toctree-l3"><a class="reference internal" href="spstr.html">spstr-字符串分割</a></li>
<li class="toctree-l3"><a class="reference internal" href="touch_j.html">touch_j-触摸校准</a></li>
<li class="toctree-l3"><a class="reference internal" href="add.html">add-往曲线控件添加数据</a></li>
<li class="toctree-l3"><a class="reference internal" href="addt.html">addt-曲线数据透传指令</a></li>
<li class="toctree-l3"><a class="reference internal" href="cle.html">cle-清除曲线控件中的数据</a></li>
<li class="toctree-l3"><a class="reference internal" href="rest.html">rest-复位串口屏</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id3">高级指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id4">文件操作指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id5">不常用指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#crc">CRC校验指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#gui">GUI绘图指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#hmi">HMI颜色代号表</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">指令集</a> &raquo;</li>
      <li>page-页面跳转指令</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="page">
<h1>page-页面跳转指令<a class="headerlink" href="#page" title="此标题的永久链接"></a></h1>
<img alt="../_images/1_11.png" src="../_images/1_11.png" />
<p>如上图所示，红色框内为页面ID，绿色框内为页面名称</p>
<div class="admonition tip">
<p class="admonition-title">小技巧</p>
<p>以上图为例，main页面的ID为2，因此如果要跳转到main页面，可以使用page 页面名称或者page 页面ID</p>
<ul class="simple">
<li><p>page main</p></li>
<li><p>page 2</p></li>
<li><p>也可以对系统变量dp赋值来实现跳转页面(如dp=2)，系统变量dp可以设置可以读取，具体请参看系统变量列表。</p></li>
</ul>
</div>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>page指令后面的代码将不会被执行，所以千万不要把代码写在page指令后面</p>
</div>
<div class="admonition caution">
<p class="admonition-title">小心</p>
<p>page指令后面的代码将不会被执行，所以千万不要把代码写在page指令后面</p>
</div>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>page指令后面的代码将不会被执行，所以千万不要把代码写在page指令后面</p>
</div>
<div class="admonition important">
<p class="admonition-title">重要</p>
<p>page指令后面的代码将不会被执行，所以千万不要把代码写在page指令后面</p>
</div>
<div class="admonition note">
<p class="admonition-title">备注</p>
<p>page指令后面的代码将不会被执行，所以千万不要把代码写在page指令后面</p>
</div>
<div class="admonition note">
<p class="admonition-title">备注</p>
<p>page指令后面的代码将不会被执行，所以千万不要把代码写在page指令后面</p>
</div>
<div class="admonition tip">
<p class="admonition-title">小技巧</p>
<p>page指令后面的代码将不会被执行，所以千万不要把代码写在page指令后面</p>
</div>
<div class="admonition warning">
<p class="admonition-title">警告</p>
<p>page指令后面的代码将不会被执行，所以千万不要把代码写在page指令后面</p>
</div>
<section id="page-1">
<h2>page-示例1<a class="headerlink" href="#page-1" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//跳转到main页面</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">page</span><span class="w"> </span><span class="n">main</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/page_1.jpg" src="../_images/page_1.jpg" />
</section>
<section id="page-2">
<h2>page-示例2 返回上一个页面<a class="headerlink" href="#page-2" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="../variables/dp.html#dp-4"><span class="std std-ref">dp-示例4:返回上一页</span></a></p>
</section>
<section id="id1">
<h2>page-应用实例<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="../widgets/Button.html#id4"><span class="std std-ref">通过按钮控件跳转页面</span></a></p>
</section>
<section id="page-c">
<h2>page-c语言示例<a class="headerlink" href="#page-c" title="此标题的永久链接"></a></h2>
<p>单片机通过串口让串口屏跳转页面</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//跳转到页面ID为1的页面</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;page 1</span><span class="se">\xff\xff\xff</span><span class="s">&quot;</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//跳转到页面名称为main的页面,推荐使用page + 页面名称 的跳转方法</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;page main</span><span class="se">\xff\xff\xff</span><span class="s">&quot;</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id2">
<h2>page指令-相关链接<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="../QA/QA81.html#id1"><span class="std std-ref">页面和页面控件的区别</span></a></p>
</section>
<section id="id3">
<h2>page指令-样例工程下载<a class="headerlink" href="#id3" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/基本指令集/page指令/page指令.HMI">《page指令》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/基本指令集/page指令/page指令-返回上一个页面.HMI">《page指令-返回上一个页面》演示工程下载</a></p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="指令集" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="prints.html" class="btn btn-neutral float-right" title="prints-从串口打印一个变量/常量" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>