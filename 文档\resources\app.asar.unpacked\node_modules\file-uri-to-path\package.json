{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "description": "Convert a file: URI to a file path", "devDependencies": {"mocha": "3"}, "directories": {"test": "test"}, "homepage": "https://github.com/TooTallNate/file-uri-to-path", "license": "MIT", "main": "index.js", "name": "file-uri-to-path", "repository": {"type": "git", "url": "git://github.com/TooTallNate/file-uri-to-path.git"}, "types": "index.d.ts", "version": "1.0.0"}