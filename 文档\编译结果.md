Rebuild started: Project: Template

\*\*\* Using Compiler 'V5.06 update 7 (build 960)', folder: 'E:\\Keil\_v5\\ARM\\ARMCC\\Bin'

Rebuild target 'Template'

assembling startup\_stm32f40\_41xxx.s...

compiling main.c...

..\\SYSTEM\\usart\\usart.h(105): error:  #20: identifier "inline" is undefined

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(105): error:  #101: "u8" has already been declared in the current scope

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(105): error:  #65: expected a ";"

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(146): warning:  #1-D: last line of file ends without a newline

&nbsp; #endif

..\\HAREWARE\\ATD5984\\ATD5984.h(65): warning:  #12-D: parsing restarts here after previous syntax error

&nbsp; void ATD5984\_Init(void);

main.c(29): error:  #5: cannot open source input file "k230\_comm.h": No such file or directory

&nbsp; #include "k230\_comm.h"

main.c: 2 warnings, 4 errors

compiling stm32f4xx\_cryp.c...

compiling stm32f4xx\_cryp\_tdes.c...

compiling stm32f4xx\_dcmi.c...

compiling misc.c...

compiling stm32f4xx\_it.c...

..\\SYSTEM\\usart\\usart.h(105): error:  #20: identifier "inline" is undefined

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(105): error:  #101: "u8" has already been declared in the current scope

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(105): error:  #65: expected a ";"

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(146): warning:  #1-D: last line of file ends without a newline

&nbsp; #endif

..\\HAREWARE\\ATD5984\\ATD5984.h(65): warning:  #12-D: parsing restarts here after previous syntax error

&nbsp; void ATD5984\_Init(void);

stm32f4xx\_it.c: 2 warnings, 3 errors

compiling stm32f4xx\_dbgmcu.c...

compiling stm32f4xx\_cryp\_des.c...

compiling system\_stm32f4xx.c...

compiling stm32f4xx\_crc.c...

compiling stm32f4xx\_dac.c...

compiling stm32f4xx\_adc.c...

compiling stm32f4xx\_can.c...

compiling stm32f4xx\_cryp\_aes.c...

compiling stm32f4xx\_dma2d.c...

compiling stm32f4xx\_dma.c...

compiling stm32f4xx\_exti.c...

compiling stm32f4xx\_flash\_ramfunc.c...

compiling stm32f4xx\_flash.c...

compiling stm32f4xx\_fsmc.c...

compiling stm32f4xx\_gpio.c...

compiling stm32f4xx\_hash.c...

compiling stm32f4xx\_hash\_md5.c...

compiling stm32f4xx\_hash\_sha1.c...

compiling stm32f4xx\_iwdg.c...

compiling stm32f4xx\_i2c.c...

compiling stm32f4xx\_pwr.c...

compiling stm32f4xx\_ltdc.c...

compiling stm32f4xx\_rng.c...

compiling stm32f4xx\_rcc.c...

compiling stm32f4xx\_sai.c...

compiling stm32f4xx\_rtc.c...

compiling stm32f4xx\_sdio.c...

compiling stm32f4xx\_syscfg.c...

compiling stm32f4xx\_spi.c...

compiling stm32f4xx\_usart.c...

compiling stm32f4xx\_wwdg.c...

compiling stm32f4xx\_tim.c...

compiling delay.c...

..\\SYSTEM\\usart\\usart.h(105): error:  #20: identifier "inline" is undefined

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(105): error:  #101: "u8" has already been declared in the current scope

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(105): error:  #65: expected a ";"

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(146): warning:  #1-D: last line of file ends without a newline

&nbsp; #endif

..\\HAREWARE\\ATD5984\\ATD5984.h(65): warning:  #12-D: parsing restarts here after previous syntax error

&nbsp; void ATD5984\_Init(void);

..\\SYSTEM\\delay\\delay.h(48): error:  #757: variable "u8"  is not a type name

&nbsp; void delay\_init(u8 SYSCLK);

..\\SYSTEM\\delay\\delay.c(23): error:  #757: variable "u8"  is not a type name

&nbsp; static u8  fac\_us = 0;  // 寰寤舵椂鍊嶄箻鏁?			   

..\\SYSTEM\\delay\\delay.c(103): error:  #757: variable "u8"  is not a type name

&nbsp; void delay\_init(u8 SYSCLK)

..\\SYSTEM\\delay\\delay.c(235): error:  #65: expected a ";"

&nbsp; 	u8 repeat = nms / 540;                        // 杩欓噷鐢?540锛屾槸鑰冭檻鍒版煇浜涘鎴峰彲鑳借秴棰戜娇鐢?

..\\SYSTEM\\delay\\delay.c(236): error:  #268: declaration may not appear after executable statement in block

&nbsp; 	u16 remain = nms % 540;

..\\SYSTEM\\delay\\delay.c(238): error:  #20: identifier "repeat" is undefined

&nbsp; 	while(repeat)

..\\SYSTEM\\delay\\delay.c: 2 warnings, 9 errors

compiling sys.c...

..\\SYSTEM\\usart\\usart.h(105): error:  #20: identifier "inline" is undefined

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(105): error:  #101: "u8" has already been declared in the current scope

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(105): error:  #65: expected a ";"

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(146): warning:  #1-D: last line of file ends without a newline

&nbsp; #endif

..\\HAREWARE\\ATD5984\\ATD5984.h(65): warning:  #12-D: parsing restarts here after previous syntax error

&nbsp; void ATD5984\_Init(void);

..\\SYSTEM\\sys\\sys.c: 2 warnings, 3 errors

compiling usart.c...

..\\SYSTEM\\usart\\usart.h(105): error:  #20: identifier "inline" is undefined

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(105): error:  #101: "u8" has already been declared in the current scope

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(105): error:  #65: expected a ";"

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(146): warning:  #1-D: last line of file ends without a newline

&nbsp; #endif

..\\HAREWARE\\ATD5984\\ATD5984.h(65): warning:  #12-D: parsing restarts here after previous syntax error

&nbsp; void ATD5984\_Init(void);

..\\SYSTEM\\usart\\usart.c(38): error:  #757: variable "u8"  is not a type name

&nbsp; volatile u8 k230\_rx\_state = 0;      // 鎺ユ敹鐘舵€?: 0=绛夊緟甯уご, 1=鎺ユ敹鏁版嵁, 2=鏁版嵁瀹屾垚

KB: Unexpected type: 0

error type>(39): error:  #757: variable "u8"  is not a type name

&nbsp; volatile u8 k230\_rx\_buffer\[6];       // 6瀛楄妭鏁版嵁鍖呯紦鍐插尯

..\\SYSTEM\\usart\\usart.c(40): error:  #757: variable "u8"  is not a type name

&nbsp; volatile u8 k230\_rx\_index = 0;       // 褰撳墠鎺ユ敹绱㈠紩

KB: Unexpected type: 0

error type>(41): error:  #757: variable "u8"  is not a type name

&nbsp; volatile u8 k230\_frame\_ready = 0;    // 鏁版嵁鍖呭畬鎴愭爣蹇?

KB: Unexpected type: 0

error type>(46): error:  #757: variable "u8"  is not a type name

&nbsp; u8 USART\_RX\_BUF\[USART\_REC\_LEN];     //鎺ユ敹缂撳啿,鏈€澶SART\_REC\_LEN涓瓧鑺?.

..\\SYSTEM\\usart\\usart.c(97): error:  #757: variable "u8"  is not a type name

&nbsp; void K230\_ParseByte(u8 data) {

..\\SYSTEM\\usart\\usart.c(115): error:  #65: expected a ";"

&nbsp;                     u8 checksum = 0xAA ^ k230\_rx\_buffer\[1] ^ k230\_rx\_buffer\[2] ^ k230\_rx\_buffer\[3] ^ 0xFF;

..\\SYSTEM\\usart\\usart.c(116): error:  #20: identifier "checksum" is undefined

&nbsp;                     if(checksum == k230\_rx\_buffer\[4]) {

..\\SYSTEM\\usart\\usart.c(129): error:  #65: expected a ";"

&nbsp; 	u8 Res;

..\\SYSTEM\\usart\\usart.c(135): error:  #20: identifier "Res" is undefined

&nbsp; 		Res = USART\_ReceiveData(USART1);//(USART1->DR);	//璇诲彇鎺ユ敹鍒扮殑鏁版嵁

..\\SYSTEM\\usart\\usart.c: 2 warnings, 13 errors

compiling ATD5984.c...

..\\SYSTEM\\usart\\usart.h(105): error:  #20: identifier "inline" is undefined

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(105): error:  #101: "u8" has already been declared in the current scope

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(105): error:  #65: expected a ";"

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(146): warning:  #1-D: last line of file ends without a newline

&nbsp; #endif

..\\SYSTEM\\sys\\../../HAREWARE/MOTOR\_CONTROL/motor\_control.h(31): warning:  #12-D: parsing restarts here after previous syntax error

&nbsp; typedef uint16\_t u16;

..\\HAREWARE\\ATD5984\\ATD5984.c(140): error:  #65: expected a ";"

&nbsp; 	u8 direction;

..\\HAREWARE\\ATD5984\\ATD5984.c(150): error:  #20: identifier "direction" is undefined

&nbsp; 		direction = DIR\_CW;  // 椤烘椂閽?

..\\HAREWARE\\ATD5984\\ATD5984.c(154): error:  #20: identifier "direction" is undefined

&nbsp; 		direction = DIR\_CCW;  // 閫嗘椂閽?

..\\HAREWARE\\ATD5984\\ATD5984.c(160): error:  #20: identifier "direction" is undefined

&nbsp; 	if (direction == DIR\_CW) {

..\\HAREWARE\\ATD5984\\ATD5984.c(505): error:  #65: expected a ";"

&nbsp; 	u8 direction;

..\\HAREWARE\\ATD5984\\ATD5984.c(515): error:  #20: identifier "direction" is undefined

&nbsp; 		direction = DIR\_CW;  // 椤烘椂閽?

..\\HAREWARE\\ATD5984\\ATD5984.c(519): error:  #20: identifier "direction" is undefined

&nbsp; 		direction = DIR\_CCW;  // 閫嗘椂閽?

..\\HAREWARE\\ATD5984\\ATD5984.c(525): error:  #20: identifier "direction" is undefined

&nbsp; 	if (direction == DIR\_CW) {

..\\HAREWARE\\ATD5984\\ATD5984.c: 2 warnings, 11 errors

compiling adc.c...

..\\SYSTEM\\usart\\usart.h(105): error:  #20: identifier "inline" is undefined

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(105): error:  #101: "u8" has already been declared in the current scope

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(105): error:  #65: expected a ";"

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(146): warning:  #1-D: last line of file ends without a newline

&nbsp; #endif

..\\HAREWARE\\ATD5984\\ATD5984.h(65): warning:  #12-D: parsing restarts here after previous syntax error

&nbsp; void ATD5984\_Init(void);

..\\HAREWARE\\ADC\\adc.h(48): error:  #757: variable "u8"  is not a type name

&nbsp; u16 Get\_adc\_Average(u8 ADC\_Channel, u8 times);

..\\HAREWARE\\ADC\\adc.h(48): error:  #757: variable "u8"  is not a type name

&nbsp; u16 Get\_adc\_Average(u8 ADC\_Channel, u8 times);

..\\HAREWARE\\ADC\\adc.c(53): error:  #757: variable "u8"  is not a type name

&nbsp; u16 Get\_Adc1(u8 ch)

..\\HAREWARE\\ADC\\adc.c(76): error:  #757: variable "u8"  is not a type name

&nbsp; u16 Get\_adc\_Average(u8 ch, u8 count)

..\\HAREWARE\\ADC\\adc.c(76): error:  #757: variable "u8"  is not a type name

&nbsp; u16 Get\_adc\_Average(u8 ch, u8 count)

..\\HAREWARE\\ADC\\adc.c(79): error:  #65: expected a ";"

&nbsp; 	u8 i;

..\\HAREWARE\\ADC\\adc.c(82): error:  #20: identifier "i" is undefined

&nbsp; 	for(i=0; i<count; i++)

..\\HAREWARE\\ADC\\adc.c: 2 warnings, 10 errors

compiling TIM.c...

..\\SYSTEM\\usart\\usart.h(105): error:  #20: identifier "inline" is undefined

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(105): error:  #101: "u8" has already been declared in the current scope

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(105): error:  #65: expected a ";"

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(146): warning:  #1-D: last line of file ends without a newline

&nbsp; #endif

..\\HAREWARE\\ATD5984\\ATD5984.h(65): warning:  #12-D: parsing restarts here after previous syntax error

&nbsp; void ATD5984\_Init(void);

..\\HAREWARE\\TIM\\TIM.c: 2 warnings, 3 errors

compiling KEY.c...

..\\SYSTEM\\usart\\usart.h(105): error:  #20: identifier "inline" is undefined

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(105): error:  #101: "u8" has already been declared in the current scope

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(105): error:  #65: expected a ";"

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(146): warning:  #1-D: last line of file ends without a newline

&nbsp; #endif

..\\HAREWARE\\ATD5984\\ATD5984.h(65): warning:  #12-D: parsing restarts here after previous syntax error

&nbsp; void ATD5984\_Init(void);

..\\HAREWARE\\KEY\\KEY.h(47): error:  #757: variable "u8"  is not a type name

&nbsp; u8 Key\_Scan(void);

..\\HAREWARE\\KEY\\KEY.c(34): error:  #757: variable "u8"  is not a type name

&nbsp; u8 Key\_Scan(void)

..\\HAREWARE\\KEY\\KEY.c(36): error:  #65: expected a ";"

&nbsp; 	u8 tmp;

..\\HAREWARE\\KEY\\KEY.c(40): error:  #20: identifier "tmp" is undefined

&nbsp; 		tmp = 1;     // 杩斿洖1琛ㄧず鎸変笅

..\\HAREWARE\\KEY\\KEY.c: 2 warnings, 7 errors

compiling motor\_control.c...

..\\SYSTEM\\usart\\usart.h(105): error:  #20: identifier "inline" is undefined

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(105): error:  #101: "u8" has already been declared in the current scope

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(105): error:  #65: expected a ";"

&nbsp; static inline u8 K230\_IsFrameReady(void) {

..\\SYSTEM\\usart\\usart.h(146): warning:  #1-D: last line of file ends without a newline

&nbsp; #endif

..\\HAREWARE\\MOTOR\_CONTROL\\motor\_control.c: 1 warning, 3 errors

"..\\OBJ\\Template.axf" - 66 Error(s), 19 Warning(s).

Target not created.

Build Time Elapsed:  00:00:03

