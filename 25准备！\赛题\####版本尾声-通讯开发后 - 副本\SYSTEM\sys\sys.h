/**
 ******************************************************************************
 * @file    sys.h  
 * <AUTHOR> Team & [Your Name]
 * @version V1.1
 * @date    2025-01-31
 * @brief   STM32F407系统配置和GPIO操作头文件
 *          
 *          本文件定义了系统时钟初始化、GPIO位操作宏和系统配置
 *          提供类似51单片机的GPIO控制功能和系统底层配置
 * 
 * @note    功能特性:
 *          - STM32F407系统时钟配置
 *          - GPIO位带操作宏定义 (类似51单片机IO控制)
 *          - 操作系统支持开关配置
 *          - 汇编函数声明 (WFI、中断控制等)
 *          - 统一包含硬件驱动头文件
 *          
 *          位带操作原理:
 *          - 基于Cortex-M4位带操作特性
 *          - 将GPIO寄存器映射到位带别名区
 *          - 实现单个IO引脚的原子操作
 ******************************************************************************
 * @attention
 * 本程序基于正点原子STM32F407开发板例程修改
 * 原作者: 正点原子@ALIENTEK (www.openedv.com)
 * 修改: 添加详细注释和功能说明
 ******************************************************************************
 */

#ifndef __SYS_H
#define __SYS_H

#include "stm32f4xx.h"

/* 基础数据类型定义 ----------------------------------------------------------*/

/**
 * @brief  基础数据类型重定义
 * @note   为了兼容正点原子代码风格，定义常用的数据类型别名
 */
typedef uint8_t  u8;     /**< 无符号8位整型 */
typedef uint16_t u16;    /**< 无符号16位整型 */
typedef uint32_t u32;    /**< 无符号32位整型 */
typedef int8_t   s8;     /**< 有符号8位整型 */
typedef int16_t  s16;    /**< 有符号16位整型 */
typedef int32_t  s32;    /**< 有符号32位整型 */

/* 系统配置 ------------------------------------------------------------------*/

/**
 * @brief  操作系统支持配置
 * @note   0: 不支持UCOS等操作系统 (裸机模式)
 *         1: 支持UCOS等操作系统 (RTOS模式)
 *         影响延时函数和中断处理方式
 */
#define SYSTEM_SUPPORT_OS    0

/* 位带操作宏定义 ------------------------------------------------------------*/

/**
 * @brief  位带操作核心宏定义
 * @note   基于Cortex-M4位带操作特性实现单bit操作
 *         位带公式: AliasAddr = 0x42000000 + (ByteAddr-0x40000000)*8*4 + BitNumber*4
 */
#define BITBAND(addr, bitnum)     ((addr & 0xF0000000)+0x2000000+((addr &0xFFFFF)<<5)+(bitnum<<2)) 
#define MEM_ADDR(addr)            *((volatile unsigned long  *)(addr)) 
#define BIT_ADDR(addr, bitnum)    MEM_ADDR(BITBAND(addr, bitnum))

/* GPIO寄存器地址映射 --------------------------------------------------------*/

/* GPIO输出数据寄存器(ODR)地址 */
#define GPIOA_ODR_Addr    (GPIOA_BASE+20) //0x40020014
#define GPIOB_ODR_Addr    (GPIOB_BASE+20) //0x40020414 
#define GPIOC_ODR_Addr    (GPIOC_BASE+20) //0x40020814 
#define GPIOD_ODR_Addr    (GPIOD_BASE+20) //0x40020C14 
#define GPIOE_ODR_Addr    (GPIOE_BASE+20) //0x40021014 
#define GPIOF_ODR_Addr    (GPIOF_BASE+20) //0x40021414    
#define GPIOG_ODR_Addr    (GPIOG_BASE+20) //0x40021814   
#define GPIOH_ODR_Addr    (GPIOH_BASE+20) //0x40021C14    
#define GPIOI_ODR_Addr    (GPIOI_BASE+20) //0x40022014     

/* GPIO输入数据寄存器(IDR)地址 */
#define GPIOA_IDR_Addr    (GPIOA_BASE+16) //0x40020010 
#define GPIOB_IDR_Addr    (GPIOB_BASE+16) //0x40020410 
#define GPIOC_IDR_Addr    (GPIOC_BASE+16) //0x40020810 
#define GPIOD_IDR_Addr    (GPIOD_BASE+16) //0x40020C10 
#define GPIOE_IDR_Addr    (GPIOE_BASE+16) //0x40021010 
#define GPIOF_IDR_Addr    (GPIOF_BASE+16) //0x40021410 
#define GPIOG_IDR_Addr    (GPIOG_BASE+16) //0x40021810 
#define GPIOH_IDR_Addr    (GPIOH_BASE+16) //0x40021C10 
#define GPIOI_IDR_Addr    (GPIOI_BASE+16) //0x40022010 

/* GPIO位操作宏定义 ----------------------------------------------------------*/

/**
 * @brief  GPIO位操作宏 - 类似51单片机IO控制
 * @note   使用方法: 
 *         PAout(5) = 1;  // PA5输出高电平
 *         if(PBin(3))    // 读取PB3输入状态
 *         确保n的值小于16! (GPIO引脚编号0-15)
 */

/* GPIOA位操作 */
#define PAout(n)   BIT_ADDR(GPIOA_ODR_Addr,n)  // PA端口输出 
#define PAin(n)    BIT_ADDR(GPIOA_IDR_Addr,n)  // PA端口输入 

/* GPIOB位操作 */
#define PBout(n)   BIT_ADDR(GPIOB_ODR_Addr,n)  // PB端口输出
#define PBin(n)    BIT_ADDR(GPIOB_IDR_Addr,n)  // PB端口输入

/* GPIOC位操作 */
#define PCout(n)   BIT_ADDR(GPIOC_ODR_Addr,n)  // PC端口输出
#define PCin(n)    BIT_ADDR(GPIOC_IDR_Addr,n)  // PC端口输入

/* GPIOD位操作 */
#define PDout(n)   BIT_ADDR(GPIOD_ODR_Addr,n)  // PD端口输出
#define PDin(n)    BIT_ADDR(GPIOD_IDR_Addr,n)  // PD端口输入

/* GPIOE位操作 */
#define PEout(n)   BIT_ADDR(GPIOE_ODR_Addr,n)  // PE端口输出
#define PEin(n)    BIT_ADDR(GPIOE_IDR_Addr,n)  // PE端口输入

/* GPIOF位操作 */
#define PFout(n)   BIT_ADDR(GPIOF_ODR_Addr,n)  // PF端口输出
#define PFin(n)    BIT_ADDR(GPIOF_IDR_Addr,n)  // PF端口输入

/* GPIOG位操作 */
#define PGout(n)   BIT_ADDR(GPIOG_ODR_Addr,n)  // PG端口输出
#define PGin(n)    BIT_ADDR(GPIOG_IDR_Addr,n)  // PG端口输入

/* GPIOH位操作 */
#define PHout(n)   BIT_ADDR(GPIOH_ODR_Addr,n)  // PH端口输出
#define PHin(n)    BIT_ADDR(GPIOH_IDR_Addr,n)  // PH端口输入

/* GPIOI位操作 */
#define PIout(n)   BIT_ADDR(GPIOI_ODR_Addr,n)  // PI端口输出
#define PIin(n)    BIT_ADDR(GPIOI_IDR_Addr,n)  // PI端口输入

/* 汇编函数声明 --------------------------------------------------------------*/

/**
 * @brief  执行WFI指令进入睡眠模式
 * @param  None
 * @retval None
 * @note   Wait For Interrupt - 等待中断唤醒，降低功耗
 */
void WFI_SET(void);

/**
 * @brief  关闭所有中断
 * @param  None
 * @retval None  
 * @note   禁用全局中断，用于临界区保护
 */
void INTX_DISABLE(void);

/**
 * @brief  开启所有中断
 * @param  None
 * @retval None
 * @note   恢复全局中断，退出临界区
 */
void INTX_ENABLE(void);

/**
 * @brief  设置栈顶地址
 * @param  addr: 栈顶地址
 * @retval None
 * @note   设置主堆栈指针MSP，用于系统初始化
 */
void MSR_MSP(uint32_t addr);

/* 硬件驱动头文件包含 --------------------------------------------------------*/
#include "delay.h"        // 延时函数
#include "usart.h"        // 串口通信
#include "ATD5984.h"      // 底层电机驱动
#include "../../HAREWARE/MOTOR_CONTROL/motor_control.h" // 统一电机控制接口

/* 标准库头文件包含 ----------------------------------------------------------*/
#include <string.h>    // 字符串操作
#include <stdio.h>     // 标准输入输出
#include <stdint.h>    // 标准整数类型
#include <stdlib.h>    // 标准库函数
#include <math.h>      // 数学函数库

#endif

