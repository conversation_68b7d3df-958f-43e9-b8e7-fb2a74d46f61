#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "OLED.h"
#include "Servo.h"
#include "LED.h"
#include "Key.h"
#include "MemoryPoint.h"                 // 新的记忆点控制模块
#include "Bluetooth.h"                   // 蓝牙模块
#include <stdio.h>
#include <string.h>

// 激光云台控制系统 - 三按键控制方案 v2.0
int main(void)
{
	/*模块初始化*/
	OLED_Init();		//OLED初始化
	LED_Init();			//LED初始化
	Servo_Init();		//总线舵机初始化
	Key_Init();			//按键初始化
	MemoryPoint_Init();	//记忆点控制模块初始化
	Bluetooth_Init();	//蓝牙模块初始化

	/*显示启动信息*/
	OLED_ShowString(1, 1, "Laser Gimbal");
	OLED_ShowString(2, 1, "System v2.0");
	OLED_ShowString(3, 1, "3-Key Control");
	OLED_ShowString(4, 1, "Ready...");

	// 延时等待系统稳定
	Delay_ms(1000);

	// 显示操作说明
	OLED_Clear();
	OLED_ShowString(1, 1, "PB0: Unload");
	OLED_ShowString(2, 1, "PB1: Record");
	OLED_ShowString(3, 1, "PB11: Return");
	OLED_ShowString(4, 1, "Status: Ready");

	// 简单的舵机通信测试
	LED_ON();

	// 测试舵机通信
	ServoError_t test_result = Servo_SetTorqueEnable(SERVO_PAN_ID, 1);
	if (test_result == SERVO_OK) {
		OLED_ShowString(4, 1, "Servo OK");
		Servo_SetTorqueEnable(SERVO_PAN_ID, 0);  // 测试后卸载
		Servo_SetTorqueEnable(SERVO_TILT_ID, 0);
	} else {
		OLED_ShowString(4, 1, "Servo Error");
		while(1) {
			// 舵机通信错误，停止运行
			LED_ON();
			Delay_ms(200);
			LED_OFF();
			Delay_ms(200);
		}
	}

	LED_OFF();
	Delay_ms(1000);

	// 三按键控制方案主循环

	while (1)
	{
		// 按键扫描 (5ms间隔)
		Key_Scan();

		// 处理PB0按键 - 舵机卸载
		if (Key_GetEvent(KEY_UNLOAD) == KEY_EVENT_CLICK) {
			MemoryResult_t result = MemoryPoint_UnloadServos();
			if (result == MEMORY_OK) {
				LED_ON();
				Delay_ms(100);
				LED_OFF();
			} else {
				// 错误指示 - 快速闪烁
				for (int i = 0; i < 3; i++) {
					LED_ON();
					Delay_ms(100);
					LED_OFF();
					Delay_ms(100);
				}
			}
			Key_ClearEvent(KEY_UNLOAD);
		}

		// 处理PB1按键 - 记录记忆点
		if (Key_GetEvent(KEY_RECORD) == KEY_EVENT_CLICK) {
			MemoryResult_t result = MemoryPoint_RecordPosition();
			if (result == MEMORY_OK) {
				// 成功指示 - 长亮1秒
				LED_ON();
				Delay_ms(1000);
				LED_OFF();
			} else {
				// 错误指示 - 快速闪烁
				for (int i = 0; i < 5; i++) {
					LED_ON();
					Delay_ms(100);
					LED_OFF();
					Delay_ms(100);
				}
			}
			Key_ClearEvent(KEY_RECORD);
		}

		// 处理PB11按键 - 回到记忆点
		if (Key_GetEvent(KEY_RETURN) == KEY_EVENT_CLICK) {
			MemoryResult_t result = MemoryPoint_ReturnToMemory();
			if (result == MEMORY_OK) {
				// 成功指示 - 双闪
				for (int i = 0; i < 2; i++) {
					LED_ON();
					Delay_ms(300);
					LED_OFF();
					Delay_ms(300);
				}
			} else {
				// 错误指示 - 快速闪烁
				for (int i = 0; i < 3; i++) {
					LED_ON();
					Delay_ms(100);
					LED_OFF();
					Delay_ms(100);
				}
			}
			Key_ClearEvent(KEY_RETURN);
		}

		// 更新记忆点模块状态
		MemoryPoint_Update();

		// 定期更新OLED显示 (每500ms更新一次)
		static uint32_t status_counter = 0;
		if (status_counter++ >= 10) {  // 简化时间处理：每10个循环更新一次
			status_counter = 0;
			// 显示当前状态
			MemoryState_t current_state = MemoryPoint_GetState();
			const char* state_str = MemoryPoint_GetStateString();

			char status_line[20];
			sprintf(status_line, "State: %s", state_str);
			OLED_ShowString(1, 1, status_line);

			// 显示记忆点信息
			if (MemoryPoint_IsValid()) {
				MemoryPoint_t* memory = MemoryPoint_GetData();
				char memory_line1[20], memory_line2[20];
				sprintf(memory_line1, "Memory: Valid");
				sprintf(memory_line2, "%.1f, %.1f", memory->pan_angle, memory->tilt_angle);
				OLED_ShowString(2, 1, memory_line1);
				OLED_ShowString(3, 1, memory_line2);
			} else {
				OLED_ShowString(2, 1, "Memory: Invalid");
				OLED_ShowString(3, 1, "Record first");
			}

			// 显示操作提示
			OLED_ShowString(4, 1, "Keys Ready");
		}

		// 蓝牙通信处理 (每3秒发送一次状态)
		static uint32_t bluetooth_counter = 0;
		if (bluetooth_counter++ >= 60) {  // 简化时间处理：每60个循环发送一次
			bluetooth_counter = 0;
			// 发送系统状态
			char status_msg[100];
			sprintf(status_msg, "[STATUS] State:%s Memory:%s",
			        MemoryPoint_GetStateString(),
			        MemoryPoint_IsValid() ? "Valid" : "Invalid");
			Bluetooth_SendString(status_msg);

			// 如果有有效记忆点，发送位置信息
			if (MemoryPoint_IsValid()) {
				MemoryPoint_t* memory = MemoryPoint_GetData();
				char pos_msg[80];
				sprintf(pos_msg, "[POSITION] Pan:%.1f Tilt:%.1f",
				        memory->pan_angle, memory->tilt_angle);
				Bluetooth_SendString(pos_msg);
			}
		}

		// 更新蓝牙模块 (处理接收数据)
		Bluetooth_Update();

		// 主循环延时 - 20Hz更新频率
		Delay_ms(50);
	}
}
