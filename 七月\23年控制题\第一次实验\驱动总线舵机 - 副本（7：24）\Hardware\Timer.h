#ifndef __TIMER_H
#define __TIMER_H

#include "stdint.h"

// 激光云台定时器配置
#define TIMER_FREQUENCY_HZ      50      // 定时器频率 50Hz (20ms周期)
#define TIMER_PERIOD_MS         20      // 定时器周期 20ms
#define TIMER_PRESCALER         7199    // 预分频器 (72MHz / 7200 = 10kHz)
#define TIMER_AUTO_RELOAD       199     // 自动重装载值 (10kHz / 200 = 50Hz)

// 定时器回调函数类型定义
typedef void (*TimerCallback_t)(void);

// 定时器状态结构体
typedef struct {
    uint8_t is_running;             // 定时器是否运行
    uint32_t tick_count;            // 定时器滴答计数
    uint32_t overflow_count;        // 溢出计数
    TimerCallback_t callback;       // 回调函数指针
    uint8_t callback_enabled;       // 回调是否启用
} TimerState_t;

// 定时器初始化和控制
void Timer_Init(void);
void Timer_Start(void);
void Timer_Stop(void);
void Timer_Reset(void);

// 回调函数管理
void Timer_SetCallback(TimerCallback_t callback);
void Timer_EnableCallback(void);
void Timer_DisableCallback(void);

// 时间获取函数
uint32_t Timer_GetTick(void);              // 获取当前滴答数
uint32_t Timer_GetTimeMs(void);            // 获取当前时间(ms)
uint8_t Timer_IsTimeout(uint32_t start_tick, uint32_t timeout_ms);

// 延时函数 (非阻塞)
void Timer_DelayMs(uint32_t delay_ms);
uint8_t Timer_IsDelayComplete(uint32_t start_tick, uint32_t delay_ms);

// 定时器状态查询
uint8_t Timer_IsRunning(void);
uint32_t Timer_GetFrequency(void);
uint32_t Timer_GetPeriodMs(void);

// 激光云台专用定时功能
void Timer_StartPathMovement(void);         // 开始路径移动定时
void Timer_StopPathMovement(void);          // 停止路径移动定时
uint8_t Timer_IsPathStepReady(void);        // 检查是否到了下一步时间
void Timer_ResetPathStep(void);             // 重置路径步进计时

// 性能监控
uint32_t Timer_GetCpuUsage(void);           // 获取CPU使用率(简单估算)
void Timer_UpdateCpuUsage(void);            // 更新CPU使用率统计

// 中断服务函数 (在stm32f10x_it.c中实现)
void TIM2_IRQHandler(void);

// 内部函数
void Timer_IRQ_Handler(void);               // 定时器中断处理函数
void Timer_ConfigureHardware(void);         // 硬件配置

#endif
