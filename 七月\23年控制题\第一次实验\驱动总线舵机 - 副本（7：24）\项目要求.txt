项目概述
这是一个基于STM32F103C8T6微控制器的嵌入式控制项目，旨在控制一个两自由度云台（由两个HTS-25L总线舵机组成，云台上安装红色激光笔），使激光点在距离1m的平面白墙上实现直线路径移动。项目强调手动记录点位和自动往返控制，墙上路径必须是直线（通过几何插值实现）。整个系统使用裸机软件架构（无RTOS），适用于入门级嵌入式开发。

核心功能和逻辑
手动记录模式：

将舵机设置为卸载状态（无扭矩），允许用户手动掰动云台，使激光点对准墙上A点。

按下PB0按键记录当前舵机角度（使用舵机角度反馈功能，水平舵机ID=1，垂直ID=2）。

再次手动调整到B点，按PB0记录B点角度。

自动往返模式：

按下PB1按键，触发激光从当前点（初始为B）匀速直线移动到另一端（A），然后下次按下时反向（A到B），以此交替。

路径计算：假设激光初始方向垂直于墙面（offset=0），使用三角函数（tan/atan）将舵机角度转换为墙上笛卡尔坐标（x, y），在坐标空间线性插值（100步），逆算回舵机角度。匀速通过定时器延时（e.g., 每步20ms，总2s）实现。

状态机管理：state=0 (等A)、1 (等B)、2 (自动)；方向布尔变量交替。

关键挑战与解决方案：

直线路径：非角度线性插值，而是墙平面坐标插值，确保墙上直线（而非弧形）。

舵机通信：UART半双工（115200bps），角度范围0-240°（协议0-1000单位）。

手动调整：舵机卸载后手掰，记录时加载扭矩。

硬件配置
主控：STM32F103C8T6核心板。

舵机：两个HTS-25L总线舵机（ID1: 水平旋转/pan，ID2: 垂直旋转/tilt）。

调试板：BusLinker（幻尔科技），用于舵机ID设置和辅助控制。连接：调试板TX接STM32 PA10 (RX)，RX接PA9 (TX)；调试板再连舵机总线和电源（6-8V）。

输入：两个按键（PB0: 记录A/B，PB1: 触发移动），GPIO输入上拉。

其他：墙距离1m，云台图纸参考几何（但简化模型为点源）；可选LED (PC13)调试。

软件环境和实现
工具：Keil uVision5，使用STM32F10x标准库（StdPeriph_Driver）。

代码结构：

main.c：初始化、状态机、按键检测、路径计算。

servo.c：舵机协议函数（发送/接收、卸载/加载、读位置、移动）。

外设：USART1 (通信)、TIM2 (延时)、GPIO。

协议：基于HTS-25L PDF，实现指令如SERVO_POS_READ、SERVO_MOVE_TIME_WRITE。

性能：匀速移动，错误处理（范围检查、通信超时），调试输出。

开发过程和讨论点
讨论历史：从逻辑细节开始（直线路径几何、按键逻辑），确认RTOS不需要；提供了多版本工作清单（融入调试板、垂直方向假设）；解释了UART半双工和offset。

可实现性：完全可行，代码简单（~200-300行），适合裸机。潜在扩展：添加中断、精确几何偏移。

测试建议：先用BusLinker设置ID，验证手动记录和直线移动；观察墙上路径是否直线匀速。