#ifndef __GEOMETRY_H
#define __GEOMETRY_H

#include "stdint.h"
#include <math.h>

// 激光云台几何参数定义
#define WALL_DISTANCE_MM        1000.0f    // 墙面距离 (mm)
#define SERVO_PAN_ID            1          // 水平舵机ID (Pan轴)
#define SERVO_TILT_ID           2          // 垂直舵机ID (Tilt轴)

// 角度限制 (基于云台机械限位和安全考虑)
#define PAN_ANGLE_MIN           30.0f      // Pan轴最小角度 (度)
#define PAN_ANGLE_MAX           210.0f     // Pan轴最大角度 (度)
#define TILT_ANGLE_MIN          30.0f      // Tilt轴最小角度 (度)
#define TILT_ANGLE_MAX          210.0f     // Tilt轴最大角度 (度)

// 墙面坐标范围 (基于1m距离和角度限制计算)
#define WALL_X_MIN              -1732.0f   // 墙面X坐标最小值 (mm)
#define WALL_X_MAX              1732.0f    // 墙面X坐标最大值 (mm)
#define WALL_Y_MIN              -1732.0f   // 墙面Y坐标最小值 (mm)
#define WALL_Y_MAX              1732.0f    // 墙面Y坐标最大值 (mm)

// 数学常数
#define PI                      3.14159265f
#define DEG_TO_RAD              (PI / 180.0f)
#define RAD_TO_DEG              (180.0f / PI)

// 坐标结构体定义
typedef struct {
    float x;    // 墙面X坐标 (mm) - 水平方向
    float y;    // 墙面Y坐标 (mm) - 垂直方向
} WallPoint_t;

typedef struct {
    float pan;   // Pan轴角度 (度) - 水平旋转
    float tilt;  // Tilt轴角度 (度) - 垂直旋转
} ServoAngle_t;

// 错误代码定义
typedef enum {
    GEOMETRY_OK = 0,
    GEOMETRY_ERROR_OUT_OF_RANGE,    // 超出范围
    GEOMETRY_ERROR_INVALID_PARAM,   // 无效参数
    GEOMETRY_ERROR_MATH_ERROR       // 数学计算错误
} GeometryError_t;

// 核心转换函数
GeometryError_t Geometry_ServoToWall(ServoAngle_t servo_angle, WallPoint_t* wall_point);
GeometryError_t Geometry_WallToServo(WallPoint_t wall_point, ServoAngle_t* servo_angle);

// 范围检查函数
uint8_t Geometry_IsServoAngleValid(ServoAngle_t servo_angle);
uint8_t Geometry_IsWallPointValid(WallPoint_t wall_point);

// 角度标准化函数
float Geometry_NormalizeAngle(float angle);
ServoAngle_t Geometry_ClampServoAngle(ServoAngle_t servo_angle);
WallPoint_t Geometry_ClampWallPoint(WallPoint_t wall_point);

// 距离和角度计算
float Geometry_CalculateDistance(WallPoint_t point1, WallPoint_t point2);
float Geometry_CalculateAngleBetweenPoints(WallPoint_t point1, WallPoint_t point2);

// 直线路径插值算法 - 激光云台核心功能
#define PATH_INTERPOLATION_STEPS    100     // 插值步数
#define PATH_MOVE_TIME_MS          2000     // 总移动时间 (ms)
#define PATH_STEP_TIME_MS          (PATH_MOVE_TIME_MS / PATH_INTERPOLATION_STEPS)  // 每步时间

// 路径插值结构体
typedef struct {
    WallPoint_t start_point;        // 起始点
    WallPoint_t end_point;          // 终点
    WallPoint_t current_point;      // 当前点
    ServoAngle_t current_servo;     // 当前舵机角度
    uint16_t current_step;          // 当前步数
    uint16_t total_steps;           // 总步数
    uint8_t is_active;              // 路径是否激活
    uint8_t direction;              // 移动方向 (0=A→B, 1=B→A)
} PathInterpolation_t;

// 路径插值函数
GeometryError_t Path_Initialize(PathInterpolation_t* path, WallPoint_t point_a, WallPoint_t point_b);
GeometryError_t Path_GetNextStep(PathInterpolation_t* path, ServoAngle_t* next_servo_angle);
GeometryError_t Path_SetDirection(PathInterpolation_t* path, uint8_t direction);
uint8_t Path_IsComplete(PathInterpolation_t* path);
void Path_Reset(PathInterpolation_t* path);

// 直线插值核心算法
GeometryError_t Path_LinearInterpolate(WallPoint_t start, WallPoint_t end,
                                     uint16_t step, uint16_t total_steps,
                                     WallPoint_t* result);

// 路径验证和优化
GeometryError_t Path_ValidatePath(WallPoint_t point_a, WallPoint_t point_b);
float Path_CalculatePathLength(WallPoint_t point_a, WallPoint_t point_b);
uint16_t Path_CalculateOptimalSteps(WallPoint_t point_a, WallPoint_t point_b);

// 调试和测试函数
void Geometry_PrintServoAngle(ServoAngle_t servo_angle);
void Geometry_PrintWallPoint(WallPoint_t wall_point);
GeometryError_t Geometry_TestConversion(void);
GeometryError_t Path_TestInterpolation(void);

#endif
