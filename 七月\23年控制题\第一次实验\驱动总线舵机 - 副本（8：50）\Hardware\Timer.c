#include "Timer.h"
#include "stm32f10x.h"
#include <stddef.h>

// 定时器状态全局变量
static TimerState_t timer_state = {0};

// 路径移动专用计时变量
static uint32_t path_step_counter = 0;
static uint8_t path_step_ready = 0;

// CPU使用率监控变量
static uint32_t cpu_idle_count = 0;
static uint32_t cpu_total_count = 0;
static uint32_t cpu_usage_percent = 0;

/**
 * 定时器初始化 - 配置TIM2为50Hz (20ms周期)
 * 激光云台路径控制专用定时器
 */
void Timer_Init(void)
{
    // 初始化状态结构体
    timer_state.is_running = 0;
    timer_state.tick_count = 0;
    timer_state.overflow_count = 0;
    timer_state.callback = NULL;
    timer_state.callback_enabled = 0;
    
    // 重置路径计时变量
    path_step_counter = 0;
    path_step_ready = 0;
    
    // 配置硬件
    Timer_ConfigureHardware();
}

/**
 * 配置TIM2硬件
 */
void Timer_ConfigureHardware(void)
{
    // 使能TIM2时钟
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2, ENABLE);
    
    // 配置TIM2基本参数
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
    TIM_TimeBaseStructure.TIM_Period = TIMER_AUTO_RELOAD;           // 自动重装载值 199
    TIM_TimeBaseStructure.TIM_Prescaler = TIMER_PRESCALER;          // 预分频器 7199
    TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;         // 时钟分频
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;     // 向上计数
    TIM_TimeBaseInit(TIM2, &TIM_TimeBaseStructure);
    
    // 配置TIM2中断
    NVIC_InitTypeDef NVIC_InitStructure;
    NVIC_InitStructure.NVIC_IRQChannel = TIM2_IRQn;                 // TIM2中断
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;       // 抢占优先级2
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;              // 子优先级0
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;                 // 使能中断
    NVIC_Init(&NVIC_InitStructure);
    
    // 使能TIM2更新中断
    TIM_ITConfig(TIM2, TIM_IT_Update, ENABLE);
    
    // 清除中断标志
    TIM_ClearITPendingBit(TIM2, TIM_IT_Update);
}

/**
 * 启动定时器
 */
void Timer_Start(void)
{
    timer_state.is_running = 1;
    TIM_Cmd(TIM2, ENABLE);
}

/**
 * 停止定时器
 */
void Timer_Stop(void)
{
    timer_state.is_running = 0;
    TIM_Cmd(TIM2, DISABLE);
}

/**
 * 重置定时器
 */
void Timer_Reset(void)
{
    timer_state.tick_count = 0;
    timer_state.overflow_count = 0;
    path_step_counter = 0;
    path_step_ready = 0;
    TIM_SetCounter(TIM2, 0);
}

/**
 * 设置回调函数
 * @param callback 回调函数指针
 */
void Timer_SetCallback(TimerCallback_t callback)
{
    timer_state.callback = callback;
}

/**
 * 启用回调函数
 */
void Timer_EnableCallback(void)
{
    timer_state.callback_enabled = 1;
}

/**
 * 禁用回调函数
 */
void Timer_DisableCallback(void)
{
    timer_state.callback_enabled = 0;
}

/**
 * 获取当前滴答数
 * @return 滴答数
 */
uint32_t Timer_GetTick(void)
{
    return timer_state.tick_count;
}

/**
 * 获取当前时间(毫秒)
 * @return 时间(ms)
 */
uint32_t Timer_GetTimeMs(void)
{
    return timer_state.tick_count * TIMER_PERIOD_MS;
}

/**
 * 检查是否超时
 * @param start_tick 起始滴答数
 * @param timeout_ms 超时时间(ms)
 * @return 1=超时, 0=未超时
 */
uint8_t Timer_IsTimeout(uint32_t start_tick, uint32_t timeout_ms)
{
    uint32_t timeout_ticks = timeout_ms / TIMER_PERIOD_MS;
    return (Timer_GetTick() - start_tick) >= timeout_ticks;
}

/**
 * 非阻塞延时
 * @param delay_ms 延时时间(ms)
 */
void Timer_DelayMs(uint32_t delay_ms)
{
    uint32_t start_tick = Timer_GetTick();
    while (!Timer_IsTimeout(start_tick, delay_ms)) {
        // 非阻塞等待
        Timer_UpdateCpuUsage();
    }
}

/**
 * 检查延时是否完成
 * @param start_tick 起始滴答数
 * @param delay_ms 延时时间(ms)
 * @return 1=完成, 0=未完成
 */
uint8_t Timer_IsDelayComplete(uint32_t start_tick, uint32_t delay_ms)
{
    return Timer_IsTimeout(start_tick, delay_ms);
}

/**
 * 检查定时器是否运行
 * @return 1=运行, 0=停止
 */
uint8_t Timer_IsRunning(void)
{
    return timer_state.is_running;
}

/**
 * 获取定时器频率
 * @return 频率(Hz)
 */
uint32_t Timer_GetFrequency(void)
{
    return TIMER_FREQUENCY_HZ;
}

/**
 * 获取定时器周期
 * @return 周期(ms)
 */
uint32_t Timer_GetPeriodMs(void)
{
    return TIMER_PERIOD_MS;
}

/**
 * 开始路径移动定时
 */
void Timer_StartPathMovement(void)
{
    path_step_counter = 0;
    path_step_ready = 0;
}

/**
 * 停止路径移动定时
 */
void Timer_StopPathMovement(void)
{
    path_step_counter = 0;
    path_step_ready = 0;
}

/**
 * 检查是否到了下一步时间
 * @return 1=准备好, 0=未准备好
 */
uint8_t Timer_IsPathStepReady(void)
{
    return path_step_ready;
}

/**
 * 重置路径步进计时
 */
void Timer_ResetPathStep(void)
{
    path_step_counter = 0;
    path_step_ready = 0;
}

/**
 * 更新CPU使用率统计
 */
void Timer_UpdateCpuUsage(void)
{
    cpu_idle_count++;
    cpu_total_count++;
}

/**
 * 获取CPU使用率
 * @return CPU使用率百分比
 */
uint32_t Timer_GetCpuUsage(void)
{
    return cpu_usage_percent;
}

/**
 * 定时器中断处理函数 - 在TIM2_IRQHandler中调用
 */
void Timer_IRQ_Handler(void)
{
    if (TIM_GetITStatus(TIM2, TIM_IT_Update) != RESET) {
        // 清除中断标志
        TIM_ClearITPendingBit(TIM2, TIM_IT_Update);
        
        // 更新滴答计数
        timer_state.tick_count++;
        
        // 检查溢出
        if (timer_state.tick_count == 0) {
            timer_state.overflow_count++;
        }
        
        // 路径步进计时
        path_step_counter++;
        if (path_step_counter >= 1) {  // 每20ms一步
            path_step_ready = 1;
            path_step_counter = 0;
        }
        
        // CPU使用率计算 (每秒更新一次)
        if (timer_state.tick_count % 50 == 0) {  // 50 * 20ms = 1秒
            if (cpu_total_count > 0) {
                cpu_usage_percent = 100 - (cpu_idle_count * 100 / cpu_total_count);
            }
            cpu_idle_count = 0;
            cpu_total_count = 0;
        }
        
        // 执行回调函数
        if (timer_state.callback_enabled && timer_state.callback != NULL) {
            timer_state.callback();
        }
    }
}
