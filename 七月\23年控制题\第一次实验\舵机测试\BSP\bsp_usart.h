/**
  ******************************************************************************
  * @file    bsp_usart.h
  * <AUTHOR> Analysis Expert
  * @version V1.0
  * @date    2025-07-17
  * @brief   USART板级支持包头文件
  ******************************************************************************
  */

#ifndef __BSP_USART_H
#define __BSP_USART_H

#include "stm32f4xx.h"

/* USART2配置 */
#define BSP_USART2              USART2
#define BSP_USART2_CLK          RCC_APB1Periph_USART2
#define BSP_USART2_BAUDRATE     115200

/* USART2 GPIO配置 */
#define BSP_USART2_GPIO_PORT    GPIOA
#define BSP_USART2_GPIO_CLK     RCC_AHB1Periph_GPIOA
#define BSP_USART2_PIN          GPIO_Pin_2
#define BSP_USART2_PINSOURCE    GPIO_PinSource2
#define BSP_USART2_AF           GPIO_AF_USART2

/* 超时定义 */
#define BSP_USART_TX_TIMEOUT    20      // 发送超时 20ms
#define BSP_USART_RX_TIMEOUT    100     // 接收超时 100ms

/* 函数声明 */
void BSP_USART2_Init(void);
int BSP_USART2_SendData(uint8_t *data, uint16_t len, uint32_t timeout);
int BSP_USART2_ReceiveData(uint8_t *data, uint16_t len, uint32_t timeout);

#endif /* __BSP_USART_H */
