/**
  ******************************************************************************
  * @file    bsp_key.h
  * <AUTHOR> Analysis Expert
  * @version V1.0
  * @date    2025-07-17
  * @brief   按键板级支持包头文件
  ******************************************************************************
  */

#ifndef __BSP_KEY_H
#define __BSP_KEY_H

#include "stm32f4xx.h"

/* 按键定义 */
#define KEY_K1           0    // PE3 - K1按键

/* 按键状态定义 */
#define KEY_PRESSED      1
#define KEY_RELEASED     0

/* 按键GPIO定义 */
#define KEY_GPIO_PORT    GPIOE
#define KEY_GPIO_CLK     RCC_AHB1Periph_GPIOE
#define KEY_K1_PIN       GPIO_Pin_3

/* 消抖时间定义 */
#define KEY_DEBOUNCE_TIME    20    // 20ms消抖时间

/* 函数声明 */
void BSP_Key_Init(void);
uint8_t BSP_Key_Scan(uint8_t key);
uint8_t BSP_Key_GetState(uint8_t key);

#endif /* __BSP_KEY_H */
