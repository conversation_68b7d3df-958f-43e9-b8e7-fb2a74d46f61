#include "sys.h"
#include "usart.h"
#include "delay.h"
#include "led.h"
////////////////////////////////////////////////////////////////////////////////// 	 
//���ʹ��ucos,����������ͷ�ļ�����.
#if SYSTEM_SUPPORT_OS
#include "includes.h"					//ucos ʹ��	  
#endif
//////////////////////////////////////////////////////////////////////////////////	 
//������ֻ��ѧϰʹ�ã�δ���������ɣ��������������κ���;
//ALIENTEK STM32F4̽���߿�����
//����1��ʼ��		   
//����ԭ��@ALIENTEK
//������̳:www.openedv.com
//�޸�����:2014/6/10
//�汾��V1.5
//��Ȩ���У�����ؾ���
//Copyright(C) �������������ӿƼ����޹�˾ 2009-2019
//All rights reserved
//********************************************************************************
//V1.3�޸�˵�� 
//֧����Ӧ��ͬƵ���µĴ��ڲ���������.
//�����˶�printf��֧��
//�����˴��ڽ��������.
//������printf��һ���ַ���ʧ��bug
//V1.4�޸�˵��
//1,�޸Ĵ��ڳ�ʼ��IO��bug
//2,�޸���USART_RX_STA,ʹ�ô����������ֽ���Ϊ2��14�η�
//3,������USART_REC_LEN,���ڶ��崮������������յ��ֽ���(������2��14�η�)
//4,�޸���EN_USART1_RX��ʹ�ܷ�ʽ
//V1.5�޸�˵��
//1,�����˶�UCOSII��֧��
////////////////////////////////////////////////////////////////////////////////// 	  
 

//////////////////////////////////////////////////////////////////
//�������´���,֧��printf����,������Ҫѡ��use MicroLIB	  
#if 1
#pragma import(__use_no_semihosting)             
//��׼����Ҫ��֧�ֺ���                 
struct __FILE 
{ 
	int handle; 
}; 

FILE __stdout;       
//����_sys_exit()�Ա���ʹ�ð�����ģʽ    
void _sys_exit(int x) 
{ 
	x = x; 
} 
//�ض���fputc���� 
int fputc(int ch, FILE *f)
{ 	
	while((USART1->SR&0X40)==0);//ѭ������,ֱ���������   
	USART1->DR = (u8) ch;      
	return ch;
}
#endif
 
#if EN_USART1_RX   //���ʹ���˽���
//����1�жϷ������
//ע��,��ȡUSARTx->SR�ܱ���Ī������Ĵ���   	
u8 USART_RX_BUF[USART_REC_LEN];     //接收缓冲,最大USART_REC_LEN个字节.
//接收状态
//bit15：	接收完成标志
//bit14：	接收到0x0d
//bit13~0：	接收到的有效字节数目
u16 USART_RX_STA=0;       //接收状态标记

// 舵机协议相关变量
u8 SERVO_RX_BUF[SERVO_MAX_PACKET_LEN]; //舵机接收缓冲
u8 SERVO_RX_STA = 0;					//舵机接收状态: 0-等待帧头1, 1-等待帧头2, 2-接收数据
u8 SERVO_RX_CNT = 0;					//舵机接收计数器
u8 SERVO_PKT_LEN = 0;					//舵机数据包长度

// 串口屏通信相关变量
u8 SCREEN_RX_BUF[SCREEN_REC_LEN];		//串口屏接收缓冲
u8 SCREEN_RX_STA = 0;					//串口屏接收状态
u8 SCREEN_RX_CNT = 0;					//串口屏接收计数

// 舵机位置跟踪变量
u16 SERVO1_POSITION = 500;				//舵机1当前位置，初始值为中位
u16 SERVO2_POSITION = 500;				//舵机2当前位置，初始值为中位	

// 角度显示控制变量
u8 auto_refresh_enabled = 0;			//自动刷新禁用，只有按显示按钮时才更新

//��ʼ��IO ����1 
//bound:������
void uart_init(u32 bound){
   //GPIO�˿�����
  GPIO_InitTypeDef GPIO_InitStructure;
	USART_InitTypeDef USART_InitStructure;
	NVIC_InitTypeDef NVIC_InitStructure;
	
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA,ENABLE); //ʹ��GPIOAʱ��
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1,ENABLE);//ʹ��USART1ʱ��
 
	//����1��Ӧ���Ÿ���ӳ��
	GPIO_PinAFConfig(GPIOA,GPIO_PinSource9,GPIO_AF_USART1); //GPIOA9����ΪUSART1
	GPIO_PinAFConfig(GPIOA,GPIO_PinSource10,GPIO_AF_USART1); //GPIOA10����ΪUSART1
	
	//USART1�˿�����
  GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9 | GPIO_Pin_10; //GPIOA9��GPIOA10
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;//���ù���
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;	//�ٶ�50MHz
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP; //���츴�����
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP; //����
	GPIO_Init(GPIOA,&GPIO_InitStructure); //��ʼ��PA9��PA10

   //USART1 ��ʼ������
	USART_InitStructure.USART_BaudRate = bound;//����������
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;//�ֳ�Ϊ8λ���ݸ�ʽ
	USART_InitStructure.USART_StopBits = USART_StopBits_1;//һ��ֹͣλ
	USART_InitStructure.USART_Parity = USART_Parity_No;//����żУ��λ
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;//��Ӳ������������
	USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;	//�շ�ģʽ
  USART_Init(USART1, &USART_InitStructure); //��ʼ������1
	
  USART_Cmd(USART1, ENABLE);  //ʹ�ܴ���1 
	
	//USART_ClearFlag(USART1, USART_FLAG_TC);
	
#if EN_USART1_RX	
	USART_ITConfig(USART1, USART_IT_RXNE, ENABLE);//��������ж�

	//Usart1 NVIC ����
  NVIC_InitStructure.NVIC_IRQChannel = USART1_IRQn;//����1�ж�ͨ��
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=3;//��ռ���ȼ�3
	NVIC_InitStructure.NVIC_IRQChannelSubPriority =3;		//�����ȼ�3
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;			//IRQͨ��ʹ��
	NVIC_Init(&NVIC_InitStructure);	//����ָ���Ĳ�����ʼ��VIC�Ĵ�����

#endif
	
}


void USART1_IRQHandler(void)                	//串口1中断服务程序
{
	u8 Res;
#if SYSTEM_SUPPORT_OS 		//如果SYSTEM_SUPPORT_OS为真，则需要支持OS.
	OSIntEnter();    
#endif
	if(USART_GetITStatus(USART1, USART_IT_RXNE) != RESET)  //接收中断
	{
		Res = USART_ReceiveData(USART1);	//读取接收到的数据
		
		// 舵机协议包状态机处理
		switch(SERVO_RX_STA)
		{
			case 0:	// 等待第一个帧头 0x55
				if(Res == SERVO_FRAME_HEADER)
				{
					SERVO_RX_STA = 1;
					SERVO_RX_CNT = 0;
				}
				break;
				
			case 1:	// 等待第二个帧头 0x55
				if(Res == SERVO_FRAME_HEADER)
				{
					SERVO_RX_STA = 2;
					SERVO_RX_BUF[0] = SERVO_FRAME_HEADER;
					SERVO_RX_BUF[1] = SERVO_FRAME_HEADER;
					SERVO_RX_CNT = 2;
				}
				else
				{
					SERVO_RX_STA = 0; // 重新开始
				}
				break;
				
			case 2:	// 接收数据包
				SERVO_RX_BUF[SERVO_RX_CNT] = Res;
				SERVO_RX_CNT++;
				
				if(SERVO_RX_CNT == 5) // 收到ID、Length、Cmd
				{
					SERVO_PKT_LEN = SERVO_RX_BUF[3] + 3; // Length + 帧头(2) + 校验(1)
					if(SERVO_PKT_LEN > SERVO_MAX_PACKET_LEN)
					{
						SERVO_RX_STA = 0; // 包长度错误，重新开始
						break;
					}
				}
				
				if(SERVO_RX_CNT >= 5 && SERVO_RX_CNT >= SERVO_PKT_LEN)
				{
					// 数据包接收完成，处理数据包
					Servo_ProcessPacket();
					// 重新开始等待下一包
					SERVO_RX_STA = 0;
				}
				
				if(SERVO_RX_CNT >= SERVO_MAX_PACKET_LEN)
				{
					SERVO_RX_STA = 0; // 防止溢出
				}
				break;
		}
  } 
#if SYSTEM_SUPPORT_OS 	//如果SYSTEM_SUPPORT_OS为真，则需要支持OS.
	OSIntExit();  											 
#endif
} 
#endif	

// 舵机控制函数实现

// 串口发送数组
void USART_SendArray(u8 *arr, u16 len) 
{
	u16 i;
	for (i = 0; i < len; i++) 
	{
		while(USART_GetFlagStatus(USART1, USART_FLAG_TXE) != SET);
		USART_SendData(USART1, arr[i]);
	}
	while(USART_GetFlagStatus(USART1, USART_FLAG_TC) != SET); // 等待发送完成
}

// 计算舵机协议校验和
u8 Servo_CalculateChecksum(u8 *data, u8 len) 
{
	u8 sum = 0;
	u8 i;
	for (i = 0; i < len; i++) 
	{
		sum += data[i];
	}
	return ~sum; // 取反
}

// 设置舵机为电机模式并控制转动速度
void Servo_SetMotorMode(u8 id, s16 speed) 
{
	u8 cmd_packet[10];
	
	// 构建命令包: 55 55 ID 07 1D 01 00 speed_low speed_high checksum
	cmd_packet[0] = 0x55;								// 帧头1
	cmd_packet[1] = 0x55;								// 帧头2
	cmd_packet[2] = id;									// 舵机ID
	cmd_packet[3] = 0x07;								// 数据长度
	cmd_packet[4] = 0x1D;								// 指令(SERVO_OR_MOTOR_MODE_WRITE = 29 = 0x1D)
	cmd_packet[5] = 0x01;								// 参数1: 电机模式
	cmd_packet[6] = 0x00;								// 参数2: 空值
	cmd_packet[7] = (u8)(speed & 0xFF);					// 参数3: 速度低字节
	cmd_packet[8] = (u8)((speed >> 8) & 0xFF);			// 参数4: 速度高字节
	
	// 计算校验和 (从ID开始到速度高字节)
	cmd_packet[9] = Servo_CalculateChecksum(&cmd_packet[2], 7);
	
	// 发送命令包
	USART_SendArray(cmd_packet, 10);
}

// 装载舵机电机(上电状态)
void Servo_LoadMotor(u8 id) 
{
	u8 cmd_packet[7];
	
	// 构建命令包: 55 55 ID 04 1F 01 checksum
	cmd_packet[0] = 0x55;								// 帧头1
	cmd_packet[1] = 0x55;								// 帧头2
	cmd_packet[2] = id;									// 舵机ID
	cmd_packet[3] = 0x04;								// 数据长度
	cmd_packet[4] = 0x1F;								// 指令(SERVO_LOAD_OR_UNLOAD_WRITE = 31 = 0x1F)
	cmd_packet[5] = 0x01;								// 参数1: 1=装载电机，0=卸载电机
	
	// 计算校验和 (从ID开始到参数)
	cmd_packet[6] = Servo_CalculateChecksum(&cmd_packet[2], 4);
	
	// 发送命令包
	USART_SendArray(cmd_packet, 7);
}

// 舵机位置控制(位置模式，带时间)
void Servo_MoveToPosition(u8 id, u16 position, u16 time) 
{
	u8 cmd_packet[10];
	
	// 构建命令包: 55 55 ID 07 01 pos_low pos_high time_low time_high checksum
	cmd_packet[0] = 0x55;								// 帧头1
	cmd_packet[1] = 0x55;								// 帧头2
	cmd_packet[2] = id;									// 舵机ID
	cmd_packet[3] = 0x07;								// 数据长度
	cmd_packet[4] = 0x01;								// 指令(SERVO_MOVE_TIME_WRITE = 1)
	cmd_packet[5] = (u8)(position & 0xFF);				// 参数1: 角度低字节
	cmd_packet[6] = (u8)((position >> 8) & 0xFF);		// 参数2: 角度高字节  
	cmd_packet[7] = (u8)(time & 0xFF);					// 参数3: 时间低字节
	cmd_packet[8] = (u8)((time >> 8) & 0xFF);			// 参数4: 时间高字节
	
	// 计算校验和 (从ID开始到时间高字节)
	cmd_packet[9] = Servo_CalculateChecksum(&cmd_packet[2], 7);
	
	// 发送命令包
	USART_SendArray(cmd_packet, 10);
}

// 读取舵机当前位置
void Servo_ReadPosition(u8 id)
{
	u8 cmd_packet[6];

	// 构建命令包: 55 55 ID 03 02 checksum
	cmd_packet[0] = 0x55;								// 帧头1
	cmd_packet[1] = 0x55;								// 帧头2
	cmd_packet[2] = id;									// 舵机ID
	cmd_packet[3] = 0x03;								// 数据长度
	cmd_packet[4] = 0x02;								// 指令(SERVO_MOVE_TIME_READ = 2)

	// 计算校验和 (从ID开始到指令)
	cmd_packet[5] = Servo_CalculateChecksum(&cmd_packet[2], 3);

	// 发送命令包
	USART_SendArray(cmd_packet, 6);
}

// 处理接收到的舵机数据包
u8 Servo_ProcessPacket(void) 
{
	u8 checksum;
	u16 position;
	u8 servo_id;
	
	// 检查数据包长度和校验和
	if(SERVO_RX_CNT < 6) return 0; // 最小包长度检查
	
	// 提取舵机ID
	servo_id = SERVO_RX_BUF[2];
	
	// 计算校验和
	checksum = Servo_CalculateChecksum(&SERVO_RX_BUF[2], SERVO_RX_CNT - 3);
	if(checksum != SERVO_RX_BUF[SERVO_RX_CNT - 1]) {
		return 0; // 校验失败
	}
	
	// 判断是否为位置读取响应 (指令0x02)
	if(SERVO_RX_BUF[4] == 0x02 && SERVO_RX_CNT >= 9) {
		// 提取位置数据 (低字节在前，高字节在后)
		position = SERVO_RX_BUF[5] | (SERVO_RX_BUF[6] << 8);
		
		// 更新对应舵机的位置变量
		if(servo_id == 1) {
			SERVO1_POSITION = position;
		} else if(servo_id == 2) {
			SERVO2_POSITION = position;
		}
		
		return 1; // 成功处理
	}
	
	return 0;
}

// UART2初始化函数(串口屏通信)
void uart2_init(u32 bound)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	USART_InitTypeDef USART_InitStructure;
	NVIC_InitTypeDef NVIC_InitStructure;
	
	// 使能UART2和GPIOA时钟
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART2, ENABLE);
 
	// PA2复用为USART2_TX, PA3复用为USART2_RX
	GPIO_PinAFConfig(GPIOA, GPIO_PinSource2, GPIO_AF_USART2);
	GPIO_PinAFConfig(GPIOA, GPIO_PinSource3, GPIO_AF_USART2);
	
	// UART2端口配置
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2 | GPIO_Pin_3;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
	GPIO_Init(GPIOA, &GPIO_InitStructure);

	// UART2初始化设置
	USART_InitStructure.USART_BaudRate = bound;
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;
	USART_InitStructure.USART_StopBits = USART_StopBits_1;
	USART_InitStructure.USART_Parity = USART_Parity_No;
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
	USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
  	USART_Init(USART2, &USART_InitStructure);
  	
#if EN_USART2_RX
	// Usart2 NVIC配置
	NVIC_InitStructure.NVIC_IRQChannel = USART2_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 3;
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 3;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_Init(&NVIC_InitStructure);
	
	USART_ITConfig(USART2, USART_IT_RXNE, ENABLE);
#endif
	
	USART_Cmd(USART2, ENABLE);
}

// UART2中断处理函数
void USART2_IRQHandler(void)
{
	u8 Res;
#if SYSTEM_SUPPORT_OS
	OSIntEnter();    
#endif
	if(USART_GetITStatus(USART2, USART_IT_RXNE) != RESET)
	{
		Res = USART_ReceiveData(USART2);
		
		// 串口屏数据接收处理
		if(SCREEN_RX_CNT < (SCREEN_REC_LEN-1))
		{
			SCREEN_RX_BUF[SCREEN_RX_CNT] = Res;
			SCREEN_RX_CNT++;
			
			// 检查是否为串口屏数据包结束标志 0xFF 0xFF 0xFF
			if(SCREEN_RX_CNT >= 3)
			{
				if(SCREEN_RX_BUF[SCREEN_RX_CNT-3] == 0xFF && 
				   SCREEN_RX_BUF[SCREEN_RX_CNT-2] == 0xFF && 
				   SCREEN_RX_BUF[SCREEN_RX_CNT-1] == 0xFF)
				{
					SCREEN_RX_STA = 1;  // 标记接收完成
				}
			}
		}
		else
		{
			SCREEN_RX_CNT = 0;  // 缓冲区溢出，重置
		}
	}
#if SYSTEM_SUPPORT_OS
	OSIntExit();  
#endif
}

// UART2发送数组
void USART2_SendArray(u8 *arr, u16 len)
{
	u16 i;
	for (i = 0; i < len; i++)
	{
		while(USART_GetFlagStatus(USART2, USART_FLAG_TXE) != SET);
		USART_SendData(USART2, arr[i]);
	}
	while(USART_GetFlagStatus(USART2, USART_FLAG_TC) != SET);
}

// 串口屏命令处理
void Screen_ProcessCommand(void)
{
	u8 component_id;
	u16 new_position;
	
	if(SCREEN_RX_STA == 1 && SCREEN_RX_CNT >= 6)
	{
		// 检查是否为控件事件 0x65
		if(SCREEN_RX_BUF[0] == 0x65)
		{
			component_id = SCREEN_RX_BUF[2];
			
			// 根据控件ID处理事件
			switch(component_id)
			{
				case 1:  // 舵机1顺时针按钮 - 增加10度(约42个位置值)
					new_position = SERVO1_POSITION + 42;  // 10度 ≈ 42位置值 (10*1000/240)
					if(new_position > 1000) new_position = 1000;  // 限制最大值
					SERVO1_POSITION = new_position;
					Servo_MoveToPosition(1, new_position, 300);  // 300ms运动时间
					Screen_UpdateAngleDisplay();  // 立即更新角度显示
					break;
					
				case 2:  // 舵机1逆时针按钮 - 减少10度  
					if(SERVO1_POSITION >= 42) {
						new_position = SERVO1_POSITION - 42;
					} else {
						new_position = 0;  // 限制最小值
					}
					SERVO1_POSITION = new_position;
					Servo_MoveToPosition(1, new_position, 300);  // 300ms运动时间
					Screen_UpdateAngleDisplay();  // 立即更新角度显示
					break;
					
				case 3:  // 舵机2顺时针按钮 - 增加10度
					new_position = SERVO2_POSITION + 42;
					if(new_position > 1000) new_position = 1000;  // 限制最大值
					SERVO2_POSITION = new_position;
					Servo_MoveToPosition(2, new_position, 300);  // 300ms运动时间
					Screen_UpdateAngleDisplay();  // 立即更新角度显示
					break;
					
				case 4:  // 舵机2逆时针按钮 - 减少10度
					if(SERVO2_POSITION >= 42) {
						new_position = SERVO2_POSITION - 42;
					} else {
						new_position = 0;  // 限制最小值
					}
					SERVO2_POSITION = new_position;
					Servo_MoveToPosition(2, new_position, 300);  // 300ms运动时间
					Screen_UpdateAngleDisplay();  // 立即更新角度显示
					break;
					
				case 5:  // 复位按钮 - 两舵机回中位
					SERVO1_POSITION = 500;
					SERVO2_POSITION = 500;
					Servo_MoveToPosition(1, 500, 1000);  // 舵机1回中位
					Servo_MoveToPosition(2, 500, 1000);  // 舵机2回中位
					Screen_UpdateAngleDisplay();  // 立即更新角度显示
					break;
					
				case 6:  // 上载按钮
					Servo_LoadMotor(1);  // 装载舵机1
					Servo_LoadMotor(2);  // 装载舵机2
					break;
					
				case 7:  // 卸载按钮
					// 发送卸载命令 (参数为0)
					{
						u8 cmd_packet[7];
						cmd_packet[0] = 0x55; cmd_packet[1] = 0x55;
						cmd_packet[2] = 1; cmd_packet[3] = 0x04; cmd_packet[4] = 0x1F; cmd_packet[5] = 0x00;
						cmd_packet[6] = Servo_CalculateChecksum(&cmd_packet[2], 4);
						USART_SendArray(cmd_packet, 7);
						
						cmd_packet[2] = 2;
						cmd_packet[6] = Servo_CalculateChecksum(&cmd_packet[2], 4);
						USART_SendArray(cmd_packet, 7);
					}
					break;
					
				case 8:  // 显示角度按钮 - 读取舵机实际位置
					// 读取两个舵机的当前位置
					Servo_ReadPosition(1);  // 读取舵机1位置
					delay_ms(100);          // 等待响应
					Servo_ReadPosition(2);  // 读取舵机2位置
					delay_ms(100);          // 等待响应
					
					// 更新角度显示
					Screen_UpdateAngleDisplay();
					break;
			}
		}
		
		// 清除接收状态
		SCREEN_RX_STA = 0;
		SCREEN_RX_CNT = 0;
	}
}

// 更新串口屏角度显示 - 显示0-240度角度值
void Screen_UpdateAngleDisplay(void)
{
	u8 cmd_str[20];
	u8 len;
	u16 angle1, angle2;
	
	// 将位置值转换为角度 (0-1000 对应 0-240度)
	angle1 = (u16)((u32)SERVO1_POSITION * 240 / 1000);
	angle2 = (u16)((u32)SERVO2_POSITION * 240 / 1000);
	
	// 发送舵机1角度到文本框t0
	len = 0;
	cmd_str[len++] = 't';
	cmd_str[len++] = '0';
	cmd_str[len++] = '.';
	cmd_str[len++] = 't';
	cmd_str[len++] = 'x';
	cmd_str[len++] = 't';
	cmd_str[len++] = '=';
	cmd_str[len++] = '"';
	
	// 角度数字转字符串
	if(angle1 >= 100) {
		cmd_str[len++] = '0' + (angle1 / 100);
	}
	if(angle1 >= 10) {
		cmd_str[len++] = '0' + ((angle1 % 100) / 10);
	}
	cmd_str[len++] = '0' + (angle1 % 10);
	
	cmd_str[len++] = '"';
	cmd_str[len++] = 0xFF;
	cmd_str[len++] = 0xFF;
	cmd_str[len++] = 0xFF;
	
	USART2_SendArray(cmd_str, len);
	delay_ms(50);  // 减少延迟提高响应速度
	
	// 发送舵机2角度到文本框t1
	len = 0;
	cmd_str[len++] = 't';
	cmd_str[len++] = '1';
	cmd_str[len++] = '.';
	cmd_str[len++] = 't';
	cmd_str[len++] = 'x';
	cmd_str[len++] = 't';
	cmd_str[len++] = '=';
	cmd_str[len++] = '"';
	
	if(angle2 >= 100) {
		cmd_str[len++] = '0' + (angle2 / 100);
	}
	if(angle2 >= 10) {
		cmd_str[len++] = '0' + ((angle2 % 100) / 10);
	}
	cmd_str[len++] = '0' + (angle2 % 10);
	
	cmd_str[len++] = '"';
	cmd_str[len++] = 0xFF;
	cmd_str[len++] = 0xFF;
	cmd_str[len++] = 0xFF;
	
	USART2_SendArray(cmd_str, len);
}

 

