#ifndef __SERVO_H
#define __SERVO_H

#include "stdint.h"

#define SERVO_HEADER        0x55
#define SERVO_CMD_MOVE_TIME_WRITE  0x01
#define SERVO_CMD_POS_READ         0x1C
#define SERVO_MAX_ANGLE     240.0f
#define SERVO_MAX_POSITION  1000

void Servo_Init(void);
void Servo_SetPosition(uint8_t id, float angle);
void Servo_SetPositionWithTime(uint8_t id, float angle, uint16_t time_ms);
int Servo_ReadPosition(uint8_t id, uint16_t *position);
float Servo_PositionToAngle(uint16_t position);

#endif
