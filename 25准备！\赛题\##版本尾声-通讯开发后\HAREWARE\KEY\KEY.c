#include "../../SYSTEM/sys/sys.h"
#include "KEY.h"

/* 宏定义 --------------------------------------------------------------------*/
#define KEY PEin(4)        // 按键状态读取宏，PE4引脚输入值
#define LED0 PFout(9)      // LED0控制宏，PF9引脚输出值

/**
 * @brief  按键和LED初始化函数
 * @param  None
 * @retval None
 * @note   配置PE4为输入模式，使能内部上拉电阻
 *         配置PF9为输出模式，用于LED0控制
 */
void Key_Init(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	
	/* 使能GPIOE和GPIOF时钟 */
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOE | RCC_AHB1Periph_GPIOF, ENABLE);
	
	/* 配置PE4为输入模式 (按键) */
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4;       // PE4引脚
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;    // 输入模式
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;    // 内部上拉电阻使能
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz; // 响应速度(输入模式下无影响)
	GPIO_Init(GPIOE, &GPIO_InitStructure);
	
	/* 配置PF9为输出模式 (LED0) */
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9;       // PF9引脚
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;   // 输出模式
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;  // 推挽输出
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz; // 50MHz速度
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL; // 无上下拉
	GPIO_Init(GPIOF, &GPIO_InitStructure);
	
	/* 初始化LED0为熄灭状态 */
	LED0_Set(0);
}

/**
 * @brief  LED0控制函数
 * @param  state: 1=点亮, 0=熄灭
 * @retval None
 * @note   控制PF9引脚输出，低电平点亮LED，高电平熄灭LED
 */
void LED0_Set(u8 state)
{
	if(state) {
		LED0 = 0;  // 低电平点亮LED
	} else {
		LED0 = 1;  // 高电平熄灭LED
	}
}

/**
 * @brief  LED0切换状态函数
 * @param  None
 * @retval None
 * @note   切换LED0当前状态，点亮变熄灭，熄灭变点亮
 */
void LED0_Toggle(void)
{
	LED0 = !LED0;  // 反转当前输出状态
}

/**
 * @brief  按键状态扫描函数
 * @param  None
 * @retval 按键状态: 1=按下, 0=释放
 * @note   读取PE4引脚电平，低电平表示按键按下
 *         无去抖处理，需要调用方自行处理
 */
u8 Key_Scan(void)
{
	u8 tmp;
	
	/* 读取按键状态并取反逻辑 */
	if(KEY == 0)     // 按键按下时，PE4为低电平
		tmp = 1;     // 返回1表示按下
	else             // 按键释放时，PE4为高电平(上拉)
		tmp = 0;     // 返回0表示释放
	
	return tmp;
}

/**
 * @brief  按键状态扫描带防抖功能
 * @param  None
 * @retval 按键事件: 1=按下事件, 0=无事件
 * @note   只在按键按下的瞬间返回1，适合状态切换
 *         内部维护按键状态，实现防抖和边沿检测
 */
u8 Key_Scan_Debounce(void)
{
	static u8 key_state = 0;  // 按键状态 0=释放, 1=按下
	static u8 key_count = 0;  // 防抖计数器
	u8 key_current;
	u8 key_event = 0;
	
	/* 读取当前按键状态 */
	key_current = (KEY == 0) ? 1 : 0;
	
	/* 状态机防抖处理 */
	if(key_current != key_state) {
		key_count++;
		if(key_count >= 3) {  // 连续3次采样一致才认为状态改变
			if(key_state == 0 && key_current == 1) {
				key_event = 1;  // 检测到按下事件
			}
			key_state = key_current;
			key_count = 0;
		}
	} else {
		key_count = 0;  // 状态一致，清零计数器
	}
	
	return key_event;
}
