# 🎯 手动调整舵机功能说明

## ✅ **您的需求确认**

您需要：**手动旋转舵机使激光点与A/B点重合，然后记录坐标**

## 🔧 **当前代码状态**

### ❌ **修复后的问题**
修复后的代码**没有卸载扭矩功能**，无法手动调整：

```c
// 当前简化版本（无法手动调整）
StateMachineError_t StateMachine_RecordPointA(LaserGimbalState_t* sm)
{
    // 直接读取位置，舵机始终锁定
    float pan_angle = 0, tilt_angle = 0;
    ServoError_t pan_error = Servo_ReadPosition(SERVO_PAN_ID, &pan_angle);
    // 立即记录，无手动调整机会
}
```

### ✅ **需要的功能**
```c
// 理想的手动调整流程
1. 按下PB0 → 卸载舵机扭矩 → 可以手动掰动
2. 手动调整激光点到目标A位置
3. 再按PB0 → 加载扭矩 → 读取并记录位置
4. 重复B点流程
```

## 🎯 **手动调整操作流程**

### 📱 **蓝牙信息提示**
```
第一次按PB0:
[Laser Gimbal] Point A: Servos Unloaded - Manually Adjust Laser to Target A

手动调整激光点位置...

2秒后:
[Laser Gimbal] Point A: Ready to Record - Press PB0 Again to Confirm

第二次按PB0:
[Laser Gimbal] Point A Recorded Successfully
```

### 🔄 **完整操作步骤**

#### 记录A点：
1. **第一次按PB0** → 舵机卸载扭矩，可以手动掰动
2. **手动调整** → 旋转舵机使激光点对准目标A位置
3. **等待2秒** → 系统提示准备记录
4. **第二次按PB0** → 舵机加载扭矩，读取并记录A点坐标

#### 记录B点：
1. **第一次按PB0** → 舵机卸载扭矩，可以手动掰动
2. **手动调整** → 旋转舵机使激光点对准目标B位置
3. **等待2秒** → 系统提示准备记录
4. **第二次按PB0** → 舵机加载扭矩，读取并记录B点坐标

#### 启动自动移动：
5. **按PB1** → 启动A-B点自动往返移动

## 🛠️ **实现方案**

### 方案1：修复编译错误（推荐）
修复当前代码的编译问题，实现完整的手动调整功能。

### 方案2：简化版本
添加一个简单的扭矩控制命令，通过蓝牙手动控制。

### 方案3：使用副本项目
直接使用"驱动总线舵机 - 副本"项目，手动添加扭矩控制。

## 🔍 **当前编译问题**

编译错误主要是C语言语法问题：
```
Hardware\StateMachine.c(207): warning: a declaration cannot have a label
Hardware\StateMachine.c(352): error: expected a declaration
```

需要修复switch语句中的变量声明问题。

## 💡 **临时解决方案**

如果编译问题复杂，可以先使用简化方案：

### 添加蓝牙命令控制扭矩
```c
// 在蓝牙命令处理中添加
if (strcmp(command, "UNLOAD") == 0) {
    Servo_SetTorqueEnable(SERVO_PAN_ID, 0);
    Servo_SetTorqueEnable(SERVO_TILT_ID, 0);
    Bluetooth_SendString("Servos Unloaded - Manual Adjust OK\r\n");
}

if (strcmp(command, "LOAD") == 0) {
    Servo_SetTorqueEnable(SERVO_PAN_ID, 1);
    Servo_SetTorqueEnable(SERVO_TILT_ID, 1);
    Bluetooth_SendString("Servos Loaded - Position Locked\r\n");
}

if (strcmp(command, "READ") == 0) {
    float pan_angle = 0, tilt_angle = 0;
    Servo_ReadPosition(SERVO_PAN_ID, &pan_angle);
    Servo_ReadPosition(SERVO_TILT_ID, &tilt_angle);
    char pos_msg[64];
    sprintf(pos_msg, "Position: Pan=%.1f° Tilt=%.1f°\r\n", pan_angle, tilt_angle);
    Bluetooth_SendString(pos_msg);
}
```

### 手动操作流程
```
1. 发送 "UNLOAD" → 卸载扭矩，手动调整到A点
2. 发送 "LOAD" → 加载扭矩，锁定位置
3. 发送 "READ" → 读取当前位置
4. 按PB0 → 记录A点
5. 重复B点流程
```

## 🎯 **推荐方案**

### 立即可用方案：
1. **使用副本项目** - 舵机功能已验证正常
2. **添加扭矩控制** - 在副本基础上添加UNLOAD/LOAD命令
3. **手动调整测试** - 验证手动调整功能

### 完整功能方案：
1. **修复编译错误** - 解决当前代码的语法问题
2. **完整手动调整流程** - 实现按键触发的自动化手动调整
3. **蓝牙反馈优化** - 提供详细的操作指导

## 🔧 **您的选择**

请告诉我您希望：

**A. 修复当前代码的编译错误** - 实现完整的按键触发手动调整功能
**B. 使用简化的蓝牙命令方案** - 通过发送命令控制扭矩
**C. 基于副本项目改进** - 在已验证的舵机功能基础上添加扭矩控制

无论选择哪种方案，最终都能实现：
✅ **手动旋转舵机对准激光点**
✅ **记录准确的A/B点坐标**
✅ **启动自动往返移动**
