#include "led.h"

/**
 * @brief  LED初始化函数
 * @param  None
 * @retval None
 * @note   初始化LED0对应的GPIO引脚
 */
void LED_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 使能GPIOF时钟
    RCC_AHB1PeriphClockCmd(LED0_CLK, ENABLE);
    
    // 配置LED0引脚
    GPIO_InitStructure.GPIO_Pin = LED0_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;         // 输出模式
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;        // 推挽输出
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;    // 高速
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;          // 上拉
    GPIO_Init(LED0_PORT, &GPIO_InitStructure);
    
    // 初始状态：LED熄灭
    LED0_OFF();
}

/**
 * @brief  LED0切换状态函数
 * @param  None
 * @retval None
 * @note   每次调用切换LED0的亮灭状态
 */
void LED0_Toggle(void)
{
    if(GPIO_ReadOutputDataBit(LED0_PORT, LED0_PIN) == RESET) {
        LED0_OFF();  // 如果当前是亮的，则熄灭
    } else {
        LED0_ON();   // 如果当前是灭的，则点亮
    }
}

/**
 * @brief  LED0设置状态函数
 * @param  state: 0-熄灭, 1-点亮
 * @retval None
 * @note   直接设置LED0的状态
 */
void LED0_Set(uint8_t state)
{
    if(state) {
        LED0_ON();   // 点亮LED0
    } else {
        LED0_OFF();  // 熄灭LED0
    }
}