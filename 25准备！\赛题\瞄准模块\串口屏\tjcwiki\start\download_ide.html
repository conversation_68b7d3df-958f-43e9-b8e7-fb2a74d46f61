<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>下载和安装上位机软件 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="上位机基本功能介绍" href="ide_introduce/index.html" />
    <link rel="prev" title="快速入门" href="index.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">快速入门</a><ul class="current">
<li class="toctree-l2 current"><a class="current reference internal" href="#">下载和安装上位机软件</a></li>
<li class="toctree-l2"><a class="reference internal" href="ide_introduce/index.html">上位机基本功能介绍</a></li>
<li class="toctree-l2"><a class="reference internal" href="first_test.html">到手测试</a></li>
<li class="toctree-l2"><a class="reference internal" href="create_project/index.html">创建工程</a></li>
<li class="toctree-l2"><a class="reference internal" href="codeSpace.html">代码编写</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">快速入门</a> &raquo;</li>
      <li>下载和安装上位机软件</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <div class="toctree-wrapper compound">
</div>
<section id="id1">
<h1>下载和安装上位机软件<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<p>1.下载上位机软件USART HMI</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/USARTHMIsetup_latest.zip">点击此处下载上位机开发软件</a></p>
<hr class="docutils" />
<p>2.右键解压下载的压缩包，得到exe格式的可执行文件，右键点击安装</p>
<img alt="../_images/install1.jpg" src="../_images/install1.jpg" />
<hr class="docutils" />
<p>3.点击更多信息</p>
<img alt="../_images/install2.jpg" src="../_images/install2.jpg" />
<hr class="docutils" />
<p>4.点击仍要运行</p>
<img alt="../_images/install3.jpg" src="../_images/install3.jpg" />
<hr class="docutils" />
<p>5.点击Next</p>
<img alt="../_images/install4.jpg" src="../_images/install4.jpg" />
<hr class="docutils" />
<p>6.此处建议使用默认的安装地址即可，点击Next</p>
<img alt="../_images/install5.jpg" src="../_images/install5.jpg" />
<hr class="docutils" />
<p>7.点击Install</p>
<img alt="../_images/install6.jpg" src="../_images/install6.jpg" />
<hr class="docutils" />
<p>8.等待程序安装完成</p>
<img alt="../_images/install7.jpg" src="../_images/install7.jpg" />
<hr class="docutils" />
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>win10安装.NET Framework 3.5目前需要联网，网上的离线安装包大部分都无法成功安装，联网在线安装是最快最稳定的方法</p>
</div>
<p>9.部分电脑因为没有.NET Framework 3.5 环境，需要联网安装.NET Framework 3.5 环境，点击下载并安装此功能</p>
<img alt="../_images/install8.jpg" src="../_images/install8.jpg" />
<p>如果需要离线安装请查看</p>
<p><a class="reference internal" href="../download/tools_download.html#frameworkdownload"><span class="std std-ref">Microsoft .net Framework 3.5</span></a></p>
<hr class="docutils" />
<p>10.等待程序自动下载并安装</p>
<img alt="../_images/install9.jpg" src="../_images/install9.jpg" />
<hr class="docutils" />
<p>11.安装完毕点击关闭</p>
<img alt="../_images/install10.jpg" src="../_images/install10.jpg" />
<hr class="docutils" />
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="快速入门" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="ide_introduce/index.html" class="btn btn-neutral float-right" title="上位机基本功能介绍" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>