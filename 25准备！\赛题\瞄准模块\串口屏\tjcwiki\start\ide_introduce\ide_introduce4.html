<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>4.资源文件窗口 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
        <script src="../../_static/jquery.js"></script>
        <script src="../../_static/underscore.js"></script>
        <script src="../../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../../_static/doctools.js"></script>
        <script src="../../_static/translations.js"></script>
        <script src="../../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../../_static/custom.js"></script>
    <script src="../../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../../genindex.html" />
    <link rel="search" title="搜索" href="../../search.html" />
    <link rel="next" title="5.界面" href="ide_introduce5.html" />
    <link rel="prev" title="3.工具箱" href="ide_introduce3.html" />
    <link href="../../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="../index.html">快速入门</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../download_ide.html">下载和安装上位机软件</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html">上位机基本功能介绍</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="ide_introduce1.html">1.菜单栏</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce2.html">2.工具栏</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce3.html">3.工具箱</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">4.资源文件窗口</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id2">图片资源</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id3">图片资源右键菜单</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id4">字库资源</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id5">字库资源右键菜单</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id6">动画资源</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id7">动画资源右键菜单</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id8">视频资源</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id9">视频资源右键菜单</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id10">音频资源</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id11">音频资源右键菜单</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce5.html">5.界面</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce6.html">6.特殊控件窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce7.html">7.输出窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce8.html">8.事件编辑窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce9.html">9.页面窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce10.html">10.属性窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce11.html">11.调试窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce12.html">12.下载窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce13.html">13.设备</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../first_test.html">到手测试</a></li>
<li class="toctree-l2"><a class="reference internal" href="../create_project/index.html">创建工程</a></li>
<li class="toctree-l2"><a class="reference internal" href="../codeSpace.html">代码编写</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="../index.html">快速入门</a> &raquo;</li>
          <li><a href="index.html">上位机基本功能介绍</a> &raquo;</li>
      <li>4.资源文件窗口</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>4.资源文件窗口<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<img alt="../../_images/sourceBar1.png" src="../../_images/sourceBar1.png" />
<p>资源文件窗口存放工程所调用到资源。其中动画，音频仅x系列支持，视频仅x5系列支持。</p>
<section id="id2">
<h2>图片资源<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<p>图片资源：t0 t1系列支持jpg bmp； k0系列支持jpg bmp；x系列支持jpg png gif bmp；</p>
<img alt="../../_images/sourceBar2.png" src="../../_images/sourceBar2.png" />
<p>如图所示,可以使用相关按钮进行图片添加，删除，替换，插入，上移，下移，全删。</p>
<img alt="../../_images/sourceBar3.png" src="../../_images/sourceBar3.png" />
<dl class="simple">
<dt>1.图片id</dt><dd><p>控件背景调用图片使用到的id。</p>
</dd>
</dl>
<p>2.图片分辨率</p>
<blockquote>
<div><p>最小图片分辨率为： 2 * 2像素</p>
<p>最大图片分辨率为当前屏幕的分辨率，例如TJC8048X550_011C，分辨率为 800 * 480，则最大的图片分辨率也是 800 * 480，也就是全屏图片</p>
</div></blockquote>
<dl class="simple">
<dt>3.图片压缩质量</dt><dd><p>压缩质量越大，图片显示效果越好，但是占用的空间会更大，x5,x3,t1系列支持图片压缩储存，k0,t0系列不支持图片压缩储存</p>
</dd>
<dt>4.图片大小</dt><dd><p>导入后的图片占用的空间可能变大也有可能变小</p>
</dd>
<dt>5.图片格式</dt><dd><p>导入图片的图片格式。</p>
</dd>
</dl>
</section>
<section id="id3">
<h2>图片资源右键菜单<a class="headerlink" href="#id3" title="此标题的永久链接"></a></h2>
<img alt="../../_images/sourceBar12.png" src="../../_images/sourceBar12.png" />
<p>1~6为添加，删除，插入，替换，导出，全删</p>
<dl class="simple">
<dt>7.压缩质量设置</dt><dd><p>仅x3、x5系列支持，当导入图片后该图片占用的存储空间大于400KB时，请调低压缩质量，否则可能导致图片无法显示。</p>
</dd>
<dt>8.允许透明像素</dt><dd><p>仅x系列支持。</p>
</dd>
<dt>9.不允许透明像素</dt><dd><p>仅x系列支持。</p>
</dd>
<dt>10.全部选中</dt><dd><p>选择所有图片。</p>
</dd>
</dl>
</section>
<section id="id4">
<h2>字库资源<a class="headerlink" href="#id4" title="此标题的永久链接"></a></h2>
<div class="admonition tip">
<p class="admonition-title">小技巧</p>
<p>字库：可以通过工具菜单中的《字库制作》工具制作</p>
</div>
<img alt="../../_images/sourceBar4.png" src="../../_images/sourceBar4.png" />
<p>如图所示,可以使用相关按钮进行字库添加，删除，替换，插入，上移，下移，全删。</p>
<img alt="../../_images/sourceBar5.png" src="../../_images/sourceBar5.png" />
<dl class="simple">
<dt>1.字库id</dt><dd><p>控件字库调用字库使用到的id。</p>
</dd>
<dt>2.字库名称</dt><dd><p>生成字库的时候进行命名。</p>
</dd>
<dt>3.字体大小</dt><dd><p>该字库的字体大小。</p>
</dd>
<dt>4.字体字符编码</dt><dd><p>该字库使用的字符编码。</p>
</dd>
<dt>5.字符数量</dt><dd><p>字库所包含的字符数量。</p>
</dd>
<dt>6.flash空间</dt><dd><p>字库所占flash空间大小。</p>
</dd>
</dl>
</section>
<section id="id5">
<h2>字库资源右键菜单<a class="headerlink" href="#id5" title="此标题的永久链接"></a></h2>
<img alt="../../_images/sourceBar13.png" src="../../_images/sourceBar13.png" />
<p>1~6为添加，删除，插入，替换，导出，全删</p>
<dl>
<dt>7.全部选中</dt><dd><p>选择所有字库。</p>
</dd>
<dt>参考：</dt><dd><p><a class="reference internal" href="../create_project/create_project_5.html#id1"><span class="std std-ref">创建字库和导入字库</span></a></p>
<p><a class="reference internal" href="../../QA/QA8.html#id1"><span class="std std-ref">指定字库</span></a></p>
<p><a class="reference internal" href="../../QA/QA29.html#id1"><span class="std std-ref">在做字库的时候有部分字体无法选择</span></a></p>
<p><a class="reference internal" href="../../QA/QA9.html#xxx"><span class="std std-ref">编译报错：XXX初始值无效</span></a></p>
<p><a class="reference internal" href="../../QA/QA76.html#utf8"><span class="std std-ref">utf8字库下如何只选择汉字</span></a></p>
<p><a class="reference internal" href="../../QA/QA44.html#file-is-too-large-for-destination-device"><span class="std std-ref">报错:file is too large for destination device</span></a></p>
</dd>
</dl>
</section>
<section id="id6">
<h2>动画资源<a class="headerlink" href="#id6" title="此标题的永久链接"></a></h2>
<p>动画：可以通过工具菜单中的动画制作，仅x3和x5系列支持</p>
<img alt="../../_images/sourceBar6.png" src="../../_images/sourceBar6.png" />
<p>如图所示,可以使用相关按钮进行动画添加，删除，替换，插入，上移，下移，全删。</p>
<img alt="../../_images/sourceBar7.png" src="../../_images/sourceBar7.png" />
<p>1.动画id，动画控件调用动画使用到的id。</p>
<p>2.动画分辨率，导入动画分辨率超出屏幕显示支持最大分辨率将会报错。</p>
<p>3.动画时长。</p>
<p>4.动画压缩质量。</p>
<p>5.动画帧数。</p>
<p>6.动画所占flash空间大小，转换后的动画占用的空间可能变大也有可能变小。</p>
</section>
<section id="id7">
<h2>动画资源右键菜单<a class="headerlink" href="#id7" title="此标题的永久链接"></a></h2>
<img alt="../../_images/sourceBar14.png" src="../../_images/sourceBar14.png" />
<p>1~6为添加，删除，插入，替换，导出，全删</p>
<dl class="simple">
<dt>7.名称设置</dt><dd><p>相当于设置备注。</p>
</dd>
<dt>8.压缩质量设置</dt><dd><p>设置压缩质量，当文件过大时，降低压缩质量可以有效降低文件体积，但是会降低动画的显示效果。</p>
</dd>
<dt>9.允许透明像素</dt><dd><p>仅x系列支持。</p>
</dd>
<dt>10.不允许透明像素</dt><dd><p>仅x系列支持。</p>
</dd>
<dt>11.全部选中</dt><dd><p>选择所有动画。</p>
</dd>
</dl>
</section>
<section id="id8">
<h2>视频资源<a class="headerlink" href="#id8" title="此标题的永久链接"></a></h2>
<div class="admonition tip">
<p class="admonition-title">小技巧</p>
<p>视频：可以通过工具菜单中的视频/音频转换</p>
</div>
<img alt="../../_images/sourceBar8.png" src="../../_images/sourceBar8.png" />
<p>如图所示,可以使用相关按钮进行视频添加，删除，替换，插入，上移，下移，全删。</p>
<img alt="../../_images/sourceBar9.png" src="../../_images/sourceBar9.png" />
<p>1.视频id，视频控件调用视频使用到的id。</p>
<p>2.视频分辨率，导入动画分辨率超出屏幕显示支持最大分辨率将会报错。</p>
<p>3.视频时长。</p>
<p>4.视频fps。</p>
<p>5.视频所占flash空间大小，转换后的视频占用的空间可能变大也有可能变小。</p>
</section>
<section id="id9">
<h2>视频资源右键菜单<a class="headerlink" href="#id9" title="此标题的永久链接"></a></h2>
<img alt="../../_images/sourceBar15.png" src="../../_images/sourceBar15.png" />
<p>1~6为添加，删除，插入，替换，导出，全删</p>
<dl class="simple">
<dt>7.名称设置</dt><dd><p>相当于设置备注。</p>
</dd>
<dt>8.全部选中</dt><dd><p>选择所有视频。</p>
</dd>
</dl>
</section>
<section id="id10">
<h2>音频资源<a class="headerlink" href="#id10" title="此标题的永久链接"></a></h2>
<div class="admonition tip">
<p class="admonition-title">小技巧</p>
<p>音频：可以通过工具菜单中的《视频/音频》转换</p>
</div>
<img alt="../../_images/sourceBar10.png" src="../../_images/sourceBar10.png" />
<p>如图所示,可以使用相关按钮进行音频添加，删除，替换，插入，上移，下移，全删。</p>
<img alt="../../_images/sourceBar11.png" src="../../_images/sourceBar11.png" />
<p>1.音频id，音频控件调用音频使用到的id。</p>
<p>2.音频名称，生成音频的时候进行命名。</p>
<p>3.音频采样率</p>
<p>4.音频时长。</p>
<p>5.音频所占flash空间大小，转换入后的音频占用的空间可能变大也有可能变小。</p>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>音频格式必须用上位机自带的《视频/音频转换》转换为wav格式。</p>
</div>
</section>
<section id="id11">
<h2>音频资源右键菜单<a class="headerlink" href="#id11" title="此标题的永久链接"></a></h2>
<img alt="../../_images/sourceBar16.png" src="../../_images/sourceBar16.png" />
<p>1~6为添加，删除，插入，替换，导出，全删</p>
<dl class="simple">
<dt>7.名称设置</dt><dd><p>相当于设置备注。</p>
</dd>
<dt>8.全部选中</dt><dd><p>选择所有音频。</p>
</dd>
<dt>9.音频压缩</dt><dd><p>修改音频的采样率。</p>
</dd>
</dl>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="ide_introduce3.html" class="btn btn-neutral float-left" title="3.工具箱" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="ide_introduce5.html" class="btn btn-neutral float-right" title="5.界面" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>