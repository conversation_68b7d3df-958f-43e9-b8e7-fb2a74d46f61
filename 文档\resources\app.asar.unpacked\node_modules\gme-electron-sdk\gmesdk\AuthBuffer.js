"use strict";
exports.__esModule = true;

//全局定义部分
var SALT_LEN = 2;
var ZERO_LEN = 7;
var DELTA = 0x9e3779b9;
var ROUNDS = 16;
var LOG_ROUNDS = 4;

const AuthBufferService = function(sdkappid, roomID, openID, secKey) {
  if (typeof sdkappid === 'undefined') {
    process.exit(1);
  }
  this.sdkappid = sdkappid;
  this.roomId = roomID;
  this.openID = openID;
  this.secKey = secKey;

  this.EncrpySignature = Buffer.alloc(512);
  this.EncrpySignatureValidLength = 0;
};

/**
 * uin8
 * @returns {number}
 */
function randomUint8() {
  var cRand = Math.ceil(Math.random() * 255);
  //cRand = 93;
  return cRand;
}

/**
 * @return {number}
 */
function MakeInt2Uint(intValue) {
  if (intValue < 0) {
    return intValue + 0xffffffff + 1;
  }
  if (intValue > 0xffffffff) {
    return intValue - 0xffffffff - 1;
  }
  return intValue;
}

var BufferAppand = function(buf1, buf2) {
  var tmp = new Buffer(buf1.length + buf2.length);
  buf1.copy(tmp, 0);
  buf2.copy(tmp, buf1.length);
  return tmp;
};

AuthBufferService.prototype.OI_TeaEncryptECB_1 = function(pInBuf, pKey) {
  //console.log(pInBuf);
  let y = pInBuf.readUInt32BE(0);
  let z = pInBuf.readUInt32BE(4);

  //4个Int型的数组
  let k0 = pKey.readUInt32BE(0);
  let k1 = pKey.readUInt32BE(4);
  let k2 = pKey.readUInt32BE(8);
  let k3 = pKey.readUInt32BE(12);

  let sum = 0;
  for (var i = 0; i < ROUNDS; i++) {
    sum += DELTA;
    let yMargin = MakeInt2Uint(((z << 4) + k0) ^ (z + sum) ^ ((z >>> 5) + k1));
    y = MakeInt2Uint(y + yMargin);
    //console.log(y);

    let zMargin = MakeInt2Uint(((y << 4) + k2) ^ (y + sum) ^ ((y >>> 5) + k3));
    z = MakeInt2Uint(z + zMargin);
    //console.log(z);
  }

  var bufferRet = Buffer.alloc(8);
  bufferRet.writeUInt32BE(y);
  bufferRet.writeUInt32BE(z, 4);
  return bufferRet;
};

AuthBufferService.prototype.getEncrpyBuffer = function() {
  //过期时间, 默认是永远不过期
  var timeStampExp = 2100000000;

  const nTotalLength = 25 + this.openID.length + this.roomId.length;
  let buf = Buffer.alloc(nTotalLength);
  let nOffset = 0;

  //cVer
  buf.writeUInt8(1, nOffset);
  nOffset += 1;

  //wOpenIDLen
  buf.writeUInt16BE(this.openID.length, nOffset);
  nOffset += 2;

  //strOpenID
  buf.fill(Buffer.from(this.openID), nOffset, nOffset + this.openID.length, 'utf-8');
  nOffset += this.openID.length;

  //dwSdkAppId
  buf.writeUInt32BE(this.sdkappid, nOffset);
  nOffset += 4;

  //dwReserve, 历史上是存放房间ID
  buf.writeUInt32BE(0, nOffset);
  nOffset += 4;

  //expTime
  buf.writeUInt32BE(timeStampExp, nOffset);
  nOffset += 4;

  //nAuthBits
  buf.writeUInt32BE(0xffffffff, nOffset);
  nOffset += 4;

  //dwReserve
  buf.writeUInt32BE(0, nOffset);
  nOffset += 4;

  //wRoomIDLength
  buf.writeInt16BE(this.roomId.length, nOffset);
  nOffset += 2;

  //strRoomID
  buf.fill(Buffer.from(this.roomId), nOffset, nOffset + this.roomId.length, 'utf-8');
  nOffset += this.roomId.length;

  if (nOffset !== nOffset) {
    //拼接完成, 检查长度
    return null;
  }

  //console.log(buf);
  return buf;
};

AuthBufferService.prototype.encrypt = function(bufferToEncrpy, authKey) {
  /*PadLen(1byte)+Salt+Body+Zero的长度 */
  let nInBufLen = bufferToEncrpy.length;
  let nPadSaltBodyZeroLen =
    nInBufLen /*Body长度 */ + 1 + SALT_LEN + ZERO_LEN; /*PadLen(1byte)+Salt(2byte)+Zero(7byte) */
  let nPadlen = nPadSaltBodyZeroLen % 8; /*len=nSaltBodyZeroLen%8 */
  if (nPadlen !== 0) {
    /*模8余0需补0,余1补7,余2补6,...,余7补1 */
    nPadlen = 8 - nPadlen;
  }

  var bufferEncrpyFinal = Buffer.alloc(0);
  let src_buf = Buffer.alloc(8);
  let cRand = randomUint8();

  /*加密第一块数据(8byte),取前面10byte */
  let varFirst = (cRand & 0x0f8) /*最低三位存PadLen,清零 */ | nPadlen;
  src_buf.writeUInt8(varFirst);

  let src_i = 1; /*src_i指向src_buf下一个位置 */
  while (nPadlen--) {
    src_buf[src_i++] = randomUint8(); /*Padding */
  }

  //console.log(src_buf);

  let i = 0;
  let zero_iv = Buffer.alloc(8);
  let iv_buf = Buffer.alloc(8);

  for (let i = 1; i <= SALT_LEN /*Salt(2byte) */; ) {
    if (src_i < 8) {
      src_buf[src_i++] = randomUint8();
      i++; /*i inc in here */
    }

    if (src_i === 8) {
      for (let j = 0; j < 8; j++ /*CBC XOR */) {
        src_buf[j] ^= iv_buf[j];
      }

      let bufT = this.OI_TeaEncryptECB_1(src_buf, authKey);
      //console.log(bufT);
      bufferEncrpyFinal = BufferAppand(bufferEncrpyFinal, bufT);
      //console.log(bufferEncrpyFinal);
      iv_buf = bufT;
      src_i = 0; /*dest_i指向dest_buf下一个位置 */
    }
  }

  let pInBufIndex = 0;
  while (nInBufLen) {
    if (src_i < 8) {
      src_buf[src_i++] = bufferToEncrpy[pInBufIndex++];
      nInBufLen--;
    }

    if (src_i === 8) {
      /*src_i==8 */

      for (i = 0; i < 8; i++ /*CBC XOR */) src_buf[i] ^= iv_buf[i];
      /*pOutBuffer、pInBuffer均为8byte, pKey为16byte */
      let bufT = this.OI_TeaEncryptECB_1(src_buf, authKey);
      src_i = 0;
      bufferEncrpyFinal = BufferAppand(bufferEncrpyFinal, bufT);
      //console.log(bufferEncrpyFinal)
      iv_buf = bufT;
    }
  }

  /*src_i指向src_buf下一个位置 */

  for (let i = 1; i <= ZERO_LEN; ) {
    if (src_i < 8) {
      src_buf[src_i++] = 0;
      i++; /*i inc in here */
    }

    if (src_i === 8) {
      /*src_i==8 */

      for (let j = 0; j < 8; j++ /*CBC XOR */) {
        src_buf[j] ^= iv_buf[j];
      }
      /*pOutBuffer、pInBuffer均为8byte, pKey为16byte */
      let bufT = this.OI_TeaEncryptECB_1(src_buf, authKey);
      bufferEncrpyFinal = BufferAppand(bufferEncrpyFinal, bufT);
      src_i = 0;
      iv_buf = bufT;
    }
  }

  return bufferEncrpyFinal;
};

AuthBufferService.prototype.getSignature = function() {
  if (this.openID.toString() === '') {
    return null;
  }

  if (this.roomId.toString() === '') {
    return null;
  }

  if (this.secKey.toString().length !== 16) {
    return null;
  }

  let encrpyBuffer = this.getEncrpyBuffer();
  if (null == encrpyBuffer) {
    return null;
  }

  let authKey = Buffer.from(this.secKey, 'utf-8');

  var signature = this.encrypt(encrpyBuffer, authKey);
  console.log(signature);
  return signature.toString('base64');
};
exports["AuthBufferService"] = AuthBufferService;
