#include "adc.h"

/**
 * @brief  ADC1初始化函数
 * @param  None
 * @retval None
 * @note   配置PC0为模拟输入，初始化ADC1为12位单通道转换模式
 */
void ADC1_Init(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	ADC_InitTypeDef ADC_InitStructure;
	ADC_CommonInitTypeDef ADC_CommonInitStructure;
	
	/* 使能相关时钟 */
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOC, ENABLE);  // GPIOC时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_ADC1, ENABLE);   // ADC1时钟
	
	/* 配置PC0为模拟输入模式 */
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;        // 模拟输入模式
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0;           // PC0引脚
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;   // 无上下拉电阻
	GPIO_Init(GPIOC, &GPIO_InitStructure);
	
	/* ADC通用参数配置 */
	ADC_CommonInitStructure.ADC_Mode = ADC_Mode_Independent;                // 独立模式
	ADC_CommonInitStructure.ADC_Prescaler = ADC_Prescaler_Div4;             // 4分频 (21MHz)
	ADC_CommonInitStructure.ADC_DMAAccessMode = ADC_DMAAccessMode_Disabled; // 禁用DMA
	ADC_CommonInitStructure.ADC_TwoSamplingDelay = ADC_TwoSamplingDelay_5Cycles; // 采样延时
	ADC_CommonInit(&ADC_CommonInitStructure);
	
	/* ADC1具体参数配置 */
	ADC_InitStructure.ADC_Resolution = ADC_Resolution_12b;                  // 12位分辨率
	ADC_InitStructure.ADC_ScanConvMode = DISABLE;                           // 禁用扫描模式
	ADC_InitStructure.ADC_ContinuousConvMode = DISABLE;                     // 单次转换模式
	ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_T1_CC1;   // 外部触发源
	ADC_InitStructure.ADC_ExternalTrigConvEdge = ADC_ExternalTrigConvEdge_None; // 无外部触发
	ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;                  // 数据右对齐
	ADC_InitStructure.ADC_NbrOfConversion = 1;                              // 转换通道数量
	ADC_Init(ADC1, &ADC_InitStructure);
	
	/* 使能ADC1 */
	ADC_Cmd(ADC1, ENABLE);
}

/**
 * @brief  获取指定通道的单次ADC转换值
 * @param  ch: ADC通道号 (0-15)
 * @retval ADC转换结果 (0-4095)
 * @note   内部函数，配置指定通道并启动单次转换
 *         采样时间设置为480个周期，提高转换精度
 */
u16 Get_Adc1(u8 ch)
{
	/* 配置要转换的通道 */
	ADC_RegularChannelConfig(ADC1, ch, 1, ADC_SampleTime_480Cycles);
	
	/* 启动软件转换 */
	ADC_SoftwareStartConv(ADC1);
	
	/* 等待转换完成 */
	while(!(ADC_GetFlagStatus(ADC1, ADC_FLAG_EOC)));
	
	/* 返回转换结果 */
	return ADC_GetConversionValue(ADC1);
}

/**
 * @brief  获取ADC多次采样的平均值
 * @param  ch: ADC通道号 (对于PC0应该传入10)
 * @param  count: 采样次数 (建议10-50次)
 * @retval ADC转换结果的平均值 (0-4095)
 * @note   通过多次采样求平均值来减少随机噪声影响
 *         每次采样间隔5ms，总耗时 = count * 5ms
 */
u16 Get_adc_Average(u8 ch, u8 count)
{
	u32 temp_val = 0;  // 累加值，使用32位防止溢出
	u8 i;
	
	/* 连续采样指定次数 */
	for(i=0; i<count; i++)
	{
		temp_val += Get_Adc1(ch);  // 累加每次采样值
		delay_ms(5);               // 延时5ms，减少连续采样间的干扰
	}
	
	/* 返回平均值 */
	return temp_val/count;
}
