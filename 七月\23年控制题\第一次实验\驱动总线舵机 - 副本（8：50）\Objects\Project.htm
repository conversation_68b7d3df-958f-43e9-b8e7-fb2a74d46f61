<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\Project.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\Project.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060528: Last Updated: Sat Jul 19 08:47:59 2025
<BR><P>
<H3>Maximum Stack Usage =       1544 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
__rt_entry_main &rArr; main &rArr; StateMachine_Update &rArr; StateMachine_HandleRecordKey &rArr; StateMachine_RecordPointB &rArr; StateMachine_TransitionTo &rArr; StateMachine_StartAutoMovement &rArr; Bluetooth_SendKeyAction &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[73]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[20]">ADC1_2_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[20]">ADC1_2_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[20]">ADC1_2_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[8]">BusFault_Handler</a> from stm32f10x_it.o(i.BusFault_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[23]">CAN1_RX1_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[24]">CAN1_SCE_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[19]">DMA1_Channel1_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[1a]">DMA1_Channel2_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[1b]">DMA1_Channel3_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[1c]">DMA1_Channel4_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[1d]">DMA1_Channel5_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[1e]">DMA1_Channel6_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[1f]">DMA1_Channel7_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[b]">DebugMon_Handler</a> from stm32f10x_it.o(i.DebugMon_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[14]">EXTI0_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[36]">EXTI15_10_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[15]">EXTI1_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[16]">EXTI2_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[17]">EXTI3_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[18]">EXTI4_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[25]">EXTI9_5_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[12]">FLASH_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[6]">HardFault_Handler</a> from stm32f10x_it.o(i.HardFault_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[2e]">I2C1_ER_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[2d]">I2C1_EV_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[30]">I2C2_ER_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[2f]">I2C2_EV_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[7]">MemManage_Handler</a> from stm32f10x_it.o(i.MemManage_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[5]">NMI_Handler</a> from stm32f10x_it.o(i.NMI_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[f]">PVD_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[c]">PendSV_Handler</a> from stm32f10x_it.o(i.PendSV_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[13]">RCC_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[37]">RTCAlarm_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[11]">RTC_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[4]">Reset_Handler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[31]">SPI1_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[32]">SPI2_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[a]">SVC_Handler</a> from stm32f10x_it.o(i.SVC_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[d]">SysTick_Handler</a> from stm32f10x_it.o(i.SysTick_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[39]">SystemInit</a> from system_stm32f10x.o(i.SystemInit) referenced from startup_stm32f10x_md.o(.text)
 <LI><a href="#[10]">TAMPER_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[26]">TIM1_BRK_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[29]">TIM1_CC_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[28]">TIM1_TRG_COM_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[27]">TIM1_UP_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[2a]">TIM2_IRQHandler</a> from stm32f10x_it.o(i.TIM2_IRQHandler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[2b]">TIM3_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[2c]">TIM4_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[33]">USART1_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[34]">USART2_IRQHandler</a> from bluetooth.o(i.USART2_IRQHandler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[35]">USART3_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[38]">USBWakeUp_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[21]">USB_HP_CAN1_TX_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[22]">USB_LP_CAN1_RX0_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[9]">UsageFault_Handler</a> from stm32f10x_it.o(i.UsageFault_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[e]">WWDG_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[3d]">__main</a> from __main.o(!!!main) referenced from startup_stm32f10x_md.o(.text)
 <LI><a href="#[3c]">_printf_input_char</a> from _printf_char_common.o(.text) referenced from _printf_char_common.o(.text)
 <LI><a href="#[3b]">_sputc</a> from _sputc.o(.text) referenced from noretval__2sprintf.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[3d]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[3e]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[40]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[164]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[165]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[41]"></a>__scatterload_copy</STRONG> (Thumb, 26 bytes, Stack size unknown bytes, __scatter_copy.o(!!handler_copy), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>

<P><STRONG><a name="[166]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[42]"></a>_printf_f</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_f.o(.ARM.Collect$$_printf_percent$$00000003))
<BR><BR>[Stack]<UL><LI>Max Depth = 324 + Unknown Stack Size
<LI>Call Chain = _printf_f &rArr; _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[5f]"></a>_printf_percent</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[44]"></a>_printf_d</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_d.o(.ARM.Collect$$_printf_percent$$00000009))
<BR><BR>[Stack]<UL><LI>Max Depth = 56 + Unknown Stack Size
<LI>Call Chain = _printf_d &rArr; _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[46]"></a>_printf_u</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A))
<BR><BR>[Stack]<UL><LI>Max Depth = 56 + Unknown Stack Size
<LI>Call Chain = _printf_u &rArr; _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[47]"></a>_printf_c</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_c.o(.ARM.Collect$$_printf_percent$$00000013))
<BR><BR>[Stack]<UL><LI>Max Depth = 24 + Unknown Stack Size
<LI>Call Chain = _printf_c &rArr; _printf_char &rArr; _printf_cs_common &rArr; _printf_str
</UL>
<BR>[Calls]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char
</UL>

<P><STRONG><a name="[49]"></a>_printf_s</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_s.o(.ARM.Collect$$_printf_percent$$00000014))
<BR><BR>[Stack]<UL><LI>Max Depth = 24 + Unknown Stack Size
<LI>Call Chain = _printf_s &rArr; _printf_string &rArr; _printf_cs_common &rArr; _printf_str
</UL>
<BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
</UL>

<P><STRONG><a name="[167]"></a>_printf_percent_end</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017))

<P><STRONG><a name="[52]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[168]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000002))

<P><STRONG><a name="[169]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[4b]"></a>__rt_lib_init_lc_common</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000F))
<BR><BR>[Calls]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
</UL>

<P><STRONG><a name="[16a]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[16b]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[16c]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[16d]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[16e]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[16f]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[4d]"></a>__rt_lib_init_lc_numeric_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000016))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_lc_numeric_2 &rArr; _get_lc_numeric
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_numeric
</UL>

<P><STRONG><a name="[170]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[171]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[172]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[173]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[174]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[175]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[176]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[177]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[178]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[179]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[17a]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[17b]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[17c]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[57]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[17d]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[17e]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000007))

<P><STRONG><a name="[17f]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[180]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000010))

<P><STRONG><a name="[181]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A))

<P><STRONG><a name="[182]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[183]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[3f]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[184]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[4f]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[51]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[185]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[53]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 1544 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; StateMachine_Update &rArr; StateMachine_HandleRecordKey &rArr; StateMachine_RecordPointB &rArr; StateMachine_TransitionTo &rArr; StateMachine_StartAutoMovement &rArr; Bluetooth_SendKeyAction &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[186]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[74]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[56]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[187]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[58]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[4]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>ADC1_2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>RTCAlarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>TAMPER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM1_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM1_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM1_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>USBWakeUp_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>USB_HP_CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>USB_LP_CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[73]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_stm32f10x_md.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[5a]"></a>__2sprintf</STRONG> (Thumb, 34 bytes, Stack size 32 bytes, noretval__2sprintf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128 + Unknown Stack Size
<LI>Call Chain = __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendKeyAction
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointB
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointA
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SetPin
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SetName
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendStartupInfo
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendErrorReport
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_PrintConnectionInfo
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_ShowStatus
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_ShowPerformance
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_ShowErrors
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_RunFullCheck
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_ShowStatus
</UL>

<P><STRONG><a name="[6f]"></a>_printf_str</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, _printf_str.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_str
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>

<P><STRONG><a name="[45]"></a>_printf_int_dec</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, _printf_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_u
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_d
</UL>

<P><STRONG><a name="[5d]"></a>__printf</STRONG> (Thumb, 352 bytes, Stack size 32 bytes, __printf_ss_wp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32 + Unknown Stack Size
<LI>Call Chain = __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_percent
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>

<P><STRONG><a name="[ad]"></a>strstr</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, strstr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_ParseCommand
</UL>

<P><STRONG><a name="[13d]"></a>strcpy</STRONG> (Thumb, 72 bytes, Stack size 12 bytes, strcpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[13b]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[188]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[189]"></a>__rt_memcpy_w</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[18a]"></a>_memcpy_lastbytes_aligned</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[107]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_Init
</UL>

<P><STRONG><a name="[18b]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[18c]"></a>__rt_memclr_w</STRONG> (Thumb, 78 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[71]"></a>_memset_w</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memclr
</UL>

<P><STRONG><a name="[60]"></a>strncpy</STRONG> (Thumb, 86 bytes, Stack size 8 bytes, strncpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strncpy
</UL>
<BR>[Calls]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_ShowErrors
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_LogError
</UL>

<P><STRONG><a name="[18d]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[18e]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[18f]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[62]"></a>__read_errno</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, _rserrno.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>

<P><STRONG><a name="[64]"></a>__set_errno</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, _rserrno.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __set_errno
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tan
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrtf
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
</UL>

<P><STRONG><a name="[5c]"></a>_printf_int_common</STRONG> (Thumb, 178 bytes, Stack size 32 bytes, _printf_intcommon.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[190]"></a>__lib_sel_fp_printf</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, _printf_fp_dec.o(.text), UNUSED)

<P><STRONG><a name="[6b]"></a>_printf_fp_dec_real</STRONG> (Thumb, 620 bytes, Stack size 104 bytes, _printf_fp_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 324<LI>Call Chain = _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[5b]"></a>_printf_char_common</STRONG> (Thumb, 32 bytes, Stack size 64 bytes, _printf_char_common.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 96 + Unknown Stack Size
<LI>Call Chain = _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>
<BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>

<P><STRONG><a name="[3b]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _sputc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> noretval__2sprintf.o(.text)
</UL>
<P><STRONG><a name="[6e]"></a>_printf_cs_common</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _printf_cs_common &rArr; _printf_str
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char
</UL>

<P><STRONG><a name="[48]"></a>_printf_char</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _printf_char &rArr; _printf_cs_common &rArr; _printf_str
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_c
</UL>

<P><STRONG><a name="[4a]"></a>_printf_string</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _printf_string &rArr; _printf_cs_common &rArr; _printf_str
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_s
</UL>

<P><STRONG><a name="[61]"></a>__aeabi_memclr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memclr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
</UL>

<P><STRONG><a name="[70]"></a>__rt_memclr</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset_w
</UL>

<P><STRONG><a name="[191]"></a>_memset</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr.o(.text), UNUSED)

<P><STRONG><a name="[4c]"></a>__rt_locale</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_locale_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_common
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[63]"></a>__aeabi_errno_addr</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__read_errno
</UL>

<P><STRONG><a name="[192]"></a>__errno$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[193]"></a>__rt_errno_addr$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[6a]"></a>_ll_udiv10</STRONG> (Thumb, 138 bytes, Stack size 12 bytes, lludiv10.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = _ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[6d]"></a>_printf_fp_infnan</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, _printf_fp_infnan.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _printf_fp_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[66]"></a>_btod_etento</STRONG> (Thumb, 224 bytes, Stack size 72 bytes, bigflt0.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[194]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[72]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[195]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[50]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[55]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[144]"></a>strcmp</STRONG> (Thumb, 128 bytes, Stack size 0 bytes, strcmpv7m.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_numeric
</UL>

<P><STRONG><a name="[59]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[196]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[197]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[198]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[67]"></a>_btod_d2e</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e))
<BR><BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_norm_op1
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[76]"></a>_d2e_denorm_low</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e_denorm_low))
<BR><BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_norm_op1
</UL>

<P><STRONG><a name="[75]"></a>_d2e_norm_op1</STRONG> (Thumb, 96 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e_norm_op1))
<BR><BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_denorm_low
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_d2e
</UL>

<P><STRONG><a name="[77]"></a>__btod_div_common</STRONG> (Thumb, 696 bytes, Stack size 24 bytes, btod.o(CL$$btod_div_common))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __btod_div_common
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>

<P><STRONG><a name="[78]"></a>_e2e</STRONG> (Thumb, 220 bytes, Stack size 24 bytes, btod.o(CL$$btod_e2e))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _e2e
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>

<P><STRONG><a name="[68]"></a>_btod_ediv</STRONG> (Thumb, 42 bytes, Stack size 28 bytes, btod.o(CL$$btod_ediv))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = _btod_ediv &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_e2e
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__btod_div_common
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[69]"></a>_btod_emul</STRONG> (Thumb, 42 bytes, Stack size 28 bytes, btod.o(CL$$btod_emul))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__btod_mult_common
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_e2e
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[79]"></a>__btod_mult_common</STRONG> (Thumb, 580 bytes, Stack size 16 bytes, btod.o(CL$$btod_mult_common))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __btod_mult_common
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
</UL>

<P><STRONG><a name="[7a]"></a>AutoMovement_ArePointsValid</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, automovement.o(i.AutoMovement_ArePointsValid))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = AutoMovement_ArePointsValid &rArr; Geometry_CalculateDistance &rArr; sqrtf &rArr; _fsqrt &rArr; __fpl_fnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_IsWallPointValid
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_CalculateDistance
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfrcmple
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_Start
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_CheckSafety
</UL>

<P><STRONG><a name="[7f]"></a>AutoMovement_CheckSafety</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, automovement.o(i.AutoMovement_CheckSafety))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = AutoMovement_CheckSafety &rArr; AutoMovement_ArePointsValid &rArr; Geometry_CalculateDistance &rArr; sqrtf &rArr; _fsqrt &rArr; __fpl_fnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_ArePointsValid
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_Update
</UL>

<P><STRONG><a name="[80]"></a>AutoMovement_EmergencyStop</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, automovement.o(i.AutoMovement_EmergencyStop))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = AutoMovement_EmergencyStop &rArr; AutoMovement_TransitionTo &rArr; LED_ON
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_StopPathMovement
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_TransitionTo
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_Update
</UL>

<P><STRONG><a name="[83]"></a>AutoMovement_ExecuteNextStep</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, automovement.o(i.AutoMovement_ExecuteNextStep))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = AutoMovement_ExecuteNextStep &rArr; Path_GetNextStep &rArr; Path_LinearInterpolate &rArr; Geometry_ClampWallPoint &rArr; __aeabi_cfcmple
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_GetTick
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_GetNextStep
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_MoveToPosition
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_HandlePathMoving
</UL>

<P><STRONG><a name="[87]"></a>AutoMovement_GetProgress</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, automovement.o(i.AutoMovement_GetProgress))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = AutoMovement_GetProgress &rArr; __aeabi_fdiv
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_ShowStatus
</UL>

<P><STRONG><a name="[9b]"></a>AutoMovement_GetStateString</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, automovement.o(i.AutoMovement_GetStateString))
<BR><BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_ShowStatus
</UL>

<P><STRONG><a name="[8a]"></a>AutoMovement_HandleDirectionChange</STRONG> (Thumb, 108 bytes, Stack size 8 bytes, automovement.o(i.AutoMovement_HandleDirectionChange))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = AutoMovement_HandleDirectionChange &rArr; Path_SetDirection &rArr; Geometry_WallToServo &rArr; Geometry_ClampServoAngle &rArr; __aeabi_cfcmple
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_IsTimeout
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_SetDirection
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_TransitionTo
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_Stop
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_Update
</UL>

<P><STRONG><a name="[8e]"></a>AutoMovement_HandleError</STRONG> (Thumb, 110 bytes, Stack size 16 bytes, automovement.o(i.AutoMovement_HandleError))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = AutoMovement_HandleError &rArr; AutoMovement_TransitionTo &rArr; LED_ON
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_StopPathMovement
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_TransitionTo
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_Update
</UL>

<P><STRONG><a name="[8f]"></a>AutoMovement_HandleMovingToStart</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, automovement.o(i.AutoMovement_HandleMovingToStart))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = AutoMovement_HandleMovingToStart &rArr; AutoMovement_MoveToPosition &rArr; Servo_SetPositionWithTime &rArr; Servo_SendCommand &rArr; USART1_SendBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_IsTimeout
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_TransitionTo
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_MoveToPosition
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_Update
</UL>

<P><STRONG><a name="[90]"></a>AutoMovement_HandlePathMoving</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, automovement.o(i.AutoMovement_HandlePathMoving))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = AutoMovement_HandlePathMoving &rArr; AutoMovement_ExecuteNextStep &rArr; Path_GetNextStep &rArr; Path_LinearInterpolate &rArr; Geometry_ClampWallPoint &rArr; __aeabi_cfcmple
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_ResetPathStep
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_IsPathStepReady
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_IsComplete
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_TransitionTo
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_ExecuteNextStep
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_Update
</UL>

<P><STRONG><a name="[94]"></a>AutoMovement_HandlePreparing</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, automovement.o(i.AutoMovement_HandlePreparing))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = AutoMovement_HandlePreparing &rArr; Path_Initialize &rArr; Path_ValidatePath &rArr; Geometry_CalculateDistance &rArr; sqrtf &rArr; _fsqrt &rArr; __fpl_fnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_IsTimeout
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_SetDirection
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_Initialize
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_TransitionTo
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_Update
</UL>

<P><STRONG><a name="[137]"></a>AutoMovement_Init</STRONG> (Thumb, 88 bytes, Stack size 0 bytes, automovement.o(i.AutoMovement_Init))
<BR><BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[13a]"></a>AutoMovement_IsActive</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, automovement.o(i.AutoMovement_IsActive))
<BR><BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[96]"></a>AutoMovement_LoadPointsFromRecord</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, automovement.o(i.AutoMovement_LoadPointsFromRecord))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = AutoMovement_LoadPointsFromRecord &rArr; ManualRecord_LoadPointData
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ManualRecord_LoadPointData
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ManualRecord_IsPointSaved
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_Start
</UL>

<P><STRONG><a name="[85]"></a>AutoMovement_MoveToPosition</STRONG> (Thumb, 28 bytes, Stack size 24 bytes, automovement.o(i.AutoMovement_MoveToPosition))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = AutoMovement_MoveToPosition &rArr; Servo_SetPositionWithTime &rArr; Servo_SendCommand &rArr; USART1_SendBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_SetPositionWithTime
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_HandleMovingToStart
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_ExecuteNextStep
</UL>

<P><STRONG><a name="[9a]"></a>AutoMovement_ShowStatus</STRONG> (Thumb, 208 bytes, Stack size 104 bytes, automovement.o(i.AutoMovement_ShowStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 232 + Unknown Stack Size
<LI>Call Chain = AutoMovement_ShowStatus &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_GetTick
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_GetStateString
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_GetProgress
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_Update
</UL>

<P><STRONG><a name="[9f]"></a>AutoMovement_Start</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, automovement.o(i.AutoMovement_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = AutoMovement_Start &rArr; AutoMovement_ArePointsValid &rArr; Geometry_CalculateDistance &rArr; sqrtf &rArr; _fsqrt &rArr; __fpl_fnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_GetTick
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_TransitionTo
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_LoadPointsFromRecord
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_ArePointsValid
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8d]"></a>AutoMovement_Stop</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, automovement.o(i.AutoMovement_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = AutoMovement_Stop &rArr; AutoMovement_TransitionTo &rArr; LED_ON
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_TransitionTo
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_Update
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_HandleDirectionChange
</UL>

<P><STRONG><a name="[82]"></a>AutoMovement_TransitionTo</STRONG> (Thumb, 98 bytes, Stack size 16 bytes, automovement.o(i.AutoMovement_TransitionTo))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = AutoMovement_TransitionTo &rArr; LED_ON
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_StopPathMovement
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_StartPathMovement
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_GetTick
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_ON
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_OFF
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_Update
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_Stop
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_Start
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_HandlePreparing
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_HandlePathMoving
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_HandleMovingToStart
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_HandleError
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_HandleDirectionChange
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_EmergencyStop
</UL>

<P><STRONG><a name="[a3]"></a>AutoMovement_Update</STRONG> (Thumb, 172 bytes, Stack size 16 bytes, automovement.o(i.AutoMovement_Update))
<BR><BR>[Stack]<UL><LI>Max Depth = 248 + Unknown Stack Size
<LI>Call Chain = AutoMovement_Update &rArr; AutoMovement_ShowStatus &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_IsTimeout
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_TransitionTo
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_Stop
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_ShowStatus
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_HandlePreparing
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_HandlePathMoving
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_HandleMovingToStart
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_HandleError
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_HandleDirectionChange
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_EmergencyStop
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_CheckSafety
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a4]"></a>Bluetooth_ConfigureModule</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, bluetooth.o(i.Bluetooth_ConfigureModule))
<BR><BR>[Stack]<UL><LI>Max Depth = 208 + Unknown Stack Size
<LI>Call Chain = Bluetooth_ConfigureModule &rArr; Bluetooth_SetName &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_TestConnection
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SetPin
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SetName
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_Init
</UL>

<P><STRONG><a name="[a9]"></a>Bluetooth_Init</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, bluetooth.o(i.Bluetooth_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 400 + Unknown Stack Size
<LI>Call Chain = Bluetooth_Init &rArr; Bluetooth_SendStartupInfo &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendStartupInfo
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_ConfigureModule
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ac]"></a>Bluetooth_ParseCommand</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, bluetooth.o(i.Bluetooth_ParseCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 400 + Unknown Stack Size
<LI>Call Chain = Bluetooth_ParseCommand &rArr; Bluetooth_PrintConnectionInfo &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendString
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_PrintConnectionInfo
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_ProcessReceivedData
</UL>

<P><STRONG><a name="[af]"></a>Bluetooth_PrintConnectionInfo</STRONG> (Thumb, 58 bytes, Stack size 264 bytes, bluetooth.o(i.Bluetooth_PrintConnectionInfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 392 + Unknown Stack Size
<LI>Call Chain = Bluetooth_PrintConnectionInfo &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendString
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_ParseCommand
</UL>

<P><STRONG><a name="[b0]"></a>Bluetooth_ProcessReceivedData</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, bluetooth.o(i.Bluetooth_ProcessReceivedData))
<BR><BR>[Stack]<UL><LI>Max Depth = 416 + Unknown Stack Size
<LI>Call Chain = Bluetooth_ProcessReceivedData &rArr; Bluetooth_ParseCommand &rArr; Bluetooth_PrintConnectionInfo &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_ParseCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_Update
</UL>

<P><STRONG><a name="[b1]"></a>Bluetooth_SendByte</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, bluetooth.o(i.Bluetooth_SendByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Bluetooth_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendString
</UL>

<P><STRONG><a name="[b4]"></a>Bluetooth_SendErrorReport</STRONG> (Thumb, 82 bytes, Stack size 280 bytes, bluetooth.o(i.Bluetooth_SendErrorReport))
<BR><BR>[Stack]<UL><LI>Max Depth = 408 + Unknown Stack Size
<LI>Call Chain = Bluetooth_SendErrorReport &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendString
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b5]"></a>Bluetooth_SendKeyAction</STRONG> (Thumb, 102 bytes, Stack size 280 bytes, bluetooth.o(i.Bluetooth_SendKeyAction))
<BR><BR>[Stack]<UL><LI>Max Depth = 408 + Unknown Stack Size
<LI>Call Chain = Bluetooth_SendKeyAction &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendString
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_StopAutoMovement
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_StartAutoMovement
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointB
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointA
</UL>

<P><STRONG><a name="[ab]"></a>Bluetooth_SendStartupInfo</STRONG> (Thumb, 32 bytes, Stack size 264 bytes, bluetooth.o(i.Bluetooth_SendStartupInfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 392 + Unknown Stack Size
<LI>Call Chain = Bluetooth_SendStartupInfo &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendString
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_Init
</UL>

<P><STRONG><a name="[ae]"></a>Bluetooth_SendString</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, bluetooth.o(i.Bluetooth_SendString))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Bluetooth_SendString &rArr; Bluetooth_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendByte
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendKeyAction
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointB
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointA
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_TestConnection
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SetPin
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SetName
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendStartupInfo
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendErrorReport
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_PrintConnectionInfo
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_ParseCommand
</UL>

<P><STRONG><a name="[a5]"></a>Bluetooth_SetName</STRONG> (Thumb, 26 bytes, Stack size 72 bytes, bluetooth.o(i.Bluetooth_SetName))
<BR><BR>[Stack]<UL><LI>Max Depth = 200 + Unknown Stack Size
<LI>Call Chain = Bluetooth_SetName &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendString
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_ConfigureModule
</UL>

<P><STRONG><a name="[a7]"></a>Bluetooth_SetPin</STRONG> (Thumb, 26 bytes, Stack size 40 bytes, bluetooth.o(i.Bluetooth_SetPin))
<BR><BR>[Stack]<UL><LI>Max Depth = 168 + Unknown Stack Size
<LI>Call Chain = Bluetooth_SetPin &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendString
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_ConfigureModule
</UL>

<P><STRONG><a name="[a8]"></a>Bluetooth_TestConnection</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, bluetooth.o(i.Bluetooth_TestConnection))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Bluetooth_TestConnection &rArr; Bluetooth_SendString &rArr; Bluetooth_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendString
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_ConfigureModule
</UL>

<P><STRONG><a name="[b6]"></a>Bluetooth_Update</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, bluetooth.o(i.Bluetooth_Update))
<BR><BR>[Stack]<UL><LI>Max Depth = 424 + Unknown Stack Size
<LI>Call Chain = Bluetooth_Update &rArr; Bluetooth_ProcessReceivedData &rArr; Bluetooth_ParseCommand &rArr; Bluetooth_PrintConnectionInfo &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_ProcessReceivedData
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f10x_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[a6]"></a>Delay_ms</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, delay.o(i.Delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = Delay_ms
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointB
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointA
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_SetTorqueEnable
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_SendCommand
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReadVoltage
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReadTemperature
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReadPosition
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_Init
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_GetStatus
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_TestConnection
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_ConfigureModule
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_RunFullCheck
</UL>

<P><STRONG><a name="[b7]"></a>Delay_us</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, delay.o(i.Delay_us))
<BR><BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
</UL>

<P><STRONG><a name="[c6]"></a>GPIO_Init</STRONG> (Thumb, 278 bytes, Stack size 24 bytes, stm32f10x_gpio.o(i.GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Init
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Init
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
</UL>

<P><STRONG><a name="[c8]"></a>GPIO_ReadInputDataBit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_gpio.o(i.GPIO_ReadInputDataBit))
<BR><BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_ReadPin
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[cd]"></a>GPIO_ResetBits</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_gpio.o(i.GPIO_ResetBits))
<BR><BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_ON
</UL>

<P><STRONG><a name="[cc]"></a>GPIO_SetBits</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_gpio.o(i.GPIO_SetBits))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_OFF
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
</UL>

<P><STRONG><a name="[d2]"></a>GPIO_WriteBit</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f10x_gpio.o(i.GPIO_WriteBit))
<BR><BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Stop
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Start
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_SendByte
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Init
</UL>

<P><STRONG><a name="[7c]"></a>Geometry_CalculateDistance</STRONG> (Thumb, 62 bytes, Stack size 40 bytes, geometry.o(i.Geometry_CalculateDistance))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Geometry_CalculateDistance &rArr; sqrtf &rArr; _fsqrt &rArr; __fpl_fnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrtf
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_ValidatePath
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_ArePointsValid
</UL>

<P><STRONG><a name="[bb]"></a>Geometry_ClampServoAngle</STRONG> (Thumb, 90 bytes, Stack size 32 bytes, geometry.o(i.Geometry_ClampServoAngle))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Geometry_ClampServoAngle &rArr; __aeabi_cfcmple
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfrcmple
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_WallToServo
</UL>

<P><STRONG><a name="[bc]"></a>Geometry_ClampWallPoint</STRONG> (Thumb, 90 bytes, Stack size 32 bytes, geometry.o(i.Geometry_ClampWallPoint))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Geometry_ClampWallPoint &rArr; __aeabi_cfcmple
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfrcmple
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_LinearInterpolate
</UL>

<P><STRONG><a name="[bd]"></a>Geometry_IsServoAngleValid</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, geometry.o(i.Geometry_IsServoAngleValid))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Geometry_IsServoAngleValid &rArr; __aeabi_cfcmple
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfrcmple
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_ServoToWall
</UL>

<P><STRONG><a name="[7b]"></a>Geometry_IsWallPointValid</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, geometry.o(i.Geometry_IsWallPointValid))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Geometry_IsWallPointValid &rArr; __aeabi_cfcmple
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfrcmple
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_ValidatePath
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_LinearInterpolate
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_WallToServo
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_ArePointsValid
</UL>

<P><STRONG><a name="[be]"></a>Geometry_ServoToWall</STRONG> (Thumb, 140 bytes, Stack size 40 bytes, geometry.o(i.Geometry_ServoToWall))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = Geometry_ServoToWall &rArr; tanf &rArr; __mathlib_rredf2 &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_IsServoAngleValid
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_isnanf
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_isinff
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
</UL>
<BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_CheckGeometry
</UL>

<P><STRONG><a name="[c2]"></a>Geometry_WallToServo</STRONG> (Thumb, 126 bytes, Stack size 48 bytes, geometry.o(i.Geometry_WallToServo))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = Geometry_WallToServo &rArr; Geometry_ClampServoAngle &rArr; __aeabi_cfcmple
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_IsWallPointValid
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_ClampServoAngle
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atanf
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_SetDirection
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_Initialize
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_GetNextStep
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_CheckGeometry
</UL>

<P><STRONG><a name="[6]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_it.o(i.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[fe]"></a>Key_ClearEvent</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, key.o(i.Key_ClearEvent))
<BR><BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_Update
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c4]"></a>Key_Init</STRONG> (Thumb, 120 bytes, Stack size 8 bytes, key.o(i.Key_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Key_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fd]"></a>Key_IsClicked</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, key.o(i.Key_IsClicked))
<BR><BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_Update
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[138]"></a>Key_IsPressed</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, key.o(i.Key_IsPressed))
<BR><BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c7]"></a>Key_ReadPin</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, key.o(i.Key_ReadPin))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Key_ReadPin
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadInputDataBit
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_UpdateState
</UL>

<P><STRONG><a name="[c9]"></a>Key_Scan</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, key.o(i.Key_Scan))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Key_Scan &rArr; Key_UpdateState &rArr; Key_ReadPin
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_UpdateState
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_Update
</UL>

<P><STRONG><a name="[ca]"></a>Key_UpdateState</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, key.o(i.Key_UpdateState))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Key_UpdateState &rArr; Key_ReadPin
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_ReadPin
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Scan
</UL>

<P><STRONG><a name="[cb]"></a>LED_Init</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, led.o(i.LED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = LED_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a0]"></a>LED_OFF</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, led.o(i.LED_OFF))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LED_OFF
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_RunFullCheck
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_TransitionTo
</UL>

<P><STRONG><a name="[a1]"></a>LED_ON</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, led.o(i.LED_ON))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LED_ON
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_RunFullCheck
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_TransitionTo
</UL>

<P><STRONG><a name="[97]"></a>ManualRecord_IsPointSaved</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, manualrecord.o(i.ManualRecord_IsPointSaved))
<BR><BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_LoadPointsFromRecord
</UL>

<P><STRONG><a name="[98]"></a>ManualRecord_LoadPointData</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, manualrecord.o(i.ManualRecord_LoadPointData))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ManualRecord_LoadPointData
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_LoadPointsFromRecord
</UL>

<P><STRONG><a name="[7]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_it.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f10x_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[116]"></a>NVIC_Init</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, misc.o(i.NVIC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = NVIC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_ConfigureHardware
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
</UL>

<P><STRONG><a name="[ce]"></a>OLED_Clear</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, oled.o(i.OLED_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = OLED_Clear &rArr; OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteData
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_SetCursor
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_ShowErrors
</UL>

<P><STRONG><a name="[d1]"></a>OLED_I2C_Init</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, oled.o(i.OLED_I2C_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = OLED_I2C_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_WriteBit
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[d3]"></a>OLED_I2C_SendByte</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, oled.o(i.OLED_I2C_SendByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_WriteBit
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteData
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCommand
</UL>

<P><STRONG><a name="[d4]"></a>OLED_I2C_Start</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, oled.o(i.OLED_I2C_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = OLED_I2C_Start
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_WriteBit
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteData
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCommand
</UL>

<P><STRONG><a name="[d5]"></a>OLED_I2C_Stop</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, oled.o(i.OLED_I2C_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = OLED_I2C_Stop
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_WriteBit
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteData
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCommand
</UL>

<P><STRONG><a name="[d6]"></a>OLED_Init</STRONG> (Thumb, 174 bytes, Stack size 16 bytes, oled.o(i.OLED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = OLED_Init &rArr; OLED_Clear &rArr; OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCommand
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Init
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[cf]"></a>OLED_SetCursor</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, oled.o(i.OLED_SetCursor))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>

<P><STRONG><a name="[d8]"></a>OLED_ShowChar</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, oled.o(i.OLED_ShowChar))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = OLED_ShowChar &rArr; OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteData
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_SetCursor
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
</UL>

<P><STRONG><a name="[9c]"></a>OLED_ShowString</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, oled.o(i.OLED_ShowString))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_ShowStatus
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_ShowPerformance
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_ShowErrors
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_RunFullCheck
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_ShowStatus
</UL>

<P><STRONG><a name="[d7]"></a>OLED_WriteCommand</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, oled.o(i.OLED_WriteCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = OLED_WriteCommand &rArr; OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Stop
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Start
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_SendByte
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_SetCursor
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[d0]"></a>OLED_WriteData</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, oled.o(i.OLED_WriteData))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = OLED_WriteData &rArr; OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Stop
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Start
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_SendByte
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>

<P><STRONG><a name="[84]"></a>Path_GetNextStep</STRONG> (Thumb, 156 bytes, Stack size 48 bytes, geometry.o(i.Path_GetNextStep))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = Path_GetNextStep &rArr; Path_LinearInterpolate &rArr; Geometry_ClampWallPoint &rArr; __aeabi_cfcmple
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_LinearInterpolate
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_IsComplete
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_WallToServo
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_UpdateAutoMovement
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_ExecuteNextStep
</UL>

<P><STRONG><a name="[95]"></a>Path_Initialize</STRONG> (Thumb, 106 bytes, Stack size 32 bytes, geometry.o(i.Path_Initialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = Path_Initialize &rArr; Path_ValidatePath &rArr; Geometry_CalculateDistance &rArr; sqrtf &rArr; _fsqrt &rArr; __fpl_fnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_ValidatePath
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_WallToServo
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_StartAutoMovement
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_Init
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_HandlePreparing
</UL>

<P><STRONG><a name="[93]"></a>Path_IsComplete</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, geometry.o(i.Path_IsComplete))
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_GetNextStep
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_HandlePathMoving
</UL>

<P><STRONG><a name="[d9]"></a>Path_LinearInterpolate</STRONG> (Thumb, 140 bytes, Stack size 56 bytes, geometry.o(i.Path_LinearInterpolate))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = Path_LinearInterpolate &rArr; Geometry_ClampWallPoint &rArr; __aeabi_cfcmple
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_IsWallPointValid
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_ClampWallPoint
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_GetNextStep
</UL>

<P><STRONG><a name="[8c]"></a>Path_SetDirection</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, geometry.o(i.Path_SetDirection))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = Path_SetDirection &rArr; Geometry_WallToServo &rArr; Geometry_ClampServoAngle &rArr; __aeabi_cfcmple
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_WallToServo
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_ToggleDirection
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_HandlePreparing
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_HandleDirectionChange
</UL>

<P><STRONG><a name="[da]"></a>Path_ValidatePath</STRONG> (Thumb, 70 bytes, Stack size 24 bytes, geometry.o(i.Path_ValidatePath))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = Path_ValidatePath &rArr; Geometry_CalculateDistance &rArr; sqrtf &rArr; _fsqrt &rArr; __fpl_fnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_IsWallPointValid
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_CalculateDistance
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfrcmple
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_Initialize
</UL>

<P><STRONG><a name="[c]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f10x_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[114]"></a>RCC_APB1PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_ConfigureHardware
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
</UL>

<P><STRONG><a name="[c5]"></a>RCC_APB2PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Init
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Init
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
</UL>

<P><STRONG><a name="[120]"></a>RCC_GetClocksFreq</STRONG> (Thumb, 192 bytes, Stack size 12 bytes, stm32f10x_rcc.o(i.RCC_GetClocksFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
</UL>

<P><STRONG><a name="[a]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f10x_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[db]"></a>Servo_AngleToPosition</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, servo.o(i.Servo_AngleToPosition))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Servo_AngleToPosition &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfrcmple
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfcmple
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2uiz
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_SetPositionWithTime
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_GetStatus
</UL>

<P><STRONG><a name="[e7]"></a>Servo_CalculateChecksum</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, servo.o(i.Servo_CalculateChecksum))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Servo_CalculateChecksum
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_SendCommand
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReceiveResponse
</UL>

<P><STRONG><a name="[dd]"></a>Servo_GetStatus</STRONG> (Thumb, 118 bytes, Stack size 32 bytes, servo.o(i.Servo_GetStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = Servo_GetStatus &rArr; Servo_ReadPosition &rArr; Servo_SendCommand &rArr; USART1_SendBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_SendCommand
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReceiveResponse
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReadVoltage
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReadTemperature
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReadPosition
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_AngleToPosition
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_CheckServos
</UL>

<P><STRONG><a name="[e3]"></a>Servo_Init</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, servo.o(i.Servo_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = Servo_Init &rArr; USART1_Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_Init
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e5]"></a>Servo_PositionToAngle</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, servo.o(i.Servo_PositionToAngle))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Servo_PositionToAngle &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReadPosition
</UL>

<P><STRONG><a name="[de]"></a>Servo_ReadPosition</STRONG> (Thumb, 126 bytes, Stack size 40 bytes, servo.o(i.Servo_ReadPosition))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = Servo_ReadPosition &rArr; Servo_SendCommand &rArr; USART1_SendBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_SendCommand
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReceiveResponse
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_PositionToAngle
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_GetStatus
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_CheckServos
</UL>

<P><STRONG><a name="[df]"></a>Servo_ReadTemperature</STRONG> (Thumb, 62 bytes, Stack size 32 bytes, servo.o(i.Servo_ReadTemperature))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = Servo_ReadTemperature &rArr; Servo_SendCommand &rArr; USART1_SendBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_SendCommand
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReceiveResponse
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_GetStatus
</UL>

<P><STRONG><a name="[e0]"></a>Servo_ReadVoltage</STRONG> (Thumb, 70 bytes, Stack size 32 bytes, servo.o(i.Servo_ReadVoltage))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = Servo_ReadVoltage &rArr; Servo_SendCommand &rArr; USART1_SendBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_SendCommand
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReceiveResponse
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_GetStatus
</UL>

<P><STRONG><a name="[e2]"></a>Servo_ReceiveResponse</STRONG> (Thumb, 272 bytes, Stack size 64 bytes, servo.o(i.Servo_ReceiveResponse))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = Servo_ReceiveResponse &rArr; Servo_CalculateChecksum
</UL>
<BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_CalculateChecksum
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReadVoltage
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReadTemperature
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReadPosition
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_GetStatus
</UL>

<P><STRONG><a name="[e1]"></a>Servo_SendCommand</STRONG> (Thumb, 128 bytes, Stack size 48 bytes, servo.o(i.Servo_SendCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = Servo_SendCommand &rArr; USART1_SendBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_SendBuffer
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_CalculateChecksum
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_SetTorqueEnable
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_SetPositionWithTime
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReadVoltage
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReadTemperature
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReadPosition
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_GetStatus
</UL>

<P><STRONG><a name="[e9]"></a>Servo_SetPosition</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, servo.o(i.Servo_SetPosition))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = Servo_SetPosition &rArr; Servo_SetPositionWithTime &rArr; Servo_SendCommand &rArr; USART1_SendBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_SetPositionWithTime
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[99]"></a>Servo_SetPositionWithTime</STRONG> (Thumb, 54 bytes, Stack size 24 bytes, servo.o(i.Servo_SetPositionWithTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = Servo_SetPositionWithTime &rArr; Servo_SendCommand &rArr; USART1_SendBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_SendCommand
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_AngleToPosition
</UL>
<BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_MoveServoToPosition
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_SetPosition
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_MoveToPosition
</UL>

<P><STRONG><a name="[ea]"></a>Servo_SetTorqueEnable</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, servo.o(i.Servo_SetTorqueEnable))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Servo_SetTorqueEnable &rArr; Servo_SendCommand &rArr; USART1_SendBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_SendCommand
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointB
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointA
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[13f]"></a>StateMachine_GetStateString</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, statemachine.o(i.StateMachine_GetStateString))
<BR><BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ed]"></a>StateMachine_HandleRecordKey</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, statemachine.o(i.StateMachine_HandleRecordKey))
<BR><BR>[Stack]<UL><LI>Max Depth = 664 + Unknown Stack Size
<LI>Call Chain = StateMachine_HandleRecordKey &rArr; StateMachine_RecordPointB &rArr; StateMachine_TransitionTo &rArr; StateMachine_StartAutoMovement &rArr; Bluetooth_SendKeyAction &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_StopAutoMovement
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointB
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointA
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_Update
</UL>

<P><STRONG><a name="[f1]"></a>StateMachine_HandleTriggerKey</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, statemachine.o(i.StateMachine_HandleTriggerKey))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = StateMachine_HandleTriggerKey &rArr; StateMachine_ToggleDirection &rArr; Path_SetDirection &rArr; Geometry_WallToServo &rArr; Geometry_ClampServoAngle &rArr; __aeabi_cfcmple
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_ToggleDirection
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_Update
</UL>

<P><STRONG><a name="[f3]"></a>StateMachine_Init</STRONG> (Thumb, 126 bytes, Stack size 32 bytes, statemachine.o(i.StateMachine_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = StateMachine_Init &rArr; Path_Initialize &rArr; Path_ValidatePath &rArr; Geometry_CalculateDistance &rArr; sqrtf &rArr; _fsqrt &rArr; __fpl_fnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Start
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Init
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_Initialize
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[140]"></a>StateMachine_IsPointRecorded</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, statemachine.o(i.StateMachine_IsPointRecorded))
<BR><BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f6]"></a>StateMachine_MoveServoToPosition</STRONG> (Thumb, 28 bytes, Stack size 24 bytes, statemachine.o(i.StateMachine_MoveServoToPosition))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = StateMachine_MoveServoToPosition &rArr; Servo_SetPositionWithTime &rArr; Servo_SendCommand &rArr; USART1_SendBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_SetPositionWithTime
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_UpdateAutoMovement
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_StartAutoMovement
</UL>

<P><STRONG><a name="[ee]"></a>StateMachine_RecordPointA</STRONG> (Thumb, 512 bytes, Stack size 216 bytes, statemachine.o(i.StateMachine_RecordPointA))
<BR><BR>[Stack]<UL><LI>Max Depth = 656 + Unknown Stack Size
<LI>Call Chain = StateMachine_RecordPointA &rArr; StateMachine_TransitionTo &rArr; StateMachine_StartAutoMovement &rArr; Bluetooth_SendKeyAction &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_IsTimeout
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_GetTick
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendString
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendKeyAction
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_TransitionTo
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_SetTorqueEnable
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tan
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_HandleRecordKey
</UL>

<P><STRONG><a name="[ef]"></a>StateMachine_RecordPointB</STRONG> (Thumb, 512 bytes, Stack size 216 bytes, statemachine.o(i.StateMachine_RecordPointB))
<BR><BR>[Stack]<UL><LI>Max Depth = 656 + Unknown Stack Size
<LI>Call Chain = StateMachine_RecordPointB &rArr; StateMachine_TransitionTo &rArr; StateMachine_StartAutoMovement &rArr; Bluetooth_SendKeyAction &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_IsTimeout
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_GetTick
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendString
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendKeyAction
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_TransitionTo
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_SetTorqueEnable
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tan
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_HandleRecordKey
</UL>

<P><STRONG><a name="[fb]"></a>StateMachine_StartAutoMovement</STRONG> (Thumb, 98 bytes, Stack size 16 bytes, statemachine.o(i.StateMachine_StartAutoMovement))
<BR><BR>[Stack]<UL><LI>Max Depth = 424 + Unknown Stack Size
<LI>Call Chain = StateMachine_StartAutoMovement &rArr; Bluetooth_SendKeyAction &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_StartPathMovement
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_GetTick
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendKeyAction
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_MoveServoToPosition
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_Initialize
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_TransitionTo
</UL>

<P><STRONG><a name="[f0]"></a>StateMachine_StopAutoMovement</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, statemachine.o(i.StateMachine_StopAutoMovement))
<BR><BR>[Stack]<UL><LI>Max Depth = 448 + Unknown Stack Size
<LI>Call Chain = StateMachine_StopAutoMovement &rArr; StateMachine_TransitionTo &rArr; StateMachine_StartAutoMovement &rArr; Bluetooth_SendKeyAction &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_StopPathMovement
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendKeyAction
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_TransitionTo
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_HandleRecordKey
</UL>

<P><STRONG><a name="[f2]"></a>StateMachine_ToggleDirection</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, statemachine.o(i.StateMachine_ToggleDirection))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = StateMachine_ToggleDirection &rArr; Path_SetDirection &rArr; Geometry_WallToServo &rArr; Geometry_ClampServoAngle &rArr; __aeabi_cfcmple
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_StartPathMovement
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_GetTick
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_SetDirection
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_UpdateAutoMovement
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_HandleTriggerKey
</UL>

<P><STRONG><a name="[fa]"></a>StateMachine_TransitionTo</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, statemachine.o(i.StateMachine_TransitionTo))
<BR><BR>[Stack]<UL><LI>Max Depth = 440 + Unknown Stack Size
<LI>Call Chain = StateMachine_TransitionTo &rArr; StateMachine_StartAutoMovement &rArr; Bluetooth_SendKeyAction &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_StartAutoMovement
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_StopAutoMovement
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointB
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointA
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fc]"></a>StateMachine_Update</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, statemachine.o(i.StateMachine_Update))
<BR><BR>[Stack]<UL><LI>Max Depth = 680 + Unknown Stack Size
<LI>Call Chain = StateMachine_Update &rArr; StateMachine_HandleRecordKey &rArr; StateMachine_RecordPointB &rArr; StateMachine_TransitionTo &rArr; StateMachine_StartAutoMovement &rArr; Bluetooth_SendKeyAction &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_UpdateAutoMovement
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_HandleTriggerKey
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_HandleRecordKey
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Scan
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_IsClicked
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_ClearEvent
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ff]"></a>StateMachine_UpdateAutoMovement</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, statemachine.o(i.StateMachine_UpdateAutoMovement))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = StateMachine_UpdateAutoMovement &rArr; Path_GetNextStep &rArr; Path_LinearInterpolate &rArr; Geometry_ClampWallPoint &rArr; __aeabi_cfcmple
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_ResetPathStep
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_IsPathStepReady
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_GetTick
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_ToggleDirection
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_MoveServoToPosition
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_GetNextStep
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_Update
</UL>

<P><STRONG><a name="[d]"></a>SysTick_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f10x_it.o(i.SysTick_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[100]"></a>SystemDiag_CheckGeometry</STRONG> (Thumb, 142 bytes, Stack size 56 bytes, systemdiagnostics.o(i.SystemDiag_CheckGeometry))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = SystemDiag_CheckGeometry &rArr; Geometry_ServoToWall &rArr; tanf &rArr; __mathlib_rredf2 &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_WallToServo
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_ServoToWall
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfrcmple
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_LogError
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_RunFullCheck
</UL>

<P><STRONG><a name="[10c]"></a>SystemDiag_CheckKeys</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, systemdiagnostics.o(i.SystemDiag_CheckKeys))
<BR><BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_RunFullCheck
</UL>

<P><STRONG><a name="[10d]"></a>SystemDiag_CheckMemory</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, systemdiagnostics.o(i.SystemDiag_CheckMemory))
<BR><BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_RunFullCheck
</UL>

<P><STRONG><a name="[102]"></a>SystemDiag_CheckServos</STRONG> (Thumb, 246 bytes, Stack size 48 bytes, systemdiagnostics.o(i.SystemDiag_CheckServos))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = SystemDiag_CheckServos &rArr; Servo_GetStatus &rArr; Servo_ReadPosition &rArr; Servo_SendCommand &rArr; USART1_SendBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReadPosition
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_GetStatus
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfrcmple
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfcmple
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_LogError
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_RunFullCheck
</UL>

<P><STRONG><a name="[103]"></a>SystemDiag_CheckTimer</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, systemdiagnostics.o(i.SystemDiag_CheckTimer))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SystemDiag_CheckTimer &rArr; SystemDiag_LogError &rArr; strncpy
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_IsRunning
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_GetFrequency
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_LogError
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_RunFullCheck
</UL>

<P><STRONG><a name="[10e]"></a>SystemDiag_GetLevelString</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, systemdiagnostics.o(i.SystemDiag_GetLevelString))
<BR><BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_RunFullCheck
</UL>

<P><STRONG><a name="[106]"></a>SystemDiag_Init</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, systemdiagnostics.o(i.SystemDiag_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SystemDiag_Init &rArr; __aeabi_memclr4
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_GetTick
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_LoadDefaultConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[108]"></a>SystemDiag_LoadDefaultConfig</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, systemdiagnostics.o(i.SystemDiag_LoadDefaultConfig))
<BR><BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_Init
</UL>

<P><STRONG><a name="[101]"></a>SystemDiag_LogError</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, systemdiagnostics.o(i.SystemDiag_LogError))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SystemDiag_LogError &rArr; strncpy
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_GetTick
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_QuickCheck
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_CheckTimer
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_CheckServos
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_CheckGeometry
</UL>

<P><STRONG><a name="[109]"></a>SystemDiag_QuickCheck</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, systemdiagnostics.o(i.SystemDiag_QuickCheck))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SystemDiag_QuickCheck &rArr; SystemDiag_LogError &rArr; strncpy
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfcmple
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_LogError
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[13e]"></a>SystemDiag_RecordKeyPress</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, systemdiagnostics.o(i.SystemDiag_RecordKeyPress))
<BR><BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10a]"></a>SystemDiag_RecordPathStep</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, systemdiagnostics.o(i.SystemDiag_RecordPathStep))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = SystemDiag_RecordPathStep &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[139]"></a>SystemDiag_RecordStateTransition</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, systemdiagnostics.o(i.SystemDiag_RecordStateTransition))
<BR><BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10b]"></a>SystemDiag_RunFullCheck</STRONG> (Thumb, 324 bytes, Stack size 40 bytes, systemdiagnostics.o(i.SystemDiag_RunFullCheck))
<BR><BR>[Stack]<UL><LI>Max Depth = 232 + Unknown Stack Size
<LI>Call Chain = SystemDiag_RunFullCheck &rArr; SystemDiag_CheckServos &rArr; Servo_GetStatus &rArr; Servo_ReadPosition &rArr; Servo_SendCommand &rArr; USART1_SendBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_ON
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_OFF
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_GetLevelString
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_CheckTimer
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_CheckServos
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_CheckMemory
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_CheckKeys
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_CheckGeometry
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10f]"></a>SystemDiag_ShowErrors</STRONG> (Thumb, 158 bytes, Stack size 48 bytes, systemdiagnostics.o(i.SystemDiag_ShowErrors))
<BR><BR>[Stack]<UL><LI>Max Depth = 176 + Unknown Stack Size
<LI>Call Chain = SystemDiag_ShowErrors &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[110]"></a>SystemDiag_ShowPerformance</STRONG> (Thumb, 92 bytes, Stack size 32 bytes, systemdiagnostics.o(i.SystemDiag_ShowPerformance))
<BR><BR>[Stack]<UL><LI>Max Depth = 160 + Unknown Stack Size
<LI>Call Chain = SystemDiag_ShowPerformance &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[111]"></a>SystemDiag_ShowStatus</STRONG> (Thumb, 118 bytes, Stack size 40 bytes, systemdiagnostics.o(i.SystemDiag_ShowStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 168 + Unknown Stack Size
<LI>Call Chain = SystemDiag_ShowStatus &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[39]"></a>SystemInit</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, system_stm32f10x.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = SystemInit &rArr; SetSysClock &rArr; SetSysClockTo72
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClock
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(.text)
</UL>
<P><STRONG><a name="[2a]"></a>TIM2_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = TIM2_IRQHandler &rArr; Timer_IRQ_Handler &rArr; TIM_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_IRQ_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[118]"></a>TIM_ClearITPendingBit</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_ClearITPendingBit))
<BR><BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_IRQ_Handler
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_ConfigureHardware
</UL>

<P><STRONG><a name="[11a]"></a>TIM_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Start
</UL>

<P><STRONG><a name="[119]"></a>TIM_GetITStatus</STRONG> (Thumb, 34 bytes, Stack size 12 bytes, stm32f10x_tim.o(i.TIM_GetITStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_GetITStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_IRQ_Handler
</UL>

<P><STRONG><a name="[117]"></a>TIM_ITConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_ITConfig))
<BR><BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_ConfigureHardware
</UL>

<P><STRONG><a name="[115]"></a>TIM_TimeBaseInit</STRONG> (Thumb, 122 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_TimeBaseInit))
<BR><BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_ConfigureHardware
</UL>

<P><STRONG><a name="[113]"></a>Timer_ConfigureHardware</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, timer.o(i.Timer_ConfigureHardware))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Timer_ConfigureHardware &rArr; NVIC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITConfig
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearITPendingBit
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Init
</UL>

<P><STRONG><a name="[105]"></a>Timer_GetFrequency</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, timer.o(i.Timer_GetFrequency))
<BR><BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_CheckTimer
</UL>

<P><STRONG><a name="[86]"></a>Timer_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, timer.o(i.Timer_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_IsTimeout
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_UpdateAutoMovement
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_ToggleDirection
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_StartAutoMovement
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointB
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointA
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_LogError
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_Init
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_TransitionTo
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_Start
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_ShowStatus
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_ExecuteNextStep
</UL>

<P><STRONG><a name="[13c]"></a>Timer_GetTimeMs</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, timer.o(i.Timer_GetTimeMs))
<BR><BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[112]"></a>Timer_IRQ_Handler</STRONG> (Thumb, 150 bytes, Stack size 8 bytes, timer.o(i.Timer_IRQ_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = Timer_IRQ_Handler &rArr; TIM_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetITStatus
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearITPendingBit
</UL>
<BR>[Called By]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_IRQHandler
</UL>

<P><STRONG><a name="[f4]"></a>Timer_Init</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, timer.o(i.Timer_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Timer_Init &rArr; Timer_ConfigureHardware &rArr; NVIC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_ConfigureHardware
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_Init
</UL>

<P><STRONG><a name="[91]"></a>Timer_IsPathStepReady</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, timer.o(i.Timer_IsPathStepReady))
<BR><BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_UpdateAutoMovement
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_HandlePathMoving
</UL>

<P><STRONG><a name="[104]"></a>Timer_IsRunning</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, timer.o(i.Timer_IsRunning))
<BR><BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_CheckTimer
</UL>

<P><STRONG><a name="[8b]"></a>Timer_IsTimeout</STRONG> (Thumb, 28 bytes, Stack size 4 bytes, timer.o(i.Timer_IsTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = Timer_IsTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointB
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointA
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_Update
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_HandlePreparing
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_HandleMovingToStart
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_HandleDirectionChange
</UL>

<P><STRONG><a name="[92]"></a>Timer_ResetPathStep</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, timer.o(i.Timer_ResetPathStep))
<BR><BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_UpdateAutoMovement
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_HandlePathMoving
</UL>

<P><STRONG><a name="[f5]"></a>Timer_Start</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, timer.o(i.Timer_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Timer_Start
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_Init
</UL>

<P><STRONG><a name="[a2]"></a>Timer_StartPathMovement</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, timer.o(i.Timer_StartPathMovement))
<BR><BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_ToggleDirection
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_StartAutoMovement
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_TransitionTo
</UL>

<P><STRONG><a name="[81]"></a>Timer_StopPathMovement</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, timer.o(i.Timer_StopPathMovement))
<BR><BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_StopAutoMovement
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_TransitionTo
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_HandleError
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_EmergencyStop
</UL>

<P><STRONG><a name="[e4]"></a>USART1_Init</STRONG> (Thumb, 120 bytes, Stack size 24 bytes, usart.o(i.USART1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = USART1_Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_Init
</UL>

<P><STRONG><a name="[e8]"></a>USART1_SendBuffer</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, usart.o(i.USART1_SendBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USART1_SendBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_SendCommand
</UL>

<P><STRONG><a name="[34]"></a>USART2_IRQHandler</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, bluetooth.o(i.USART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USART2_IRQHandler &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[11e]"></a>USART_ClearITPendingBit</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, stm32f10x_usart.o(i.USART_ClearITPendingBit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USART_ClearITPendingBit
</UL>
<BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[11c]"></a>USART_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_usart.o(i.USART_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_Init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
</UL>

<P><STRONG><a name="[b2]"></a>USART_GetFlagStatus</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_usart.o(i.USART_GetFlagStatus))
<BR><BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_SendBuffer
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReceiveResponse
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReadPosition
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendByte
</UL>

<P><STRONG><a name="[11d]"></a>USART_GetITStatus</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, stm32f10x_usart.o(i.USART_GetITStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART_GetITStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[11f]"></a>USART_ITConfig</STRONG> (Thumb, 74 bytes, Stack size 20 bytes, stm32f10x_usart.o(i.USART_ITConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = USART_ITConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
</UL>

<P><STRONG><a name="[11b]"></a>USART_Init</STRONG> (Thumb, 210 bytes, Stack size 56 bytes, stm32f10x_usart.o(i.USART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_Init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
</UL>

<P><STRONG><a name="[e6]"></a>USART_ReceiveData</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f10x_usart.o(i.USART_ReceiveData))
<BR><BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReceiveResponse
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReadPosition
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[b3]"></a>USART_SendData</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f10x_usart.o(i.USART_SendData))
<BR><BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_SendBuffer
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendByte
</UL>

<P><STRONG><a name="[9]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[6c]"></a>__ARM_fpclassify</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[136]"></a>__ARM_fpclassifyf</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, fpclassifyf.o(i.__ARM_fpclassifyf))
<BR><BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atanf
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
</UL>

<P><STRONG><a name="[121]"></a>__ieee754_rem_pio2</STRONG> (Thumb, 828 bytes, Stack size 128 bytes, rred.o(i.__ieee754_rem_pio2))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = __ieee754_rem_pio2 &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tan
</UL>

<P><STRONG><a name="[129]"></a>__kernel_poly</STRONG> (Thumb, 170 bytes, Stack size 24 bytes, poly.o(i.__kernel_poly))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
</UL>

<P><STRONG><a name="[12a]"></a>__kernel_tan</STRONG> (Thumb, 586 bytes, Stack size 80 bytes, tan_i.o(i.__kernel_tan))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = __kernel_tan &rArr; __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tan
</UL>

<P><STRONG><a name="[12d]"></a>__mathlib_dbl_infnan</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dunder.o(i.__mathlib_dbl_infnan))
<BR><BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tan
</UL>

<P><STRONG><a name="[12e]"></a>__mathlib_dbl_invalid</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, dunder.o(i.__mathlib_dbl_invalid))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __mathlib_dbl_invalid &rArr; __aeabi_ddiv
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tan
</UL>

<P><STRONG><a name="[12c]"></a>__mathlib_dbl_underflow</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, dunder.o(i.__mathlib_dbl_underflow))
<BR><BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
</UL>

<P><STRONG><a name="[12f]"></a>__mathlib_flt_infnan</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_infnan))
<BR><BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbnf
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atanf
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
</UL>

<P><STRONG><a name="[131]"></a>__mathlib_flt_invalid</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_invalid))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __mathlib_flt_invalid &rArr; __aeabi_fdiv
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
</UL>

<P><STRONG><a name="[132]"></a>__mathlib_flt_underflow</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_underflow))
<BR><BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbnf
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atanf
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
</UL>

<P><STRONG><a name="[133]"></a>__mathlib_rredf2</STRONG> (Thumb, 344 bytes, Stack size 32 bytes, rredf.o(i.__mathlib_rredf2))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __mathlib_rredf2 &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_frsub
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbnf
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
</UL>

<P><STRONG><a name="[5e]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, __printf_wp.o(i._is_digit))
<BR><BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[c3]"></a>atanf</STRONG> (Thumb, 324 bytes, Stack size 24 bytes, atanf.o(i.atanf))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = atanf &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassifyf
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_WallToServo
</UL>

<P><STRONG><a name="[54]"></a>main</STRONG> (Thumb, 1868 bytes, Stack size 864 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 1544 + Unknown Stack Size
<LI>Call Chain = main &rArr; StateMachine_Update &rArr; StateMachine_HandleRecordKey &rArr; StateMachine_RecordPointB &rArr; StateMachine_TransitionTo &rArr; StateMachine_StartAutoMovement &rArr; Bluetooth_SendKeyAction &rArr; __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadInputDataBit
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_GetTimeMs
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_IsTimeout
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_GetTick
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendString
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_Update
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_TransitionTo
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_IsPointRecorded
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_Init
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_GetStateString
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_SetTorqueEnable
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_SetPosition
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_ReadPosition
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_Init
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_IsPressed
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_IsClicked
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Init
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_ClearEvent
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_ON
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_OFF
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_Update
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_SendErrorReport
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_Init
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_ShowStatus
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_ShowPerformance
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_ShowErrors
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_RunFullCheck
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_RecordStateTransition
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_RecordPathStep
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_RecordKeyPress
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_QuickCheck
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_LogError
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_Init
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_Update
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_Stop
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_Start
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_ShowStatus
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_IsActive
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[ba]"></a>sqrtf</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, sqrtf.o(i.sqrtf))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = sqrtf &rArr; _fsqrt &rArr; __fpl_fnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsqrt
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_CalculateDistance
</UL>

<P><STRONG><a name="[f7]"></a>tan</STRONG> (Thumb, 90 bytes, Stack size 32 bytes, tan.o(i.tan))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = tan &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointB
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointA
</UL>

<P><STRONG><a name="[bf]"></a>tanf</STRONG> (Thumb, 314 bytes, Stack size 32 bytes, tanf.o(i.tanf))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = tanf &rArr; __mathlib_rredf2 &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_frsub
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_invalid
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassifyf
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_frnd
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_ServoToWall
</UL>

<P><STRONG><a name="[4e]"></a>_get_lc_numeric</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, lc_numeric_c.o(locale$$code))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _get_lc_numeric
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_numeric_2
</UL>

<P><STRONG><a name="[f9]"></a>__aeabi_d2f</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, d2f.o(x$fpl$d2f))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointB
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointA
</UL>

<P><STRONG><a name="[145]"></a>_d2f</STRONG> (Thumb, 98 bytes, Stack size 32 bytes, d2f.o(x$fpl$d2f), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[123]"></a>__aeabi_dadd</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, daddsub_clz.o(x$fpl$dadd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
</UL>

<P><STRONG><a name="[148]"></a>_dadd</STRONG> (Thumb, 332 bytes, Stack size 16 bytes, daddsub_clz.o(x$fpl$dadd), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub1
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[14b]"></a>__fpl_dcheck_NaN1</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, dcheck1.o(x$fpl$dcheck1))
<BR><BR>[Calls]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
</UL>

<P><STRONG><a name="[12b]"></a>__aeabi_ddiv</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, ddiv.o(x$fpl$ddiv))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
</UL>

<P><STRONG><a name="[14d]"></a>_ddiv</STRONG> (Thumb, 552 bytes, Stack size 32 bytes, ddiv.o(x$fpl$ddiv), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[124]"></a>__aeabi_d2iz</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dfix.o(x$fpl$dfix))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_d2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
</UL>

<P><STRONG><a name="[14e]"></a>_dfix</STRONG> (Thumb, 94 bytes, Stack size 32 bytes, dfix.o(x$fpl$dfix), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[125]"></a>__aeabi_i2d</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dflt))
<BR><BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
</UL>

<P><STRONG><a name="[199]"></a>_dflt</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dflt), UNUSED)

<P><STRONG><a name="[127]"></a>__aeabi_ui2d</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dfltu))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
</UL>

<P><STRONG><a name="[19a]"></a>_dfltu</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dfltu), UNUSED)

<P><STRONG><a name="[f8]"></a>__aeabi_dmul</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointB
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointA
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
</UL>

<P><STRONG><a name="[14f]"></a>_dmul</STRONG> (Thumb, 332 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[147]"></a>__fpl_dnaninf</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, dnaninf.o(x$fpl$dnaninf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2f
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dfix
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ddiv
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
</UL>

<P><STRONG><a name="[14a]"></a>__fpl_dretinf</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, dretinf.o(x$fpl$dretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ddiv
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
</UL>

<P><STRONG><a name="[126]"></a>__aeabi_drsub</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, daddsub_clz.o(x$fpl$drsb))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_drsub
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
</UL>

<P><STRONG><a name="[150]"></a>_drsb</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, daddsub_clz.o(x$fpl$drsb), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub1
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd1
</UL>

<P><STRONG><a name="[122]"></a>__aeabi_dsub</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, daddsub_clz.o(x$fpl$dsub))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_dsub
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
</UL>

<P><STRONG><a name="[152]"></a>_dsub</STRONG> (Thumb, 464 bytes, Stack size 32 bytes, daddsub_clz.o(x$fpl$dsub), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd1
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[9e]"></a>__aeabi_f2d</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointB
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointA
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_ShowStatus
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_ShowPerformance
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_ShowStatus
</UL>

<P><STRONG><a name="[153]"></a>_f2d</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
</UL>

<P><STRONG><a name="[b9]"></a>__aeabi_fadd</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, faddsub_clz.o(x$fpl$fadd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointB
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointA
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_LinearInterpolate
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_WallToServo
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_CalculateDistance
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atanf
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_RecordPathStep
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
</UL>

<P><STRONG><a name="[155]"></a>_fadd</STRONG> (Thumb, 196 bytes, Stack size 16 bytes, faddsub_clz.o(x$fpl$fadd), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub1
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[157]"></a>__fpl_fcheck_NaN1</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, fcheck1.o(x$fpl$fcheck1))
<BR><BR>[Calls]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbnf
</UL>

<P><STRONG><a name="[15c]"></a>__fpl_fcmp_Inf</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, fcmpi.o(x$fpl$fcmpinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmple
</UL>

<P><STRONG><a name="[89]"></a>__aeabi_fdiv</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, fdiv.o(x$fpl$fdiv))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointB
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointA
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_LinearInterpolate
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_WallToServo
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_PositionToAngle
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_AngleToPosition
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atanf
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_RecordPathStep
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_GetProgress
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_invalid
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
</UL>

<P><STRONG><a name="[158]"></a>_fdiv</STRONG> (Thumb, 384 bytes, Stack size 16 bytes, fdiv.o(x$fpl$fdiv), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[143]"></a>__aeabi_f2iz</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, ffix.o(x$fpl$ffix))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_f2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
</UL>

<P><STRONG><a name="[159]"></a>_ffix</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, ffix.o(x$fpl$ffix), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[dc]"></a>__aeabi_f2uiz</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, ffixu.o(x$fpl$ffixu))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_f2uiz
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_AngleToPosition
</UL>

<P><STRONG><a name="[15a]"></a>_ffixu</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, ffixu.o(x$fpl$ffixu), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[134]"></a>__aeabi_i2f</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, fflt_clz.o(x$fpl$fflt))
<BR><BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
</UL>

<P><STRONG><a name="[19b]"></a>_fflt</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, fflt_clz.o(x$fpl$fflt), UNUSED)

<P><STRONG><a name="[88]"></a>__aeabi_ui2f</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, fflt_clz.o(x$fpl$ffltu))
<BR><BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointB
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointA
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_LinearInterpolate
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_PositionToAngle
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_RecordPathStep
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_GetProgress
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
</UL>

<P><STRONG><a name="[19c]"></a>_ffltu</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, fflt_clz.o(x$fpl$ffltu), UNUSED)

<P><STRONG><a name="[7d]"></a>__aeabi_cfcmple</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, fleqf.o(x$fpl$fleqf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_cfcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_ValidatePath
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_IsWallPointValid
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_IsServoAngleValid
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_ClampWallPoint
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_ClampServoAngle
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_AngleToPosition
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_QuickCheck
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_CheckServos
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_ArePointsValid
</UL>

<P><STRONG><a name="[15b]"></a>_fcmple</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, fleqf.o(x$fpl$fleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmp_Inf
</UL>

<P><STRONG><a name="[15f]"></a>__fpl_fcmple_InfNaN</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fleqf.o(x$fpl$fleqf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_frcmple
</UL>

<P><STRONG><a name="[9d]"></a>__aeabi_fmul</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, fmul.o(x$fpl$fmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointB
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointA
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_LinearInterpolate
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_WallToServo
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_ServoToWall
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_CalculateDistance
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_PositionToAngle
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_AngleToPosition
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atanf
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_RecordPathStep
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_ShowStatus
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
</UL>

<P><STRONG><a name="[15d]"></a>_fmul</STRONG> (Thumb, 258 bytes, Stack size 16 bytes, fmul.o(x$fpl$fmul), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[154]"></a>__fpl_fnaninf</STRONG> (Thumb, 140 bytes, Stack size 8 bytes, fnaninf.o(x$fpl$fnaninf))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __fpl_fnaninf
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fmul
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmple
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ffixu
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fdiv
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsqrt
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_frnd
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ffix
</UL>

<P><STRONG><a name="[146]"></a>__fpl_fretinf</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fretinf.o(x$fpl$fretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fmul
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fdiv
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2f
</UL>

<P><STRONG><a name="[7e]"></a>__aeabi_cfrcmple</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, frleqf.o(x$fpl$frleqf))
<BR><BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_ValidatePath
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_IsWallPointValid
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_IsServoAngleValid
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_ClampWallPoint
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_ClampServoAngle
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servo_AngleToPosition
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_CheckServos
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_CheckGeometry
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoMovement_ArePointsValid
</UL>

<P><STRONG><a name="[15e]"></a>_frcmple</STRONG> (Thumb, 98 bytes, Stack size 0 bytes, frleqf.o(x$fpl$frleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmple_InfNaN
</UL>

<P><STRONG><a name="[142]"></a>_frnd</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, frnd.o(x$fpl$frnd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _frnd &rArr; __fpl_fnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
</UL>

<P><STRONG><a name="[135]"></a>__aeabi_frsub</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, faddsub_clz.o(x$fpl$frsb))
<BR><BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
</UL>

<P><STRONG><a name="[160]"></a>_frsb</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, faddsub_clz.o(x$fpl$frsb), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub1
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd1
</UL>

<P><STRONG><a name="[141]"></a>_fsqrt</STRONG> (Thumb, 272 bytes, Stack size 24 bytes, fsqrt.o(x$fpl$fsqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _fsqrt &rArr; __fpl_fnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrtf
</UL>

<P><STRONG><a name="[b8]"></a>__aeabi_fsub</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, faddsub_clz.o(x$fpl$fsub))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fsub
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointB
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StateMachine_RecordPointA
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Path_LinearInterpolate
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_ServoToWall
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_CalculateDistance
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atanf
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemDiag_CheckGeometry
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tanf
</UL>

<P><STRONG><a name="[162]"></a>_fsub</STRONG> (Thumb, 234 bytes, Stack size 16 bytes, faddsub_clz.o(x$fpl$fsub), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd1
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[43]"></a>_printf_fp_dec</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, printf1.o(x$fpl$printf1))
<BR><BR>[Stack]<UL><LI>Max Depth = 324<LI>Call Chain = _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_f
</UL>

<P><STRONG><a name="[14c]"></a>__fpl_return_NaN</STRONG> (Thumb, 100 bytes, Stack size 0 bytes, retnan.o(x$fpl$retnan))
<BR><BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_cmpreturn
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcheck_NaN1
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcheck_NaN1
</UL>

<P><STRONG><a name="[128]"></a>__ARM_scalbn</STRONG> (Thumb, 92 bytes, Stack size 0 bytes, scalbn.o(x$fpl$scalbn))
<BR><BR>[Calls]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcheck_NaN1
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
</UL>

<P><STRONG><a name="[130]"></a>__ARM_scalbnf</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, scalbnf.o(x$fpl$scalbnf))
<BR><BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcheck_NaN1
</UL>
<BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan
</UL>

<P><STRONG><a name="[163]"></a>__fpl_cmpreturn</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, trapv.o(x$fpl$trapveneer))
<BR><BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[eb]"></a>SetSysClock</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, system_stm32f10x.o(i.SetSysClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SetSysClock &rArr; SetSysClockTo72
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClockTo72
</UL>
<BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[ec]"></a>SetSysClockTo72</STRONG> (Thumb, 214 bytes, Stack size 12 bytes, system_stm32f10x.o(i.SetSysClockTo72))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SetSysClockTo72
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClock
</UL>

<P><STRONG><a name="[c1]"></a>__ARM_isinff</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, geometry.o(i.__ARM_isinff))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_ServoToWall
</UL>

<P><STRONG><a name="[c0]"></a>__ARM_isnanf</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, geometry.o(i.__ARM_isnanf))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Geometry_ServoToWall
</UL>

<P><STRONG><a name="[aa]"></a>USART2_Init</STRONG> (Thumb, 158 bytes, Stack size 32 bytes, bluetooth.o(i.USART2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = USART2_Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bluetooth_Init
</UL>

<P><STRONG><a name="[161]"></a>_fadd1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, faddsub_clz.o(x$fpl$fadd), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_frsb
</UL>

<P><STRONG><a name="[156]"></a>_fsub1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, faddsub_clz.o(x$fpl$fsub), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_frsb
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd
</UL>

<P><STRONG><a name="[65]"></a>_fp_digits</STRONG> (Thumb, 432 bytes, Stack size 96 bytes, _printf_fp_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 220<LI>Call Chain = _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_d2e
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[3c]"></a>_printf_input_char</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _printf_char_common.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_common.o(.text)
</UL>
<P><STRONG><a name="[151]"></a>_dadd1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, daddsub_clz.o(x$fpl$dadd), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_drsb
</UL>

<P><STRONG><a name="[149]"></a>_dsub1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, daddsub_clz.o(x$fpl$dsub), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_drsb
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
