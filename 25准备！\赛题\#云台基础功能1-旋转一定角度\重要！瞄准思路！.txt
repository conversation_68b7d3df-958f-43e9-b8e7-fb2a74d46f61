基础部分第二问：
目的：自选小车巡线路径中的某一位置，启动瞄准模块，启动上电后两秒内，预判激光落点，控制云台指向落点方向，打开激光器射向靶心。
硬件：42步进电机两自由度云台，电机驱动板，STM32F407ZGT6主控板，庐山派K230，20mw蓝紫色激光器，A4纯白UV纸制作的靶子。
场地设置：题目说明靶子可自带，提前设置好靶心与激光器光源处于同一水平面。
硬件组合描述：设定车头朝向为前方，寻迹小车上固定瞄准模块，瞄准模块由云台、激光器（正极接开发板某gpio引脚）、庐山派组成，其中水平自由度电机位于底面，竖直自由度电机与庐山派位于水平自由度电机上方且相对固定。激光器固定在竖直自由度电机的可旋转3D打印件上，可旋转3D打印件被机械的固定好使得激光器发出的激光永远水平（与题目要求擦边，要求二自由度云台，实际只控制水平自由度电机）。激光器和庐山派摄像头指向正前方。提前固定好激光器和摄像头，使竖直自由度电机旋转一周时摄像头拍到的激光轨迹与画面像素中心点所在竖直像素线重合（该描述能否理解？）
实现逻辑：将小车放于离靶子最近的正方形边线的中点，启动时无须云台控制摄像头画面中就有靶子。庐山派识别到靶子和靶心位置，判断靶心像素坐标在以画面像素中心为原点的平面坐标系的哪一个象限，而后庐山派向主控发送对应电机控制方向，由pid控制使旋转后靶心位于画面像素中心点所在竖直像素线上，而后gpio控制激光器打开0.5秒就关闭。完成第二问。
讨论思路，是否有可改进的逻辑，我没有限位开关，所以竖直自由度电机只能固定。分析开发步骤（在当前云台已经可以控制以某一固定速度旋转的基础上）。think ultra