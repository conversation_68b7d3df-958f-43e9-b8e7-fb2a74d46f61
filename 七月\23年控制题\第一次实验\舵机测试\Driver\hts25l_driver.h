/**
  ******************************************************************************
  * @file    hts25l_driver.h
  * <AUTHOR> Analysis Expert
  * @version V1.0
  * @date    2025-07-17
  * @brief   HTS-25L总线舵机驱动头文件
  ******************************************************************************
  */

#ifndef __HTS25L_DRIVER_H
#define __HTS25L_DRIVER_H

#include "stm32f4xx.h"

/* 协议常量定义 */
#define HTS25L_HEADER               0x55
#define HTS25L_BROADCAST_ID         0xFE

/* 指令定义 */
#define HTS25L_CMD_MOVE_TIME_WRITE      0x01
#define HTS25L_CMD_MOVE_TIME_READ       0x02
#define HTS25L_CMD_MOVE_TIME_WAIT_WRITE 0x07
#define HTS25L_CMD_MOVE_TIME_WAIT_READ  0x08
#define HTS25L_CMD_MOVE_START           0x0B
#define HTS25L_CMD_MOVE_STOP            0x0C
#define HTS25L_CMD_ID_WRITE             0x0D
#define HTS25L_CMD_ID_READ              0x0E
#define HTS25L_CMD_ANGLE_OFFSET_ADJUST  0x11
#define HTS25L_CMD_ANGLE_OFFSET_WRITE   0x12
#define HTS25L_CMD_ANGLE_OFFSET_READ    0x13
#define HTS25L_CMD_ANGLE_LIMIT_WRITE    0x14
#define HTS25L_CMD_ANGLE_LIMIT_READ     0x15
#define HTS25L_CMD_VIN_LIMIT_WRITE      0x16
#define HTS25L_CMD_VIN_LIMIT_READ       0x17
#define HTS25L_CMD_TEMP_MAX_LIMIT_WRITE 0x18
#define HTS25L_CMD_TEMP_MAX_LIMIT_READ  0x19
#define HTS25L_CMD_TEMP_READ            0x1A
#define HTS25L_CMD_VIN_READ             0x1B
#define HTS25L_CMD_POS_READ             0x1C
#define HTS25L_CMD_OR_MOTOR_MODE_WRITE  0x1D
#define HTS25L_CMD_OR_MOTOR_MODE_READ   0x1E
#define HTS25L_CMD_LOAD_OR_UNLOAD_WRITE 0x1F
#define HTS25L_CMD_LOAD_OR_UNLOAD_READ  0x20
#define HTS25L_CMD_LED_CTRL_WRITE       0x21
#define HTS25L_CMD_LED_CTRL_READ        0x22
#define HTS25L_CMD_LED_ERROR_WRITE      0x23
#define HTS25L_CMD_LED_ERROR_READ       0x24

/* 位置角度转换常量 */
#define HTS25L_POS_MIN              0
#define HTS25L_POS_MAX              1000
#define HTS25L_ANGLE_MAX_DEG        240.0f
#define HTS25L_MID_POS              500
#define HTS25L_MID_ANGLE_DEG        120.0f
#define HTS25L_STEP_30_DEG          125     // 30度对应的位置增量

/* 模式定义 */
#define HTS25L_MODE_SERVO           0       // 伺服模式
#define HTS25L_MODE_MOTOR           1       // 电机模式

/* 力矩控制定义 */
#define HTS25L_TORQUE_UNLOAD        0       // 卸载力矩
#define HTS25L_TORQUE_LOAD          1       // 加载力矩

/* 函数声明 */
void HTS25L_Init(void);
int HTS25L_MoveTimeWrite(uint8_t id, uint16_t pos, uint16_t time_ms);
int HTS25L_OrMotorModeWrite(uint8_t id, uint8_t mode, int16_t speed);
int HTS25L_LoadOrUnloadWrite(uint8_t id, uint8_t load);
int HTS25L_ReadPos(uint8_t id, uint16_t *pos);
int HTS25L_ReadTemp(uint8_t id, uint8_t *temp);
int HTS25L_ReadVin(uint8_t id, uint16_t *vin_mv);

/* 工具函数 */
uint16_t HTS25L_AngleDegToPos(float deg);
float HTS25L_PosToAngleDeg(uint16_t pos);

#endif /* __HTS25L_DRIVER_H */
