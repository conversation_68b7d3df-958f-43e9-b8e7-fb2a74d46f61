/**
  @page DAC_SignalsGeneration DAC Signals generation example
  
  @verbatim
  ******************** (C) COPYRIGHT 2016 STMicroelectronics *******************
  * @file    DAC/DAC_SignalsGeneration/readme.txt 
  * <AUTHOR> Application Team
  * @version V1.8.1
  * @date    27-January-2022
  * @brief   Description of the DAC Signals generation example.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2016 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  @endverbatim

@par Example Description 

This example provides a short description of how to use the DAC peripheral to 
generate several signals using DMA controller.
When the user presses the KEY push-button, DMA transfers the two selected 
waveforms to the DAC.
For each press on KEY button, 2 signals has been selected and can be monitored on  
the two DAC channels:
    - Escalator waveform (Channel 1) and Sine waveForm (Channel 2).
    - Noise waveform (Channel 1) and Triangle waveform (Channel 2).


@par Directory contents 
  
  - DAC/DAC_SignalsGeneration/system_stm32f4xx.c   STM32F4xx system clock configuration file
  - DAC/DAC_SignalsGeneration/stm32f4xx_conf.h     Library Configuration file
  - DAC/DAC_SignalsGeneration/stm32f4xx_it.c       Interrupt handlers
  - DAC/DAC_SignalsGeneration/stm32f4xx_it.h       Interrupt handlers header file
  - DAC/DAC_SignalsGeneration/main.c               Main program
  - DAC/DAC_SignalsGeneration/main.h               Main program header file


@par Hardware and Software environment
  
  - This example runs on STM32F405xx/407xx, STM32F415xx/417xx and STM32F427xx/437xx 
    devices.
    
  - This example has been tested with STMicroelectronics STM324xG-EVAL (STM32F40xx/
    STM32F41xx Devices) and STM32437I-EVAL (STM32F427xx/STM32F437xx Devices) evaluation 
    boards and can be easily tailored to any other supported device and development board.

  - STM324xG-EVAL and STM32437I-EVAL Set-up 	
     - Use KEY push-button connected to PG15.
     - Connect PA4 (DAC Channel1) and PA5 (DAC Channel2) pins to an oscilloscope.
       @note Make sure that JP31 is open.


@par How to use it ? 

In order to make the program work, you must do the following:
 - Copy all source files from this example folder to the template folder under
   Project\STM32F4xx_StdPeriph_Templates
 - Open your preferred toolchain 
 - Select the project workspace related to the used device 
   - If "STM32F40_41xxx" is selected as default project Add the following files in the project source list:
     - Utilities\STM32_EVAL\STM3240_41_G_EVAL\stm324xg_eval.c
        
   - If "STM32F427_437xx" is selected as default project Add the following files in the project source list:
     - Utilities\STM32_EVAL\STM324x7I_EVAL\stm324x7i_eval.c
             
 - Rebuild all files and load your image into target memory
 - Run the example
  
 
 */
 