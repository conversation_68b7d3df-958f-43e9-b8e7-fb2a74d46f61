# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

这是2025年全国大学生电子设计竞赛E题"简易自行瞄准装置"的瞄准模块开发项目。本项目专注于42步进电机组成的2自由度云台系统，实现激光精确瞄准功能。

## Repository Structure & Build Systems

### STM32项目结构 (Keil开发环境)
```
实验1：成功驱动/
├── USER/              # 用户应用层
│   ├── main.c         # 主程序入口
│   ├── Template.uvprojx # Keil工程文件
│   └── *.h            # 系统配置头文件
├── HAREWARE/          # 硬件抽象层
│   ├── ATD5984/       # 步进电机驱动器控制
│   ├── ADC/           # 模数转换器
│   ├── KEY/           # 按键输入
│   └── TIM/           # 定时器PWM控制
├── SYSTEM/            # 系统服务层
│   ├── delay/         # SysTick延时服务
│   ├── sys/           # 系统初始化和GPIO位操作
│   └── usart/         # 串口通信服务
├── FWLIB/             # STM32F4标准外设库
├── CORE/              # ARM Cortex-M4内核文件
└── OBJ/               # 编译输出目录
```

### Build Commands
- **编译**: 打开`Template.uvprojx`在Keil µVision 5中，按F7编译
- **清理**: 运行`keilkilll.bat`清除所有编译中间文件
- **输出**: 编译后的`.hex`文件位于`OBJ/`目录，用于STM32烧录

### Clean Command Details
`keilkilll.bat`会清理以下文件类型：
- 编译中间文件: *.crf, *.d, *.o
- 链接文件: *.axf, *.map, *.htm
- 调试文件: *.dep, JLinkLog.txt

## Architecture Overview

### 硬件控制架构
该项目实现了完整的2DOF云台控制系统：

**主控制器**: STM32F407ZGT6 @ 168MHz
- 高性能ARM Cortex-M4内核，带FPU
- 1MB Flash + 192KB RAM
- 丰富的外设资源和GPIO

**电机驱动系统**: D36A双路步进电机驱动器
- 核心芯片: ATD5984步进电机专用驱动IC  
- 细分控制: 支持1/16细分，实现0.1125°/步精度
- 电流控制: 8档可调(0.55A-1.44A)，支持不同负载
- 控制接口: STEP/DIR/ENABLE数字信号控制

### 关键引脚分配 (STM32F407ZGT6)
```c
// 步进电机A (水平轴)
PC8  → STEP-A  (TIM8_CH3 PWM脉冲输出)
PC13 → DIR-A   (方向控制GPIO)  
PD2  → SLEEP-A (使能控制GPIO，低电平使能)

// 步进电机B (垂直轴)  
PC9  → STEP-B  (TIM8_CH4 PWM脉冲输出)
PB12 → DIR-B   (方向控制GPIO)
PC12 → SLEEP-B (使能控制GPIO，低电平使能)

// 系统外设
PA9/PA10 → USART1 (串口通信，115200bps)
PC0      → ADC1_IN10 (电压监测，11倍分压)
PB14     → KEY (按键输入检测)
```

## Core Technical Components

### PWM控制系统 (HAREWARE/TIM/)
- **定时器**: TIM8高级定时器，双通道PWM输出
- **频率计算**: f = 168MHz / ((psc+1) * (arr+1)) = 3333Hz
- **占空比**: 固定50%，适配ATD5984驱动器要求
- **精度优势**: 168MHz主频提供高精度时基

### ATD5984驱动接口 (HAREWARE/ATD5984/)
核心控制函数：
- `ATD5984_Init()`: 初始化所有控制引脚
- `STEP12_PWM_Init()`: 配置双路PWM输出

D36A驱动器配置要求：
- 细分设置: 推荐1/16细分 (拨码开关123: 000)
- 电流设置: 推荐0.77A (拨码开关456: 100)  
- 控制逻辑: PWM上升沿有效，DIR高低电平切换方向

### 串口通信系统 (SYSTEM/usart/)
- **波特率**: 115200bps
- **功能**: printf重定向，实时状态监测
- **中断**: 支持接收中断处理

### ADC电压监测 (HAREWARE/ADC/)
- **通道**: PC0 → ADC1_IN10
- **分压比**: 1:11，监测12-24V供电电压
- **采样**: 多次采样平均滤波，提高精度

## Development Workflow & Best Practices

### 开发环境要求
- **IDE**: Keil µVision 5
- **编译器**: ARMCC v5.06或更高版本
- **调试器**: ST-Link V2/J-Link
- **库文件**: STM32F4标准外设库

### 代码开发规范
1. **中文注释**: 所有函数和关键变量使用详细中文注释
2. **模块化设计**: 硬件抽象层清晰分离，便于移植和维护
3. **错误处理**: ADC采样异常、串口通信错误等保护机制
4. **位带操作**: 利用Cortex-M4位带特性实现高效GPIO控制

### 调试验证方法
- **万用表**: 测量电源电压，验证供电正常
- **示波器**: 观察PWM波形，验证频率和占空比
- **串口工具**: 实时监测系统状态和电压值
- **电机测试**: 验证方向控制和转速响应

## Key Technical Breakthroughs

### 解决的关键问题
1. **电机抖动问题**: 
   - 原因: 42步进电机线序错误 + PWM占空比过低(0.7%)
   - 解决: 标准线序接线 + 50%占空比PWM信号

2. **引脚冲突优化**:
   - 将电机B方向控制从PE7改为PB12，便于接线
   - GPIO时钟配置和初始化相应更新

3. **系统稳定性**:
   - 多次ADC采样平均滤波
   - TIM2定时器10ms周期任务调度
   - 完善的错误处理和状态监测

## Competition Requirements & Performance

### 精度要求 (E题规格)
- **激光光斑直径**: ≤0.5cm
- **瞄准精度**: 距靶心≤2cm  
- **响应时间**: 静止2s内，运动4s内完成瞄准
- **控制范围**: A4纸大小目标(297×210mm)

### 性能指标
- **步进精度**: 0.1125°/步 (1/16细分)
- **PWM频率**: 3333Hz稳定输出
- **电压监测**: 实时11倍分压精度
- **通信速率**: 115200bps无丢包

## Integration Points

### K230视觉系统集成接口 (规划中)
- 预留串口通信协议，接收目标坐标
- 坐标系转换算法，将像素坐标转换为步进角度
- PID闭环控制，实现激光点精确追踪

### 激光器控制模块 (待集成)
- GPIO控制激光器开关
- PWM调制激光强度(可选)
- 安全保护机制

### 串口屏显示接口 (可扩展)
- TJC串口屏通信协议支持
- 实时状态显示: 电压、角度、目标坐标
- 参数调节界面

## Project Status & Next Steps

### 当前状态 ✅
- 双路步进电机成功驱动
- PWM频率和占空比优化完成  
- 电压监测系统正常工作
- 串口通信稳定运行
- 系统启动和基础控制验证通过

### 下一步开发计划
1. **坐标逆解算法**: 2D目标坐标到双轴步进角度转换
2. **K230视觉集成**: 目标检测和激光点识别
3. **PID闭环控制**: 激光点自动回中算法
4. **完整系统测试**: 精度验证和性能优化

## Notes for Future Development

- 所有GPIO操作使用位带技术，确保原子性和高效性
- PWM参数已优化，轻易不要修改，避免电机抖动复现
- D36A驱动器散热重要，确保风扇正常工作
- 线序标准: A+红, A-蓝, B+绿, B-黑，严格按标准接线
- 拨码开关设置：细分000（1/16），电流100（0.77A）为最佳配置
- 电源电压建议24V，确保驱动力充足