{
  "mcpServers": {
    "promptx": {
      "command": "npx",
      "args": [
        "-y",
        "-f",
        "--registry",
        "https://registry.npmjs.org",
        "dpml-prompt@beta",
        "mcp-server"
      ]
    }
  }
}





{
  "mcpServers": {
    "@wopal-mcp-server-hotnews": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "@wopal/mcp-server-hotnews"
      ]
    }
  }
}





{
  "mcpServers": {
    "playwright": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@executeautomation/playwright-mcp-server"
      ]
    }
  }
}



让我为您详细介绍当前可用的MCP工具及其功能：

🎯 PromptX专业角色系统 (6个工具)
1. promptx_init_promptx 🚀
功能：系统初始化和角色发现
用途：首次使用、刷新角色注册表、查看版本号
使用场景：系统启动时必须先调用
2. promptx_hello_promptx 🎭
功能：显示可用角色菜单
用途：查看所有可激活的专业角色清单
使用场景：选择合适的专家身份前
3. promptx_action_promptx ⚡
功能：激活指定专业角色
参数：role (角色ID，如 balance-car-expert)
用途：获得专家级思维和技能
使用场景：需要专业服务时
4. promptx_learn_promptx 🧠
功能：学习专业技能和思维模式
参数：resource (如 thought://creativity, execution://best-practice)
用途：快速掌握特定专业能力
使用场景：想要专业化成长时
5. promptx_recall_promptx 🔍
功能：回忆和检索历史经验
参数：query (检索关键词，可选)
用途：找回之前学会的技能和处理过的经验
使用场景：需要参考历史经验做决策时
6. promptx_remember_promptx 💾
功能：存储重要经验和知识
参数：content (要保存的内容), tags (标签，可选)
用途：永久记住有价值的经验和解决方案
使用场景：获得重要经验时应该存储


🌐 热点新闻获取 (1个工具)
7. get_hot_news__wopal-mcp-server-hotnews 📰
功能：获取各平台热门资讯
参数：sources (平台ID数组，1-9)
支持平台：知乎、36氪、百度、B站、微博、抖音、虎扑、豆瓣、IT新闻
使用场景：了解行业动态、技术趋势




🎭 Playwright自动化测试 (25个工具)
核心功能
playwright_navigate_playwright：网页导航
playwright_screenshot_playwright：截图
playwright_click_playwright：点击元素
playwright_fill_playwright：填写表单
playwright_get_visible_text_playwright：获取页面文本
高级功能
start_codegen_session_playwright：开始录制测试代码
playwright_evaluate_playwright：执行JavaScript
playwright_console_logs_playwright：获取控制台日志
playwright_expect_response_playwright：等待HTTP响应
📝 文件操作 (3个工具)
8. str-replace-editor ✏️
功能：编辑现有文件
支持：字符串替换、插入内容
限制：单次最多150行编辑
9. save-file 💾
功能：创建新文件
限制：最多300行内容
用途：保存代码、文档等
10. view 👀
功能：查看文件和目录
支持：正则搜索、行范围查看
用途：代码审查、文件浏览
🌍 网络功能 (3个工具)
11. web-search 🔍
功能：Google搜索
参数：query, num_results (1-10)
返回：Markdown格式结果
12. web-fetch 📄
功能：获取网页内容
返回：Markdown格式页面内容
13. open-browser 🌐
功能：在默认浏览器打开URL
注意：不要重复打开同一URL
💻 系统操作 (6个工具)
进程管理
launch-process：启动进程
read-process：读取进程输出
write-process：向进程写入
kill-process：终止进程
list-processes：列出所有进程
系统信息
read-terminal：读取VSCode终端输出
🔍 代码分析 (3个工具)
14. codebase-retrieval 🎯
功能：智能代码搜索和上下文分析
特点：世界级代码库上下文引擎
用途：理解代码结构、查找相关代码
15. diagnostics 🔧
功能：获取IDE错误和警告信息
用途：代码质量检查
16. remove-files 🗑️
功能：安全删除文件
特点：支持撤销操作
🧠 AI增强 (2个工具)
17. remember 💭
功能：长期记忆存储
用途：记住重要信息供后续使用
18. render-mermaid 📊
功能：渲染Mermaid图表
支持：流程图、时序图、类图等
特点：交互式图表，支持缩放