#ifndef __USART_H
#define __USART_H
#include "stdio.h"	
#include "stm32f4xx_conf.h"
#include "sys.h" 
//////////////////////////////////////////////////////////////////////////////////	 
//������ֻ��ѧϰʹ�ã�δ���������ɣ��������������κ���;
//Mini STM32������
//����1��ʼ��		   
//����ԭ��@ALIENTEK
//������̳:www.openedv.csom
//�޸�����:2011/6/14
//�汾��V1.4
//��Ȩ���У�����ؾ���
//Copyright(C) ����ԭ�� 2009-2019
//All rights reserved
//********************************************************************************
//V1.3�޸�˵�� 
//֧����Ӧ��ͬƵ���µĴ��ڲ���������.
//�����˶�printf��֧��
//�����˴��ڽ��������.
//������printf��һ���ַ���ʧ��bug
//V1.4�޸�˵��
//1,�޸Ĵ��ڳ�ʼ��IO��bug
//2,�޸���USART_RX_STA,ʹ�ô����������ֽ���Ϊ2��14�η�
//3,������USART_REC_LEN,���ڶ��崮������������յ��ֽ���(������2��14�η�)
//4,�޸���EN_USART1_RX��ʹ�ܷ�ʽ
////////////////////////////////////////////////////////////////////////////////// 	
#define USART_REC_LEN  			200  	//定义最大接收字节数 200
#define EN_USART1_RX 			1		//使能（1）/禁止（0）串口1接收

// 舵机协议相关定义
#define SERVO_FRAME_HEADER		0x55	//舵机数据包帧头
#define SERVO_MAX_PACKET_LEN	20		//舵机数据包最大长度
	  	
extern u8  USART_RX_BUF[USART_REC_LEN]; //接收缓冲,最大USART_REC_LEN个字节.末字节为换行符 
extern u16 USART_RX_STA;         		//接收状态标记
extern u8  SERVO_RX_BUF[SERVO_MAX_PACKET_LEN]; //舵机接收缓冲
extern u8  SERVO_RX_STA;				//舵机接收状态: 0-等待帧头1, 1-等待帧头2, 2-接收数据
extern u8  SERVO_RX_CNT;				//舵机接收计数器
extern u8  SERVO_PKT_LEN;				//舵机数据包长度	
//串口相关函数声明，请不要注释下面这句话
void uart_init(u32 bound);

// 舵机控制函数声明
void USART_SendArray(u8 *arr, u16 len);
u8 Servo_CalculateChecksum(u8 *data, u8 len);
void Servo_SetMotorMode(u8 id, s16 speed);
void Servo_LoadMotor(u8 id);
void Servo_MoveToPosition(u8 id, u16 position, u16 time);
u8 Servo_ProcessPacket(void);
#endif


