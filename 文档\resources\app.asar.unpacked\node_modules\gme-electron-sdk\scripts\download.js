const download = require('download');
const path = require('path');
const rimraf = require('rimraf');
const fs = require("fs-sync");

const {
    detectOS,
    detectOwnVersion,
} = require('./utils');


const buildDownloadInfo = () => {

    // build os label
    const osLabel = detectOS();

    // build version label
    const version = detectOwnVersion();

    // generate download sdkZipName
    const sdkName = 'sdk-' + osLabel + "-" + version + ".zip";

    // generate download url
    const sdkUrl = 'https://gme-electron-sdk-1256590279.cos.ap-guangzhou.myqcloud.com/' + version + '/' + sdkName;

    return {
        sdkZipName: sdkName,
        downloadUrl: sdkUrl
    };
};

const main = () => {
    const {
        sdkZipName,
        downloadUrl
    } = buildDownloadInfo();

    const outputDir = './temp';
    const buildDir = path.join(__dirname, '../build');

    rimraf(outputDir, err => {
        if (err) {
            console.log('copy end!');
            process.exit(1);
        }

        // start
        download(downloadUrl, outputDir, {
            strip: 0,
            extract: true
        }).then(() => {
            fs.copy(path.join(outputDir, './build'), buildDir)
            rimraf.sync(outputDir);
            console.log('copy end!');
        }).catch(err => {
            console.error(err);
        });
    });
};

main();