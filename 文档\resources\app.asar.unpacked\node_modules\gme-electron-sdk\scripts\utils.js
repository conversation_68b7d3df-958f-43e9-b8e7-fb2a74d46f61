const pkg = require('../package.json');
const {Platform} = require('./constant');

module.exports.detectOS = () => {
    const platform = process.platform;
    const arch = process.arch;
    if (platform === 'darwin') {
        return Platform.MACOS;
    } else if (platform === 'win32') {
        if (arch === 'x64') {
            return Platform.WINDOWS64;
        } else {
            return Platform.WINDOWS32;
        }
    }
    return Platform.UNSUPPORTED;
};

module.exports.detectOwnVersion = () => {
    return pkg.version
};
