#include "usart.h"

// 串口初始化 (支持半双工总线通信)
void USART1_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;

    // 打开 GPIOA 时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
    // 打开 USART1 时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1, ENABLE);

    // 配置 PA9 (USART1_TX)
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9;       // TX
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;  // 复用推挽输出
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    // 配置 PA10 (USART1_RX)
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10;      // RX
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING;  // 浮空输入
    GPIO_Init(GPIOA, &GPIO_InitStructure);

#if USE_RS485_DE_CONTROL
    // 配置 PA8 作为发送使能控制引脚 (RS485半双工控制)
    GPIO_InitStructure.GPIO_Pin = USART_DE_PIN;     // DE (Driver Enable)
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP; // 推挽输出
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(USART_DE_PORT, &GPIO_InitStructure);

    // 初始状态设为接收模式
    USART_DE_LOW();
#endif

    // USART1 配置 (总线舵机标准配置)
    USART_InitStructure.USART_BaudRate = USART_BAUD_RATE;    // 115200 bps
    USART_InitStructure.USART_WordLength = USART_WordLength_8b; // 8位数据位
    USART_InitStructure.USART_StopBits = USART_StopBits_1;   // 1位停止位
    USART_InitStructure.USART_Parity = USART_Parity_No;      // 无奇偶校验
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;  // 无硬件流控
    USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;  // 发送和接收模式

    USART_Init(USART1, &USART_InitStructure);

    // 启动 USART1
    USART_Cmd(USART1, ENABLE);
}

/**
  * 函    数：等待发送完成
  * 参    数：无
  * 返 回 值：无
  * 说    明：等待USART发送完成，用于半双工控制
  */
void USART1_WaitTransmitComplete(void)
{
    // 等待发送完成标志
    while (USART_GetFlagStatus(USART1, USART_FLAG_TC) == RESET);
}

/**
  * 函    数：串口发送一个字节 (带半双工控制)
  * 参    数：byte 要发送的字节
  * 返 回 值：无
  * 说    明：发送单个字节，自动控制发送使能
  */
void USART1_SendByte(uint8_t byte)
{
    // 切换到发送模式
    USART_DE_HIGH();

    // 等待发送缓冲区为空
    while (USART_GetFlagStatus(USART1, USART_FLAG_TXE) == RESET);

    // 发送数据
    USART_SendData(USART1, byte);

    // 等待发送完成
    while (USART_GetFlagStatus(USART1, USART_FLAG_TC) == RESET);

    // 切换回接收模式
    USART_DE_LOW();
}

/**
  * 函    数：串口发送多个字节 (带半双工控制)
  * 参    数：buffer 数据缓冲区指针
  *          length 数据长度
  * 返 回 值：无
  * 说    明：发送多个字节，优化的半双工控制
  */
void USART1_SendBuffer(uint8_t* buffer, uint16_t length)
{
    if (length == 0) return;

    // 切换到发送模式
    USART_DE_HIGH();

    // 发送所有字节
    for (uint16_t i = 0; i < length; i++) {
        // 等待发送缓冲区为空
        while (USART_GetFlagStatus(USART1, USART_FLAG_TXE) == RESET);

        // 发送数据
        USART_SendData(USART1, buffer[i]);

        // 字节间微小延时 (总线舵机时序要求)
        for (volatile int j = 0; j < 100; j++);
    }

    // 等待最后一个字节发送完成
    while (USART_GetFlagStatus(USART1, USART_FLAG_TC) == RESET);

    // 切换回接收模式
    USART_DE_LOW();
}

// 串口接收一个字节
uint8_t USART1_ReceiveByte(void) {
    // 等待直到接收数据准备好
    while (USART_GetFlagStatus(USART1, USART_FLAG_RXNE) == RESET);
    // 返回接收到的数据
    return USART_ReceiveData(USART1);
}

// 串口接收多个字节
void USART1_ReceiveBuffer(uint8_t* buffer, uint16_t length) {
    for (uint16_t i = 0; i < length; i++) {
        buffer[i] = USART1_ReceiveByte();
    }
}
