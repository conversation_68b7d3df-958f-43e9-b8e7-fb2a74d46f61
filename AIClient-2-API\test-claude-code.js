#!/usr/bin/env node

// 测试 Claude Code 连接到 Kiro 的脚本
const axios = require('axios');

async function testClaudeCodeConnection() {
  console.log('🧪 测试 Claude Code 连接到 Kiro...\n');

  // 设置环境变量（临时）
  process.env.ANTHROPIC_BASE_URL = 'http://127.0.0.1:3000';
  process.env.ANTHROPIC_AUTH_TOKEN = '123456';

  console.log('📋 配置信息:');
  console.log(`   Base URL: ${process.env.ANTHROPIC_BASE_URL}`);
  console.log(`   Auth Token: ${process.env.ANTHROPIC_AUTH_TOKEN}`);
  console.log('');

  try {
    // 测试健康检查
    console.log('🔍 1. 测试服务器健康状态...');
    const healthResponse = await axios.get('http://127.0.0.1:3000/health');
    console.log('✅ 服务器健康检查通过');
    console.log(`   状态: ${healthResponse.data.status}`);
    console.log(`   提供商: ${healthResponse.data.provider}`);
    console.log('');

    // 测试模型列表
    console.log('🔍 2. 测试模型列表...');
    const modelsResponse = await axios.get('http://127.0.0.1:3000/v1/models', {
      headers: { 'Authorization': 'Bearer 123456' }
    });
    console.log('✅ 模型列表获取成功');
    console.log(`   可用模型: ${modelsResponse.data.data.map(m => m.id).join(', ')}`);
    console.log('');

    // 测试聊天功能
    console.log('🔍 3. 测试聊天功能...');
    const chatResponse = await axios.post('http://127.0.0.1:3000/v1/chat/completions', {
      model: 'claude-sonnet-4',
      messages: [
        { role: 'user', content: '你好，这是一个测试消息。请简短回复确认你收到了。' }
      ],
      max_tokens: 100
    }, {
      headers: { 
        'Authorization': 'Bearer 123456',
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ 聊天功能测试成功');
    console.log(`   模型: ${chatResponse.data.model}`);
    console.log(`   响应: ${chatResponse.data.choices[0].message.content.substring(0, 100)}...`);
    console.log('');

    console.log('🎉 所有测试通过！Claude Code 现在可以使用 Kiro 的 Claude 模型了！');
    console.log('');
    console.log('📝 使用说明:');
    console.log('1. 确保 AIClient-2-API 服务器正在运行');
    console.log('2. 在 Claude Code 中设置环境变量:');
    console.log('   ANTHROPIC_BASE_URL=http://127.0.0.1:3000');
    console.log('   ANTHROPIC_AUTH_TOKEN=123456');
    console.log('3. 重启 Claude Code 或 VS Code');
    console.log('4. 开始使用 Kiro 的 Claude Sonnet 4 模型！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error(`   HTTP状态: ${error.response.status}`);
      console.error(`   响应: ${JSON.stringify(error.response.data, null, 2)}`);
    }
  }
}

// 运行测试
testClaudeCodeConnection();
