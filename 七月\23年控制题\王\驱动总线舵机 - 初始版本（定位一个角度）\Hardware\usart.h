#ifndef __USART_H
#define __USART_H

#include "stm32f10x.h"

// 串口波特率 (总线舵机标准波特率115200)
#define USART_BAUD_RATE 115200

// 发送使能控制引脚定义 (用于RS485半双工控制)
// 如果不使用RS485驱动板，设为0禁用DE控制
#define USE_RS485_DE_CONTROL  0  // 设为0禁用DE控制，1启用DE控制

#if USE_RS485_DE_CONTROL
#define USART_DE_PORT    GPIOA
#define USART_DE_PIN     GPIO_Pin_8
#define USART_DE_HIGH()  GPIO_SetBits(USART_DE_PORT, USART_DE_PIN)
#define USART_DE_LOW()   GPIO_ResetBits(USART_DE_PORT, USART_DE_PIN)
#else
#define USART_DE_HIGH()  // 空操作
#define USART_DE_LOW()   // 空操作
#endif

// 串口初始化函数
void USART1_Init(void);

// 串口发送一个字节 (带发送使能控制)
void USART1_SendByte(uint8_t byte);

// 串口发送多个字节 (带发送使能控制)
void USART1_SendBuffer(uint8_t* buffer, uint16_t length);

// 等待发送完成
void USART1_WaitTransmitComplete(void);

// 串口接收一个字节
uint8_t USART1_ReceiveByte(void);

// 串口接收多个字节
void USART1_ReceiveBuffer(uint8_t* buffer, uint16_t length);

#endif
