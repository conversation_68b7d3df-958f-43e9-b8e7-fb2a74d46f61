#ifndef __MANUAL_RECORD_H
#define __MANUAL_RECORD_H

#include "stdint.h"
#include "Geometry.h"
#include "Servo.h"
#include "Timer.h"

// 手动记录状态定义
typedef enum {
    RECORD_STATE_IDLE = 0,          // 空闲状态
    RECORD_STATE_UNLOADING,         // 正在卸载舵机
    RECORD_STATE_MANUAL_ADJUST,     // 手动调整中
    RECORD_STATE_READING,           // 正在读取位置
    RECORD_STATE_LOADING,           // 正在加载舵机
    RECORD_STATE_COMPLETE,          // 记录完成
    RECORD_STATE_ERROR              // 记录错误
} RecordState_t;

// 手动记录配置参数
#define RECORD_UNLOAD_DELAY_MS      500     // 卸载延时
#define RECORD_MANUAL_TIMEOUT_MS    10000   // 手动调整超时 (10秒)
#define RECORD_READ_RETRY_COUNT     3       // 读取重试次数
#define RECORD_LOAD_DELAY_MS        300     // 加载延时
#define RECORD_STABILIZE_DELAY_MS   200     // 稳定延时

// 手动记录结果结构体
typedef struct {
    uint8_t success;                // 记录是否成功
    WallPoint_t wall_point;         // 墙面坐标
    ServoAngle_t servo_angle;       // 舵机角度
    uint32_t record_time;           // 记录时间戳
    uint8_t point_id;               // 点位ID (1=A点, 2=B点)
    RecordState_t final_state;      // 最终状态
} RecordResult_t;

// 手动记录过程控制结构体
typedef struct {
    RecordState_t current_state;    // 当前状态
    uint8_t point_id;               // 要记录的点位ID
    uint32_t state_start_time;      // 状态开始时间
    uint32_t manual_start_time;     // 手动调整开始时间
    uint8_t retry_count;            // 重试计数
    uint8_t user_confirmed;         // 用户确认标志
    RecordResult_t result;          // 记录结果
} ManualRecordProcess_t;

// 错误代码定义
typedef enum {
    RECORD_OK = 0,
    RECORD_ERROR_TIMEOUT,
    RECORD_ERROR_SERVO_COMM,
    RECORD_ERROR_GEOMETRY_CALC,
    RECORD_ERROR_USER_CANCEL,
    RECORD_ERROR_INVALID_PARAM,
    RECORD_ERROR_SYSTEM_BUSY
} RecordError_t;

// 手动记录主要接口
RecordError_t ManualRecord_Start(ManualRecordProcess_t* process, uint8_t point_id);
RecordError_t ManualRecord_Update(ManualRecordProcess_t* process);
RecordError_t ManualRecord_Cancel(ManualRecordProcess_t* process);
uint8_t ManualRecord_IsComplete(ManualRecordProcess_t* process);
RecordResult_t ManualRecord_GetResult(ManualRecordProcess_t* process);

// 状态管理函数
void ManualRecord_Init(ManualRecordProcess_t* process);
RecordError_t ManualRecord_TransitionTo(ManualRecordProcess_t* process, RecordState_t new_state);
const char* ManualRecord_GetStateString(RecordState_t state);

// 核心功能函数
RecordError_t ManualRecord_UnloadServos(void);
RecordError_t ManualRecord_LoadServos(void);
RecordError_t ManualRecord_ReadCurrentPosition(ServoAngle_t* servo_angle, WallPoint_t* wall_point);
RecordError_t ManualRecord_ValidatePosition(WallPoint_t wall_point);

// 用户交互函数
uint8_t ManualRecord_WaitUserAdjustment(ManualRecordProcess_t* process);
uint8_t ManualRecord_CheckUserConfirmation(void);
void ManualRecord_ShowInstructions(uint8_t point_id);
void ManualRecord_ShowProgress(ManualRecordProcess_t* process);

// 数据存储函数
RecordError_t ManualRecord_SavePointData(uint8_t point_id, WallPoint_t wall_point, ServoAngle_t servo_angle);
RecordError_t ManualRecord_LoadPointData(uint8_t point_id, WallPoint_t* wall_point, ServoAngle_t* servo_angle);
uint8_t ManualRecord_IsPointSaved(uint8_t point_id);

// 质量检查函数
RecordError_t ManualRecord_CheckPositionQuality(ServoAngle_t servo_angle, WallPoint_t wall_point);
float ManualRecord_CalculatePositionAccuracy(ServoAngle_t servo_angle, WallPoint_t wall_point);
uint8_t ManualRecord_IsPositionReasonable(WallPoint_t wall_point);

// 调试和监控函数
void ManualRecord_PrintProcess(ManualRecordProcess_t* process);
void ManualRecord_PrintResult(RecordResult_t result);
uint32_t ManualRecord_GetProcessTime(ManualRecordProcess_t* process);

#endif
