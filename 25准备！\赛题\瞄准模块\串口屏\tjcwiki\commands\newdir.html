<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>newdir-创建文件夹 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="deldir-删除文件夹" href="deldir.html" />
    <link rel="prev" title="findfile-查找文件" href="findfile.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../widgets/index.html">控件详解</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">基本指令集</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="index.html#id2">常用指令集</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id3">高级指令</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html#id4">文件操作指令</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="twfile.html">twfile-单片机发送文件给串口屏</a></li>
<li class="toctree-l3"><a class="reference internal" href="rdfile.html">rdfile-串口屏发送文件给单片机</a></li>
<li class="toctree-l3"><a class="reference internal" href="newfile.html">newfile-创建文件</a></li>
<li class="toctree-l3"><a class="reference internal" href="delfile.html">delfile-删除文件</a></li>
<li class="toctree-l3"><a class="reference internal" href="refile.html">refile-重命名文件</a></li>
<li class="toctree-l3"><a class="reference internal" href="findfile.html">findfile-查找文件</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">newdir-创建文件夹</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#newdir-1">newdir-示例1</a></li>
<li class="toctree-l4"><a class="reference internal" href="#newdir-2">newdir-示例2</a></li>
<li class="toctree-l4"><a class="reference internal" href="#newdir-3">newdir-示例3</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id1">newdir指令-样例工程下载</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="deldir.html">deldir-删除文件夹</a></li>
<li class="toctree-l3"><a class="reference internal" href="redir.html">redir-重命名文件夹</a></li>
<li class="toctree-l3"><a class="reference internal" href="finddir.html">finddir-查找文件夹</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id5">不常用指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#crc">CRC校验指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#gui">GUI绘图指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#hmi">HMI颜色代号表</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">指令集</a> &raquo;</li>
      <li>newdir-创建文件夹</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="newdir">
<h1>newdir-创建文件夹<a class="headerlink" href="#newdir" title="此标题的永久链接"></a></h1>
<p>仅X2、X3、X5系列支持</p>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>创建文件夹时文件夹名不允许包含以下字符   \ / : * ? ” &lt; &gt; |</p>
</div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">newdir</span> <span class="nb">dir</span>

<span class="nb">dir</span><span class="p">:</span><span class="n">文件夹目录</span><span class="p">(</span><span class="n">如</span><span class="p">:</span><span class="s2">&quot;sd0/newfolder/&quot;</span><span class="p">)</span>
</pre></div>
</div>
<section id="newdir-1">
<h2>newdir-示例1<a class="headerlink" href="#newdir-1" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//在SD卡根目录中创建一个名称为a的子目录</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">newdir</span><span class="w"> </span><span class="s">&quot;sd0/a/&quot;</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/newdir_1.jpg" src="../_images/newdir_1.jpg" />
</section>
<section id="newdir-2">
<h2>newdir-示例2<a class="headerlink" href="#newdir-2" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//在SD卡根目录中创建一个名称为picture的子目录</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">newdir</span><span class="w"> </span><span class="s">&quot;sd0/picture/&quot;</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/newdir_2.jpg" src="../_images/newdir_2.jpg" />
</section>
<section id="newdir-3">
<h2>newdir-示例3<a class="headerlink" href="#newdir-3" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//在SD卡根目录中创建一个名称为picture的子目录</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">t0</span><span class="p">.</span><span class="n">txt</span><span class="o">=</span><span class="s">&quot;sd0/picture/&quot;</span><span class="w"></span>
<span class="linenos">3</span><span class="w"> </span><span class="n">newdir</span><span class="w"> </span><span class="n">t0</span><span class="p">.</span><span class="n">txt</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/newdir_3.jpg" src="../_images/newdir_3.jpg" />
<p>请保证t0的txt_maxl足够大，否则会提示操作文件失败。</p>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>内存文件系统不支持子目录，SD卡支持子目录，目录路径必须以”/”结尾。</p>
<p>在创建或文件夹前请先使用finddir指令检查该文件夹是否存在。</p>
<p>在某个文件夹下创建子文件夹，需保证父文件夹已存在。</p>
</div>
</section>
<section id="id1">
<h2>newdir指令-样例工程下载<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/文件浏览器控件/文件浏览器v2.0.HMI">《文件浏览器v2.0》演示工程下载</a></p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="findfile.html" class="btn btn-neutral float-left" title="findfile-查找文件" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="deldir.html" class="btn btn-neutral float-right" title="deldir-删除文件夹" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>