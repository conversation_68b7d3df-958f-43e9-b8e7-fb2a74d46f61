/**
  ******************************************************************************
  * @file    button_led_example.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024-07-17
  * @brief   按键控制LED示例代码
  ******************************************************************************
  * @attention
  * 
  * 这是一个完整的按键控制LED示例，展示如何使用模板进行开发
  * 将此代码替换main.c中的主循环部分即可实现按键控制LED功能
  * 
  ******************************************************************************
  */

// 将以下代码替换main.c中的while(1)循环内容

while (1)
{
    // 检测K0按键（PE4）
    if(GPIO_ReadInputDataBit(GPIOE, GPIO_Pin_4) == 0)  // 按键按下为低电平
    {
        Delay_ms(20);  // 消抖
        if(GPIO_ReadInputDataBit(GPIOE, GPIO_Pin_4) == 0)  // 再次确认按键按下
        {
            // 等待按键释放
            while(GPIO_ReadInputDataBit(GPIOE, GPIO_Pin_4) == 0);
            
            // 切换LED0状态
            if(GPIO_ReadOutputDataBit(GPIOF, GPIO_Pin_9) == 1)
            {
                GPIO_ResetBits(GPIOF, GPIO_Pin_9);  // 点亮LED0
            }
            else
            {
                GPIO_SetBits(GPIOF, GPIO_Pin_9);    // 熄灭LED0
            }
        }
    }
    
    // 检测K1按键（PE3）
    if(GPIO_ReadInputDataBit(GPIOE, GPIO_Pin_3) == 0)  // 按键按下为低电平
    {
        Delay_ms(20);  // 消抖
        if(GPIO_ReadInputDataBit(GPIOE, GPIO_Pin_3) == 0)  // 再次确认按键按下
        {
            // 等待按键释放
            while(GPIO_ReadInputDataBit(GPIOE, GPIO_Pin_3) == 0);
            
            // 切换LED1状态
            if(GPIO_ReadOutputDataBit(GPIOF, GPIO_Pin_10) == 1)
            {
                GPIO_ResetBits(GPIOF, GPIO_Pin_10);  // 点亮LED1
            }
            else
            {
                GPIO_SetBits(GPIOF, GPIO_Pin_10);    // 熄灭LED1
            }
        }
    }
}

/**
  * 使用说明：
  * 1. 将上述while循环代码替换main.c中的主循环
  * 2. 编译下载到开发板
  * 3. 按下K0按键控制LED0，按下K1按键控制LED1
  * 4. 每次按键都会切换对应LED的亮灭状态
  */
