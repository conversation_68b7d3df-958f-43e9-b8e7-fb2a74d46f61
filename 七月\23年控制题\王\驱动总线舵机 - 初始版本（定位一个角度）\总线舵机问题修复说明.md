# 总线舵机问题修复说明

## 🔍 **问题分析**

### **原始问题**
- 上电后舵机不动作
- 串口接线正确，舵机ID设置正确，电源供电正常
- 需要实现半双工异步串行总线通讯，波特率115200bps

### **发现的问题**
1. **缺少半双工控制**: 总线舵机需要发送使能信号控制
2. **协议格式错误**: 原始代码协议格式不完整
3. **时序控制不当**: 缺少必要的延时和同步
4. **校验和算法错误**: 校验和计算方法不正确

## 🔧 **修复内容**

### **1. 半双工通信控制**

#### **新增发送使能控制**
```c
// 发送使能控制引脚定义 (PA8)
#define USART_DE_PORT    GPIOA
#define USART_DE_PIN     GPIO_Pin_8
#define USART_DE_HIGH()  GPIO_SetBits(USART_DE_PORT, USART_DE_PIN)
#define USART_DE_LOW()   GPIO_ResetBits(USART_DE_PORT, USART_DE_PIN)
```

#### **优化发送函数**
```c
void USART1_SendBuffer(uint8_t* buffer, uint16_t length) 
{
    // 切换到发送模式
    USART_DE_HIGH();
    
    // 发送所有字节 (带字节间延时)
    for (uint16_t i = 0; i < length; i++) {
        while (USART_GetFlagStatus(USART1, USART_FLAG_TXE) == RESET);
        USART_SendData(USART1, buffer[i]);
        for (volatile int j = 0; j < 100; j++);  // 字节间延时
    }
    
    // 等待发送完成，切换回接收模式
    while (USART_GetFlagStatus(USART1, USART_FLAG_TC) == RESET);
    USART_DE_LOW();
}
```

### **2. 协议格式修正**

#### **标准总线舵机协议**
```
| 字节 | 名称 | 说明 |
|------|------|------|
| 0 | Header1 | 0xFF (包头1) |
| 1 | Header2 | 0xFF (包头2) |
| 2 | ID | 舵机ID |
| 3 | Length | 数据长度 |
| 4 | Instruction | 指令 |
| 5-N | Data | 数据 |
| N+1 | Checksum | 校验和 |
```

#### **位置控制命令示例**
```c
// ID1舵机90度命令
0xFF 0xFF 0x01 0x05 0x03 0x77 0x01 0x00 0x00 [校验和]

解析:
- 0xFF 0xFF: 包头
- 0x01: 舵机ID1  
- 0x05: 数据长度(指令+数据+校验和)
- 0x03: 写位置指令
- 0x77 0x01: 位置值375 (小端格式)
- 0x00 0x00: 时间参数(可选)
- [校验和]: 计算得出
```

### **3. 校验和算法修正**

#### **正确的校验和计算**
```c
uint8_t Servo_CalculateChecksum(uint8_t* data, uint8_t length)
{
    uint8_t checksum = 0;
    for (uint8_t i = 0; i < length; i++) {
        checksum += data[i];
    }
    return (~checksum) & 0xFF;  // 取反作为校验和
}
```

### **4. 时序优化**

#### **关键时序点**
- **初始化延时**: 串口初始化后等待100ms
- **字节间延时**: 发送字节间添加微延时
- **命令间延时**: 命令间等待10ms
- **运动等待**: 舵机运动后等待足够时间

## 🔌 **硬件连接更新**

### **新增PA8发送使能控制**
```
STM32F103C8T6    →    总线舵机/RS485转换器
PA9 (USART1_TX)  →    A+ (差分信号正)
PA10 (USART1_RX) →    A- (差分信号负) [可选]
PA8 (DE控制)     →    DE/RE (发送使能)
GND              →    GND
5V               →    VCC
```

### **如果使用TTL电平直连**
```
STM32F103C8T6    →    总线舵机
PA9 (USART1_TX)  →    信号线 (黄色/白色)
PA8 (DE控制)     →    悬空 (或连接到信号线)
GND              →    GND (黑色/棕色)
5V               →    VCC (红色)
```

## 📊 **测试验证**

### **1. 信号测试**
使用示波器检查PA9引脚输出：
- **波特率**: 115200 bps
- **数据格式**: 8N1
- **包头**: 0xFF 0xFF
- **DE控制**: PA8在发送时为高电平

### **2. 命令验证**
**ID1舵机90度命令**:
```
发送: FF FF 01 05 03 77 01 00 00 [校验和]
```

**ID2舵机120度命令**:
```
发送: FF FF 02 05 03 F4 01 00 00 [校验和]
```

### **3. 运行测试**
1. 上电后OLED显示系统信息
2. 2秒后开始发送命令
3. ID1舵机应转到90度位置
4. ID2舵机应转到120度位置
5. LED指示命令完成
6. 每5秒重复一次测试

## ⚠️ **注意事项**

### **1. 硬件要求**
- **电源**: 确保5V电源能提供足够电流 (每舵机1-2A)
- **信号**: 如果距离较远，建议使用RS485转换器
- **接地**: 必须连接公共地线

### **2. 软件配置**
- **波特率**: 必须设置为115200 bps
- **舵机ID**: 确保舵机已正确设置ID (1和2)
- **协议**: 严格按照总线舵机协议格式

### **3. 调试方法**
- **示波器**: 监控PA9的串口信号
- **逻辑分析仪**: 分析完整的数据包
- **LED指示**: 观察系统运行状态
- **OLED显示**: 查看系统状态信息

## 🚀 **性能改进**

### **1. 通信可靠性**
- 添加了发送使能控制
- 优化了字节间时序
- 改进了校验和算法

### **2. 协议兼容性**
- 支持标准总线舵机协议
- 兼容多种舵机型号
- 支持位置和时间控制

### **3. 系统稳定性**
- 增加了初始化延时
- 添加了错误处理
- 优化了时序控制

## 📈 **预期效果**

修复后的系统应该能够：
1. ✅ 正确初始化串口通信
2. ✅ 成功控制ID1舵机到90度
3. ✅ 成功控制ID2舵机到120度
4. ✅ 稳定的半双工通信
5. ✅ 可靠的命令传输
6. ✅ 正确的时序控制

---
*如果问题仍然存在，请检查舵机ID设置、电源供电和信号连接。*
