#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "OLED.h"
#include "Servo.h"
#include "LED.h"
#include "Key.h"
#include "Geometry.h"
#include "StateMachine.h"
#include "ManualRecord.h"
#include "AutoMovement.h"
#include "SystemDiagnostics.h"
#include "Timer.h"
#include "Bluetooth.h"                   // 新增蓝牙模块
#include <stdio.h>
#include <string.h>

// 激光云台控制系统 - 最终集成版本 v1.0
int main(void)
{
	/*模块初始化*/
	OLED_Init();		//OLED初始化
	LED_Init();			//LED初始化
	Servo_Init();		//总线舵机初始化
	Key_Init();			//按键初始化
	Bluetooth_Init();	//蓝牙模块初始化

	/*显示启动信息*/
	OLED_ShowString(1, 1, "Laser Gimbal");
	OLED_ShowString(2, 1, "System v1.1");
	OLED_ShowString(3, 1, "Bluetooth OK");
	OLED_ShowString(4, 1, "Please wait...");

	// 延时等待系统稳定
	Delay_ms(1000);

	// 舵机扭矩加载和初始化
	OLED_ShowString(3, 1, "Servo Init...");

	// 加载舵机扭矩（重要！）
	ServoError_t servo_init_error1 = Servo_SetTorqueEnable(SERVO_PAN_ID, 1);
	ServoError_t servo_init_error2 = Servo_SetTorqueEnable(SERVO_TILT_ID, 1);

	if (servo_init_error1 == SERVO_OK && servo_init_error2 == SERVO_OK) {
		OLED_ShowString(3, 1, "Servo OK");
		Delay_ms(500);

		// 设置舵机到中位
		Servo_SetPosition(SERVO_PAN_ID, 120.0f);
		Delay_ms(200);
		Servo_SetPosition(SERVO_TILT_ID, 120.0f);
		Delay_ms(500);
	} else {
		OLED_ShowString(3, 1, "Servo FAIL");
		OLED_ShowString(4, 1, "Check Connect");
		Delay_ms(2000);
	}

	// 初始化所有模块
	LaserGimbalState_t gimbal_state;
	StateMachine_Init(&gimbal_state);

	AutoMovementControl_t auto_ctrl;
	AutoMovement_Init(&auto_ctrl);

	SystemDiagnostics_t system_diag;
	SystemDiag_Init(&system_diag);

	// 运行系统自检
	OLED_ShowString(3, 1, "Self Check...");
	LED_ON();

	DiagnosticLevel_t diag_result = SystemDiag_RunFullCheck(&system_diag);

	if (diag_result == DIAG_OK) {
		OLED_ShowString(1, 1, "System Ready");
		OLED_ShowString(2, 1, "All OK");
		OLED_ShowString(3, 1, "PB0:Record");
		OLED_ShowString(4, 1, "PB1:Auto Move");
	} else {
		OLED_ShowString(1, 1, "System Error");
		OLED_ShowString(2, 1, "Check Failed");
		SystemDiag_ShowErrors(&system_diag);
		while(1) {
			// 系统错误，停止运行
			LED_ON();
			Delay_ms(500);
			LED_OFF();
			Delay_ms(500);
		}
	}

	LED_OFF();
	Delay_ms(2000);

	// 激光云台完整系统主循环 - 集成诊断监控和按需蓝牙报告
	uint32_t last_diag_time = Timer_GetTick();
	uint8_t diag_mode = 0;  // 0=正常模式, 1=诊断模式
	static uint32_t both_pressed_time = 0;  // 双键按下时间记录

	while (1)
	{
		// 检查是否进入诊断模式 (同时按下两个按键)
		if (Key_IsPressed(KEY_RECORD) && Key_IsPressed(KEY_TRIGGER)) {
			if (both_pressed_time == 0) {
				both_pressed_time = Timer_GetTick();
			} else if (Timer_IsTimeout(both_pressed_time, 2000)) {  // 按住2秒
				diag_mode = !diag_mode;
				both_pressed_time = 0;

				if (diag_mode) {
					OLED_ShowString(1, 1, "Diag Mode ON");
					SystemDiag_ShowStatus(&system_diag);
				} else {
					OLED_ShowString(1, 1, "Normal Mode");
				}

				// 清除按键事件
				Key_ClearEvent(KEY_RECORD);
				Key_ClearEvent(KEY_TRIGGER);
				Delay_ms(1000);
			}
		} else {
			// 重置按键计时 - 使用上面定义的变量
			both_pressed_time = 0;
		}

		// 诊断模式处理
		if (diag_mode) {
			// 诊断模式下的按键处理
			if (Key_IsClicked(KEY_RECORD)) {
				SystemDiag_ShowPerformance(&system_diag);
				Key_ClearEvent(KEY_RECORD);
			}

			if (Key_IsClicked(KEY_TRIGGER)) {
				SystemDiag_ShowErrors(&system_diag);
				Key_ClearEvent(KEY_TRIGGER);
			}

			// 定期快速检查
			if (Timer_IsTimeout(last_diag_time, 5000)) {  // 每5秒检查一次
				DiagnosticLevel_t quick_result = SystemDiag_QuickCheck(&system_diag);
				if (quick_result > DIAG_OK) {
					SystemDiag_ShowErrors(&system_diag);
				}
				last_diag_time = Timer_GetTick();
			}

			Delay_ms(100);
			continue;  // 诊断模式下不执行正常功能
		}

		// 正常模式 - 更新状态机
		StateMachineError_t sm_error = StateMachine_Update(&gimbal_state);

		// 记录状态转换 (用于性能监控)
		static SystemState_t last_state = STATE_WAIT_POINT_A;
		if (gimbal_state.current_state != last_state) {
			SystemDiag_RecordStateTransition(&system_diag);
			last_state = gimbal_state.current_state;
		}

		// 检查是否可以启动自动移动
		if (gimbal_state.current_state == STATE_AUTO_MOVING && !AutoMovement_IsActive(&auto_ctrl)) {
			// 状态机进入自动移动状态，启动自动往返控制器
			AutoMovementError_t auto_error = AutoMovement_Start(&auto_ctrl, MOVE_MODE_CONTINUOUS);
			if (auto_error != AUTO_OK) {
				// 启动失败，记录错误并回到等待状态
				SystemDiag_LogError(&system_diag, MODULE_SYSTEM, 1, "Auto start failed");

				// 发送蓝牙错误报告
				ErrorReport_t error_report = {
					.timestamp = Timer_GetTimeMs(),
					.module_id = MODULE_SYSTEM,
					.error_code = 1,
					.severity = DIAG_ERROR
				};
				strcpy(error_report.error_message, "Auto movement start failed");
				Bluetooth_SendErrorReport(&error_report);

				StateMachine_TransitionTo(&gimbal_state, STATE_WAIT_POINT_A);
			}
		}

		// 更新自动往返控制器
		if (AutoMovement_IsActive(&auto_ctrl)) {
			uint32_t step_start_time = Timer_GetTick();
			AutoMovementError_t auto_error = AutoMovement_Update(&auto_ctrl);
			uint32_t step_end_time = Timer_GetTick();

			// 记录步进时间 (用于性能监控)
			float step_time = (step_end_time - step_start_time) * 20.0f;  // 转换为ms
			SystemDiag_RecordPathStep(&system_diag, step_time);

			// 检查自动移动错误
			if (auto_error != AUTO_OK) {
				SystemDiag_LogError(&system_diag, MODULE_SYSTEM, 2, "Auto movement error");

				// 发送蓝牙错误报告
				ErrorReport_t error_report = {
					.timestamp = Timer_GetTimeMs(),
					.module_id = MODULE_SYSTEM,
					.error_code = 2,
					.severity = DIAG_WARNING
				};
				strcpy(error_report.error_message, "Auto movement execution error");
				Bluetooth_SendErrorReport(&error_report);
			}

			// 检查用户停止请求
			if (Key_IsClicked(KEY_RECORD)) {
				SystemDiag_RecordKeyPress(&system_diag);
				AutoMovement_Stop(&auto_ctrl);
				StateMachine_TransitionTo(&gimbal_state, STATE_WAIT_POINT_A);
				Key_ClearEvent(KEY_RECORD);
			}

			// 检查方向切换请求
			if (Key_IsClicked(KEY_TRIGGER)) {
				SystemDiag_RecordKeyPress(&system_diag);
				// 在自动移动模式下，PB1用于暂停/恢复
				if (AutoMovement_IsActive(&auto_ctrl)) {
					// 暂停/恢复功能暂时简化为停止
					AutoMovement_Stop(&auto_ctrl);
					StateMachine_TransitionTo(&gimbal_state, STATE_WAIT_POINT_A);
				}
				Key_ClearEvent(KEY_TRIGGER);
			}

			// 显示自动移动状态
			AutoMovement_ShowStatus(&auto_ctrl);

			// 自动移动时LED闪烁
			static uint32_t led_toggle_time = 0;
			if (Timer_IsTimeout(led_toggle_time, 300)) {
				static uint8_t led_state = 0;
				if (led_state) {
					LED_ON();
				} else {
					LED_OFF();
				}
				led_state = !led_state;
				led_toggle_time = Timer_GetTick();
			}
		} else {
			// 正常模式 - 更新状态机 (这里处理按键逻辑)
			StateMachineError_t sm_error = StateMachine_Update(&gimbal_state);

			// 记录状态转换 (用于性能监控)
			static SystemState_t last_state = STATE_WAIT_POINT_A;
			if (gimbal_state.current_state != last_state) {
				SystemDiag_RecordStateTransition(&system_diag);
				last_state = gimbal_state.current_state;
			}

			// 显示状态机状态
			const char* state_str = StateMachine_GetStateString(gimbal_state.current_state);
			char status_str[16];
			sprintf(status_str, "S:%s", state_str);
			OLED_ShowString(1, 1, status_str);

			// 显示点位记录状态
			char point_str[16];
			sprintf(point_str, "A:%c B:%c",
			        StateMachine_IsPointRecorded(&gimbal_state, 1) ? 'Y' : 'N',
			        StateMachine_IsPointRecorded(&gimbal_state, 2) ? 'Y' : 'N');
			OLED_ShowString(2, 1, point_str);

			// 显示操作提示和系统信息
			if (gimbal_state.current_state == STATE_WAIT_POINT_A) {
				OLED_ShowString(3, 1, "Record Point A");
				OLED_ShowString(4, 1, "Press PB0");
			} else if (gimbal_state.current_state == STATE_WAIT_POINT_B) {
				OLED_ShowString(3, 1, "Record Point B");
				OLED_ShowString(4, 1, "Press PB0");
			} else if (gimbal_state.current_state == STATE_AUTO_MOVING) {
				OLED_ShowString(3, 1, "Auto Ready");
				OLED_ShowString(4, 1, "Press PB1");
			}

			LED_OFF();
		}

		// 显示错误状态和系统效率
		if (sm_error != SM_OK) {
			char error_str[16];
			sprintf(error_str, "E:%d", sm_error);
			OLED_ShowString(1, 10, error_str);
			SystemDiag_LogError(&system_diag, MODULE_SYSTEM, sm_error, "State machine error");
		} else {
			// 显示系统效率
			char eff_str[16];
			sprintf(eff_str, "%.0f%%", system_diag.performance.system_efficiency);
			OLED_ShowString(1, 12, eff_str);
		}

		// 定期快速诊断检查 (每30秒)
		if (Timer_IsTimeout(last_diag_time, 30000)) {
			DiagnosticLevel_t quick_result = SystemDiag_QuickCheck(&system_diag);
			if (quick_result > DIAG_WARNING) {
				// 严重问题，显示警告
				LED_ON();
				Delay_ms(100);
				LED_OFF();
			}
			last_diag_time = Timer_GetTick();
		}

		// 更新蓝牙模块 (只处理接收数据，不再定期发送)
		Bluetooth_Update();

		// 按键硬件测试 (每2秒发送一次状态)
		static uint32_t last_key_test = 0;
		if (Timer_IsTimeout(last_key_test, 2000)) {
			uint8_t pb0_state = GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_0);
			uint8_t pb1_state = GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_1);

			char key_test_msg[64];
			sprintf(key_test_msg, "[KEY TEST] PB0:%s PB1:%s\r\n",
			        pb0_state ? "UP" : "DOWN",
			        pb1_state ? "UP" : "DOWN");
			Bluetooth_SendString(key_test_msg);

			last_key_test = Timer_GetTick();
		}

		// 舵机状态监控 (每5秒发送一次状态，不控制舵机移动)
		static uint32_t last_servo_test = 0;
		if (Timer_IsTimeout(last_servo_test, 5000)) {
			float pan_angle = 0, tilt_angle = 0;
			ServoError_t pan_error = Servo_ReadPosition(SERVO_PAN_ID, &pan_angle);
			ServoError_t tilt_error = Servo_ReadPosition(SERVO_TILT_ID, &tilt_angle);

			char servo_test_msg[128];
			sprintf(servo_test_msg, "[SERVO STATUS] Pan:%s(%.1f deg) Tilt:%s(%.1f deg)\r\n",
			        pan_error == SERVO_OK ? "OK" : "FAIL", pan_angle,
			        tilt_error == SERVO_OK ? "OK" : "FAIL", tilt_angle);
			Bluetooth_SendString(servo_test_msg);

			last_servo_test = Timer_GetTick();
		}



		// 更新系统运行时间
		system_diag.performance.total_runtime = Timer_GetTimeMs();

		Delay_ms(50);  // 主循环更新频率20Hz
	}
}
