#include "sys.h"
#include "usart.h"	
////////////////////////////////////////////////////////////////////////////////// 	 
//���ʹ��ucos,����������ͷ�ļ�����.
#if SYSTEM_SUPPORT_OS
#include "includes.h"					//ucos ʹ��	  
#endif
//////////////////////////////////////////////////////////////////////////////////	 
//������ֻ��ѧϰʹ�ã�δ���������ɣ��������������κ���;
//ALIENTEK STM32F4̽���߿�����
//����1��ʼ��		   
//����ԭ��@ALIENTEK
//������̳:www.openedv.com
//�޸�����:2014/6/10
//�汾��V1.5
//��Ȩ���У�����ؾ���
//Copyright(C) �������������ӿƼ����޹�˾ 2009-2019
//All rights reserved
//********************************************************************************
//V1.3�޸�˵�� 
//֧����Ӧ��ͬƵ���µĴ��ڲ���������.
//�����˶�printf��֧��
//�����˴��ڽ��������.
//������printf��һ���ַ���ʧ��bug
//V1.4�޸�˵��
//1,�޸Ĵ��ڳ�ʼ��IO��bug
//2,�޸���USART_RX_STA,ʹ�ô����������ֽ���Ϊ2��14�η�
//3,������USART_REC_LEN,���ڶ��崮������������յ��ֽ���(������2��14�η�)
//4,�޸���EN_USART1_RX��ʹ�ܷ�ʽ
//V1.5�޸�˵��
//1,�����˶�UCOSII��֧��
////////////////////////////////////////////////////////////////////////////////// 	  
 

//////////////////////////////////////////////////////////////////
//�������´���,֧��printf����,������Ҫѡ��use MicroLIB	  
#if 1
#pragma import(__use_no_semihosting)             
//��׼����Ҫ��֧�ֺ���                 
struct __FILE 
{ 
	int handle; 
}; 

FILE __stdout;       
//����_sys_exit()�Ա���ʹ�ð�����ģʽ    
void _sys_exit(int x) 
{ 
	x = x; 
} 
//�ض���fputc���� 
int fputc(int ch, FILE *f)
{ 	
	while((USART1->SR&0X40)==0);//ѭ������,ֱ���������   
	USART1->DR = (u8) ch;      
	return ch;
}
#endif
 
#if EN_USART1_RX   //���ʹ���˽���
//����1�жϷ������
//ע��,��ȡUSARTx->SR�ܱ���Ī������Ĵ���   	
u8 USART_RX_BUF[USART_REC_LEN];     //接收缓冲,最大USART_REC_LEN个字节.
//接收状态
//bit15：	接收完成标志
//bit14：	接收到0x0d
//bit13~0：	接收到的有效字节数目
u16 USART_RX_STA=0;       //接收状态标记

// 舵机协议相关变量
u8 SERVO_RX_BUF[SERVO_MAX_PACKET_LEN]; //舵机接收缓冲
u8 SERVO_RX_STA = 0;					//舵机接收状态: 0-等待帧头1, 1-等待帧头2, 2-接收数据
u8 SERVO_RX_CNT = 0;					//舵机接收计数器
u8 SERVO_PKT_LEN = 0;					//舵机数据包长度	

//��ʼ��IO ����1 
//bound:������
void uart_init(u32 bound){
   //GPIO�˿�����
  GPIO_InitTypeDef GPIO_InitStructure;
	USART_InitTypeDef USART_InitStructure;
	NVIC_InitTypeDef NVIC_InitStructure;
	
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA,ENABLE); //ʹ��GPIOAʱ��
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1,ENABLE);//ʹ��USART1ʱ��
 
	//����1��Ӧ���Ÿ���ӳ��
	GPIO_PinAFConfig(GPIOA,GPIO_PinSource9,GPIO_AF_USART1); //GPIOA9����ΪUSART1
	GPIO_PinAFConfig(GPIOA,GPIO_PinSource10,GPIO_AF_USART1); //GPIOA10����ΪUSART1
	
	//USART1�˿�����
  GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9 | GPIO_Pin_10; //GPIOA9��GPIOA10
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;//���ù���
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;	//�ٶ�50MHz
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP; //���츴�����
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP; //����
	GPIO_Init(GPIOA,&GPIO_InitStructure); //��ʼ��PA9��PA10

   //USART1 ��ʼ������
	USART_InitStructure.USART_BaudRate = bound;//����������
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;//�ֳ�Ϊ8λ���ݸ�ʽ
	USART_InitStructure.USART_StopBits = USART_StopBits_1;//һ��ֹͣλ
	USART_InitStructure.USART_Parity = USART_Parity_No;//����żУ��λ
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;//��Ӳ������������
	USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;	//�շ�ģʽ
  USART_Init(USART1, &USART_InitStructure); //��ʼ������1
	
  USART_Cmd(USART1, ENABLE);  //ʹ�ܴ���1 
	
	//USART_ClearFlag(USART1, USART_FLAG_TC);
	
#if EN_USART1_RX	
	USART_ITConfig(USART1, USART_IT_RXNE, ENABLE);//��������ж�

	//Usart1 NVIC ����
  NVIC_InitStructure.NVIC_IRQChannel = USART1_IRQn;//����1�ж�ͨ��
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=3;//��ռ���ȼ�3
	NVIC_InitStructure.NVIC_IRQChannelSubPriority =3;		//�����ȼ�3
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;			//IRQͨ��ʹ��
	NVIC_Init(&NVIC_InitStructure);	//����ָ���Ĳ�����ʼ��VIC�Ĵ�����

#endif
	
}


void USART1_IRQHandler(void)                	//串口1中断服务程序
{
	u8 Res;
#if SYSTEM_SUPPORT_OS 		//如果SYSTEM_SUPPORT_OS为真，则需要支持OS.
	OSIntEnter();    
#endif
	if(USART_GetITStatus(USART1, USART_IT_RXNE) != RESET)  //接收中断
	{
		Res = USART_ReceiveData(USART1);	//读取接收到的数据
		
		// 舵机协议包状态机处理
		switch(SERVO_RX_STA)
		{
			case 0:	// 等待第一个帧头 0x55
				if(Res == SERVO_FRAME_HEADER)
				{
					SERVO_RX_STA = 1;
					SERVO_RX_CNT = 0;
				}
				break;
				
			case 1:	// 等待第二个帧头 0x55
				if(Res == SERVO_FRAME_HEADER)
				{
					SERVO_RX_STA = 2;
					SERVO_RX_BUF[0] = SERVO_FRAME_HEADER;
					SERVO_RX_BUF[1] = SERVO_FRAME_HEADER;
					SERVO_RX_CNT = 2;
				}
				else
				{
					SERVO_RX_STA = 0; // 重新开始
				}
				break;
				
			case 2:	// 接收数据包
				SERVO_RX_BUF[SERVO_RX_CNT] = Res;
				SERVO_RX_CNT++;
				
				if(SERVO_RX_CNT == 5) // 收到ID、Length、Cmd
				{
					SERVO_PKT_LEN = SERVO_RX_BUF[3] + 3; // Length + 帧头(2) + 校验(1)
					if(SERVO_PKT_LEN > SERVO_MAX_PACKET_LEN)
					{
						SERVO_RX_STA = 0; // 包长度错误，重新开始
						break;
					}
				}
				
				if(SERVO_RX_CNT >= 5 && SERVO_RX_CNT >= SERVO_PKT_LEN)
				{
					// 数据包接收完成，重新开始等待下一包
					SERVO_RX_STA = 0;
				}
				
				if(SERVO_RX_CNT >= SERVO_MAX_PACKET_LEN)
				{
					SERVO_RX_STA = 0; // 防止溢出
				}
				break;
		}
  } 
#if SYSTEM_SUPPORT_OS 	//如果SYSTEM_SUPPORT_OS为真，则需要支持OS.
	OSIntExit();  											 
#endif
} 
#endif	

// 舵机控制函数实现

// 串口发送数组
void USART_SendArray(u8 *arr, u16 len) 
{
	u16 i;
	for (i = 0; i < len; i++) 
	{
		while(USART_GetFlagStatus(USART1, USART_FLAG_TXE) != SET);
		USART_SendData(USART1, arr[i]);
	}
	while(USART_GetFlagStatus(USART1, USART_FLAG_TC) != SET); // 等待发送完成
}

// 计算舵机协议校验和
u8 Servo_CalculateChecksum(u8 *data, u8 len) 
{
	u8 sum = 0;
	u8 i;
	for (i = 0; i < len; i++) 
	{
		sum += data[i];
	}
	return ~sum; // 取反
}

// 设置舵机为电机模式并控制转动速度
void Servo_SetMotorMode(u8 id, s16 speed) 
{
	u8 cmd_packet[10];
	
	// 构建命令包: 55 55 ID 07 1D 01 00 speed_low speed_high checksum
	cmd_packet[0] = 0x55;								// 帧头1
	cmd_packet[1] = 0x55;								// 帧头2
	cmd_packet[2] = id;									// 舵机ID
	cmd_packet[3] = 0x07;								// 数据长度
	cmd_packet[4] = 0x1D;								// 指令(SERVO_OR_MOTOR_MODE_WRITE = 29 = 0x1D)
	cmd_packet[5] = 0x01;								// 参数1: 电机模式
	cmd_packet[6] = 0x00;								// 参数2: 空值
	cmd_packet[7] = (u8)(speed & 0xFF);					// 参数3: 速度低字节
	cmd_packet[8] = (u8)((speed >> 8) & 0xFF);			// 参数4: 速度高字节
	
	// 计算校验和 (从ID开始到速度高字节)
	cmd_packet[9] = Servo_CalculateChecksum(&cmd_packet[2], 7);
	
	// 发送命令包
	USART_SendArray(cmd_packet, 10);
}

// 装载舵机电机(上电状态)
void Servo_LoadMotor(u8 id) 
{
	u8 cmd_packet[7];
	
	// 构建命令包: 55 55 ID 04 1F 01 checksum
	cmd_packet[0] = 0x55;								// 帧头1
	cmd_packet[1] = 0x55;								// 帧头2
	cmd_packet[2] = id;									// 舵机ID
	cmd_packet[3] = 0x04;								// 数据长度
	cmd_packet[4] = 0x1F;								// 指令(SERVO_LOAD_OR_UNLOAD_WRITE = 31 = 0x1F)
	cmd_packet[5] = 0x01;								// 参数1: 1=装载电机，0=卸载电机
	
	// 计算校验和 (从ID开始到参数)
	cmd_packet[6] = Servo_CalculateChecksum(&cmd_packet[2], 4);
	
	// 发送命令包
	USART_SendArray(cmd_packet, 7);
}

// 舵机位置控制(位置模式，带时间)
void Servo_MoveToPosition(u8 id, u16 position, u16 time) 
{
	u8 cmd_packet[10];
	
	// 构建命令包: 55 55 ID 07 01 pos_low pos_high time_low time_high checksum
	cmd_packet[0] = 0x55;								// 帧头1
	cmd_packet[1] = 0x55;								// 帧头2
	cmd_packet[2] = id;									// 舵机ID
	cmd_packet[3] = 0x07;								// 数据长度
	cmd_packet[4] = 0x01;								// 指令(SERVO_MOVE_TIME_WRITE = 1)
	cmd_packet[5] = (u8)(position & 0xFF);				// 参数1: 角度低字节
	cmd_packet[6] = (u8)((position >> 8) & 0xFF);		// 参数2: 角度高字节  
	cmd_packet[7] = (u8)(time & 0xFF);					// 参数3: 时间低字节
	cmd_packet[8] = (u8)((time >> 8) & 0xFF);			// 参数4: 时间高字节
	
	// 计算校验和 (从ID开始到时间高字节)
	cmd_packet[9] = Servo_CalculateChecksum(&cmd_packet[2], 7);
	
	// 发送命令包
	USART_SendArray(cmd_packet, 10);
}

// 处理接收到的舵机数据包(暂未使用，预留)
u8 Servo_ProcessPacket(void) 
{
	// 这里可以添加对舵机响应包的处理逻辑
	// 目前只是简单测试，不处理响应
	return 0;
}

 

