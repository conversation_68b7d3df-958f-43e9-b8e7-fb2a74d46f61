Component: ARM Compiler 5.06 update 3 (build 300) Tool: armlink [4d35c9]

==============================================================================

Section Cross References

    main.o(.text) refers to showhz.o(.text) for LCD_DisplayHZstr
    main.o(.text) refers to lcd.o(.text) for LCD_DisplayNum
    main.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_Init
    main.o(.text) refers to common.o(.text) for Stm32_Clock_Init
    main.o(.text) refers to usart1.o(.text) for uart1_init
    main.o(.text) refers to key.o(.text) for KEY_Init
    main.o(.text) refers to led.o(.text) for LED_Init
    main.o(.text) refers to rtc.o(.text) for RTC_InitConfig
    main.o(.text) refers to w25qxx.o(.text) for W25QXX_Init
    main.o(.text) refers to updatefont.o(.text) for font_init
    main.o(.text) refers to malloc.o(.text) for Memory_Init
    main.o(.text) refers to lunar_calendar.o(.text) for GetLunarCalendarStr
    main.o(.text) refers to rtc.o(.data) for RTC_DateStruct
    main.o(.text) refers to rtc.o(.bss) for RTC_TimeStruct
    main.o(.text) refers to main.o(.data) for weekdate
    main.o(.text) refers to key.o(.data) for keydown_data
    main.o(.text) refers to lcd.o(.data) for BRUSH_COLOR
    main.o(.data) refers to main.o(.conststring) for .conststring
    stm32f4xx_it.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_IncTick
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to rtc.o(.text) for RTC_WKUP_IRQHandler
    startup_stm32f407xx.o(RESET) refers to usart1.o(.text) for USART1_IRQHandler
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(.text) for SystemInit
    startup_stm32f407xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    led.o(.text) refers to stm32f4xx_hal_gpio.o(.text) for HAL_GPIO_Init
    key.o(.text) refers to stm32f4xx_hal_gpio.o(.text) for HAL_GPIO_Init
    key.o(.text) refers to key.o(.data) for keyup_data
    lcd.o(.text) refers to common.o(.text) for delay_us
    lcd.o(.text) refers to lcd.o(.data) for write_gramcmd
    lcd.o(.text) refers to stm32f4xx_hal_gpio.o(.text) for HAL_GPIO_Init
    lcd.o(.text) refers to stm32f4xx_hal_sram.o(.text) for HAL_SRAM_Init
    lcd.o(.text) refers to lcd.o(.bss) for LCDSRAM_Handler
    lcd.o(.text) refers to lcd.o(.constdata) for char_1206
    usart1.o(.text) refers to stm32f4xx_hal_gpio.o(.text) for HAL_GPIO_Init
    usart1.o(.text) refers to stm32f4xx_hal_cortex.o(.text) for HAL_NVIC_EnableIRQ
    usart1.o(.text) refers to stm32f4xx_hal_uart.o(.text) for HAL_UART_Init
    usart1.o(.text) refers to strcmp.o(.text) for strcmp
    usart1.o(.text) refers to usart1.o(.bss) for UART1_Handler
    usart1.o(.text) refers to usart1.o(.data) for aRxBuffer
    malloc.o(.text) refers to malloc.o(.constdata) for memtblsize
    malloc.o(.text) refers to malloc.o(.data) for memmap
    malloc.o(.data) refers to malloc.o(.bss) for inmenbase
    malloc.o(.data) refers to malloc.o(.ARM.__AT_0x68000000) for exmen2base
    malloc.o(.data) refers to malloc.o(.ARM.__AT_0x680C8000) for exmemmapbase
    spi.o(.text) refers to stm32f4xx_hal_spi.o(.text) for HAL_SPI_TransmitReceive
    spi.o(.text) refers to stm32f4xx_hal_gpio.o(.text) for HAL_GPIO_Init
    spi.o(.text) refers to spi.o(.bss) for SPI1_Handler
    w25qxx.o(.text) refers to spi.o(.text) for SPI1_ReadWriteByte
    w25qxx.o(.text) refers to stm32f4xx_hal_gpio.o(.text) for HAL_GPIO_Init
    w25qxx.o(.text) refers to common.o(.text) for delay_us
    w25qxx.o(.text) refers to w25qxx.o(.data) for W25QXX_ID
    w25qxx.o(.text) refers to w25qxx.o(.bss) for TS_BUFFER
    tfcard_sdio.o(.text) refers to stm32f4xx_hal_sd.o(.text) for HAL_SD_Init
    tfcard_sdio.o(.text) refers to stm32f4xx_hal_gpio.o(.text) for HAL_GPIO_Init
    tfcard_sdio.o(.text) refers to common.o(.emb_text) for INTX_DISABLE
    tfcard_sdio.o(.text) refers to tfcard_sdio.o(.bss) for SDCARD_Handler
    rtc.o(.text) refers to stm32f4xx_hal_rtc.o(.text) for HAL_RTC_SetDate
    rtc.o(.text) refers to stm32f4xx_hal_rtc_ex.o(.text) for HAL_RTCEx_BKUPRead
    rtc.o(.text) refers to stm32f4xx_hal_pwr.o(.text) for HAL_PWR_EnableBkUpAccess
    rtc.o(.text) refers to stm32f4xx_hal_rcc.o(.text) for HAL_RCC_OscConfig
    rtc.o(.text) refers to stm32f4xx_hal_rcc_ex.o(.text) for HAL_RCCEx_PeriphCLKConfig
    rtc.o(.text) refers to stm32f4xx_hal_cortex.o(.text) for HAL_NVIC_SetPriority
    rtc.o(.text) refers to rtc.o(.constdata) for month_amendBuf
    rtc.o(.text) refers to rtc.o(.bss) for RTC_Handler
    rtc.o(.text) refers to rtc.o(.data) for RTC_DateStruct
    lunar_calendar.o(.text) refers to lunar_calendar.o(.constdata) for year_code
    lunar_calendar.o(.text) refers to lunar_calendar.o(.data) for sky
    lunar_calendar.o(.data) refers to lunar_calendar.o(.conststring) for .conststring
    stm32f4xx_hal.o(.text) refers to stm32f4xx_hal_cortex.o(.text) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(.text) refers to stm32f4xx_hal_msp.o(.text) for HAL_MspInit
    stm32f4xx_hal.o(.text) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_pwr_ex.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.text) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(.text) refers to stm32f4xx_hal_gpio.o(.text) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(.text) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_dma.o(.text) refers to stm32f4xx_hal_dma.o(.constdata) for flagBitshiftOffset
    stm32f4xx_hal_dma.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_ll_fsmc.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_sram.o(.text) refers to lcd.o(.text) for HAL_SRAM_MspInit
    stm32f4xx_hal_sram.o(.text) refers to stm32f4xx_ll_fsmc.o(.text) for FSMC_NORSRAM_Init
    stm32f4xx_hal_sram.o(.text) refers to stm32f4xx_hal_dma.o(.text) for HAL_DMA_Start_IT
    stm32f4xx_hal_usart.o(.text) refers to stm32f4xx_hal_rcc.o(.text) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_usart.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_usart.o(.text) refers to stm32f4xx_hal_dma.o(.text) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.text) refers to stm32f4xx_hal_rcc.o(.text) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(.text) refers to usart1.o(.text) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_uart.o(.text) refers to stm32f4xx_hal_dma.o(.text) for HAL_DMA_Start_IT
    stm32f4xx_hal_sd.o(.text) refers to stm32f4xx_ll_sdmmc.o(.text) for SDIO_GetPowerState
    stm32f4xx_hal_sd.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_Delay
    stm32f4xx_hal_sd.o(.text) refers to tfcard_sdio.o(.text) for HAL_SD_MspInit
    stm32f4xx_hal_sd.o(.text) refers to stm32f4xx_hal_dma.o(.text) for HAL_DMA_Start_IT
    stm32f4xx_ll_sdmmc.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(.text) refers to spi.o(.text) for HAL_SPI_MspInit
    stm32f4xx_hal_spi.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_spi.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(.text) refers to stm32f4xx_hal_dma.o(.text) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.text) refers to stm32f4xx_hal_dma.o(.text) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.text) refers to stm32f4xx_hal_tim_ex.o(.text) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim_ex.o(.text) refers to stm32f4xx_hal_tim.o(.text) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(.text) refers to stm32f4xx_hal_dma.o(.text) for HAL_DMA_Start_IT
    stm32f4xx_hal_rtc.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_rtc.o(.text) refers to rtc.o(.text) for HAL_RTC_MspInit
    stm32f4xx_hal_rtc.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rtc.o(.text) refers to stm32f4xx_hal_rtc_ex.o(.text) for HAL_RTCEx_AlarmBEventCallback
    stm32f4xx_hal_rtc_ex.o(.text) refers to stm32f4xx_hal_rtc.o(.text) for RTC_Bcd2ToByte
    stm32f4xx_hal_rtc_ex.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_rtc_ex.o(.text) refers to rtc.o(.text) for HAL_RTCEx_WakeUpTimerEventCallback
    stm32f4xx_hal_rtc_ex.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    diskio.o(.text) refers to tfcard_sdio.o(.text) for SD_Init
    diskio.o(.text) refers to w25qxx.o(.text) for W25QXX_Init
    diskio.o(.text) refers to malloc.o(.text) for Mem_malloc
    diskio.o(.text) refers to diskio.o(.data) for FLASH_SECTOR_COUNT
    diskio.o(.text) refers to tfcard_sdio.o(.bss) for SDCardInfo
    ff.o(.text) refers to diskio.o(.text) for disk_write
    ff.o(.text) refers to cc936.o(.text) for ff_wtoupper
    ff.o(.text) refers to ff.o(.constdata) for LfnOfs
    ff.o(.text) refers to ff.o(.data) for FatFs
    cc936.o(.text) refers to w25qxx.o(.text) for W25QXX_Read
    cc936.o(.text) refers to updatefont.o(.bss) for hzfont_info
    cc936.o(.text) refers to cc936.o(.constdata) for tbl_lower
    common.o(.text) refers to stm32f4xx_hal_rcc.o(.text) for HAL_RCC_OscConfig
    common.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetREVID
    common.o(.text) refers to stm32f4xx_hal_cortex.o(.text) for HAL_SYSTICK_CLKSourceConfig
    common.o(.text) refers to common.o(.data) for fac_us
    updatefont.o(.text) refers to lcd.o(.text) for LCD_DisplayString
    updatefont.o(.text) refers to malloc.o(.text) for Mem_malloc
    updatefont.o(.text) refers to ff.o(.text) for f_open
    updatefont.o(.text) refers to w25qxx.o(.text) for W25QXX_SectorWrite
    updatefont.o(.text) refers to common.o(.text) for delay_ms
    updatefont.o(.text) refers to tfcard_sdio.o(.text) for SD_Init
    updatefont.o(.text) refers to strcpy.o(.text) for strcpy
    updatefont.o(.text) refers to updatefont.o(.bss) for hzfont_info
    updatefont.o(.text) refers to lcd.o(.data) for BRUSH_COLOR
    updatefont.o(.text) refers to updatefont.o(.constdata) for UNIGBK_PATH
    updatefont.o(.constdata) refers to updatefont.o(.conststring) for .conststring
    showhz.o(.text) refers to w25qxx.o(.text) for W25QXX_Read
    showhz.o(.text) refers to lcd.o(.text) for LCD_Color_DrawPoint
    showhz.o(.text) refers to strlen.o(.text) for strlen
    showhz.o(.text) refers to updatefont.o(.bss) for hzfont_info
    showhz.o(.text) refers to lcd.o(.data) for BRUSH_COLOR
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text) for main
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing startup_stm32f407xx.o(HEAP), (512 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing led.o(.rrx_text), (6 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing lcd.o(.rev16_text), (4 bytes).
    Removing lcd.o(.revsh_text), (4 bytes).
    Removing lcd.o(.rrx_text), (6 bytes).
    Removing usart1.o(.rev16_text), (4 bytes).
    Removing usart1.o(.revsh_text), (4 bytes).
    Removing usart1.o(.rrx_text), (6 bytes).
    Removing malloc.o(.rev16_text), (4 bytes).
    Removing malloc.o(.revsh_text), (4 bytes).
    Removing malloc.o(.rrx_text), (6 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.rrx_text), (6 bytes).
    Removing w25qxx.o(.rev16_text), (4 bytes).
    Removing w25qxx.o(.revsh_text), (4 bytes).
    Removing w25qxx.o(.rrx_text), (6 bytes).
    Removing tfcard_sdio.o(.rev16_text), (4 bytes).
    Removing tfcard_sdio.o(.revsh_text), (4 bytes).
    Removing tfcard_sdio.o(.rrx_text), (6 bytes).
    Removing rtc.o(.rev16_text), (4 bytes).
    Removing rtc.o(.revsh_text), (4 bytes).
    Removing rtc.o(.rrx_text), (6 bytes).
    Removing lunar_calendar.o(.rev16_text), (4 bytes).
    Removing lunar_calendar.o(.revsh_text), (4 bytes).
    Removing lunar_calendar.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text), (256 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text), (4232 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ll_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ll_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ll_fsmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_sram.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_usart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_usart.o(.text), (4604 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_sd.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_sd.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_sd.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ll_sdmmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ll_sdmmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ll_sdmmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_spi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.text), (11122 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text), (3624 bytes).
    Removing stm32f4xx_hal_rtc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rtc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rtc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(.rrx_text), (6 bytes).
    Removing diskio.o(.rev16_text), (4 bytes).
    Removing diskio.o(.revsh_text), (4 bytes).
    Removing diskio.o(.rrx_text), (6 bytes).
    Removing cc936.o(.rev16_text), (4 bytes).
    Removing cc936.o(.revsh_text), (4 bytes).
    Removing cc936.o(.rrx_text), (6 bytes).
    Removing common.o(.rev16_text), (4 bytes).
    Removing common.o(.revsh_text), (4 bytes).
    Removing common.o(.rrx_text), (6 bytes).
    Removing updatefont.o(.rev16_text), (4 bytes).
    Removing updatefont.o(.revsh_text), (4 bytes).
    Removing showhz.o(.rev16_text), (4 bytes).
    Removing showhz.o(.revsh_text), (4 bytes).

121 unused section(s) (total 24884 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/string/strcmp.c         0x00000000   Number         0  strcmp.o ABSOLUTE
    ../clib/microlib/string/strcpy.c         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ..\Common\common.c                       0x00000000   Number         0  common.o ABSOLUTE
    ..\FATFS\cc936.c                         0x00000000   Number         0  cc936.o ABSOLUTE
    ..\FATFS\src\diskio.c                    0x00000000   Number         0  diskio.o ABSOLUTE
    ..\FATFS\src\ff.c                        0x00000000   Number         0  ff.o ABSOLUTE
    ..\HanZi\showhz.c                        0x00000000   Number         0  showhz.o ABSOLUTE
    ..\HanZi\updatefont.c                    0x00000000   Number         0  updatefont.o ABSOLUTE
    ..\Main\main.c                           0x00000000   Number         0  main.o ABSOLUTE
    ..\Main\stm32f4xx_it.c                   0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_rtc.c 0x00000000   Number         0  stm32f4xx_hal_rtc.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_rtc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rtc_ex.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_sd.c 0x00000000   Number         0  stm32f4xx_hal_sd.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_spi.c 0x00000000   Number         0  stm32f4xx_hal_spi.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_usart.c 0x00000000   Number         0  stm32f4xx_hal_usart.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_ll_fsmc.c 0x00000000   Number         0  stm32f4xx_ll_fsmc.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_ll_sdmmc.c 0x00000000   Number         0  stm32f4xx_ll_sdmmc.o ABSOLUTE
    ..\Startup_config\startup_stm32f407xx.s  0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    ..\Startup_config\stm32f4xx_hal_msp.c    0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Startup_config\system_stm32f4xx.c     0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\USER\KEY\key.c                        0x00000000   Number         0  key.o ABSOLUTE
    ..\USER\LCD\lcd.c                        0x00000000   Number         0  lcd.o ABSOLUTE
    ..\USER\LED\led.c                        0x00000000   Number         0  led.o ABSOLUTE
    ..\USER\Lunar_Calendar\lunar_calendar.c  0x00000000   Number         0  lunar_calendar.o ABSOLUTE
    ..\USER\MALLOC\malloc.c                  0x00000000   Number         0  malloc.o ABSOLUTE
    ..\USER\RTC\rtc.c                        0x00000000   Number         0  rtc.o ABSOLUTE
    ..\USER\SPI\spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    ..\USER\TF_SDIO\tfcard_sdio.c            0x00000000   Number         0  tfcard_sdio.o ABSOLUTE
    ..\USER\W25QXX\w25qxx.c                  0x00000000   Number         0  w25qxx.o ABSOLUTE
    ..\USER\usart1\usart1.c                  0x00000000   Number         0  usart1.o ABSOLUTE
    ..\\Common\\common.c                     0x00000000   Number         0  common.o ABSOLUTE
    ..\\FATFS\\cc936.c                       0x00000000   Number         0  cc936.o ABSOLUTE
    ..\\FATFS\\src\\diskio.c                 0x00000000   Number         0  diskio.o ABSOLUTE
    ..\\HanZi\\showhz.c                      0x00000000   Number         0  showhz.o ABSOLUTE
    ..\\HanZi\\updatefont.c                  0x00000000   Number         0  updatefont.o ABSOLUTE
    ..\\Main\\main.c                         0x00000000   Number         0  main.o ABSOLUTE
    ..\\Main\\stm32f4xx_it.c                 0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_rtc.c 0x00000000   Number         0  stm32f4xx_hal_rtc.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_rtc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rtc_ex.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_sd.c 0x00000000   Number         0  stm32f4xx_hal_sd.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_spi.c 0x00000000   Number         0  stm32f4xx_hal_spi.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_usart.c 0x00000000   Number         0  stm32f4xx_hal_usart.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_ll_fsmc.c 0x00000000   Number         0  stm32f4xx_ll_fsmc.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_ll_sdmmc.c 0x00000000   Number         0  stm32f4xx_ll_sdmmc.o ABSOLUTE
    ..\\Startup_config\\stm32f4xx_hal_msp.c  0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\\Startup_config\\system_stm32f4xx.c   0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\\USER\\KEY\\key.c                     0x00000000   Number         0  key.o ABSOLUTE
    ..\\USER\\LCD\\lcd.c                     0x00000000   Number         0  lcd.o ABSOLUTE
    ..\\USER\\LED\\led.c                     0x00000000   Number         0  led.o ABSOLUTE
    ..\\USER\\Lunar_Calendar\\lunar_calendar.c 0x00000000   Number         0  lunar_calendar.o ABSOLUTE
    ..\\USER\\MALLOC\\malloc.c               0x00000000   Number         0  malloc.o ABSOLUTE
    ..\\USER\\RTC\\rtc.c                     0x00000000   Number         0  rtc.o ABSOLUTE
    ..\\USER\\SPI\\spi.c                     0x00000000   Number         0  spi.o ABSOLUTE
    ..\\USER\\TF_SDIO\\tfcard_sdio.c         0x00000000   Number         0  tfcard_sdio.o ABSOLUTE
    ..\\USER\\W25QXX\\w25qxx.c               0x00000000   Number         0  w25qxx.o ABSOLUTE
    ..\\USER\\usart1\\usart1.c               0x00000000   Number         0  usart1.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x08000198   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000198   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000198   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000198   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .emb_text                                0x0800019c   Section       16  common.o(.emb_text)
    $v0                                      0x0800019c   Number         0  common.o(.emb_text)
    .text                                    0x080001ac   Section        0  main.o(.text)
    .text                                    0x080006f8   Section        0  stm32f4xx_it.o(.text)
    .text                                    0x08000718   Section        0  system_stm32f4xx.o(.text)
    .text                                    0x08000838   Section        0  stm32f4xx_hal_msp.o(.text)
    .text                                    0x0800083c   Section       36  startup_stm32f407xx.o(.text)
    $v0                                      0x0800083c   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x08000860   Section        0  led.o(.text)
    .text                                    0x080008bc   Section        0  key.o(.text)
    .text                                    0x080009e0   Section        0  lcd.o(.text)
    .text                                    0x08001750   Section        0  usart1.o(.text)
    .text                                    0x08001940   Section        0  malloc.o(.text)
    .text                                    0x08001b5c   Section        0  spi.o(.text)
    .text                                    0x08001c84   Section        0  w25qxx.o(.text)
    .text                                    0x080020c8   Section        0  tfcard_sdio.o(.text)
    .text                                    0x0800229c   Section        0  rtc.o(.text)
    .text                                    0x080024fc   Section        0  lunar_calendar.o(.text)
    .text                                    0x08002b80   Section        0  stm32f4xx_hal.o(.text)
    .text                                    0x08002d74   Section        0  stm32f4xx_hal_cortex.o(.text)
    __NVIC_SetPriority                       0x08002d75   Thumb Code    32  stm32f4xx_hal_cortex.o(.text)
    __NVIC_GetPriorityGrouping               0x08003125   Thumb Code    12  stm32f4xx_hal_cortex.o(.text)
    .text                                    0x08003144   Section        0  stm32f4xx_hal_gpio.o(.text)
    .text                                    0x080034f8   Section        0  stm32f4xx_hal_pwr.o(.text)
    .text                                    0x080036d4   Section        0  stm32f4xx_hal_rcc.o(.text)
    .text                                    0x08003fc0   Section        0  stm32f4xx_hal_rcc_ex.o(.text)
    .text                                    0x08004434   Section        0  stm32f4xx_hal_dma.o(.text)
    DMA_CalcBaseAndBitshift                  0x08004435   Thumb Code    46  stm32f4xx_hal_dma.o(.text)
    DMA_CheckFifoParam                       0x08004463   Thumb Code   170  stm32f4xx_hal_dma.o(.text)
    DMA_SetConfig                            0x08004665   Thumb Code    44  stm32f4xx_hal_dma.o(.text)
    .text                                    0x08004cf0   Section        0  stm32f4xx_ll_fsmc.o(.text)
    .text                                    0x08005078   Section        0  stm32f4xx_hal_sram.o(.text)
    .text                                    0x080053f8   Section        0  stm32f4xx_hal_uart.o(.text)
    UART_SetConfig                           0x080053f9   Thumb Code   864  stm32f4xx_hal_uart.o(.text)
    UART_WaitOnFlagUntilTimeout              0x080059b1   Thumb Code   108  stm32f4xx_hal_uart.o(.text)
    UART_EndRxTransfer                       0x08005c9b   Thumb Code    32  stm32f4xx_hal_uart.o(.text)
    UART_EndTxTransfer                       0x08005cbb   Thumb Code    20  stm32f4xx_hal_uart.o(.text)
    UART_DMAError                            0x08005ccf   Thumb Code    80  stm32f4xx_hal_uart.o(.text)
    UART_DMATxHalfCplt                       0x08005d21   Thumb Code    14  stm32f4xx_hal_uart.o(.text)
    UART_DMATransmitCplt                     0x08005d31   Thumb Code    54  stm32f4xx_hal_uart.o(.text)
    UART_DMARxHalfCplt                       0x08005df7   Thumb Code    14  stm32f4xx_hal_uart.o(.text)
    UART_DMAReceiveCplt                      0x08005e07   Thumb Code    70  stm32f4xx_hal_uart.o(.text)
    UART_DMARxAbortCallback                  0x080061e5   Thumb Code    52  stm32f4xx_hal_uart.o(.text)
    UART_DMATxAbortCallback                  0x08006219   Thumb Code    52  stm32f4xx_hal_uart.o(.text)
    UART_DMATxOnlyAbortCallback              0x08006327   Thumb Code    24  stm32f4xx_hal_uart.o(.text)
    UART_DMARxOnlyAbortCallback              0x080063a9   Thumb Code    24  stm32f4xx_hal_uart.o(.text)
    UART_EndTransmit_IT                      0x08006435   Thumb Code    32  stm32f4xx_hal_uart.o(.text)
    UART_Transmit_IT                         0x08006455   Thumb Code   104  stm32f4xx_hal_uart.o(.text)
    UART_DMAAbortOnError                     0x080064bd   Thumb Code    20  stm32f4xx_hal_uart.o(.text)
    UART_Receive_IT                          0x080064d1   Thumb Code   166  stm32f4xx_hal_uart.o(.text)
    .text                                    0x08006834   Section        0  stm32f4xx_hal_sd.o(.text)
    SD_InitCard                              0x08006a25   Thumb Code   252  stm32f4xx_hal_sd.o(.text)
    SD_PowerON                               0x08006b21   Thumb Code   238  stm32f4xx_hal_sd.o(.text)
    SD_PowerOFF                              0x08006cc9   Thumb Code    14  stm32f4xx_hal_sd.o(.text)
    SD_SendStatus                            0x08007345   Thumb Code    50  stm32f4xx_hal_sd.o(.text)
    SD_DMAError                              0x0800739f   Thumb Code    92  stm32f4xx_hal_sd.o(.text)
    SD_DMAReceiveCplt                        0x080073fd   Thumb Code    70  stm32f4xx_hal_sd.o(.text)
    SD_DMATransmitCplt                       0x0800755f   Thumb Code    18  stm32f4xx_hal_sd.o(.text)
    SD_DMARxAbort                            0x0800779f   Thumb Code    76  stm32f4xx_hal_sd.o(.text)
    SD_DMATxAbort                            0x080077eb   Thumb Code    76  stm32f4xx_hal_sd.o(.text)
    SD_Read_IT                               0x08007837   Thumb Code    38  stm32f4xx_hal_sd.o(.text)
    SD_Write_IT                              0x0800785d   Thumb Code    56  stm32f4xx_hal_sd.o(.text)
    SD_SendSDStatus                          0x08007b69   Thumb Code   306  stm32f4xx_hal_sd.o(.text)
    SD_FindSCR                               0x08007d8b   Thumb Code   310  stm32f4xx_hal_sd.o(.text)
    SD_WideBus_Disable                       0x08007ec1   Thumb Code   106  stm32f4xx_hal_sd.o(.text)
    SD_WideBus_Enable                        0x08007f2b   Thumb Code   106  stm32f4xx_hal_sd.o(.text)
    .text                                    0x08008140   Section        0  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_GetCmdResp1                        0x08008235   Thumb Code   412  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_GetCmdError                        0x08008693   Thumb Code    60  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_GetCmdResp7                        0x080086fd   Thumb Code    82  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_GetCmdResp3                        0x080087bf   Thumb Code    74  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_GetCmdResp2                        0x080088b5   Thumb Code    88  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_GetCmdResp6                        0x08008975   Thumb Code   184  stm32f4xx_ll_sdmmc.o(.text)
    .text                                    0x08008b48   Section        0  stm32f4xx_hal_spi.o(.text)
    SPI_WaitFlagStateUntilTimeout            0x08008c23   Thumb Code   168  stm32f4xx_hal_spi.o(.text)
    SPI_EndRxTxTransaction                   0x08008ccb   Thumb Code   100  stm32f4xx_hal_spi.o(.text)
    SPI_EndRxTransaction                     0x08008f09   Thumb Code   148  stm32f4xx_hal_spi.o(.text)
    SPI_CloseTx_ISR                          0x08009483   Thumb Code   154  stm32f4xx_hal_spi.o(.text)
    SPI_TxISR_8BIT                           0x0800951d   Thumb Code    56  stm32f4xx_hal_spi.o(.text)
    SPI_TxISR_16BIT                          0x08009555   Thumb Code    56  stm32f4xx_hal_spi.o(.text)
    SPI_CloseRx_ISR                          0x08009651   Thumb Code   128  stm32f4xx_hal_spi.o(.text)
    SPI_RxISR_8BITCRC                        0x080096d1   Thumb Code    16  stm32f4xx_hal_spi.o(.text)
    SPI_RxISR_8BIT                           0x080096e1   Thumb Code    78  stm32f4xx_hal_spi.o(.text)
    SPI_RxISR_16BITCRC                       0x0800972f   Thumb Code    28  stm32f4xx_hal_spi.o(.text)
    SPI_RxISR_16BIT                          0x0800974b   Thumb Code    78  stm32f4xx_hal_spi.o(.text)
    SPI_CloseRxTx_ISR                        0x0800979b   Thumb Code   244  stm32f4xx_hal_spi.o(.text)
    SPI_2linesTxISR_8BIT                     0x0800988f   Thumb Code    88  stm32f4xx_hal_spi.o(.text)
    SPI_2linesRxISR_8BITCRC                  0x080098e7   Thumb Code    32  stm32f4xx_hal_spi.o(.text)
    SPI_2linesRxISR_8BIT                     0x08009907   Thumb Code    68  stm32f4xx_hal_spi.o(.text)
    SPI_2linesTxISR_16BIT                    0x0800994b   Thumb Code    88  stm32f4xx_hal_spi.o(.text)
    SPI_2linesRxISR_16BITCRC                 0x080099a3   Thumb Code    28  stm32f4xx_hal_spi.o(.text)
    SPI_2linesRxISR_16BIT                    0x080099bf   Thumb Code    68  stm32f4xx_hal_spi.o(.text)
    SPI_DMAError                             0x08009bc7   Thumb Code    40  stm32f4xx_hal_spi.o(.text)
    SPI_DMATransmitCplt                      0x08009bef   Thumb Code   128  stm32f4xx_hal_spi.o(.text)
    SPI_DMAHalfTransmitCplt                  0x08009c71   Thumb Code    14  stm32f4xx_hal_spi.o(.text)
    SPI_DMAReceiveCplt                       0x08009db5   Thumb Code   162  stm32f4xx_hal_spi.o(.text)
    SPI_DMAHalfReceiveCplt                   0x08009e59   Thumb Code    14  stm32f4xx_hal_spi.o(.text)
    SPI_DMATransmitReceiveCplt               0x08009e67   Thumb Code   168  stm32f4xx_hal_spi.o(.text)
    SPI_DMAHalfTransmitReceiveCplt           0x08009f11   Thumb Code    14  stm32f4xx_hal_spi.o(.text)
    SPI_AbortRx_ISR                          0x0800a1bf   Thumb Code    98  stm32f4xx_hal_spi.o(.text)
    SPI_AbortTx_ISR                          0x0800a221   Thumb Code    32  stm32f4xx_hal_spi.o(.text)
    SPI_DMARxAbortCallback                   0x0800a3c7   Thumb Code   144  stm32f4xx_hal_spi.o(.text)
    SPI_DMATxAbortCallback                   0x0800a457   Thumb Code   166  stm32f4xx_hal_spi.o(.text)
    SPI_DMAAbortOnError                      0x0800a713   Thumb Code    20  stm32f4xx_hal_spi.o(.text)
    .text                                    0x0800a8ac   Section        0  stm32f4xx_hal_rtc.o(.text)
    .text                                    0x0800b424   Section        0  stm32f4xx_hal_rtc_ex.o(.text)
    .text                                    0x0800c218   Section        0  diskio.o(.text)
    .text                                    0x0800c3ec   Section        0  ff.o(.text)
    mem_cpy                                  0x0800c3ed   Thumb Code    26  ff.o(.text)
    mem_set                                  0x0800c407   Thumb Code    20  ff.o(.text)
    mem_cmp                                  0x0800c41b   Thumb Code    38  ff.o(.text)
    chk_chr                                  0x0800c441   Thumb Code    20  ff.o(.text)
    sync_window                              0x0800c455   Thumb Code    76  ff.o(.text)
    move_window                              0x0800c4a1   Thumb Code    50  ff.o(.text)
    sync_fs                                  0x0800c4d3   Thumb Code   200  ff.o(.text)
    remove_chain                             0x0800c7d3   Thumb Code   108  ff.o(.text)
    create_chain                             0x0800c83f   Thumb Code   214  ff.o(.text)
    clmt_clust                               0x0800c915   Thumb Code    50  ff.o(.text)
    dir_sdi                                  0x0800c947   Thumb Code   158  ff.o(.text)
    dir_next                                 0x0800c9e5   Thumb Code   272  ff.o(.text)
    dir_alloc                                0x0800caf5   Thumb Code    92  ff.o(.text)
    ld_clust                                 0x0800cb51   Thumb Code    32  ff.o(.text)
    st_clust                                 0x0800cb71   Thumb Code    16  ff.o(.text)
    cmp_lfn                                  0x0800cb81   Thumb Code   138  ff.o(.text)
    pick_lfn                                 0x0800cc0b   Thumb Code   112  ff.o(.text)
    fit_lfn                                  0x0800cc7b   Thumb Code   122  ff.o(.text)
    gen_numname                              0x0800ccf5   Thumb Code   194  ff.o(.text)
    sum_sfn                                  0x0800cdb7   Thumb Code    32  ff.o(.text)
    dir_find                                 0x0800cdd7   Thumb Code   222  ff.o(.text)
    dir_read                                 0x0800ceb5   Thumb Code   196  ff.o(.text)
    dir_register                             0x0800cf79   Thumb Code   308  ff.o(.text)
    dir_remove                               0x0800d0ad   Thumb Code   106  ff.o(.text)
    get_fileinfo                             0x0800d117   Thumb Code   268  ff.o(.text)
    create_name                              0x0800d223   Thumb Code   604  ff.o(.text)
    follow_path                              0x0800d47f   Thumb Code   144  ff.o(.text)
    get_ldnumber                             0x0800d50f   Thumb Code    74  ff.o(.text)
    check_fs                                 0x0800d559   Thumb Code   140  ff.o(.text)
    find_volume                              0x0800d5e5   Thumb Code   916  ff.o(.text)
    validate                                 0x0800d979   Thumb Code    54  ff.o(.text)
    putc_bfd                                 0x0800f313   Thumb Code    68  ff.o(.text)
    .text                                    0x0800f684   Section        0  cc936.o(.text)
    .text                                    0x0800f750   Section        0  common.o(.text)
    .text                                    0x0800faac   Section        0  updatefont.o(.text)
    .text                                    0x08010140   Section        0  showhz.o(.text)
    .text                                    0x080103c4   Section        0  uldiv.o(.text)
    .text                                    0x08010426   Section        0  strlen.o(.text)
    .text                                    0x08010434   Section        0  strcmp.o(.text)
    .text                                    0x08010450   Section        0  strcpy.o(.text)
    .text                                    0x08010462   Section        0  llshl.o(.text)
    .text                                    0x08010480   Section        0  llushr.o(.text)
    .text                                    0x080104a0   Section       36  init.o(.text)
    .text                                    0x080104c4   Section        0  __dczerorl2.o(.text)
    i.__scatterload_copy                     0x0801051a   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08010528   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x0801052a   Section       14  handlers.o(i.__scatterload_zeroinit)
    .constdata                               0x08010538   Section       24  system_stm32f4xx.o(.constdata)
    .constdata                               0x08010550   Section     7220  lcd.o(.constdata)
    .constdata                               0x08012184   Section       24  malloc.o(.constdata)
    .constdata                               0x0801219c   Section       12  rtc.o(.constdata)
    .constdata                               0x080121a8   Section      802  lunar_calendar.o(.constdata)
    MonthDayMax                              0x080124be   Data          12  lunar_calendar.o(.constdata)
    .constdata                               0x080124ca   Section        8  stm32f4xx_hal_dma.o(.constdata)
    flagBitshiftOffset                       0x080124ca   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x080124d2   Section       58  ff.o(.constdata)
    LfnOfs                                   0x080124d2   Data          13  ff.o(.constdata)
    vst                                      0x080124e0   Data          22  ff.o(.constdata)
    cst                                      0x080124f6   Data          22  ff.o(.constdata)
    .constdata                               0x0801250c   Section      960  cc936.o(.constdata)
    tbl_lower                                0x0801250c   Data         480  cc936.o(.constdata)
    tbl_upper                                0x080126ec   Data         480  cc936.o(.constdata)
    .constdata                               0x080128cc   Section       16  updatefont.o(.constdata)
    .conststring                             0x080128dc   Section       71  main.o(.conststring)
    .conststring                             0x08012924   Section      311  lunar_calendar.o(.conststring)
    .conststring                             0x08012a5c   Section      127  updatefont.o(.conststring)
    .data                                    0x20000000   Section       52  main.o(.data)
    .data                                    0x20000034   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000038   Section        6  key.o(.data)
    .data                                    0x2000003e   Section       18  lcd.o(.data)
    .data                                    0x20000050   Section        2  usart1.o(.data)
    .data                                    0x20000054   Section       18  malloc.o(.data)
    .data                                    0x20000066   Section        2  w25qxx.o(.data)
    .data                                    0x20000068   Section        4  rtc.o(.data)
    .data                                    0x2000006c   Section      248  lunar_calendar.o(.data)
    .data                                    0x20000164   Section        9  stm32f4xx_hal.o(.data)
    .data                                    0x2000016e   Section        2  diskio.o(.data)
    .data                                    0x20000170   Section       10  ff.o(.data)
    FatFs                                    0x20000170   Data           8  ff.o(.data)
    Fsid                                     0x20000178   Data           2  ff.o(.data)
    .data                                    0x2000017a   Section        1  common.o(.data)
    fac_us                                   0x2000017a   Data           1  common.o(.data)
    .bss                                     0x2000017c   Section       80  lcd.o(.bss)
    .bss                                     0x200001cc   Section      164  usart1.o(.bss)
    .bss                                     0x20000270   Section    87040  malloc.o(.bss)
    .bss                                     0x20015670   Section       88  spi.o(.bss)
    .bss                                     0x200156c8   Section     4096  w25qxx.o(.bss)
    .bss                                     0x200166c8   Section      868  tfcard_sdio.o(.bss)
    .bss                                     0x20016a2c   Section       52  rtc.o(.bss)
    .bss                                     0x20016a60   Section       33  updatefont.o(.bss)
    STACK                                    0x20016a88   Section     1024  startup_stm32f407xx.o(STACK)
    .ARM.__AT_0x68000000                     0x68000000   Section    819200  malloc.o(.ARM.__AT_0x68000000)
    .ARM.__AT_0x680C8000                     0x680c8000   Section    51200  malloc.o(.ARM.__AT_0x680C8000)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000199   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000199   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    WFI_SET                                  0x0800019d   Thumb Code     2  common.o(.emb_text)
    INTX_DISABLE                             0x0800019f   Thumb Code     4  common.o(.emb_text)
    INTX_ENABLE                              0x080001a3   Thumb Code     4  common.o(.emb_text)
    MSR_MSP                                  0x080001a7   Thumb Code     6  common.o(.emb_text)
    Time_Display                             0x080001ad   Thumb Code   194  main.o(.text)
    Adjust_Time                              0x0800026f   Thumb Code   408  main.o(.text)
    main                                     0x08000407   Thumb Code   710  main.o(.text)
    NMI_Handler                              0x080006f9   Thumb Code     2  stm32f4xx_it.o(.text)
    HardFault_Handler                        0x080006fb   Thumb Code     4  stm32f4xx_it.o(.text)
    MemManage_Handler                        0x080006ff   Thumb Code     4  stm32f4xx_it.o(.text)
    BusFault_Handler                         0x08000703   Thumb Code     4  stm32f4xx_it.o(.text)
    UsageFault_Handler                       0x08000707   Thumb Code     4  stm32f4xx_it.o(.text)
    SVC_Handler                              0x0800070b   Thumb Code     2  stm32f4xx_it.o(.text)
    DebugMon_Handler                         0x0800070d   Thumb Code     2  stm32f4xx_it.o(.text)
    PendSV_Handler                           0x0800070f   Thumb Code     2  stm32f4xx_it.o(.text)
    SysTick_Handler                          0x08000711   Thumb Code     8  stm32f4xx_it.o(.text)
    SystemInit                               0x08000719   Thumb Code    82  system_stm32f4xx.o(.text)
    SystemCoreClockUpdate                    0x0800076b   Thumb Code   174  system_stm32f4xx.o(.text)
    HAL_MspInit                              0x08000839   Thumb Code     2  stm32f4xx_hal_msp.o(.text)
    HAL_MspDeInit                            0x0800083b   Thumb Code     2  stm32f4xx_hal_msp.o(.text)
    Reset_Handler                            0x0800083d   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART2_IRQHandler                        0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x08000857   Thumb Code     0  startup_stm32f407xx.o(.text)
    LED_Init                                 0x08000861   Thumb Code    84  led.o(.text)
    KEY_Init                                 0x080008bd   Thumb Code    66  key.o(.text)
    key_scan                                 0x080008ff   Thumb Code   192  key.o(.text)
    LCD_WriteReg                             0x080009e1   Thumb Code    10  lcd.o(.text)
    LCD_ReadReg                              0x080009eb   Thumb Code    22  lcd.o(.text)
    lcdm_delay                               0x08000a01   Thumb Code    14  lcd.o(.text)
    LCD_WriteGRAM                            0x08000a0f   Thumb Code    10  lcd.o(.text)
    LCD_DisplayOn                            0x08000a19   Thumb Code     8  lcd.o(.text)
    LCD_DisplayOff                           0x08000a21   Thumb Code     8  lcd.o(.text)
    LCD_Open_Window                          0x08000a29   Thumb Code   166  lcd.o(.text)
    Set_Scan_Direction                       0x08000acf   Thumb Code   204  lcd.o(.text)
    Set_Display_Mode                         0x08000b9b   Thumb Code   214  lcd.o(.text)
    LCD_SetCursor                            0x08000c71   Thumb Code   186  lcd.o(.text)
    LCD_GetPoint                             0x08000d2b   Thumb Code   116  lcd.o(.text)
    LCD_DrawPoint                            0x08000d9f   Thumb Code    66  lcd.o(.text)
    LCD_Color_DrawPoint                      0x08000de1   Thumb Code    30  lcd.o(.text)
    Ssd1963_Set_BackLight                    0x08000dff   Thumb Code    32  lcd.o(.text)
    LCD_Clear                                0x08000e1f   Thumb Code    50  lcd.o(.text)
    HAL_SRAM_MspInit                         0x08000e51   Thumb Code   238  lcd.o(.text)
    LCD_FSMC_Config                          0x08000f3f   Thumb Code   122  lcd.o(.text)
    ILI9341_Read_id                          0x08000fb9   Thumb Code    30  lcd.o(.text)
    SSD1963_Read_id                          0x08000fd7   Thumb Code    28  lcd.o(.text)
    LCD_Init                                 0x08000ff3   Thumb Code  1020  lcd.o(.text)
    LCD_Fill_onecolor                        0x080013ef   Thumb Code    82  lcd.o(.text)
    LCD_Draw_Picture                         0x08001441   Thumb Code    94  lcd.o(.text)
    LCD_DisplayChar                          0x0800149f   Thumb Code   206  lcd.o(.text)
    LCD_DisplayString                        0x0800156d   Thumb Code    64  lcd.o(.text)
    LCD_DisplayString_color                  0x080015ad   Thumb Code    68  lcd.o(.text)
    Counter_Power                            0x080015f1   Thumb Code    22  lcd.o(.text)
    LCD_DisplayNum                           0x08001607   Thumb Code   244  lcd.o(.text)
    LCD_DisplayNum_color                     0x080016fb   Thumb Code    78  lcd.o(.text)
    HAL_UART_MspInit                         0x08001751   Thumb Code   144  usart1.o(.text)
    uart1_init                               0x080017e1   Thumb Code    44  usart1.o(.text)
    uart1SendChar                            0x0800180d   Thumb Code    20  usart1.o(.text)
    uart1SendChars                           0x08001821   Thumb Code    26  usart1.o(.text)
    USART1_IRQHandler                        0x0800183b   Thumb Code    56  usart1.o(.text)
    HAL_UART_RxCpltCallback                  0x08001873   Thumb Code   148  usart1.o(.text)
    mymemcpy                                 0x08001941   Thumb Code    26  malloc.o(.text)
    mymemset                                 0x0800195b   Thumb Code    20  malloc.o(.text)
    Memory_Init                              0x0800196f   Thumb Code    50  malloc.o(.text)
    Mem_perused                              0x080019a1   Thumb Code    54  malloc.o(.text)
    memin_malloc                             0x080019d7   Thumb Code   156  malloc.o(.text)
    memin_free                               0x08001a73   Thumb Code    84  malloc.o(.text)
    Mem_free                                 0x08001ac7   Thumb Code    30  malloc.o(.text)
    Mem_malloc                               0x08001ae5   Thumb Code    34  malloc.o(.text)
    Remem_malloc                             0x08001b07   Thumb Code    60  malloc.o(.text)
    SPI1_ReadWriteByte                       0x08001b5d   Thumb Code    28  spi.o(.text)
    SPI1_Init                                0x08001b79   Thumb Code    78  spi.o(.text)
    HAL_SPI_MspInit                          0x08001bc7   Thumb Code   106  spi.o(.text)
    SPI1_Setclock                            0x08001c31   Thumb Code    66  spi.o(.text)
    W25QXX_ReadID                            0x08001c85   Thumb Code    64  w25qxx.o(.text)
    W25QXX_Init                              0x08001cc5   Thumb Code    86  w25qxx.o(.text)
    W25QXX_ReadSR                            0x08001d1b   Thumb Code    36  w25qxx.o(.text)
    W25QXX_Write_SR                          0x08001d3f   Thumb Code    32  w25qxx.o(.text)
    W25QXX_Write_Enable                      0x08001d5f   Thumb Code    24  w25qxx.o(.text)
    W25QXX_Wait_Busy                         0x08001d77   Thumb Code    18  w25qxx.o(.text)
    W25QXX_Write_Disable                     0x08001d89   Thumb Code    24  w25qxx.o(.text)
    W25QXX_Erase_Chip                        0x08001da1   Thumb Code    36  w25qxx.o(.text)
    W25QXX_Erase_Sector                      0x08001dc5   Thumb Code    62  w25qxx.o(.text)
    W25QXX_PowerDown                         0x08001e03   Thumb Code    30  w25qxx.o(.text)
    W25QXX_WAKEUP                            0x08001e21   Thumb Code    30  w25qxx.o(.text)
    W25QXX_Read                              0x08001e3f   Thumb Code    72  w25qxx.o(.text)
    W25QXX_Write_Page                        0x08001e87   Thumb Code    78  w25qxx.o(.text)
    W25QXX_PageWrite                         0x08001ed5   Thumb Code   290  w25qxx.o(.text)
    W25QXX_SectorWrite                       0x08001ff7   Thumb Code   208  w25qxx.o(.text)
    SD_Init                                  0x080020c9   Thumb Code    68  tfcard_sdio.o(.text)
    HAL_SD_MspInit                           0x0800210d   Thumb Code   180  tfcard_sdio.o(.text)
    SD_GetCardInfo                           0x080021c1   Thumb Code    18  tfcard_sdio.o(.text)
    SD_GetCardState                          0x080021d3   Thumb Code    20  tfcard_sdio.o(.text)
    SD_ReadSDisk                             0x080021e7   Thumb Code    76  tfcard_sdio.o(.text)
    SD_WriteSDisk                            0x08002233   Thumb Code    76  tfcard_sdio.o(.text)
    RTC_GetWeek                              0x0800229d   Thumb Code   102  rtc.o(.text)
    RTC_SetTimes                             0x08002303   Thumb Code    92  rtc.o(.text)
    RTC_GetTimes                             0x0800235f   Thumb Code    26  rtc.o(.text)
    RTC_InitConfig                           0x08002379   Thumb Code    96  rtc.o(.text)
    HAL_RTC_MspInit                          0x080023d9   Thumb Code    84  rtc.o(.text)
    RTC_Set_WakeUp                           0x0800242d   Thumb Code    54  rtc.o(.text)
    RTC_WKUP_IRQHandler                      0x08002463   Thumb Code    10  rtc.o(.text)
    HAL_RTCEx_WakeUpTimerEventCallback       0x0800246d   Thumb Code    18  rtc.o(.text)
    RTC_SetAlarmA                            0x0800247f   Thumb Code    84  rtc.o(.text)
    RTC_Alarm_IRQHandler                     0x080024d3   Thumb Code    10  rtc.o(.text)
    GetMoonDay                               0x080024fd   Thumb Code   282  lunar_calendar.o(.text)
    GetChinaCalendar                         0x08002617   Thumb Code   506  lunar_calendar.o(.text)
    GetSkyEarth                              0x08002811   Thumb Code    56  lunar_calendar.o(.text)
    StrCopyss                                0x08002849   Thumb Code    24  lunar_calendar.o(.text)
    GetLunarCalendarStr                      0x08002861   Thumb Code   296  lunar_calendar.o(.text)
    GetJieQi                                 0x08002989   Thumb Code   158  lunar_calendar.o(.text)
    GetJieQiStr                              0x08002a27   Thumb Code   300  lunar_calendar.o(.text)
    HAL_InitTick                             0x08002b83   Thumb Code    64  stm32f4xx_hal.o(.text)
    HAL_Init                                 0x08002bc3   Thumb Code    44  stm32f4xx_hal.o(.text)
    HAL_DeInit                               0x08002bf1   Thumb Code    64  stm32f4xx_hal.o(.text)
    HAL_IncTick                              0x08002c31   Thumb Code    16  stm32f4xx_hal.o(.text)
    HAL_GetTick                              0x08002c41   Thumb Code     6  stm32f4xx_hal.o(.text)
    HAL_GetTickPrio                          0x08002c47   Thumb Code     6  stm32f4xx_hal.o(.text)
    HAL_SetTickFreq                          0x08002c4d   Thumb Code    32  stm32f4xx_hal.o(.text)
    HAL_GetTickFreq                          0x08002c6d   Thumb Code     6  stm32f4xx_hal.o(.text)
    HAL_Delay                                0x08002c73   Thumb Code    36  stm32f4xx_hal.o(.text)
    HAL_SuspendTick                          0x08002c97   Thumb Code    18  stm32f4xx_hal.o(.text)
    HAL_ResumeTick                           0x08002ca9   Thumb Code    18  stm32f4xx_hal.o(.text)
    HAL_GetHalVersion                        0x08002cbb   Thumb Code     4  stm32f4xx_hal.o(.text)
    HAL_GetREVID                             0x08002cbf   Thumb Code     8  stm32f4xx_hal.o(.text)
    HAL_GetDEVID                             0x08002cc7   Thumb Code    10  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_EnableDBGSleepMode            0x08002cd1   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_DisableDBGSleepMode           0x08002cdf   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_EnableDBGStopMode             0x08002ced   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_DisableDBGStopMode            0x08002cfb   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_EnableDBGStandbyMode          0x08002d09   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_DisableDBGStandbyMode         0x08002d17   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_EnableCompensationCell               0x08002d25   Thumb Code     8  stm32f4xx_hal.o(.text)
    HAL_DisableCompensationCell              0x08002d2d   Thumb Code     8  stm32f4xx_hal.o(.text)
    HAL_GetUIDw0                             0x08002d35   Thumb Code     6  stm32f4xx_hal.o(.text)
    HAL_GetUIDw1                             0x08002d3b   Thumb Code     8  stm32f4xx_hal.o(.text)
    HAL_GetUIDw2                             0x08002d43   Thumb Code     8  stm32f4xx_hal.o(.text)
    HAL_NVIC_SetPriorityGrouping             0x08002d95   Thumb Code    36  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_SetPriority                     0x08002db9   Thumb Code   124  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_EnableIRQ                       0x08002e35   Thumb Code    32  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_DisableIRQ                      0x08002e55   Thumb Code    62  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_SystemReset                     0x08002e93   Thumb Code    64  stm32f4xx_hal_cortex.o(.text)
    HAL_SYSTICK_Config                       0x08002ed3   Thumb Code    52  stm32f4xx_hal_cortex.o(.text)
    HAL_MPU_Disable                          0x08002f07   Thumb Code    42  stm32f4xx_hal_cortex.o(.text)
    HAL_MPU_Enable                           0x08002f31   Thumb Code    60  stm32f4xx_hal_cortex.o(.text)
    HAL_MPU_ConfigRegion                     0x08002f6d   Thumb Code    90  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_GetPriorityGrouping             0x08002fc7   Thumb Code     8  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_GetPriority                     0x08002fcf   Thumb Code   138  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_SetPendingIRQ                   0x08003059   Thumb Code    32  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_GetPendingIRQ                   0x08003079   Thumb Code    46  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_ClearPendingIRQ                 0x080030a7   Thumb Code    30  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_GetActive                       0x080030c5   Thumb Code    46  stm32f4xx_hal_cortex.o(.text)
    HAL_SYSTICK_CLKSourceConfig              0x080030f3   Thumb Code    40  stm32f4xx_hal_cortex.o(.text)
    HAL_SYSTICK_Callback                     0x0800311b   Thumb Code     2  stm32f4xx_hal_cortex.o(.text)
    HAL_SYSTICK_IRQHandler                   0x0800311d   Thumb Code     8  stm32f4xx_hal_cortex.o(.text)
    HAL_GPIO_Init                            0x08003145   Thumb Code   466  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_DeInit                          0x08003317   Thumb Code   316  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_ReadPin                         0x08003453   Thumb Code    16  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_WritePin                        0x08003463   Thumb Code    12  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_TogglePin                       0x0800346f   Thumb Code    18  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_LockPin                         0x08003481   Thumb Code    46  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_EXTI_Callback                   0x080034af   Thumb Code     2  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_EXTI_IRQHandler                 0x080034b1   Thumb Code    28  stm32f4xx_hal_gpio.o(.text)
    HAL_PWR_DeInit                           0x080034f9   Thumb Code    24  stm32f4xx_hal_pwr.o(.text)
    HAL_PWR_EnableBkUpAccess                 0x08003511   Thumb Code     8  stm32f4xx_hal_pwr.o(.text)
    HAL_PWR_DisableBkUpAccess                0x08003519   Thumb Code     8  stm32f4xx_hal_pwr.o(.text)
    HAL_PWR_ConfigPVD                        0x08003521   Thumb Code   172  stm32f4xx_hal_pwr.o(.text)
    HAL_PWR_EnablePVD                        0x080035cd   Thumb Code     8  stm32f4xx_hal_pwr.o(.text)
    HAL_PWR_DisablePVD                       0x080035d5   Thumb Code     8  stm32f4xx_hal_pwr.o(.text)
    HAL_PWR_EnableWakeUpPin                  0x080035dd   Thumb Code    12  stm32f4xx_hal_pwr.o(.text)
    HAL_PWR_DisableWakeUpPin                 0x080035e9   Thumb Code    12  stm32f4xx_hal_pwr.o(.text)
    HAL_PWR_EnterSLEEPMode                   0x080035f5   Thumb Code    28  stm32f4xx_hal_pwr.o(.text)
    HAL_PWR_EnterSTOPMode                    0x08003611   Thumb Code    54  stm32f4xx_hal_pwr.o(.text)
    HAL_PWR_EnterSTANDBYMode                 0x08003647   Thumb Code    32  stm32f4xx_hal_pwr.o(.text)
    HAL_PWR_PVDCallback                      0x08003667   Thumb Code     2  stm32f4xx_hal_pwr.o(.text)
    HAL_PWR_PVD_IRQHandler                   0x08003669   Thumb Code    30  stm32f4xx_hal_pwr.o(.text)
    HAL_PWR_EnableSleepOnExit                0x08003687   Thumb Code    14  stm32f4xx_hal_pwr.o(.text)
    HAL_PWR_DisableSleepOnExit               0x08003695   Thumb Code    14  stm32f4xx_hal_pwr.o(.text)
    HAL_PWR_EnableSEVOnPend                  0x080036a3   Thumb Code    14  stm32f4xx_hal_pwr.o(.text)
    HAL_PWR_DisableSEVOnPend                 0x080036b1   Thumb Code    14  stm32f4xx_hal_pwr.o(.text)
    HAL_RCC_OscConfig                        0x080036d9   Thumb Code  1086  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetSysClockFreq                  0x08003b17   Thumb Code   164  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_ClockConfig                      0x08003bbb   Thumb Code   388  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_MCOConfig                        0x08003d3f   Thumb Code   186  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_EnableCSS                        0x08003df9   Thumb Code     8  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_DisableCSS                       0x08003e01   Thumb Code     8  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetHCLKFreq                      0x08003e09   Thumb Code     6  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetPCLK1Freq                     0x08003e0f   Thumb Code    24  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetPCLK2Freq                     0x08003e27   Thumb Code    24  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetOscConfig                     0x08003e3f   Thumb Code   278  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetClockConfig                   0x08003f55   Thumb Code    66  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_CSSCallback                      0x08003f97   Thumb Code     2  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_NMI_IRQHandler                   0x08003f99   Thumb Code    30  stm32f4xx_hal_rcc.o(.text)
    HAL_RCCEx_PeriphCLKConfig                0x08003fc1   Thumb Code   396  stm32f4xx_hal_rcc_ex.o(.text)
    HAL_RCCEx_GetPeriphCLKConfig             0x0800414d   Thumb Code    54  stm32f4xx_hal_rcc_ex.o(.text)
    HAL_RCCEx_GetPeriphCLKFreq               0x08004183   Thumb Code   128  stm32f4xx_hal_rcc_ex.o(.text)
    HAL_RCCEx_EnablePLLI2S                   0x08004203   Thumb Code   104  stm32f4xx_hal_rcc_ex.o(.text)
    HAL_RCCEx_DisablePLLI2S                  0x0800426b   Thumb Code    46  stm32f4xx_hal_rcc_ex.o(.text)
    HAL_RCC_DeInit                           0x08004299   Thumb Code   398  stm32f4xx_hal_rcc_ex.o(.text)
    HAL_DMA_Init                             0x0800450d   Thumb Code   232  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_DeInit                           0x080045f5   Thumb Code   112  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_Start                            0x08004691   Thumb Code   102  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_Start_IT                         0x080046f7   Thumb Code   158  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_Abort                            0x08004795   Thumb Code   180  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_Abort_IT                         0x08004849   Thumb Code    40  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_PollForTransfer                  0x08004871   Thumb Code   346  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_IRQHandler                       0x080049cb   Thumb Code   570  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_RegisterCallback                 0x08004c05   Thumb Code    94  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_UnRegisterCallback               0x08004c63   Thumb Code   124  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_GetState                         0x08004cdf   Thumb Code     8  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_GetError                         0x08004ce7   Thumb Code     6  stm32f4xx_hal_dma.o(.text)
    FSMC_NORSRAM_Init                        0x08004cf1   Thumb Code    88  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NORSRAM_DeInit                      0x08004d49   Thumb Code    56  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NORSRAM_Timing_Init                 0x08004d81   Thumb Code    68  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NORSRAM_Extended_Timing_Init        0x08004dc5   Thumb Code    66  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NORSRAM_WriteOperation_Enable       0x08004e07   Thumb Code    18  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NORSRAM_WriteOperation_Disable      0x08004e19   Thumb Code    18  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NAND_Init                           0x08004e2b   Thumb Code    70  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NAND_CommonSpace_Timing_Init        0x08004e71   Thumb Code    54  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NAND_AttributeSpace_Timing_Init     0x08004ea7   Thumb Code    54  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NAND_DeInit                         0x08004edd   Thumb Code    66  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NAND_ECC_Enable                     0x08004f1f   Thumb Code    28  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NAND_ECC_Disable                    0x08004f3b   Thumb Code    28  stm32f4xx_ll_fsmc.o(.text)
    FSMC_NAND_GetECC                         0x08004f57   Thumb Code    88  stm32f4xx_ll_fsmc.o(.text)
    FSMC_PCCARD_Init                         0x08004faf   Thumb Code    38  stm32f4xx_ll_fsmc.o(.text)
    FSMC_PCCARD_CommonSpace_Timing_Init      0x08004fd5   Thumb Code    38  stm32f4xx_ll_fsmc.o(.text)
    FSMC_PCCARD_AttributeSpace_Timing_Init   0x08004ffb   Thumb Code    38  stm32f4xx_ll_fsmc.o(.text)
    FSMC_PCCARD_IOSpace_Timing_Init          0x08005021   Thumb Code    38  stm32f4xx_ll_fsmc.o(.text)
    FSMC_PCCARD_DeInit                       0x08005047   Thumb Code    32  stm32f4xx_ll_fsmc.o(.text)
    HAL_SRAM_Init                            0x0800507b   Thumb Code    88  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_MspDeInit                       0x080050d3   Thumb Code     2  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_DeInit                          0x080050d5   Thumb Code    34  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_DMA_XferCpltCallback            0x080050f7   Thumb Code     2  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_DMA_XferErrorCallback           0x080050f9   Thumb Code     2  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_Read_8b                         0x080050fb   Thumb Code    70  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_Write_8b                        0x08005141   Thumb Code    82  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_Read_16b                        0x08005193   Thumb Code    70  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_Write_16b                       0x080051d9   Thumb Code    82  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_Read_32b                        0x0800522b   Thumb Code    68  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_Write_32b                       0x0800526f   Thumb Code    80  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_Read_DMA                        0x080052bf   Thumb Code    86  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_Write_DMA                       0x08005315   Thumb Code    98  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_WriteOperation_Enable           0x08005377   Thumb Code    54  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_WriteOperation_Disable          0x080053ad   Thumb Code    60  stm32f4xx_hal_sram.o(.text)
    HAL_SRAM_GetState                        0x080053e9   Thumb Code     8  stm32f4xx_hal_sram.o(.text)
    HAL_UART_Init                            0x0800575b   Thumb Code   114  stm32f4xx_hal_uart.o(.text)
    HAL_HalfDuplex_Init                      0x080057cd   Thumb Code   128  stm32f4xx_hal_uart.o(.text)
    HAL_LIN_Init                             0x0800584d   Thumb Code   142  stm32f4xx_hal_uart.o(.text)
    HAL_MultiProcessor_Init                  0x080058db   Thumb Code   154  stm32f4xx_hal_uart.o(.text)
    HAL_UART_MspDeInit                       0x08005975   Thumb Code     2  stm32f4xx_hal_uart.o(.text)
    HAL_UART_DeInit                          0x08005977   Thumb Code    58  stm32f4xx_hal_uart.o(.text)
    HAL_UART_Transmit                        0x08005a1d   Thumb Code   214  stm32f4xx_hal_uart.o(.text)
    HAL_UART_Receive                         0x08005af3   Thumb Code   222  stm32f4xx_hal_uart.o(.text)
    HAL_UART_Transmit_IT                     0x08005bd1   Thumb Code    88  stm32f4xx_hal_uart.o(.text)
    HAL_UART_Receive_IT                      0x08005c29   Thumb Code   112  stm32f4xx_hal_uart.o(.text)
    HAL_UART_ErrorCallback                   0x08005c99   Thumb Code     2  stm32f4xx_hal_uart.o(.text)
    HAL_UART_TxHalfCpltCallback              0x08005d1f   Thumb Code     2  stm32f4xx_hal_uart.o(.text)
    HAL_UART_TxCpltCallback                  0x08005d2f   Thumb Code     2  stm32f4xx_hal_uart.o(.text)
    HAL_UART_Transmit_DMA                    0x08005d67   Thumb Code   142  stm32f4xx_hal_uart.o(.text)
    HAL_UART_RxHalfCpltCallback              0x08005df5   Thumb Code     2  stm32f4xx_hal_uart.o(.text)
    HAL_UART_Receive_DMA                     0x08005e4d   Thumb Code   182  stm32f4xx_hal_uart.o(.text)
    HAL_UART_DMAPause                        0x08005f03   Thumb Code   124  stm32f4xx_hal_uart.o(.text)
    HAL_UART_DMAResume                       0x08005f7f   Thumb Code   126  stm32f4xx_hal_uart.o(.text)
    HAL_UART_DMAStop                         0x08005ffd   Thumb Code   102  stm32f4xx_hal_uart.o(.text)
    HAL_UART_Abort                           0x08006063   Thumb Code   170  stm32f4xx_hal_uart.o(.text)
    HAL_UART_AbortTransmit                   0x0800610d   Thumb Code    90  stm32f4xx_hal_uart.o(.text)
    HAL_UART_AbortReceive                    0x08006167   Thumb Code   124  stm32f4xx_hal_uart.o(.text)
    HAL_UART_AbortCpltCallback               0x080061e3   Thumb Code     2  stm32f4xx_hal_uart.o(.text)
    HAL_UART_Abort_IT                        0x0800624d   Thumb Code   216  stm32f4xx_hal_uart.o(.text)
    HAL_UART_AbortTransmitCpltCallback       0x08006325   Thumb Code     2  stm32f4xx_hal_uart.o(.text)
    HAL_UART_AbortTransmit_IT                0x0800633f   Thumb Code   104  stm32f4xx_hal_uart.o(.text)
    HAL_UART_AbortReceiveCpltCallback        0x080063a7   Thumb Code     2  stm32f4xx_hal_uart.o(.text)
    HAL_UART_AbortReceive_IT                 0x080063c1   Thumb Code   116  stm32f4xx_hal_uart.o(.text)
    HAL_UART_IRQHandler                      0x08006577   Thumb Code   354  stm32f4xx_hal_uart.o(.text)
    HAL_LIN_SendBreak                        0x080066d9   Thumb Code    62  stm32f4xx_hal_uart.o(.text)
    HAL_MultiProcessor_EnterMuteMode         0x08006717   Thumb Code    62  stm32f4xx_hal_uart.o(.text)
    HAL_MultiProcessor_ExitMuteMode          0x08006755   Thumb Code    62  stm32f4xx_hal_uart.o(.text)
    HAL_HalfDuplex_EnableTransmitter         0x08006793   Thumb Code    68  stm32f4xx_hal_uart.o(.text)
    HAL_HalfDuplex_EnableReceiver            0x080067d7   Thumb Code    68  stm32f4xx_hal_uart.o(.text)
    HAL_UART_GetState                        0x0800681b   Thumb Code    20  stm32f4xx_hal_uart.o(.text)
    HAL_UART_GetError                        0x0800682f   Thumb Code     6  stm32f4xx_hal_uart.o(.text)
    HAL_SD_GetCardCSD                        0x08006835   Thumb Code   496  stm32f4xx_hal_sd.o(.text)
    HAL_SD_InitCard                          0x08006c0f   Thumb Code   128  stm32f4xx_hal_sd.o(.text)
    HAL_SD_Init                              0x08006c91   Thumb Code    54  stm32f4xx_hal_sd.o(.text)
    HAL_SD_MspDeInit                         0x08006cc7   Thumb Code     2  stm32f4xx_hal_sd.o(.text)
    HAL_SD_DeInit                            0x08006cd7   Thumb Code    40  stm32f4xx_hal_sd.o(.text)
    HAL_SD_ReadBlocks                        0x08006cff   Thumb Code   588  stm32f4xx_hal_sd.o(.text)
    HAL_SD_WriteBlocks                       0x08006f4b   Thumb Code   522  stm32f4xx_hal_sd.o(.text)
    HAL_SD_ReadBlocks_IT                     0x08007155   Thumb Code   248  stm32f4xx_hal_sd.o(.text)
    HAL_SD_WriteBlocks_IT                    0x0800724d   Thumb Code   246  stm32f4xx_hal_sd.o(.text)
    HAL_SD_ErrorCallback                     0x08007343   Thumb Code     2  stm32f4xx_hal_sd.o(.text)
    HAL_SD_GetCardState                      0x08007377   Thumb Code    40  stm32f4xx_hal_sd.o(.text)
    HAL_SD_RxCpltCallback                    0x080073fb   Thumb Code     2  stm32f4xx_hal_sd.o(.text)
    HAL_SD_ReadBlocks_DMA                    0x08007443   Thumb Code   284  stm32f4xx_hal_sd.o(.text)
    HAL_SD_WriteBlocks_DMA                   0x08007571   Thumb Code   282  stm32f4xx_hal_sd.o(.text)
    HAL_SD_Erase                             0x0800768b   Thumb Code   274  stm32f4xx_hal_sd.o(.text)
    HAL_SD_AbortCallback                     0x0800779d   Thumb Code     2  stm32f4xx_hal_sd.o(.text)
    HAL_SD_TxCpltCallback                    0x08007895   Thumb Code     2  stm32f4xx_hal_sd.o(.text)
    HAL_SD_IRQHandler                        0x08007897   Thumb Code   504  stm32f4xx_hal_sd.o(.text)
    HAL_SD_GetState                          0x08007a8f   Thumb Code     8  stm32f4xx_hal_sd.o(.text)
    HAL_SD_GetError                          0x08007a97   Thumb Code     6  stm32f4xx_hal_sd.o(.text)
    HAL_SD_GetCardCID                        0x08007a9d   Thumb Code   204  stm32f4xx_hal_sd.o(.text)
    HAL_SD_GetCardStatus                     0x08007c9b   Thumb Code   202  stm32f4xx_hal_sd.o(.text)
    HAL_SD_GetCardInfo                       0x08007d65   Thumb Code    38  stm32f4xx_hal_sd.o(.text)
    HAL_SD_ConfigWideBusOperation            0x08007f95   Thumb Code   168  stm32f4xx_hal_sd.o(.text)
    HAL_SD_Abort                             0x0800803d   Thumb Code   106  stm32f4xx_hal_sd.o(.text)
    HAL_SD_Abort_IT                          0x080080a7   Thumb Code   144  stm32f4xx_hal_sd.o(.text)
    SDIO_Init                                0x08008141   Thumb Code    52  stm32f4xx_ll_sdmmc.o(.text)
    SDIO_ReadFIFO                            0x08008175   Thumb Code     8  stm32f4xx_ll_sdmmc.o(.text)
    SDIO_WriteFIFO                           0x0800817d   Thumb Code    12  stm32f4xx_ll_sdmmc.o(.text)
    SDIO_PowerState_ON                       0x08008189   Thumb Code    10  stm32f4xx_ll_sdmmc.o(.text)
    SDIO_PowerState_OFF                      0x08008193   Thumb Code     8  stm32f4xx_ll_sdmmc.o(.text)
    SDIO_GetPowerState                       0x0800819b   Thumb Code    10  stm32f4xx_ll_sdmmc.o(.text)
    SDIO_SendCommand                         0x080081a5   Thumb Code    40  stm32f4xx_ll_sdmmc.o(.text)
    SDIO_GetCommandResponse                  0x080081cd   Thumb Code     8  stm32f4xx_ll_sdmmc.o(.text)
    SDIO_GetResponse                         0x080081d5   Thumb Code    22  stm32f4xx_ll_sdmmc.o(.text)
    SDIO_ConfigData                          0x080081eb   Thumb Code    44  stm32f4xx_ll_sdmmc.o(.text)
    SDIO_GetDataCounter                      0x08008217   Thumb Code     6  stm32f4xx_ll_sdmmc.o(.text)
    SDIO_GetFIFOCount                        0x0800821d   Thumb Code     8  stm32f4xx_ll_sdmmc.o(.text)
    SDIO_SetSDMMCReadWaitMode                0x08008225   Thumb Code    16  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdBlockLength                     0x080083d1   Thumb Code    58  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdReadSingleBlock                 0x0800840b   Thumb Code    58  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdReadMultiBlock                  0x08008445   Thumb Code    58  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdWriteSingleBlock                0x0800847f   Thumb Code    58  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdWriteMultiBlock                 0x080084b9   Thumb Code    58  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdSDEraseStartAdd                 0x080084f3   Thumb Code    58  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdSDEraseEndAdd                   0x0800852d   Thumb Code    58  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdEraseStartAdd                   0x08008567   Thumb Code    58  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdEraseEndAdd                     0x080085a1   Thumb Code    58  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdErase                           0x080085db   Thumb Code    58  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdStopTransfer                    0x08008615   Thumb Code    66  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdSelDesel                        0x08008657   Thumb Code    60  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdGoIdleState                     0x080086cf   Thumb Code    46  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdOperCond                        0x0800874f   Thumb Code    54  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdAppCommand                      0x08008785   Thumb Code    58  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdAppOperCommand                  0x08008809   Thumb Code    56  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdBusWidth                        0x08008841   Thumb Code    58  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdSendSCR                         0x0800887b   Thumb Code    58  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdSendCID                         0x0800890d   Thumb Code    52  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdSendCSD                         0x08008941   Thumb Code    52  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdSetRelAdd                       0x08008a2d   Thumb Code    58  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdSendStatus                      0x08008a67   Thumb Code    58  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdStatusRegister                  0x08008aa1   Thumb Code    58  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdOpCondition                     0x08008adb   Thumb Code    52  stm32f4xx_ll_sdmmc.o(.text)
    SDMMC_CmdSwitch                          0x08008b0f   Thumb Code    58  stm32f4xx_ll_sdmmc.o(.text)
    HAL_SPI_Init                             0x08008b4b   Thumb Code   160  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_MspDeInit                        0x08008beb   Thumb Code     2  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_DeInit                           0x08008bed   Thumb Code    54  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_Transmit                         0x08008d2f   Thumb Code   474  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_TransmitReceive                  0x08008f9d   Thumb Code   722  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_Receive                          0x0800926f   Thumb Code   528  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_TxCpltCallback                   0x0800947f   Thumb Code     2  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_ErrorCallback                    0x08009481   Thumb Code     2  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_Transmit_IT                      0x0800958d   Thumb Code   194  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_RxCpltCallback                   0x0800964f   Thumb Code     2  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_TxRxCpltCallback                 0x08009799   Thumb Code     2  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_TransmitReceive_IT               0x08009a03   Thumb Code   222  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_Receive_IT                       0x08009ae1   Thumb Code   230  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_TxHalfCpltCallback               0x08009c6f   Thumb Code     2  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_Transmit_DMA                     0x08009c7f   Thumb Code   310  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_RxHalfCpltCallback               0x08009e57   Thumb Code     2  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_TxRxHalfCpltCallback             0x08009f0f   Thumb Code     2  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_TransmitReceive_DMA              0x08009f1f   Thumb Code   378  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_Receive_DMA                      0x0800a099   Thumb Code   294  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_Abort                            0x0800a241   Thumb Code   388  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_AbortCpltCallback                0x0800a3c5   Thumb Code     2  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_Abort_IT                         0x0800a4fd   Thumb Code   362  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_DMAPause                         0x0800a667   Thumb Code    50  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_DMAResume                        0x0800a699   Thumb Code    50  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_DMAStop                          0x0800a6cb   Thumb Code    72  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_IRQHandler                       0x0800a727   Thumb Code   372  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_GetState                         0x0800a89b   Thumb Code     8  stm32f4xx_hal_spi.o(.text)
    HAL_SPI_GetError                         0x0800a8a3   Thumb Code     6  stm32f4xx_hal_spi.o(.text)
    HAL_RTC_WaitForSynchro                   0x0800a8ad   Thumb Code    58  stm32f4xx_hal_rtc.o(.text)
    RTC_EnterInitMode                        0x0800a8e7   Thumb Code    62  stm32f4xx_hal_rtc.o(.text)
    HAL_RTC_Init                             0x0800a927   Thumb Code   212  stm32f4xx_hal_rtc.o(.text)
    HAL_RTC_MspDeInit                        0x0800a9fb   Thumb Code     2  stm32f4xx_hal_rtc.o(.text)
    HAL_RTC_DeInit                           0x0800a9fd   Thumb Code   254  stm32f4xx_hal_rtc.o(.text)
    RTC_ByteToBcd2                           0x0800aafb   Thumb Code    26  stm32f4xx_hal_rtc.o(.text)
    HAL_RTC_SetTime                          0x0800ab15   Thumb Code   296  stm32f4xx_hal_rtc.o(.text)
    RTC_Bcd2ToByte                           0x0800ac3d   Thumb Code    22  stm32f4xx_hal_rtc.o(.text)
    HAL_RTC_GetTime                          0x0800ac53   Thumb Code    92  stm32f4xx_hal_rtc.o(.text)
    HAL_RTC_SetDate                          0x0800acaf   Thumb Code   268  stm32f4xx_hal_rtc.o(.text)
    HAL_RTC_GetDate                          0x0800adbb   Thumb Code    72  stm32f4xx_hal_rtc.o(.text)
    HAL_RTC_SetAlarm                         0x0800ae03   Thumb Code   460  stm32f4xx_hal_rtc.o(.text)
    HAL_RTC_SetAlarm_IT                      0x0800afcf   Thumb Code   522  stm32f4xx_hal_rtc.o(.text)
    HAL_RTC_DeactivateAlarm                  0x0800b1d9   Thumb Code   244  stm32f4xx_hal_rtc.o(.text)
    HAL_RTC_GetAlarm                         0x0800b2cd   Thumb Code   154  stm32f4xx_hal_rtc.o(.text)
    HAL_RTC_AlarmAEventCallback              0x0800b367   Thumb Code     2  stm32f4xx_hal_rtc.o(.text)
    HAL_RTC_AlarmIRQHandler                  0x0800b369   Thumb Code   104  stm32f4xx_hal_rtc.o(.text)
    HAL_RTC_PollForAlarmAEvent               0x0800b3d1   Thumb Code    74  stm32f4xx_hal_rtc.o(.text)
    HAL_RTC_GetState                         0x0800b41b   Thumb Code     6  stm32f4xx_hal_rtc.o(.text)
    HAL_RTCEx_SetTimeStamp                   0x0800b425   Thumb Code   122  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_SetTimeStamp_IT                0x0800b49f   Thumb Code   178  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_DeactivateTimeStamp            0x0800b551   Thumb Code    96  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_GetTimeStamp                   0x0800b5b1   Thumb Code   166  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_SetTamper                      0x0800b657   Thumb Code   124  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_SetTamper_IT                   0x0800b6d3   Thumb Code   200  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_DeactivateTamper               0x0800b79b   Thumb Code    50  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_Tamper2EventCallback           0x0800b7cd   Thumb Code     2  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_Tamper1EventCallback           0x0800b7cf   Thumb Code     2  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_TimeStampEventCallback         0x0800b7d1   Thumb Code     2  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_TamperTimeStampIRQHandler      0x0800b7d3   Thumb Code   146  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_PollForTimeStampEvent          0x0800b865   Thumb Code    92  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_PollForTamper1Event            0x0800b8c1   Thumb Code    88  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_PollForTamper2Event            0x0800b919   Thumb Code    74  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_SetWakeUpTimer                 0x0800b963   Thumb Code   254  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_SetWakeUpTimer_IT              0x0800ba61   Thumb Code   316  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_DeactivateWakeUpTimer          0x0800bb9d   Thumb Code   152  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_GetWakeUpTimer                 0x0800bc35   Thumb Code    10  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_WakeUpTimerIRQHandler          0x0800bc41   Thumb Code    62  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_PollForWakeUpTimerEvent        0x0800bc7f   Thumb Code    74  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_BKUPWrite                      0x0800bcc9   Thumb Code    18  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_BKUPRead                       0x0800bcdb   Thumb Code    18  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_SetCoarseCalib                 0x0800bced   Thumb Code   138  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_DeactivateCoarseCalib          0x0800bd77   Thumb Code   126  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_SetSmoothCalib                 0x0800bdf5   Thumb Code   172  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_SetSynchroShift                0x0800bea1   Thumb Code   224  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_SetCalibrationOutPut           0x0800bf81   Thumb Code   100  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_DeactivateCalibrationOutPut    0x0800bfe5   Thumb Code    78  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_SetRefClock                    0x0800c033   Thumb Code   126  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_DeactivateRefClock             0x0800c0b1   Thumb Code   126  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_EnableBypassShadow             0x0800c12f   Thumb Code    78  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_DisableBypassShadow            0x0800c17d   Thumb Code    78  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_AlarmBEventCallback            0x0800c1cb   Thumb Code     2  stm32f4xx_hal_rtc_ex.o(.text)
    HAL_RTCEx_PollForAlarmBEvent             0x0800c1cd   Thumb Code    74  stm32f4xx_hal_rtc_ex.o(.text)
    disk_initialize                          0x0800c219   Thumb Code    52  diskio.o(.text)
    disk_status                              0x0800c24d   Thumb Code     6  diskio.o(.text)
    disk_read                                0x0800c253   Thumb Code   122  diskio.o(.text)
    disk_write                               0x0800c2cd   Thumb Code   122  diskio.o(.text)
    disk_ioctl                               0x0800c347   Thumb Code   124  diskio.o(.text)
    get_fattime                              0x0800c3c3   Thumb Code     4  diskio.o(.text)
    ff_memalloc                              0x0800c3c7   Thumb Code    14  diskio.o(.text)
    ff_memfree                               0x0800c3d5   Thumb Code    14  diskio.o(.text)
    clust2sect                               0x0800c59b   Thumb Code    26  ff.o(.text)
    get_fat                                  0x0800c5b5   Thumb Code   232  ff.o(.text)
    put_fat                                  0x0800c69d   Thumb Code   310  ff.o(.text)
    f_mount                                  0x0800d9af   Thumb Code    86  ff.o(.text)
    f_open                                   0x0800da05   Thumb Code   384  ff.o(.text)
    f_read                                   0x0800db85   Thumb Code   450  ff.o(.text)
    f_write                                  0x0800dd47   Thumb Code   498  ff.o(.text)
    f_sync                                   0x0800df39   Thumb Code   176  ff.o(.text)
    f_close                                  0x0800dfe9   Thumb Code    32  ff.o(.text)
    f_lseek                                  0x0800e009   Thumb Code   654  ff.o(.text)
    f_opendir                                0x0800e297   Thumb Code   136  ff.o(.text)
    f_closedir                               0x0800e31f   Thumb Code    22  ff.o(.text)
    f_readdir                                0x0800e335   Thumb Code   114  ff.o(.text)
    f_stat                                   0x0800e3a7   Thumb Code    84  ff.o(.text)
    f_getfree                                0x0800e3fb   Thumb Code   258  ff.o(.text)
    f_truncate                               0x0800e4fd   Thumb Code   184  ff.o(.text)
    f_unlink                                 0x0800e5b5   Thumb Code   194  ff.o(.text)
    f_mkdir                                  0x0800e677   Thumb Code   380  ff.o(.text)
    f_chmod                                  0x0800e7f3   Thumb Code   112  ff.o(.text)
    f_utime                                  0x0800e863   Thumb Code   110  ff.o(.text)
    f_rename                                 0x0800e8d1   Thumb Code   314  ff.o(.text)
    f_getlabel                               0x0800ea0b   Thumb Code   190  ff.o(.text)
    f_setlabel                               0x0800eac9   Thumb Code   470  ff.o(.text)
    f_mkfs                                   0x0800ec9f   Thumb Code  1570  ff.o(.text)
    f_gets                                   0x0800f2c1   Thumb Code    82  ff.o(.text)
    f_putc                                   0x0800f357   Thumb Code    64  ff.o(.text)
    f_puts                                   0x0800f397   Thumb Code    74  ff.o(.text)
    f_printf                                 0x0800f3e1   Thumb Code   674  ff.o(.text)
    ff_convert                               0x0800f685   Thumb Code   146  cc936.o(.text)
    ff_wtoupper                              0x0800f717   Thumb Code    46  cc936.o(.text)
    GPIO_group_OUT                           0x0800f751   Thumb Code   454  common.o(.text)
    GPIO_bits_OUT                            0x0800f917   Thumb Code    92  common.o(.text)
    Stm32_Clock_Init                         0x0800f973   Thumb Code   190  common.o(.text)
    delay_init                               0x0800fa31   Thumb Code    16  common.o(.text)
    delay_us                                 0x0800fa41   Thumb Code    68  common.o(.text)
    delay_ms                                 0x0800fa85   Thumb Code    24  common.o(.text)
    fupd_prog                                0x0800faad   Thumb Code   144  updatefont.o(.text)
    updata_fontx                             0x0800fb3d   Thumb Code   346  updatefont.o(.text)
    update_hzfont                            0x0800fc97   Thumb Code  1040  updatefont.o(.text)
    font_init                                0x080100a7   Thumb Code    64  updatefont.o(.text)
    Get_HzMat                                0x08010141   Thumb Code   220  showhz.o(.text)
    LCD_Display1HZ                           0x0801021d   Thumb Code   186  showhz.o(.text)
    LCD_DisplayHZstr                         0x080102d7   Thumb Code   136  showhz.o(.text)
    Show_Str_Mid                             0x0801035f   Thumb Code    86  showhz.o(.text)
    __aeabi_uldivmod                         0x080103c5   Thumb Code    98  uldiv.o(.text)
    strlen                                   0x08010427   Thumb Code    14  strlen.o(.text)
    strcmp                                   0x08010435   Thumb Code    28  strcmp.o(.text)
    strcpy                                   0x08010451   Thumb Code    18  strcpy.o(.text)
    __aeabi_llsl                             0x08010463   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08010463   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08010481   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08010481   Thumb Code     0  llushr.o(.text)
    __scatterload                            0x080104a1   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080104a1   Thumb Code     0  init.o(.text)
    __decompress                             0x080104c5   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x080104c5   Thumb Code    86  __dczerorl2.o(.text)
    __scatterload_copy                       0x0801051b   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08010529   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x0801052b   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    AHBPrescTable                            0x08010538   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x08010548   Data           8  system_stm32f4xx.o(.constdata)
    char_1206                                0x08010550   Data        1140  lcd.o(.constdata)
    char_1608                                0x080109c4   Data        1520  lcd.o(.constdata)
    char_2412                                0x08010fb4   Data        4560  lcd.o(.constdata)
    memtblsize                               0x08012184   Data           8  malloc.o(.constdata)
    memblksize                               0x0801218c   Data           8  malloc.o(.constdata)
    memsize                                  0x08012194   Data           8  malloc.o(.constdata)
    month_amendBuf                           0x0801219c   Data          12  rtc.o(.constdata)
    year_code                                0x080121a8   Data         597  lunar_calendar.o(.constdata)
    YearMonthBit                             0x080123fd   Data         153  lunar_calendar.o(.constdata)
    days                                     0x08012496   Data          24  lunar_calendar.o(.constdata)
    day_code1                                0x080124ae   Data           9  lunar_calendar.o(.constdata)
    day_code2                                0x080124b8   Data           6  lunar_calendar.o(.constdata)
    GBK24_PATH                               0x080128cc   Data           4  updatefont.o(.constdata)
    GBK16_PATH                               0x080128d0   Data           4  updatefont.o(.constdata)
    GBK12_PATH                               0x080128d4   Data           4  updatefont.o(.constdata)
    UNIGBK_PATH                              0x080128d8   Data           4  updatefont.o(.constdata)
    Region$$Table$$Base                      0x08012adc   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08012afc   Number         0  anon$$obj.o(Region$$Table)
    weekdate                                 0x20000000   Data          28  main.o(.data)
    set_option                               0x2000001c   Data          24  main.o(.data)
    SystemCoreClock                          0x20000034   Data           4  system_stm32f4xx.o(.data)
    keydown_data                             0x20000038   Data           1  key.o(.data)
    keyup_data                               0x20000039   Data           1  key.o(.data)
    key_time                                 0x2000003a   Data           2  key.o(.data)
    key_tem                                  0x2000003c   Data           1  key.o(.data)
    key_bak                                  0x2000003d   Data           1  key.o(.data)
    BRUSH_COLOR                              0x2000003e   Data           2  lcd.o(.data)
    BACK_COLOR                               0x20000040   Data           2  lcd.o(.data)
    lcd_id                                   0x20000042   Data           2  lcd.o(.data)
    dir_flag                                 0x20000044   Data           1  lcd.o(.data)
    lcd_width                                0x20000046   Data           2  lcd.o(.data)
    lcd_height                               0x20000048   Data           2  lcd.o(.data)
    write_gramcmd                            0x2000004a   Data           2  lcd.o(.data)
    setxcmd                                  0x2000004c   Data           2  lcd.o(.data)
    setycmd                                  0x2000004e   Data           2  lcd.o(.data)
    uart_byte_count                          0x20000050   Data           1  usart1.o(.data)
    aRxBuffer                                0x20000051   Data           1  usart1.o(.data)
    membase                                  0x20000054   Data           8  malloc.o(.data)
    memmap                                   0x2000005c   Data           8  malloc.o(.data)
    memrdy                                   0x20000064   Data           2  malloc.o(.data)
    W25QXX_ID                                0x20000066   Data           2  w25qxx.o(.data)
    RTC_DateStruct                           0x20000068   Data           4  rtc.o(.data)
    JieQiStr                                 0x2000006c   Data          96  lunar_calendar.o(.data)
    sky                                      0x200000cc   Data          40  lunar_calendar.o(.data)
    earth                                    0x200000f4   Data          48  lunar_calendar.o(.data)
    monthcode                                0x20000124   Data          48  lunar_calendar.o(.data)
    nongliday                                0x20000154   Data          16  lunar_calendar.o(.data)
    uwTick                                   0x20000164   Data           4  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000168   Data           4  stm32f4xx_hal.o(.data)
    uwTickFreq                               0x2000016c   Data           1  stm32f4xx_hal.o(.data)
    FLASH_SECTOR_COUNT                       0x2000016e   Data           2  diskio.o(.data)
    LCDSRAM_Handler                          0x2000017c   Data          80  lcd.o(.bss)
    receive_str                              0x200001cc   Data         100  usart1.o(.bss)
    UART1_Handler                            0x20000230   Data          64  usart1.o(.bss)
    inmenbase                                0x20000270   Data       81920  malloc.o(.bss)
    inmemmapbase                             0x20014270   Data        5120  malloc.o(.bss)
    SPI1_Handler                             0x20015670   Data          88  spi.o(.bss)
    TS_BUFFER                                0x200156c8   Data        4096  w25qxx.o(.bss)
    SDCARD_Handler                           0x200166c8   Data         132  tfcard_sdio.o(.bss)
    SDCardInfo                               0x2001674c   Data          32  tfcard_sdio.o(.bss)
    SDTxDMAHandler                           0x2001676c   Data          96  tfcard_sdio.o(.bss)
    SDRxDMAHandler                           0x200167cc   Data          96  tfcard_sdio.o(.bss)
    SDIO_DATA_BUFFER                         0x2001682c   Data         512  tfcard_sdio.o(.bss)
    RTC_Handler                              0x20016a2c   Data          32  rtc.o(.bss)
    RTC_TimeStruct                           0x20016a4c   Data          20  rtc.o(.bss)
    hzfont_info                              0x20016a60   Data          33  updatefont.o(.bss)
    __initial_sp                             0x20016e88   Data           0  startup_stm32f407xx.o(STACK)
    exmen2base                               0x68000000   Data       819200  malloc.o(.ARM.__AT_0x68000000)
    exmemmapbase                             0x680c8000   Data       51200  malloc.o(.ARM.__AT_0x680C8000)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00012c78, Max: 0x00100000, ABSOLUTE, COMPRESSED[0x00012bf4])

    Execution Region ER_IROM1 (Base: 0x08000000, Size: 0x00012afc, Max: 0x00100000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x00000188   Data   RO          372    RESET               startup_stm32f407xx.o
    0x08000188   0x00000000   Code   RO         1490  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x00000004   Code   RO         1501    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x00000004   Code   RO         1504    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x00000000   Code   RO         1506    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x00000000   Code   RO         1508    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x00000008   Code   RO         1509    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x00000000   Code   RO         1511    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000198   0x00000000   Code   RO         1513    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000198   0x00000004   Code   RO         1502    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x0800019c   0x00000010   Code   RO         1270    .emb_text           common.o
    0x080001ac   0x0000054c   Code   RO            4    .text               main.o
    0x080006f8   0x00000020   Code   RO          288    .text               stm32f4xx_it.o
    0x08000718   0x00000120   Code   RO          318    .text               system_stm32f4xx.o
    0x08000838   0x00000004   Code   RO          349    .text               stm32f4xx_hal_msp.o
    0x0800083c   0x00000024   Code   RO          373    .text               startup_stm32f407xx.o
    0x08000860   0x0000005c   Code   RO          380    .text               led.o
    0x080008bc   0x00000124   Code   RO          404    .text               key.o
    0x080009e0   0x00000d70   Code   RO          434    .text               lcd.o
    0x08001750   0x000001f0   Code   RO          469    .text               usart1.o
    0x08001940   0x0000021c   Code   RO          513    .text               malloc.o
    0x08001b5c   0x00000128   Code   RO          544    .text               spi.o
    0x08001c84   0x00000442   Code   RO          574    .text               w25qxx.o
    0x080020c6   0x00000002   PAD
    0x080020c8   0x000001d4   Code   RO          609    .text               tfcard_sdio.o
    0x0800229c   0x00000260   Code   RO          642    .text               rtc.o
    0x080024fc   0x00000684   Code   RO          674    .text               lunar_calendar.o
    0x08002b80   0x000001f4   Code   RO          706    .text               stm32f4xx_hal.o
    0x08002d74   0x000003d0   Code   RO          733    .text               stm32f4xx_hal_cortex.o
    0x08003144   0x000003b4   Code   RO          761    .text               stm32f4xx_hal_gpio.o
    0x080034f8   0x000001dc   Code   RO          785    .text               stm32f4xx_hal_pwr.o
    0x080036d4   0x000008ec   Code   RO          833    .text               stm32f4xx_hal_rcc.o
    0x08003fc0   0x00000474   Code   RO          857    .text               stm32f4xx_hal_rcc_ex.o
    0x08004434   0x000008bc   Code   RO          905    .text               stm32f4xx_hal_dma.o
    0x08004cf0   0x00000388   Code   RO          930    .text               stm32f4xx_ll_fsmc.o
    0x08005078   0x00000380   Code   RO          954    .text               stm32f4xx_hal_sram.o
    0x080053f8   0x0000143c   Code   RO         1002    .text               stm32f4xx_hal_uart.o
    0x08006834   0x0000190c   Code   RO         1026    .text               stm32f4xx_hal_sd.o
    0x08008140   0x00000a08   Code   RO         1050    .text               stm32f4xx_ll_sdmmc.o
    0x08008b48   0x00001d64   Code   RO         1074    .text               stm32f4xx_hal_spi.o
    0x0800a8ac   0x00000b78   Code   RO         1146    .text               stm32f4xx_hal_rtc.o
    0x0800b424   0x00000df2   Code   RO         1170    .text               stm32f4xx_hal_rtc_ex.o
    0x0800c216   0x00000002   PAD
    0x0800c218   0x000001d4   Code   RO         1194    .text               diskio.o
    0x0800c3ec   0x00003296   Code   RO         1221    .text               ff.o
    0x0800f682   0x00000002   PAD
    0x0800f684   0x000000cc   Code   RO         1245    .text               cc936.o
    0x0800f750   0x0000035c   Code   RO         1271    .text               common.o
    0x0800faac   0x00000694   Code   RO         1299    .text               HanZiUse.lib(updatefont.o)
    0x08010140   0x00000284   Code   RO         1465    .text               HanZiUse.lib(showhz.o)
    0x080103c4   0x00000062   Code   RO         1493    .text               mc_w.l(uldiv.o)
    0x08010426   0x0000000e   Code   RO         1495    .text               mc_w.l(strlen.o)
    0x08010434   0x0000001c   Code   RO         1497    .text               mc_w.l(strcmp.o)
    0x08010450   0x00000012   Code   RO         1499    .text               mc_w.l(strcpy.o)
    0x08010462   0x0000001e   Code   RO         1515    .text               mc_w.l(llshl.o)
    0x08010480   0x00000020   Code   RO         1517    .text               mc_w.l(llushr.o)
    0x080104a0   0x00000024   Code   RO         1519    .text               mc_w.l(init.o)
    0x080104c4   0x00000056   Code   RO         1529    .text               mc_w.l(__dczerorl2.o)
    0x0801051a   0x0000000e   Code   RO         1523    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08010528   0x00000002   Code   RO         1524    i.__scatterload_null  mc_w.l(handlers.o)
    0x0801052a   0x0000000e   Code   RO         1525    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08010538   0x00000018   Data   RO          319    .constdata          system_stm32f4xx.o
    0x08010550   0x00001c34   Data   RO          436    .constdata          lcd.o
    0x08012184   0x00000018   Data   RO          517    .constdata          malloc.o
    0x0801219c   0x0000000c   Data   RO          644    .constdata          rtc.o
    0x080121a8   0x00000322   Data   RO          675    .constdata          lunar_calendar.o
    0x080124ca   0x00000008   Data   RO          906    .constdata          stm32f4xx_hal_dma.o
    0x080124d2   0x0000003a   Data   RO         1222    .constdata          ff.o
    0x0801250c   0x000003c0   Data   RO         1246    .constdata          cc936.o
    0x080128cc   0x00000010   Data   RO         1301    .constdata          HanZiUse.lib(updatefont.o)
    0x080128dc   0x00000047   Data   RO            5    .conststring        main.o
    0x08012923   0x00000001   PAD
    0x08012924   0x00000137   Data   RO          676    .conststring        lunar_calendar.o
    0x08012a5b   0x00000001   PAD
    0x08012a5c   0x0000007f   Data   RO         1302    .conststring        HanZiUse.lib(updatefont.o)
    0x08012adb   0x00000001   PAD
    0x08012adc   0x00000020   Data   RO         1521    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Base: 0x20000000, Size: 0x00016e88, Max: 0x00020000, ABSOLUTE, COMPRESSED[0x000000f8])

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x00000034   Data   RW            6    .data               main.o
    0x20000034   0x00000004   Data   RW          320    .data               system_stm32f4xx.o
    0x20000038   0x00000006   Data   RW          405    .data               key.o
    0x2000003e   0x00000012   Data   RW          437    .data               lcd.o
    0x20000050   0x00000002   Data   RW          471    .data               usart1.o
    0x20000052   0x00000002   PAD
    0x20000054   0x00000012   Data   RW          518    .data               malloc.o
    0x20000066   0x00000002   Data   RW          576    .data               w25qxx.o
    0x20000068   0x00000004   Data   RW          645    .data               rtc.o
    0x2000006c   0x000000f8   Data   RW          677    .data               lunar_calendar.o
    0x20000164   0x00000009   Data   RW          707    .data               stm32f4xx_hal.o
    0x2000016d   0x00000001   PAD
    0x2000016e   0x00000002   Data   RW         1195    .data               diskio.o
    0x20000170   0x0000000a   Data   RW         1223    .data               ff.o
    0x2000017a   0x00000001   Data   RW         1272    .data               common.o
    0x2000017b   0x00000001   PAD
    0x2000017c   0x00000050   Zero   RW          435    .bss                lcd.o
    0x200001cc   0x000000a4   Zero   RW          470    .bss                usart1.o
    0x20000270   0x00015400   Zero   RW          516    .bss                malloc.o
    0x20015670   0x00000058   Zero   RW          545    .bss                spi.o
    0x200156c8   0x00001000   Zero   RW          575    .bss                w25qxx.o
    0x200166c8   0x00000364   Zero   RW          610    .bss                tfcard_sdio.o
    0x20016a2c   0x00000034   Zero   RW          643    .bss                rtc.o
    0x20016a60   0x00000021   Zero   RW         1300    .bss                HanZiUse.lib(updatefont.o)
    0x20016a81   0x00000007   PAD
    0x20016a88   0x00000400   Zero   RW          370    STACK               startup_stm32f407xx.o



  Load Region LR$$.ARM.__AT_0x68000000 (Base: 0x68000000, Size: 0x00000000, Max: 0x000c8000, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x68000000 (Base: 0x68000000, Size: 0x000c8000, Max: 0x000c8000, ABSOLUTE, UNINIT)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x68000000   0x000c8000   Zero   RW          514    .ARM.__AT_0x68000000  malloc.o



  Load Region LR$$.ARM.__AT_0x680C8000 (Base: 0x680c8000, Size: 0x00000000, Max: 0x0000c800, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x680C8000 (Base: 0x680c8000, Size: 0x0000c800, Max: 0x0000c800, ABSOLUTE, UNINIT)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x680c8000   0x0000c800   Zero   RW          515    .ARM.__AT_0x680C8000  malloc.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       204         12        960          0          0       1821   cc936.o
       876         48          0          1          0       3861   common.o
       468         10          0          2          0       4834   diskio.o
     12950        138         58         10          0      38586   ff.o
       292         34          0          6          0       1819   key.o
      3440        154       7220         18         80      11561   lcd.o
        92          8          0          0          0       1015   led.o
      1668        102       1113        248          0       5794   lunar_calendar.o
      1356        300         71         52          0     744686   main.o
       540         26         24         18     957440       4414   malloc.o
       608         32         12          4         52       4311   rtc.o
       296         18          0          0         88       2052   spi.o
        36          8        392          0       1024        900   startup_stm32f407xx.o
       500         42          0          9          0       6168   stm32f4xx_hal.o
       976         20          0          0          0      36410   stm32f4xx_hal_cortex.o
      2236         26          8          0          0       7293   stm32f4xx_hal_dma.o
       948         44          0          0          0       3775   stm32f4xx_hal_gpio.o
         4          0          0          0          0        660   stm32f4xx_hal_msp.o
       476         22          0          0          0       3747   stm32f4xx_hal_pwr.o
      2284         66          0          0          0       5459   stm32f4xx_hal_rcc.o
      1140         54          0          0          0       3278   stm32f4xx_hal_rcc_ex.o
      2936         26          0          0          0       8429   stm32f4xx_hal_rtc.o
      3570         22          0          0          0      13141   stm32f4xx_hal_rtc_ex.o
      6412         44          0          0          0      17753   stm32f4xx_hal_sd.o
      7524        134          0          0          0      20385   stm32f4xx_hal_spi.o
       896          8          0          0          0       6556   stm32f4xx_hal_sram.o
      5180         48          0          0          0      16452   stm32f4xx_hal_uart.o
        32          0          0          0          0       1462   stm32f4xx_it.o
       904         18          0          0          0       6672   stm32f4xx_ll_fsmc.o
      2568         20          0          0          0      14977   stm32f4xx_ll_sdmmc.o
       288         32         24          4          0       1857   system_stm32f4xx.o
       468         30          0          0        868       3374   tfcard_sdio.o
       496         58          0          2        164       3027   usart1.o
      1090         26          0          2       4096       4969   w25qxx.o

    ----------------------------------------------------------------------
     63760       <USER>       <GROUP>        380     963812    1011498   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         6          0          2          4          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       644         16          0          0          0       2785   showhz.o
      1684        312        143          0         33      11884   updatefont.o
        86          0          0          0          0          0   __dczerorl2.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        30          0          0          0          0         68   llshl.o
        32          0          0          0          0         68   llushr.o
        28          0          0          0          0         76   strcmp.o
        18          0          0          0          0         68   strcpy.o
        14          0          0          0          0         68   strlen.o
        98          0          0          0          0         92   uldiv.o

    ----------------------------------------------------------------------
      2720        <USER>        <GROUP>          0         40      15177   Library Totals
         0          0          1          0          7          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2328        328        143          0         33      14669   HanZiUse.lib
       392         16          0          0          0        508   mc_w.l

    ----------------------------------------------------------------------
      2720        <USER>        <GROUP>          0         40      15177   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     66480       1974      10060        380     963852    1018103   Grand Totals
     66480       1974      10060        248     963852    1018103   ELF Image Totals (compressed)
     66480       1974      10060        248          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                76540 (  74.75kB)
    Total RW  Size (RW Data + ZI Data)            964232 ( 941.63kB)
    Total ROM Size (Code + RO Data + RW Data)      76788 (  74.99kB)

==============================================================================

