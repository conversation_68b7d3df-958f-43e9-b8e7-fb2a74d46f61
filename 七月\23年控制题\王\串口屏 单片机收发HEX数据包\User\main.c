/* ================================================================================================
 * 串口屏+总线舵机控制系统
 * 功能：通过串口屏控制LED和总线舵机
 * 硬件：STM32F103C8T6 + 串口屏 + HTS-25L总线舵机
 * 作者：STM32开发团队
 * 版本：V2.0
 * 日期：2024-07-18
 *
 * 功能说明：
 * - 串口屏发送0x01：PA1输出高电平，LED亮
 * - 串口屏发送0x02：PA1输出低电平，LED灭
 * - 串口屏发送0x03：两个舵机复位到固定角度
 * - OLED显示两个舵机的实时角度
 *
 * 硬件连接：
 * - USART1 (串口屏):  PA9-TX, PA10-RX
 * - USART2 (总线舵机): PA2-TX, PA3-RX
 * - LED控制: PA1
 * - OLED: PB6-SCL, PB7-SDA
 * - 波特率：115200 bps
 * ================================================================================================ */

#include "stm32f10x.h"
#include "Delay.h"
#include "OLED.h"
#include "Serial.h"
#include "LED.h"
#include "Servo.h"
#include "Key.h"

/* 全局变量定义 */
static float servo1_angle = 0.0f;       // 舵机1当前角度
static uint32_t last_read_time = 0;     // 上次读取时间

/* 函数声明 */
void System_Init(void);
void Read_And_Display_Angle(void);
void Display_Angle(float angle, uint8_t is_error);

/**
  * 函    数：主函数
  * 参    数：无
  * 返 回 值：无
  * 说    明：系统主循环，处理串口屏命令并控制LED
  */
int main(void)
{
    // 系统初始化
    System_Init();

    // 显示启动信息
    OLED_ShowString(1, 1, "Servo Test");
    OLED_ShowString(2, 1, "Reading...");

    // 主循环
    while (1)
    {
        // 5Hz频率读取并显示角度
        Read_And_Display_Angle();

        Delay_ms(10);  // 主循环延时
    }
}
/* ================================================================================================
 * 功能函数实现
 * ================================================================================================ */

/**
  * 函    数：系统初始化
  * 参    数：无
  * 返 回 值：无
  * 说    明：初始化所有硬件模块
  */
void System_Init(void)
{
    OLED_Init();        // OLED显示器初始化
    Serial_Init();      // 双串口模块初始化 (串口屏+舵机)
    Servo_Init();       // 舵机模块初始化

    Delay_ms(500);      // 等待系统稳定
}

/**
  * 函    数：5Hz频率读取并显示角度
  * 参    数：无
  * 返 回 值：无
  * 说    明：每200ms读取一次舵机角度并显示
  */
void Read_And_Display_Angle(void)
{
    static uint32_t counter = 0;

    // 简单计数方式实现200ms间隔 (主循环10ms * 20 = 200ms)
    counter++;
    if (counter >= 20) {
        counter = 0;

        float angle;
        ServoStatus_t status = Servo_ReadRealTimeAngle(SERVO_ID1, &angle);

        if (status == SERVO_STATUS_OK) {
            // 读取成功，显示角度
            Display_Angle(angle, 0);
        } else {
            // 读取失败，显示错误
            Display_Angle(0.0f, 1);
        }
    }
}

/**
  * 函    数：显示角度信息
  * 参    数：angle 角度值
  *          is_error 是否为错误状态 (1-错误, 0-正常)
  * 返 回 值：无
  * 说    明：在OLED上显示角度或错误信息
  */
void Display_Angle(float angle, uint8_t is_error)
{
    OLED_ShowString(1, 1, "Servo Test");

    if (is_error) {
        OLED_ShowString(3, 1, "ID1: error    ");
    } else {
        char angle_str[16];
        // 格式化角度显示，保留1位小数
        int integer_part = (int)angle;
        int decimal_part = (int)((angle - integer_part) * 10);
        sprintf(angle_str, "ID1: %d.%d deg   ", integer_part, decimal_part);
        OLED_ShowString(3, 1, angle_str);
    }
}

/* ================================================================================================
 * 以下功能已注释掉，仅用于通信测试
 * ================================================================================================ */

/*
// 原有的所有功能函数都已注释掉
// 包括：Process_SerialCommand, Process_KeyCommand, Update_Display等
// 本次实验仅测试STM32与舵机的串口通信功能
*/

/**
  * 函    数：处理串口屏命令
  * 参    数：无
  * 返 回 值：无
  * 说    明：处理来自串口屏的控制命令：LED控制和舵机复位
  */
void Process_SerialCommand(void)
{
    static uint32_t rx_count = 0;  // 接收计数器

    if (Serial1_GetRxFlag() == 1)    // 如果接收到串口屏数据包
    {
        rx_count++;  // 增加接收计数

        // 获取命令
        uint8_t command = Serial1_RxPacket[0];  // 获取第一个字节作为命令

        // 处理命令
        switch (command) {
            case CMD_LED_ON:  // 0x01 - 亮灯命令
                GPIO_SetBits(GPIOA, GPIO_Pin_1);    // PA1输出高电平
                LED1_ON();                          // 调用LED函数
                led_status = 1;                     // 更新LED状态
                OLED_ShowString(2, 1, "01:LED ON  02:LED 03:RST 04:TOP 05:TRACK");
                OLED_ShowString(4, 1, "LED:ON  Ready");
                break;

            case CMD_LED_OFF: // 0x02 - 灭灯命令
                GPIO_ResetBits(GPIOA, GPIO_Pin_1);  // PA1输出低电平
                LED1_OFF();                         // 调用LED函数
                led_status = 0;                     // 更新LED状态
                OLED_ShowString(2, 1, "01:LED 02:LED OFF 03:RST 04:TOP 05:TRACK");
                OLED_ShowString(4, 1, "LED:OFF Ready");
                break;

            case CMD_SERVO_RESET: // 0x03 - 舵机复位命令
                OLED_ShowString(2, 1, "01:LED 02:LED 03:RESET 04:TOP 05:TRACK");
                OLED_ShowString(4, 1, "Screen Reset...");

                // 如果不在轨迹运动中，直接执行复位
                // 如果在轨迹运动中，复位会在Check_ResetCommand()中处理
                if (!Get_TrackRunningFlag()) {
                    Servo_ResetWithDebug();
                }
                break;

            case CMD_SERVO_TOP: // 0x04 - 舵机移动到顶点命令
                OLED_ShowString(2, 1, "01:LED 02:LED 03:RST 04:TOP 05:TRACK");
                OLED_ShowString(4, 1, "Screen Move Top...");

                // 执行舵机移动到顶点 (带调试)
                Servo_MoveToTopWithDebug();
                break;

            case CMD_SERVO_TRACK: // 0x05 - 舵机沿轨迹运动命令
                OLED_ShowString(2, 1, "01:LED 02:LED 03:RST 04:TOP 05:TRACK");
                OLED_ShowString(4, 1, "Screen Track Run...");

                // 清除轨迹中断标志，设置轨迹运行标志
                Clear_TrackInterruptFlag();
                Set_TrackRunningFlag();

                // 执行舵机轨迹运动 (带调试)
                Servo_RunTrackWithDebug();

                // 轨迹运动结束，清除运行标志
                Clear_TrackRunningFlag();
                break;

            default: // 未知命令
                OLED_ShowString(2, 1, "Unknown CMD:");
                OLED_ShowHexNum(2, 13, command, 2);
                OLED_ShowString(4, 1, "Check Command!");
                Delay_ms(1000);
                OLED_ShowString(2, 1, "01:LED 02:LED 03:RST 04:TOP 05:TRACK");
                OLED_ShowString(4, 1, "LED:OFF Ready");
                break;
        }
    }
}

/**
  * 函    数：更新显示
  * 参    数：无
  * 返 回 值：无
  * 说    明：更新OLED显示内容，包括舵机角度
  */
void Update_Display(void)
{
    static uint32_t display_counter = 0;

    display_counter++;

    // 每20次循环更新一次舵机角度显示 (约1秒)
    if (display_counter % 20 == 0) {
        Display_ServoAngles();
    }
}

/**
  * 函    数：显示舵机角度
  * 参    数：无
  * 返 回 值：无
  * 说    明：在OLED第3行显示两个舵机的角度
  */
void Display_ServoAngles(void)
{
    // 获取当前舵机角度
    Servo_GetBothAngles(&servo1_angle, &servo2_angle);

    // 格式化显示：S1:090.0 S2:120.0
    char display_str[21];

    // 转换为整数和小数部分
    uint16_t s1_int = (uint16_t)servo1_angle;
    uint16_t s1_dec = (uint16_t)((servo1_angle - s1_int) * 10);
    uint16_t s2_int = (uint16_t)servo2_angle;
    uint16_t s2_dec = (uint16_t)((servo2_angle - s2_int) * 10);

    // 构建显示字符串
    display_str[0] = 'S';
    display_str[1] = '1';
    display_str[2] = ':';
    display_str[3] = '0' + (s1_int / 100);
    display_str[4] = '0' + ((s1_int % 100) / 10);
    display_str[5] = '0' + (s1_int % 10);
    display_str[6] = '.';
    display_str[7] = '0' + s1_dec;
    display_str[8] = ' ';
    display_str[9] = 'S';
    display_str[10] = '2';
    display_str[11] = ':';
    display_str[12] = '0' + (s2_int / 100);
    display_str[13] = '0' + ((s2_int % 100) / 10);
    display_str[14] = '0' + (s2_int % 10);
    display_str[15] = '.';
    display_str[16] = '0' + s2_dec;
    display_str[17] = '\0';

    // 显示在第3行
    OLED_ShowString(3, 1, display_str);
}



/**
  * 函    数：处理按键命令
  * 参    数：无
  * 返 回 值：无
  * 说    明：处理PB1按键，实现舵机复位功能
  */
void Process_KeyCommand(void)
{
    uint8_t key = Key_GetNum();

    if (key == KEY_PB1) {  // PB1按键按下 - 舵机复位
        OLED_ShowString(2, 1, "01:LED 02:LED 03:RESET");
        OLED_ShowString(4, 1, "Key Reset...");

        // 执行舵机复位 (带调试)
        Servo_ResetWithDebug();
    }
}

/**
  * 函    数：舵机复位 (带调试信息)
  * 参    数：无
  * 返 回 值：无
  * 说    明：执行舵机复位到固定角度，并显示详细调试信息
  */
void Servo_ResetWithDebug(void)
{
    // 显示开始复位
    OLED_ShowString(4, 1, "Resetting...");
    Delay_ms(500);

    // 发送舵机1复位命令
    OLED_ShowString(4, 1, "S1 -> 127.0");
    Servo_SetPositionWithTime(SERVO_ID1, SERVO_RESET_ANGLE1, 1000);
    Delay_ms(100);  // 命令间隔

    // 发送舵机2复位命令
    OLED_ShowString(4, 1, "S2 -> 113.0");
    Servo_SetPositionWithTime(SERVO_ID2, SERVO_RESET_ANGLE2, 1000);
    Delay_ms(100);  // 命令间隔

    // 更新角度记录
    servo1_angle = SERVO_RESET_ANGLE1;  // 127.0度
    servo2_angle = SERVO_RESET_ANGLE2;  // 113.0度

    // 显示完成状态
    OLED_ShowString(4, 1, "Reset Complete!");
    Delay_ms(1500);  // 显示1.5秒

    // 恢复就绪状态
    if (led_status == 1) {
        OLED_ShowString(4, 1, "LED:ON  Ready");
    } else {
        OLED_ShowString(4, 1, "LED:OFF Ready");
    }
}

/**
  * 函    数：舵机移动到顶点 (带调试信息)
  * 参    数：无
  * 返 回 值：无
  * 说    明：执行舵机移动到顶点角度，并显示详细调试信息
  */
void Servo_MoveToTopWithDebug(void)
{
    // 显示开始移动
    OLED_ShowString(4, 1, "Moving to Top...");
    Delay_ms(500);

    // 发送舵机1移动命令
    OLED_ShowString(4, 1, "S1 -> 146.0");
    Servo_SetPositionWithTime(SERVO_ID1, SERVO_TOP_ANGLE1, 1000);
    Delay_ms(100);  // 命令间隔

    // 发送舵机2移动命令
    OLED_ShowString(4, 1, "S2 -> 130.0");
    Servo_SetPositionWithTime(SERVO_ID2, SERVO_TOP_ANGLE2, 1000);
    Delay_ms(100);  // 命令间隔

    // 更新角度记录
    servo1_angle = SERVO_TOP_ANGLE1;  // 146.0度
    servo2_angle = SERVO_TOP_ANGLE2;  // 130.0度

    // 显示完成状态
    OLED_ShowString(4, 1, "Top Complete!");
    Delay_ms(1500);  // 显示1.5秒

    // 恢复就绪状态
    if (led_status == 1) {
        OLED_ShowString(4, 1, "LED:ON  Ready");
    } else {
        OLED_ShowString(4, 1, "LED:OFF Ready");
    }
}

/**
  * 函    数：舵机轨迹运动 (带调试信息)
  * 参    数：无
  * 返 回 值：无
  * 说    明：执行舵机沿正方形轨迹运动，并显示详细调试信息
  */
void Servo_RunTrackWithDebug(void)
{
    // 显示开始轨迹运动
    OLED_ShowString(4, 1, "Track Starting...");
    Delay_ms(500);

    // 显示轨迹信息
    OLED_ShowString(4, 1, "Square Track");
    Delay_ms(1000);

    // 显示第一段
    OLED_ShowString(4, 1, "Seg1: 146->113");
    Delay_ms(500);

    // 执行轨迹运动
    Servo_RunTrack();

    // 检查是否被中断
    if (Get_TrackInterruptFlag()) {
        // 轨迹运动被中断
        OLED_ShowString(4, 1, "Track Interrupted!");
        Delay_ms(1000);

        // 清除中断标志
        Clear_TrackInterruptFlag();

        // 复位操作已经在Check_ResetCommand()中触发，这里不需要重复执行
    } else {
        // 轨迹运动正常完成
        OLED_ShowString(4, 1, "Track Complete!");
        Delay_ms(2000);  // 显示2秒

        // 恢复就绪状态
        if (led_status == 1) {
            OLED_ShowString(4, 1, "LED:ON  Ready");
        } else {
            OLED_ShowString(4, 1, "LED:OFF Ready");
        }
    }
}

/* ================================================================================================
 * 轨迹中断标志管理函数
 * ================================================================================================ */

/**
  * 函    数：设置轨迹中断标志
  * 参    数：无
  * 返 回 值：无
  * 说    明：设置轨迹运动中断标志，用于中断正在进行的轨迹运动
  */
void Set_TrackInterruptFlag(void)
{
    track_interrupt_flag = 1;
}

/**
  * 函    数：清除轨迹中断标志
  * 参    数：无
  * 返 回 值：无
  * 说    明：清除轨迹运动中断标志，用于开始新的轨迹运动
  */
void Clear_TrackInterruptFlag(void)
{
    track_interrupt_flag = 0;
}

/**
  * 函    数：获取轨迹中断标志
  * 参    数：无
  * 返 回 值：中断标志状态：0-无中断，1-有中断
  * 说    明：获取轨迹运动中断标志状态
  */
uint8_t Get_TrackInterruptFlag(void)
{
    return track_interrupt_flag;
}

/**
  * 函    数：设置轨迹运行标志
  * 参    数：无
  * 返 回 值：无
  * 说    明：设置轨迹运动进行标志，表示当前正在进行轨迹运动
  */
void Set_TrackRunningFlag(void)
{
    track_running_flag = 1;
}

/**
  * 函    数：清除轨迹运行标志
  * 参    数：无
  * 返 回 值：无
  * 说    明：清除轨迹运动进行标志，表示轨迹运动已结束
  */
void Clear_TrackRunningFlag(void)
{
    track_running_flag = 0;
}

/**
  * 函    数：获取轨迹运行标志
  * 参    数：无
  * 返 回 值：运行标志状态：0-未运行，1-正在运行
  * 说    明：获取轨迹运动进行标志状态
  */
uint8_t Get_TrackRunningFlag(void)
{
    return track_running_flag;
}

/**
  * 函    数：检查复位命令
  * 参    数：无
  * 返 回 值：1-收到复位命令，0-未收到
  * 说    明：在轨迹运动过程中主动检查是否收到复位命令(0x03)
  */
uint8_t Check_ResetCommand(void)
{
    if (Serial1_GetRxFlag() == 1)    // 如果接收到串口屏数据包
    {
        uint8_t command = Serial1_RxPacket[0];  // 获取第一个字节作为命令

        if (command == CMD_SERVO_RESET) {  // 如果是复位命令
            // 设置中断标志
            Set_TrackInterruptFlag();

            // 显示中断信息
            OLED_ShowString(2, 1, "01:LED 02:LED 03:RESET 04:TOP 05:TRACK");
            OLED_ShowString(4, 1, "Track Interrupted!");
            Delay_ms(500);

            // 立即执行复位操作
            OLED_ShowString(4, 1, "Executing Reset...");
            Servo_ResetWithDebug();

            return 1;  // 返回收到复位命令
        }
        // 如果是其他命令，暂时忽略（轨迹运动期间只响应复位命令）
    }

    return 0;  // 未收到复位命令
}
