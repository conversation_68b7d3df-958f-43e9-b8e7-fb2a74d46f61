#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "OLED.h"
#include "Servo.h"
#include "LED.h"
#include "Key.h"
#include <stdio.h>

// 简化的舵机测试程序 - 按键控制舵机旋转
// PB0控制舵机1，PB1控制舵机2，每按一次旋转30度

// 舵机当前角度记录
float servo1_angle = 120.0f;  // 舵机1当前角度，初始化为中位120度
float servo2_angle = 120.0f;  // 舵机2当前角度，初始化为中位120度

// 角度增量
#define ANGLE_STEP 30.0f      // 每次按键旋转30度

int main(void)
{
	/*模块初始化*/
	OLED_Init();		//OLED初始化
	LED_Init();			//LED初始化
	Servo_Init();		//总线舵机初始化
	Key_Init();			//按键初始化

	/*显示启动信息*/
	OLED_ShowString(1, 1, "Servo Test");
	OLED_ShowString(2, 1, "PB0:Servo1");
	OLED_ShowString(3, 1, "PB1:Servo2");
	OLED_ShowString(4, 1, "Ready...");

	// 延时等待系统稳定
	Delay_ms(1000);

	// 初始化舵机到中位
	Servo_SetPosition(SERVO_PAN_ID, servo1_angle);   // 舵机1到中位
	Delay_ms(500);
	Servo_SetPosition(SERVO_TILT_ID, servo2_angle);  // 舵机2到中位
	Delay_ms(500);

	LED_OFF();

	// 简化的主循环 - 按键控制舵机测试
	while (1)
	{
		// 扫描按键状态
		Key_Scan();

		// 检查PB0按键 - 控制舵机1
		if (Key_IsClicked(KEY_RECORD)) {
			// 舵机1顺时针旋转30度
			servo1_angle += ANGLE_STEP;

			// 角度限制 (0-240度)
			if (servo1_angle > SERVO_MAX_ANGLE) {
				servo1_angle = 0.0f;  // 超过最大角度时回到0度
			}

			// 控制舵机1移动
			Servo_SetPosition(SERVO_PAN_ID, servo1_angle);

			// LED指示
			LED_ON();
			Delay_ms(100);
			LED_OFF();

			// 清除按键事件
			Key_ClearEvent(KEY_RECORD);

			// 显示当前角度
			char angle_str[16];
			sprintf(angle_str, "S1:%.0f", servo1_angle);
			OLED_ShowString(1, 1, angle_str);
		}

		// 检查PB1按键 - 控制舵机2
		if (Key_IsClicked(KEY_TRIGGER)) {
			// 舵机2逆时针旋转30度
			servo2_angle -= ANGLE_STEP;

			// 角度限制 (0-240度)
			if (servo2_angle < 0.0f) {
				servo2_angle = SERVO_MAX_ANGLE;  // 小于0度时回到最大角度
			}

			// 控制舵机2移动
			Servo_SetPosition(SERVO_TILT_ID, servo2_angle);

			// LED指示
			LED_ON();
			Delay_ms(100);
			LED_OFF();

			// 清除按键事件
			Key_ClearEvent(KEY_TRIGGER);

			// 显示当前角度
			char angle_str[16];
			sprintf(angle_str, "S2:%.0f", servo2_angle);
			OLED_ShowString(2, 1, angle_str);
		}

		// 显示操作提示
		OLED_ShowString(3, 1, "PB0:S1+30");
		OLED_ShowString(4, 1, "PB1:S2-30");

		Delay_ms(10);  // 主循环延时
	}
}
