# 串口屏+总线舵机控制系统

## 📋 **系统概述**

本系统基于STM32F103C8T6微控制器，通过串口屏控制LED灯和HTS-25L总线舵机，实现完整的人机交互控制系统。

### **主要功能**
- ✅ 串口屏发送0x01：PA1输出高电平，LED亮
- ✅ 串口屏发送0x02：PA1输出低电平，LED灭
- ✅ 串口屏发送0x03：两个舵机复位到固定角度
- ✅ OLED实时显示两个舵机的角度
- ✅ 双串口通信：串口屏+总线舵机

## 🔌 **硬件连接**

### **STM32F103C8T6引脚分配**
```
串口屏通信 (USART1):
PA9  (TX)  → 串口屏RXD
PA10 (RX)  → 串口屏TXD

总线舵机通信 (USART2):
PA2  (TX)  → 舵机驱动板RXD
PA3  (RX)  → 舵机驱动板TXD

LED控制:
PA1        → LED正极 (通过限流电阻)

按键输入:
PB1        → 舵机复位按键 (上拉输入)

OLED显示:
PB6        → OLED_SCL
PB7        → OLED_SDA

电源:
GND        → 公共地
5V         → 电源正极
```

### **舵机连接**
```
舵机驱动板 → HTS-25L舵机
信号线     → 黄线 (信号)
VCC        → 红线 (电源+)
GND        → 黑线 (电源-)

舵机ID设置:
舵机1: ID = 1
舵机2: ID = 2
```

## 📊 **OLED显示界面**

### **启动界面**
```
Screen+Servo Sys
01:LED 02:LED 03:RST
S1:090.0 S2:120.0
LED:OFF Ready
```

### **运行界面示例**
```
Screen+Servo Sys
01:LED ON  02:LED 03:RST    ← 命令状态
S1:090.0 S2:120.0           ← 舵机角度
LED:ON  Ready               ← LED状态
```

**显示格式说明**：
- 第1行：系统标题
- 第2行：当前命令状态
- 第3行：S1(舵机1角度) S2(舵机2角度)
- 第4行：LED状态和系统状态

## 🎮 **操作说明**

### **串口屏按键配置**

#### **按键1 - LED亮灯**
- **发送命令**：0x01 (HEX)
- **系统响应**：PA1输出高电平，LED亮
- **OLED显示**：`LED:ON Ready`

#### **按键2 - LED灭灯**
- **发送命令**：0x02 (HEX)
- **系统响应**：PA1输出低电平，LED灭
- **OLED显示**：`LED:OFF Ready`

#### **按键3 - 舵机复位 (串口屏)**
- **发送命令**：0x03 (HEX)
- **系统响应**：
  - 舵机1转到90.0度
  - 舵机2转到120.0度
  - OLED显示复位过程
- **OLED显示**：`Screen Reset...` → `S1 -> 90.0` → `S2 -> 120.0` → `Reset Complete!`

#### **PB1按键 - 舵机复位 (硬件按键)**
- **触发方式**：按下STM32的PB1引脚按键
- **系统响应**：
  - 舵机1转到90.0度
  - 舵机2转到120.0度
  - OLED显示复位过程
- **OLED显示**：`Key Reset...` → `S1 -> 90.0` → `S2 -> 120.0` → `Reset Complete!`

### **固定角度设置**
```c
// 在Servo.h中定义的复位角度
#define SERVO_RESET_ANGLE1    90.0f   // 舵机1复位角度
#define SERVO_RESET_ANGLE2    120.0f  // 舵机2复位角度
```

## ⚙️ **技术参数**

### **通信参数**
- **波特率**：115200 bps (双串口)
- **数据格式**：8N1
- **协议**：
  - 串口屏：单字节命令
  - 舵机：HTS-25L协议 (0x55帧头)

### **舵机参数**
- **型号**：HTS-25L总线舵机
- **角度范围**：0° ~ 240°
- **位置精度**：0.24° (1000步/240°)
- **运动时间**：1000ms (可调)
- **支持ID**：1, 2

### **系统性能**
- **命令响应时间**：<50ms
- **角度显示更新**：每1秒
- **LED控制延时**：<10ms

## 🔧 **代码结构**

### **模块化设计**
```
Hardware/
├── Serial.h/c     # 双串口通信模块 (USART1+USART2)
├── Servo.h/c      # 总线舵机控制模块
├── LED.h/c        # LED控制模块
├── OLED.h/c       # OLED显示模块
└── Key.h/c        # 按键模块 (保留)

User/
├── main.c         # 主程序
├── stm32f10x_it.c # 中断处理
└── stm32f10x_conf.h # 配置文件

System/
└── Delay.h/c      # 延时函数
```

### **主要函数**

#### **双串口通信**
```c
void Serial1_Init(void);              // 串口屏通信初始化
void Serial2_Init(void);              // 舵机通信初始化
uint8_t Serial1_GetRxFlag(void);      // 获取串口屏接收标志
void Serial2_SendArray(uint8_t *Array, uint16_t Length); // 发送到舵机
```

#### **舵机控制**
```c
void Servo_SetPositionWithTime(uint8_t id, float angle, uint16_t time_ms);
void Servo_ResetToOrigin(void);       // 复位到固定角度
void Servo_GetBothAngles(float* angle1, float* angle2); // 获取角度
```

#### **LED控制**
```c
void LED1_ON(void);                   // LED亮
void LED1_OFF(void);                  // LED灭
```

## 🚀 **使用流程**

### **1. 系统启动**
1. 连接所有硬件并上电
2. 观察LED闪烁3次 (硬件测试)
3. 确认OLED显示启动信息
4. 系统进入就绪状态

### **2. LED控制测试**
1. **亮灯测试**：按串口屏按键1 → 观察LED亮起
2. **灭灯测试**：按串口屏按键2 → 观察LED熄灭
3. **状态确认**：观察OLED显示LED状态

### **3. 舵机控制测试**
1. **复位测试**：按串口屏按键3
2. **观察动作**：
   - 舵机1转到90度
   - 舵机2转到120度
   - OLED显示复位过程
3. **角度确认**：观察OLED显示的角度值

### **4. 系统监控**
- OLED第3行实时显示舵机角度
- OLED第4行显示LED和系统状态
- 所有操作都有OLED反馈

## ⚠️ **注意事项**

### **硬件注意事项**
- 确保双串口连接正确 (TX↔RX)
- 舵机ID必须正确设置为1和2
- 电源供电能力要足够 (建议5V/3A以上)
- LED需要串联限流电阻

### **软件注意事项**
- 波特率必须设置为115200
- 串口屏发送格式要正确 (单字节HEX)
- 避免频繁发送命令 (建议间隔>100ms)

### **故障排除**
1. **LED不响应**：检查PA1连接、串口屏通信
2. **舵机不动**：检查PA2/PA3连接、舵机ID、电源
3. **OLED显示异常**：检查PB6/PB7连接、I2C通信
4. **角度显示错误**：检查舵机通信、协议格式

## 📈 **扩展功能**

### **可扩展特性**
- 支持更多舵机ID (修改代码即可)
- 添加舵机角度实时读取功能
- 实现复杂的运动轨迹控制
- 添加参数存储到Flash
- 支持更多LED控制模式

### **性能优化**
- 使用DMA提高串口效率
- 添加舵机状态反馈
- 实现错误自动恢复
- 优化显示刷新算法

---

**版本信息**：V2.0  
**更新日期**：2024-07-18  
**适用硬件**：STM32F103C8T6 + 串口屏 + HTS-25L舵机  
**技术支持**：STM32开发团队
