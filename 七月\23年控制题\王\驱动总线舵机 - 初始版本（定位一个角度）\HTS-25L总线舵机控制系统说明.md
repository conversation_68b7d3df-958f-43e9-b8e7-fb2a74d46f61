# HTS-25L总线舵机控制系统

## 🎯 **项目概述**

本项目使用STM32F103C8T6芯片通过串口控制两个HTS-25L总线舵机，实现精确的角度控制。

### **舵机参数**
- **型号**: HTS-25L 总线舵机
- **位置范围**: 0~1000 (对应 0°~240°)
- **中位位置**: 500 (对应 120°)
- **通信方式**: 串口总线通信
- **控制精度**: 0.24°/步

## 🔧 **硬件连接**

### **STM32F103C8T6引脚连接**

#### **串口连接 (USART1)**
```
STM32F103C8T6    →    HTS-25L舵机总线
PA9 (USART1_TX)  →    舵机信号线 (黄色/白色)
PA10 (USART1_RX) →    舵机信号线 (黄色/白色) [可选，用于反馈]
GND              →    舵机GND (黑色/棕色)
5V               →    舵机VCC (红色)
```

#### **OLED显示屏连接**
```
STM32F103C8T6    →    OLED显示屏
PB8 (I2C1_SCL)   →    SCL
PB9 (I2C1_SDA)   →    SDA
GND              →    GND
3.3V             →    VCC
```

#### **LED指示灯连接**
```
STM32F103C8T6    →    LED
PC13             →    LED正极 (通过限流电阻)
GND              →    LED负极
```

### **舵机总线连接**
```
舵机ID1 ←→ 舵机ID2 ←→ STM32串口
   ↑           ↑
 设置ID=1   设置ID=2
```

**注意**: 
- 多个舵机可以并联在同一条总线上
- 每个舵机需要设置不同的ID
- 总线末端建议添加120Ω终端电阻

## ⚙️ **软件架构**

### **模块结构**
```
├── Hardware/
│   ├── Servo.c/h     - 总线舵机控制模块 ⭐核心⭐
│   ├── usart.c/h     - 串口通信模块
│   ├── OLED.c/h      - OLED显示模块
│   ├── LED.c/h       - LED指示模块
│   └── Key.c/h       - 按键模块 (可选)
├── System/
│   └── Delay.c/h     - 延时模块
└── User/
    └── main.c        - 主程序
```

### **核心功能**
1. **Servo_Init()**: 初始化串口通信
2. **Servo_SetPosition()**: 设置舵机到指定角度
3. **Servo_AngleToPosition()**: 角度转位置值
4. **Servo_SendCommand()**: 发送控制命令

## 📊 **控制协议**

### **HTS-25L命令格式**
```
| 字节 | 名称 | 数值 | 说明 |
|------|------|------|------|
| 0 | Header | 0xFF | 数据头 |
| 1 | ID | 1或2 | 舵机ID |
| 2 | Instruction | 0x03 | 设置位置指令 |
| 3 | Data[0] | 高字节 | 位置值高8位 |
| 4 | Data[1] | 低字节 | 位置值低8位 |
| 5 | Checksum | 校验和 | 前5字节之和 |
```

### **角度与位置值转换**
```c
// 角度转位置值公式
position = angle * 1000 / 240

// 示例转换
90°  → 375  (0x0177)
120° → 500  (0x01F4)
180° → 750  (0x02EE)
240° → 1000 (0x03E8)
```

### **数据字节顺序**
```c
// 正确的字节顺序 (大端模式)
cmd.data[0] = (uint8_t)(position >> 8);   // 高字节
cmd.data[1] = (uint8_t)(position & 0xFF); // 低字节

// 示例：位置值375 (0x0177)
data[0] = 0x01  // 高字节
data[1] = 0x77  // 低字节
```

## 🚀 **运行流程**

### **初始化序列**
1. **系统初始化**: 时钟、GPIO配置
2. **OLED初始化**: 显示屏准备
3. **LED初始化**: 指示灯准备
4. **串口初始化**: USART1配置 (115200, 8N1)

### **舵机控制序列**
1. **延时1秒**: 等待系统稳定
2. **控制ID1**: 设置到90°位置
3. **延时500ms**: 等待舵机运动
4. **控制ID2**: 设置到120°位置
5. **延时500ms**: 等待舵机运动
6. **LED闪烁**: 指示命令完成

### **主循环**
- 持续运行，可扩展其他功能
- 100ms周期循环
- 可添加状态监控、命令接收等

## 📋 **技术参数**

### **通信参数**
| 参数 | 数值 | 说明 |
|------|------|------|
| 波特率 | 115200 bps | 标准波特率 |
| 数据位 | 8 bit | 无奇偶校验 |
| 停止位 | 1 bit | 标准配置 |
| 流控制 | 无 | 简化连接 |

### **控制参数**
| 参数 | 数值 | 说明 |
|------|------|------|
| 角度范围 | 0°~240° | 舵机物理限制 |
| 位置精度 | 0.24°/步 | 1000步分辨率 |
| 响应时间 | <100ms | 典型值 |
| 控制周期 | 500ms | 命令间隔 |

### **目标位置设置**
| 舵机ID | 目标角度 | 位置值 | 十六进制 |
|--------|----------|--------|----------|
| ID1 | 90° | 375 | 0x0177 |
| ID2 | 120° | 500 | 0x01F4 |

## 🔍 **调试信息**

### **OLED显示内容**
```
HTS-25L Servo    ← 第1行：系统标识
ID1: 90 deg      ← 第2行：ID1舵机角度
ID2:120 deg      ← 第3行：ID2舵机角度
UART Control     ← 第4行：控制方式
```

### **LED指示**
- **常亮**: 系统正常运行
- **闪烁**: 命令发送完成
- **熄灭**: 系统待机

## ⚠️ **注意事项**

### **硬件注意事项**
1. **电源供电**: 确保5V电源能提供足够电流 (每个舵机约1A)
2. **信号线连接**: 串口信号线要可靠连接，避免干扰
3. **GND连接**: 必须连接公共地线
4. **舵机ID**: 确保每个舵机设置了正确的ID

### **软件注意事项**
1. **波特率匹配**: 确保舵机和STM32波特率一致
2. **命令间隔**: 发送命令间要有适当延时
3. **校验和计算**: 确保校验和正确计算
4. **字节顺序**: 注意高低字节顺序

### **调试建议**
1. **串口监控**: 使用示波器或逻辑分析仪监控串口信号
2. **分步测试**: 先测试单个舵机，再测试多个
3. **电源检查**: 确认电源电压稳定
4. **连线检查**: 仔细检查所有连线

## 🛠️ **扩展功能**

### **可添加功能**
1. **位置反馈**: 读取舵机当前位置
2. **速度控制**: 设置舵机运动速度
3. **多舵机控制**: 扩展到更多舵机
4. **外部命令**: 通过按键或其他接口控制
5. **状态监控**: 实时显示舵机状态

### **协议扩展**
```c
// 读取位置指令
cmd.instruction = 0x02;  // 读取位置

// 设置速度指令  
cmd.instruction = 0x07;  // 设置速度
```

---
*本系统实现了STM32F103C8T6对HTS-25L总线舵机的精确控制，具有良好的扩展性和可靠性。*
