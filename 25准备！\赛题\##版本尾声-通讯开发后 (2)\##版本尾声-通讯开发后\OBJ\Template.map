Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    main.o(.text) refers to delay.o(.text) for delay_init
    main.o(.text) refers to usart.o(.text) for uart_init
    main.o(.text) refers to key.o(.text) for Key_Init
    main.o(.text) refers to motor_control.o(.text) for Motor_System_Init
    main.o(.text) refers to k230_comm.o(.text) for K230_Comm_Init
    main.o(.text) refers to noretval__2printf.o(.text) for __2printf
    main.o(.text) refers to main.o(.data) for aim_completed_flag
    stm32f4xx_it.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_GetITStatus
    stm32f4xx_it.o(.text) refers to main.o(.data) for system_tick_ms
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    startup_stm32f40_41xxx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(.text) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(.text) for USART1_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(.text) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(HEAP) for Heap_Mem
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(STACK) for Stack_Mem
    stm32f4xx_adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_can.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_cryp.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_dac.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_dma2d.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_fsmc.o(.text) refers to stm32f4xx_fsmc.o(.constdata) for FSMC_DefaultTimingStruct
    stm32f4xx_gpio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_hash.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_hash_md5.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DeInit
    stm32f4xx_hash_sha1.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DeInit
    stm32f4xx_i2c.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_ltdc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_pwr.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_rcc.o(.text) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_rng.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_sai.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_sdio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_syscfg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_wwdg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    delay.o(.text) refers to misc.o(.text) for SysTick_CLKSourceConfig
    delay.o(.text) refers to delay.o(.data) for fac_us
    usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    usart.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    usart.o(.text) refers to stm32f4xx_usart.o(.text) for USART_Init
    usart.o(.text) refers to misc.o(.text) for NVIC_Init
    usart.o(.text) refers to usart.o(.data) for str_index
    usart.o(.text) refers to usart.o(.bss) for k230_str_buffer
    atd5984.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    atd5984.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    atd5984.o(.text) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    atd5984.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    atd5984.o(.text) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    atd5984.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_ResetBits
    atd5984.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    atd5984.o(.text) refers to noretval__2printf.o(.text) for __2printf
    atd5984.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    atd5984.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    atd5984.o(.text) refers to delay.o(.text) for delay_ms
    atd5984.o(.text) refers to atd5984.o(.data) for current_pwm_frequency
    adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    adc.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    adc.o(.text) refers to stm32f4xx_adc.o(.text) for ADC_CommonInit
    adc.o(.text) refers to delay.o(.text) for delay_ms
    key.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    key.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    key.o(.text) refers to key.o(.data) for key_state
    tim.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphClockCmd
    tim.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    tim.o(.text) refers to misc.o(.text) for NVIC_Init
    motor_control.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    motor_control.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    motor_control.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    motor_control.o(.text) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    motor_control.o(.text) refers to _printf_str.o(.text) for _printf_str
    motor_control.o(.text) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    motor_control.o(.text) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    motor_control.o(.text) refers to noretval__2printf.o(.text) for __2printf
    motor_control.o(.text) refers to atd5984.o(.text) for ATD5984_Init
    motor_control.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_CCxCmd
    motor_control.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    motor_control.o(.text) refers to motor_control.o(.data) for motor_state
    k230_comm.o(.text) refers to _scanf_int.o(.text) for _scanf_int
    k230_comm.o(.text) refers to _scanf_str.o(.text) for _scanf_string
    k230_comm.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    k230_comm.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    k230_comm.o(.text) refers to motor_control.o(.text) for Motor_SetSpeed
    k230_comm.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    k230_comm.o(.text) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    k230_comm.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    k230_comm.o(.text) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    k230_comm.o(.text) refers to delay.o(.text) for delay_ms
    k230_comm.o(.text) refers to strncmp.o(.text) for strncmp
    k230_comm.o(.text) refers to k230_comm.o(i.K230_ClearStringFlag) for K230_ClearStringFlag
    k230_comm.o(.text) refers to main.o(.data) for system_tick_ms
    k230_comm.o(.text) refers to k230_comm.o(.bss) for g_k230_pid
    k230_comm.o(.text) refers to usart.o(.data) for k230_str_ready
    k230_comm.o(.text) refers to usart.o(.bss) for k230_str_buffer
    k230_comm.o(.text) refers to main.o(.text) for Set_Aim_Completed_Flag
    k230_comm.o(.text) refers to __0sscanf.o(.text) for __0sscanf
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to stdio_streams.o(.bss) for __stdout
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to stdio_streams.o(.bss) for __stdout
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    fabs.o(i.__hardfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to fputc.o(i.fputc) for fputc
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace.o(.text) for isspace
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _printf_char_common.o(.text) refers to __printf_wp.o(.text) for __printf
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    _scanf.o(.text) refers (Weak) to _scanf_str.o(.text) for _scanf_string
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fputc.o(i.fputc) refers to flsbuf.o(.text) for __flsbuf_byte
    initio.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000024) for __rt_lib_init_stdio_2
    initio.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) for __rt_lib_shutdown_stdio_2
    initio.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    initio.o(.text) refers to fopen.o(.text) for freopen
    initio.o(.text) refers to defsig_rtred_outer.o(.text) for __rt_SIGRTRED
    initio.o(.text) refers to setvbuf.o(.text) for setvbuf
    initio.o(.text) refers to fclose.o(.text) for _fclose_internal
    initio.o(.text) refers to h1_free.o(.text) for free
    initio.o(.text) refers to stdio_streams.o(.bss) for __stdin
    initio.o(.text) refers to stdio_streams.o(.bss) for __stdout
    initio.o(.text) refers to stdio_streams.o(.bss) for __stderr
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdin
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdout
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stderr
    initio.o(.text) refers to sys_io.o(.constdata) for __stdin_name
    initio.o(.text) refers to sys_io.o(.constdata) for __stdout_name
    initio.o(.text) refers to sys_io.o(.constdata) for __stderr_name
    initio_locked.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000024) for __rt_lib_init_stdio_2
    initio_locked.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) for __rt_lib_shutdown_stdio_2
    initio_locked.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    initio_locked.o(.text) refers to fopen.o(.text) for freopen
    initio_locked.o(.text) refers to defsig_rtred_outer.o(.text) for __rt_SIGRTRED
    initio_locked.o(.text) refers to setvbuf.o(.text) for setvbuf
    initio_locked.o(.text) refers to fclose.o(.text) for _fclose_internal
    initio_locked.o(.text) refers to h1_free.o(.text) for free
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stdout
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stderr
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdin
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdout
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stderr
    initio_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stdin_name
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stdout_name
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stderr_name
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_io.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.text) refers to strlen.o(.text) for strlen
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f40_41xxx.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    free.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    free.o(.text) refers to heapstubs.o(.text) for __Heap_Free
    h1_free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_free_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._FDIterate) refers to heap2.o(.conststring) for .conststring
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i.___Heap_Stats$realtime) refers to heap2.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(i._FDIterate) for _FDIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(.conststring) for .conststring
    heap2.o(i._free$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._malloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._malloc$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._posix_memalign$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._posix_memalign$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to h1_free.o(.text) for free
    heap2.o(i._realloc$realtime) refers to h1_alloc.o(.text) for malloc
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._realloc$realtime) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    heap2mt.o(i._FDIterate) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i.___Heap_Initialize$realtime$concurrent) refers to mutex_dummy.o(.text) for _mutex_initialize
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i.___Heap_Stats$realtime$concurrent) refers to heap2mt.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(i._FDIterate) for _FDIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i._free$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._malloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._malloc$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_free.o(.text) for free
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_alloc.o(.text) for malloc
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    flsbuf.o(.text) refers to stdio.o(.text) for _deferredlazyseek
    flsbuf.o(.text) refers to sys_io.o(.text) for _sys_flen
    flsbuf.o(.text) refers to h1_alloc.o(.text) for malloc
    streamlock.o(.data) refers (Special) to initio.o(.text) for _initio
    fopen.o(.text) refers to fclose.o(.text) for _fclose_internal
    fopen.o(.text) refers to sys_io.o(.text) for _sys_open
    fopen.o(.text) refers to fseek.o(.text) for _fseek
    fopen.o(.text) refers to h1_alloc.o(.text) for malloc
    fopen.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen.o(.text) refers to stdio_streams.o(.bss) for __stdin
    fclose.o(.text) refers to stdio.o(.text) for _fflush
    fclose.o(.text) refers to sys_io.o(.text) for _sys_close
    fclose.o(.text) refers to h1_free.o(.text) for free
    fclose.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen_locked.o(.text) refers to fclose.o(.text) for _fclose_internal
    fopen_locked.o(.text) refers to sys_io.o(.text) for _sys_open
    fopen_locked.o(.text) refers to fseek.o(.text) for _fseek
    fopen_locked.o(.text) refers to h1_alloc.o(.text) for malloc
    fopen_locked.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    fopen_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_rtred_outer.o(.text) refers to defsig_rtred_inner.o(.text) for __rt_SIGRTRED_inner
    defsig_rtred_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtred_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000005) refers (Weak) to init_alloc.o(.text) for _init_alloc
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000024) refers (Weak) to initio.o(.text) for _initio
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) refers (Weak) to initio.o(.text) for _terminateio
    libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) refers (Weak) to term_alloc.o(.text) for _terminate_alloc
    flsbuf_fwide.o(.text) refers to flsbuf.o(.text) for __flsbuf
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    rt_heap_descriptor.o(.text) refers to rt_heap_descriptor.o(.bss) for __rt_heap_descriptor_data
    rt_heap_descriptor_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    init_alloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    init_alloc.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000005) for __rt_lib_init_heap_2
    init_alloc.o(.text) refers (Special) to maybetermalloc1.o(.emb_text) for _maybe_terminate_alloc
    init_alloc.o(.text) refers to h1_extend.o(.text) for __Heap_ProvideMemory
    init_alloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    init_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    init_alloc.o(.text) refers to h1_init.o(.text) for __Heap_Initialize
    malloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    malloc.o(.text) refers (Special) to init_alloc.o(.text) for _init_alloc
    malloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    malloc.o(.text) refers to heapstubs.o(.text) for __Heap_Alloc
    h1_alloc.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_alloc_mt.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc_mt.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_alloc_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    _printf_char_file_locked.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file_locked.o(.text) refers to fputc.o(i._fputc$unlocked) for _fputc$unlocked
    fseek.o(.text) refers to sys_io.o(.text) for _sys_istty
    fseek.o(.text) refers to ftell.o(.text) for _ftell_internal
    fseek.o(.text) refers to stdio.o(.text) for _seterr
    stdio.o(.text) refers to sys_io.o(.text) for _sys_seek
    fwritefast.o(.text) refers to stdio.o(.text) for _writebuf
    fwritefast.o(.text) refers to flsbuf.o(.text) for __flsbuf_byte
    fwritefast.o(.text) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    fwritefast_locked.o(.text) refers to stdio.o(.text) for _writebuf
    fwritefast_locked.o(.text) refers to flsbuf.o(.text) for __flsbuf_byte
    fwritefast_locked.o(.text) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    assert_stdio.o(.text) refers to fputs.o(.text) for fputs
    assert_stdio.o(.text) refers to fflush.o(.text) for fflush
    assert_stdio.o(.text) refers to stdio_streams.o(.bss) for __stderr
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    maybetermalloc2.o(.emb_text) refers (Special) to term_alloc.o(.text) for _terminate_alloc
    h1_extend.o(.text) refers to h1_free.o(.text) for free
    h1_init_mt.o(.text) refers to mutex_dummy.o(.text) for _mutex_initialize
    h1_extend_mt.o(.text) refers to h1_free_mt.o(.text) for _free_internal
    fflush.o(.text) refers to stdio.o(.text) for _fflush
    fflush.o(.text) refers to fseek.o(.text) for _fseek
    fflush.o(.text) refers to stdio_streams.o(.bss) for __stdin
    fputs.o(.text) refers to fputc.o(i.fputc) for fputc
    ftell.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    fflush_locked.o(.text) refers to stdio.o(.text) for _fflush
    fflush_locked.o(.text) refers to fseek.o(.text) for _fseek
    fflush_locked.o(.text) refers to fflush.o(.text) for _do_fflush
    fflush_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    fflush_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    heapauxa.o(.text) refers to heapauxa.o(.data) for .data
    _get_argv.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv.o(.text) refers to h1_alloc.o(.text) for malloc
    _get_argv.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv.o(.text) refers to sys_command.o(.text) for _sys_command_string
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    term_alloc.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_heap_2
    term_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    term_alloc.o(.text) refers to h1_final.o(.text) for __Heap_Finalize
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig.o(CL$$defsig) refers to defsig_rtred_inner.o(.text) for __rt_SIGRTRED_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    k230_comm.o(i.K230_ClearStringFlag) refers to usart.o(.data) for k230_str_ready


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.text), (1124 bytes).
    Removing stm32f4xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(.text), (2544 bytes).
    Removing stm32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_crc.o(.text), (72 bytes).
    Removing stm32f4xx_cryp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.text), (856 bytes).
    Removing stm32f4xx_cryp_aes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.text), (4586 bytes).
    Removing stm32f4xx_cryp_des.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.text), (472 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.text), (536 bytes).
    Removing stm32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dac.o(.text), (528 bytes).
    Removing stm32f4xx_dbgmcu.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.text), (100 bytes).
    Removing stm32f4xx_dcmi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.text), (396 bytes).
    Removing stm32f4xx_dma2d.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.text), (948 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma.o(.text), (936 bytes).
    Removing stm32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(.text), (272 bytes).
    Removing stm32f4xx_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash.o(.text), (1684 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.text), (64 bytes).
    Removing stm32f4xx_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.text), (1480 bytes).
    Removing stm32f4xx_fsmc.o(.constdata), (28 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.text), (552 bytes).
    Removing stm32f4xx_hash_md5.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.text), (534 bytes).
    Removing stm32f4xx_hash_sha1.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.text), (548 bytes).
    Removing stm32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.text), (1110 bytes).
    Removing stm32f4xx_iwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.text), (64 bytes).
    Removing stm32f4xx_ltdc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.text), (1672 bytes).
    Removing stm32f4xx_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.text), (364 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rng.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.text), (160 bytes).
    Removing stm32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.text), (3432 bytes).
    Removing stm32f4xx_sai.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sai.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sai.o(.text), (524 bytes).
    Removing stm32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.text), (476 bytes).
    Removing stm32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spi.o(.text), (1152 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.text), (148 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.text), (144 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.emb_text), (16 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing atd5984.o(.rev16_text), (4 bytes).
    Removing atd5984.o(.revsh_text), (4 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.text), (208 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.text), (86 bytes).
    Removing motor_control.o(.rev16_text), (4 bytes).
    Removing motor_control.o(.revsh_text), (4 bytes).
    Removing k230_comm.o(.rev16_text), (4 bytes).
    Removing k230_comm.o(.revsh_text), (4 bytes).

128 unused section(s) (total 28192 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  mutex_dummy.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_io.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/assert.c                         0x00000000   Number         0  assert_stdio.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init_mt.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  fdtree.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2mt.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  term_alloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  free.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  init_alloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  heapstubs.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxa.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file_locked.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_int.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_str.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_outer.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  setvbuf_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fopen_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fflush_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  stdio_streams.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputs_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fflush.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  initio.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputc_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  initio_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fwritefast_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputs.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ftell.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fwritefast.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  stdio.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  flsbuf.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fseek.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  setvbuf.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  streamlock.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fopen.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fclose.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/wchar.c                          0x00000000   Number         0  flsbuf_fwide.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ..\CORE\startup_stm32f40_41xxx.s         0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    ..\FWLIB\src\misc.c                      0x00000000   Number         0  misc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_adc.c             0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_can.c             0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_crc.c             0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp.c            0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_aes.c        0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_des.c        0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_tdes.c       0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dac.c             0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dbgmcu.c          0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dcmi.c            0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma.c             0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma2d.c           0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_exti.c            0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_flash.c           0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_flash_ramfunc.c   0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_fsmc.c            0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_gpio.c            0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash.c            0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash_md5.c        0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash_sha1.c       0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_i2c.c             0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_iwdg.c            0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_ltdc.c            0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_pwr.c             0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rcc.c             0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rng.c             0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rtc.c             0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_sai.c             0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_sdio.c            0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_spi.c             0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_syscfg.c          0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_tim.c             0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_usart.c           0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_wwdg.c            0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\HAREWARE\ADC\adc.c                    0x00000000   Number         0  adc.o ABSOLUTE
    ..\HAREWARE\ATD5984\ATD5984.c            0x00000000   Number         0  atd5984.o ABSOLUTE
    ..\HAREWARE\K230_COMM\k230_comm.c        0x00000000   Number         0  k230_comm.o ABSOLUTE
    ..\HAREWARE\KEY\KEY.c                    0x00000000   Number         0  key.o ABSOLUTE
    ..\HAREWARE\MOTOR_CONTROL\motor_control.c 0x00000000   Number         0  motor_control.o ABSOLUTE
    ..\HAREWARE\TIM\TIM.c                    0x00000000   Number         0  tim.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\FWLIB\\src\\misc.c                   0x00000000   Number         0  misc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_adc.c          0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_can.c          0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_crc.c          0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp.c         0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_aes.c     0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_des.c     0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_tdes.c    0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dac.c          0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dbgmcu.c       0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dcmi.c         0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma.c          0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma2d.c        0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_exti.c         0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_flash.c        0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_fsmc.c         0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_gpio.c         0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash.c         0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash_md5.c     0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash_sha1.c    0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_i2c.c          0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_iwdg.c         0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_ltdc.c         0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_pwr.c          0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rcc.c          0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rng.c          0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rtc.c          0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_sai.c          0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_sdio.c         0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_spi.c          0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_syscfg.c       0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_tim.c          0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_usart.c        0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_wwdg.c         0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\\HAREWARE\\ADC\\adc.c                 0x00000000   Number         0  adc.o ABSOLUTE
    ..\\HAREWARE\\ATD5984\\ATD5984.c         0x00000000   Number         0  atd5984.o ABSOLUTE
    ..\\HAREWARE\\K230_COMM\\k230_comm.c     0x00000000   Number         0  k230_comm.o ABSOLUTE
    ..\\HAREWARE\\KEY\\KEY.c                 0x00000000   Number         0  key.o ABSOLUTE
    ..\\HAREWARE\\MOTOR_CONTROL\\motor_control.c 0x00000000   Number         0  motor_control.o ABSOLUTE
    ..\\HAREWARE\\TIM\\TIM.c                 0x00000000   Number         0  tim.o ABSOLUTE
    ..\\SYSTEM\\delay\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    ..\\SYSTEM\\usart\\usart.c               0x00000000   Number         0  usart.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001fc   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x080001fc   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000009  0x08000202   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$00000014  0x08000208   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x0800020e   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000212   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x08000214   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000218   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000005          0x08000218   Section        8  libinit2.o(.ARM.Collect$$libinit$$00000005)
    .ARM.Collect$$libinit$$0000000A          0x08000220   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000220   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000220   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000220   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000226   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x08000226   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x08000232   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000232   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000232   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x0800023c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800023c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800023c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800023c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800023c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800023c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800023c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000024          0x0800023c   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000024)
    .ARM.Collect$$libinit$$00000025          0x08000240   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000240   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000240   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000240   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000240   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000240   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000242   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000244   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000244   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000005      0x08000244   Section        4  libshutdown2.o(.ARM.Collect$$libshutdown$$00000005)
    .ARM.Collect$$libshutdown$$00000006      0x08000248   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000248   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08000248   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08000248   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08000248   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08000248   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x0800024a   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0800024a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0800024a   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000250   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000250   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000254   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000254   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800025c   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800025e   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800025e   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000262   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .emb_text                                0x08000268   Section        0  maybetermalloc1.o(.emb_text)
    .text                                    0x08000268   Section        0  main.o(.text)
    .text                                    0x08000390   Section        0  stm32f4xx_it.o(.text)
    .text                                    0x080003d0   Section        0  system_stm32f4xx.o(.text)
    SetSysClock                              0x080003d1   Thumb Code   220  system_stm32f4xx.o(.text)
    .text                                    0x080005e0   Section       64  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x080005e0   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x08000620   Section        0  misc.o(.text)
    .text                                    0x08000700   Section        0  stm32f4xx_gpio.o(.text)
    .text                                    0x08000994   Section        0  stm32f4xx_rcc.o(.text)
    .text                                    0x08000ff0   Section        0  stm32f4xx_tim.o(.text)
    TI4_Config                               0x0800178f   Thumb Code    80  stm32f4xx_tim.o(.text)
    TI3_Config                               0x080017f1   Thumb Code    72  stm32f4xx_tim.o(.text)
    TI2_Config                               0x08001853   Thumb Code    90  stm32f4xx_tim.o(.text)
    TI1_Config                               0x080018bf   Thumb Code    58  stm32f4xx_tim.o(.text)
    .text                                    0x08001c94   Section        0  stm32f4xx_usart.o(.text)
    .text                                    0x080020e8   Section        0  delay.o(.text)
    .text                                    0x080021ec   Section        0  usart.o(.text)
    .text                                    0x08002310   Section        0  atd5984.o(.text)
    .text                                    0x08003200   Section        0  key.o(.text)
    .text                                    0x08003294   Section        0  motor_control.o(.text)
    .text                                    0x08003740   Section        0  k230_comm.o(.text)
    get_tick_ms                              0x08003741   Thumb Code     6  k230_comm.o(.text)
    constrain_float                          0x080037f1   Thumb Code    42  k230_comm.o(.text)
    constrain_uint16                         0x080038e3   Thumb Code    58  k230_comm.o(.text)
    .text                                    0x08003cd0   Section        0  noretval__2printf.o(.text)
    .text                                    0x08003ce8   Section        0  _printf_str.o(.text)
    .text                                    0x08003d3c   Section        0  _printf_dec.o(.text)
    .text                                    0x08003db4   Section        0  __printf_wp.o(.text)
    .text                                    0x08003ec4   Section        0  __0sscanf.o(.text)
    .text                                    0x08003f00   Section        0  _scanf_int.o(.text)
    .text                                    0x0800404c   Section        0  _scanf_str.o(.text)
    .text                                    0x0800412c   Section        0  strncmp.o(.text)
    .text                                    0x080041c2   Section        0  heapauxi.o(.text)
    .text                                    0x080041c8   Section        0  _printf_intcommon.o(.text)
    .text                                    0x0800427a   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x0800427d   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08004698   Section        0  _printf_char.o(.text)
    .text                                    0x080046c4   Section        0  _printf_char_file.o(.text)
    .text                                    0x080046e8   Section        0  _chval.o(.text)
    .text                                    0x08004704   Section        0  scanf_char.o(.text)
    _scanf_char_input                        0x08004705   Thumb Code    12  scanf_char.o(.text)
    .text                                    0x08004730   Section        0  _sgetc.o(.text)
    .text                                    0x08004770   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08004778   Section      138  lludiv10.o(.text)
    .text                                    0x08004802   Section        0  isspace.o(.text)
    .text                                    0x08004814   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08004815   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08004844   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x080048c4   Section        0  _scanf.o(.text)
    .text                                    0x08004c38   Section        0  bigflt0.o(.text)
    .text                                    0x08004d1c   Section        0  ferror.o(.text)
    .text                                    0x08004d24   Section        0  initio.o(.text)
    .text                                    0x08004e5c   Section        0  sys_io.o(.text)
    .text                                    0x08004ec4   Section        8  libspace.o(.text)
    .text                                    0x08004ecc   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08004f18   Section       16  rt_ctype_table.o(.text)
    .text                                    0x08004f28   Section        0  h1_free.o(.text)
    .text                                    0x08004f76   Section        0  flsbuf.o(.text)
    .text                                    0x0800514c   Section        0  setvbuf.o(.text)
    .text                                    0x08005194   Section        0  fopen.o(.text)
    _freopen_locked                          0x08005195   Thumb Code     0  fopen.o(.text)
    .text                                    0x08005280   Section        0  fclose.o(.text)
    .text                                    0x080052cc   Section        0  exit.o(.text)
    .text                                    0x080052de   Section        0  defsig_rtred_outer.o(.text)
    .text                                    0x080052ec   Section       78  rt_memclr_w.o(.text)
    .text                                    0x0800533c   Section      128  strcmpv7m.o(.text)
    .text                                    0x080053bc   Section        2  use_no_semi.o(.text)
    .text                                    0x080053be   Section        0  indicate_semi.o(.text)
    .text                                    0x080053c0   Section        8  rt_heap_descriptor_intlibspace.o(.text)
    .text                                    0x080053c8   Section        0  hguard.o(.text)
    .text                                    0x080053cc   Section        0  init_alloc.o(.text)
    .text                                    0x08005456   Section        0  h1_alloc.o(.text)
    .text                                    0x080054b4   Section        0  fseek.o(.text)
    .text                                    0x080055ac   Section        0  stdio.o(.text)
    .text                                    0x0800569c   Section        0  defsig_exit.o(.text)
    .text                                    0x080056a8   Section        0  defsig_rtred_inner.o(.text)
    .text                                    0x080056dc   Section        0  strlen.o(.text)
    .text                                    0x0800571c   Section        0  sys_exit.o(.text)
    .text                                    0x08005728   Section        0  h1_init.o(.text)
    .text                                    0x08005736   Section        0  h1_extend.o(.text)
    .text                                    0x0800576a   Section        0  ftell.o(.text)
    .text                                    0x080057ac   Section        0  defsig_general.o(.text)
    .text                                    0x080057de   Section        0  defsig_rtmem_outer.o(.text)
    .text                                    0x080057ec   Section        0  sys_wrch.o(.text)
    .text                                    0x080057fc   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08005804   Section        0  defsig_rtmem_inner.o(.text)
    CL$$btod_d2e                             0x08005854   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08005892   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x080058d8   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08005938   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08005c70   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08005d4c   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08005d76   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08005da0   Section      580  btod.o(CL$$btod_mult_common)
    i.K230_ClearStringFlag                   0x08005fe4   Section        0  k230_comm.o(i.K230_ClearStringFlag)
    i.__ARM_fpclassify                       0x08005ff0   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__hardfp_fabs                          0x08006020   Section        0  fabs.o(i.__hardfp_fabs)
    i._is_digit                              0x08006034   Section        0  __printf_wp.o(i._is_digit)
    i.fputc                                  0x08006042   Section        0  fputc.o(i.fputc)
    locale$$code                             0x0800605c   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x08006088   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$dfixu                              0x080060b4   Section       90  dfixu.o(x$fpl$dfixu)
    $v0                                      0x080060b4   Number         0  dfixu.o(x$fpl$dfixu)
    x$fpl$dmul                               0x08006110   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x08006110   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08006264   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x08006264   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08006300   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x08006300   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$f2d                                0x0800630c   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x0800630c   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x08006362   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x08006362   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x080063ee   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x080063ee   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$printf1                            0x080063f8   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x080063f8   Number         0  printf1.o(x$fpl$printf1)
    .constdata                               0x080063fc   Section      148  bigflt0.o(.constdata)
    x$fpl$usenofp                            0x080063fc   Section        0  usenofp.o(x$fpl$usenofp)
    tenpwrs_x                                0x080063fc   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x08006438   Data          64  bigflt0.o(.constdata)
    .constdata                               0x08006490   Section        4  sys_io.o(.constdata)
    .constdata                               0x08006494   Section        4  sys_io.o(.constdata)
    .constdata                               0x08006498   Section        4  sys_io.o(.constdata)
    locale$$data                             0x080064bc   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x080064c0   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x080064c8   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x080064d4   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x080064d6   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x080064d7   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x080064d8   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x080064d8   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x080064dc   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x080064e4   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x080065e8   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section        6  main.o(.data)
    aim_state                                0x20000004   Data           1  main.o(.data)
    aim_completed_flag                       0x20000005   Data           1  main.o(.data)
    .data                                    0x20000008   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x2000001c   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x2000001c   Data          16  stm32f4xx_rcc.o(.data)
    .data                                    0x2000002c   Section        4  delay.o(.data)
    fac_us                                   0x2000002c   Data           1  delay.o(.data)
    fac_ms                                   0x2000002e   Data           2  delay.o(.data)
    .data                                    0x20000030   Section        2  usart.o(.data)
    str_index                                0x20000030   Data           1  usart.o(.data)
    .data                                    0x20000032   Section        4  atd5984.o(.data)
    current_pwm_frequency                    0x20000032   Data           2  atd5984.o(.data)
    current_pwm_frequency_motorB             0x20000034   Data           2  atd5984.o(.data)
    .data                                    0x20000036   Section        2  key.o(.data)
    key_state                                0x20000036   Data           1  key.o(.data)
    key_count                                0x20000037   Data           1  key.o(.data)
    .data                                    0x20000038   Section        2  motor_control.o(.data)
    motor_state                              0x20000038   Data           2  motor_control.o(.data)
    .data                                    0x2000003c   Section        4  stdio_streams.o(.data)
    .data                                    0x20000040   Section        4  stdio_streams.o(.data)
    .data                                    0x20000044   Section        4  stdio_streams.o(.data)
    .bss                                     0x20000048   Section      232  usart.o(.bss)
    .bss                                     0x20000130   Section       48  k230_comm.o(.bss)
    .bss                                     0x20000160   Section       84  stdio_streams.o(.bss)
    .bss                                     0x200001b4   Section       84  stdio_streams.o(.bss)
    .bss                                     0x20000208   Section       84  stdio_streams.o(.bss)
    .bss                                     0x2000025c   Section       96  libspace.o(.bss)
    HEAP                                     0x200002c0   Section      512  startup_stm32f40_41xxx.o(HEAP)
    Heap_Mem                                 0x200002c0   Data         512  startup_stm32f40_41xxx.o(HEAP)
    STACK                                    0x200004c0   Section     1024  startup_stm32f40_41xxx.o(STACK)
    Stack_Mem                                0x200004c0   Data        1024  startup_stm32f40_41xxx.o(STACK)
    __initial_sp                             0x200008c0   Data           0  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    __user_heap_extent                        - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_free                               - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    _scanf_mbtowc                             - Undefined Weak Reference
    _scanf_real                               - Undefined Weak Reference
    _scanf_wctomb                             - Undefined Weak Reference
    _scanf_wstring                            - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x080001fd   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x080001fd   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x08000203   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_s                                0x08000209   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x0800020f   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000213   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000215   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_2                     0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000005)
    __rt_lib_init_preinit_1                  0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_heap_1                     0x08000221   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000221   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_rand_1                     0x08000221   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000221   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000227   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x08000227   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x08000233   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000233   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000233   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_atexit_1                   0x0800023d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800023d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_fp_trap_1                  0x0800023d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800023d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x0800023d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800023d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_signal_1                   0x0800023d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_2                    0x0800023d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000024)
    __rt_lib_init_alloca_1                   0x08000241   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000241   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_cpp_1                      0x08000241   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000241   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_return                     0x08000241   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_stdio_1                    0x08000241   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000243   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000245   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08000245   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_stdio_2                0x08000245   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000005)
    __rt_lib_shutdown_fp_trap_1              0x08000249   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08000249   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08000249   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08000249   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08000249   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08000249   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x0800024b   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0800024b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0800024b   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000251   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000251   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000255   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000255   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800025d   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800025f   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800025f   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000263   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Set_Aim_Completed_Flag                   0x08000269   Thumb Code     8  main.o(.text)
    _maybe_terminate_alloc                   0x08000269   Thumb Code     0  maybetermalloc1.o(.emb_text)
    main                                     0x08000271   Thumb Code   218  main.o(.text)
    NMI_Handler                              0x08000391   Thumb Code     2  stm32f4xx_it.o(.text)
    HardFault_Handler                        0x08000393   Thumb Code     4  stm32f4xx_it.o(.text)
    MemManage_Handler                        0x08000397   Thumb Code     4  stm32f4xx_it.o(.text)
    BusFault_Handler                         0x0800039b   Thumb Code     4  stm32f4xx_it.o(.text)
    UsageFault_Handler                       0x0800039f   Thumb Code     4  stm32f4xx_it.o(.text)
    SVC_Handler                              0x080003a3   Thumb Code     2  stm32f4xx_it.o(.text)
    DebugMon_Handler                         0x080003a5   Thumb Code     2  stm32f4xx_it.o(.text)
    PendSV_Handler                           0x080003a7   Thumb Code     2  stm32f4xx_it.o(.text)
    SysTick_Handler                          0x080003a9   Thumb Code    12  stm32f4xx_it.o(.text)
    TIM2_IRQHandler                          0x080003b5   Thumb Code    22  stm32f4xx_it.o(.text)
    SystemInit                               0x080004ad   Thumb Code    88  system_stm32f4xx.o(.text)
    SystemCoreClockUpdate                    0x08000505   Thumb Code   174  system_stm32f4xx.o(.text)
    Reset_Handler                            0x080005e1   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI0_IRQHandler                         0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM3_IRQHandler                          0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM4_IRQHandler                          0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM5_IRQHandler                          0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM6_DAC_IRQHandler                      0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART2_IRQHandler                        0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART3_IRQHandler                        0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x080005fb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __user_initial_stackheap                 0x080005fd   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    NVIC_PriorityGroupConfig                 0x08000621   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x0800062b   Thumb Code   106  misc.o(.text)
    NVIC_SetVectorTable                      0x08000695   Thumb Code    14  misc.o(.text)
    NVIC_SystemLPConfig                      0x080006a3   Thumb Code    34  misc.o(.text)
    SysTick_CLKSourceConfig                  0x080006c5   Thumb Code    40  misc.o(.text)
    GPIO_DeInit                              0x08000701   Thumb Code   268  stm32f4xx_gpio.o(.text)
    GPIO_Init                                0x0800080d   Thumb Code   144  stm32f4xx_gpio.o(.text)
    GPIO_StructInit                          0x0800089d   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_PinLockConfig                       0x080008af   Thumb Code    34  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x080008d1   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputData                       0x080008e3   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x080008eb   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputData                      0x080008fd   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_SetBits                             0x08000905   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ResetBits                           0x08000909   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_WriteBit                            0x0800090d   Thumb Code    10  stm32f4xx_gpio.o(.text)
    GPIO_Write                               0x08000917   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ToggleBits                          0x0800091b   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_PinAFConfig                         0x08000923   Thumb Code    70  stm32f4xx_gpio.o(.text)
    RCC_DeInit                               0x08000995   Thumb Code    82  stm32f4xx_rcc.o(.text)
    RCC_HSEConfig                            0x080009e7   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_GetFlagStatus                        0x080009f5   Thumb Code    60  stm32f4xx_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x08000a31   Thumb Code    56  stm32f4xx_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x08000a69   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_HSICmd                               0x08000a7d   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_LSEConfig                            0x08000a83   Thumb Code    46  stm32f4xx_rcc.o(.text)
    RCC_LSICmd                               0x08000ab1   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLConfig                            0x08000ab7   Thumb Code    32  stm32f4xx_rcc.o(.text)
    RCC_PLLCmd                               0x08000ad7   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SConfig                         0x08000add   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SCmd                            0x08000aeb   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLSAIConfig                         0x08000af1   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PLLSAICmd                            0x08000b05   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x08000b0b   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_MCO1Config                           0x08000b11   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_MCO2Config                           0x08000b2d   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SYSCLKConfig                         0x08000b49   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x08000b5d   Thumb Code    12  stm32f4xx_rcc.o(.text)
    RCC_HCLKConfig                           0x08000b69   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK1Config                          0x08000b7d   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK2Config                          0x08000b91   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_GetClocksFreq                        0x08000ba7   Thumb Code   222  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKConfig                         0x08000c85   Thumb Code    54  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKCmd                            0x08000cbb   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_BackupResetCmd                       0x08000cc3   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_I2SCLKConfig                         0x08000ccb   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLI2SClkDivConfig                0x08000cd1   Thumb Code    26  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLSAIClkDivConfig                0x08000ceb   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockACLKConfig                   0x08000d07   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockBCLKConfig                   0x08000d1b   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_LTDCCLKDivConfig                     0x08000d2f   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_TIMCLKPresConfig                     0x08000d43   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockCmd                   0x08000d49   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockCmd                   0x08000d6b   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockCmd                   0x08000db9   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x08000ddb   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x08000dfd   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphResetCmd                   0x08000e1f   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphResetCmd                   0x08000e41   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphResetCmd                   0x08000e63   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x08000e85   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x08000ea7   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockLPModeCmd             0x08000ec9   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockLPModeCmd             0x08000eeb   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockLPModeCmd             0x08000f0d   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockLPModeCmd             0x08000f2f   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockLPModeCmd             0x08000f51   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_LSEModeConfig                        0x08000f73   Thumb Code    40  stm32f4xx_rcc.o(.text)
    RCC_ITConfig                             0x08000f9b   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_ClearFlag                            0x08000fbd   Thumb Code    18  stm32f4xx_rcc.o(.text)
    RCC_GetITStatus                          0x08000fcf   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_ClearITPendingBit                    0x08000fe5   Thumb Code     8  stm32f4xx_rcc.o(.text)
    TIM_DeInit                               0x08000ff1   Thumb Code   346  stm32f4xx_tim.o(.text)
    TIM_TimeBaseInit                         0x0800114b   Thumb Code   104  stm32f4xx_tim.o(.text)
    TIM_TimeBaseStructInit                   0x080011b3   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PrescalerConfig                      0x080011c5   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_CounterModeConfig                    0x080011cb   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetCounter                           0x080011dd   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetAutoreload                        0x080011e1   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetCounter                           0x080011e5   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetPrescaler                         0x080011eb   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_UpdateDisableConfig                  0x080011f1   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_UpdateRequestConfig                  0x08001209   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ARRPreloadConfig                     0x08001221   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_SelectOnePulseMode                   0x08001239   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetClockDivision                     0x0800124b   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_Cmd                                  0x0800125d   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_OC1Init                              0x08001275   Thumb Code   114  stm32f4xx_tim.o(.text)
    TIM_OC2Init                              0x080012e7   Thumb Code   154  stm32f4xx_tim.o(.text)
    TIM_OC3Init                              0x08001381   Thumb Code   204  stm32f4xx_tim.o(.text)
    TIM_OC4Init                              0x0800144d   Thumb Code   112  stm32f4xx_tim.o(.text)
    TIM_OCStructInit                         0x080014bd   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_SelectOCxM                           0x080014d1   Thumb Code    86  stm32f4xx_tim.o(.text)
    TIM_SetCompare1                          0x08001527   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare2                          0x0800152b   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare3                          0x0800152f   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare4                          0x08001533   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_ForcedOC1Config                      0x08001537   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ForcedOC2Config                      0x08001549   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_ForcedOC3Config                      0x08001563   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ForcedOC4Config                      0x08001575   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC1PreloadConfig                     0x0800158f   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2PreloadConfig                     0x080015a1   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3PreloadConfig                     0x080015bb   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC4PreloadConfig                     0x080015cd   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC1FastConfig                        0x080015e7   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2FastConfig                        0x080015f9   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3FastConfig                        0x08001613   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC4FastConfig                        0x08001625   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_ClearOC1Ref                          0x0800163f   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearOC2Ref                          0x08001651   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ClearOC3Ref                          0x08001669   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearOC4Ref                          0x0800167b   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_OC1PolarityConfig                    0x08001693   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC1NPolarityConfig                   0x080016a5   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2PolarityConfig                    0x080016b7   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC2NPolarityConfig                   0x080016d1   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3PolarityConfig                    0x080016eb   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3NPolarityConfig                   0x08001705   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC4PolarityConfig                    0x0800171f   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_CCxCmd                               0x08001739   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_CCxNCmd                              0x08001757   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_SetIC4Prescaler                      0x08001775   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_SetIC3Prescaler                      0x080017df   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetIC2Prescaler                      0x08001839   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_SetIC1Prescaler                      0x080018ad   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ICInit                               0x080018f9   Thumb Code   110  stm32f4xx_tim.o(.text)
    TIM_ICStructInit                         0x08001967   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PWMIConfig                           0x08001979   Thumb Code   124  stm32f4xx_tim.o(.text)
    TIM_GetCapture1                          0x080019f5   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture2                          0x080019fb   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture3                          0x08001a01   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture4                          0x08001a07   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_BDTRConfig                           0x08001a0d   Thumb Code    32  stm32f4xx_tim.o(.text)
    TIM_BDTRStructInit                       0x08001a2d   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_CtrlPWMOutputs                       0x08001a3f   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_SelectCOM                            0x08001a5d   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_CCPreloadControl                     0x08001a75   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ITConfig                             0x08001a8d   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_GenerateEvent                        0x08001a9f   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetFlagStatus                        0x08001aa3   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearFlag                            0x08001ab5   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetITStatus                          0x08001abb   Thumb Code    34  stm32f4xx_tim.o(.text)
    TIM_ClearITPendingBit                    0x08001add   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_DMAConfig                            0x08001ae3   Thumb Code    10  stm32f4xx_tim.o(.text)
    TIM_DMACmd                               0x08001aed   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectCCDMA                          0x08001aff   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_InternalClockConfig                  0x08001b17   Thumb Code    12  stm32f4xx_tim.o(.text)
    TIM_SelectInputTrigger                   0x08001b23   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ITRxExternalClockConfig              0x08001b35   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_TIxExternalClockConfig               0x08001b4d   Thumb Code    62  stm32f4xx_tim.o(.text)
    TIM_ETRConfig                            0x08001b8b   Thumb Code    28  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode1Config                  0x08001ba7   Thumb Code    54  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode2Config                  0x08001bdd   Thumb Code    32  stm32f4xx_tim.o(.text)
    TIM_SelectOutputTrigger                  0x08001bfd   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectSlaveMode                      0x08001c0f   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectMasterSlaveMode                0x08001c21   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_EncoderInterfaceConfig               0x08001c33   Thumb Code    66  stm32f4xx_tim.o(.text)
    TIM_SelectHallSensor                     0x08001c75   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_RemapConfig                          0x08001c8d   Thumb Code     6  stm32f4xx_tim.o(.text)
    USART_DeInit                             0x08001c95   Thumb Code   206  stm32f4xx_usart.o(.text)
    USART_Init                               0x08001d63   Thumb Code   204  stm32f4xx_usart.o(.text)
    USART_StructInit                         0x08001e2f   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_ClockInit                          0x08001e47   Thumb Code    32  stm32f4xx_usart.o(.text)
    USART_ClockStructInit                    0x08001e67   Thumb Code    12  stm32f4xx_usart.o(.text)
    USART_Cmd                                0x08001e73   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetPrescaler                       0x08001e8b   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_OverSampling8Cmd                   0x08001e9b   Thumb Code    22  stm32f4xx_usart.o(.text)
    USART_OneBitMethodCmd                    0x08001eb1   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendData                           0x08001ec9   Thumb Code     8  stm32f4xx_usart.o(.text)
    USART_ReceiveData                        0x08001ed1   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_SetAddress                         0x08001edb   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x08001eed   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_WakeUpConfig                       0x08001f05   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x08001f17   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINCmd                             0x08001f29   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendBreak                          0x08001f41   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_HalfDuplexCmd                      0x08001f4b   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetGuardTime                       0x08001f63   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_SmartCardCmd                       0x08001f73   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SmartCardNACKCmd                   0x08001f8b   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_IrDAConfig                         0x08001fa3   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_IrDACmd                            0x08001fb5   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_DMACmd                             0x08001fcd   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ITConfig                           0x08001fdf   Thumb Code    74  stm32f4xx_usart.o(.text)
    USART_GetFlagStatus                      0x08002029   Thumb Code    26  stm32f4xx_usart.o(.text)
    USART_ClearFlag                          0x08002043   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_GetITStatus                        0x08002055   Thumb Code   118  stm32f4xx_usart.o(.text)
    USART_ClearITPendingBit                  0x080020cb   Thumb Code    30  stm32f4xx_usart.o(.text)
    delay_init                               0x080020e9   Thumb Code    52  delay.o(.text)
    delay_us                                 0x0800211d   Thumb Code    72  delay.o(.text)
    delay_xms                                0x08002165   Thumb Code    72  delay.o(.text)
    delay_ms                                 0x080021ad   Thumb Code    56  delay.o(.text)
    uart_init                                0x080021ed   Thumb Code   164  usart.o(.text)
    K230_ParseString                         0x08002291   Thumb Code    76  usart.o(.text)
    USART1_IRQHandler                        0x080022dd   Thumb Code    30  usart.o(.text)
    Motor_B_Disable                          0x08002311   Thumb Code    14  atd5984.o(.text)
    ATD5984_Init                             0x0800231f   Thumb Code   180  atd5984.o(.text)
    STEP12_PWM_Init                          0x080023d3   Thumb Code   172  atd5984.o(.text)
    Motor_A_Rotate                           0x0800247f   Thumb Code   264  atd5984.o(.text)
    Motor_A_DirectionTest                    0x08002587   Thumb Code   188  atd5984.o(.text)
    Motor_A_StepsCalibration                 0x08002643   Thumb Code  1154  atd5984.o(.text)
    STEP_B_PWM_Init                          0x08002ac5   Thumb Code   192  atd5984.o(.text)
    TIM8_SetFrequency                        0x08002b85   Thumb Code   158  atd5984.o(.text)
    TIM8_GetCurrentFrequency                 0x08002c23   Thumb Code     6  atd5984.o(.text)
    TIM1_SetFrequency                        0x08002c29   Thumb Code   144  atd5984.o(.text)
    TIM1_GetCurrentFrequency                 0x08002cb9   Thumb Code     6  atd5984.o(.text)
    Motor_B_Rotate                           0x08002cbf   Thumb Code  1220  atd5984.o(.text)
    Motor_B_Enable                           0x08003183   Thumb Code    14  atd5984.o(.text)
    Motor_A_Enable                           0x08003191   Thumb Code    12  atd5984.o(.text)
    Motor_A_Disable                          0x0800319d   Thumb Code    12  atd5984.o(.text)
    Key_Init                                 0x08003201   Thumb Code    42  key.o(.text)
    Key_Scan                                 0x0800322b   Thumb Code    14  key.o(.text)
    Key_Scan_Debounce                        0x08003239   Thumb Code    74  key.o(.text)
    Motor_System_Init                        0x08003295   Thumb Code    50  motor_control.o(.text)
    Motor_Enable                             0x080032c7   Thumb Code    52  motor_control.o(.text)
    Motor_Stop                               0x080032fb   Thumb Code    64  motor_control.o(.text)
    Motor_Disable                            0x0800333b   Thumb Code    58  motor_control.o(.text)
    Motor_SetSpeed                           0x08003375   Thumb Code    98  motor_control.o(.text)
    Motor_Rotate                             0x080033d7   Thumb Code   130  motor_control.o(.text)
    Motor_Emergency_Stop                     0x08003459   Thumb Code    52  motor_control.o(.text)
    Motor_GetState                           0x0800348d   Thumb Code    16  motor_control.o(.text)
    Motor_GetSpeed                           0x0800349d   Thumb Code    26  motor_control.o(.text)
    Motor_IsMoving                           0x080034b7   Thumb Code    26  motor_control.o(.text)
    K230_Comm_Init                           0x08003747   Thumb Code   154  k230_comm.o(.text)
    K230_PID_SetParams                       0x080037e1   Thumb Code    16  k230_comm.o(.text)
    K230_PID_Calculate                       0x0800381b   Thumb Code   180  k230_comm.o(.text)
    K230_PID_Reset                           0x080038cf   Thumb Code    20  k230_comm.o(.text)
    K230_Offset_To_Speed                     0x0800391d   Thumb Code   120  k230_comm.o(.text)
    K230_NoCalib_Angle_Mapping               0x08003995   Thumb Code    88  k230_comm.o(.text)
    K230_Execute_Command                     0x080039ed   Thumb Code   262  k230_comm.o(.text)
    K230_Process_Frame                       0x08003af3   Thumb Code   322  k230_comm.o(.text)
    K230_Check_Timeout                       0x08003c35   Thumb Code    56  k230_comm.o(.text)
    K230_Get_CommStatus                      0x08003c6d   Thumb Code    30  k230_comm.o(.text)
    K230_Get_LastOffset                      0x08003c8b   Thumb Code     8  k230_comm.o(.text)
    K230_Get_PIDOutput                       0x08003c93   Thumb Code     8  k230_comm.o(.text)
    __2printf                                0x08003cd1   Thumb Code    20  noretval__2printf.o(.text)
    _printf_str                              0x08003ce9   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08003d3d   Thumb Code   104  _printf_dec.o(.text)
    __printf                                 0x08003db5   Thumb Code   270  __printf_wp.o(.text)
    __0sscanf                                0x08003ec5   Thumb Code    52  __0sscanf.o(.text)
    _scanf_int                               0x08003f01   Thumb Code   332  _scanf_int.o(.text)
    _scanf_string                            0x0800404d   Thumb Code   224  _scanf_str.o(.text)
    strncmp                                  0x0800412d   Thumb Code   150  strncmp.o(.text)
    __use_two_region_memory                  0x080041c3   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow                         0x080041c5   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand                         0x080041c7   Thumb Code     2  heapauxi.o(.text)
    _printf_int_common                       0x080041c9   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x0800427b   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x0800442d   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_cs_common                        0x08004699   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x080046ad   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x080046bd   Thumb Code     8  _printf_char.o(.text)
    _printf_char_file                        0x080046c5   Thumb Code    32  _printf_char_file.o(.text)
    _chval                                   0x080046e9   Thumb Code    28  _chval.o(.text)
    __vfscanf_char                           0x08004711   Thumb Code    24  scanf_char.o(.text)
    _sgetc                                   0x08004731   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x0800474f   Thumb Code    34  _sgetc.o(.text)
    __rt_locale                              0x08004771   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _ll_udiv10                               0x08004779   Thumb Code   138  lludiv10.o(.text)
    isspace                                  0x08004803   Thumb Code    18  isspace.o(.text)
    _printf_char_common                      0x0800481f   Thumb Code    32  _printf_char_common.o(.text)
    _printf_fp_infnan                        0x08004845   Thumb Code   112  _printf_fp_infnan.o(.text)
    __vfscanf                                0x080048c5   Thumb Code   880  _scanf.o(.text)
    _btod_etento                             0x08004c39   Thumb Code   224  bigflt0.o(.text)
    ferror                                   0x08004d1d   Thumb Code     8  ferror.o(.text)
    _initio                                  0x08004d25   Thumb Code   210  initio.o(.text)
    _terminateio                             0x08004df7   Thumb Code    56  initio.o(.text)
    _sys_open                                0x08004e5d   Thumb Code    20  sys_io.o(.text)
    _sys_close                               0x08004e71   Thumb Code    12  sys_io.o(.text)
    _sys_write                               0x08004e7d   Thumb Code    16  sys_io.o(.text)
    _sys_read                                0x08004e8d   Thumb Code    14  sys_io.o(.text)
    _sys_istty                               0x08004e9b   Thumb Code    12  sys_io.o(.text)
    _sys_seek                                0x08004ea7   Thumb Code    14  sys_io.o(.text)
    _sys_ensure                              0x08004eb5   Thumb Code     2  sys_io.o(.text)
    _sys_flen                                0x08004eb7   Thumb Code    12  sys_io.o(.text)
    __user_libspace                          0x08004ec5   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08004ec5   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08004ec5   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08004ecd   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x08004f19   Thumb Code    16  rt_ctype_table.o(.text)
    free                                     0x08004f29   Thumb Code    78  h1_free.o(.text)
    __flsbuf                                 0x08004f77   Thumb Code   470  flsbuf.o(.text)
    __flsbuf_byte                            0x08004f77   Thumb Code     0  flsbuf.o(.text)
    __flsbuf_wide                            0x08004f77   Thumb Code     0  flsbuf.o(.text)
    setvbuf                                  0x0800514d   Thumb Code    70  setvbuf.o(.text)
    freopen                                  0x08005195   Thumb Code   158  fopen.o(.text)
    fopen                                    0x08005233   Thumb Code    74  fopen.o(.text)
    _fclose_internal                         0x08005281   Thumb Code    76  fclose.o(.text)
    fclose                                   0x08005281   Thumb Code     0  fclose.o(.text)
    exit                                     0x080052cd   Thumb Code    18  exit.o(.text)
    __rt_SIGRTRED                            0x080052df   Thumb Code    14  defsig_rtred_outer.o(.text)
    __aeabi_memclr4                          0x080052ed   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080052ed   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080052ed   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x080052f1   Thumb Code     0  rt_memclr_w.o(.text)
    strcmp                                   0x0800533d   Thumb Code   128  strcmpv7m.o(.text)
    __I$use$semihosting                      0x080053bd   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080053bd   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x080053bf   Thumb Code     0  indicate_semi.o(.text)
    __rt_heap_descriptor                     0x080053c1   Thumb Code     8  rt_heap_descriptor_intlibspace.o(.text)
    __use_no_heap                            0x080053c9   Thumb Code     2  hguard.o(.text)
    __heap$guard                             0x080053cb   Thumb Code     2  hguard.o(.text)
    _terminate_user_alloc                    0x080053cd   Thumb Code     2  init_alloc.o(.text)
    _init_user_alloc                         0x080053cf   Thumb Code     2  init_alloc.o(.text)
    __Heap_Full                              0x080053d1   Thumb Code    34  init_alloc.o(.text)
    __Heap_Broken                            0x080053f3   Thumb Code     6  init_alloc.o(.text)
    _init_alloc                              0x080053f9   Thumb Code    94  init_alloc.o(.text)
    malloc                                   0x08005457   Thumb Code    94  h1_alloc.o(.text)
    _fseek                                   0x080054b5   Thumb Code   242  fseek.o(.text)
    fseek                                    0x080054b5   Thumb Code     0  fseek.o(.text)
    _seterr                                  0x080055ad   Thumb Code    20  stdio.o(.text)
    _writebuf                                0x080055c1   Thumb Code    84  stdio.o(.text)
    _fflush                                  0x08005615   Thumb Code    70  stdio.o(.text)
    _deferredlazyseek                        0x0800565b   Thumb Code    60  stdio.o(.text)
    __sig_exit                               0x0800569d   Thumb Code    10  defsig_exit.o(.text)
    __rt_SIGRTRED_inner                      0x080056a9   Thumb Code    14  defsig_rtred_inner.o(.text)
    strlen                                   0x080056dd   Thumb Code    62  strlen.o(.text)
    _sys_exit                                0x0800571d   Thumb Code     8  sys_exit.o(.text)
    __Heap_Initialize                        0x08005729   Thumb Code    10  h1_init.o(.text)
    __Heap_DescSize                          0x08005733   Thumb Code     4  h1_init.o(.text)
    __Heap_ProvideMemory                     0x08005737   Thumb Code    52  h1_extend.o(.text)
    _ftell_internal                          0x0800576b   Thumb Code    66  ftell.o(.text)
    ftell                                    0x0800576b   Thumb Code     0  ftell.o(.text)
    __default_signal_display                 0x080057ad   Thumb Code    50  defsig_general.o(.text)
    __rt_SIGRTMEM                            0x080057df   Thumb Code    14  defsig_rtmem_outer.o(.text)
    _ttywrch                                 0x080057ed   Thumb Code    14  sys_wrch.o(.text)
    __aeabi_errno_addr                       0x080057fd   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x080057fd   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x080057fd   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_SIGRTMEM_inner                      0x08005805   Thumb Code    22  defsig_rtmem_inner.o(.text)
    _btod_d2e                                0x08005855   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08005893   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x080058d9   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08005939   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08005c71   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08005d4d   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08005d77   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08005da1   Thumb Code   580  btod.o(CL$$btod_mult_common)
    K230_ClearStringFlag                     0x08005fe5   Thumb Code     8  k230_comm.o(i.K230_ClearStringFlag)
    __ARM_fpclassify                         0x08005ff1   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_fabs                            0x08006021   Thumb Code    20  fabs.o(i.__hardfp_fabs)
    _is_digit                                0x08006035   Thumb Code    14  __printf_wp.o(i._is_digit)
    fputc                                    0x08006043   Thumb Code    26  fputc.o(i.fputc)
    _get_lc_numeric                          0x0800605d   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x08006089   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __aeabi_d2uiz                            0x080060b5   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x080060b5   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_dmul                             0x08006111   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08006111   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08006265   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08006301   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_f2d                              0x0800630d   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x0800630d   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x08006363   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x080063ef   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x080063f7   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x080063f7   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    _printf_fp_dec                           0x080063f9   Thumb Code     4  printf1.o(x$fpl$printf1)
    __I$use$fp                               0x080063fc   Number         0  usenofp.o(x$fpl$usenofp)
    __stdin_name                             0x08006490   Data           4  sys_io.o(.constdata)
    __stdout_name                            0x08006494   Data           4  sys_io.o(.constdata)
    __stderr_name                            0x08006498   Data           4  sys_io.o(.constdata)
    Region$$Table$$Base                      0x0800649c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080064bc   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x080064e5   Data           0  lc_ctype_c.o(locale$$data)
    system_tick_ms                           0x20000000   Data           4  main.o(.data)
    SystemCoreClock                          0x20000008   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x2000000c   Data          16  system_stm32f4xx.o(.data)
    k230_str_ready                           0x20000031   Data           1  usart.o(.data)
    __aeabi_stdin                            0x2000003c   Data           4  stdio_streams.o(.data)
    __aeabi_stdout                           0x20000040   Data           4  stdio_streams.o(.data)
    __aeabi_stderr                           0x20000044   Data           4  stdio_streams.o(.data)
    k230_str_buffer                          0x20000048   Data          32  usart.o(.bss)
    USART_RX_BUF                             0x20000068   Data         200  usart.o(.bss)
    g_k230_pid                               0x20000130   Data          28  k230_comm.o(.bss)
    g_k230_control                           0x2000014c   Data          20  k230_comm.o(.bss)
    __stdin                                  0x20000160   Data          84  stdio_streams.o(.bss)
    __stdout                                 0x200001b4   Data          84  stdio_streams.o(.bss)
    __stderr                                 0x20000208   Data          84  stdio_streams.o(.bss)
    __libspace_start                         0x2000025c   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200002bc   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00006630, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000065e8, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO          256    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000008   Code   RO         1287  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         1725    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         1727    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         1729    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000000   Code   RO         1276    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001fc   0x080001fc   0x00000006   Code   RO         1275    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000202   0x08000202   0x00000006   Code   RO         1274    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000208   0x08000208   0x00000006   Code   RO         1273    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x0800020e   0x0800020e   0x00000004   Code   RO         1314    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000212   0x08000212   0x00000002   Code   RO         1510    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000214   0x08000214   0x00000004   Code   RO         1511    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000218   0x08000218   0x00000000   Code   RO         1514    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000218   0x08000218   0x00000008   Code   RO         1515    .ARM.Collect$$libinit$$00000005  c_w.l(libinit2.o)
    0x08000220   0x08000220   0x00000000   Code   RO         1517    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000220   0x08000220   0x00000000   Code   RO         1519    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000220   0x08000220   0x00000000   Code   RO         1521    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000220   0x08000220   0x00000006   Code   RO         1522    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000226   0x08000226   0x00000000   Code   RO         1524    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000226   0x08000226   0x0000000c   Code   RO         1525    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         1526    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         1528    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000232   0x08000232   0x0000000a   Code   RO         1529    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         1530    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         1532    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         1534    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         1536    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         1538    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         1540    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         1542    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800023c   0x0800023c   0x00000004   Code   RO         1543    .ARM.Collect$$libinit$$00000024  c_w.l(libinit2.o)
    0x08000240   0x08000240   0x00000000   Code   RO         1544    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000240   0x08000240   0x00000000   Code   RO         1548    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000240   0x08000240   0x00000000   Code   RO         1550    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000240   0x08000240   0x00000000   Code   RO         1552    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000240   0x08000240   0x00000000   Code   RO         1554    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000240   0x08000240   0x00000002   Code   RO         1555    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000242   0x08000242   0x00000002   Code   RO         1704    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000244   0x08000244   0x00000000   Code   RO         1557    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000244   0x08000244   0x00000000   Code   RO         1559    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000244   0x08000244   0x00000004   Code   RO         1560    .ARM.Collect$$libshutdown$$00000005  c_w.l(libshutdown2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         1561    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         1564    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         1567    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         1569    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         1572    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08000248   0x08000248   0x00000002   Code   RO         1573    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x0800024a   0x0800024a   0x00000000   Code   RO         1305    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x0800024a   0x0800024a   0x00000000   Code   RO         1335    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x0800024a   0x0800024a   0x00000006   Code   RO         1347    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000250   0x08000250   0x00000000   Code   RO         1337    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000250   0x08000250   0x00000004   Code   RO         1338    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000254   0x08000254   0x00000000   Code   RO         1340    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000254   0x08000254   0x00000008   Code   RO         1341    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800025c   0x0800025c   0x00000002   Code   RO         1579    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800025e   0x0800025e   0x00000000   Code   RO         1640    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800025e   0x0800025e   0x00000004   Code   RO         1641    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000262   0x08000262   0x00000006   Code   RO         1642    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000268   0x08000268   0x00000000   Code   RO         1646    .emb_text           c_w.l(maybetermalloc1.o)
    0x08000268   0x08000268   0x00000128   Code   RO            3    .text               main.o
    0x08000390   0x08000390   0x00000040   Code   RO          173    .text               stm32f4xx_it.o
    0x080003d0   0x080003d0   0x00000210   Code   RO          230    .text               system_stm32f4xx.o
    0x080005e0   0x080005e0   0x00000040   Code   RO          257    .text               startup_stm32f40_41xxx.o
    0x08000620   0x08000620   0x000000e0   Code   RO          263    .text               misc.o
    0x08000700   0x08000700   0x00000294   Code   RO          612    .text               stm32f4xx_gpio.o
    0x08000994   0x08000994   0x0000065c   Code   RO          775    .text               stm32f4xx_rcc.o
    0x08000ff0   0x08000ff0   0x00000ca2   Code   RO          920    .text               stm32f4xx_tim.o
    0x08001c92   0x08001c92   0x00000002   PAD
    0x08001c94   0x08001c94   0x00000454   Code   RO          940    .text               stm32f4xx_usart.o
    0x080020e8   0x080020e8   0x00000104   Code   RO          980    .text               delay.o
    0x080021ec   0x080021ec   0x00000124   Code   RO         1020    .text               usart.o
    0x08002310   0x08002310   0x00000ef0   Code   RO         1048    .text               atd5984.o
    0x08003200   0x08003200   0x00000094   Code   RO         1105    .text               key.o
    0x08003294   0x08003294   0x000004ac   Code   RO         1164    .text               motor_control.o
    0x08003740   0x08003740   0x00000590   Code   RO         1192    .text               k230_comm.o
    0x08003cd0   0x08003cd0   0x00000018   Code   RO         1245    .text               c_w.l(noretval__2printf.o)
    0x08003ce8   0x08003ce8   0x00000052   Code   RO         1249    .text               c_w.l(_printf_str.o)
    0x08003d3a   0x08003d3a   0x00000002   PAD
    0x08003d3c   0x08003d3c   0x00000078   Code   RO         1251    .text               c_w.l(_printf_dec.o)
    0x08003db4   0x08003db4   0x0000010e   Code   RO         1261    .text               c_w.l(__printf_wp.o)
    0x08003ec2   0x08003ec2   0x00000002   PAD
    0x08003ec4   0x08003ec4   0x0000003c   Code   RO         1277    .text               c_w.l(__0sscanf.o)
    0x08003f00   0x08003f00   0x0000014c   Code   RO         1279    .text               c_w.l(_scanf_int.o)
    0x0800404c   0x0800404c   0x000000e0   Code   RO         1281    .text               c_w.l(_scanf_str.o)
    0x0800412c   0x0800412c   0x00000096   Code   RO         1283    .text               c_w.l(strncmp.o)
    0x080041c2   0x080041c2   0x00000006   Code   RO         1285    .text               c_w.l(heapauxi.o)
    0x080041c8   0x080041c8   0x000000b2   Code   RO         1306    .text               c_w.l(_printf_intcommon.o)
    0x0800427a   0x0800427a   0x0000041e   Code   RO         1308    .text               c_w.l(_printf_fp_dec.o)
    0x08004698   0x08004698   0x0000002c   Code   RO         1310    .text               c_w.l(_printf_char.o)
    0x080046c4   0x080046c4   0x00000024   Code   RO         1312    .text               c_w.l(_printf_char_file.o)
    0x080046e8   0x080046e8   0x0000001c   Code   RO         1315    .text               c_w.l(_chval.o)
    0x08004704   0x08004704   0x0000002c   Code   RO         1317    .text               c_w.l(scanf_char.o)
    0x08004730   0x08004730   0x00000040   Code   RO         1319    .text               c_w.l(_sgetc.o)
    0x08004770   0x08004770   0x00000008   Code   RO         1352    .text               c_w.l(rt_locale_intlibspace.o)
    0x08004778   0x08004778   0x0000008a   Code   RO         1354    .text               c_w.l(lludiv10.o)
    0x08004802   0x08004802   0x00000012   Code   RO         1356    .text               c_w.l(isspace.o)
    0x08004814   0x08004814   0x00000030   Code   RO         1358    .text               c_w.l(_printf_char_common.o)
    0x08004844   0x08004844   0x00000080   Code   RO         1360    .text               c_w.l(_printf_fp_infnan.o)
    0x080048c4   0x080048c4   0x00000374   Code   RO         1362    .text               c_w.l(_scanf.o)
    0x08004c38   0x08004c38   0x000000e4   Code   RO         1364    .text               c_w.l(bigflt0.o)
    0x08004d1c   0x08004d1c   0x00000008   Code   RO         1389    .text               c_w.l(ferror.o)
    0x08004d24   0x08004d24   0x00000138   Code   RO         1395    .text               c_w.l(initio.o)
    0x08004e5c   0x08004e5c   0x00000066   Code   RO         1410    .text               c_w.l(sys_io.o)
    0x08004ec2   0x08004ec2   0x00000002   PAD
    0x08004ec4   0x08004ec4   0x00000008   Code   RO         1415    .text               c_w.l(libspace.o)
    0x08004ecc   0x08004ecc   0x0000004a   Code   RO         1420    .text               c_w.l(sys_stackheap_outer.o)
    0x08004f16   0x08004f16   0x00000002   PAD
    0x08004f18   0x08004f18   0x00000010   Code   RO         1422    .text               c_w.l(rt_ctype_table.o)
    0x08004f28   0x08004f28   0x0000004e   Code   RO         1426    .text               c_w.l(h1_free.o)
    0x08004f76   0x08004f76   0x000001d6   Code   RO         1482    .text               c_w.l(flsbuf.o)
    0x0800514c   0x0800514c   0x00000046   Code   RO         1484    .text               c_w.l(setvbuf.o)
    0x08005192   0x08005192   0x00000002   PAD
    0x08005194   0x08005194   0x000000ec   Code   RO         1487    .text               c_w.l(fopen.o)
    0x08005280   0x08005280   0x0000004c   Code   RO         1489    .text               c_w.l(fclose.o)
    0x080052cc   0x080052cc   0x00000012   Code   RO         1495    .text               c_w.l(exit.o)
    0x080052de   0x080052de   0x0000000e   Code   RO         1497    .text               c_w.l(defsig_rtred_outer.o)
    0x080052ec   0x080052ec   0x0000004e   Code   RO         1501    .text               c_w.l(rt_memclr_w.o)
    0x0800533a   0x0800533a   0x00000002   PAD
    0x0800533c   0x0800533c   0x00000080   Code   RO         1503    .text               c_w.l(strcmpv7m.o)
    0x080053bc   0x080053bc   0x00000002   Code   RO         1576    .text               c_w.l(use_no_semi.o)
    0x080053be   0x080053be   0x00000000   Code   RO         1578    .text               c_w.l(indicate_semi.o)
    0x080053be   0x080053be   0x00000002   PAD
    0x080053c0   0x080053c0   0x00000008   Code   RO         1586    .text               c_w.l(rt_heap_descriptor_intlibspace.o)
    0x080053c8   0x080053c8   0x00000004   Code   RO         1588    .text               c_w.l(hguard.o)
    0x080053cc   0x080053cc   0x0000008a   Code   RO         1590    .text               c_w.l(init_alloc.o)
    0x08005456   0x08005456   0x0000005e   Code   RO         1596    .text               c_w.l(h1_alloc.o)
    0x080054b4   0x080054b4   0x000000f8   Code   RO         1612    .text               c_w.l(fseek.o)
    0x080055ac   0x080055ac   0x000000f0   Code   RO         1614    .text               c_w.l(stdio.o)
    0x0800569c   0x0800569c   0x0000000a   Code   RO         1623    .text               c_w.l(defsig_exit.o)
    0x080056a6   0x080056a6   0x00000002   PAD
    0x080056a8   0x080056a8   0x00000034   Code   RO         1625    .text               c_w.l(defsig_rtred_inner.o)
    0x080056dc   0x080056dc   0x0000003e   Code   RO         1629    .text               c_w.l(strlen.o)
    0x0800571a   0x0800571a   0x00000002   PAD
    0x0800571c   0x0800571c   0x0000000c   Code   RO         1637    .text               c_w.l(sys_exit.o)
    0x08005728   0x08005728   0x0000000e   Code   RO         1648    .text               c_w.l(h1_init.o)
    0x08005736   0x08005736   0x00000034   Code   RO         1650    .text               c_w.l(h1_extend.o)
    0x0800576a   0x0800576a   0x00000042   Code   RO         1660    .text               c_w.l(ftell.o)
    0x080057ac   0x080057ac   0x00000032   Code   RO         1668    .text               c_w.l(defsig_general.o)
    0x080057de   0x080057de   0x0000000e   Code   RO         1670    .text               c_w.l(defsig_rtmem_outer.o)
    0x080057ec   0x080057ec   0x0000000e   Code   RO         1685    .text               c_w.l(sys_wrch.o)
    0x080057fa   0x080057fa   0x00000002   PAD
    0x080057fc   0x080057fc   0x00000008   Code   RO         1692    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08005804   0x08005804   0x00000050   Code   RO         1698    .text               c_w.l(defsig_rtmem_inner.o)
    0x08005854   0x08005854   0x0000003e   Code   RO         1367    CL$$btod_d2e        c_w.l(btod.o)
    0x08005892   0x08005892   0x00000046   Code   RO         1369    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x080058d8   0x080058d8   0x00000060   Code   RO         1368    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08005938   0x08005938   0x00000338   Code   RO         1377    CL$$btod_div_common  c_w.l(btod.o)
    0x08005c70   0x08005c70   0x000000dc   Code   RO         1374    CL$$btod_e2e        c_w.l(btod.o)
    0x08005d4c   0x08005d4c   0x0000002a   Code   RO         1371    CL$$btod_ediv       c_w.l(btod.o)
    0x08005d76   0x08005d76   0x0000002a   Code   RO         1370    CL$$btod_emul       c_w.l(btod.o)
    0x08005da0   0x08005da0   0x00000244   Code   RO         1376    CL$$btod_mult_common  c_w.l(btod.o)
    0x08005fe4   0x08005fe4   0x0000000c   Code   RO         1210    i.K230_ClearStringFlag  k230_comm.o
    0x08005ff0   0x08005ff0   0x00000030   Code   RO         1408    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08006020   0x08006020   0x00000014   Code   RO         1299    i.__hardfp_fabs     m_wm.l(fabs.o)
    0x08006034   0x08006034   0x0000000e   Code   RO         1263    i._is_digit         c_w.l(__printf_wp.o)
    0x08006042   0x08006042   0x0000001a   Code   RO         1392    i.fputc             c_w.l(fputc.o)
    0x0800605c   0x0800605c   0x0000002c   Code   RO         1404    locale$$code        c_w.l(lc_numeric_c.o)
    0x08006088   0x08006088   0x0000002c   Code   RO         1621    locale$$code        c_w.l(lc_ctype_c.o)
    0x080060b4   0x080060b4   0x0000005a   Code   RO         1289    x$fpl$dfixu         fz_wm.l(dfixu.o)
    0x0800610e   0x0800610e   0x00000002   PAD
    0x08006110   0x08006110   0x00000154   Code   RO         1293    x$fpl$dmul          fz_wm.l(dmul.o)
    0x08006264   0x08006264   0x0000009c   Code   RO         1327    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x08006300   0x08006300   0x0000000c   Code   RO         1329    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x0800630c   0x0800630c   0x00000056   Code   RO         1295    x$fpl$f2d           fz_wm.l(f2d.o)
    0x08006362   0x08006362   0x0000008c   Code   RO         1331    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x080063ee   0x080063ee   0x0000000a   Code   RO         1635    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x080063f8   0x080063f8   0x00000004   Code   RO         1297    x$fpl$printf1       fz_wm.l(printf1.o)
    0x080063fc   0x080063fc   0x00000000   Code   RO         1333    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x080063fc   0x080063fc   0x00000094   Data   RO         1365    .constdata          c_w.l(bigflt0.o)
    0x08006490   0x08006490   0x00000004   Data   RO         1411    .constdata          c_w.l(sys_io.o)
    0x08006494   0x08006494   0x00000004   Data   RO         1412    .constdata          c_w.l(sys_io.o)
    0x08006498   0x08006498   0x00000004   Data   RO         1413    .constdata          c_w.l(sys_io.o)
    0x0800649c   0x0800649c   0x00000020   Data   RO         1723    Region$$Table       anon$$obj.o
    0x080064bc   0x080064bc   0x0000001c   Data   RO         1403    locale$$data        c_w.l(lc_numeric_c.o)
    0x080064d8   0x080064d8   0x00000110   Data   RO         1620    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080065e8, Size: 0x000008c0, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080065e8   0x00000006   Data   RW            4    .data               main.o
    0x20000006   0x080065ee   0x00000002   PAD
    0x20000008   0x080065f0   0x00000014   Data   RW          231    .data               system_stm32f4xx.o
    0x2000001c   0x08006604   0x00000010   Data   RW          776    .data               stm32f4xx_rcc.o
    0x2000002c   0x08006614   0x00000004   Data   RW          981    .data               delay.o
    0x20000030   0x08006618   0x00000002   Data   RW         1022    .data               usart.o
    0x20000032   0x0800661a   0x00000004   Data   RW         1049    .data               atd5984.o
    0x20000036   0x0800661e   0x00000002   Data   RW         1106    .data               key.o
    0x20000038   0x08006620   0x00000002   Data   RW         1165    .data               motor_control.o
    0x2000003a   0x08006622   0x00000002   PAD
    0x2000003c   0x08006624   0x00000004   Data   RW         1324    .data               c_w.l(stdio_streams.o)
    0x20000040   0x08006628   0x00000004   Data   RW         1325    .data               c_w.l(stdio_streams.o)
    0x20000044   0x0800662c   0x00000004   Data   RW         1326    .data               c_w.l(stdio_streams.o)
    0x20000048        -       0x000000e8   Zero   RW         1021    .bss                usart.o
    0x20000130        -       0x00000030   Zero   RW         1193    .bss                k230_comm.o
    0x20000160        -       0x00000054   Zero   RW         1321    .bss                c_w.l(stdio_streams.o)
    0x200001b4        -       0x00000054   Zero   RW         1322    .bss                c_w.l(stdio_streams.o)
    0x20000208        -       0x00000054   Zero   RW         1323    .bss                c_w.l(stdio_streams.o)
    0x2000025c        -       0x00000060   Zero   RW         1416    .bss                c_w.l(libspace.o)
    0x200002bc   0x08006630   0x00000004   PAD
    0x200002c0        -       0x00000200   Zero   RW          255    HEAP                startup_stm32f40_41xxx.o
    0x200004c0        -       0x00000400   Zero   RW          254    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      3824       2018          0          4          0       4988   atd5984.o
       260          8          0          4          0       1537   delay.o
      1436        126          0          0         48       8145   k230_comm.o
       148         18          0          2          0       1154   key.o
       296         70          0          6          0     283319   main.o
       224         20          0          0          0       1877   misc.o
      1196        624          0          2          0       4868   motor_control.o
        64         26        392          0       1536        852   startup_stm32f40_41xxx.o
       660         44          0          0          0       4217   stm32f4xx_gpio.o
        64          6          0          0          0       1594   stm32f4xx_it.o
      1628         52          0         16          0      13124   stm32f4xx_rcc.o
      3234         60          0          0          0      23072   stm32f4xx_tim.o
      1108         34          0          0          0       7940   stm32f4xx_usart.o
       528         46          0         20          0       1867   system_stm32f4xx.o
       292         22          0          2        232       1957   usart.o

    ----------------------------------------------------------------------
     14964       <USER>        <GROUP>         60       1816     360511   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         2          0          0          4          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        60          8          0          0          0         84   __0sscanf.o
         8          0          0          0          0         68   __main.o
       284          0          0          0          0        156   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        28          0          0          0          0         68   _chval.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
       884          4          0          0          0        100   _scanf.o
       332          0          0          0          0         96   _scanf_int.o
       224          0          0          0          0         96   _scanf_str.o
        64          0          0          0          0         84   _sgetc.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        10          0          0          0          0         68   defsig_exit.o
        50          0          0          0          0         88   defsig_general.o
        80         58          0          0          0         76   defsig_rtmem_inner.o
        14          0          0          0          0         80   defsig_rtmem_outer.o
        52         38          0          0          0         76   defsig_rtred_inner.o
        14          0          0          0          0         80   defsig_rtred_outer.o
        18          0          0          0          0         80   exit.o
        76          0          0          0          0         88   fclose.o
         8          0          0          0          0         68   ferror.o
       470          0          0          0          0         88   flsbuf.o
       236          4          0          0          0        128   fopen.o
        26          0          0          0          0         68   fputc.o
       248          6          0          0          0         84   fseek.o
        66          0          0          0          0         76   ftell.o
        94          0          0          0          0         80   h1_alloc.o
        52          0          0          0          0         68   h1_extend.o
        78          0          0          0          0         80   h1_free.o
        14          0          0          0          0         84   h1_init.o
         6          0          0          0          0        152   heapauxi.o
         4          0          0          0          0        136   hguard.o
         0          0          0          0          0          0   indicate_semi.o
       138          0          0          0          0        168   init_alloc.o
       312         46          0          0          0        112   initio.o
        18          0          0          0          0         76   isspace.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        46          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         6          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
         0          0          0          0          0          0   maybetermalloc1.o
        24          4          0          0          0         84   noretval__2printf.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_heap_descriptor_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        44          8          0          0          0         84   scanf_char.o
        70          0          0          0          0         80   setvbuf.o
       240          6          0          0          0        156   stdio.o
         0          0          0         12        252          0   stdio_streams.o
       128          0          0          0          0         68   strcmpv7m.o
        62          0          0          0          0         76   strlen.o
       150          0          0          0          0         80   strncmp.o
        12          4          0          0          0         68   sys_exit.o
       102          0         12          0          0        240   sys_io.o
        74          0          0          0          0         80   sys_stackheap_outer.o
        14          0          0          0          0         76   sys_wrch.o
         2          0          0          0          0         68   use_no_semi.o
        90          4          0          0          0        140   dfixu.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
         4          0          0          0          0        116   printf1.o
         0          0          0          0          0          0   usenofp.o
        20          0          0          0          0        124   fabs.o
        48          0          0          0          0        124   fpclassify.o

    ----------------------------------------------------------------------
     10240        <USER>        <GROUP>         12        352       7580   Library Totals
        24          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      9310        408        460         12        348       6288   c_w.l
       838         28          0          0          0       1044   fz_wm.l
        68          0          0          0          0        248   m_wm.l

    ----------------------------------------------------------------------
     10240        <USER>        <GROUP>         12        352       7580   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     25204       3610        884         72       2168     360355   Grand Totals
     25204       3610        884         72       2168     360355   ELF Image Totals
     25204       3610        884         72          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                26088 (  25.48kB)
    Total RW  Size (RW Data + ZI Data)              2240 (   2.19kB)
    Total ROM Size (Code + RO Data + RW Data)      26160 (  25.55kB)

==============================================================================

