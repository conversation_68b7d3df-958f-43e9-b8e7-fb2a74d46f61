#include "stm32f10x.h"                  // Device header
#include "Key.h"
#include "Delay.h"

// 按键状态数组 - 支持三个按键
static KeyState_t key_states[4] = {0}; // [0]保留, [1]KEY_UNLOAD, [2]KEY_RECORD, [3]KEY_RETURN

/**
 * 三按键控制方案初始化
 * 配置PB0(舵机卸载)、PB1(记录记忆点)、PB11(回到记忆点)为上拉输入
 */
void Key_Init(void)
{
    /*开启时钟*/
    RCC_APB2PeriphClockCmd(KEY_RCC, ENABLE);

    /*GPIO初始化*/
    GPIO_InitTypeDef GPIO_InitStructure;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;       // 上拉输入
    GPIO_InitStructure.GPIO_Pin = KEY_UNLOAD_PIN | KEY_RECORD_PIN | KEY_RETURN_PIN;  // PB0、PB1、PB11
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(KEY_PORT, &GPIO_InitStructure);

    // 初始化按键状态
    for (int i = 1; i <= 3; i++) {
        key_states[i].current_state = 1;    // 初始为释放状态
        key_states[i].last_state = 1;
        key_states[i].stable_state = 1;
        key_states[i].debounce_counter = 0;
        key_states[i].press_flag = 0;
        key_states[i].release_flag = 0;
        key_states[i].click_flag = 0;
    }
}

/**
 * 读取指定按键的物理状态
 * @param key_id 按键ID
 * @return 0=按下, 1=释放
 */
uint8_t Key_ReadPin(KeyID_t key_id)
{
    switch (key_id) {
        case KEY_UNLOAD:
            return GPIO_ReadInputDataBit(KEY_PORT, KEY_UNLOAD_PIN);
        case KEY_RECORD:
            return GPIO_ReadInputDataBit(KEY_PORT, KEY_RECORD_PIN);
        case KEY_RETURN:
            return GPIO_ReadInputDataBit(KEY_PORT, KEY_RETURN_PIN);
        default:
            return 1; // 默认释放状态
    }
}

/**
 * 更新指定按键的状态 (包含消抖处理)
 * @param key_id 按键ID
 */
void Key_UpdateState(KeyID_t key_id)
{
    if (key_id < KEY_UNLOAD || key_id > KEY_RETURN) return;

    KeyState_t* state = &key_states[key_id];

    // 读取当前物理状态
    state->current_state = Key_ReadPin(key_id);

    // 消抖处理
    if (state->current_state != state->last_state) {
        state->debounce_counter = 0;  // 重置消抖计数器
    } else {
        state->debounce_counter++;
    }

    // 消抖时间到达，更新稳定状态
    if (state->debounce_counter >= (KEY_DEBOUNCE_TIME / KEY_SCAN_INTERVAL)) {
        if (state->stable_state != state->current_state) {
            state->stable_state = state->current_state;

            // 检测按键事件
            if (state->stable_state == 0) {
                // 按键按下事件
                state->press_flag = 1;
            } else {
                // 按键释放事件
                state->release_flag = 1;
                // 如果之前有按下，则产生点击事件
                if (state->press_flag) {
                    state->click_flag = 1;
                }
            }
        }
    }

    state->last_state = state->current_state;
}

/**
 * 按键扫描函数 - 需要定时调用 (建议5ms间隔)
 * 三按键控制方案核心处理函数
 */
void Key_Scan(void)
{
    Key_UpdateState(KEY_UNLOAD);   // 扫描卸载按键
    Key_UpdateState(KEY_RECORD);   // 扫描记录按键
    Key_UpdateState(KEY_RETURN);   // 扫描回位按键
}

/**
 * 获取按键事件 - 三按键控制方案专用
 * @param key_id 按键ID
 * @return 按键事件类型
 */
KeyEvent_t Key_GetEvent(KeyID_t key_id)
{
    if (key_id < KEY_UNLOAD || key_id > KEY_RETURN) return KEY_EVENT_NONE;

    KeyState_t* state = &key_states[key_id];

    if (state->click_flag) {
        return KEY_EVENT_CLICK;
    } else if (state->press_flag) {
        return KEY_EVENT_PRESS;
    } else if (state->release_flag) {
        return KEY_EVENT_RELEASE;
    }

    return KEY_EVENT_NONE;
}

/**
 * 检查按键是否按下
 * @param key_id 按键ID
 * @return 1=按下, 0=未按下
 */
uint8_t Key_IsPressed(KeyID_t key_id)
{
    if (key_id < KEY_UNLOAD || key_id > KEY_RETURN) return 0;
    return (key_states[key_id].stable_state == 0);
}

/**
 * 检查按键是否释放
 * @param key_id 按键ID
 * @return 1=释放, 0=未释放
 */
uint8_t Key_IsReleased(KeyID_t key_id)
{
    if (key_id < KEY_UNLOAD || key_id > KEY_RETURN) return 1;
    return (key_states[key_id].stable_state == 1);
}

/**
 * 检查按键是否被点击 (按下后释放)
 * @param key_id 按键ID
 * @return 1=被点击, 0=未点击
 */
uint8_t Key_IsClicked(KeyID_t key_id)
{
    if (key_id < KEY_UNLOAD || key_id > KEY_RETURN) return 0;
    return key_states[key_id].click_flag;
}

/**
 * 清除按键事件标志
 * @param key_id 按键ID
 */
void Key_ClearEvent(KeyID_t key_id)
{
    if (key_id < KEY_UNLOAD || key_id > KEY_RETURN) return;

    KeyState_t* state = &key_states[key_id];
    state->press_flag = 0;
    state->release_flag = 0;
    state->click_flag = 0;
}

/**
 * 兼容原有接口 - 阻塞式按键检测 (三按键版本)
 * @return 按键编号 (1=PB0卸载, 2=PB1记录, 3=PB11回位, 0=无按键)
 */
uint8_t Key_GetNum(void)
{
    // 检查卸载按键
    if (GPIO_ReadInputDataBit(KEY_PORT, KEY_UNLOAD_PIN) == 0) {
        Delay_ms(20);  // 消抖
        while (GPIO_ReadInputDataBit(KEY_PORT, KEY_UNLOAD_PIN) == 0);  // 等待释放
        Delay_ms(20);  // 消抖
        return KEY_UNLOAD;
    }

    // 检查记录按键
    if (GPIO_ReadInputDataBit(KEY_PORT, KEY_RECORD_PIN) == 0) {
        Delay_ms(20);  // 消抖
        while (GPIO_ReadInputDataBit(KEY_PORT, KEY_RECORD_PIN) == 0);  // 等待释放
        Delay_ms(20);  // 消抖
        return KEY_RECORD;
    }

    // 检查回位按键
    if (GPIO_ReadInputDataBit(KEY_PORT, KEY_RETURN_PIN) == 0) {
        Delay_ms(20);  // 消抖
        while (GPIO_ReadInputDataBit(KEY_PORT, KEY_RETURN_PIN) == 0);  // 等待释放
        Delay_ms(20);  // 消抖
        return KEY_RETURN;
    }

    return KEY_NONE;
}
