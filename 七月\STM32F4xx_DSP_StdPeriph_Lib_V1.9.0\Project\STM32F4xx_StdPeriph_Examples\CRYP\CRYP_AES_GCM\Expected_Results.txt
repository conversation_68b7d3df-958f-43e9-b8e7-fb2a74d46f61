 =============================================================
 ================= AES using GCM mode ===================
 ============================================================
 ---------------------------------------
 Plain Data :
 ---------------------------------------
[0xC3][0xB3][0xC4][0x1F][0x11][0x3A][0x31][0xB7][0x3D][0x9A][0x5C][0xD4][0x32][0x10][0x30][0x69]  Block 0 

 =======================================
 Encrypted Data with AES 128  Mode  GCM
 ---------------------------------------
[0x93][0xFE][0x7D][0x9E][0x9B][0xFD][0x10][0x34][0x8A][0x56][0x06][0xE5][0xCA][0xFA][0x73][0x54] Block 0 

 =======================================
 Message Authentication Code (TAG):
  ---------------------------------------
[0x00][0x32][0xA1][0xDC][0x85][0xF1][0xC9][0x78][0x69][0x25][0xA2][0xE7][0x1D][0x82][0x72][0xDD]
 =======================================
 Decrypted Data with AES 128  Mode  GCM
 ---------------------------------------
[0xC3][0xB3][0xC4][0x1F][0x11][0x3A][0x31][0xB7][0x3D][0x9A][0x5C][0xD4][0x32][0x10][0x30][0x69] Block 0 

 =======================================
 Message Authentication Code (TAG):
  ---------------------------------------
[0x00][0x32][0xA1][0xDC][0x85][0xF1][0xC9][0x78][0x69][0x25][0xA2][0xE7][0x1D][0x82][0x72][0xDD]
 Press any key to continue...