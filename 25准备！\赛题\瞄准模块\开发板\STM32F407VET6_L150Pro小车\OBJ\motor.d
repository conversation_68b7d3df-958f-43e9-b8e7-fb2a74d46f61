..\obj\motor.o: ..\HARDWARE\motor.c
..\obj\motor.o: ..\HARDWARE\motor.h
..\obj\motor.o: ..\BALANCE\system.h
..\obj\motor.o: ..\USER\stm32f4xx.h
..\obj\motor.o: ..\CORE\core_cm4.h
..\obj\motor.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\stdint.h
..\obj\motor.o: ..\CORE\core_cmInstr.h
..\obj\motor.o: ..\CORE\core_cmFunc.h
..\obj\motor.o: ..\CORE\core_cm4_simd.h
..\obj\motor.o: ..\USER\system_stm32f4xx.h
..\obj\motor.o: ..\CORE\arm_math.h
..\obj\motor.o: ..\CORE\core_cm4.h
..\obj\motor.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\string.h
..\obj\motor.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\math.h
..\obj\motor.o: ..\USER\stm32f4xx_conf.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_adc.h
..\obj\motor.o: ..\USER\stm32f4xx.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_crc.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_dbgmcu.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_dma.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_exti.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_flash.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_gpio.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_i2c.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_iwdg.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_pwr.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_rcc.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_rtc.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_sdio.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_spi.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_syscfg.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_tim.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_usart.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_wwdg.h
..\obj\motor.o: ..\FWLIB\inc\misc.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_cryp.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_hash.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_rng.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_can.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_dac.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_dcmi.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_fsmc.h
..\obj\motor.o: ..\SYSTEM\sys\sys.h
..\obj\motor.o: ..\SYSTEM\delay\delay.h
..\obj\motor.o: ..\SYSTEM\usart\usart.h
..\obj\motor.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\stdio.h
..\obj\motor.o: ..\BALANCE\balance.h
..\obj\motor.o: ..\BALANCE\system.h
..\obj\motor.o: ..\HARDWARE\led.h
..\obj\motor.o: ..\HARDWARE\oled.h
..\obj\motor.o: ..\HARDWARE\usartx.h
..\obj\motor.o: ..\HARDWARE\adc.h
..\obj\motor.o: ..\HARDWARE\can.h
..\obj\motor.o: ..\HARDWARE\motor.h
..\obj\motor.o: ..\HARDWARE\timer.h
..\obj\motor.o: ..\HARDWARE\encoder.h
..\obj\motor.o: ..\BALANCE\show.h
..\obj\motor.o: ..\HARDWARE\pstwo.h
..\obj\motor.o: ..\HARDWARE\key.h
..\obj\motor.o: ..\BALANCE\robot_select_init.h
..\obj\motor.o: ..\HARDWARE\MPU6050\I2C.h
..\obj\motor.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\stdbool.h
..\obj\motor.o: ..\HARDWARE\MPU6050\MPU6050.h
..\obj\motor.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\stdlib.h
..\obj\motor.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\stdarg.h
