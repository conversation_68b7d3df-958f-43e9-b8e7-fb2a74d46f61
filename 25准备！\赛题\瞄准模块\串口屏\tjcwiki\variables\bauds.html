<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>bauds-上电默认波特率值 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="baud-当前波特率值" href="baud.html" />
    <link rel="prev" title="dim-当前背光亮度值" href="dim.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../commands/index.html">基本指令集</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">系统变量</a><ul class="current">
<li class="toctree-l2 current"><a class="reference internal" href="index.html#id2">常用变量</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="sys0-sys2.html">sys0-sys2默认变量</a></li>
<li class="toctree-l3"><a class="reference internal" href="dp.html">dp-当前页面ID</a></li>
<li class="toctree-l3"><a class="reference internal" href="volume.html">volume-系统音量</a></li>
<li class="toctree-l3"><a class="reference internal" href="dims.html">dims-上电默认背光亮度值</a></li>
<li class="toctree-l3"><a class="reference internal" href="dim.html">dim-当前背光亮度值</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">bauds-上电默认波特率值</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#bauds-1">bauds-示例1</a></li>
<li class="toctree-l4"><a class="reference internal" href="#bauds-2">bauds-示例2</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id1">bauds-相关链接</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="baud.html">baud-当前波特率值</a></li>
<li class="toctree-l3"><a class="reference internal" href="ussp.html">ussp-无串口数据自动睡眠时间</a></li>
<li class="toctree-l3"><a class="reference internal" href="thsp.html">thsp-无触摸操作自动睡眠时间</a></li>
<li class="toctree-l3"><a class="reference internal" href="thup.html">thup-睡眠模式下触摸自动唤醒开关</a></li>
<li class="toctree-l3"><a class="reference internal" href="usup.html">usup-睡眠模式下串口数据自动唤醒开关</a></li>
<li class="toctree-l3"><a class="reference internal" href="wup.html">wup-睡眠唤醒后刷新页面设置</a></li>
<li class="toctree-l3"><a class="reference internal" href="sleep.html">sleep-睡眠</a></li>
<li class="toctree-l3"><a class="reference internal" href="lowpower.html">lowpower-睡眠模式设定</a></li>
<li class="toctree-l3"><a class="reference internal" href="bkcmd.html">bkcmd-串口指令执行状态数据返回</a></li>
<li class="toctree-l3"><a class="reference internal" href="delay.html">delay-延时</a></li>
<li class="toctree-l3"><a class="reference internal" href="rand.html">rand-随机数</a></li>
<li class="toctree-l3"><a class="reference internal" href="crcval.html">crcval-crc校验结果</a></li>
<li class="toctree-l3"><a class="reference internal" href="rtc0-rtc6.html">rtc0~rtc6-RTC时钟变量</a></li>
<li class="toctree-l3"><a class="reference internal" href="pio0-pio7.html">pio0~pio7-扩展IO端口</a></li>
<li class="toctree-l3"><a class="reference internal" href="pwm4-pwm7.html">pwm4~pwm7-扩展IO占空比</a></li>
<li class="toctree-l3"><a class="reference internal" href="pwmf.html">pwmf-PWM输出的频率</a></li>
<li class="toctree-l3"><a class="reference internal" href="audio0-audio1.html">audio0~audio1-音频通道控制</a></li>
<li class="toctree-l3"><a class="reference internal" href="hmi_color.html">串口HMI颜色代号表</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id3">不常用变量</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">系统变量</a> &raquo;</li>
      <li>bauds-上电默认波特率值</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="bauds">
<h1>bauds-上电默认波特率值<a class="headerlink" href="#bauds" title="此标题的永久链接"></a></h1>
<p>（掉电后保存，重启后继续有效）</p>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>通常配置在program.s文件中，请写在page指令的前面，page指令后面的代码是不会执行的</p>
</div>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>设置bauds时，会判断当前值与flash中保存的值是否一致，如果一致则不写入，如果不一致，则会写入flash中保存，因此修改bauds值会消耗flash寿命</p>
</div>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>设备支持的波特率有:2400 4800 9600 19200 38400 57600 115200 230400 256000 512000 921600</p>
</div>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>bauds可以在任意时刻读写，但是在上位机模拟器调试时，不管bauds设置的是多少，读取bauds始终都会是9600，但是在串口屏实物上是正常的，你设置的多少读出来就是多少</p>
</div>
<section id="bauds-1">
<h2>bauds-示例1<a class="headerlink" href="#bauds-1" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//当前波特率设置为9600</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">bauds</span><span class="o">=</span><span class="mi">9600</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/bauds_1.png" src="../_images/bauds_1.png" />
</section>
<section id="bauds-2">
<h2>bauds-示例2<a class="headerlink" href="#bauds-2" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//默认波特率设置为115200</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">bauds</span><span class="o">=</span><span class="mi">115200</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/bauds_2.png" src="../_images/bauds_2.png" />
</section>
<section id="id1">
<h2>bauds-相关链接<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="../QA/baudrate.html#id1"><span class="std std-ref">如何配置亮度,主动解析,波特率</span></a></p>
<p><a class="reference internal" href="../QA/QA99.html#baudbauds"><span class="std std-ref">baud和bauds的区别</span></a></p>
<p><a class="reference internal" href="../QA/QA57.html#id1"><span class="std std-ref">使用串口下载工程速度慢怎么办</span></a></p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="dim.html" class="btn btn-neutral float-left" title="dim-当前背光亮度值" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="baud.html" class="btn btn-neutral float-right" title="baud-当前波特率值" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>