/**
  ******************************************************************************
  * @file    stm32f4xx_it.c
  * <AUTHOR> Analysis Expert
  * @version V1.0
  * @date    2025-07-17
  * @brief   中断服务程序实现
  ******************************************************************************
  */

#include "stm32f4xx_it.h"

/**
  * @brief  NMI中断处理函数
  * @param  None
  * @retval None
  */
void NMI_Handler(void)
{
}

/**
  * @brief  硬件错误中断处理函数
  * @param  None
  * @retval None
  */
void HardFault_Handler(void)
{
  while (1)
  {
  }
}

/**
  * @brief  内存管理错误中断处理函数
  * @param  None
  * @retval None
  */
void MemManage_Handler(void)
{
  while (1)
  {
  }
}

/**
  * @brief  总线错误中断处理函数
  * @param  None
  * @retval None
  */
void BusFault_Handler(void)
{
  while (1)
  {
  }
}

/**
  * @brief  用法错误中断处理函数
  * @param  None
  * @retval None
  */
void UsageFault_Handler(void)
{
  while (1)
  {
  }
}

/**
  * @brief  SVC中断处理函数
  * @param  None
  * @retval None
  */
void SVC_Handler(void)
{
}

/**
  * @brief  调试监控中断处理函数
  * @param  None
  * @retval None
  */
void DebugMon_Handler(void)
{
}

/**
  * @brief  PendSV中断处理函数
  * @param  None
  * @retval None
  */
void PendSV_Handler(void)
{
}

/**
  * @brief  SysTick中断处理函数（在main.c中实现）
  * @param  None
  * @retval None
  */
// void SysTick_Handler(void) - 在main.c中实现
