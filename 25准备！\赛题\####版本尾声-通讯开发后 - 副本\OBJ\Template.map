Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    main.o(.text) refers to delay.o(.text) for delay_init
    main.o(.text) refers to usart.o(.text) for uart_init
    main.o(.text) refers to motor_control.o(.text) for Motor_System_Init
    main.o(.text) refers to k230_comm.o(.text) for K230_Comm_Init
    stm32f4xx_it.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_GetITStatus
    stm32f4xx_it.o(.text) refers to main.o(.data) for system_tick_ms
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    startup_stm32f40_41xxx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(.text) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(.text) for USART1_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(.text) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(HEAP) for Heap_Mem
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(STACK) for Stack_Mem
    stm32f4xx_adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_can.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_cryp.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_dac.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_dma2d.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_fsmc.o(.text) refers to stm32f4xx_fsmc.o(.constdata) for FSMC_DefaultTimingStruct
    stm32f4xx_gpio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_hash.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_hash_md5.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DeInit
    stm32f4xx_hash_sha1.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DeInit
    stm32f4xx_i2c.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_ltdc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_pwr.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_rcc.o(.text) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_rng.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_sai.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_sdio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_syscfg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_wwdg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    delay.o(.text) refers to misc.o(.text) for SysTick_CLKSourceConfig
    delay.o(.text) refers to delay.o(.data) for fac_us
    usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    usart.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    usart.o(.text) refers to stm32f4xx_usart.o(.text) for USART_Init
    usart.o(.text) refers to misc.o(.text) for NVIC_Init
    usart.o(.text) refers to usart.o(.data) for k230_rx_state
    atd5984.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    atd5984.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    atd5984.o(.text) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    atd5984.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    atd5984.o(.text) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    atd5984.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_ResetBits
    atd5984.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    atd5984.o(.text) refers to noretval__2printf.o(.text) for __2printf
    atd5984.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    atd5984.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    atd5984.o(.text) refers to delay.o(.text) for delay_ms
    atd5984.o(.text) refers to atd5984.o(.data) for current_pwm_frequency
    adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    adc.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    adc.o(.text) refers to stm32f4xx_adc.o(.text) for ADC_CommonInit
    adc.o(.text) refers to delay.o(.text) for delay_ms
    key.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    key.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    tim.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphClockCmd
    tim.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    tim.o(.text) refers to misc.o(.text) for NVIC_Init
    motor_control.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    motor_control.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    motor_control.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    motor_control.o(.text) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    motor_control.o(.text) refers to _printf_str.o(.text) for _printf_str
    motor_control.o(.text) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    motor_control.o(.text) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    motor_control.o(.text) refers to noretval__2printf.o(.text) for __2printf
    motor_control.o(.text) refers to atd5984.o(.text) for ATD5984_Init
    motor_control.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_CCxCmd
    motor_control.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    motor_control.o(.text) refers to motor_control.o(.data) for motor_state
    k230_comm.o(.text) refers to motor_control.o(.text) for Motor_SetSpeed
    k230_comm.o(.text) refers to delay.o(.text) for delay_ms
    k230_comm.o(.text) refers to main.o(.data) for system_tick_ms
    k230_comm.o(.text) refers to k230_comm.o(.bss) for g_k230_pid
    k230_comm.o(.text) refers to usart.o(.data) for k230_frame_ready
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to stdio_streams.o(.bss) for __stdout
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to stdio_streams.o(.bss) for __stdout
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to fputc.o(i.fputc) for fputc
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_char_common.o(.text) refers to __printf_wp.o(.text) for __printf
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fputc.o(i.fputc) refers to flsbuf.o(.text) for __flsbuf_byte
    initio.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000024) for __rt_lib_init_stdio_2
    initio.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) for __rt_lib_shutdown_stdio_2
    initio.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    initio.o(.text) refers to fopen.o(.text) for freopen
    initio.o(.text) refers to defsig_rtred_outer.o(.text) for __rt_SIGRTRED
    initio.o(.text) refers to setvbuf.o(.text) for setvbuf
    initio.o(.text) refers to fclose.o(.text) for _fclose_internal
    initio.o(.text) refers to h1_free.o(.text) for free
    initio.o(.text) refers to stdio_streams.o(.bss) for __stdin
    initio.o(.text) refers to stdio_streams.o(.bss) for __stdout
    initio.o(.text) refers to stdio_streams.o(.bss) for __stderr
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdin
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdout
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stderr
    initio.o(.text) refers to sys_io.o(.constdata) for __stdin_name
    initio.o(.text) refers to sys_io.o(.constdata) for __stdout_name
    initio.o(.text) refers to sys_io.o(.constdata) for __stderr_name
    initio_locked.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000024) for __rt_lib_init_stdio_2
    initio_locked.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) for __rt_lib_shutdown_stdio_2
    initio_locked.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    initio_locked.o(.text) refers to fopen.o(.text) for freopen
    initio_locked.o(.text) refers to defsig_rtred_outer.o(.text) for __rt_SIGRTRED
    initio_locked.o(.text) refers to setvbuf.o(.text) for setvbuf
    initio_locked.o(.text) refers to fclose.o(.text) for _fclose_internal
    initio_locked.o(.text) refers to h1_free.o(.text) for free
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stdout
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stderr
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdin
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdout
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stderr
    initio_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stdin_name
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stdout_name
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stderr_name
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_io.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.text) refers to strlen.o(.text) for strlen
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f40_41xxx.o(.text) for __user_initial_stackheap
    free.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    free.o(.text) refers to heapstubs.o(.text) for __Heap_Free
    h1_free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_free_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._FDIterate) refers to heap2.o(.conststring) for .conststring
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i.___Heap_Stats$realtime) refers to heap2.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(i._FDIterate) for _FDIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(.conststring) for .conststring
    heap2.o(i._free$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._malloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._malloc$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._posix_memalign$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._posix_memalign$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to h1_free.o(.text) for free
    heap2.o(i._realloc$realtime) refers to h1_alloc.o(.text) for malloc
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._realloc$realtime) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    heap2mt.o(i._FDIterate) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i.___Heap_Initialize$realtime$concurrent) refers to mutex_dummy.o(.text) for _mutex_initialize
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i.___Heap_Stats$realtime$concurrent) refers to heap2mt.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(i._FDIterate) for _FDIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i._free$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._malloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._malloc$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_free.o(.text) for free
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_alloc.o(.text) for malloc
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    flsbuf.o(.text) refers to stdio.o(.text) for _deferredlazyseek
    flsbuf.o(.text) refers to sys_io.o(.text) for _sys_flen
    flsbuf.o(.text) refers to h1_alloc.o(.text) for malloc
    streamlock.o(.data) refers (Special) to initio.o(.text) for _initio
    fopen.o(.text) refers to fclose.o(.text) for _fclose_internal
    fopen.o(.text) refers to sys_io.o(.text) for _sys_open
    fopen.o(.text) refers to fseek.o(.text) for _fseek
    fopen.o(.text) refers to h1_alloc.o(.text) for malloc
    fopen.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen.o(.text) refers to stdio_streams.o(.bss) for __stdin
    fclose.o(.text) refers to stdio.o(.text) for _fflush
    fclose.o(.text) refers to sys_io.o(.text) for _sys_close
    fclose.o(.text) refers to h1_free.o(.text) for free
    fclose.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen_locked.o(.text) refers to fclose.o(.text) for _fclose_internal
    fopen_locked.o(.text) refers to sys_io.o(.text) for _sys_open
    fopen_locked.o(.text) refers to fseek.o(.text) for _fseek
    fopen_locked.o(.text) refers to h1_alloc.o(.text) for malloc
    fopen_locked.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    fopen_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_rtred_outer.o(.text) refers to defsig_rtred_inner.o(.text) for __rt_SIGRTRED_inner
    defsig_rtred_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtred_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000005) refers (Weak) to init_alloc.o(.text) for _init_alloc
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000024) refers (Weak) to initio.o(.text) for _initio
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) refers (Weak) to initio.o(.text) for _terminateio
    libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) refers (Weak) to term_alloc.o(.text) for _terminate_alloc
    flsbuf_fwide.o(.text) refers to flsbuf.o(.text) for __flsbuf
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    rt_heap_descriptor.o(.text) refers to rt_heap_descriptor.o(.bss) for __rt_heap_descriptor_data
    rt_heap_descriptor_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    init_alloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    init_alloc.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000005) for __rt_lib_init_heap_2
    init_alloc.o(.text) refers (Special) to maybetermalloc1.o(.emb_text) for _maybe_terminate_alloc
    init_alloc.o(.text) refers to h1_extend.o(.text) for __Heap_ProvideMemory
    init_alloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    init_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    init_alloc.o(.text) refers to h1_init.o(.text) for __Heap_Initialize
    malloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    malloc.o(.text) refers (Special) to init_alloc.o(.text) for _init_alloc
    malloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    malloc.o(.text) refers to heapstubs.o(.text) for __Heap_Alloc
    h1_alloc.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_alloc_mt.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc_mt.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_alloc_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    _printf_char_file_locked.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file_locked.o(.text) refers to fputc.o(i._fputc$unlocked) for _fputc$unlocked
    fseek.o(.text) refers to sys_io.o(.text) for _sys_istty
    fseek.o(.text) refers to ftell.o(.text) for _ftell_internal
    fseek.o(.text) refers to stdio.o(.text) for _seterr
    stdio.o(.text) refers to sys_io.o(.text) for _sys_seek
    fwritefast.o(.text) refers to stdio.o(.text) for _writebuf
    fwritefast.o(.text) refers to flsbuf.o(.text) for __flsbuf_byte
    fwritefast.o(.text) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    fwritefast_locked.o(.text) refers to stdio.o(.text) for _writebuf
    fwritefast_locked.o(.text) refers to flsbuf.o(.text) for __flsbuf_byte
    fwritefast_locked.o(.text) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    assert_stdio.o(.text) refers to fputs.o(.text) for fputs
    assert_stdio.o(.text) refers to fflush.o(.text) for fflush
    assert_stdio.o(.text) refers to stdio_streams.o(.bss) for __stderr
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    maybetermalloc2.o(.emb_text) refers (Special) to term_alloc.o(.text) for _terminate_alloc
    h1_extend.o(.text) refers to h1_free.o(.text) for free
    h1_init_mt.o(.text) refers to mutex_dummy.o(.text) for _mutex_initialize
    h1_extend_mt.o(.text) refers to h1_free_mt.o(.text) for _free_internal
    fflush.o(.text) refers to stdio.o(.text) for _fflush
    fflush.o(.text) refers to fseek.o(.text) for _fseek
    fflush.o(.text) refers to stdio_streams.o(.bss) for __stdin
    fputs.o(.text) refers to fputc.o(i.fputc) for fputc
    ftell.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    fflush_locked.o(.text) refers to stdio.o(.text) for _fflush
    fflush_locked.o(.text) refers to fseek.o(.text) for _fseek
    fflush_locked.o(.text) refers to fflush.o(.text) for _do_fflush
    fflush_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    fflush_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    heapauxa.o(.text) refers to heapauxa.o(.data) for .data
    _get_argv.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv.o(.text) refers to h1_alloc.o(.text) for malloc
    _get_argv.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv.o(.text) refers to sys_command.o(.text) for _sys_command_string
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    term_alloc.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_heap_2
    term_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    term_alloc.o(.text) refers to h1_final.o(.text) for __Heap_Finalize
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig.o(CL$$defsig) refers to defsig_rtred_inner.o(.text) for __rt_SIGRTRED_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.text), (1124 bytes).
    Removing stm32f4xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(.text), (2544 bytes).
    Removing stm32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_crc.o(.text), (72 bytes).
    Removing stm32f4xx_cryp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.text), (856 bytes).
    Removing stm32f4xx_cryp_aes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.text), (4586 bytes).
    Removing stm32f4xx_cryp_des.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.text), (472 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.text), (536 bytes).
    Removing stm32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dac.o(.text), (528 bytes).
    Removing stm32f4xx_dbgmcu.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.text), (100 bytes).
    Removing stm32f4xx_dcmi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.text), (396 bytes).
    Removing stm32f4xx_dma2d.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.text), (948 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma.o(.text), (936 bytes).
    Removing stm32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(.text), (272 bytes).
    Removing stm32f4xx_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash.o(.text), (1684 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.text), (64 bytes).
    Removing stm32f4xx_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.text), (1480 bytes).
    Removing stm32f4xx_fsmc.o(.constdata), (28 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.text), (552 bytes).
    Removing stm32f4xx_hash_md5.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.text), (534 bytes).
    Removing stm32f4xx_hash_sha1.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.text), (548 bytes).
    Removing stm32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.text), (1110 bytes).
    Removing stm32f4xx_iwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.text), (64 bytes).
    Removing stm32f4xx_ltdc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.text), (1672 bytes).
    Removing stm32f4xx_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.text), (364 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rng.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.text), (160 bytes).
    Removing stm32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.text), (3432 bytes).
    Removing stm32f4xx_sai.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sai.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sai.o(.text), (524 bytes).
    Removing stm32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.text), (476 bytes).
    Removing stm32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spi.o(.text), (1152 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.text), (148 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.text), (144 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.emb_text), (16 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.bss), (200 bytes).
    Removing atd5984.o(.rev16_text), (4 bytes).
    Removing atd5984.o(.revsh_text), (4 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.text), (208 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.text), (68 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.text), (86 bytes).
    Removing motor_control.o(.rev16_text), (4 bytes).
    Removing motor_control.o(.revsh_text), (4 bytes).
    Removing k230_comm.o(.rev16_text), (4 bytes).
    Removing k230_comm.o(.revsh_text), (4 bytes).

130 unused section(s) (total 28460 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  mutex_dummy.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_io.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/assert.c                         0x00000000   Number         0  assert_stdio.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final_mt.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2mt.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  fdtree.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  init_alloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  term_alloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  free.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  heapstubs.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxa.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file_locked.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  streamlock.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputs.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  setvbuf.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fflush.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  flsbuf.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputs_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fwritefast_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fwritefast.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  stdio.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fseek.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  initio.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputc_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fopen_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  initio_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  setvbuf_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fclose.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fopen.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fflush_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  stdio_streams.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ftell.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/wchar.c                          0x00000000   Number         0  flsbuf_fwide.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ..\CORE\startup_stm32f40_41xxx.s         0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    ..\FWLIB\src\misc.c                      0x00000000   Number         0  misc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_adc.c             0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_can.c             0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_crc.c             0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp.c            0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_aes.c        0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_des.c        0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_tdes.c       0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dac.c             0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dbgmcu.c          0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dcmi.c            0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma.c             0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma2d.c           0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_exti.c            0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_flash.c           0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_flash_ramfunc.c   0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_fsmc.c            0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_gpio.c            0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash.c            0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash_md5.c        0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash_sha1.c       0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_i2c.c             0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_iwdg.c            0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_ltdc.c            0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_pwr.c             0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rcc.c             0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rng.c             0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rtc.c             0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_sai.c             0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_sdio.c            0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_spi.c             0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_syscfg.c          0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_tim.c             0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_usart.c           0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_wwdg.c            0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\HAREWARE\ADC\adc.c                    0x00000000   Number         0  adc.o ABSOLUTE
    ..\HAREWARE\ATD5984\ATD5984.c            0x00000000   Number         0  atd5984.o ABSOLUTE
    ..\HAREWARE\K230_COMM\k230_comm.c        0x00000000   Number         0  k230_comm.o ABSOLUTE
    ..\HAREWARE\KEY\KEY.c                    0x00000000   Number         0  key.o ABSOLUTE
    ..\HAREWARE\MOTOR_CONTROL\motor_control.c 0x00000000   Number         0  motor_control.o ABSOLUTE
    ..\HAREWARE\TIM\TIM.c                    0x00000000   Number         0  tim.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\FWLIB\\src\\misc.c                   0x00000000   Number         0  misc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_adc.c          0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_can.c          0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_crc.c          0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp.c         0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_aes.c     0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_des.c     0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_tdes.c    0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dac.c          0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dbgmcu.c       0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dcmi.c         0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma.c          0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma2d.c        0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_exti.c         0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_flash.c        0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_fsmc.c         0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_gpio.c         0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash.c         0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash_md5.c     0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash_sha1.c    0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_i2c.c          0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_iwdg.c         0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_ltdc.c         0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_pwr.c          0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rcc.c          0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rng.c          0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rtc.c          0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_sai.c          0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_sdio.c         0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_spi.c          0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_syscfg.c       0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_tim.c          0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_usart.c        0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_wwdg.c         0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\\HAREWARE\\ADC\\adc.c                 0x00000000   Number         0  adc.o ABSOLUTE
    ..\\HAREWARE\\ATD5984\\ATD5984.c         0x00000000   Number         0  atd5984.o ABSOLUTE
    ..\\HAREWARE\\K230_COMM\\k230_comm.c     0x00000000   Number         0  k230_comm.o ABSOLUTE
    ..\\HAREWARE\\KEY\\KEY.c                 0x00000000   Number         0  key.o ABSOLUTE
    ..\\HAREWARE\\MOTOR_CONTROL\\motor_control.c 0x00000000   Number         0  motor_control.o ABSOLUTE
    ..\\HAREWARE\\TIM\\TIM.c                 0x00000000   Number         0  tim.o ABSOLUTE
    ..\\SYSTEM\\delay\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    ..\\SYSTEM\\usart\\usart.c               0x00000000   Number         0  usart.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001fc   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x080001fc   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000009  0x08000202   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$00000014  0x08000208   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x0800020e   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000212   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x08000214   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000218   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000005          0x08000218   Section        8  libinit2.o(.ARM.Collect$$libinit$$00000005)
    .ARM.Collect$$libinit$$0000000A          0x08000220   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000220   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000220   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000220   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000226   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000226   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000226   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000226   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x08000230   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000230   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000230   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000230   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000230   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000230   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000230   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000024          0x08000230   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000024)
    .ARM.Collect$$libinit$$00000025          0x08000234   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000234   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000234   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000234   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000234   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000234   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000236   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000238   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000238   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000005      0x08000238   Section        4  libshutdown2.o(.ARM.Collect$$libshutdown$$00000005)
    .ARM.Collect$$libshutdown$$00000006      0x0800023c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x0800023c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x0800023c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x0800023c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x0800023c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x0800023c   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x0800023e   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0800023e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0800023e   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000244   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000244   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000248   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000248   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000250   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000252   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000252   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000256   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .emb_text                                0x0800025c   Section        0  maybetermalloc1.o(.emb_text)
    .text                                    0x0800025c   Section        0  main.o(.text)
    .text                                    0x080002c0   Section        0  stm32f4xx_it.o(.text)
    .text                                    0x08000300   Section        0  system_stm32f4xx.o(.text)
    SetSysClock                              0x08000301   Thumb Code   220  system_stm32f4xx.o(.text)
    .text                                    0x08000510   Section       64  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x08000510   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x08000550   Section        0  misc.o(.text)
    .text                                    0x08000630   Section        0  stm32f4xx_gpio.o(.text)
    .text                                    0x080008c4   Section        0  stm32f4xx_rcc.o(.text)
    .text                                    0x08000f20   Section        0  stm32f4xx_tim.o(.text)
    TI4_Config                               0x080016bf   Thumb Code    80  stm32f4xx_tim.o(.text)
    TI3_Config                               0x08001721   Thumb Code    72  stm32f4xx_tim.o(.text)
    TI2_Config                               0x08001783   Thumb Code    90  stm32f4xx_tim.o(.text)
    TI1_Config                               0x080017ef   Thumb Code    58  stm32f4xx_tim.o(.text)
    .text                                    0x08001bc4   Section        0  stm32f4xx_usart.o(.text)
    .text                                    0x08002018   Section        0  delay.o(.text)
    .text                                    0x0800211c   Section        0  usart.o(.text)
    .text                                    0x0800226c   Section        0  atd5984.o(.text)
    .text                                    0x0800315c   Section        0  motor_control.o(.text)
    .text                                    0x08003608   Section        0  k230_comm.o(.text)
    get_tick_ms                              0x08003609   Thumb Code     6  k230_comm.o(.text)
    constrain_float                          0x08003685   Thumb Code    42  k230_comm.o(.text)
    constrain_uint16                         0x08003777   Thumb Code    58  k230_comm.o(.text)
    .text                                    0x080039ac   Section        0  noretval__2printf.o(.text)
    .text                                    0x080039c4   Section        0  _printf_str.o(.text)
    .text                                    0x08003a18   Section        0  _printf_dec.o(.text)
    .text                                    0x08003a90   Section        0  __printf_wp.o(.text)
    .text                                    0x08003b9e   Section        0  heapauxi.o(.text)
    .text                                    0x08003ba4   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08003c56   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08003c59   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08004074   Section        0  _printf_char.o(.text)
    .text                                    0x080040a0   Section        0  _printf_char_file.o(.text)
    .text                                    0x080040c4   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x080040cc   Section      138  lludiv10.o(.text)
    .text                                    0x08004158   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08004159   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08004188   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08004208   Section        0  bigflt0.o(.text)
    .text                                    0x080042ec   Section        0  ferror.o(.text)
    .text                                    0x080042f4   Section        0  initio.o(.text)
    .text                                    0x0800442c   Section        0  sys_io.o(.text)
    .text                                    0x08004494   Section        8  libspace.o(.text)
    .text                                    0x0800449c   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080044e6   Section        0  h1_free.o(.text)
    .text                                    0x08004534   Section        0  flsbuf.o(.text)
    .text                                    0x0800470a   Section        0  setvbuf.o(.text)
    .text                                    0x08004750   Section        0  fopen.o(.text)
    _freopen_locked                          0x08004751   Thumb Code     0  fopen.o(.text)
    .text                                    0x0800483c   Section        0  fclose.o(.text)
    .text                                    0x08004888   Section        0  exit.o(.text)
    .text                                    0x0800489a   Section        0  defsig_rtred_outer.o(.text)
    .text                                    0x080048a8   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080048f8   Section      128  strcmpv7m.o(.text)
    .text                                    0x08004978   Section        2  use_no_semi.o(.text)
    .text                                    0x0800497a   Section        0  indicate_semi.o(.text)
    .text                                    0x0800497c   Section        8  rt_heap_descriptor_intlibspace.o(.text)
    .text                                    0x08004984   Section        0  hguard.o(.text)
    .text                                    0x08004988   Section        0  init_alloc.o(.text)
    .text                                    0x08004a12   Section        0  h1_alloc.o(.text)
    .text                                    0x08004a70   Section        0  fseek.o(.text)
    .text                                    0x08004b68   Section        0  stdio.o(.text)
    .text                                    0x08004c58   Section        0  defsig_exit.o(.text)
    .text                                    0x08004c64   Section        0  defsig_rtred_inner.o(.text)
    .text                                    0x08004c98   Section        0  strlen.o(.text)
    .text                                    0x08004cd8   Section        0  sys_exit.o(.text)
    .text                                    0x08004ce4   Section        0  h1_init.o(.text)
    .text                                    0x08004cf2   Section        0  h1_extend.o(.text)
    .text                                    0x08004d26   Section        0  ftell.o(.text)
    .text                                    0x08004d68   Section        0  defsig_general.o(.text)
    .text                                    0x08004d9a   Section        0  defsig_rtmem_outer.o(.text)
    .text                                    0x08004da8   Section        0  sys_wrch.o(.text)
    .text                                    0x08004db8   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08004dc0   Section        0  defsig_rtmem_inner.o(.text)
    CL$$btod_d2e                             0x08004e10   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08004e4e   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08004e94   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08004ef4   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x0800522c   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08005308   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08005332   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x0800535c   Section      580  btod.o(CL$$btod_mult_common)
    i.__ARM_fpclassify                       0x080055a0   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i._is_digit                              0x080055d0   Section        0  __printf_wp.o(i._is_digit)
    i.fputc                                  0x080055de   Section        0  fputc.o(i.fputc)
    locale$$code                             0x080055f8   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$dretinf                            0x08005624   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x08005624   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$f2d                                0x08005630   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x08005630   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x08005686   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x08005686   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x08005712   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08005712   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$printf1                            0x0800571c   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x0800571c   Number         0  printf1.o(x$fpl$printf1)
    .constdata                               0x08005720   Section      148  bigflt0.o(.constdata)
    x$fpl$usenofp                            0x08005720   Section        0  usenofp.o(x$fpl$usenofp)
    tenpwrs_x                                0x08005720   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x0800575c   Data          64  bigflt0.o(.constdata)
    .constdata                               0x080057b4   Section        4  sys_io.o(.constdata)
    .constdata                               0x080057b8   Section        4  sys_io.o(.constdata)
    .constdata                               0x080057bc   Section        4  sys_io.o(.constdata)
    locale$$data                             0x080057e0   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x080057e4   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x080057ec   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x080057f8   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x080057fa   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x080057fb   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x080057fc   Data           0  lc_numeric_c.o(locale$$data)
    .data                                    0x20000000   Section        4  main.o(.data)
    .data                                    0x20000004   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x20000018   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x20000018   Data          16  stm32f4xx_rcc.o(.data)
    .data                                    0x20000028   Section        4  delay.o(.data)
    fac_us                                   0x20000028   Data           1  delay.o(.data)
    fac_ms                                   0x2000002a   Data           2  delay.o(.data)
    .data                                    0x2000002c   Section        9  usart.o(.data)
    .data                                    0x20000036   Section        4  atd5984.o(.data)
    current_pwm_frequency                    0x20000036   Data           2  atd5984.o(.data)
    current_pwm_frequency_motorB             0x20000038   Data           2  atd5984.o(.data)
    .data                                    0x2000003a   Section        2  motor_control.o(.data)
    motor_state                              0x2000003a   Data           2  motor_control.o(.data)
    .data                                    0x2000003c   Section        4  stdio_streams.o(.data)
    .data                                    0x20000040   Section        4  stdio_streams.o(.data)
    .data                                    0x20000044   Section        4  stdio_streams.o(.data)
    .bss                                     0x20000048   Section       48  k230_comm.o(.bss)
    .bss                                     0x20000078   Section       84  stdio_streams.o(.bss)
    .bss                                     0x200000cc   Section       84  stdio_streams.o(.bss)
    .bss                                     0x20000120   Section       84  stdio_streams.o(.bss)
    .bss                                     0x20000174   Section       96  libspace.o(.bss)
    HEAP                                     0x200001d8   Section      512  startup_stm32f40_41xxx.o(HEAP)
    Heap_Mem                                 0x200001d8   Data         512  startup_stm32f40_41xxx.o(HEAP)
    STACK                                    0x200003d8   Section     1024  startup_stm32f40_41xxx.o(STACK)
    Stack_Mem                                0x200003d8   Data        1024  startup_stm32f40_41xxx.o(STACK)
    __initial_sp                             0x200007d8   Data           0  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    __user_heap_extent                        - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_free                               - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x080001fd   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x080001fd   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x08000203   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_s                                0x08000209   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x0800020f   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000213   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000215   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_2                     0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000005)
    __rt_lib_init_preinit_1                  0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_heap_1                     0x08000221   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000221   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_rand_1                     0x08000221   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000221   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000227   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000227   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000227   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000227   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_atexit_1                   0x08000231   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000231   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_fp_trap_1                  0x08000231   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000231   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x08000231   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000231   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_signal_1                   0x08000231   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_2                    0x08000231   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000024)
    __rt_lib_init_alloca_1                   0x08000235   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000235   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_cpp_1                      0x08000235   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000235   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_return                     0x08000235   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_stdio_1                    0x08000235   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000237   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000239   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08000239   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_stdio_2                0x08000239   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000005)
    __rt_lib_shutdown_fp_trap_1              0x0800023d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x0800023d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x0800023d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x0800023d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x0800023d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x0800023d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x0800023f   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0800023f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0800023f   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000245   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000245   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000249   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000249   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000251   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000253   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000253   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000257   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    _maybe_terminate_alloc                   0x0800025d   Thumb Code     0  maybetermalloc1.o(.emb_text)
    main                                     0x0800025d   Thumb Code   100  main.o(.text)
    NMI_Handler                              0x080002c1   Thumb Code     2  stm32f4xx_it.o(.text)
    HardFault_Handler                        0x080002c3   Thumb Code     4  stm32f4xx_it.o(.text)
    MemManage_Handler                        0x080002c7   Thumb Code     4  stm32f4xx_it.o(.text)
    BusFault_Handler                         0x080002cb   Thumb Code     4  stm32f4xx_it.o(.text)
    UsageFault_Handler                       0x080002cf   Thumb Code     4  stm32f4xx_it.o(.text)
    SVC_Handler                              0x080002d3   Thumb Code     2  stm32f4xx_it.o(.text)
    DebugMon_Handler                         0x080002d5   Thumb Code     2  stm32f4xx_it.o(.text)
    PendSV_Handler                           0x080002d7   Thumb Code     2  stm32f4xx_it.o(.text)
    SysTick_Handler                          0x080002d9   Thumb Code    12  stm32f4xx_it.o(.text)
    TIM2_IRQHandler                          0x080002e5   Thumb Code    22  stm32f4xx_it.o(.text)
    SystemInit                               0x080003dd   Thumb Code    88  system_stm32f4xx.o(.text)
    SystemCoreClockUpdate                    0x08000435   Thumb Code   174  system_stm32f4xx.o(.text)
    Reset_Handler                            0x08000511   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream0_IRQHandler                  0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream2_IRQHandler                  0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI0_IRQHandler                         0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM3_IRQHandler                          0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM4_IRQHandler                          0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM5_IRQHandler                          0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM6_DAC_IRQHandler                      0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART2_IRQHandler                        0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART3_IRQHandler                        0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x0800052b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __user_initial_stackheap                 0x0800052d   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    NVIC_PriorityGroupConfig                 0x08000551   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x0800055b   Thumb Code   106  misc.o(.text)
    NVIC_SetVectorTable                      0x080005c5   Thumb Code    14  misc.o(.text)
    NVIC_SystemLPConfig                      0x080005d3   Thumb Code    34  misc.o(.text)
    SysTick_CLKSourceConfig                  0x080005f5   Thumb Code    40  misc.o(.text)
    GPIO_DeInit                              0x08000631   Thumb Code   268  stm32f4xx_gpio.o(.text)
    GPIO_Init                                0x0800073d   Thumb Code   144  stm32f4xx_gpio.o(.text)
    GPIO_StructInit                          0x080007cd   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_PinLockConfig                       0x080007df   Thumb Code    34  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x08000801   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputData                       0x08000813   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x0800081b   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputData                      0x0800082d   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_SetBits                             0x08000835   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ResetBits                           0x08000839   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_WriteBit                            0x0800083d   Thumb Code    10  stm32f4xx_gpio.o(.text)
    GPIO_Write                               0x08000847   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ToggleBits                          0x0800084b   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_PinAFConfig                         0x08000853   Thumb Code    70  stm32f4xx_gpio.o(.text)
    RCC_DeInit                               0x080008c5   Thumb Code    82  stm32f4xx_rcc.o(.text)
    RCC_HSEConfig                            0x08000917   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_GetFlagStatus                        0x08000925   Thumb Code    60  stm32f4xx_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x08000961   Thumb Code    56  stm32f4xx_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x08000999   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_HSICmd                               0x080009ad   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_LSEConfig                            0x080009b3   Thumb Code    46  stm32f4xx_rcc.o(.text)
    RCC_LSICmd                               0x080009e1   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLConfig                            0x080009e7   Thumb Code    32  stm32f4xx_rcc.o(.text)
    RCC_PLLCmd                               0x08000a07   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SConfig                         0x08000a0d   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SCmd                            0x08000a1b   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLSAIConfig                         0x08000a21   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PLLSAICmd                            0x08000a35   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x08000a3b   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_MCO1Config                           0x08000a41   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_MCO2Config                           0x08000a5d   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SYSCLKConfig                         0x08000a79   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x08000a8d   Thumb Code    12  stm32f4xx_rcc.o(.text)
    RCC_HCLKConfig                           0x08000a99   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK1Config                          0x08000aad   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK2Config                          0x08000ac1   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_GetClocksFreq                        0x08000ad7   Thumb Code   222  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKConfig                         0x08000bb5   Thumb Code    54  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKCmd                            0x08000beb   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_BackupResetCmd                       0x08000bf3   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_I2SCLKConfig                         0x08000bfb   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLI2SClkDivConfig                0x08000c01   Thumb Code    26  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLSAIClkDivConfig                0x08000c1b   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockACLKConfig                   0x08000c37   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockBCLKConfig                   0x08000c4b   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_LTDCCLKDivConfig                     0x08000c5f   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_TIMCLKPresConfig                     0x08000c73   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockCmd                   0x08000c79   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockCmd                   0x08000c9b   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockCmd                   0x08000ce9   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x08000d0b   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x08000d2d   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphResetCmd                   0x08000d4f   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphResetCmd                   0x08000d71   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphResetCmd                   0x08000d93   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x08000db5   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x08000dd7   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockLPModeCmd             0x08000df9   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockLPModeCmd             0x08000e1b   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockLPModeCmd             0x08000e3d   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockLPModeCmd             0x08000e5f   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockLPModeCmd             0x08000e81   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_LSEModeConfig                        0x08000ea3   Thumb Code    40  stm32f4xx_rcc.o(.text)
    RCC_ITConfig                             0x08000ecb   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_ClearFlag                            0x08000eed   Thumb Code    18  stm32f4xx_rcc.o(.text)
    RCC_GetITStatus                          0x08000eff   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_ClearITPendingBit                    0x08000f15   Thumb Code     8  stm32f4xx_rcc.o(.text)
    TIM_DeInit                               0x08000f21   Thumb Code   346  stm32f4xx_tim.o(.text)
    TIM_TimeBaseInit                         0x0800107b   Thumb Code   104  stm32f4xx_tim.o(.text)
    TIM_TimeBaseStructInit                   0x080010e3   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PrescalerConfig                      0x080010f5   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_CounterModeConfig                    0x080010fb   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetCounter                           0x0800110d   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetAutoreload                        0x08001111   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetCounter                           0x08001115   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetPrescaler                         0x0800111b   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_UpdateDisableConfig                  0x08001121   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_UpdateRequestConfig                  0x08001139   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ARRPreloadConfig                     0x08001151   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_SelectOnePulseMode                   0x08001169   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetClockDivision                     0x0800117b   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_Cmd                                  0x0800118d   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_OC1Init                              0x080011a5   Thumb Code   114  stm32f4xx_tim.o(.text)
    TIM_OC2Init                              0x08001217   Thumb Code   154  stm32f4xx_tim.o(.text)
    TIM_OC3Init                              0x080012b1   Thumb Code   204  stm32f4xx_tim.o(.text)
    TIM_OC4Init                              0x0800137d   Thumb Code   112  stm32f4xx_tim.o(.text)
    TIM_OCStructInit                         0x080013ed   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_SelectOCxM                           0x08001401   Thumb Code    86  stm32f4xx_tim.o(.text)
    TIM_SetCompare1                          0x08001457   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare2                          0x0800145b   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare3                          0x0800145f   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare4                          0x08001463   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_ForcedOC1Config                      0x08001467   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ForcedOC2Config                      0x08001479   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_ForcedOC3Config                      0x08001493   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ForcedOC4Config                      0x080014a5   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC1PreloadConfig                     0x080014bf   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2PreloadConfig                     0x080014d1   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3PreloadConfig                     0x080014eb   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC4PreloadConfig                     0x080014fd   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC1FastConfig                        0x08001517   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2FastConfig                        0x08001529   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3FastConfig                        0x08001543   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC4FastConfig                        0x08001555   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_ClearOC1Ref                          0x0800156f   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearOC2Ref                          0x08001581   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ClearOC3Ref                          0x08001599   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearOC4Ref                          0x080015ab   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_OC1PolarityConfig                    0x080015c3   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC1NPolarityConfig                   0x080015d5   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2PolarityConfig                    0x080015e7   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC2NPolarityConfig                   0x08001601   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3PolarityConfig                    0x0800161b   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3NPolarityConfig                   0x08001635   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC4PolarityConfig                    0x0800164f   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_CCxCmd                               0x08001669   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_CCxNCmd                              0x08001687   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_SetIC4Prescaler                      0x080016a5   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_SetIC3Prescaler                      0x0800170f   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetIC2Prescaler                      0x08001769   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_SetIC1Prescaler                      0x080017dd   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ICInit                               0x08001829   Thumb Code   110  stm32f4xx_tim.o(.text)
    TIM_ICStructInit                         0x08001897   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PWMIConfig                           0x080018a9   Thumb Code   124  stm32f4xx_tim.o(.text)
    TIM_GetCapture1                          0x08001925   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture2                          0x0800192b   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture3                          0x08001931   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture4                          0x08001937   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_BDTRConfig                           0x0800193d   Thumb Code    32  stm32f4xx_tim.o(.text)
    TIM_BDTRStructInit                       0x0800195d   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_CtrlPWMOutputs                       0x0800196f   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_SelectCOM                            0x0800198d   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_CCPreloadControl                     0x080019a5   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ITConfig                             0x080019bd   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_GenerateEvent                        0x080019cf   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetFlagStatus                        0x080019d3   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearFlag                            0x080019e5   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetITStatus                          0x080019eb   Thumb Code    34  stm32f4xx_tim.o(.text)
    TIM_ClearITPendingBit                    0x08001a0d   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_DMAConfig                            0x08001a13   Thumb Code    10  stm32f4xx_tim.o(.text)
    TIM_DMACmd                               0x08001a1d   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectCCDMA                          0x08001a2f   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_InternalClockConfig                  0x08001a47   Thumb Code    12  stm32f4xx_tim.o(.text)
    TIM_SelectInputTrigger                   0x08001a53   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ITRxExternalClockConfig              0x08001a65   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_TIxExternalClockConfig               0x08001a7d   Thumb Code    62  stm32f4xx_tim.o(.text)
    TIM_ETRConfig                            0x08001abb   Thumb Code    28  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode1Config                  0x08001ad7   Thumb Code    54  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode2Config                  0x08001b0d   Thumb Code    32  stm32f4xx_tim.o(.text)
    TIM_SelectOutputTrigger                  0x08001b2d   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectSlaveMode                      0x08001b3f   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectMasterSlaveMode                0x08001b51   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_EncoderInterfaceConfig               0x08001b63   Thumb Code    66  stm32f4xx_tim.o(.text)
    TIM_SelectHallSensor                     0x08001ba5   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_RemapConfig                          0x08001bbd   Thumb Code     6  stm32f4xx_tim.o(.text)
    USART_DeInit                             0x08001bc5   Thumb Code   206  stm32f4xx_usart.o(.text)
    USART_Init                               0x08001c93   Thumb Code   204  stm32f4xx_usart.o(.text)
    USART_StructInit                         0x08001d5f   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_ClockInit                          0x08001d77   Thumb Code    32  stm32f4xx_usart.o(.text)
    USART_ClockStructInit                    0x08001d97   Thumb Code    12  stm32f4xx_usart.o(.text)
    USART_Cmd                                0x08001da3   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetPrescaler                       0x08001dbb   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_OverSampling8Cmd                   0x08001dcb   Thumb Code    22  stm32f4xx_usart.o(.text)
    USART_OneBitMethodCmd                    0x08001de1   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendData                           0x08001df9   Thumb Code     8  stm32f4xx_usart.o(.text)
    USART_ReceiveData                        0x08001e01   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_SetAddress                         0x08001e0b   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x08001e1d   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_WakeUpConfig                       0x08001e35   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x08001e47   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINCmd                             0x08001e59   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendBreak                          0x08001e71   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_HalfDuplexCmd                      0x08001e7b   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetGuardTime                       0x08001e93   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_SmartCardCmd                       0x08001ea3   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SmartCardNACKCmd                   0x08001ebb   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_IrDAConfig                         0x08001ed3   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_IrDACmd                            0x08001ee5   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_DMACmd                             0x08001efd   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ITConfig                           0x08001f0f   Thumb Code    74  stm32f4xx_usart.o(.text)
    USART_GetFlagStatus                      0x08001f59   Thumb Code    26  stm32f4xx_usart.o(.text)
    USART_ClearFlag                          0x08001f73   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_GetITStatus                        0x08001f85   Thumb Code   118  stm32f4xx_usart.o(.text)
    USART_ClearITPendingBit                  0x08001ffb   Thumb Code    30  stm32f4xx_usart.o(.text)
    delay_init                               0x08002019   Thumb Code    52  delay.o(.text)
    delay_us                                 0x0800204d   Thumb Code    72  delay.o(.text)
    delay_xms                                0x08002095   Thumb Code    72  delay.o(.text)
    delay_ms                                 0x080020dd   Thumb Code    56  delay.o(.text)
    uart_init                                0x0800211d   Thumb Code   164  usart.o(.text)
    K230_ParseByte                           0x080021c1   Thumb Code   118  usart.o(.text)
    USART1_IRQHandler                        0x08002237   Thumb Code    30  usart.o(.text)
    Motor_B_Disable                          0x0800226d   Thumb Code    14  atd5984.o(.text)
    ATD5984_Init                             0x0800227b   Thumb Code   180  atd5984.o(.text)
    STEP12_PWM_Init                          0x0800232f   Thumb Code   172  atd5984.o(.text)
    Motor_A_Rotate                           0x080023db   Thumb Code   264  atd5984.o(.text)
    Motor_A_DirectionTest                    0x080024e3   Thumb Code   188  atd5984.o(.text)
    Motor_A_StepsCalibration                 0x0800259f   Thumb Code  1154  atd5984.o(.text)
    STEP_B_PWM_Init                          0x08002a21   Thumb Code   192  atd5984.o(.text)
    TIM8_SetFrequency                        0x08002ae1   Thumb Code   158  atd5984.o(.text)
    TIM8_GetCurrentFrequency                 0x08002b7f   Thumb Code     6  atd5984.o(.text)
    TIM1_SetFrequency                        0x08002b85   Thumb Code   144  atd5984.o(.text)
    TIM1_GetCurrentFrequency                 0x08002c15   Thumb Code     6  atd5984.o(.text)
    Motor_B_Rotate                           0x08002c1b   Thumb Code  1220  atd5984.o(.text)
    Motor_B_Enable                           0x080030df   Thumb Code    14  atd5984.o(.text)
    Motor_A_Enable                           0x080030ed   Thumb Code    12  atd5984.o(.text)
    Motor_A_Disable                          0x080030f9   Thumb Code    12  atd5984.o(.text)
    Motor_System_Init                        0x0800315d   Thumb Code    50  motor_control.o(.text)
    Motor_Enable                             0x0800318f   Thumb Code    52  motor_control.o(.text)
    Motor_Stop                               0x080031c3   Thumb Code    64  motor_control.o(.text)
    Motor_Disable                            0x08003203   Thumb Code    58  motor_control.o(.text)
    Motor_SetSpeed                           0x0800323d   Thumb Code    98  motor_control.o(.text)
    Motor_Rotate                             0x0800329f   Thumb Code   130  motor_control.o(.text)
    Motor_Emergency_Stop                     0x08003321   Thumb Code    52  motor_control.o(.text)
    Motor_GetState                           0x08003355   Thumb Code    16  motor_control.o(.text)
    Motor_GetSpeed                           0x08003365   Thumb Code    26  motor_control.o(.text)
    Motor_IsMoving                           0x0800337f   Thumb Code    26  motor_control.o(.text)
    K230_Comm_Init                           0x0800360f   Thumb Code   102  k230_comm.o(.text)
    K230_PID_SetParams                       0x08003675   Thumb Code    16  k230_comm.o(.text)
    K230_PID_Calculate                       0x080036af   Thumb Code   180  k230_comm.o(.text)
    K230_PID_Reset                           0x08003763   Thumb Code    20  k230_comm.o(.text)
    K230_Offset_To_Speed                     0x080037b1   Thumb Code   120  k230_comm.o(.text)
    K230_Execute_Command                     0x08003829   Thumb Code   164  k230_comm.o(.text)
    K230_Process_Frame                       0x080038cd   Thumb Code    74  k230_comm.o(.text)
    K230_Check_Timeout                       0x08003917   Thumb Code    56  k230_comm.o(.text)
    K230_Get_CommStatus                      0x0800394f   Thumb Code    30  k230_comm.o(.text)
    K230_Get_LastOffset                      0x0800396d   Thumb Code     8  k230_comm.o(.text)
    K230_Get_PIDOutput                       0x08003975   Thumb Code     8  k230_comm.o(.text)
    __2printf                                0x080039ad   Thumb Code    20  noretval__2printf.o(.text)
    _printf_str                              0x080039c5   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08003a19   Thumb Code   104  _printf_dec.o(.text)
    __printf                                 0x08003a91   Thumb Code   270  __printf_wp.o(.text)
    __use_two_region_memory                  0x08003b9f   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow                         0x08003ba1   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand                         0x08003ba3   Thumb Code     2  heapauxi.o(.text)
    _printf_int_common                       0x08003ba5   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x08003c57   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08003e09   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_cs_common                        0x08004075   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08004089   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08004099   Thumb Code     8  _printf_char.o(.text)
    _printf_char_file                        0x080040a1   Thumb Code    32  _printf_char_file.o(.text)
    __rt_locale                              0x080040c5   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _ll_udiv10                               0x080040cd   Thumb Code   138  lludiv10.o(.text)
    _printf_char_common                      0x08004163   Thumb Code    32  _printf_char_common.o(.text)
    _printf_fp_infnan                        0x08004189   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08004209   Thumb Code   224  bigflt0.o(.text)
    ferror                                   0x080042ed   Thumb Code     8  ferror.o(.text)
    _initio                                  0x080042f5   Thumb Code   210  initio.o(.text)
    _terminateio                             0x080043c7   Thumb Code    56  initio.o(.text)
    _sys_open                                0x0800442d   Thumb Code    20  sys_io.o(.text)
    _sys_close                               0x08004441   Thumb Code    12  sys_io.o(.text)
    _sys_write                               0x0800444d   Thumb Code    16  sys_io.o(.text)
    _sys_read                                0x0800445d   Thumb Code    14  sys_io.o(.text)
    _sys_istty                               0x0800446b   Thumb Code    12  sys_io.o(.text)
    _sys_seek                                0x08004477   Thumb Code    14  sys_io.o(.text)
    _sys_ensure                              0x08004485   Thumb Code     2  sys_io.o(.text)
    _sys_flen                                0x08004487   Thumb Code    12  sys_io.o(.text)
    __user_libspace                          0x08004495   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08004495   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08004495   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x0800449d   Thumb Code    74  sys_stackheap_outer.o(.text)
    free                                     0x080044e7   Thumb Code    78  h1_free.o(.text)
    __flsbuf                                 0x08004535   Thumb Code   470  flsbuf.o(.text)
    __flsbuf_byte                            0x08004535   Thumb Code     0  flsbuf.o(.text)
    __flsbuf_wide                            0x08004535   Thumb Code     0  flsbuf.o(.text)
    setvbuf                                  0x0800470b   Thumb Code    70  setvbuf.o(.text)
    freopen                                  0x08004751   Thumb Code   158  fopen.o(.text)
    fopen                                    0x080047ef   Thumb Code    74  fopen.o(.text)
    _fclose_internal                         0x0800483d   Thumb Code    76  fclose.o(.text)
    fclose                                   0x0800483d   Thumb Code     0  fclose.o(.text)
    exit                                     0x08004889   Thumb Code    18  exit.o(.text)
    __rt_SIGRTRED                            0x0800489b   Thumb Code    14  defsig_rtred_outer.o(.text)
    __aeabi_memclr4                          0x080048a9   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080048a9   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080048a9   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x080048ad   Thumb Code     0  rt_memclr_w.o(.text)
    strcmp                                   0x080048f9   Thumb Code   128  strcmpv7m.o(.text)
    __I$use$semihosting                      0x08004979   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08004979   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x0800497b   Thumb Code     0  indicate_semi.o(.text)
    __rt_heap_descriptor                     0x0800497d   Thumb Code     8  rt_heap_descriptor_intlibspace.o(.text)
    __use_no_heap                            0x08004985   Thumb Code     2  hguard.o(.text)
    __heap$guard                             0x08004987   Thumb Code     2  hguard.o(.text)
    _terminate_user_alloc                    0x08004989   Thumb Code     2  init_alloc.o(.text)
    _init_user_alloc                         0x0800498b   Thumb Code     2  init_alloc.o(.text)
    __Heap_Full                              0x0800498d   Thumb Code    34  init_alloc.o(.text)
    __Heap_Broken                            0x080049af   Thumb Code     6  init_alloc.o(.text)
    _init_alloc                              0x080049b5   Thumb Code    94  init_alloc.o(.text)
    malloc                                   0x08004a13   Thumb Code    94  h1_alloc.o(.text)
    _fseek                                   0x08004a71   Thumb Code   242  fseek.o(.text)
    fseek                                    0x08004a71   Thumb Code     0  fseek.o(.text)
    _seterr                                  0x08004b69   Thumb Code    20  stdio.o(.text)
    _writebuf                                0x08004b7d   Thumb Code    84  stdio.o(.text)
    _fflush                                  0x08004bd1   Thumb Code    70  stdio.o(.text)
    _deferredlazyseek                        0x08004c17   Thumb Code    60  stdio.o(.text)
    __sig_exit                               0x08004c59   Thumb Code    10  defsig_exit.o(.text)
    __rt_SIGRTRED_inner                      0x08004c65   Thumb Code    14  defsig_rtred_inner.o(.text)
    strlen                                   0x08004c99   Thumb Code    62  strlen.o(.text)
    _sys_exit                                0x08004cd9   Thumb Code     8  sys_exit.o(.text)
    __Heap_Initialize                        0x08004ce5   Thumb Code    10  h1_init.o(.text)
    __Heap_DescSize                          0x08004cef   Thumb Code     4  h1_init.o(.text)
    __Heap_ProvideMemory                     0x08004cf3   Thumb Code    52  h1_extend.o(.text)
    _ftell_internal                          0x08004d27   Thumb Code    66  ftell.o(.text)
    ftell                                    0x08004d27   Thumb Code     0  ftell.o(.text)
    __default_signal_display                 0x08004d69   Thumb Code    50  defsig_general.o(.text)
    __rt_SIGRTMEM                            0x08004d9b   Thumb Code    14  defsig_rtmem_outer.o(.text)
    _ttywrch                                 0x08004da9   Thumb Code    14  sys_wrch.o(.text)
    __aeabi_errno_addr                       0x08004db9   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08004db9   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08004db9   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_SIGRTMEM_inner                      0x08004dc1   Thumb Code    22  defsig_rtmem_inner.o(.text)
    _btod_d2e                                0x08004e11   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08004e4f   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08004e95   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08004ef5   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x0800522d   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08005309   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08005333   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x0800535d   Thumb Code   580  btod.o(CL$$btod_mult_common)
    __ARM_fpclassify                         0x080055a1   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    _is_digit                                0x080055d1   Thumb Code    14  __printf_wp.o(i._is_digit)
    fputc                                    0x080055df   Thumb Code    26  fputc.o(i.fputc)
    _get_lc_numeric                          0x080055f9   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __fpl_dretinf                            0x08005625   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_f2d                              0x08005631   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08005631   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x08005687   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x08005713   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x0800571b   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x0800571b   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    _printf_fp_dec                           0x0800571d   Thumb Code     4  printf1.o(x$fpl$printf1)
    __I$use$fp                               0x08005720   Number         0  usenofp.o(x$fpl$usenofp)
    __stdin_name                             0x080057b4   Data           4  sys_io.o(.constdata)
    __stdout_name                            0x080057b8   Data           4  sys_io.o(.constdata)
    __stderr_name                            0x080057bc   Data           4  sys_io.o(.constdata)
    Region$$Table$$Base                      0x080057c0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080057e0   Number         0  anon$$obj.o(Region$$Table)
    system_tick_ms                           0x20000000   Data           4  main.o(.data)
    SystemCoreClock                          0x20000004   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x20000008   Data          16  system_stm32f4xx.o(.data)
    k230_rx_state                            0x2000002c   Data           1  usart.o(.data)
    k230_rx_buffer                           0x2000002d   Data           6  usart.o(.data)
    k230_rx_index                            0x20000033   Data           1  usart.o(.data)
    k230_frame_ready                         0x20000034   Data           1  usart.o(.data)
    __aeabi_stdin                            0x2000003c   Data           4  stdio_streams.o(.data)
    __aeabi_stdout                           0x20000040   Data           4  stdio_streams.o(.data)
    __aeabi_stderr                           0x20000044   Data           4  stdio_streams.o(.data)
    g_k230_pid                               0x20000048   Data          28  k230_comm.o(.bss)
    g_k230_control                           0x20000064   Data          20  k230_comm.o(.bss)
    __stdin                                  0x20000078   Data          84  stdio_streams.o(.bss)
    __stdout                                 0x200000cc   Data          84  stdio_streams.o(.bss)
    __stderr                                 0x20000120   Data          84  stdio_streams.o(.bss)
    __libspace_start                         0x20000174   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200001d4   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00005844, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000057fc, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO          253    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000008   Code   RO         1273  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         1682    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         1684    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         1686    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000000   Code   RO         1270    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001fc   0x080001fc   0x00000006   Code   RO         1269    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000202   0x08000202   0x00000006   Code   RO         1268    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000208   0x08000208   0x00000006   Code   RO         1267    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x0800020e   0x0800020e   0x00000004   Code   RO         1288    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000212   0x08000212   0x00000002   Code   RO         1470    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000214   0x08000214   0x00000004   Code   RO         1471    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000218   0x08000218   0x00000000   Code   RO         1474    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000218   0x08000218   0x00000008   Code   RO         1475    .ARM.Collect$$libinit$$00000005  c_w.l(libinit2.o)
    0x08000220   0x08000220   0x00000000   Code   RO         1477    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000220   0x08000220   0x00000000   Code   RO         1479    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000220   0x08000220   0x00000000   Code   RO         1481    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000220   0x08000220   0x00000006   Code   RO         1482    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000226   0x08000226   0x00000000   Code   RO         1484    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000226   0x08000226   0x00000000   Code   RO         1486    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000226   0x08000226   0x00000000   Code   RO         1488    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000226   0x08000226   0x0000000a   Code   RO         1489    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x08000230   0x08000230   0x00000000   Code   RO         1490    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000230   0x08000230   0x00000000   Code   RO         1492    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000230   0x08000230   0x00000000   Code   RO         1494    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000230   0x08000230   0x00000000   Code   RO         1496    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000230   0x08000230   0x00000000   Code   RO         1498    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000230   0x08000230   0x00000000   Code   RO         1500    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000230   0x08000230   0x00000000   Code   RO         1502    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000230   0x08000230   0x00000004   Code   RO         1503    .ARM.Collect$$libinit$$00000024  c_w.l(libinit2.o)
    0x08000234   0x08000234   0x00000000   Code   RO         1504    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000234   0x08000234   0x00000000   Code   RO         1508    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000234   0x08000234   0x00000000   Code   RO         1510    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000234   0x08000234   0x00000000   Code   RO         1512    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000234   0x08000234   0x00000000   Code   RO         1514    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000234   0x08000234   0x00000002   Code   RO         1515    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000002   Code   RO         1661    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000238   0x08000238   0x00000000   Code   RO         1517    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000238   0x08000238   0x00000000   Code   RO         1519    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000238   0x08000238   0x00000004   Code   RO         1520    .ARM.Collect$$libshutdown$$00000005  c_w.l(libshutdown2.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         1521    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         1524    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         1527    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         1529    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         1532    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x0800023c   0x0800023c   0x00000002   Code   RO         1533    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x0800023e   0x0800023e   0x00000000   Code   RO         1279    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x0800023e   0x0800023e   0x00000000   Code   RO         1301    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x0800023e   0x0800023e   0x00000006   Code   RO         1313    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000244   0x08000244   0x00000000   Code   RO         1303    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000244   0x08000244   0x00000004   Code   RO         1304    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         1306    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000248   0x08000248   0x00000008   Code   RO         1307    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000250   0x08000250   0x00000002   Code   RO         1539    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000252   0x08000252   0x00000000   Code   RO         1597    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000252   0x08000252   0x00000004   Code   RO         1598    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000256   0x08000256   0x00000006   Code   RO         1599    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x0800025c   0x0800025c   0x00000000   Code   RO         1603    .emb_text           c_w.l(maybetermalloc1.o)
    0x0800025c   0x0800025c   0x00000064   Code   RO            3    .text               main.o
    0x080002c0   0x080002c0   0x00000040   Code   RO          170    .text               stm32f4xx_it.o
    0x08000300   0x08000300   0x00000210   Code   RO          227    .text               system_stm32f4xx.o
    0x08000510   0x08000510   0x00000040   Code   RO          254    .text               startup_stm32f40_41xxx.o
    0x08000550   0x08000550   0x000000e0   Code   RO          260    .text               misc.o
    0x08000630   0x08000630   0x00000294   Code   RO          609    .text               stm32f4xx_gpio.o
    0x080008c4   0x080008c4   0x0000065c   Code   RO          772    .text               stm32f4xx_rcc.o
    0x08000f20   0x08000f20   0x00000ca2   Code   RO          917    .text               stm32f4xx_tim.o
    0x08001bc2   0x08001bc2   0x00000002   PAD
    0x08001bc4   0x08001bc4   0x00000454   Code   RO          937    .text               stm32f4xx_usart.o
    0x08002018   0x08002018   0x00000104   Code   RO          977    .text               delay.o
    0x0800211c   0x0800211c   0x00000150   Code   RO         1017    .text               usart.o
    0x0800226c   0x0800226c   0x00000ef0   Code   RO         1045    .text               atd5984.o
    0x0800315c   0x0800315c   0x000004ac   Code   RO         1160    .text               motor_control.o
    0x08003608   0x08003608   0x000003a4   Code   RO         1188    .text               k230_comm.o
    0x080039ac   0x080039ac   0x00000018   Code   RO         1239    .text               c_w.l(noretval__2printf.o)
    0x080039c4   0x080039c4   0x00000052   Code   RO         1243    .text               c_w.l(_printf_str.o)
    0x08003a16   0x08003a16   0x00000002   PAD
    0x08003a18   0x08003a18   0x00000078   Code   RO         1245    .text               c_w.l(_printf_dec.o)
    0x08003a90   0x08003a90   0x0000010e   Code   RO         1255    .text               c_w.l(__printf_wp.o)
    0x08003b9e   0x08003b9e   0x00000006   Code   RO         1271    .text               c_w.l(heapauxi.o)
    0x08003ba4   0x08003ba4   0x000000b2   Code   RO         1280    .text               c_w.l(_printf_intcommon.o)
    0x08003c56   0x08003c56   0x0000041e   Code   RO         1282    .text               c_w.l(_printf_fp_dec.o)
    0x08004074   0x08004074   0x0000002c   Code   RO         1284    .text               c_w.l(_printf_char.o)
    0x080040a0   0x080040a0   0x00000024   Code   RO         1286    .text               c_w.l(_printf_char_file.o)
    0x080040c4   0x080040c4   0x00000008   Code   RO         1318    .text               c_w.l(rt_locale_intlibspace.o)
    0x080040cc   0x080040cc   0x0000008a   Code   RO         1320    .text               c_w.l(lludiv10.o)
    0x08004156   0x08004156   0x00000002   PAD
    0x08004158   0x08004158   0x00000030   Code   RO         1322    .text               c_w.l(_printf_char_common.o)
    0x08004188   0x08004188   0x00000080   Code   RO         1324    .text               c_w.l(_printf_fp_infnan.o)
    0x08004208   0x08004208   0x000000e4   Code   RO         1326    .text               c_w.l(bigflt0.o)
    0x080042ec   0x080042ec   0x00000008   Code   RO         1351    .text               c_w.l(ferror.o)
    0x080042f4   0x080042f4   0x00000138   Code   RO         1357    .text               c_w.l(initio.o)
    0x0800442c   0x0800442c   0x00000066   Code   RO         1372    .text               c_w.l(sys_io.o)
    0x08004492   0x08004492   0x00000002   PAD
    0x08004494   0x08004494   0x00000008   Code   RO         1377    .text               c_w.l(libspace.o)
    0x0800449c   0x0800449c   0x0000004a   Code   RO         1382    .text               c_w.l(sys_stackheap_outer.o)
    0x080044e6   0x080044e6   0x0000004e   Code   RO         1386    .text               c_w.l(h1_free.o)
    0x08004534   0x08004534   0x000001d6   Code   RO         1442    .text               c_w.l(flsbuf.o)
    0x0800470a   0x0800470a   0x00000046   Code   RO         1444    .text               c_w.l(setvbuf.o)
    0x08004750   0x08004750   0x000000ec   Code   RO         1447    .text               c_w.l(fopen.o)
    0x0800483c   0x0800483c   0x0000004c   Code   RO         1449    .text               c_w.l(fclose.o)
    0x08004888   0x08004888   0x00000012   Code   RO         1455    .text               c_w.l(exit.o)
    0x0800489a   0x0800489a   0x0000000e   Code   RO         1457    .text               c_w.l(defsig_rtred_outer.o)
    0x080048a8   0x080048a8   0x0000004e   Code   RO         1461    .text               c_w.l(rt_memclr_w.o)
    0x080048f6   0x080048f6   0x00000002   PAD
    0x080048f8   0x080048f8   0x00000080   Code   RO         1463    .text               c_w.l(strcmpv7m.o)
    0x08004978   0x08004978   0x00000002   Code   RO         1536    .text               c_w.l(use_no_semi.o)
    0x0800497a   0x0800497a   0x00000000   Code   RO         1538    .text               c_w.l(indicate_semi.o)
    0x0800497a   0x0800497a   0x00000002   PAD
    0x0800497c   0x0800497c   0x00000008   Code   RO         1546    .text               c_w.l(rt_heap_descriptor_intlibspace.o)
    0x08004984   0x08004984   0x00000004   Code   RO         1548    .text               c_w.l(hguard.o)
    0x08004988   0x08004988   0x0000008a   Code   RO         1550    .text               c_w.l(init_alloc.o)
    0x08004a12   0x08004a12   0x0000005e   Code   RO         1556    .text               c_w.l(h1_alloc.o)
    0x08004a70   0x08004a70   0x000000f8   Code   RO         1572    .text               c_w.l(fseek.o)
    0x08004b68   0x08004b68   0x000000f0   Code   RO         1574    .text               c_w.l(stdio.o)
    0x08004c58   0x08004c58   0x0000000a   Code   RO         1580    .text               c_w.l(defsig_exit.o)
    0x08004c62   0x08004c62   0x00000002   PAD
    0x08004c64   0x08004c64   0x00000034   Code   RO         1582    .text               c_w.l(defsig_rtred_inner.o)
    0x08004c98   0x08004c98   0x0000003e   Code   RO         1586    .text               c_w.l(strlen.o)
    0x08004cd6   0x08004cd6   0x00000002   PAD
    0x08004cd8   0x08004cd8   0x0000000c   Code   RO         1594    .text               c_w.l(sys_exit.o)
    0x08004ce4   0x08004ce4   0x0000000e   Code   RO         1605    .text               c_w.l(h1_init.o)
    0x08004cf2   0x08004cf2   0x00000034   Code   RO         1607    .text               c_w.l(h1_extend.o)
    0x08004d26   0x08004d26   0x00000042   Code   RO         1617    .text               c_w.l(ftell.o)
    0x08004d68   0x08004d68   0x00000032   Code   RO         1625    .text               c_w.l(defsig_general.o)
    0x08004d9a   0x08004d9a   0x0000000e   Code   RO         1627    .text               c_w.l(defsig_rtmem_outer.o)
    0x08004da8   0x08004da8   0x0000000e   Code   RO         1642    .text               c_w.l(sys_wrch.o)
    0x08004db6   0x08004db6   0x00000002   PAD
    0x08004db8   0x08004db8   0x00000008   Code   RO         1649    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08004dc0   0x08004dc0   0x00000050   Code   RO         1655    .text               c_w.l(defsig_rtmem_inner.o)
    0x08004e10   0x08004e10   0x0000003e   Code   RO         1329    CL$$btod_d2e        c_w.l(btod.o)
    0x08004e4e   0x08004e4e   0x00000046   Code   RO         1331    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08004e94   0x08004e94   0x00000060   Code   RO         1330    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08004ef4   0x08004ef4   0x00000338   Code   RO         1339    CL$$btod_div_common  c_w.l(btod.o)
    0x0800522c   0x0800522c   0x000000dc   Code   RO         1336    CL$$btod_e2e        c_w.l(btod.o)
    0x08005308   0x08005308   0x0000002a   Code   RO         1333    CL$$btod_ediv       c_w.l(btod.o)
    0x08005332   0x08005332   0x0000002a   Code   RO         1332    CL$$btod_emul       c_w.l(btod.o)
    0x0800535c   0x0800535c   0x00000244   Code   RO         1338    CL$$btod_mult_common  c_w.l(btod.o)
    0x080055a0   0x080055a0   0x00000030   Code   RO         1370    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x080055d0   0x080055d0   0x0000000e   Code   RO         1257    i._is_digit         c_w.l(__printf_wp.o)
    0x080055de   0x080055de   0x0000001a   Code   RO         1354    i.fputc             c_w.l(fputc.o)
    0x080055f8   0x080055f8   0x0000002c   Code   RO         1366    locale$$code        c_w.l(lc_numeric_c.o)
    0x08005624   0x08005624   0x0000000c   Code   RO         1295    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x08005630   0x08005630   0x00000056   Code   RO         1275    x$fpl$f2d           fz_wm.l(f2d.o)
    0x08005686   0x08005686   0x0000008c   Code   RO         1297    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x08005712   0x08005712   0x0000000a   Code   RO         1592    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x0800571c   0x0800571c   0x00000004   Code   RO         1277    x$fpl$printf1       fz_wm.l(printf1.o)
    0x08005720   0x08005720   0x00000000   Code   RO         1299    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x08005720   0x08005720   0x00000094   Data   RO         1327    .constdata          c_w.l(bigflt0.o)
    0x080057b4   0x080057b4   0x00000004   Data   RO         1373    .constdata          c_w.l(sys_io.o)
    0x080057b8   0x080057b8   0x00000004   Data   RO         1374    .constdata          c_w.l(sys_io.o)
    0x080057bc   0x080057bc   0x00000004   Data   RO         1375    .constdata          c_w.l(sys_io.o)
    0x080057c0   0x080057c0   0x00000020   Data   RO         1680    Region$$Table       anon$$obj.o
    0x080057e0   0x080057e0   0x0000001c   Data   RO         1365    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080057fc, Size: 0x000007d8, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080057fc   0x00000004   Data   RW            4    .data               main.o
    0x20000004   0x08005800   0x00000014   Data   RW          228    .data               system_stm32f4xx.o
    0x20000018   0x08005814   0x00000010   Data   RW          773    .data               stm32f4xx_rcc.o
    0x20000028   0x08005824   0x00000004   Data   RW          978    .data               delay.o
    0x2000002c   0x08005828   0x00000009   Data   RW         1019    .data               usart.o
    0x20000035   0x08005831   0x00000001   PAD
    0x20000036   0x08005832   0x00000004   Data   RW         1046    .data               atd5984.o
    0x2000003a   0x08005836   0x00000002   Data   RW         1161    .data               motor_control.o
    0x2000003c   0x08005838   0x00000004   Data   RW         1292    .data               c_w.l(stdio_streams.o)
    0x20000040   0x0800583c   0x00000004   Data   RW         1293    .data               c_w.l(stdio_streams.o)
    0x20000044   0x08005840   0x00000004   Data   RW         1294    .data               c_w.l(stdio_streams.o)
    0x20000048        -       0x00000030   Zero   RW         1189    .bss                k230_comm.o
    0x20000078        -       0x00000054   Zero   RW         1289    .bss                c_w.l(stdio_streams.o)
    0x200000cc        -       0x00000054   Zero   RW         1290    .bss                c_w.l(stdio_streams.o)
    0x20000120        -       0x00000054   Zero   RW         1291    .bss                c_w.l(stdio_streams.o)
    0x20000174        -       0x00000060   Zero   RW         1378    .bss                c_w.l(libspace.o)
    0x200001d4   0x08005844   0x00000004   PAD
    0x200001d8        -       0x00000200   Zero   RW          252    HEAP                startup_stm32f40_41xxx.o
    0x200003d8        -       0x00000400   Zero   RW          251    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      3824       2018          0          4          0       5012   atd5984.o
       260          8          0          4          0       1557   delay.o
       932         48          0          0         48       7147   k230_comm.o
       100          0          0          4          0     282920   main.o
       224         20          0          0          0       1897   misc.o
      1196        624          0          2          0       4912   motor_control.o
        64         26        392          0       1536        860   startup_stm32f40_41xxx.o
       660         44          0          0          0       4241   stm32f4xx_gpio.o
        64          6          0          0          0       1602   stm32f4xx_it.o
      1628         52          0         16          0      13152   stm32f4xx_rcc.o
      3234         60          0          0          0      23096   stm32f4xx_tim.o
      1108         34          0          0          0       7964   stm32f4xx_usart.o
       528         46          0         20          0       1899   system_stm32f4xx.o
       336         24          0          9          0       2128   usart.o

    ----------------------------------------------------------------------
     14160       <USER>        <GROUP>         60       1584     358387   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         2          0          0          1          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       284          0          0          0          0        156   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        10          0          0          0          0         68   defsig_exit.o
        50          0          0          0          0         88   defsig_general.o
        80         58          0          0          0         76   defsig_rtmem_inner.o
        14          0          0          0          0         80   defsig_rtmem_outer.o
        52         38          0          0          0         76   defsig_rtred_inner.o
        14          0          0          0          0         80   defsig_rtred_outer.o
        18          0          0          0          0         80   exit.o
        76          0          0          0          0         88   fclose.o
         8          0          0          0          0         68   ferror.o
       470          0          0          0          0         88   flsbuf.o
       236          4          0          0          0        128   fopen.o
        26          0          0          0          0         68   fputc.o
       248          6          0          0          0         84   fseek.o
        66          0          0          0          0         76   ftell.o
        94          0          0          0          0         80   h1_alloc.o
        52          0          0          0          0         68   h1_extend.o
        78          0          0          0          0         80   h1_free.o
        14          0          0          0          0         84   h1_init.o
         6          0          0          0          0        152   heapauxi.o
         4          0          0          0          0        136   hguard.o
         0          0          0          0          0          0   indicate_semi.o
       138          0          0          0          0        168   init_alloc.o
       312         46          0          0          0        112   initio.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         6          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
         0          0          0          0          0          0   maybetermalloc1.o
        24          4          0          0          0         84   noretval__2printf.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_heap_descriptor_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        70          0          0          0          0         80   setvbuf.o
       240          6          0          0          0        156   stdio.o
         0          0          0         12        252          0   stdio_streams.o
       128          0          0          0          0         68   strcmpv7m.o
        62          0          0          0          0         76   strlen.o
        12          4          0          0          0         68   sys_exit.o
       102          0         12          0          0        240   sys_io.o
        74          0          0          0          0         80   sys_stackheap_outer.o
        14          0          0          0          0         76   sys_wrch.o
         2          0          0          0          0         68   use_no_semi.o
        12          0          0          0          0        116   dretinf.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
         4          0          0          0          0        116   printf1.o
         0          0          0          0          0          0   usenofp.o
        48          0          0          0          0        124   fpclassify.o

    ----------------------------------------------------------------------
      7752        <USER>        <GROUP>         12        352       6104   Library Totals
        18          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      7434        374        188         12        348       5368   c_w.l
       252          8          0          0          0        612   fz_wm.l
        48          0          0          0          0        124   m_wm.l

    ----------------------------------------------------------------------
      7752        <USER>        <GROUP>         12        352       6104   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     21912       3392        612         72       1936     358227   Grand Totals
     21912       3392        612         72       1936     358227   ELF Image Totals
     21912       3392        612         72          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                22524 (  22.00kB)
    Total RW  Size (RW Data + ZI Data)              2008 (   1.96kB)
    Total ROM Size (Code + RO Data + RW Data)      22596 (  22.07kB)

==============================================================================

