<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>文件流控件 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="动画控件" href="Gmov.html" />
    <link rel="prev" title="文件浏览器控件" href="FileBrowser.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">控件详解</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="component.html">认识控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Page.html">页面控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Text.html">文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Scrolling_text.html">滚动文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Number.html">数字控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Xfloat.html">虚拟浮点数控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Button.html">按钮控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Progress_bar.html">进度条控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Picture.html">图片控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Crop.html">切图控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Hotspot.html">触摸热区控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="TouchCap.html">触摸捕捉控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Gauge.html">指针控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Waveform.html">曲线波形控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Slider.html">滑块控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Timer.html">定时器控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Variable.html">变量控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Dual-state_button.html">双态按钮控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Checkbox.html">复选框控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Radio.html">单选框控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="QRcode.html">二维码控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Switch.html">状态开关控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="ComboBox.html">下拉框控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="TextSelect.html">选择文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="SLText.html">滑动文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="DataRecord.html">数据记录控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="FileBrowser.html">文件浏览器控件</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">文件流控件</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#id5">文件流控件-使用详解</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id6">文件流关键属性</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id7">文件流控件有哪些方法</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#id12">文件流控件-常见问题</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id13">在电脑模拟器上是正常打开的，但是下载到串口屏后总是提示打开失败</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id14">在电脑模拟器上读取到的数据和串口屏上的数据不一致</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id15">文件流控件如何跨页面调用同一个文件</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#id16">文件流控件-样例工程下载</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id17">文件流控件-相关链接</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id18">文件流控件-属性详解</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id19">文件流控件-方法详解</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="Gmov.html">动画控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Video.html">视频控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Audio.html">音频控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="ExPicture.html">外部图片控件</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">所有控件详解</a> &raquo;</li>
      <li>文件流控件</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>文件流控件<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>视频教程： <a class="reference external" href="https://www.bilibili.com/video/BV1GtJqztEyr">文件流控件使用教程一</a></p>
</div>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>视频教程： <a class="reference external" href="https://www.bilibili.com/video/BV1gwJnz3E7R">文件流控件使用教程二</a></p>
</div>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>视频教程： <a class="reference external" href="https://www.bilibili.com/video/BV18hJxzdEnh">文件流控件使用教程三</a></p>
</div>
<p>文件流控件仅X2、X3、X5系列支持</p>
<p>文件流控件用于在串口屏上对SD卡中的文件进行读写操作，仅X系列支持，文件流控件只能设置为私有</p>
<p>文件流控件位于特殊控件栏上</p>
<img alt="../_images/filestream_1.jpg" src="../_images/filestream_1.jpg" />
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>导入字库请参考:  <a class="reference internal" href="../QA/QA115.html#id1"><span class="std std-ref">如何导入字库</span></a></p>
<p>导入图片请参考:  <a class="reference internal" href="../QA/QA116.html#id1"><span class="std std-ref">如何导入图片</span></a></p>
<p>导入动画请参考:  <a class="reference internal" href="../QA/QA117.html#id1"><span class="std std-ref">如何导入动画</span></a></p>
<p>导入视频请参考:  <a class="reference internal" href="../QA/QA118.html#id1"><span class="std std-ref">如何导入视频</span></a></p>
<p>导入音频请参考:  <a class="reference internal" href="../QA/QA119.html#id1"><span class="std std-ref">如何导入音频</span></a></p>
</div>
<section id="id5">
<h2>文件流控件-使用详解<a class="headerlink" href="#id5" title="此标题的永久链接"></a></h2>
<p>请先查看 <a class="reference internal" href="../advanced/SDcard.html#sd"><span class="std std-ref">SD卡读写文件流程</span></a></p>
<section id="id6">
<h3>文件流关键属性<a class="headerlink" href="#id6" title="此标题的永久链接"></a></h3>
<section id="val">
<h4>val-文件流当前数据指针<a class="headerlink" href="#val" title="此标题的永久链接"></a></h4>
<blockquote>
<div><p>1、每次使用open方法打开一个文件时，文件流的val数据指针会默认变为0</p>
<p>2、write方法会基于当前的val数据指针的位置写入数据，写入成功后会自动移动val数据指针的位置</p>
<p>3、read方法会基于当前的val数据指针的位置读取数据，读取成功后会自动移动val数据指针的位置</p>
<p>4、find方法查找成功后，会自动移动指针到关键字中第一个字符所在的位置</p>
<p>5、find方法可以查找汉字，如果查找失败，注意检查工程的编码与文件的编码是否一致</p>
</div></blockquote>
</section>
<section id="qty">
<h4>qty-文件大小<a class="headerlink" href="#qty" title="此标题的永久链接"></a></h4>
<p>例-获取当前文件的大小：</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="o">=</span><span class="n">fs0</span><span class="p">.</span><span class="n">qty</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="en">
<h4>en-文件打开状态<a class="headerlink" href="#en" title="此标题的永久链接"></a></h4>
<p>例-获取当前文件打开状态，如果是打开的就关闭：</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="k">if</span><span class="p">(</span><span class="n">fs0</span><span class="p">.</span><span class="n">en</span><span class="o">==</span><span class="mi">1</span><span class="p">)</span><span class="w"></span>
<span class="linenos">2</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="linenos">3</span><span class="w">     </span><span class="n">fs0</span><span class="p">.</span><span class="n">close</span><span class="p">()</span><span class="w"></span>
<span class="linenos">4</span><span class="w"> </span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="id7">
<h3>文件流控件有哪些方法<a class="headerlink" href="#id7" title="此标题的永久链接"></a></h3>
<img alt="../_images/filestream_2.jpg" src="../_images/filestream_2.jpg" />
<p>文件流控件有5个方法，分别为open，read，write，close，find</p>
<p>open：打开文件(成功返回1,失败返回0)</p>
<p>read：从当前流读数据(成功返回1,失败返回0,从当前数据指针[val属性]位置开始读,读完以后指针将自动移动相应的长度)</p>
<p>write：将数据写入当前流(成功返回1,失败返回0,从当前数据指针[val属性]位置开始写,写完以后指针将自动移动相应的长度)</p>
<p>close：关闭文件流(成功返回1,失败返回0,文件打开读写操作完成后一定要记得关闭文件,同一个文件在打开后,关闭之前,是不能被另外一个文件流控件打开的)</p>
<p>find：按关键字查询并定位文件流指针(查询成功返回1,失败返回0,从当前流的当前数据指针[val属性]位置开始查询关键字,如果查询成功,数据指针将会移动到关键字中第一个字符处;如果查询失败保持数据当前指针不变)</p>
<section id="open">
<h4>open-打开文件<a class="headerlink" href="#open" title="此标题的永久链接"></a></h4>
<p>int open(string path)</p>
<blockquote>
<div><p>成功返回1,失败返回0</p>
<p>path 文件路径如“sd0/aa.txt”</p>
</div></blockquote>
<p>例：</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="n">fs0</span><span class="p">.</span><span class="n">open</span><span class="p">(</span><span class="s">&quot;sd0/aa.txt&quot;</span><span class="p">)</span><span class="w">  </span><span class="c1">//打开sd0下aa.txt</span>
</pre></div>
</div>
<p>因为有返回值，所以可以用一个变量来判断是否打开成功</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="n">sys0</span><span class="o">=</span><span class="n">fs0</span><span class="p">.</span><span class="n">open</span><span class="p">(</span><span class="s">&quot;sd0/aa.txt&quot;</span><span class="p">)</span><span class="w"></span>
<span class="linenos">2</span><span class="w"> </span><span class="k">if</span><span class="p">(</span><span class="n">sys0</span><span class="o">==</span><span class="mi">1</span><span class="p">)</span><span class="w"></span>
<span class="linenos">3</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="linenos">4</span><span class="w">     </span><span class="c1">//文件打开成功</span>
<span class="linenos">5</span><span class="w"> </span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>1、文件打开读写操作完成后一定要记得关闭文件，同一个文件在打开后，关闭之前，是不能被另外一个文件流控件打开的。</p>
<p>2、文件每次打开，文件流属性val都会被赋值为0。</p>
</div>
</section>
<section id="read">
<h4>read-从文件流读取数据<a class="headerlink" href="#read" title="此标题的永久链接"></a></h4>
<p>int read(object att,int star,int lenth)</p>
<blockquote>
<div><p>读取成功返回1，失败返回0</p>
<p>att 变量名称</p>
<p>star 变量起始地址(一般为0)</p>
<p>lenth 读入数据长度</p>
</div></blockquote>
<section id="id8">
<h5>文件流读取-示例<a class="headerlink" href="#id8" title="此标题的永久链接"></a></h5>
<p>以下代码将会从文件的100地址处读取10个字节，读取完毕后，文件流的指针（fs0.val）会自动变为110</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="n">fs0</span><span class="p">.</span><span class="n">val</span><span class="o">=</span><span class="mi">100</span><span class="w"> </span><span class="c1">//设置读取的位置</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">fs0</span><span class="p">.</span><span class="n">read</span><span class="p">(</span><span class="n">va2</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">10</span><span class="p">)</span><span class="w"> </span><span class="c1">//读取10个字节,将数据放入va2.txt</span>
</pre></div>
</div>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>起始地址不为0时，若此时文本控件为空文本，即首字符为”\0”，此时是读取成功的，但是文本控件的第一个数据是”\0”，被判断为字符串已经结束了，因此还是显示空白文本。</p>
<p>读取要注意控件属性(txt_maxl),读取数据长度超过范围，会读取失败，返回0，无法显示读取的内容。</p>
<p>读取成功，文件流指针会自动增加。</p>
</div>
</section>
</section>
<section id="write">
<h4>write-将数据写入文件流<a class="headerlink" href="#write" title="此标题的永久链接"></a></h4>
<blockquote>
<div><p>int write(object att,int star,int lenth)</p>
<p>写入成功返回1，失败返回0</p>
<p>att 变量名称</p>
<p>star 变量起始地址(一般为0)</p>
<p>lenth 写入数据长度</p>
</div></blockquote>
<section id="id9">
<h5>文件流写入-示例<a class="headerlink" href="#id9" title="此标题的永久链接"></a></h5>
<p>以下代码将会从文件的1000地址处读取100个字节，读取完毕后，文件流的指针（fs0.val）会自动变为1100</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="n">fs0</span><span class="p">.</span><span class="n">val</span><span class="o">=</span><span class="mi">1000</span><span class="w"> </span><span class="c1">//设置写入的位置</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">fs0</span><span class="p">.</span><span class="n">write</span><span class="p">(</span><span class="n">va2</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">100</span><span class="p">)</span><span class="w"> </span><span class="c1">//将va2.txt的字符内容写入当前打开的文件中</span>
</pre></div>
</div>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>写入成功，文件流指针会自动增加</p>
<p>变量起始地址不为0时，写入变量的起始地址，不是文件的起始位置。</p>
</div>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="n">t0</span><span class="p">.</span><span class="n">txt</span><span class="o">=</span><span class="s">&quot;abcd123456&quot;</span><span class="w"></span>
<span class="linenos">2</span><span class="w"> </span><span class="n">fs0</span><span class="p">.</span><span class="n">write</span><span class="p">(</span><span class="n">t0</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="mi">2</span><span class="p">,</span><span class="mi">10</span><span class="p">)</span><span class="w">  </span><span class="c1">//此时写入的是cd123456,长度不足的部分被写入0x00</span>
</pre></div>
</div>
</section>
</section>
<section id="close">
<h4>close-关闭文件流<a class="headerlink" href="#close" title="此标题的永久链接"></a></h4>
<p>int close()</p>
<blockquote>
<div><p>成功返回1,失败返回0</p>
<p>文件打开读写操作完成后一定要记得关闭文件,同一个文件在打开后,关闭之前,是不能被另外一个文件流控件打开的</p>
</div></blockquote>
<section id="id10">
<h5>文件流关闭-示例<a class="headerlink" href="#id10" title="此标题的永久链接"></a></h5>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="k">if</span><span class="p">(</span><span class="n">fs0</span><span class="p">.</span><span class="n">en</span><span class="o">==</span><span class="mi">1</span><span class="p">)</span><span class="w"></span>
<span class="linenos">2</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="linenos">3</span><span class="w">     </span><span class="n">fs0</span><span class="p">.</span><span class="n">close</span><span class="p">()</span><span class="w"></span>
<span class="linenos">4</span><span class="w"> </span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="find">
<h4>find-按关键字查询并定位文件流指针<a class="headerlink" href="#find" title="此标题的永久链接"></a></h4>
<p>int find(string key)</p>
<blockquote>
<div><p>成功返回1，失败返回0</p>
<p>key 关键字字符串变量/常量</p>
</div></blockquote>
<section id="id11">
<h5>文件流查找-示例<a class="headerlink" href="#id11" title="此标题的永久链接"></a></h5>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="n">t0</span><span class="p">.</span><span class="n">txt</span><span class="o">=</span><span class="s">&quot;abcd123456&quot;</span><span class="w"></span>
<span class="linenos">2</span><span class="w"> </span><span class="n">fs0</span><span class="p">.</span><span class="n">write</span><span class="p">(</span><span class="n">t0</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">15</span><span class="p">)</span><span class="w">  </span><span class="c1">//写入成功后fs0.val变为15</span>
<span class="linenos">3</span><span class="w"> </span><span class="n">fs0</span><span class="p">.</span><span class="n">val</span><span class="o">=</span><span class="mi">0</span><span class="w">   </span><span class="c1">//将文件流指针复位，否则查找不到</span>
<span class="linenos">4</span><span class="w"> </span><span class="n">sys0</span><span class="o">=</span><span class="n">fs0</span><span class="p">.</span><span class="n">find</span><span class="p">(</span><span class="s">&quot;123&quot;</span><span class="p">)</span><span class="w"></span>
<span class="linenos">5</span><span class="w"> </span><span class="k">if</span><span class="p">(</span><span class="n">sys0</span><span class="o">==</span><span class="mi">1</span><span class="p">)</span><span class="w"></span>
<span class="linenos">6</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="linenos">7</span><span class="w">     </span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="o">=</span><span class="n">fs0</span><span class="p">.</span><span class="n">val</span><span class="w"></span>
<span class="linenos">8</span><span class="w"> </span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<p>查找结果n0.val=4</p>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>从当前流的当前数据指针(val属性)位置开始查询关键字，如果查询成功，数据指针将会移动到关键字中第一个字符串：如果查询失败保持数据当前指针不变</p>
</div>
</section>
</section>
</section>
</section>
<section id="id12">
<h2>文件流控件-常见问题<a class="headerlink" href="#id12" title="此标题的永久链接"></a></h2>
<section id="id13">
<h3>在电脑模拟器上是正常打开的，但是下载到串口屏后总是提示打开失败<a class="headerlink" href="#id13" title="此标题的永久链接"></a></h3>
<p>请按以下步骤进行检查</p>
<blockquote>
<div><p>1、串口屏上是否插入了SD卡</p>
<p>2、SD卡是否超过32GB，请勿使用超过32GB的SD卡（例如：512M、1GB、2GB、4GB、8GB、16GB、32GB都是可用的）</p>
<p>3、SD卡的文件格式是否是FAT32，目前只支持FAT32格式</p>
</div></blockquote>
</section>
<section id="id14">
<h3>在电脑模拟器上读取到的数据和串口屏上的数据不一致<a class="headerlink" href="#id14" title="此标题的永久链接"></a></h3>
<p>电脑上在创建文件时，会自动将整个文件都初始化为0x00,但是在串口屏上并不会</p>
<p>这是因为电脑的cpu是多线程的且电脑的性能远远高于串口屏，而串口屏是单线程</p>
<p>如果串口屏也自动初始化整个文件，当用户创建比较大的文件，如1GB的文件时，串口屏将会因为初始化文件的原因卡住一段时间，可能几分钟到十几分钟不等，取决于SD卡性能</p>
<p>如有需求，可以按照下面的方法来初始化文件</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//新建文件，并初始化整个文件</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">newfile</span><span class="w"> </span><span class="s">&quot;sd0/1.txt&quot;</span><span class="p">,</span><span class="mi">4096</span><span class="w"></span>
<span class="linenos">3</span><span class="w"> </span><span class="k">for</span><span class="p">(</span><span class="n">sys0</span><span class="o">=</span><span class="mi">0</span><span class="p">;</span><span class="n">sys0</span><span class="o">&lt;=</span><span class="mi">1024</span><span class="p">;</span><span class="n">sys0</span><span class="o">++</span><span class="p">)</span><span class="w"></span>
<span class="linenos">4</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="linenos">5</span><span class="w">     </span><span class="c1">//循环1024次，每次写入4字节，总共写入4096个字节，将0-4095初始化为0x00</span>
<span class="linenos">6</span><span class="w">     </span><span class="n">fs0</span><span class="p">.</span><span class="n">write</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">4</span><span class="p">)</span><span class="w"></span>
<span class="linenos">7</span><span class="w"> </span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id15">
<h3>文件流控件如何跨页面调用同一个文件<a class="headerlink" href="#id15" title="此标题的永久链接"></a></h3>
<p>文件流控件只能是私有的，不能跨页面调用，因此只能在当前页面调用文件。</p>
<p>需要跨页面调用同一个文件的情况下，请使用以下方法。</p>
<p>用一个全局文本控件记录当前文件流控件调用的文件位置。</p>
<p>用一个全局数字控件记录当前文件流控件的val属性。</p>
<p>每个需要用到文件流读写的页面都要添加一个文件流控件。</p>
<p>当跳转到另一个页面时，重新通过全局文本控件打开之前的文件，重新通过全局数字控件赋值给新的文件流控件的val属性即可进行读写,读写完毕后关闭文件流。</p>
<p>以上方法也可以用于一个文件流控件同时操作记录多个文件的名称以及val属性。</p>
</section>
</section>
<section id="id16">
<h2>文件流控件-样例工程下载<a class="headerlink" href="#id16" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/文件浏览器控件/文件浏览器v2.0.HMI">《文件浏览器v2.0》演示工程下载</a></p>
</section>
<section id="id17">
<h2>文件流控件-相关链接<a class="headerlink" href="#id17" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="../advanced/SDcard.html#sd"><span class="std std-ref">SD卡读写文件流程</span></a></p>
<p><a class="reference internal" href="../QA/QA10.html#id1"><span class="std std-ref">哪些控件属性可以运行中修改，哪些不能运行中修改，绿色属性和黑色属性有什么区别？</span></a></p>
<p><a class="reference internal" href="../QA/QA11.html#txtval"><span class="std std-ref">txt属性和val属性有什么区别，为什么有些属性赋值要加双引号，有些不用加双引号</span></a></p>
<p><a class="reference internal" href="../grammar/global_variable.html#id1"><span class="std std-ref">跨页面赋值，全局变量操作</span></a></p>
</section>
<section id="id18">
<h2>文件流控件-属性详解<a class="headerlink" href="#id18" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="component.html#id6"><span class="std std-ref">控件属性解析</span></a></p>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>绿色属性可以通过上位机或者串口屏指令进行修改，黑色属性只能在上位机中修改或者不可修改，可通过上位机进行修改指“选中控件后通过属性栏修改控件的属性”</p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">type属性</span></code> -控件类型，固定值，不同类型的控件type值不同，相同类型的控件type值相同，可读，不可通过上位机修改，不可通过指令修改。参考： <a class="reference internal" href="component.html#id10"><span class="std std-ref">控件属性-控件id对照表</span></a></p>
<p><code class="docutils literal notranslate"><span class="pre">id属性</span></code> -控件ID，可通过上位机左上角的上下箭头置顶或置底，可读，可通过上位机修改左上角的箭头置顶或置地间接修改，不可通过指令修改。参考： <a class="reference internal" href="../QA/QA28.html#id1"><span class="std std-ref">如何更改控件的前后图层关系</span></a></p>
<p><code class="docutils literal notranslate"><span class="pre">objname属性</span></code> -控件名称。不可读，可通过上位机进行修改，不可通过指令更改。</p>
<p><code class="docutils literal notranslate"><span class="pre">vscope属性</span></code> -内存占用(私有占用只能在当前页面被访问，全局占用可以在所有页面被访问)，当设置为私有时，跳转页面后，该控件占用的内存会被释放，重新返回该页面后该控件会恢复到最初的设置。可读，可通过上位机进行修改，不可通过指令更改。参考：<a class="reference internal" href="../grammar/global_variable.html#id1"><span class="std std-ref">跨页面赋值，全局变量操作</span></a></p>
<p>文件流控件的vscope属性仅能为私有。</p>
<p><code class="docutils literal notranslate"><span class="pre">val属性</span></code> -此文件流当前数据指针(打开文件时恢复为0,读写操作过程中自动移动,支持手动设置)</p>
<p><code class="docutils literal notranslate"><span class="pre">qty属性</span></code> -文件大小(运行中根据实际打开的文件自动更新,只可获取不可设置)</p>
<p><code class="docutils literal notranslate"><span class="pre">en属性</span></code> -文件打开状态(只可获取不可设置)</p>
</section>
<section id="id19">
<h2>文件流控件-方法详解<a class="headerlink" href="#id19" title="此标题的永久链接"></a></h2>
<p><code class="docutils literal notranslate"><span class="pre">open方法</span></code> - int open(string path) 打开文件(成功返回1,失败返回0)</p>
<p><code class="docutils literal notranslate"><span class="pre">read方法</span></code> - int read(object att,int star,int lenth) 从当前流读数据(成功返回1,失败返回0,从当前数据指针[val属性]位置开始读,读完以后指针将自动移动相应的长度)</p>
<p><code class="docutils literal notranslate"><span class="pre">write方法</span></code> - int write(object att,int star,int lenth) 将数据写入当前流(成功返回1,失败返回0,从当前数据指针[val属性]位置开始写,写完以后指针将自动移动相应的长度)</p>
<p><code class="docutils literal notranslate"><span class="pre">close方法</span></code> - int close() 关闭文件流(成功返回1,失败返回0,文件打开读写操作完成后一定要记得关闭文件,同一个文件在打开后,关闭之前,是不能被另外一个文件流控件打开的)</p>
<p><code class="docutils literal notranslate"><span class="pre">find方法</span></code> - int find(string key) 按关键字查询并定位文件流指针(查询成功返回1,失败返回0,从当前流的当前数据指针[val属性]位置开始查询关键字,如果查询成功,数据指针将会移动到关键字中第一个字符处;如果查询失败保持数据当前指针不变)</p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="FileBrowser.html" class="btn btn-neutral float-left" title="文件浏览器控件" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="Gmov.html" class="btn btn-neutral float-right" title="动画控件" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>