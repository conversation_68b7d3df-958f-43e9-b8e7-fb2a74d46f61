<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="BLEdebug User Guide">
    <title>BLEdebug User Guide</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
            display: flex;
            min-height: 100vh;
            flex-direction: row;
        }

        header {
            background-color: #f4f4f4;
            color: white;
            padding: 10px 20px;
            text-align: center;
            width: 100%;
        }

        h1 {
            font-size: 2em;
        }

        .content {
            margin-left: 220px;
            /* 给内容区域留出空间，避免被目录遮挡 */
            padding: 20px;
            width: 100%;
            overflow-y: auto;
            /* 允许内容区域滚动 */
            flex-grow: 1;
        }

        h2 {
            font-size: 1.6em;
            margin-top: 20px;
            color: #333;
        }

        h4 {
            margin-top: 0px;
            text-indent: 2em;
            margin-bottom: 0px;
        }

        p {
            text-align: left;
            text-indent: 2em;
            font-size: 1em;
            color: #000;
        }

        ul {
            list-style-type: disc;
            margin-left: 20px;
        }

        /* 固定目录区域 */
        .toc {
            position: fixed;
            top: 20px;
            left: 20px;
            background-color: #e9ecef;
            padding: 15px;
            width: 180px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            height: calc(100vh - 40px);
            /* 使目录高度填满整个页面 */
            overflow-y: auto;
            /* 允许目录滚动 */
        }

        .toc h2 {
            font-size: 1.4em;
        }

        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }

        .toc li {
            margin: 10px 0;
        }

        .toc a {
            text-decoration: none;
            color: #007bff;
        }

        img {
            display: block;
            margin: auto;
        }

        footer {
            background-color: #f4f4f4;
            color: white;
            padding: 10px;
            text-align: center;
            position: fixed;
            width: 100%;
            bottom: 0;
        }
    </style>
</head>

<body>

    <!-- <header>
    <h1>BLEdebug Introduction</h1>
    <p>Wlecome to use our software!</p>
</header> -->

    <div class="toc">
        <h2>Contents</h2>
        <ul>
            <li><a href="#intro">1. Software Introduction</a></li>
            <li><a href="#platform">2. Runtime</a></li>
            <li><a href="#usage">3. Start to use</a>
                <ul><li><a href="#usage_1">3.1 Preparation for using software</a></li>
                    <li><a href="#usage_2">3.2 View broadcast device information</a></li>
                    <li><a href="#usage_3">3.3 Connect device</a></li>
                    <li><a href="#usage_4">3.4 Service and characteristics debugging</a></li>
                </ul>
            </li>
            <li><a href="#features">4. Software functional description</a>
                <ul><li><a href="#features_1">4.1 Interface overview</a></li>
                    <li><a href="#features_2">4.2 Device list</a></li>
                    <li><a href="#features_3">4.3 Service and characteristics debugging</a></li>
                    <li><a href="#features_4">4.4 Automatic connection management</a></li>
                    <li><a href="#features_5">4.5 Output window</a></li>
                </ul>
            </li>
            <li><a href="#faq">5. Q&A</a></li>
        </ul>
    </div>

    <div class="content">
        <section id="intro">
            <h2>1. Software Introduction</h2>
            <p>BLEDebug is a Bluetooth low energy debugging tool software on Windows platform. The software supports the following functions:</p>
            <ul>
                <li>BLE broadcast device scanning, broadcast information viewing</li>
                <li>BLE device connection, device pairing</li>
                <li>Device characteristics, service information display and characteristics debugging</li>
                <li>Automatic connection of broadcast device</li>
                <li>Output log of debugging</li>
            </ul>
        </section>

        <section id="platform">
            <h2>2. Runtime</h2>
            <p>The software runs on Windows 10 and above PC with Bluetooth adapter.</p>
        </section>

        <section id="usage">
            <h2>3. Starting to use</h2>
            <h3 id="usage_1">3.1 Preparation for using software</h3>
            <p>Please confirm that your computer has a Bluetooth adapter and enable Bluetooth in system settings.</p>
            <h3 id="usage_2">3.2 View broadcast device information</h3>
            <p>When the software is opened, it will automatically scan the Bluetooth broadcast signal in the coverage area of the current RF signal.BLEDebug determines whether a device is connectable by the type of broadcast packet scanned，
                if there is no broadcast packet of connectable type(such as ConnectableUndirected type),the "Connect" and "Pair" buttons will not be shown in the device list</p>
            <p>Click the device of the device list,the broadcast data information of the device will display below the list;
                The broadcast data will be displayed in form by type, and if there are multiple types of broadcast packets, multiple tables will be displayed.</p>
            <h3 id="usage_3">3.3 Connect device</h3>
            <p>Click the "Connect" button,the service and characteristics debug window on the right will show the connection status of the device.
                After connecting to the Bluetooth device successfully, the service and the characteristics information of each service contains are automatically enumerated,
                and the MTU size will be obtained, as shown in the following figure:</p>
            <img src="document.files/Connect device.png" alt="Connect device" width="635" style="display: block; margin: auto;" />
            <p>GATT information is displayed in a tree structure, and double-click operations can expand/collapse service entries.
                The read, write, and notify operations supported by the entry are identified by different ICONS to the right of the entry.
                Here you can see the all functions of the device and interact with its functions.</p>
                <p>BLEDebug supports multi-device debugging, when connecting multiple devices, multiple Tab pages will appear in the service/characteristics debugging window, corresponding to different devices.
                     At this time, you can click the icon at the top of the window to display the device information in columns.</p>
            <h3 id="usage_4">3.4 Service and characteristics debugging</h3>
            <p>In the GATT information tree, if the target characteristic to be debugged is selected, the corresponding debug window will pop up on its right.</p>
            <p>Let's take the device "CH9141BLE2U" as an example, after connectting, you can see that the device supports multiple service,the ones with specific names are defined by the Bluetooth Special Interest Group,
                the customised ones are shown as Unknown Service.Click on a specific service, the case is "Unknown Service", click on a specific characteristic value, 
                and the property of this characteristic value is read, write, and notify, which correspond to read, write, and notify on the right. You can read characteristic values, write data, or subscribe to notifications in this window.
                Both HEX and ASCII formats are supported for reading and writing data.The number of bytes sent/read and the real-time speed are automatically updated.
            </p>
            <img src="document.files/Service View.png" alt="Service View" width="635" />
            <p>When writing data, the tool supports single send data, continuous send data and sending file.When sending data continuously,the interval time needs to be filled in the edit box.
                In order to look up,the data sent and read are recorded in the log information of the output window.</p>
        </section>

        <section id="features">
            <h2>4. Software functional description</h2>
            <h3 id="features_1">4.1 Interface overview</h3>
            <img src="document.files/Interface Overview.png" alt="Interface Overview" width="635" />
            <p>The whole interface is divided into three parts: device list and device information, service/characteristic debugging and output window.</p>
            <ul>
                <li>Device list and device information：device list displays the Bluetooth devices scanned nearby and their names, MAC addresses, and RSSI information (dynamic). 
                    Devices can be filtered by name and MAC address, and the device list can be rearranged by MAC address and RSSI.</li>
                <li>Service view: display the service and their characteristics of Bluetooth devices connected successfully。</li>
                <li>Log: The output window can see the specific steps of the operation process, including the connection and disconnection information of the device, 
                    the data read and written by the device, and whether the notification is subscribed</li>
            </ul>
            <h3 id="features_2">4.2 Device list</h3>
            <p>You can discover,connect and explore BLE devices nearby by broadcast device list.</p>
                <img src="document.files/Device List.png" alt="Device List" width="460" />
            <p>The software automatically scans BLE devices in the surrounding environment after opening. Display the scan results dynamically in the device list. Each list entry contains:</p>
            <ol style="text-indent: 2em;" >
                <li>Device name(if available)</li>
                <li>MAC addres,unique identifier</li>
                <li>Signal strength indication</li>
                <li>Broadcast interval indication</li>
            </ol>
            <p><b>Device name</b>
                The device name is from Local Name data of the device broadcasted.If no relevant data is scanned, N/A is displayed.</p> 
            <p><b>Signal strength indication</b>
                Signal strength is RSSI value，it is expressed as a negative value in dBm. Which can be used to indicate the distance between the device and the host. A value closer to 0 indicates a stronger signal.</p>
            <p>In the device list of BLEDebug, a value icon between 0 and -50 dBm indicates full, and a value from -50 dBm to -70 dBm indicates lower.When the icon shows 1 bar, the RSSI is less then − 80 dBm. </p> 
            <p><b>Broadcast interval indication</b>It represents the time interval between two continuous broadcast packets of the same device scanned, and the interval is in milliseconds.</p> 
            <p>By default, the device is sorted in the order that it is scanned, connectable devices are sorted first, and devices with a local name are sorted first.
                 In addition, the software supports rearranging the device list by MAC address and RSSI strength , and filtering devices by name /MAC address.</p>
            <p>If you have doubts about the device information displayed in the device list, such as if the device is suspected to be in range but not displayed or if you want the device list to display 
                the latest information of nearby BLE devices, you can click the "Stop scanning" button and then manually click the "Start scanning" button to refresh the device list manually.</p>
            <h4>Broadcast data window</h4>
            <p>Clicking an item in the list,the broadcast data for that item will bring up at the bottom of the list, as shown in the following figure.</p> 
                <img src="document.files/Broadcast dada information.png" alt="Broadcast dada information" width="453"/>
                <p>The broadcast data will be displayed in form by type, and if there are multiple types of broadcast packets, multiple tables will be displayed.
                    As an example, the above figure shows that the device sends two types of broadcast packets, CoonnectableUndirected and ScanResponse, and the specific data contained in different broadcast packets.</p>
            
            <h4>Bonded device view</h4>
            <p>You can view bonded devices by clicking the "Bonded" above the list.</p>

            <h3 id="features_3">4.3 Service and characteristics debugging</h3>
            <p>After successfully connecting to the Bluetooth device, the services and the characteristics information contained in each service will be automatically enumerated.GATT information 
                is displayed in the service/characteristic debug window. Users can read, write, and subscribe to notifications or indications of different characteristics, enabling comprehensive device analysis and interaction.</p>
            <img src="document.files/Service View.png" alt="Service View" width="635" />
            <p>For BLE devices, each device has several services, and each service has several characteristics.
               You can think of a characteristic as a parameter with properties that can be read, written, both read and written, and some that support notifications.</p>
            <p>After reading and selecting the service and characteristics of the device, you can access it according to the property of characteristic. If the characteristic supports reading, you can click the "Read" button.
                If the service characteristic supports notifying, after the subscription, if the value of the parameter changes, the received data will eventually be displayed in the data area as well as the data read.</p>
            <p>In the process of writing characteristic,there is two type properties:"Write" and "WritewithoutResponse",the former requires the device to confirm receiving data, 
                and the latter does not, so it is faster to Write, but you need to check the "WriteNoResponse" option when writing.</p>
            <p>On the far right of the service/characteristic, there is a star ⭐ icon, which can be clicked to mark the corresponding service/characteristic.
                The marked services/characteristics can be quickly viewed after checking the "Only favourite" option in the check box above the GATT tree information.</p>
            <h3 id="features_4">4.4 Automatic connection management</h3>
            <p>BLEDebug supports the function of device auto-connection, select "Connection management" in the menu bar, as shown in the following figure.
                In the connection management window, it supports to set the specified MAC address as automatic connection and view the MAC address of the devices that have been set as automatic connect.
                Setting a MAC address as an automatic connection can also be added by right-clicking in the broadcast device list.
                You can also add a MAC address to automatic connection by right-clicking in the broadcast device list.</p>
            <p> If the specified device is found during a scan for BLE devices, a connection will be automatically initiated.</p>
            <img src="document.files/Auto Connection.png" alt="Auto Connection" width="411" />
            <h3 id="features_5">4.5 Output window</h3>
            <img src="document.files/Log.png" alt="Log" width="809" />
            <p>Output window is used to record and view log,and output relevant information which contains user operation information,the stataus of device and the data sent and received.</p>
            <p>Output window supports the functions of exporting,clearing and auto-scroll,which can be achieved by the buttons at the top of the window.</p>
            <p>You can filter the type of output information by the drop-down box above the output window, and the default is to display all.
                For example, if you are only concerned with the results of sending and receiving data, you can switch to "DATA" mode, in which case operation-related information will not be output.</p>
        </section>

        <section id="faq">
            <h2>5. Q&A</h2>
            <p>Here are some frequently asked questions and answers:</p>
            <ul>
                <li><strong>Q1: BLE device cannot be scanned</strong>
                        <br>A1: There may be the following reasons why the device cannot be scanned:
                        <ol><li>Bluetooth adapter is not enabled or old driver.</li>
                        <li>Target device is not in broadcast mode</li>
                        <li>Windows Bluetooth service exception.</li>
                        </ol>
                Solution:
                        <ol>
                            <li>Check Bluetooth status: Go to Settings - Bluetooth and Other Devices and make sure the Bluetooth switch is ‘On’. Make sure there is no exclamation mark or error message
                                    on the Bluetooth adapter in Device Manager.</li>
                            <li>Restart Bluetooth service: Press Win + R and enter services.msc, find Bluetooth support service, right click and select ‘Restart’.</li>
                            <li>Verify device broadcasting: Use a mobile app (e.g. nRF Connect) to confirm that the target device is visible.</li>
                        </ol>
                </li>
                <li><strong>Q2:Why can't I scan BLE extended advertisements?</strong>
                    <br> A2:Windows systems support the BLE 5.0 Extended Advertisement format starting with Windows 10 Build 19041. If your system version is earlier than 19041, Extended Advertisement is not supported.
                </li>
                <li><strong>Q3:Why are there no connect buttons on many of the devices scanned?</strong>
                    <br> A3:BLEDebug determines whether a device is connectable or not by the type of broadcast packet scanned. If no broadcast packet of connectable type is scanned, such as ConnectableUndirected type, 
                    the Connect button will not be shown in the device list.
                </li>
            </ul>
        </section>
    </div>
</body>

</html>