/**
 ******************************************************************************
 * @file    delay.c
 * <AUTHOR> Team & [Your Name]
 * @version V1.1
 * @date    2025-01-31
 * @brief   STM32F407系统延时函数源文件
 *          
 *          本文件实现了基于SysTick滴答定时器的精确延时功能
 *          支持微秒级和毫秒级延时，兼容RTOS环境
 ******************************************************************************
 */

#include "delay.h"
#include "sys.h"

/* 宏定义检查是否支持操作系统 */
#if SYSTEM_SUPPORT_OS
#include "includes.h"   // 支持OS时包含相关头文件
#endif

/* 私有变量 ------------------------------------------------------------------*/
static u8  fac_us = 0;  // 微秒延时倍乘数			   
static u16 fac_ms = 0;  // 毫秒延时倍乘数，在OS下，代表每个节拍的ms数

#if SYSTEM_SUPPORT_OS   // 如果需要支持OS
/* OS相关函数实现 ------------------------------------------------------------*/

/**
 * @brief  禁止OS任务调度
 * @param  None
 * @retval None
 * @note   在us级延时时调用，防止任务切换影响延时精度
 */
void delay_osschedlock(void)
{
#ifdef CPU_CFG_CRITICAL_METHOD   // 使用UCOSIII
	OS_ERR err; 
	OSSchedLock(&err);
#else                            // 否则UCOSII
	OSSchedLock();
#endif
}

/**
 * @brief  恢复OS任务调度
 * @param  None
 * @retval None
 * @note   us级延时结束后调用，恢复正常任务调度
 */
void delay_osschedunlock(void)
{	
#ifdef CPU_CFG_CRITICAL_METHOD   // 使用UCOSIII
	OS_ERR err; 
	OSSchedUnlock(&err);
#else                            // 否则UCOSII
	OSSchedUnlock();
#endif
}

/**
 * @brief  调用OS自带的延时函数进行延时
 * @param  ticks: 延时的节拍数
 * @retval None
 * @note   利用OS提供的任务延时机制，不占用CPU
 */
void delay_ostimedly(u32 ticks)
{
#ifdef CPU_CFG_CRITICAL_METHOD
	OS_ERR err; 
	OSTimeDly(ticks, OS_OPT_TIME_PERIODIC, &err); // UCOSIII延时函数周期模式
#else
	OSTimeDly(ticks);                              // UCOSII延时
#endif 
}

/**
 * @brief  SysTick中断服务函数，OS使用
 * @param  None
 * @retval None
 * @note   当使用OS时，SysTick用于OS时钟节拍
 *         需要在中断中调用OS相关函数
 */
void SysTick_Handler(void)
{	
	if(delay_osrunning == 1)  // OS开始跑了，才执行正常的调度处理
	{
		OSIntEnter();         // 进入中断
		OSTimeTick();         // 调用ucos的时钟服务程序               
		OSIntExit();          // 触发任务切换软中断
	}
}
#endif

/**
 * @brief  延时初始化函数
 * @param  SYSCLK: 系统时钟频率(MHz)
 * @retval None
 * @note   初始化SysTick定时器，配置延时相关参数
 *         STM32F407: SYSCLK = 168MHz
 *         SysTick时钟固定为HCLK的1/8 = 21MHz
 */
void delay_init(u8 SYSCLK)
{
#if SYSTEM_SUPPORT_OS 
	u32 reload;
#endif
 	/* 配置SysTick时钟源为HCLK/8 */
	SysTick_CLKSourceConfig(SysTick_CLKSource_HCLK_Div8); 
	
	/* 计算微秒延时因子: 21MHz / 1000000 = 21 */
	fac_us = SYSCLK / 8;
	
#if SYSTEM_SUPPORT_OS
	/* OS模式下的配置 */
	reload = SYSCLK / 8;                          // 每秒钟的计数次数 单位为M	   
	reload *= 1000000 / delay_ostickspersec;     // 根据delay_ostickspersec设定溢出时间
	                                              // reload为24位寄存器,最大值:16777216,在168M下,约为0.7989s左右	
	fac_ms = 1000 / delay_ostickspersec;         // 代表OS可以延时的最少单位	   
	
	SysTick->CTRL |= SysTick_CTRL_TICKINT_Msk;   // 开启SYSTICK中断
	SysTick->LOAD = reload;                      // 每1/delay_ostickspersec秒中断一次	
	SysTick->CTRL |= SysTick_CTRL_ENABLE_Msk;    // 开启SYSTICK    
#else
	/* 裸机模式下的配置 */
	fac_ms = (u16)fac_us * 1000;                 // 非OS下,代表每个ms需要的systick时钟数   
#endif
}

#if SYSTEM_SUPPORT_OS
/**
 * @brief  微秒级延时函数 (OS版本)
 * @param  nus: 要延时的us数 (0~204522252)
 * @retval None
 * @note   在OS环境下，通过读取SysTick计数器实现精确延时
 */
void delay_us(u32 nus)
{		
	u32 ticks;
	u32 told, tnow, tcnt = 0;
	u32 reload = SysTick->LOAD;    // LOAD的值	    	 
	
	ticks = nus * fac_us;          // 需要的节拍数 
	delay_osschedlock();           // 阻止OS调度，防止影响us延时
	told = SysTick->VAL;          // 刚进入时的计数器值
	
	while(1)
	{
		tnow = SysTick->VAL;	
		if(tnow != told)
		{	    
			if(tnow < told)
				tcnt += told - tnow;      // 正常计数
			else 
				tcnt += reload - tnow + told;  // 重新装载过了
			told = tnow;
			if(tcnt >= ticks) break;      // 时间超过/等于要延迟的时间,则退出
		}  
	}
	delay_osschedunlock();            // 恢复OS调度
}

/**
 * @brief  毫秒级延时函数 (OS版本)
 * @param  nms: 要延时的ms数 (0~65535)
 * @retval None
 * @note   优先使用OS延时机制，不足部分用us延时补充
 */
void delay_ms(u16 nms)
{	
	if(delay_osrunning && delay_osintnesting == 0) // 如果OS已经在跑了,并且不是在中断里面
	{		 
		if(nms >= fac_ms)                           // 延时的时间大于OS的最少时间周期 
		{ 
   			delay_ostimedly(nms / fac_ms);          // OS延时
		}
		nms %= fac_ms;                              // OS已经无法提供这么小的延时了,用普通方式延时    
	}
	delay_us((u32)(nms * 1000));                    // 普通方式延时
}

#else  /* 裸机版本 */

/**
 * @brief  微秒级延时函数 (裸机版本)
 * @param  nus: 要延时的us数
 * @retval None
 * @note   注意:nus的值,不要大于798915us(最大值2^24/fac_us@fac_us=21)
 */
void delay_us(u32 nus)
{		
	u32 temp;	    	 
	SysTick->LOAD = nus * fac_us;                 // 时间加载	  		 
	SysTick->VAL = 0x00;                          // 清空计数器
	SysTick->CTRL |= SysTick_CTRL_ENABLE_Msk;     // 开始倒数 	 
	do
	{
		temp = SysTick->CTRL;
	} while((temp & 0x01) && !(temp & (1 << 16))); // 等待时间到达   
	SysTick->CTRL &= ~SysTick_CTRL_ENABLE_Msk;     // 关闭计数器
	SysTick->VAL = 0X00;                           // 清空计数器 
}

/**
 * @brief  毫秒级延时函数内部实现 (裸机版本)
 * @param  nms: 要延时的ms数
 * @retval None
 * @note   SysTick->LOAD为24位寄存器,因此,最大延时为:
 *         nms<=0xffffff*8*1000/SYSCLK (SYSCLK单位为Hz,nms单位为ms)
 *         在168M条件下,nms<=798ms 
 */
void delay_xms(u16 nms)
{	 		  	  
	u32 temp;		   
	SysTick->LOAD = (u32)nms * fac_ms;            // 时间加载(SysTick->LOAD为24bit)
	SysTick->VAL = 0x00;                          // 清空计数器
	SysTick->CTRL |= SysTick_CTRL_ENABLE_Msk;     // 开始倒数 
	do
	{
		temp = SysTick->CTRL;
	} while((temp & 0x01) && !(temp & (1 << 16))); // 等待时间到达   
	SysTick->CTRL &= ~SysTick_CTRL_ENABLE_Msk;     // 关闭计数器
	SysTick->VAL = 0X00;                           // 清空计数器	  	    
} 

/**
 * @brief  毫秒级延时函数 (裸机版本)
 * @param  nms: 要延时的ms数 (0~65535)
 * @retval None
 * @note   通过分段延时实现长时间延时
 *         考虑到某些客户可能超频使用,超频到248M的时候,delay_xms最多只能延时541ms左右了
 */
void delay_ms(u16 nms)
{	
	u8 repeat;
	u16 remain;
	
	repeat = nms / 540;                        // 这里用540，是考虑到某些客户可能超频使用
	remain = nms % 540;
	
	while(repeat)
	{
		delay_xms(540);
		repeat--;
	}
	if(remain) delay_xms(remain);
} 
#endif
