Windows PowerShell
版权所有（C） Microsoft Corporation。保留所有权利。

安装最新的 PowerShell，了解新功能和改进！https://aka.ms/PSWindows

PS C:\Users\<USER>\Users\LHQ> git clone https://github.com/justlovemaki/AIClient-2-API
Cloning into 'AIClient-2-API'...
remote: Enumerating objects: 248, done.
remote: Counting objects: 100% (121/121), done.
remote: Compressing objects: 100% (73/73), done.
remote: Total 248 (delta 72), reused 80 (delta 48), pack-reused 127 (from 1)
Receiving objects: 100% (248/248), 371.62 KiB | 23.23 MiB/s, done.
Resolving deltas: 100% (125/125), done.
PS C:\Users\<USER>\Users\LHQ\AIClient-2-API> npm install
npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
npm warn deprecated supertest@6.3.4: Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net
npm warn deprecated node-domexception@1.0.0: Use your platform's native DOMException instead
npm warn deprecated superagent@8.1.2: Please upgrade to superagent v10.2.2+, see release notes at https://github.com/forwardemail/superagent/releases/tag/v10.2.2 - maintenance is supported by Forward Email @ https://forwardemail.net

added 441 packages in 3s

58 packages are looking for funding
  run `npm fund` for details
PS C:\Users\<USER>\AIClient-2-API> node src/api-server.js --model-provider claude-kiro-oauth
[Config] Loaded configuration from config.json
[System Prompt] Specified system prompt file not found: C:\Users\<USER>\AIClient-2-API\input_system_prompt.txt
[Initialization] Initializing service adapter for gemini-cli-oauth...
[Gemini] Initializing Gemini API Service...
[Initialization] Initializing service adapter for openai-custom...
[Initialization Warning] Failed to initialize service adapter for openai-custom: OpenAI API Key is required for OpenAIApiService.
[Initialization] Initializing service adapter for claude-custom...
[Initialization Warning] Failed to initialize service adapter for claude-custom: Claude API Key is required for ClaudeApiService.
[Initialization] Initializing service adapter for claude-kiro-oauth...
[Kiro] Initializing Gemini API Service...
[Kiro Auth] Attempting to load credentials from specified file: C:\Users\<USER>\.aws\sso\cache\kiro-auth-token.json
[Gemini Auth] Credentials file 'C:\Users\<USER>\.gemini\oauth_creds.json' not found. Starting new authentication flow...
                                                                                                                        [Gemini Auth] Please open this URL in your browser to authenticate:                                                     https://accounts.google.com/o/oauth2/v2/auth?access_type=offline&scope=https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcloud-platform&response_type=code&client_id=************-oo8ft2oprdrnp9e3aqf6av3hmdib135j.apps.googleusercontent.com&redirect_uri=http%3A%2F%2Flocalhost%3A8085

node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use 127.0.0.1:3000
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at GetAddrInfoReqWrap.callback (node:net:2205:7)
    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '127.0.0.1',
  port: 3000
}

Node.js v22.16.0
PS C:\Users\<USER>\AIClient-2-API> curl http://localhost:3000/health


StatusCode        : 200
StatusDescription : OK
Content           : {"status":"ok","timestamp":"2025-08-12T13:14:19.601Z","provider":"kiro-api","version":"1.0.0"}
RawContent        : HTTP/1.1 200 OK
                    Access-Control-Allow-Origin: *
                    Connection: keep-alive
                    Keep-Alive: timeout=5
                    Content-Length: 94
                    Content-Type: application/json; charset=utf-8
                    Date: Tue, 12 Aug 2025 13:14:19 GMT
                    ...
Forms             : {}
Headers           : {[Access-Control-Allow-Origin, *], [Connection, keep-alive], [Keep-Alive, timeout=5], [Content-Leng                     th, 94]...}                                                                                         Images            : {}                                                                                                  InputFields       : {}                                                                                                  Links             : {}
ParsedHtml        : mshtml.HTMLDocumentClass
RawContentLength  : 94



PS C:\Users\<USER>\AIClient-2-API> curl http://localhost:3000/v1/models
curl : {"error":"Invalid API key"}
所在位置 行:1 字符: 1
+ curl http://localhost:3000/v1/models
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (System.Net.HttpWebRequest:HttpWebRequest) [Invoke-WebRequest]，WebExce
    ption
    + FullyQualifiedErrorId : WebCmdletWebResponseException,Microsoft.PowerShell.Commands.InvokeWebRequestCommand
PS C:\Users\<USER>\AIClient-2-API> curl http://localhost:3000/v1/models
curl : {"error":"Invalid API key"}
所在位置 行:1 字符: 1
+ curl http://localhost:3000/v1/models
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (System.Net.HttpWebRequest:HttpWebRequest) [Invoke-WebRequest]，WebExce
    ption
    + FullyQualifiedErrorId : WebCmdletWebResponseException,Microsoft.PowerShell.Commands.InvokeWebRequestCommand
PS C:\Users\<USER>\AIClient-2-API> curl http://localhost:3000/health


StatusCode        : 200
StatusDescription : OK
Content           : {"status":"ok","timestamp":"2025-08-12T13:14:53.717Z","provider":"kiro-api","version":"1.0.0"}
RawContent        : HTTP/1.1 200 OK
                    Access-Control-Allow-Origin: *
                    Connection: keep-alive
                    Keep-Alive: timeout=5
                    Content-Length: 94
                    Content-Type: application/json; charset=utf-8
                    Date: Tue, 12 Aug 2025 13:14:53 GMT
                    ...
Forms             : {}
Headers           : {[Access-Control-Allow-Origin, *], [Connection, keep-alive], [Keep-Alive, timeout=5], [Content-Leng
                    th, 94]...}
Images            : {}
InputFields       : {}
Links             : {}
ParsedHtml        : mshtml.HTMLDocumentClass
RawContentLength  : 94



PS C:\Users\<USER>\AIClient-2-API> node src/api-server.js --model-provider claude-kiro-oauth
[Config] Loaded configuration from config.json
[System Prompt] Specified system prompt file not found: C:\Users\<USER>\AIClient-2-API\input_system_prompt.txt
[Initialization] Initializing service adapter for gemini-cli-oauth...
[Gemini] Initializing Gemini API Service...
[Initialization] Initializing service adapter for openai-custom...
[Initialization Warning] Failed to initialize service adapter for openai-custom: OpenAI API Key is required for OpenAIApiService.
[Initialization] Initializing service adapter for claude-custom...
[Initialization Warning] Failed to initialize service adapter for claude-custom: Claude API Key is required for ClaudeApiService.
[Initialization] Initializing service adapter for claude-kiro-oauth...
[Kiro] Initializing Gemini API Service...
[Kiro Auth] Attempting to load credentials from specified file: C:\Users\<USER>\.aws\sso\cache\kiro-auth-token.json
[Gemini Auth] Credentials file 'C:\Users\<USER>\.gemini\oauth_creds.json' not found. Starting new authentication flow...

[Gemini Auth] Please open this URL in your browser to authenticate:
https://accounts.google.com/o/oauth2/v2/auth?access_type=offline&scope=https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcloud-platform&response_type=code&client_id=************-oo8ft2oprdrnp9e3aqf6av3hmdib135j.apps.googleusercontent.com&redirect_uri=http%3A%2F%2Flocalhost%3A8085

node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use 127.0.0.1:3000
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at GetAddrInfoReqWrap.callback (node:net:2205:7)
    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '127.0.0.1',
  port: 3000
}

Node.js v22.16.0
PS C:\Users\<USER>\AIClient-2-API> curl http://localhost:3000/v1/models
curl : {"error":"Invalid API key"}
所在位置 行:1 字符: 1
+ curl http://localhost:3000/v1/models
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (System.Net.HttpWebRequest:HttpWebRequest) [Invoke-WebRequest]，WebExce
    ption
    + FullyQualifiedErrorId : WebCmdletWebResponseException,Microsoft.PowerShell.Commands.InvokeWebRequestCommand
PS C:\Users\<USER>\AIClient-2-API>

