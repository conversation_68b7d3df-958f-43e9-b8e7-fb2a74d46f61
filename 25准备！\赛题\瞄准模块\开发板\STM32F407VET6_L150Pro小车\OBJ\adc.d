..\obj\adc.o: ..\HARDWARE\adc.c
..\obj\adc.o: ..\HARDWARE\adc.h
..\obj\adc.o: ..\SYSTEM\sys\sys.h
..\obj\adc.o: ..\USER\stm32f4xx.h
..\obj\adc.o: ..\CORE\core_cm4.h
..\obj\adc.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\stdint.h
..\obj\adc.o: ..\CORE\core_cmInstr.h
..\obj\adc.o: ..\CORE\core_cmFunc.h
..\obj\adc.o: ..\CORE\core_cm4_simd.h
..\obj\adc.o: ..\USER\system_stm32f4xx.h
..\obj\adc.o: ..\CORE\arm_math.h
..\obj\adc.o: ..\CORE\core_cm4.h
..\obj\adc.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\string.h
..\obj\adc.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\math.h
..\obj\adc.o: ..\USER\stm32f4xx_conf.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_adc.h
..\obj\adc.o: ..\USER\stm32f4xx.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_crc.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_dbgmcu.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_dma.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_exti.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_flash.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_gpio.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_i2c.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_iwdg.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_pwr.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_rcc.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_rtc.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_sdio.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_spi.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_syscfg.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_tim.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_usart.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_wwdg.h
..\obj\adc.o: ..\FWLIB\inc\misc.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_cryp.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_hash.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_rng.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_can.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_dac.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_dcmi.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_fsmc.h
..\obj\adc.o: ..\BALANCE\system.h
..\obj\adc.o: ..\SYSTEM\delay\delay.h
..\obj\adc.o: ..\SYSTEM\usart\usart.h
..\obj\adc.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\stdio.h
..\obj\adc.o: ..\BALANCE\balance.h
..\obj\adc.o: ..\BALANCE\system.h
..\obj\adc.o: ..\HARDWARE\led.h
..\obj\adc.o: ..\HARDWARE\oled.h
..\obj\adc.o: ..\HARDWARE\usartx.h
..\obj\adc.o: ..\HARDWARE\adc.h
..\obj\adc.o: ..\HARDWARE\can.h
..\obj\adc.o: ..\HARDWARE\motor.h
..\obj\adc.o: ..\HARDWARE\timer.h
..\obj\adc.o: ..\HARDWARE\encoder.h
..\obj\adc.o: ..\BALANCE\show.h
..\obj\adc.o: ..\HARDWARE\pstwo.h
..\obj\adc.o: ..\HARDWARE\key.h
..\obj\adc.o: ..\BALANCE\robot_select_init.h
..\obj\adc.o: ..\HARDWARE\MPU6050\I2C.h
..\obj\adc.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\stdbool.h
..\obj\adc.o: ..\HARDWARE\MPU6050\MPU6050.h
..\obj\adc.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\stdlib.h
..\obj\adc.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\stdarg.h
