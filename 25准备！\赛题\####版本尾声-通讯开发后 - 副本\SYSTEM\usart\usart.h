/**
 ******************************************************************************
 * @file    usart.h
 * <AUTHOR> Team & E题开发团队
 * @version V1.6
 * @date    2025-08-02
 * @brief   STM32F407 USART1串口通信驱动头文件
 *          
 *          本文件定义了USART1串口的初始化和K230通信接口
 *          专门用于接收K230发送的6字节控制协议数据包
 * 
 * @note    功能特性:
 *          - USART1串口初始化 (PA9/PA10)
 *          - K230协议接收和解析
 *          - 6字节数据包状态机处理
 *          - 校验和验证机制
 *          
 *          硬件连接:
 *          - PA9:  USART1_TX (发送到K230 RX)
 *          - PA10: USART1_RX (接收K230 TX)
 *          - 波特率: 115200 (固定)
 ******************************************************************************
 * @attention
 * 本程序基于正点原子STM32开发板例程修改
 * 原作者: 正点原子@ALIENTEK (www.openedv.com)
 * 修改: 删除printf支持，专用于K230通信
 ******************************************************************************
 */

#ifndef __USART_H
#define __USART_H

#include "stdio.h"	
#include "stm32f4xx_conf.h"
#include "sys.h"

/* 串口配置宏定义 ------------------------------------------------------------*/

/**
 * @brief  串口接收缓存长度定义
 * @note   定义串口接收缓存区大小，单位：字节
 *         保留原有缓存以兼容旧代码
 */
#define USART_REC_LEN    200

/**
 * @brief  串口接收使能控制
 * @note   1: 使能USART1接收中断
 *         0: 禁用USART1接收中断
 */
#define EN_USART1_RX     1

/* K230通信协议常量定义 -----------------------------------------------------*/

/**
 * @brief  K230协议帧格式常量
 */
#define K230_FRAME_HEADER    0xAA    /**< 帧头标识 */
#define K230_FRAME_TAIL      0xFF    /**< 帧尾标识 */
#define K230_FRAME_LENGTH    6       /**< 数据包长度 */

/**
 * @brief  K230控制指令定义
 */
#define K230_CMD_LEFT        1       /**< 左转指令 */
#define K230_CMD_RIGHT       2       /**< 右转指令 */
#define K230_CMD_STOP        3       /**< 停止指令 */
#define K230_CMD_FIRE        4       /**< 发射指令 */

/* 全局变量声明 --------------------------------------------------------------*/

#if EN_USART1_RX
extern u8  USART_RX_BUF[USART_REC_LEN]; // 接收缓冲,最大USART_REC_LEN个字节
extern volatile u8 k230_rx_state;        // K230接收状态机状态
extern volatile u8 k230_rx_buffer[6];    // K230数据包缓冲区
extern volatile u8 k230_rx_index;        // K230接收索引
extern volatile u8 k230_frame_ready;     // K230数据包就绪标志
#endif

/* 函数声明 ------------------------------------------------------------------*/

/**
 * @brief  USART1串口初始化
 * @param  bound: 波特率设置
 * @retval None
 * @note   初始化USART1串口，配置GPIO和中断
 *         专用于K230通信，固定115200波特率
 */
void uart_init(u32 bound);

/**
 * @brief  K230协议字节解析函数
 * @param  data: 接收到的字节
 * @retval None
 * @note   由USART中断调用，实现6字节协议状态机解析
 */
void K230_ParseByte(u8 data);

/**
 * @brief  获取K230数据包状态
 * @param  None
 * @retval u8: 1=数据包就绪, 0=无数据包
 * @note   主循环中检查是否有新的K230数据包
 */
__inline u8 K230_IsFrameReady(void) {
    return k230_frame_ready;
}

/**
 * @brief  清除K230数据包就绪标志
 * @param  None
 * @retval None
 * @note   处理完数据包后调用此函数
 */
__inline void K230_ClearFrameFlag(void) {
    k230_frame_ready = 0;
}

/**
 * @brief  获取K230接收到的指令
 * @param  None
 * @retval u8: 控制指令 (1=LEFT, 2=RIGHT, 3=STOP, 4=FIRE)
 * @note   仅在K230_IsFrameReady()返回1时调用有效
 */
__inline u8 K230_GetCommand(void) {
    return k230_rx_buffer[1];
}

/**
 * @brief  获取K230接收到的像素偏差值
 * @param  None
 * @retval s16: 有符号像素偏差值
 * @note   仅在K230_IsFrameReady()返回1时调用有效
 *         负数表示左偏，正数表示右偏
 */
__inline s16 K230_GetPixelOffset(void) {
    u16 unsigned_offset = (k230_rx_buffer[2] << 8) | k230_rx_buffer[3];
    // 转换为有符号数
    if(unsigned_offset > 32767) {
        return (s16)(unsigned_offset - 65536);
    } else {
        return (s16)unsigned_offset;
    }
}

#endif
