#include "sys.h"
#include "delay.h"
#include "usart.h"
#include "led.h"


//ALIENTEK 探索者STM32F407开发板 实验4
//串口屏控制舵机系统
//技术支持：www.openedv.com
//淘宝店铺：http://eboard.taobao.com
//广州市星翼电子科技有限公司  
//作者：正点原子 @ALIENTEK


int main(void)
{ 
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);//设置系统中断优先级分组2
	delay_init(168);		//延时初始化 
	uart_init(115200);		//串口1初始化，与舵机通信
	uart2_init(115200);		//串口2初始化，与串口屏通信
	LED_Init();		  		//初始化与LED连接的硬件接口  
	
	delay_ms(1000);			//等待系统稳定
	
	// 初始化激光云台控制系统
	LaserSystem_Init();
	
	while(1)
	{
		// 处理串口屏命令
		Screen_ProcessCommand();
		
		// LED闪烁，指示系统运行
		LED0=!LED0;
		delay_ms(1);  // 1ms延迟
	}
}
