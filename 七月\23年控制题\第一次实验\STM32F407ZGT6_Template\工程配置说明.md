# STM32F407ZGT6工程模板配置说明

## 🎯 Keil工程配置详解

### 1. 目标设备配置
- **Device**: STM32F407ZGTx
- **Vendor**: STMicroelectronics
- **Pack**: Keil.STM32F4xx_DFP.2.14.0
- **CPU**: Cortex-M4 FPU2 CLOCK(12000000)

### 2. 编译器设置
- **Toolchain**: ARM Compiler 5.06 update 5 (build 528)
- **Optimization**: Level 0 (调试时)
- **C/C++**: C99 Mode
- **Warnings**: All Warnings

### 3. 调试器配置

#### ST-Link设置
```
Debug → Use: ST-Link Debugger
Settings:
  - Port: SW (SWD模式)
  - Max Clock: 1.8 MHz
  - Reset and Run: 勾选
  - Verify Code Download: 勾选
```

#### Flash下载设置
```
Flash Download:
  - Erase Sectors: 勾选
  - Program: 勾选
  - Verify: 勾选
  - Reset and Run: 勾选
```

### 4. 内存配置
```
IRAM1: 0x20000000 Size: 0x20000 (128KB)
IRAM2: 0x10000000 Size: 0x10000 (64KB)
IROM1: 0x08000000 Size: 0x100000 (1MB)
```

### 5. 预处理器定义
```
STM32F40_41xxx
USE_STDPERIPH_DRIVER
```

### 6. 包含路径
```
.\User
.\System
.\Start
.\Library
```

## 🔧 常见问题解决

### 1. 编译错误
**问题**: 找不到头文件
**解决**: 检查Include Paths设置，确保包含所有必要路径

**问题**: 未定义的引用
**解决**: 检查stm32f4xx_conf.h中是否包含了所需的外设头文件

### 2. 下载错误
**问题**: Flash Download failed
**解决**: 
- 检查ST-Link连接
- 降低下载时钟频率到1.8MHz
- 检查目标板供电

**问题**: Target DLL has been cancelled
**解决**:
- 重新安装ST-Link驱动
- 更换USB线缆
- 检查硬件连接

### 3. 调试问题
**问题**: 无法进入调试模式
**解决**:
- 确认调试器配置正确
- 检查Reset and Run设置
- 验证代码下载成功

## 📋 工程文件说明

### 核心文件
- `STM32F407ZGT6_Template.uvprojx`: Keil工程文件
- `STM32F407ZGT6_Template.uvoptx`: 工程选项文件（自动生成）
- `STM32F407ZGT6_Template.uvguix.*`: 用户界面配置（自动生成）

### 源代码文件
- `User/main.c`: 主程序
- `User/stm32f4xx_it.c`: 中断服务程序
- `System/Delay.c`: 延时功能
- `Start/startup_stm32f40_41xxx.s`: 启动文件
- `Start/system_stm32f4xx.c`: 系统初始化

### 配置文件
- `User/stm32f4xx_conf.h`: 外设库配置
- `User/stm32f4xx.h`: STM32F4xx总头文件
- `User/stm32f4xx_it.h`: 中断服务程序头文件

## 🚀 快速配置步骤

### 新建项目时
1. 复制整个模板文件夹
2. 重命名文件夹和.uvprojx文件
3. 修改工程文件中的TargetName
4. 打开Keil，加载工程
5. 检查设备配置和调试器设置
6. 编译测试

### 添加新功能时
1. 在stm32f4xx_conf.h中启用相关外设
2. 添加对应的.c和.h文件到工程
3. 配置Include路径
4. 编写初始化和功能代码
5. 编译调试

## ⚙️ 高级配置

### 1. 优化设置
- **调试阶段**: Optimization Level 0
- **发布阶段**: Optimization Level 2 或 3
- **代码大小优化**: Optimize for Size

### 2. 堆栈配置
```
Stack_Size      EQU     0x00000400  ; 1KB
Heap_Size       EQU     0x00000200  ; 512B
```

### 3. 中断向量表
- 位置: 0x08000000 (Flash起始地址)
- 大小: 0x188 (98个中断向量)

### 4. 时钟配置验证
```c
// 在main函数中添加以下代码验证时钟配置
RCC_ClocksTypeDef RCC_Clocks;
RCC_GetClocksFreq(&RCC_Clocks);
// 通过调试器查看RCC_Clocks结构体内容
```

---

**配置完成后，工程即可正常编译、下载和调试！**
