# STM32F407ZGT6开发板引脚功能对照表

> **文档创建日期**: 2025年1月15日  
> **适用开发板**: STM32F407ZGT6开发板  
> **文档用途**: 引脚与外设功能对照参考  

## 1. LED和按键引脚分配

### 1.1 LED引脚

| 引脚名称 | 端口 | 功能描述 | 电气特性 | 控制逻辑 |
|---------|------|---------|---------|---------|
| PF9     | GPIOF | LED0 | 共阳极，通过510Ω电阻连接到3V3 | 输出低电平点亮，高电平熄灭 |
| PF10    | GPIOF | LED1 | 共阳极，通过510Ω电阻连接到3V3 | 输出低电平点亮，高电平熄灭 |

### 1.2 按键引脚

| 引脚名称 | 端口 | 功能描述 | 电气特性 | 读取逻辑 |
|---------|------|---------|---------|---------|
| PE4     | GPIOE | KEY0 (K0) | 下拉输入，按键接GND | 按下时为低电平，需上拉 |
| PE3     | GPIOE | KEY1 (K1) | 下拉输入，按键接GND | 按下时为低电平，需上拉 |
| PA0     | GPIOA | WK_UP | 上拉输入，按键接3V3 | 按下时为高电平，需下拉 |

## 2. 通信接口引脚

### 2.1 串口引脚

| 引脚名称 | 端口 | 功能描述 | 备注 |
|---------|------|---------|------|
| PA9     | GPIOA | USART1_TX | 可用于调试输出 |
| PA10    | GPIOA | USART1_RX | 可用于调试输入 |
| PA2     | GPIOA | USART2_TX | 可用于外设通信 |
| PA3     | GPIOA | USART2_RX | 可用于外设通信 |

### 2.2 SPI接口引脚

| 引脚名称 | 端口 | 功能描述 | 备注 |
|---------|------|---------|------|
| PA5     | GPIOA | SPI1_SCK | 时钟线 |
| PA6     | GPIOA | SPI1_MISO | 主机输入 |
| PA7     | GPIOA | SPI1_MOSI | 主机输出 |
| PA4     | GPIOA | SPI1_NSS | 片选信号 |

### 2.3 I2C接口引脚

| 引脚名称 | 端口 | 功能描述 | 备注 |
|---------|------|---------|------|
| PB6     | GPIOB | I2C1_SCL | 时钟线 |
| PB7     | GPIOB | I2C1_SDA | 数据线 |

## 3. 其他常用外设引脚

### 3.1 定时器引脚

| 引脚名称 | 端口 | 功能描述 | 备注 |
|---------|------|---------|------|
| PA8     | GPIOA | TIM1_CH1 | 可用于PWM输出 |
| PB0     | GPIOB | TIM3_CH3 | 可用于PWM输出 |
| PB1     | GPIOB | TIM3_CH4 | 可用于PWM输出 |

### 3.2 ADC引脚

| 引脚名称 | 端口 | 功能描述 | 备注 |
|---------|------|---------|------|
| PA1     | GPIOA | ADC1_IN1 | 模拟输入通道1 |
| PC0     | GPIOC | ADC1_IN10 | 模拟输入通道10 |
| PC1     | GPIOC | ADC1_IN11 | 模拟输入通道11 |

## 4. 系统和电源引脚

| 引脚名称 | 功能描述 | 备注 |
|---------|---------|------|
| NRST    | 系统复位 | 低电平有效 |
| BOOT0   | 启动模式选择 | 高电平：系统存储器，低电平：Flash |
| VDD     | 电源正极 | 3.3V |
| VSS     | 电源地 | GND |
| VBAT    | 备用电池电源 | 用于RTC和备份寄存器 |

## 5. 晶振引脚

| 引脚名称 | 功能描述 | 备注 |
|---------|---------|------|
| OSC_IN  | 外部晶振输入 | 连接8MHz晶振 |
| OSC_OUT | 外部晶振输出 | 连接8MHz晶振 |
| OSC32_IN | 32.768KHz晶振输入 | 用于RTC |
| OSC32_OUT | 32.768KHz晶振输出 | 用于RTC |

## 6. 按键控制LED功能实现

### 6.1 功能描述

- K0按键(PE4)控制LED0(PF9)的亮灭
- K1按键(PE3)控制LED1(PF10)的亮灭

### 6.2 GPIO配置要点

#### LED配置
- 配置PF9和PF10为推挽输出模式
- 设置输出速度为50MHz
- 初始状态设置为高电平（LED熄灭）

```c
// LED GPIO配置示例
GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9 | GPIO_Pin_10;
GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
GPIO_Init(GPIOF, &GPIO_InitStructure);
```

#### 按键配置
- 配置PE4和PE3为输入模式
- 设置为上拉输入（按键接GND）

```c
// 按键GPIO配置示例
GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4 | GPIO_Pin_3;
GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;
GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
GPIO_Init(GPIOE, &GPIO_InitStructure);
```

### 6.3 控制逻辑

- 检测按键状态：`GPIO_ReadInputDataBit(GPIOE, GPIO_Pin_4)` 和 `GPIO_ReadInputDataBit(GPIOE, GPIO_Pin_3)`
- 控制LED状态：`GPIO_ResetBits(GPIOF, GPIO_Pin_9)` (点亮) 和 `GPIO_SetBits(GPIOF, GPIO_Pin_9)` (熄灭)

## 7. 注意事项

1. **LED控制**：由于LED为共阳极连接，输出低电平点亮LED，输出高电平熄灭LED
2. **按键读取**：按键按下时为低电平，松开时为高电平（通过上拉电阻）
3. **按键消抖**：实际应用中需要进行按键消抖处理，避免误触发
4. **时钟配置**：使用外设前需要先使能对应的时钟，如：`RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOF, ENABLE)`

---

**文档创建者**: 竞赛任务分析专家  
**参考资料**: STM32F407ZGT6开发板原理图  
**适用项目**: LED按键控制程序
