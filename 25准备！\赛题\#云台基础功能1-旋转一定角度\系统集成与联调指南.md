# 系统集成与联调指南

## 一、系统架构概述

本指南详细介绍STM32F407电机控制系统与K230视觉识别系统的集成方法，确保在2秒内完成自动瞄准任务。

### 系统组成
- **主控制器**: STM32F407（电机控制+激光控制）
- **视觉处理**: K230（目标识别+偏差计算）
- **通信方式**: UART 115200bps
- **控制算法**: PID闭环控制

### 数据流向
```
摄像头 → K230(识别) → UART → STM32(控制) → 电机/激光
         ↑                                      ↓
         └──────── 视觉反馈闭环 ←───────────────┘
```

## 二、通信协议规范

### 2.1 硬件连接

#### 接线定义
```
K230端:
- TX  → STM32 PA3 (USART2_RX)
- RX  → STM32 PA2 (USART2_TX)
- GND → STM32 GND

注意：检查电平兼容性（3.3V）
```

### 2.2 通信协议详细设计

#### 帧格式定义
```
┌─────────┬─────────┬─────────┬──────────┐
│ Header  │ Command │  Data   │ Checksum │
│ (0x55)  │ (1byte) │ (1byte) │ (1byte)  │
└─────────┴─────────┴─────────┴──────────┘
```

#### 命令集定义
```c
// 电机控制命令（K230→STM32）
#define CMD_FAST_LEFT   0x01  // 快速左转
#define CMD_SLOW_LEFT   0x02  // 慢速左转
#define CMD_STOP        0x00  // 停止/对准
#define CMD_SLOW_RIGHT  0x03  // 慢速右转
#define CMD_FAST_RIGHT  0x04  // 快速右转

// 系统控制命令（K230→STM32）
#define CMD_LASER_ON    0x10  // 开启激光
#define CMD_LASER_OFF   0x11  // 关闭激光
#define CMD_SYS_RESET   0x20  // 系统复位

// 状态反馈命令（STM32→K230）
#define CMD_ACK         0x80  // 确认收到
#define CMD_BUSY        0x81  // 系统忙碌
#define CMD_ERROR       0x82  // 错误状态
#define CMD_READY       0x83  // 系统就绪
```

### 2.3 协议实现

#### STM32端接收处理
```c
// 接收缓冲区
#define RX_BUFFER_SIZE 64
uint8_t rx_buffer[RX_BUFFER_SIZE];
uint8_t rx_index = 0;
uint8_t frame_buffer[4];
uint8_t frame_index = 0;

// UART中断接收
void USART2_IRQHandler(void) {
    if (USART_GetITStatus(USART2, USART_IT_RXNE)) {
        uint8_t data = USART_ReceiveData(USART2);
        
        // 状态机解析
        if (frame_index == 0 && data == 0x55) {
            // 帧头
            frame_buffer[frame_index++] = data;
        } else if (frame_index > 0 && frame_index < 4) {
            // 数据接收
            frame_buffer[frame_index++] = data;
            
            if (frame_index == 4) {
                // 完整帧接收
                if (CheckFrame(frame_buffer)) {
                    ProcessCommand(frame_buffer[1], frame_buffer[2]);
                }
                frame_index = 0;
            }
        } else {
            // 错误恢复
            frame_index = 0;
        }
    }
}

// 校验函数
uint8_t CheckFrame(uint8_t* frame) {
    uint8_t checksum = frame[0] + frame[1] + frame[2];
    return (checksum & 0xFF) == frame[3];
}
```

#### K230端发送实现
```python
import ustruct

class SerialProtocol:
    def __init__(self, uart):
        self.uart = uart
        self.HEADER = 0x55
        
    def send_command(self, cmd, data=0):
        """发送命令帧"""
        # 计算校验和
        checksum = (self.HEADER + cmd + data) & 0xFF
        
        # 构造帧
        frame = ustruct.pack('BBBB', self.HEADER, cmd, data, checksum)
        
        # 发送
        self.uart.write(frame)
        
        # 调试输出
        print(f"Sent: {frame.hex()}")
    
    def receive_ack(self, timeout=100):
        """接收确认（可选）"""
        start = time.ticks_ms()
        buffer = bytearray(4)
        index = 0
        
        while time.ticks_diff(time.ticks_ms(), start) < timeout:
            if self.uart.any():
                data = self.uart.read(1)[0]
                
                if index == 0 and data == self.HEADER:
                    buffer[index] = data
                    index += 1
                elif index > 0 and index < 4:
                    buffer[index] = data
                    index += 1
                    
                    if index == 4:
                        # 验证校验和
                        if self._check_frame(buffer):
                            return buffer[1]  # 返回命令
                        index = 0
        
        return None
```

## 三、PID控制闭环实现

### 3.1 控制系统架构

```
┌─────────────┐     ┌──────────────┐     ┌─────────────┐
│   目标位置   │ --> │  PID控制器   │ --> │  电机速度   │
│  (画面中心)  │     │ (K230计算)   │     │ (STM32执行) │
└─────────────┘     └──────────────┘     └─────────────┘
        ↑                                         │
        │                                         │
        └────────── 实际位置(视觉反馈) ←───────────┘
```

### 3.2 K230端PID实现

```python
class PIDController:
    def __init__(self, kp=0.5, ki=0.1, kd=0.05, dt=0.033):
        self.kp = kp
        self.ki = ki  
        self.kd = kd
        self.dt = dt  # 采样时间(30fps)
        
        self.last_error = 0
        self.integral = 0
        self.integral_limit = 100  # 积分限幅
        
    def compute(self, error):
        """PID计算"""
        # P项
        p_term = self.kp * error
        
        # I项（带限幅）
        self.integral += error * self.dt
        self.integral = max(-self.integral_limit, 
                           min(self.integral_limit, self.integral))
        i_term = self.ki * self.integral
        
        # D项
        derivative = (error - self.last_error) / self.dt
        d_term = self.kd * derivative
        
        # 总输出
        output = p_term + i_term + d_term
        
        # 更新历史
        self.last_error = error
        
        return output
    
    def reset(self):
        """重置PID状态"""
        self.integral = 0
        self.last_error = 0

# 控制映射
def pid_to_command(pid_output):
    """将PID输出映射到离散命令"""
    # 输出限幅
    pid_output = max(-1.0, min(1.0, pid_output))
    
    # 死区
    if abs(pid_output) < 0.05:
        return CMD_STOP
    
    # 分段映射
    if pid_output > 0.5:
        return CMD_FAST_RIGHT
    elif pid_output > 0.05:
        return CMD_SLOW_RIGHT
    elif pid_output < -0.5:
        return CMD_FAST_LEFT
    else:
        return CMD_SLOW_LEFT
```

### 3.3 STM32端执行器

```c
// 速度映射表
typedef struct {
    uint8_t command;
    int16_t speed;     // 步/秒
    uint8_t direction;
} SpeedMapping_t;

const SpeedMapping_t speed_map[] = {
    {CMD_STOP,       0,     0},
    {CMD_SLOW_LEFT,  2000,  DIR_CCW},
    {CMD_FAST_LEFT,  8000,  DIR_CCW},
    {CMD_SLOW_RIGHT, 2000,  DIR_CW},
    {CMD_FAST_RIGHT, 8000,  DIR_CW}
};

// 命令处理函数
void ProcessCommand(uint8_t cmd, uint8_t data) {
    switch(cmd) {
        case CMD_FAST_LEFT:
        case CMD_SLOW_LEFT:
        case CMD_STOP:
        case CMD_SLOW_RIGHT:
        case CMD_FAST_RIGHT:
            // 更新电机速度
            UpdateMotorSpeed(cmd);
            break;
            
        case CMD_LASER_ON:
            // 开启激光
            LASER_ON();
            // 定时0.5秒后关闭
            StartTimer(500, LaserOffCallback);
            break;
            
        case CMD_SYS_RESET:
            // 系统复位
            SystemReset();
            break;
    }
}

// 更新电机速度
void UpdateMotorSpeed(uint8_t cmd) {
    for (int i = 0; i < sizeof(speed_map)/sizeof(speed_map[0]); i++) {
        if (speed_map[i].command == cmd) {
            Motor_SetSpeed(speed_map[i].speed);
            Motor_SetDirection(speed_map[i].direction);
            break;
        }
    }
}
```

## 四、系统状态机设计

### 4.1 状态定义

```python
class SystemState:
    INIT = 0        # 初始化
    SEARCHING = 1   # 搜索目标
    TRACKING = 2    # 跟踪目标
    ALIGNING = 3    # 精确对准
    ALIGNED = 4     # 已对准
    FIRING = 5      # 发射激光
    DONE = 6        # 完成任务
    ERROR = 7       # 错误状态
```

### 4.2 状态转换逻辑

```python
class StateMachine:
    def __init__(self):
        self.state = SystemState.INIT
        self.start_time = time.ticks_ms()
        self.state_enter_time = self.start_time
        self.timeout = 2000  # 2秒总超时
        
    def get_elapsed_time(self):
        """获取总用时"""
        return time.ticks_diff(time.ticks_ms(), self.start_time)
    
    def get_state_time(self):
        """获取当前状态持续时间"""
        return time.ticks_diff(time.ticks_ms(), self.state_enter_time)
    
    def transition_to(self, new_state):
        """状态转换"""
        print(f"State: {self.state} -> {new_state}")
        self.state = new_state
        self.state_enter_time = time.ticks_ms()
    
    def check_timeout(self):
        """超时检查"""
        if self.get_elapsed_time() > self.timeout:
            if self.state not in [SystemState.FIRING, SystemState.DONE]:
                print("System timeout! Emergency firing...")
                return True
        return False
```

### 4.3 主控制循环

```python
def main_control_loop():
    """主控制循环"""
    # 初始化
    sm = StateMachine()
    pid = PIDController(kp=0.01, ki=0.001, kd=0.005)
    protocol = SerialProtocol(uart)
    
    # 性能统计
    frame_count = 0
    last_fps_time = time.ticks_ms()
    
    # 对准确认
    aligned_count = 0
    ALIGN_THRESHOLD = 3  # 连续3帧确认
    
    while sm.state != SystemState.DONE:
        loop_start = time.ticks_us()
        
        # 超时检查
        if sm.check_timeout():
            protocol.send_command(CMD_LASER_ON)
            sm.transition_to(SystemState.DONE)
            break
        
        # 获取图像
        img = sensor.snapshot()
        frame_count += 1
        
        # 状态机处理
        if sm.state == SystemState.INIT:
            # 系统初始化
            pid.reset()
            protocol.send_command(CMD_STOP)
            sm.transition_to(SystemState.SEARCHING)
            
        elif sm.state == SystemState.SEARCHING:
            # 搜索目标
            target = find_target_fast(img)
            if target:
                print(f"Target found at ({target.cx()}, {target.cy()})")
                sm.transition_to(SystemState.TRACKING)
                
        elif sm.state == SystemState.TRACKING:
            # 跟踪目标
            target = find_target_fast(img)
            if target:
                # 计算误差
                error_x = target.cx() - CENTER_X
                
                # PID控制
                control = pid.compute(error_x)
                cmd = pid_to_command(control)
                
                # 发送命令
                protocol.send_command(cmd)
                
                # 检查对准
                if abs(error_x) < 10:  # 10像素容差
                    sm.transition_to(SystemState.ALIGNING)
                    aligned_count = 0
                    
                # 调试信息
                if frame_count % 10 == 0:
                    print(f"Error: {error_x}, Control: {control:.3f}, Cmd: 0x{cmd:02X}")
            else:
                # 目标丢失
                print("Target lost!")
                sm.transition_to(SystemState.SEARCHING)
                
        elif sm.state == SystemState.ALIGNING:
            # 精确对准
            target = find_target_fast(img)
            if target:
                error_x = target.cx() - CENTER_X
                
                if abs(error_x) < 5:  # 5像素精确容差
                    aligned_count += 1
                    if aligned_count >= ALIGN_THRESHOLD:
                        sm.transition_to(SystemState.ALIGNED)
                else:
                    # 继续调整
                    control = pid.compute(error_x)
                    cmd = pid_to_command(control)
                    protocol.send_command(cmd)
                    aligned_count = 0
            else:
                sm.transition_to(SystemState.SEARCHING)
                
        elif sm.state == SystemState.ALIGNED:
            # 已对准，准备发射
            protocol.send_command(CMD_STOP)
            time.sleep_ms(50)  # 稳定延时
            protocol.send_command(CMD_LASER_ON)
            sm.transition_to(SystemState.FIRING)
            print("Laser fired!")
            
        elif sm.state == SystemState.FIRING:
            # 等待激光发射
            if sm.get_state_time() > 500:  # 0.5秒
                sm.transition_to(SystemState.DONE)
                
        # 绘制调试信息
        draw_debug_info(img, sm.state, frame_count)
        
        # 性能统计
        if time.ticks_diff(time.ticks_ms(), last_fps_time) > 1000:
            fps = frame_count / 1.0
            print(f"FPS: {fps}")
            frame_count = 0
            last_fps_time = time.ticks_ms()
        
        # 循环时间控制
        loop_time = time.ticks_diff(time.ticks_us(), loop_start)
        if loop_time < 33000:  # 保持30fps
            time.sleep_us(33000 - loop_time)
        
        # 内存管理
        if frame_count % 30 == 0:
            gc.collect()
    
    # 任务完成
    elapsed = sm.get_elapsed_time() / 1000.0
    print(f"Mission completed in {elapsed:.2f} seconds")
```

## 五、时序优化方案

### 5.1 并行初始化

```python
# K230端并行初始化
def parallel_init():
    """并行初始化各模块"""
    # 同时启动
    sensor_init_task = start_async(init_sensor)
    uart_init_task = start_async(init_uart)
    
    # 等待完成
    wait_all([sensor_init_task, uart_init_task])
```

```c
// STM32端并行初始化
void System_ParallelInit(void) {
    // 同时初始化多个外设
    TIM2_Init();    // PWM输出
    USART2_Init();  // 通信接口
    GPIO_Init();    // 激光控制
    
    // 电机预热（可选）
    Motor_Preheat();
}
```

### 5.2 预测控制

```python
class PredictiveController:
    def __init__(self):
        self.history = []
        self.max_history = 5
        
    def predict_next_position(self, current_pos):
        """预测下一帧目标位置"""
        self.history.append(current_pos)
        if len(self.history) > self.max_history:
            self.history.pop(0)
        
        if len(self.history) < 2:
            return current_pos
        
        # 简单线性预测
        if len(self.history) >= 2:
            velocity = self.history[-1] - self.history[-2]
            predicted = self.history[-1] + velocity
            return predicted
        
        return current_pos
```

### 5.3 快速收敛策略

```python
def adaptive_pid_control(error, pid):
    """自适应PID控制"""
    abs_error = abs(error)
    
    # 大误差时使用Bang-Bang控制
    if abs_error > 100:
        return CMD_FAST_LEFT if error < 0 else CMD_FAST_RIGHT
    
    # 中等误差时增大P增益
    elif abs_error > 50:
        pid.kp = 0.02  # 临时增大
        control = pid.compute(error)
        pid.kp = 0.01  # 恢复
        return pid_to_command(control)
    
    # 小误差时正常PID
    else:
        return pid_to_command(pid.compute(error))
```

## 六、调试与测试方案

### 6.1 分模块测试

#### 通信测试
```python
# K230端通信测试
def test_communication():
    """测试通信链路"""
    protocol = SerialProtocol(uart)
    
    # 发送测试命令
    test_commands = [
        CMD_STOP, CMD_SLOW_LEFT, CMD_SLOW_RIGHT, 
        CMD_FAST_LEFT, CMD_FAST_RIGHT
    ]
    
    for cmd in test_commands:
        print(f"Testing command: 0x{cmd:02X}")
        protocol.send_command(cmd)
        time.sleep_ms(500)
    
    # 测试激光
    protocol.send_command(CMD_LASER_ON)
    print("Communication test completed")
```

#### 电机响应测试
```c
// STM32端响应测试
void Test_MotorResponse(void) {
    // 测试各速度档位
    uint8_t test_cmds[] = {
        CMD_SLOW_LEFT, CMD_FAST_LEFT,
        CMD_STOP,
        CMD_SLOW_RIGHT, CMD_FAST_RIGHT
    };
    
    for (int i = 0; i < sizeof(test_cmds); i++) {
        ProcessCommand(test_cmds[i], 0);
        Delay_ms(1000);
    }
}
```

### 6.2 系统集成测试

#### 静态目标测试
```python
def test_static_target():
    """静态目标瞄准测试"""
    success_count = 0
    total_tests = 10
    
    for i in range(total_tests):
        print(f"\nTest {i+1}/{total_tests}")
        
        # 运行主程序
        start_time = time.ticks_ms()
        result = run_aiming_system()
        elapsed = time.ticks_diff(time.ticks_ms(), start_time)
        
        if result and elapsed < 2000:
            success_count += 1
            print(f"Success! Time: {elapsed}ms")
        else:
            print(f"Failed! Time: {elapsed}ms")
        
        # 复位系统
        time.sleep(2)
    
    print(f"\nSuccess rate: {success_count}/{total_tests} ({success_count/total_tests*100:.1f}%)")
```

### 6.3 性能优化测试

#### 时序分析
```python
class PerformanceMonitor:
    def __init__(self):
        self.timings = {
            'capture': [],
            'process': [],
            'control': [],
            'total': []
        }
    
    def measure(self, phase, func, *args):
        """测量函数执行时间"""
        start = time.ticks_us()
        result = func(*args)
        elapsed = time.ticks_diff(time.ticks_us(), start)
        
        self.timings[phase].append(elapsed)
        return result
    
    def report(self):
        """生成性能报告"""
        for phase, times in self.timings.items():
            if times:
                avg = sum(times) / len(times) / 1000.0  # ms
                print(f"{phase}: {avg:.2f}ms")
```

## 七、故障诊断与恢复

### 7.1 常见故障处理

#### 通信故障
```python
def handle_communication_error():
    """通信错误处理"""
    # 尝试重新初始化
    uart.deinit()
    time.sleep_ms(100)
    uart.init(baudrate=115200)
    
    # 发送复位命令
    send_command(CMD_SYS_RESET)
```

#### 目标丢失处理
```python
def handle_target_lost(last_known_position):
    """目标丢失恢复策略"""
    # 回到最后已知位置
    if last_known_position:
        error = last_known_position - CENTER_X
        if abs(error) > 50:
            # 慢速移动到大概位置
            cmd = CMD_SLOW_LEFT if error < 0 else CMD_SLOW_RIGHT
            send_command(cmd)
```

### 7.2 应急方案

```python
def emergency_procedure():
    """紧急情况处理"""
    print("Emergency procedure activated!")
    
    # 停止电机
    send_command(CMD_STOP)
    
    # 直接发射（盲射）
    time.sleep_ms(100)
    send_command(CMD_LASER_ON)
    
    # 标记完成
    return SystemState.DONE
```

## 八、竞赛现场快速调试指南

### 8.1 快速检查清单

```
硬件检查：
□ 电源电压正常（7.4V）
□ 串口连接正确
□ 电机能够转动
□ 激光器能够点亮
□ 摄像头图像清晰

软件检查：
□ 程序正确烧录
□ 串口波特率匹配
□ 颜色阈值已调试
□ PID参数已优化
□ 超时保护启用
```

### 8.2 现场调试流程

```python
# 快速调试程序
def field_debug():
    """现场快速调试"""
    print("Field debugging mode")
    
    # 1. 测试通信
    print("1. Testing communication...")
    test_communication()
    
    # 2. 测试电机
    print("2. Testing motors...")
    test_motor_response()
    
    # 3. 测试视觉
    print("3. Testing vision...")
    test_target_detection()
    
    # 4. 测试完整流程
    print("4. Testing full system...")
    test_static_target()
    
    print("Debug completed!")
```

### 8.3 参数快速调优

```python
# 参数配置文件
config = {
    'pid': {
        'kp': 0.01,
        'ki': 0.001,
        'kd': 0.005
    },
    'threshold': {
        'color': [(30, 100, 15, 127, 15, 127)],
        'pixels': 200,
        'area': 200
    },
    'control': {
        'dead_zone': 5,
        'fast_threshold': 50,
        'align_count': 3
    },
    'timing': {
        'timeout': 2000,
        'laser_duration': 500
    }
}

# 快速加载配置
def load_config(filename='config.txt'):
    """加载配置文件"""
    try:
        with open(filename, 'r') as f:
            import json
            return json.load(f)
    except:
        return config  # 使用默认配置
```

## 九、总结与最佳实践

### 9.1 关键成功因素

1. **硬件稳定性**
   - 可靠的机械结构
   - 稳定的电源供应
   - 良好的线缆连接

2. **软件鲁棒性**
   - 完善的异常处理
   - 合理的超时机制
   - 有效的状态管理

3. **算法优化**
   - 快速的目标识别
   - 稳定的PID控制
   - 高效的通信协议

### 9.2 性能指标

```
目标性能指标：
- 启动时间：< 200ms
- 识别时间：< 300ms
- 对准时间：< 1000ms
- 发射时间：500ms
- 总计时间：< 2000ms
- 成功率：> 90%
```

### 9.3 持续改进方向

1. **算法升级**
   - 卡尔曼滤波预测
   - 自适应PID参数
   - 机器学习识别

2. **硬件优化**
   - 更高精度编码器
   - 更快速的电机
   - 更稳定的激光器

3. **系统集成**
   - 多传感器融合
   - 分布式处理
   - 云端辅助计算