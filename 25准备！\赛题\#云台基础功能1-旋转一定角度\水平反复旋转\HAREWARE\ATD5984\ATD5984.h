/**
 ******************************************************************************
 * @file    ATD5984.h
 * <AUTHOR> Name]
 * @version V1.0
 * @date    2025-01-31
 * @brief   ATD5984水平电机控制头文件 - 简化版
 *          
 *          本文件定义了水平电机(电机A)的控制接口
 *          实现固定动作序列：顺时针90deg -> 逆时针188deg -> 顺时针90deg
 * 
 * @note    引脚分配 (STM32F407ZGT6):
 *          水平电机A: STEP=PC8, DIR=PD3, SLEEP=PD2
 *          
 *          ATD5984驱动器特性:
 *          - 1/16微步细分
 *          - 最大电流1.44A
 *          - 12-24V供电
 *          - 脉冲+方向控制模式
 ******************************************************************************
 */

#ifndef __ATD5984_H
#define __ATD5984_H

#include "sys.h"
#include "delay.h"

/* 角度控制宏定义 ------------------------------------------------------------*/
#define STEPS_PER_DEGREE    8.889f  // 步/度 *正确值：1.8°步距角+1/16细分=3200步/圈，0.1125deg/步*
#define DIR_CW              0       // 顺时针方向 *修正：尝试反转逻辑*
#define DIR_CCW             1       // 逆时针方向 *修正：尝试反转逻辑*

/* 细分模式参考表 (42步进电机, 1.8°步距角) -------------------------------*/
/*
 * 42步进电机细分模式对照表 (步距角1.8°)：
 * 1/1细分:   200步/圈   → 0.556步/度 (1.8deg/步)
 * 1/2细分:   400步/圈   → 1.111步/度 (0.9deg/步)
 * 1/4细分:   800步/圈   → 2.222步/度 (0.45deg/步)
 * 1/8细分:   1600步/圈  → 4.444步/度 (0.225deg/步)
 * 1/16细分:  3200步/圈  → 8.889步/度 (0.1125deg/步) *当前使用*
 * 1/32细分:  6400步/圈  → 17.778步/度 (0.05625deg/步)
 * 
 * D36A拨码开关设置 (SW1-SW3):
 * 000 = 1/16细分 (推荐，平衡精度和速度)
 * 001 = 1/8细分
 * 010 = 1/4细分  
 * 011 = 1/2细分
 * 100 = 1/1细分
 * 101 = 1/32细分 (最高精度)
 */

/* 函数声明 ------------------------------------------------------------------*/

/**
 * @brief  水平电机A初始化
 * @param  None
 * @retval None
 * @note   初始化水平电机控制引脚，设置默认状态：
 *         - 电机A使能 (SLEEP=HIGH)
 *         - 电机A正向 (DIR=LOW)
 *         - 所有控制引脚配置为推挽输出，50MHz
 */
void ATD5984_Init(void);

/**
 * @brief  初始化水平电机PWM信号发生器
 * @param  arr: 自动重装载值 (决定PWM频率)
 * @param  psc: 预分频系数 (决定定时器时钟)
 * @retval None
 * @note   使用TIM8的CH3(PC8)输出PWM连接到水平电机A
 *         PWM频率 = 168MHz / ((psc+1) * (arr+1))
 *         推荐: arr=10499, psc=6 -> 频率=1600Hz (中速平衡)
 *         占空比固定为50% (pulse = arr/2)
 */
void STEP12_PWM_Init(u16 arr, u16 psc);

/**
 * @brief  水平电机A旋转指定角度
 * @param  angle: 旋转角度 (度，正数为顺时针，负数为逆时针)
 * @retval None
 * @note   根据角度自动计算步数和方向，执行旋转动作
 *         内部包含适当延时确保动作完成
 */
void Motor_A_Rotate(float angle);

/**
 * @brief  方向控制测试函数
 * @param  None
 * @retval None
 * @note   执行简单的左右旋转测试，验证方向控制正确性
 *         用于校准DIR信号极性
 */
void Motor_A_DirectionTest(void);

/**
 * @brief  步数校准测试函数
 * @param  None
 * @retval None
 * @note   执行360deg旋转测试，用于校准STEPS_PER_DEGREE值
 *         通过实际测量验证步数计算的准确性
 */
void Motor_A_StepsCalibration(void);

#endif
