/**
 ******************************************************************************
 * @file    k230_comm.h
 * <AUTHOR>
 * @version V1.0
 * @date    2025-08-02
 * @brief   K230通信模块头文件
 *          
 *          本模块实现与K230视觉模块的串口通信功能
 *          支持6字节控制指令协议解析和PID控制算法
 * 
 * @note    通信协议:
 *          格式: [0xAA][CMD][OFFSET_H][OFFSET_L][CHECKSUM][0xFF]
 *          CMD: 1=LEFT, 2=RIGHT, 3=STOP, 4=FIRE
 *          OFFSET: 有符号16位像素偏差值
 *          
 *          PID控制:
 *          输入: 像素偏差值 (-300 到 +300)
 *          输出: 电机速度控制值 (Hz)
 ******************************************************************************
 */

#ifndef __K230_COMM_H
#define __K230_COMM_H

#include "stm32f4xx.h"
#include <stdint.h>
#include <stdbool.h>
#include "../MOTOR_CONTROL/motor_control.h"
#include "../../SYSTEM/usart/usart.h"

/* PID控制参数定义 --------------------------------------------------------*/

/**
 * @brief  PID参数结构体
 */
typedef struct {
    float kp;                   /**< 比例系数 */
    float ki;                   /**< 积分系数 */
    float kd;                   /**< 微分系数 */
    float integral;             /**< 积分累积 */
    float last_error;           /**< 上次误差 */
    float max_integral;         /**< 积分限幅 */
    float max_output;           /**< 输出限幅 */
} K230_PID_t;

/**
 * @brief  控制状态结构体
 */
typedef struct {
    bool enabled;               /**< 控制使能标志 */
    uint32_t last_cmd_time;     /**< 最后指令时间 */
    uint8_t last_cmd;           /**< 最后收到的指令 */
    int16_t current_offset;     /**< 当前偏差值 */
    float pid_output;           /**< PID输出值 */
    uint32_t timeout_ms;        /**< 超时时间(ms) */
} K230_Control_t;

/* 控制参数常量定义 -------------------------------------------------------*/

/** PID控制死区 */
#define K230_DEAD_ZONE           10      /**< 像素偏差死区(±10像素) */

/** 超时保护 */
#define K230_TIMEOUT_MS          500     /**< 通信超时时间(500ms) */

/** 速度控制范围 */
#define K230_MIN_SPEED           400     /**< 最小电机速度(Hz) */
#define K230_MAX_SPEED           1500    /**< 最大电机速度(Hz) */

/** PID参数默认值 */
#define K230_DEFAULT_KP          1.0f    /**< 默认比例系数 */
#define K230_DEFAULT_KI          0.2f    /**< 默认积分系数 */
#define K230_DEFAULT_KD          0.1f    /**< 默认微分系数 */

/* 全局变量声明 -----------------------------------------------------------*/
extern K230_PID_t g_k230_pid;             /**< PID控制参数 */
extern K230_Control_t g_k230_control;     /**< 控制状态 */

/* 函数声明 --------------------------------------------------------------*/

/* 初始化接口 */

/**
 * @brief  K230通信模块初始化
 * @param  None
 * @retval bool: true=初始化成功, false=初始化失败
 * @note   初始化通信接收状态机和PID参数
 */
bool K230_Comm_Init(void);

/* PID控制接口 */

/**
 * @brief  PID参数设置
 * @param  kp: 比例系数
 * @param  ki: 积分系数  
 * @param  kd: 微分系数
 * @retval None
 * @note   动态调整PID参数
 */
void K230_PID_SetParams(float kp, float ki, float kd);

/**
 * @brief  PID控制计算
 * @param  setpoint: 目标值(通常为0，表示居中)
 * @param  current_value: 当前值(像素偏差)
 * @retval float: PID输出值
 * @note   计算PID控制输出，用于电机速度控制
 */
float K230_PID_Calculate(float setpoint, float current_value);

/**
 * @brief  PID积分清零
 * @param  None
 * @retval None
 * @note   清除积分累积，用于防止积分饱和
 */
void K230_PID_Reset(void);

/* 电机控制接口 */

/**
 * @brief  执行控制指令
 * @param  cmd: 控制指令
 * @param  pixel_offset: 像素偏差值
 * @retval bool: true=执行成功, false=执行失败
 * @note   将控制指令转换为电机动作
 */
bool K230_Execute_Command(uint8_t cmd, int16_t pixel_offset);

/**
 * @brief  像素偏差到电机速度转换
 * @param  pixel_offset: 像素偏差值
 * @retval uint16_t: 电机转速(Hz)
 * @note   基于PID输出计算电机控制速度
 */
uint16_t K230_Offset_To_Speed(int16_t pixel_offset);

/* 主处理接口 */

/**
 * @brief  处理K230数据包
 * @param  None
 * @retval bool: true=处理了新数据包, false=无新数据
 * @note   在主循环中调用，处理接收完成的数据包
 */
bool K230_Process_Frame(void);

/**
 * @brief  检查通信超时
 * @param  None
 * @retval None
 * @note   检查是否超时，超时则停止电机
 */
void K230_Check_Timeout(void);

/* 状态查询接口 */

/**
 * @brief  获取通信状态
 * @param  None
 * @retval bool: true=通信正常, false=通信异常
 * @note   检查最近是否收到有效数据包
 */
bool K230_Get_CommStatus(void);

/**
 * @brief  获取最后接收到的偏差值
 * @param  None
 * @retval int16_t: 像素偏差值
 */
int16_t K230_Get_LastOffset(void);

/**
 * @brief  获取PID输出值
 * @param  None
 * @retval float: 当前PID输出
 */
float K230_Get_PIDOutput(void);

#endif
