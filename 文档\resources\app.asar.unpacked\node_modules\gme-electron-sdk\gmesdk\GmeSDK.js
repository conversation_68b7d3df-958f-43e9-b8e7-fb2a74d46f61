"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GmeContext = exports.GmeAudioDeviceInfo = exports.GmeLogger = void 0;
var GmeError_1 = require("./GmeError");
var GmeType_1 = require("./GmeType");
var AuthBuffer_1 = require("./AuthBuffer");
var GmeLogger = exports.GmeLogger = /** @class */ (function () {
    function GmeLogger() {
    }
    GmeLogger.Log = function (message) {
        switch (this.debugLevel) {
            case GmeType_1.ITMG_LOG_LEVEL.TMG_LOG_LEVEL_NONE:
                {
                    break;
                }
            case GmeType_1.ITMG_LOG_LEVEL.TMG_LOG_LEVEL_INFO:
                {
                    console.log(message);
                    break;
                }
            case GmeType_1.ITMG_LOG_LEVEL.TMG_LOG_LEVEL_ERROR:
                {
                    console.error(message);
                    break;
                }
        }
    };
    GmeLogger.debugLevel = GmeType_1.ITMG_LOG_LEVEL.TMG_LOG_LEVEL_NONE;
    return GmeLogger;
}());
var GmeAudioDeviceInfo = /** @class */ (function () {
    function GmeAudioDeviceInfo(name, id) {
        this.deviceName = name;
        this.deviceId = id;
    }
    ;
    return GmeAudioDeviceInfo;
}());
exports.GmeAudioDeviceInfo = GmeAudioDeviceInfo;
var gmeAddon = require('./thirdpart/bindings')('gme_electron_sdk');
var GmeContext = exports.GmeContext = /** @class */ (function () {
    function GmeContext() {
        this.gmeSDKCpp = null;
        this.pollHandler = -1;
        if (!this.gmeSDKCpp && (typeof gmeAddon.GMEContext) !== "undefined") {
            this.gmeSDKCpp = new gmeAddon.GMEContext();
        }
    }
    GmeContext.GetInstance = function () {
        if (this._instance == null) {
            this._instance = new GmeContext();
            return this._instance;
        }
        else {
            return this._instance;
        }
    };
    /**
     * 初始化GME的SDK
     * @param appid sdk的appId
     * @param openid sdk的openId
     */
    GmeContext.prototype.Init = function (appid, openid) {
        if (this.gmeSDKCpp) {
            var res = this.gmeSDKCpp.init(appid, openid);
            if (res !== GmeError_1.default.AV_OK) {
                return res;
            }
            return res;
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return GmeError_1.default.AV_ERR_NOT_IMPLEMENTED;
        }
    };
    /**
     * GME定时触发函数,建议在帧率刷新时候同步调用
     */
    GmeContext.prototype.Poll = function () {
        if (this.gmeSDKCpp) {
            this.gmeSDKCpp.poll();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
        }
    };
    /**
     * 反初始化GME的SDK
     */
    GmeContext.prototype.Uninit = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.uninit();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    /**
     * 暂停GME的SDK
     */
    GmeContext.prototype.Pause = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pause();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    /**
     * 继续GME的SDK
     */
    GmeContext.prototype.Resume = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.resume();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    /**
     * 设置log级别，TRACE(0), DEBUG(1), INFO(2), WARN(3), ERROR(4), NONE(5)
     */
    GmeContext.prototype.SetLogLevel = function (level) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.setLogLevel(level, level);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    /**
     * 设置用户应用名称和版本
     */
    GmeContext.prototype.SetAppVersion = function (appVersion) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.setAppVersion(appVersion);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    /**
     * 设置log日志路径
     * @param 日志路径
     */
    GmeContext.prototype.SetLogPath = function (logPath) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.setLogPath(logPath);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    /**
     * 获取log日志路径
     * @returns 日志路径
     */
    GmeContext.prototype.GetLogPath = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.getLogPath();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
        }
    };
    /**
     * 获取GMESDK版本
     * @returns GMESDK版本
     */
    GmeContext.prototype.GetSDKVersion = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.getSDKVersion();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
        }
    };
    /**
     * 设置GMESDK使用范围
     * @param 范围字符串
     */
    GmeContext.prototype.SetRegion = function (region) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.setRegion(region);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    /**
     * 设置回调函数，对外提供GME api的回调事件和回调信息
     * @param 回调方法
     */
    GmeContext.prototype.SetTMGDelegate = function (cb) {
        if (this.gmeSDKCpp) {
            this.gmeSDKCpp.setTMGDelegate(function (eventId, msg) {
                cb.onEvent(eventId, msg);
            });
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
        }
    };
    /**
     * 获取鉴权信息
     * @param appId
     * @param roomId
     * @param openId
     * @param appKey
     * @returns
     */
    GmeContext.prototype.GenAuthBuffer = function (appId, roomId, openId, appKey) {
        var authBuffer = new AuthBuffer_1.AuthBufferService(appId, roomId, openId, appKey);
        var userSig = authBuffer.getSignature();
        return userSig;
    };
    /**
     * 进房
     * @param roomId     --- 房间ID
     * @param roomType   --- 加入房间类型
     * @param authBuffer --- 进房密钥, 通过后台获取, 参见文档:
     */
    GmeContext.prototype.EnterRoom = function (roomid, roomType, appKey) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.enterRoom(roomid, roomType, appKey);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    /**
     * 退出房间函数，客户端会停止推流，同时关闭本地流，将远端流置为空流。
     */
    GmeContext.prototype.ExitRoom = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.exitRoom();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.IsRoomEntered = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.isRoomEntered();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return false;
        }
    };
    /**
     * 禁用（启用）音频轨道，禁用音频轨道,让本地流停止发送音频，相反会解除该禁用
     * @param bEnable true禁用音频轨道，false解除禁用
     */
    GmeContext.prototype.EnableAudioSend = function (bEnable) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.enableAudioSend(bEnable);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    /**
     * 禁用（启用）音频轨道，禁用音频,让本地流停止接收音频，相反会解除该禁用
     * @param bEnable true禁用音频轨道，false解除禁用
     */
    GmeContext.prototype.EnableAudioRecv = function (bEnable) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.enableAudioRecv(bEnable);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    /**
     * 打开或关闭麦克风，会通过禁用本地流的音频轨道，让本地流停止发送音频，相反会打开本地流发送音频
     * @param bEnable true为打开，false为关闭
     */
    GmeContext.prototype.EnableMic = function (bEnable) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.enableMic(bEnable);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    /**
     *
     * @param bEnable
     */
    GmeContext.prototype.EnableLoopBack = function (bEnable) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.enableLoopBack(bEnable);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    /**
     *
     * @param openId
     */
    GmeContext.prototype.AddAudioBlackList = function (openId) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.addAudioBlackList(openId);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    /**
     *
     * @param openId
     */
    GmeContext.prototype.RemoveAudioBlackList = function (openId) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.removeAudioBlackList(openId);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    /**
     *
     * @param openId
     */
    GmeContext.prototype.IsOpenIdInAudioBlackList = function (openId) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.isOpenIdInAudioBlackList(openId);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return false;
        }
    };
    /**
     *
     * 获取音频上行实时音量
     */
    GmeContext.prototype.GetSendStreamLevel = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.getSendStreamLevel();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return 0;
        }
    };
    /**
     *
     * 获取房间内其他成员下行实时音量
     * @param openId
     */
    GmeContext.prototype.GetRecvStreamLevel = function (openId) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.getRecvStreamLevel(openId);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return 0;
        }
    };
    /**
     * 获取麦克风状态
     * @return 麦克风状态 1表示打开 0表示关闭
     */
    GmeContext.prototype.GetMicState = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.getMicState();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return 0;
        }
    };
    /**
     * 打开或关闭扬声器，会遍历远程流，停止播放音频流，但是仍然会接收音频数据，
     * 相反会继续播放音频流同事接收音频数据。
     * @param bEnable true为打开，false为关闭
     */
    GmeContext.prototype.EnableSpeaker = function (bEnable) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.enableSpeaker(bEnable);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.GetSpeakerState = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.getSpeakerState();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return 0;
        }
    };
    GmeContext.prototype.EnableAudioCaptureDevice = function (enable) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.enableAudioCaptureDevice(enable);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.EnableAudioPlayDevice = function (enable) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.enableAudioPlayDevice(enable);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.IsAudioCaptureDeviceEnabled = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.isAudioCaptureDeviceEnabled();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return false;
        }
    };
    GmeContext.prototype.IsAudioPlayDeviceEnabled = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.isAudioPlayDeviceEnabled();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return false;
        }
    };
    GmeContext.prototype.IsAudioSendEnabled = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.isAudioSendEnabled();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return false;
        }
    };
    GmeContext.prototype.IsAudioRecvEnabled = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.isAudioRecvEnabled();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return false;
        }
    };
    GmeContext.prototype.GetMicLevel = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.getMicLevel();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.GetSpeakerLevel = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.getSpeakerLevel();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.SetSpeakerVolume = function (volume) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.setSpeakerVolume(volume);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.GetSpeakerVolume = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.getSpeakerVolume();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return 0;
        }
    };
    GmeContext.prototype.SetMicVolume = function (volume) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.setMicVolume(volume);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.GetMicVolume = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.getMicVolume();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.SetSpeakerVolumeByOpenID = function (openId, volume) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.setSpeakerVolumeByOpenID(openId, volume);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.GetSpeakerVolumeByOpenID = function (openId) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.getSpeakerVolumeByOpenID(openId);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return 0;
        }
    };
    /**
     * 获取麦克风个数
     * @returns 麦克风个数
     */
    GmeContext.prototype.GetMicListCount = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.getMicListCount();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return 0;
        }
    };
    /**
     * 获取麦克风列表
     * @returns 麦克风列表
     */
    GmeContext.prototype.GetMicList = function () {
        var micList = [];
        if (this.gmeSDKCpp) {
            var micListJson = this.gmeSDKCpp.getMicList();
            var jsonParsed = JSON.parse(micListJson);
            for (var i = 0; i < jsonParsed.length; i++) {
                micList[i] = new GmeAudioDeviceInfo(jsonParsed[i].deviceName, jsonParsed[i].deviceId);
            }
            return micList;
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return micList;
        }
    };
    /**
     * 选择麦克风
     *
     */
    GmeContext.prototype.SelectMic = function (micId) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.selectMic(micId);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    /**
     * 获取扬声器个数
     * @returns 扬声器个数
     */
    GmeContext.prototype.GetSpeakerListCount = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.getSpeakerListCount();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return 0;
        }
    };
    /**
     * 获取扬声器列表
     * @returns 扬声器列表
     */
    GmeContext.prototype.GetSpeakerList = function () {
        var speakList = [];
        if (this.gmeSDKCpp) {
            var speakData = this.gmeSDKCpp.getSpeakerList();
            var jsonParsed = JSON.parse(speakData);
            for (var i = 0; i < jsonParsed.length; i++) {
                speakList[i] = new GmeAudioDeviceInfo(jsonParsed[i].deviceName, jsonParsed[i].deviceId);
            }
            return speakList;
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return speakList;
        }
    };
    /**
     * 选择扬声器
     *
     */
    GmeContext.prototype.SelectSpeaker = function (speakerId) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.selectSpeaker(speakerId);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    /**
     * 获取本地流的音轨
     */
    /*GetMicTrackData() : MediaStreamTrack{ //未完成
        let track = null;
        return null;
    }*/
    GmeContext.prototype.SetAdvanceParam = function (key, value) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.setAdvanceParam(key, value);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    /**
     * 获取麦克风的数据信息，如麦克风的设备id等
     * @returns 麦克风的数据信息
     */
    // GetMicrophones():MediaDeviceInfo[] {
    //     var ret:MediaDeviceInfo[] = [];
    //     return ret;
    // }
    /**
     * 修改房间语音类型，流畅 标准 高清
     * @param 房间语音类型
     */
    GmeContext.prototype.ChangeRoomType = function (roomType) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.changeRoomType(roomType);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    /**
     * 获取房间类型 流畅 标准 高清
     * @returns 1-流畅 2-标准 3-高清
     */
    GmeContext.prototype.GetRoomType = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.getRoomType();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    /**
     * 获取房间质量信息
     * @returns 质量信息字符串
     */
    GmeContext.prototype.GetQualityTips = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.getQualityTips();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return "";
        }
    };
    /**
     * 开始播放伴奏和背景音乐，同时开始推流
     * @param file 背景音乐的播放文件
     * @param loopBack 是混音并且发送
     * @param loopCount 循环次数
     */
    GmeContext.prototype.StartAccompany = function (file, loopBack, loopCount, msTime) {
        if (loopBack === void 0) { loopBack = true; }
        if (loopCount === void 0) { loopCount = 1; }
        if (msTime === void 0) { msTime = 0; }
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.startAccompany(file, loopBack, loopCount, msTime);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    /**
     * 停止伴奏播放，同时停止推流
     */
    GmeContext.prototype.StopAccompany = function (duckerTime) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.stopAccompany(duckerTime);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    /**
     * 设置伴奏音量
     * @param volume 音量
     */
    GmeContext.prototype.SetAccompanyVolume = function (volume) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.setAccompanyVolume(volume);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PauseAccompany = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pauseAccompany();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.ResumeAccompany = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.resumeAccompany();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.TrackingVolume = function (timeS) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.trackingVolume(timeS);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.StopTrackingVolume = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.stopTrackingVolume();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.GetCurrentSpeaker = function () {
        var deviceInfo;
        if (this.gmeSDKCpp) {
            var josn = this.gmeSDKCpp.getCurrentSpeaker();
            var jsonParsed = JSON.parse(josn);
            var nRet = jsonParsed.errCode;
            if (nRet == GmeError_1.default.AV_OK) {
                deviceInfo = new GmeAudioDeviceInfo(jsonParsed.deviceName, jsonParsed.deviceId);
            }
            else {
                deviceInfo = undefined;
            }
            return [nRet, deviceInfo];
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return [-1, undefined];
        }
    };
    GmeContext.prototype.GetCurrentMic = function () {
        var deviceInfo;
        if (this.gmeSDKCpp) {
            var josn = this.gmeSDKCpp.getCurrentMic();
            var jsonParsed = JSON.parse(josn);
            var nRet = jsonParsed.errCode;
            if (nRet == GmeError_1.default.AV_OK) {
                deviceInfo = new GmeAudioDeviceInfo(jsonParsed.deviceName, jsonParsed.deviceId);
            }
            else {
                deviceInfo = undefined;
            }
            return [nRet, deviceInfo];
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return [-1, undefined];
        }
    };
    GmeContext.prototype.ApplyPTTAuthbuffer = function (authBuffer) {
        if (this.gmeSDKCpp) {
            GmeLogger.Log("pptAuthb");
            return this.gmeSDKCpp.applyPTTAuthbuffer(authBuffer);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PttSetMaxMessageLength = function (msTime) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pttSetMaxMessageLength(msTime);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PttStartRecording = function (filePath) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pttStartRecording(filePath);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PttStopRecording = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pttStopRecording();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PttCancelRecording = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pttCancelRecording();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PttUploadRecordedFile = function (filePath) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pttUploadRecordedFile(filePath);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PttDownloadRecordedFile = function (fileId, filePath) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pttDownloadRecordedFile(fileId, filePath);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PttPlayRecordedFile = function (filePath, voiceType) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pttPlayRecordedFile(filePath, voiceType);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PttStopPlayFile = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pttStopPlayFile();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PttGetMicLevel = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pttGetMicLevel();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PttSetMicVolume = function (vol) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pttSetMicVolume(vol);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PttGetMicVolume = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pttGetMicVolume();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PttGetSpeakerLevel = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pttGetSpeakerLevel();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PttSetSpeakerVolume = function (vol) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pttSetSpeakerVolume(vol);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PttGetSpeakerVolume = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pttGetSpeakerVolume();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PttSpeechToText = function (fileID, speechLanguage, translateLanguage) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pttSpeechToText(fileID, speechLanguage, translateLanguage);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PttTranslateText = function (text, sourceLanguage, translateLanguage) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pttTranslateText(text, sourceLanguage, translateLanguage);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PttGetFileSize = function (filePath) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pttGetFileSize(filePath);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PttGetVoiceFileDuration = function (filePath) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pttGetVoiceFileDuration(filePath);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PttStartRecordingWithStreamingRecognition = function (filePath, speechLanguage, translateLanguage) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pttStartRecordingWithStreamingRecognition(filePath, speechLanguage, translateLanguage);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PttPauseRecording = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pttPauseRecording();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PttResumeRecording = function () {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pttResumeRecording();
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PttSetPTTSourceLanguage = function (sourceLanguage) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.setPTTSourceLanguage(sourceLanguage);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.PttTextToSpeech = function (text, voiceName, languageCode, speakingRate) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.pttTextToSpeech(text, voiceName, languageCode, speakingRate);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.EnableRoomMic = function (enable, receiverID) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.enableRoomMic(enable, receiverID);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    //房间管理
    GmeContext.prototype.EnableRoomSpeaker = function (enable, receiverID) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.enableRoomSpeaker(enable, receiverID);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.EnableRoomAudioCaptureDevice = function (enable, receiverID) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.enableRoomAudioCaptureDevice(enable, receiverID);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.EnableRoomAudioPlayDevice = function (enable, receiverID) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.enableRoomAudioPlayDevice(enable, receiverID);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.EnableRoomAudioSend = function (bEnable, receiverID) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.enableRoomAudioSend(bEnable, receiverID);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.EnableRoomAudioRecv = function (enable, receiverID) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.enableRoomAudioRecv(enable, receiverID);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.GetRoomMicState = function (receiverID) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.getRoomMicState(receiverID);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.GetRoomSpeakerState = function (receiverID) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.getRoomSpeakerState(receiverID);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext.prototype.ForbidRoomUserOperation = function (enable, receiverID) {
        if (this.gmeSDKCpp) {
            return this.gmeSDKCpp.forbidRoomUserOperation(enable, receiverID);
        }
        else {
            GmeLogger.Log("H5 Can't debug temporarily");
            return -1;
        }
    };
    GmeContext._instance = null;
    GmeContext.num = 1;
    return GmeContext;
}());
