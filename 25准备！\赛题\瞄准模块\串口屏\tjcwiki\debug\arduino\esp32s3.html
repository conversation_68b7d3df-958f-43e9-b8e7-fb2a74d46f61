<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>ESP32S3(源地)与串口屏通讯 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
        <script src="../../_static/jquery.js"></script>
        <script src="../../_static/underscore.js"></script>
        <script src="../../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../../_static/doctools.js"></script>
        <script src="../../_static/translations.js"></script>
        <script src="../../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../../_static/custom.js"></script>
    <script src="../../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../../genindex.html" />
    <link rel="search" title="搜索" href="../../search.html" />
    <link rel="next" title="ESP32S3(合宙)与串口屏通讯" href="esp32s3_2.html" />
    <link rel="prev" title="ESP32C3与串口屏通讯" href="esp32c3.html" />
    <link href="../../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../../start/index.html">快速入门</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="../index.html">串口屏调试</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../simulator/index.html">模拟器调试基础知识</a></li>
<li class="toctree-l2"><a class="reference internal" href="../connect_ttl.html">和USB转TTL的接线说明</a></li>
<li class="toctree-l2"><a class="reference internal" href="../connect_mcu.html">和单片机的接线说明</a></li>
<li class="toctree-l2"><a class="reference internal" href="../base/index.html">联机调试基础知识</a></li>
<li class="toctree-l2"><a class="reference internal" href="../usart_protocol/index.html">串口屏通讯协议</a></li>
<li class="toctree-l2"><a class="reference internal" href="../debug_with_sscom.html">串口助手软件(sscom)和屏幕联调</a></li>
<li class="toctree-l2"><a class="reference internal" href="../debug_with_vspd.html">串口助手软件和串口屏模拟器联调1</a></li>
<li class="toctree-l2"><a class="reference internal" href="../debug_with_vspd2.html">串口助手软件和串口屏模拟器联调2</a></li>
<li class="toctree-l2"><a class="reference internal" href="../stm32/index.html">与stm32单片机联调</a></li>
<li class="toctree-l2"><a class="reference internal" href="../timcu/index.html">与TI（德州仪器）单片机联调</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html">与arduino联调</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="arduino_uno.html">arduino uno与串口屏通讯</a></li>
<li class="toctree-l3"><a class="reference internal" href="arduino_mega2560.html">arduino mega2560与串口屏通讯</a></li>
<li class="toctree-l3"><a class="reference internal" href="mixly_arduino_mega2560.html">米思齐Mixly arduino mega2560与串口屏通讯</a></li>
<li class="toctree-l3"><a class="reference internal" href="esp32c3.html">ESP32C3与串口屏通讯</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">ESP32S3(源地)与串口屏通讯</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id1">ESP32S3(源地)与串口屏通讯工程下载</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id2">ESP32S3(源地)与串口屏的连接方式</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id3">ESP32S3(源地)代码</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="esp32s3_2.html">ESP32S3(合宙)与串口屏通讯</a></li>
<li class="toctree-l3"><a class="reference internal" href="esp32s3_3.html">ESP32S3(嘉立创)与串口屏通讯</a></li>
<li class="toctree-l3"><a class="reference internal" href="raspberrypi_pico.html">树莓派pico使用arduino与串口屏通讯</a></li>
<li class="toctree-l3"><a class="reference internal" href="arduino_download.html">arduino通讯演示工程下载</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../micropython/index.html">与MicroPython联调</a></li>
<li class="toctree-l2"><a class="reference internal" href="../python/index.html">与python联调</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="../index.html">串口屏调试</a> &raquo;</li>
          <li><a href="index.html">与arduino联调</a> &raquo;</li>
      <li>ESP32S3(源地)与串口屏通讯</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="esp32s3">
<h1>ESP32S3(源地)与串口屏通讯<a class="headerlink" href="#esp32s3" title="此标题的永久链接"></a></h1>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>以下的教程均基于arduino2.0以上的版本，在低于arduino2.0的版本下编译可能会报错</p>
</div>
<section id="id1">
<h2>ESP32S3(源地)与串口屏通讯工程下载<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h2>
<p><a class="reference external" href="arduino_download.html"><img alt="download-logo" src="../../_images/download_project.png" /></a></p>
<p>串口屏怎么下载程序</p>
<p><a class="reference internal" href="../../start/create_project/create_project_9.html#id1"><span class="std std-ref">通过串口下载工程到串口屏</span></a></p>
<p><a class="reference internal" href="../../start/create_project/create_project_9_2.html#tft-tftfiledownload"><span class="std std-ref">使用TFT文件下载助手(TFTFileDownload)通过串口下载工程到串口屏</span></a></p>
<p><a class="reference internal" href="../../start/create_project/create_project_10.html#sd"><span class="std std-ref">通过SD卡下载工程到串口屏</span></a></p>
</section>
<section id="id2">
<h2>ESP32S3(源地)与串口屏的连接方式<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<img alt="../../_images/YD_esp32s3.png" src="../../_images/YD_esp32s3.png" />
</section>
<section id="id3">
<h2>ESP32S3(源地)代码<a class="headerlink" href="#id3" title="此标题的永久链接"></a></h2>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>以下代码仅为演示代码，用于测试显示屏能实现最基本的通信功能，如果您需要在正式产品中进行使用，请根据自己的需求对代码进行相应的优化和修改，或以自己的方式实现相应的功能</p>
</div>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>以下代码适用于ESP32S3</p>
<p>arduino只支持utf8编码，因此需要发送汉字时，在新建串口屏工程时请选择UTF8编码，导入字库时也要使用UTF8字库，参考 <a class="reference internal" href="../../QA/QA103.html#arduino"><span class="std std-ref">arduino发送中文时乱码</span></a></p>
</div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos">  1</span> //arduino IDE 上的型号选择ESP32S3 Dev Module
<span class="linenos">  2</span> //arduino的GND接串口屏或串口工具的GND,共地
<span class="linenos">  3</span> //arduino的9接串口屏或串口工具的RX
<span class="linenos">  4</span> //arduino的8接串口屏或串口工具的TX
<span class="linenos">  5</span> //arduino的5V接串口屏的5V,如果是串口工具,不用接5V也可以
<span class="linenos">  6</span> //根据自己的实际修改对应的TX_Pin和RX_Pin
<span class="linenos">  7</span> #define TJC Serial1
<span class="linenos">  8</span> #define TX_Pin 17
<span class="linenos">  9</span> #define RX_Pin 18
<span class="linenos"> 10</span> #define FRAME_LENGTH 7
<span class="linenos"> 11</span>
<span class="linenos"> 12</span> int a;
<span class="linenos"> 13</span> unsigned long nowtime;
<span class="linenos"> 14</span> void setup() {
<span class="linenos"> 15</span>   // put your setup code here, to run once:
<span class="linenos"> 16</span>   //初始化串口
<span class="linenos"> 17</span>   TJC.begin(115200, SERIAL_8N1, RX_Pin, TX_Pin);
<span class="linenos"> 18</span>
<span class="linenos"> 19</span>   //因为串口屏开机会发送88 ff ff ff,所以要清空串口缓冲区
<span class="linenos"> 20</span>   while (TJC.read() &gt;= 0); //清空串口缓冲区
<span class="linenos"> 21</span>   TJC.print(&quot;page main\xff\xff\xff&quot;); //发送命令让屏幕跳转到main页面
<span class="linenos"> 22</span>   nowtime = millis(); //获取当前已经运行的时间
<span class="linenos"> 23</span> }
<span class="linenos"> 24</span>
<span class="linenos"> 25</span> void loop() {
<span class="linenos"> 26</span>   // put your main code here, to run repeatedly:
<span class="linenos"> 27</span>   char str[100];
<span class="linenos"> 28</span>   if (millis() &gt;= nowtime + 1000) {
<span class="linenos"> 29</span>     nowtime = millis(); //获取当前已经运行的时间
<span class="linenos"> 30</span>
<span class="linenos"> 31</span>     //用sprintf来格式化字符串，给n0的val属性赋值
<span class="linenos"> 32</span>     sprintf(str, &quot;n0.val=%d\xff\xff\xff&quot;, a);
<span class="linenos"> 33</span>     //把字符串发送出去
<span class="linenos"> 34</span>     TJC.print(str);
<span class="linenos"> 35</span>
<span class="linenos"> 36</span>     //用sprintf来格式化字符串，给t0的txt属性赋值
<span class="linenos"> 37</span>     sprintf(str, &quot;t0.txt=\&quot;现在是%d\&quot;\xff\xff\xff&quot;, a);
<span class="linenos"> 38</span>     //把字符串发送出去
<span class="linenos"> 39</span>     TJC.print(str);
<span class="linenos"> 40</span>
<span class="linenos"> 41</span>
<span class="linenos"> 42</span>     //用sprintf来格式化字符串，触发b0的按下事件,直接把结束符整合在字符串中
<span class="linenos"> 43</span>     sprintf(str, &quot;click b0,1\xff\xff\xff&quot;);
<span class="linenos"> 44</span>     //把字符串发送出去
<span class="linenos"> 45</span>     TJC.print(str);
<span class="linenos"> 46</span>
<span class="linenos"> 47</span>     delay(50);  //延时50ms,才能看清楚点击效果
<span class="linenos"> 48</span>
<span class="linenos"> 49</span>     //用sprintf来格式化字符串，触发b0的弹起事件,直接把结束符整合在字符串中
<span class="linenos"> 50</span>     sprintf(str, &quot;click b0,0\xff\xff\xff&quot;);
<span class="linenos"> 51</span>     //把字符串发送出去
<span class="linenos"> 52</span>     TJC.print(str);
<span class="linenos"> 53</span>
<span class="linenos"> 54</span>     a++;
<span class="linenos"> 55</span>   }
<span class="linenos"> 56</span>
<span class="linenos"> 57</span>   //串口数据格式：
<span class="linenos"> 58</span>   //串口数据帧长度：7字节
<span class="linenos"> 59</span>   //帧头     参数1    参数2   参数3       帧尾
<span class="linenos"> 60</span>   //0x55     1字节   1字节    1字节     0xffffff
<span class="linenos"> 61</span>   //当参数是01时
<span class="linenos"> 62</span>   //帧头     参数1    参数2   参数3       帧尾
<span class="linenos"> 63</span>   //0x55     01     led编号  led状态    0xffffff
<span class="linenos"> 64</span>   //例子1：上位机代码  printh 55 01 01 00 ff ff ff  含义：1号led关闭
<span class="linenos"> 65</span>   //例子2：上位机代码  printh 55 01 04 01 ff ff ff  含义：4号led打开
<span class="linenos"> 66</span>   //例子3：上位机代码  printh 55 01 00 01 ff ff ff  含义：0号led打开
<span class="linenos"> 67</span>   //例子4：上位机代码  printh 55 01 04 00 ff ff ff  含义：4号led关闭
<span class="linenos"> 68</span>
<span class="linenos"> 69</span>   //当参数是02或03时
<span class="linenos"> 70</span>   //帧头     参数1    参数2   参数3       帧尾
<span class="linenos"> 71</span>   //0x55     02/03   滑动值    00    0xffffff
<span class="linenos"> 72</span>   //例子1：上位机代码  printh 55 02 64 00 ff ff ff  含义：h0.val=100
<span class="linenos"> 73</span>   //例子2：上位机代码  printh 55 02 00 00 ff ff ff  含义：h0.val=0
<span class="linenos"> 74</span>   //例子3：上位机代码  printh 55 03 64 00 ff ff ff  含义：h1.val=100
<span class="linenos"> 75</span>   //例子4：上位机代码  printh 55 03 00 00 ff ff ff  含义：h1.val=0
<span class="linenos"> 76</span>
<span class="linenos"> 77</span>
<span class="linenos"> 78</span>   //当串口缓冲区大于等于一帧的长度时
<span class="linenos"> 79</span>   while (TJC.available() &gt;= FRAME_LENGTH) {
<span class="linenos"> 80</span>     unsigned char ubuffer[FRAME_LENGTH];
<span class="linenos"> 81</span>     //从串口缓冲读取1个字节但不删除
<span class="linenos"> 82</span>     unsigned char frame_header = TJC.peek();
<span class="linenos"> 83</span>     //当获取的数据是包头(0x55)时
<span class="linenos"> 84</span>     if (frame_header == 0x55) {
<span class="linenos"> 85</span>       //从串口缓冲区读取7字节
<span class="linenos"> 86</span>       TJC.readBytes(ubuffer, FRAME_LENGTH);
<span class="linenos"> 87</span>       if (ubuffer[4] == 0xff &amp;&amp; ubuffer[5] == 0xff &amp;&amp; ubuffer[6] == 0xff) {
<span class="linenos"> 88</span>         if(ubuffer[1] == 0x01)
<span class="linenos"> 89</span>         {
<span class="linenos"> 90</span>           //下发的是LED信息
<span class="linenos"> 91</span>           sprintf(str, &quot;msg.txt=\&quot;led %d is %s\&quot;\xff\xff\xff&quot;, ubuffer[2], ubuffer[3] ? &quot;on&quot; : &quot;off&quot;);
<span class="linenos"> 92</span>           TJC.print(str);
<span class="linenos"> 93</span>
<span class="linenos"> 94</span>         }else if(ubuffer[1] == 0x02)
<span class="linenos"> 95</span>         {
<span class="linenos"> 96</span>           //下发的是滑动条h0.val的信息
<span class="linenos"> 97</span>           sprintf(str, &quot;msg.txt=\&quot;h0.val is %d\&quot;\xff\xff\xff&quot;, ubuffer[2]);
<span class="linenos"> 98</span>           TJC.print(str);
<span class="linenos"> 99</span>
<span class="linenos">100</span>         }else if(ubuffer[1] == 0x03)
<span class="linenos">101</span>         {
<span class="linenos">102</span>           //下发的是滑动条h1.val的信息
<span class="linenos">103</span>           sprintf(str, &quot;msg.txt=\&quot;h1.val is %d\&quot;\xff\xff\xff&quot;, ubuffer[2]);
<span class="linenos">104</span>           TJC.print(str);
<span class="linenos">105</span>
<span class="linenos">106</span>         }
<span class="linenos">107</span>
<span class="linenos">108</span>       }
<span class="linenos">109</span>     } else {
<span class="linenos">110</span>       TJC.read();  //从串口缓冲读取1个字节并删除
<span class="linenos">111</span>     }
<span class="linenos">112</span>   }
<span class="linenos">113</span> }
</pre></div>
</div>
<p>其他参考链接</p>
<p><a class="reference internal" href="../../QA/QA7.html#id1"><span class="std std-ref">屏幕通电后不断的闪烁(不断重启)</span></a></p>
<hr class="docutils" />
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="esp32c3.html" class="btn btn-neutral float-left" title="ESP32C3与串口屏通讯" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="esp32s3_2.html" class="btn btn-neutral float-right" title="ESP32S3(合宙)与串口屏通讯" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>