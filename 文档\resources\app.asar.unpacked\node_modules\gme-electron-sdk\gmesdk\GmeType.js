"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ITMG_AUDIO_ROUTE_TYPE = exports.ITMG_RANGE_AUDIO_MODE = exports.ITMG_KARAOKE_TYPE = exports.ITMG_VOICE_TYPE = exports.ITMG_LOG_LEVEL = exports.ITMG_EVENT_ID_USER_UPDATE = exports.ITMG_MAIN_EVENT_TYPE = exports.ITMG_ROOM_TYPE_SUB_EVENT = exports.ITMG_ROOM_TYPE = exports.ITMG_RECORD_PERMISSION = void 0;
var ITMG_RECORD_PERMISSION;
(function (ITMG_RECORD_PERMISSION) {
    ITMG_RECORD_PERMISSION[ITMG_RECORD_PERMISSION["ITMG_PERMISSION_GRANTED"] = 0] = "ITMG_PERMISSION_GRANTED";
    ITMG_RECORD_PERMISSION[ITMG_RECORD_PERMISSION["ITMG_PERMISSION_Denied"] = 1] = "ITMG_PERMISSION_Denied";
    ITMG_RECORD_PERMISSION[ITMG_RECORD_PERMISSION["ITMG_PERMISSION_NotDetermined"] = 2] = "ITMG_PERMISSION_NotDetermined";
    ITMG_RECORD_PERMISSION[ITMG_RECORD_PERMISSION["ITMG_PERMISSION_ERROR"] = 3] = "ITMG_PERMISSION_ERROR";
})(ITMG_RECORD_PERMISSION || (exports.ITMG_RECORD_PERMISSION = ITMG_RECORD_PERMISSION = {}));
var ITMG_ROOM_TYPE;
(function (ITMG_ROOM_TYPE) {
    ITMG_ROOM_TYPE[ITMG_ROOM_TYPE["ITMG_ROOM_TYPE_FLUENCY"] = 1] = "ITMG_ROOM_TYPE_FLUENCY";
    ITMG_ROOM_TYPE[ITMG_ROOM_TYPE["ITMG_ROOM_TYPE_STANDARD"] = 2] = "ITMG_ROOM_TYPE_STANDARD";
    ITMG_ROOM_TYPE[ITMG_ROOM_TYPE["ITMG_ROOM_TYPE_HIGHQUALITY"] = 3] = "ITMG_ROOM_TYPE_HIGHQUALITY";
})(ITMG_ROOM_TYPE || (exports.ITMG_ROOM_TYPE = ITMG_ROOM_TYPE = {}));
var ITMG_ROOM_TYPE_SUB_EVENT;
(function (ITMG_ROOM_TYPE_SUB_EVENT) {
    ITMG_ROOM_TYPE_SUB_EVENT[ITMG_ROOM_TYPE_SUB_EVENT["ITMG_ROOM_CHANGE_EVENT_ENTERROOM"] = 1] = "ITMG_ROOM_CHANGE_EVENT_ENTERROOM";
    ITMG_ROOM_TYPE_SUB_EVENT[ITMG_ROOM_TYPE_SUB_EVENT["ITMG_ROOM_CHANGE_EVENT_START"] = 2] = "ITMG_ROOM_CHANGE_EVENT_START";
    ITMG_ROOM_TYPE_SUB_EVENT[ITMG_ROOM_TYPE_SUB_EVENT["ITMG_ROOM_CHANGE_EVENT_COMPLETE"] = 3] = "ITMG_ROOM_CHANGE_EVENT_COMPLETE";
    ITMG_ROOM_TYPE_SUB_EVENT[ITMG_ROOM_TYPE_SUB_EVENT["ITMG_ROOM_CHANGE_EVENT_REQUEST"] = 4] = "ITMG_ROOM_CHANGE_EVENT_REQUEST";
})(ITMG_ROOM_TYPE_SUB_EVENT || (exports.ITMG_ROOM_TYPE_SUB_EVENT = ITMG_ROOM_TYPE_SUB_EVENT = {}));
/*
 *	//TMG event export enumeration that from a callback event
 */
var ITMG_MAIN_EVENT_TYPE;
(function (ITMG_MAIN_EVENT_TYPE) {
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_NONE"] = 0] = "ITMG_MAIN_EVENT_TYPE_NONE";
    //Notification of entering a room, triggered by EnterRoom API.
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_ENTER_ROOM"] = 1] = "ITMG_MAIN_EVENT_TYPE_ENTER_ROOM";
    //Notification of exiting a room, triggered by ExitRoom API.
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_EXIT_ROOM"] = 2] = "ITMG_MAIN_EVENT_TYPE_EXIT_ROOM";
    //Notification of room disconnection due to network or other reasons, which will trigger automatically.
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_ROOM_DISCONNECT"] = 3] = "ITMG_MAIN_EVENT_TYPE_ROOM_DISCONNECT";
    //Notification of the updates of room members, the notification contains detailed information, refer to ITMG_EVENT_ID_USER_UPDATE.
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVNET_TYPE_USER_UPDATE"] = 4] = "ITMG_MAIN_EVNET_TYPE_USER_UPDATE";
    //Notification of room reconnection happened, which indicates services will be temporarily unavailable.
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_RECONNECT_START"] = 11] = "ITMG_MAIN_EVENT_TYPE_RECONNECT_START";
    //Notification of room reconnection succeeded, which indicates services have recovered.
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_RECONNECT_SUCCESS"] = 12] = "ITMG_MAIN_EVENT_TYPE_RECONNECT_SUCCESS";
    //Notification of RoomType have been Changed by Other EndUser
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_CHANGE_ROOM_TYPE"] = 21] = "ITMG_MAIN_EVENT_TYPE_CHANGE_ROOM_TYPE";
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_AUDIO_DATA_EMPTY"] = 22] = "ITMG_MAIN_EVENT_TYPE_AUDIO_DATA_EMPTY";
    //Notify user the default speaker device is changed in the PC, refresh the Speaker devices when you recv this event.
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_SPEAKER_DEFAULT_DEVICE_CHANGED"] = 1008] = "ITMG_MAIN_EVENT_TYPE_SPEAKER_DEFAULT_DEVICE_CHANGED";
    //Notify user new Speaker device in the PC, refresh the Speaker devices when you recv this event.
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_SPEAKER_NEW_DEVICE"] = 1009] = "ITMG_MAIN_EVENT_TYPE_SPEAKER_NEW_DEVICE";
    //Notify user speaker device lost from the PC, refresh the Speaker devices when you recv this event.
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_SPEAKER_LOST_DEVICE"] = 1010] = "ITMG_MAIN_EVENT_TYPE_SPEAKER_LOST_DEVICE";
    //Notify user new mic device in the PC, refresh the Speaker devices when you recv this event.
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_MIC_NEW_DEVICE"] = 1011] = "ITMG_MAIN_EVENT_TYPE_MIC_NEW_DEVICE";
    //Notify user mic device lost from the PC, refresh the Speaker devices when you recv this event.
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_MIC_LOST_DEVICE"] = 1012] = "ITMG_MAIN_EVENT_TYPE_MIC_LOST_DEVICE";
    //Notify user the default mic device is changed in the PC, refresh the mic devices when you recv this event.
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_MIC_DEFAULT_DEVICE_CHANGED"] = 1013] = "ITMG_MAIN_EVENT_TYPE_MIC_DEFAULT_DEVICE_CHANGED";
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_AUDIO_ROUTE_CHANGED"] = 1014] = "ITMG_MAIN_EVENT_TYPE_AUDIO_ROUTE_CHANGED";
    //Notification of volumes of users in room
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_USER_VOLUMES"] = 1020] = "ITMG_MAIN_EVENT_TYPE_USER_VOLUMES";
    //quality information
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_CHANGE_ROOM_QUALITY"] = 1022] = "ITMG_MAIN_EVENT_TYPE_CHANGE_ROOM_QUALITY";
    //Notification of accompany finished
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_ACCOMPANY_FINISH"] = 1090] = "ITMG_MAIN_EVENT_TYPE_ACCOMPANY_FINISH";
    //Notification of Server Audio Route Event
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_SERVER_AUDIO_ROUTE_EVENT"] = 1091] = "ITMG_MAIN_EVENT_TYPE_SERVER_AUDIO_ROUTE_EVENT";
    //Notification of Custom Audio Data
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_CUSTOMDATA_UPDATE"] = 1092] = "ITMG_MAIN_EVENT_TYPE_CUSTOMDATA_UPDATE";
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_REALTIME_ASR"] = 1093] = "ITMG_MAIN_EVENT_TYPE_REALTIME_ASR";
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_CHORUS_EVENT"] = 1094] = "ITMG_MAIN_EVENT_TYPE_CHORUS_EVENT";
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_CHANGETEAMID"] = 1095] = "ITMG_MAIN_EVENT_TYPE_CHANGETEAMID";
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVENT_TYPE_AGE_DETECTED"] = 1096] = "ITMG_MAIN_EVENT_TYPE_AGE_DETECTED";
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVNET_TYPE_AUDIO_READY"] = 2000] = "ITMG_MAIN_EVNET_TYPE_AUDIO_READY";
    // Notification of PTT Record
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVNET_TYPE_PTT_RECORD_COMPLETE"] = 5001] = "ITMG_MAIN_EVNET_TYPE_PTT_RECORD_COMPLETE";
    // Notification of PTT Upload
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVNET_TYPE_PTT_UPLOAD_COMPLETE"] = 5002] = "ITMG_MAIN_EVNET_TYPE_PTT_UPLOAD_COMPLETE";
    // Notification of PTT Download
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVNET_TYPE_PTT_DOWNLOAD_COMPLETE"] = 5003] = "ITMG_MAIN_EVNET_TYPE_PTT_DOWNLOAD_COMPLETE";
    // Notification of PTT Play
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVNET_TYPE_PTT_PLAY_COMPLETE"] = 5004] = "ITMG_MAIN_EVNET_TYPE_PTT_PLAY_COMPLETE";
    // Notification of PTT 2Text
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVNET_TYPE_PTT_SPEECH2TEXT_COMPLETE"] = 5005] = "ITMG_MAIN_EVNET_TYPE_PTT_SPEECH2TEXT_COMPLETE";
    // Notification of StreamRecognition
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVNET_TYPE_PTT_STREAMINGRECOGNITION_COMPLETE"] = 5006] = "ITMG_MAIN_EVNET_TYPE_PTT_STREAMINGRECOGNITION_COMPLETE";
    ITMG_MAIN_EVENT_TYPE[ITMG_MAIN_EVENT_TYPE["ITMG_MAIN_EVNET_TYPE_PTT_STREAMINGRECOGNITION_IS_RUNNING"] = 5007] = "ITMG_MAIN_EVNET_TYPE_PTT_STREAMINGRECOGNITION_IS_RUNNING";
})(ITMG_MAIN_EVENT_TYPE || (exports.ITMG_MAIN_EVENT_TYPE = ITMG_MAIN_EVENT_TYPE = {}));
/*
 *	对应，ITMG_MAIN_EVENT_TYPE::ITMG_MAIN_EVNET_TYPE_USER_UPDATE//correspond,ITMG_MAIN_EVENT_TYPE::ITMG_MAIN_EVNET_TYPE_USER_UPDATE
 *  详情枚举//Details export enumeration
 */
var ITMG_EVENT_ID_USER_UPDATE;
(function (ITMG_EVENT_ID_USER_UPDATE) {
    //Notification of entering a room
    ITMG_EVENT_ID_USER_UPDATE[ITMG_EVENT_ID_USER_UPDATE["ITMG_EVENT_ID_USER_ENTER"] = 1] = "ITMG_EVENT_ID_USER_ENTER";
    //Notification of exiting a room
    ITMG_EVENT_ID_USER_UPDATE[ITMG_EVENT_ID_USER_UPDATE["ITMG_EVENT_ID_USER_EXIT"] = 2] = "ITMG_EVENT_ID_USER_EXIT";
    //Notification of member audio event
    ITMG_EVENT_ID_USER_UPDATE[ITMG_EVENT_ID_USER_UPDATE["ITMG_EVENT_ID_USER_HAS_AUDIO"] = 5] = "ITMG_EVENT_ID_USER_HAS_AUDIO";
    //Notification of no member audio event is received for 2 seconds
    ITMG_EVENT_ID_USER_UPDATE[ITMG_EVENT_ID_USER_UPDATE["ITMG_EVENT_ID_USER_NO_AUDIO"] = 6] = "ITMG_EVENT_ID_USER_NO_AUDIO";
    // Notification of some member opens his mic
    ITMG_EVENT_ID_USER_UPDATE[ITMG_EVENT_ID_USER_UPDATE["ITMG_EVENT_ID_USER_MIC_OPENED"] = 11] = "ITMG_EVENT_ID_USER_MIC_OPENED";
    // Notification of some member closes his mic
    ITMG_EVENT_ID_USER_UPDATE[ITMG_EVENT_ID_USER_UPDATE["ITMG_EVENT_ID_USER_MIC_CLOSED"] = 12] = "ITMG_EVENT_ID_USER_MIC_CLOSED";
})(ITMG_EVENT_ID_USER_UPDATE || (exports.ITMG_EVENT_ID_USER_UPDATE = ITMG_EVENT_ID_USER_UPDATE = {}));
var ITMG_LOG_LEVEL;
(function (ITMG_LOG_LEVEL) {
    //Do not print the log
    ITMG_LOG_LEVEL[ITMG_LOG_LEVEL["TMG_LOG_LEVEL_NONE"] = -1] = "TMG_LOG_LEVEL_NONE";
    //Used for critical log
    ITMG_LOG_LEVEL[ITMG_LOG_LEVEL["TMG_LOG_LEVEL_ERROR"] = 1] = "TMG_LOG_LEVEL_ERROR";
    //Used to prompt for information
    ITMG_LOG_LEVEL[ITMG_LOG_LEVEL["TMG_LOG_LEVEL_INFO"] = 2] = "TMG_LOG_LEVEL_INFO";
    //For development and debugging
    ITMG_LOG_LEVEL[ITMG_LOG_LEVEL["TMG_LOG_LEVEL_DEBUG"] = 3] = "TMG_LOG_LEVEL_DEBUG";
    //For high-frequency printing information
    ITMG_LOG_LEVEL[ITMG_LOG_LEVEL["TMG_LOG_LEVEL_VERBOSE"] = 4] = "TMG_LOG_LEVEL_VERBOSE";
})(ITMG_LOG_LEVEL || (exports.ITMG_LOG_LEVEL = ITMG_LOG_LEVEL = {}));
/*
 *voice change types,
 */
var ITMG_VOICE_TYPE;
(function (ITMG_VOICE_TYPE) {
    ITMG_VOICE_TYPE[ITMG_VOICE_TYPE["ITMG_VOICE_TYPE_ORIGINAL_SOUND"] = 0] = "ITMG_VOICE_TYPE_ORIGINAL_SOUND";
    ITMG_VOICE_TYPE[ITMG_VOICE_TYPE["ITMG_VOICE_TYPE_LOLITA"] = 1] = "ITMG_VOICE_TYPE_LOLITA";
    ITMG_VOICE_TYPE[ITMG_VOICE_TYPE["ITMG_VOICE_TYPE_UNCLE"] = 2] = "ITMG_VOICE_TYPE_UNCLE";
    ITMG_VOICE_TYPE[ITMG_VOICE_TYPE["ITMG_VOICE_TYPE_INTANGIBLE"] = 3] = "ITMG_VOICE_TYPE_INTANGIBLE";
    ITMG_VOICE_TYPE[ITMG_VOICE_TYPE["ITMG_VOICE_TYPE_DEAD_FATBOY"] = 4] = "ITMG_VOICE_TYPE_DEAD_FATBOY";
    ITMG_VOICE_TYPE[ITMG_VOICE_TYPE["ITMG_VOICE_TYPE_HEAVY_MENTAL"] = 5] = "ITMG_VOICE_TYPE_HEAVY_MENTAL";
    ITMG_VOICE_TYPE[ITMG_VOICE_TYPE["ITMG_VOICE_TYPE_DIALECT"] = 6] = "ITMG_VOICE_TYPE_DIALECT";
    ITMG_VOICE_TYPE[ITMG_VOICE_TYPE["ITMG_VOICE_TYPE_INFLUENZA"] = 7] = "ITMG_VOICE_TYPE_INFLUENZA";
    ITMG_VOICE_TYPE[ITMG_VOICE_TYPE["ITMG_VOICE_TYPE_CAGED_ANIMAL"] = 8] = "ITMG_VOICE_TYPE_CAGED_ANIMAL";
    ITMG_VOICE_TYPE[ITMG_VOICE_TYPE["ITMG_VOICE_TYPE_HEAVY_MACHINE"] = 9] = "ITMG_VOICE_TYPE_HEAVY_MACHINE";
    ITMG_VOICE_TYPE[ITMG_VOICE_TYPE["ITMG_VOICE_TYPE_STRONG_CURRENT"] = 10] = "ITMG_VOICE_TYPE_STRONG_CURRENT";
    ITMG_VOICE_TYPE[ITMG_VOICE_TYPE["ITMG_VOICE_TYPE_KINDER_GARTEN"] = 11] = "ITMG_VOICE_TYPE_KINDER_GARTEN";
    ITMG_VOICE_TYPE[ITMG_VOICE_TYPE["ITMG_VOICE_TYPE_HUANG"] = 12] = "ITMG_VOICE_TYPE_HUANG";
})(ITMG_VOICE_TYPE || (exports.ITMG_VOICE_TYPE = ITMG_VOICE_TYPE = {}));
var ITMG_KARAOKE_TYPE;
(function (ITMG_KARAOKE_TYPE) {
    ITMG_KARAOKE_TYPE[ITMG_KARAOKE_TYPE["ITMG_KARAOKE_TYPE_ORIGINAL"] = 0] = "ITMG_KARAOKE_TYPE_ORIGINAL";
    ITMG_KARAOKE_TYPE[ITMG_KARAOKE_TYPE["ITMG_KARAOKE_TYPE_POP"] = 1] = "ITMG_KARAOKE_TYPE_POP";
    ITMG_KARAOKE_TYPE[ITMG_KARAOKE_TYPE["ITMG_KARAOKE_TYPE_ROCK"] = 2] = "ITMG_KARAOKE_TYPE_ROCK";
    ITMG_KARAOKE_TYPE[ITMG_KARAOKE_TYPE["ITMG_KARAOKE_TYPE_RB"] = 3] = "ITMG_KARAOKE_TYPE_RB";
    ITMG_KARAOKE_TYPE[ITMG_KARAOKE_TYPE["ITMG_KARAOKE_TYPE_DANCE"] = 4] = "ITMG_KARAOKE_TYPE_DANCE";
    ITMG_KARAOKE_TYPE[ITMG_KARAOKE_TYPE["ITMG_KARAOKE_TYPE_HEAVEN"] = 5] = "ITMG_KARAOKE_TYPE_HEAVEN";
    ITMG_KARAOKE_TYPE[ITMG_KARAOKE_TYPE["ITMG_KARAOKE_TYPE_TTS"] = 6] = "ITMG_KARAOKE_TYPE_TTS";
})(ITMG_KARAOKE_TYPE || (exports.ITMG_KARAOKE_TYPE = ITMG_KARAOKE_TYPE = {}));
var ITMG_RANGE_AUDIO_MODE;
(function (ITMG_RANGE_AUDIO_MODE) {
    ITMG_RANGE_AUDIO_MODE[ITMG_RANGE_AUDIO_MODE["ITMG_RANGE_AUDIO_MODE_WORLD"] = 0] = "ITMG_RANGE_AUDIO_MODE_WORLD";
    ITMG_RANGE_AUDIO_MODE[ITMG_RANGE_AUDIO_MODE["ITMG_RANGE_AUDIO_MODE_TEAM"] = 1] = "ITMG_RANGE_AUDIO_MODE_TEAM";
})(ITMG_RANGE_AUDIO_MODE || (exports.ITMG_RANGE_AUDIO_MODE = ITMG_RANGE_AUDIO_MODE = {}));
var ITMG_AUDIO_ROUTE_TYPE;
(function (ITMG_AUDIO_ROUTE_TYPE) {
    ITMG_AUDIO_ROUTE_TYPE[ITMG_AUDIO_ROUTE_TYPE["AUDIO_ROUTE_TYPE_OTHERS"] = -1] = "AUDIO_ROUTE_TYPE_OTHERS";
    ITMG_AUDIO_ROUTE_TYPE[ITMG_AUDIO_ROUTE_TYPE["AUDIO_ROUTE_TYPE_BUILDINRECIEVER"] = 0] = "AUDIO_ROUTE_TYPE_BUILDINRECIEVER";
    ITMG_AUDIO_ROUTE_TYPE[ITMG_AUDIO_ROUTE_TYPE["AUDIO_ROUTE_TYPE_SPEAKER"] = 1] = "AUDIO_ROUTE_TYPE_SPEAKER";
    ITMG_AUDIO_ROUTE_TYPE[ITMG_AUDIO_ROUTE_TYPE["AUDIO_ROUTE_TYPE_HEADPHONE"] = 2] = "AUDIO_ROUTE_TYPE_HEADPHONE";
    ITMG_AUDIO_ROUTE_TYPE[ITMG_AUDIO_ROUTE_TYPE["AUDIO_ROUTE_TYPE_BLUETOOTH"] = 3] = "AUDIO_ROUTE_TYPE_BLUETOOTH";
})(ITMG_AUDIO_ROUTE_TYPE || (exports.ITMG_AUDIO_ROUTE_TYPE = ITMG_AUDIO_ROUTE_TYPE = {}));
