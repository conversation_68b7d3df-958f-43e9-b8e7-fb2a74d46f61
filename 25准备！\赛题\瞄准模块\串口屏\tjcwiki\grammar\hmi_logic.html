<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>HMI逻辑语句 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="数组/名称组使用说明" href="name_array.html" />
    <link rel="prev" title="跨页面赋值，全局变量操作" href="global_variable.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">书写语法</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="assignment_operation.html">赋值操作</a></li>
<li class="toctree-l2"><a class="reference internal" href="arithmetic_operation.html">运算操作</a></li>
<li class="toctree-l2"><a class="reference internal" href="global_variable.html">跨页面赋值，全局变量操作</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">HMI逻辑语句</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#if">if语句</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#if-1">if-示例1:</a></li>
<li class="toctree-l4"><a class="reference internal" href="#if-2">if-示例2:</a></li>
<li class="toctree-l4"><a class="reference internal" href="#if-3">if-示例3:</a></li>
<li class="toctree-l4"><a class="reference internal" href="#if-4">if-示例4:</a></li>
<li class="toctree-l4"><a class="reference internal" href="#if-5">if-示例5:</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#while">while语句</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#while-1">while-示例1:</a></li>
<li class="toctree-l4"><a class="reference internal" href="#while-2">while-示例2:</a></li>
<li class="toctree-l4"><a class="reference internal" href="#while-3">while-示例3:</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#for">for语句</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#for-1">for-示例1:</a></li>
<li class="toctree-l4"><a class="reference internal" href="#for-2">for-示例2:</a></li>
<li class="toctree-l4"><a class="reference internal" href="#for-3">for-示例3:</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="name_array.html">数组/名称组使用说明</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">书写语法</a> &raquo;</li>
      <li>HMI逻辑语句</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="hmi">
<h1>HMI逻辑语句<a class="headerlink" href="#hmi" title="此标题的永久链接"></a></h1>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>所有的逻辑语句只能在上位编辑状态下写入控件的事件中，不支持串口传输逻辑语句。</p>
<p>所有的判断语句都不支持多余空格，添加进任何空格，编译都会报错</p>
</div>
<section id="if">
<h2>if语句<a class="headerlink" href="#if" title="此标题的永久链接"></a></h2>
<section id="if-1">
<h3>if-示例1:<a class="headerlink" href="#if-1" title="此标题的永久链接"></a></h3>
<p>如果password.txt等于”123456”那么就切换到admin页面</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="k">if</span><span class="p">(</span><span class="n">password</span><span class="p">.</span><span class="n">txt</span><span class="o">==</span><span class="s">&quot;123456&quot;</span><span class="p">)</span><span class="w"></span>
<span class="linenos">2</span><span class="p">{</span><span class="w"></span>
<span class="linenos">3</span><span class="w">   </span><span class="n">page</span><span class="w"> </span><span class="n">admin</span><span class="w"></span>
<span class="linenos">4</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/hmi_logic_2.jpg" src="../_images/hmi_logic_2.jpg" />
</section>
<section id="if-2">
<h3>if-示例2:<a class="headerlink" href="#if-2" title="此标题的永久链接"></a></h3>
<p>如果t0.txt不等于”a”也不等于b”那么就切换到页面1</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="k">if</span><span class="p">(</span><span class="n">t0</span><span class="p">.</span><span class="n">txt</span><span class="o">!=</span><span class="s">&quot;a&quot;</span><span class="o">&amp;&amp;</span><span class="n">t0</span><span class="p">.</span><span class="n">txt</span><span class="o">!=</span><span class="s">&quot;b&quot;</span><span class="p">)</span><span class="w"></span>
<span class="linenos">2</span><span class="p">{</span><span class="w"></span>
<span class="linenos">3</span><span class="w">   </span><span class="n">page</span><span class="w"> </span><span class="mi">1</span><span class="w"></span>
<span class="linenos">4</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/hmi_logic_3.jpg" src="../_images/hmi_logic_3.jpg" />
</section>
<section id="if-3">
<h3>if-示例3:<a class="headerlink" href="#if-3" title="此标题的永久链接"></a></h3>
<p>如果  10 &lt; n0.val ≤ 100  那么就切换到页面1</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="k">if</span><span class="p">(</span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="o">&gt;</span><span class="mi">10</span><span class="o">&amp;&amp;</span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="o">&lt;=</span><span class="mi">100</span><span class="p">)</span><span class="w"></span>
<span class="linenos">2</span><span class="p">{</span><span class="w"></span>
<span class="linenos">3</span><span class="w">   </span><span class="n">page</span><span class="w"> </span><span class="mi">1</span><span class="w"></span>
<span class="linenos">4</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/hmi_logic_4.jpg" src="../_images/hmi_logic_4.jpg" />
</section>
<section id="if-4">
<h3>if-示例4:<a class="headerlink" href="#if-4" title="此标题的永久链接"></a></h3>
<p>以下语句写在b0按钮的按下事件中将实现b0的txt内容在开始和停止之间来回切换</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="k">if</span><span class="p">(</span><span class="n">b0</span><span class="p">.</span><span class="n">txt</span><span class="o">==</span><span class="s">&quot;开始&quot;</span><span class="p">)</span><span class="w"></span>
<span class="linenos">2</span><span class="p">{</span><span class="w"></span>
<span class="linenos">3</span><span class="w">   </span><span class="n">b0</span><span class="p">.</span><span class="n">txt</span><span class="o">=</span><span class="s">&quot;停止&quot;</span><span class="w"></span>
<span class="linenos">4</span><span class="p">}</span><span class="k">else</span><span class="w"></span>
<span class="linenos">5</span><span class="p">{</span><span class="w"></span>
<span class="linenos">6</span><span class="w">   </span><span class="n">b0</span><span class="p">.</span><span class="n">txt</span><span class="o">=</span><span class="s">&quot;开始&quot;</span><span class="w"></span>
<span class="linenos">7</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/hmi_logic_5.jpg" src="../_images/hmi_logic_5.jpg" />
</section>
<section id="if-5">
<h3>if-示例5:<a class="headerlink" href="#if-5" title="此标题的永久链接"></a></h3>
<p>以下语句写在b0按钮的按下事件中将实现b0的txt内容在1,2,3之间来回切换</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="k">if</span><span class="p">(</span><span class="n">b0</span><span class="p">.</span><span class="n">txt</span><span class="o">==</span><span class="s">&quot;1&quot;</span><span class="p">)</span><span class="w"></span>
<span class="linenos"> 2</span><span class="p">{</span><span class="w"></span>
<span class="linenos"> 3</span><span class="w">   </span><span class="n">b0</span><span class="p">.</span><span class="n">txt</span><span class="o">=</span><span class="s">&quot;2&quot;</span><span class="w"></span>
<span class="linenos"> 4</span><span class="p">}</span><span class="k">else</span><span class="w"> </span><span class="k">if</span><span class="p">(</span><span class="n">b0</span><span class="p">.</span><span class="n">txt</span><span class="o">==</span><span class="s">&quot;2&quot;</span><span class="p">)</span><span class="w"></span>
<span class="linenos"> 5</span><span class="p">{</span><span class="w"></span>
<span class="linenos"> 6</span><span class="w">   </span><span class="n">b0</span><span class="p">.</span><span class="n">txt</span><span class="o">=</span><span class="s">&quot;3&quot;</span><span class="w"></span>
<span class="linenos"> 7</span><span class="p">}</span><span class="k">else</span><span class="w"></span>
<span class="linenos"> 8</span><span class="p">{</span><span class="w"></span>
<span class="linenos"> 9</span><span class="w">   </span><span class="n">b0</span><span class="p">.</span><span class="n">txt</span><span class="o">=</span><span class="s">&quot;1&quot;</span><span class="w"></span>
<span class="linenos">10</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/hmi_logic_6.jpg" src="../_images/hmi_logic_6.jpg" />
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>1.数值类型变量支持:</p>
<blockquote>
<div><p>大于 &gt;</p>
<p>小于 &lt;</p>
<p>等于 ==</p>
<p>不等于 !=</p>
<p>大于等于 &gt;=</p>
<p>小于等于 &lt;=</p>
</div></blockquote>
<p>2.字符串类型仅支持:</p>
<blockquote>
<div><p>等于 ==</p>
<p>不等于 !=</p>
</div></blockquote>
<p>3.if判断的时候不支持括号优先级，比如:if((t0.txt==”a”||t0.txt==”b”)&amp;&amp;t1.txt==”1”)这样是不支持的。</p>
<p>4.if判断的时候不允许出现运算，比如:if(n0.val+2==3)这样是不支持的，请提前用中间变量计算好，再在if中判断</p>
<p>5.不要出现多余的空格,会导致编译时报错</p>
</div>
<img alt="../_images/hmi_logic_1.jpg" src="../_images/hmi_logic_1.jpg" />
</section>
</section>
<section id="while">
<h2>while语句<a class="headerlink" href="#while" title="此标题的永久链接"></a></h2>
<section id="while-1">
<h3>while-示例1:<a class="headerlink" href="#while-1" title="此标题的永久链接"></a></h3>
<p>n0.val一直自加到100为止，在自加过程中屏幕不会刷新显示，直到整个过程所有语句结束</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="k">while</span><span class="p">(</span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="o">&lt;</span><span class="mi">100</span><span class="p">)</span><span class="w"></span>
<span class="linenos">2</span><span class="p">{</span><span class="w"></span>
<span class="linenos">3</span><span class="w">   </span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="o">++</span><span class="w"></span>
<span class="linenos">4</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/hmi_logic_7.jpg" src="../_images/hmi_logic_7.jpg" />
</section>
<section id="while-2">
<h3>while-示例2:<a class="headerlink" href="#while-2" title="此标题的永久链接"></a></h3>
<p>while提前退出循环</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="n">sys0</span><span class="o">=</span><span class="n">va0</span><span class="p">.</span><span class="n">id</span><span class="w"></span>
<span class="linenos"> 2</span><span class="n">whileflag</span><span class="o">=</span><span class="mi">1</span><span class="w"></span>
<span class="linenos"> 3</span><span class="k">while</span><span class="p">(</span><span class="n">sys0</span><span class="o">&lt;</span><span class="mi">100</span><span class="o">&amp;&amp;</span><span class="n">whileflag</span><span class="o">==</span><span class="mi">1</span><span class="p">)</span><span class="w"></span>
<span class="linenos"> 4</span><span class="p">{</span><span class="w"></span>
<span class="linenos"> 5</span><span class="w">   </span><span class="n">sys0</span><span class="o">++</span><span class="w"></span>
<span class="linenos"> 6</span><span class="w">   </span><span class="k">if</span><span class="p">(</span><span class="n">b</span><span class="p">[</span><span class="n">sys0</span><span class="p">].</span><span class="n">val</span><span class="o">==</span><span class="mi">1</span><span class="p">)</span><span class="w"></span>
<span class="linenos"> 7</span><span class="w">   </span><span class="p">{</span><span class="w"></span>
<span class="linenos"> 8</span><span class="w">      </span><span class="c1">//提前退出循环</span>
<span class="linenos"> 9</span><span class="w">      </span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="o">=</span><span class="n">sys0</span><span class="w"></span>
<span class="linenos">10</span><span class="w">      </span><span class="n">whileflag</span><span class="o">=</span><span class="mi">0</span><span class="w"></span>
<span class="linenos">11</span><span class="w">   </span><span class="p">}</span><span class="w"></span>
<span class="linenos">12</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/hmi_logic_8.jpg" src="../_images/hmi_logic_8.jpg" />
</section>
<section id="while-3">
<h3>while-示例3:<a class="headerlink" href="#while-3" title="此标题的永久链接"></a></h3>
<p>n0.val一直自加到100为止，在自加过程中屏幕会一直不断的刷新n0控件的显示</p>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>1.在一个较多指令的过程执行中，或者在一个较长时间的循环语句中，系统所有控制权被此过程全部占用，在过程结束之前，尽管相应的内存数据可以任意正常读写，但是屏幕不会刷新显示，加入doevents后可以转让控制权给屏幕刷新，执行doevents之后，屏幕会刷新所有被改变过的控件，刷新完之后，控制权交回当前过程继续执行。防止屏幕呈现假死的显示状态。</p>
<p>2.while语句循环过程中，设备不会响应触摸事件，串口指令会接收到缓冲区，但不会执行，直到当前过程所有语句执行完毕为止，请慎重使用，以防进入死循环。</p>
</div>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="k">while</span><span class="p">(</span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="o">&lt;</span><span class="mi">100</span><span class="p">)</span><span class="w"></span>
<span class="linenos">2</span><span class="p">{</span><span class="w"></span>
<span class="linenos">3</span><span class="w">   </span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="o">++</span><span class="w"></span>
<span class="linenos">4</span><span class="w">   </span><span class="n">doevents</span><span class="w"></span>
<span class="linenos">5</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/hmi_logic_9.jpg" src="../_images/hmi_logic_9.jpg" />
</section>
</section>
<section id="for">
<h2>for语句<a class="headerlink" href="#for" title="此标题的永久链接"></a></h2>
<section id="for-1">
<h3>for-示例1:<a class="headerlink" href="#for-1" title="此标题的永久链接"></a></h3>
<p>n0.val一直自加到100为止，在自加过程中屏幕不会刷新显示，直到整个过程所有语句结束</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="k">for</span><span class="p">(</span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="o">=</span><span class="mi">0</span><span class="p">;</span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="o">&lt;</span><span class="mi">100</span><span class="p">;</span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="o">++</span><span class="p">)</span><span class="w"></span>
<span class="linenos">2</span><span class="p">{</span><span class="w"></span>
<span class="linenos">3</span><span class="w">   </span><span class="c1">//需要循环100次的代码</span>
<span class="linenos">4</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/hmi_logic_10.jpg" src="../_images/hmi_logic_10.jpg" />
</section>
<section id="for-2">
<h3>for-示例2:<a class="headerlink" href="#for-2" title="此标题的永久链接"></a></h3>
<p>满足条件后退出for循环</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="w"> </span><span class="k">for</span><span class="p">(</span><span class="n">sys0</span><span class="o">=</span><span class="n">n0</span><span class="p">.</span><span class="n">id</span><span class="p">;</span><span class="n">sys0</span><span class="o">&lt;</span><span class="n">n19</span><span class="p">.</span><span class="n">id</span><span class="p">;</span><span class="n">sys0</span><span class="o">++</span><span class="p">)</span><span class="w"></span>
<span class="linenos"> 2</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="linenos"> 3</span><span class="w">   </span><span class="k">if</span><span class="p">(</span><span class="n">b</span><span class="p">[</span><span class="n">sys0</span><span class="p">].</span><span class="n">val</span><span class="o">!=</span><span class="mi">0</span><span class="p">)</span><span class="w"></span>
<span class="linenos"> 4</span><span class="w">   </span><span class="p">{</span><span class="w"></span>
<span class="linenos"> 5</span><span class="w">      </span><span class="c1">//如果需要记录这个不为零的值，可以将它赋值给另一个变量</span>
<span class="linenos"> 6</span><span class="w">      </span><span class="n">sys1</span><span class="o">=</span><span class="n">sys0</span><span class="w"></span>
<span class="linenos"> 7</span>
<span class="linenos"> 8</span><span class="w">      </span><span class="c1">//使其不满足条件退出循环</span>
<span class="linenos"> 9</span><span class="w">      </span><span class="n">sys0</span><span class="o">=</span><span class="mi">100</span><span class="w"></span>
<span class="linenos">10</span><span class="w">   </span><span class="p">}</span><span class="w"></span>
<span class="linenos">11</span><span class="w"> </span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/hmi_logic_11.jpg" src="../_images/hmi_logic_11.jpg" />
</section>
<section id="for-3">
<h3>for-示例3:<a class="headerlink" href="#for-3" title="此标题的永久链接"></a></h3>
<p>n0.val一直自加到100为止，在自加过程中屏幕会一直不断的刷新n0控件的显示</p>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>1.在一个较多指令的过程执行中，或者在一个较长时间的循环语句中，系统所有控制权被此过程全部占用，在过程结束之前，尽管相应的内存数据可以任意正常读写，但是屏幕不会刷新显示，加入doevents后可以转让控制权给屏幕刷新，执行doevents之后，屏幕会刷新所有被改变过的控件，刷新完之后，控制权交回当前过程继续执行。防止屏幕呈现假死的显示状态。</p>
<p>2.for语句循环过程中，设备不会响应触摸事件，串口指令会接收到缓冲区，但不会执行，直到当前过程所有语句执行完毕为止，请慎重使用，以防进入死循环。</p>
</div>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="k">for</span><span class="p">(</span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="o">=</span><span class="mi">0</span><span class="p">;</span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="o">&lt;</span><span class="mi">100</span><span class="p">;</span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="o">++</span><span class="p">)</span><span class="w"></span>
<span class="linenos">2</span><span class="p">{</span><span class="w"></span>
<span class="linenos">3</span><span class="w">   </span><span class="c1">//需要循环100次的代码</span>
<span class="linenos">4</span><span class="w">   </span><span class="n">doevents</span><span class="w"></span>
<span class="linenos">5</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/hmi_logic_12.jpg" src="../_images/hmi_logic_12.jpg" />
</section>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="global_variable.html" class="btn btn-neutral float-left" title="跨页面赋值，全局变量操作" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="name_array.html" class="btn btn-neutral float-right" title="数组/名称组使用说明" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>