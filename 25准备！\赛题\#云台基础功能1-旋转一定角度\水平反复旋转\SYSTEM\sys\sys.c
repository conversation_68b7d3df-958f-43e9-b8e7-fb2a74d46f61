/**
 ******************************************************************************
 * @file    sys.c
 * <AUTHOR> Team & [Your Name]
 * @version V1.1
 * @date    2025-01-31
 * @brief   STM32F407系统配置和汇编函数源文件
 *          
 *          本文件实现了系统底层汇编函数，包括：
 *          - 中断控制函数
 *          - 低功耗控制函数  
 *          - 堆栈指针设置函数
 * 
 * @note    汇编函数说明:
 *          - 使用ARM Thumb指令集
 *          - 直接操作Cortex-M4内核寄存器
 *          - 提供系统级底层控制接口
 ******************************************************************************
 * @attention
 * 本程序基于正点原子STM32F407开发板例程修改
 * 原作者: 正点原子@ALIENTEK (www.openedv.com)
 * 修改: 添加详细注释和功能说明
 ******************************************************************************
 */

#include "sys.h"

/* 汇编函数实现 --------------------------------------------------------------*/

/**
 * @brief  执行WFI指令进入低功耗等待模式
 * @param  None
 * @retval None
 * @note   Wait For Interrupt - CPU进入睡眠状态
 *         等待任意中断唤醒，可有效降低系统功耗
 *         唤醒后从下一条指令继续执行
 */
__asm void WFI_SET(void)
{
	WFI
}

/**
 * @brief  关闭所有可屏蔽中断
 * @param  None
 * @retval None
 * @note   CPSID I - 清除PRIMASK，禁用IRQ中断
 *         不包括Fault和NMI等不可屏蔽中断
 *         用于进入临界区，保护重要代码段
 */
__asm void INTX_DISABLE(void)
{
	CPSID I
	BX lr
}

/**
 * @brief  开启所有可屏蔽中断
 * @param  None  
 * @retval None
 * @note   CPSIE I - 设置PRIMASK，使能IRQ中断
 *         恢复中断响应能力
 *         用于退出临界区，恢复正常中断处理
 */
__asm void INTX_ENABLE(void)
{
	CPSIE I
	BX lr
}

/**
 * @brief  设置主堆栈指针
 * @param  addr: 堆栈顶地址
 * @retval None
 * @note   MSR MSP, r0 - 设置主堆栈指针寄存器
 *         用于系统初始化或堆栈切换
 *         addr应指向RAM区域的有效地址
 *         通常在系统启动或任务切换时使用
 */
__asm void MSR_MSP(uint32_t addr) 
{
	MSR MSP, r0
	BX lr
}
