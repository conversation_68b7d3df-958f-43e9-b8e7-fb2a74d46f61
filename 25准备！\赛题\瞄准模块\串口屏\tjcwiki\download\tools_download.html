<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>常用工具下载 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="标准出厂工程样例" href="default_project.html" />
    <link rel="prev" title="开发文档下载" href="development_doc.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">资料下载</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="usart_hmi.html">上位机下载</a></li>
<li class="toctree-l2"><a class="reference internal" href="development_doc.html">开发文档下载</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">常用工具下载</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#sscom">sscom串口调试助手下载</a></li>
<li class="toctree-l3"><a class="reference internal" href="#vspd">虚拟串口工具vspd下载</a></li>
<li class="toctree-l3"><a class="reference internal" href="#tft-tftfiledownload">TFT文件下载助手(TFTFileDownload)</a></li>
<li class="toctree-l3"><a class="reference internal" href="#serialfileup">文件透传工具(SerialFileUp)</a></li>
<li class="toctree-l3"><a class="reference internal" href="#vc">VC++运行库合集</a></li>
<li class="toctree-l3"><a class="reference internal" href="#microsoft-net-framework-3-5">Microsoft .net Framework 3.5</a></li>
<li class="toctree-l3"><a class="reference internal" href="#excel-databox">数据记录控件数据转excel文件工具(DataBox)</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id10">常见串口驱动下载</a></li>
<li class="toctree-l3"><a class="reference internal" href="#powertoys">powertoys下载</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="default_project.html">标准出厂工程样例</a></li>
<li class="toctree-l2"><a class="reference internal" href="moreProject/index.html">官方样例工程</a></li>
<li class="toctree-l2"><a class="reference internal" href="ui_demo.html">UI样例工程</a></li>
<li class="toctree-l2"><a class="reference internal" href="other_project.html">网友提供应用样例</a></li>
<li class="toctree-l2"><a class="reference internal" href="tjc_game.html">游戏工程</a></li>
<li class="toctree-l2"><a class="reference internal" href="font_download.html">免费字体下载</a></li>
<li class="toctree-l2"><a class="reference internal" href="voice_download.html">声音资源下载</a></li>
<li class="toctree-l2"><a class="reference internal" href="scheme_download.html">原理图下载</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">资料下载</a> &raquo;</li>
      <li>常用工具下载</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>常用工具下载<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<section id="sscom">
<h2>sscom串口调试助手下载<a class="headerlink" href="#sscom" title="此标题的永久链接"></a></h2>
<p>说明：sscom5.13.1，比较常用的串口调试助手，调试淘晶驰串口屏必须使用5.13.1这个版本</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/常用工具下载/sscom5.13.1.zip">sscom串口助手下载</a></p>
<p>sscom串口调试助手使用说明</p>
<p><a class="reference internal" href="../debug/debug_with_sscom.html#sscom"><span class="std std-ref">串口助手软件(sscom)和屏幕联调</span></a></p>
<p><a class="reference internal" href="../debug/debug_with_vspd.html#id1"><span class="std std-ref">串口助手软件和串口屏模拟器联调1</span></a></p>
<p><a class="reference internal" href="../debug/debug_with_vspd2.html#id1"><span class="std std-ref">串口助手软件和串口屏模拟器联调2</span></a></p>
</section>
<hr class="docutils" />
<section id="vspd">
<h2>虚拟串口工具vspd下载<a class="headerlink" href="#vspd" title="此标题的永久链接"></a></h2>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/常用工具下载/虚拟串口工具vspd.zip">《虚拟串口工具vspd》</a></p>
</section>
<section id="tft-tftfiledownload">
<h2>TFT文件下载助手(TFTFileDownload)<a class="headerlink" href="#tft-tftfiledownload" title="此标题的永久链接"></a></h2>
<p>说明：此工具专用于tft文件的串口下载，没有其他功能。</p>
<p>使用方法： <a class="reference internal" href="../QA/QA107.html#tft-tftfiledownload"><span class="std std-ref">如何使用TFT文件下载助手(TFTFileDownload)下载工程</span></a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/常用工具下载/TFTFileDownload/淘晶驰TFT文件下载助手(TFTFileDownload).zip">TFTFileDownload软件下载</a></p>
<p>TFTFileDownload工具是淘晶驰开源的下载工具，您可以下载源码进行二次开发，此工程基于visual studio进行开发，使用的语言为c#，需要您熟练掌握c#语言，淘晶驰不对TFTFileDownload二次开发提供技术支持</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/常用工具下载/TFTFileDownload/淘晶驰TFT文件下载助手(TFTFileDownload)源码.zip">TFTFileDownload源代码下载</a></p>
<p>如果需要自行开发对应的下载工具，详细的下载协议请参考  <a class="reference internal" href="../advanced/hmi_download_protocol.html#hmi-ota"><span class="std std-ref">HMI下载协议详解/OTA升级</span></a></p>
<p>相关链接</p>
<p><a class="reference internal" href="../QA/QA77.html#tft"><span class="std std-ref">TFT文件如何下载到串口屏中</span></a></p>
<p><a class="reference internal" href="../start/create_project/create_project_10.html#tft"><span class="std std-ref">如何输出TFT生产文件</span></a></p>
<hr class="docutils" />
</section>
<section id="serialfileup">
<h2>文件透传工具(SerialFileUp)<a class="headerlink" href="#serialfileup" title="此标题的永久链接"></a></h2>
<p>说明：此工具用于电脑通过串口透传文件到屏幕的内存中或SD卡，根据高级应用文档说明使用VS2008开发，开发语言为C#,源代码完全开源。</p>
<img alt="../_images/SerialFileUp.png" src="../_images/SerialFileUp.png" />
<p>以我这里测试为例，实际使用，导入图片文件good.jpg，选中存到屏幕SD卡上，端口号为3，屏幕通讯波特率9600，每包数据包大小4000字节，校验方式为crc16校验，点击Start开始下载。等待下方进度条填满并且下方cmd输出信息框提示File Send SUC说明文件传输成功</p>
<img alt="../_images/SerialFileUp2.png" src="../_images/SerialFileUp2.png" />
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/常用工具下载/SerialFileUp/淘晶驰文件透传工具(SerialFileUp).zip">SerialFileUp软件下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/常用工具下载/SerialFileUp/淘晶驰文件透传工具(SerialFileUp)源码.zip">SerialFileUp源代码下载</a></p>
<hr class="docutils" />
</section>
<section id="vc">
<h2>VC++运行库合集<a class="headerlink" href="#vc" title="此标题的永久链接"></a></h2>
<p>说明：微软常用运行库合集是采用microsoft visual studio 20xx编写的软件必须使用的公用dll运行库，是目前为止最全最新的运行库集合，相当于程序的字典文件。 某些网上和论坛的部分精简软件没有附带这些公用dll，所以安装这些运行库是系统安装后第一件要做的事情。所有的安装文件全部来自微软官方网站，且为最新数字签名版本。</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/gongju/vc.zip">VC++ 运行库合集下载</a></p>
<hr class="docutils" />
</section>
<section id="microsoft-net-framework-3-5">
<span id="frameworkdownload"></span><h2>Microsoft .net Framework 3.5<a class="headerlink" href="#microsoft-net-framework-3-5" title="此标题的永久链接"></a></h2>
<p>说明：USART HMI界面设计软件必须要系统有framework 3.5才能正常运行，假如你的系统只有framework 4.0,没有3.5也是不行的，微软的framework 4.0并不完全兼容framework 3.5。每个版本都各不兼容，所以必须要系统有framework 3.5才能正常运行。</p>
<p>win7系统是自带framework 3.5和framwork 4.0两个版本都有的，可以直接运行USART HMI，但是某些ghost版本可能精简掉framework 3.5。</p>
<p>XP系统系统没有自带，所以XP系统一定要安装framework 3.5。</p>
<p>win10和win11系统需要联网才能安装Framework3.5，请不要关闭win10和win11自动升级服务，否则会导致自动下载失败，如果自动安装.NET Framework 3.5失败，请开启所有的自动升级相关服务，然后重启电脑，重新打开USART HMI根据提示进行自动安装</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/USARTHMI/gongju/dotnetfx35.rar">Fmework3.5运行环境下载(xp、win7)</a></p>
<p>win10离线安装.NET Framework 3.5请参考 <a class="reference external" href="https://blog.csdn.net/wlz527525671/article/details/113179084">Win10内网离线安装NET3.5，绝对成功</a></p>
<p>部分客户反馈上面的连接需要付费才能查看，所以把文章贴出来，上文链接的文章内容如下</p>
<p>信息化运维有时候需要net3.5组件,cdsn爬了太多坑，总结其实就一条，不同大版本win10的镜像内net3.5不能混刷，下面各镜像版本的net3.5已经提取出来了 ，把我提取的文件放在C盘根目录，在管理员模式下powershell或者cmd复制下面脚本直接刷就行了。大家去替换路径名刷入就行!</p>
<p>如何查看windows版本</p>
<div class="highlight-powershell notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="n">cmd输入winver查看window版本版本信息</span>
</pre></div>
</div>
<p>如何安装.net Framework 3.5</p>
<div class="highlight-powershell notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="n">dism</span><span class="p">.</span><span class="n">exe</span> <span class="p">/</span><span class="n">online</span> <span class="p">/</span><span class="nb">enable-feature</span> <span class="p">/</span><span class="n">featurename</span><span class="p">:</span><span class="n">netfx3</span> <span class="p">/</span><span class="n">Source</span><span class="p">:</span><span class="n">解压后的路径</span><span class="p">\</span><span class="n">sxs</span>
</pre></div>
</div>
<p>例如下载后解压的路径为D:DownloaddotnetFramework35.7z</p>
<p>解压后的路径为D:DownloaddotnetFramework35sxs</p>
<p>则完整的安装指令为</p>
<div class="highlight-powershell notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="n">dism</span><span class="p">.</span><span class="n">exe</span> <span class="p">/</span><span class="n">online</span> <span class="p">/</span><span class="nb">enable-feature</span> <span class="p">/</span><span class="n">featurename</span><span class="p">:</span><span class="n">netfx3</span> <span class="p">/</span><span class="n">Source</span><span class="p">:</span><span class="n">D</span><span class="p">:\</span><span class="n">Download</span><span class="p">\</span><span class="n">dotnetFramework35</span><span class="p">\</span><span class="n">sxs</span>
</pre></div>
</div>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/常用工具下载/dotnetFramework/1809/dotnetFramework35.7z">1809可独装</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/常用工具下载/dotnetFramework/1903/dotnetFramework35.7z">1903-1909可混装</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/常用工具下载/dotnetFramework/2004-21h2/dotnetFramework35.7z">2004-20h2-22h1</a></p>
<p>另附微软官网的.NET Framework 3.5 SP1 Offline Installer下载</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/常用工具下载/dotnetFramework3-5/dotnetfx35sp1offline.exe">dotnetfx35sp1offline.exe</a></p>
<hr class="docutils" />
</section>
<section id="excel-databox">
<h2>数据记录控件数据转excel文件工具(DataBox)<a class="headerlink" href="#excel-databox" title="此标题的永久链接"></a></h2>
<p>说明：此工具可以将数据记录控件生成的data文件转换为csv文件。</p>
<p>csv文件可以用office套件中的excel或者金山wps直接打开。</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/常用工具下载/数据记录转换工具.zip">DataBox软件下载</a></p>
</section>
<hr class="docutils" />
<section id="id10">
<h2>常见串口驱动下载<a class="headerlink" href="#id10" title="此标题的永久链接"></a></h2>
<p>驱动来自网络，淘晶驰不保证其可靠性和安全性</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/常用工具下载/常见串口驱动.zip">常见串口驱动下载</a></p>
<img alt="../_images/blank.png" src="../_images/blank.png" />
</section>
<hr class="docutils" />
<section id="powertoys">
<h2>powertoys下载<a class="headerlink" href="#powertoys" title="此标题的永久链接"></a></h2>
<p>PowerToys0.90.1-x64</p>
<p>PowerToys(微软小工具)，可以使用其中的“图像大小调整器”辅助调整图片分辨率</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/常用工具下载/PowerToys.zip">powertoys下载</a></p>
<hr class="docutils" />
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="development_doc.html" class="btn btn-neutral float-left" title="开发文档下载" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="default_project.html" class="btn btn-neutral float-right" title="标准出厂工程样例" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>