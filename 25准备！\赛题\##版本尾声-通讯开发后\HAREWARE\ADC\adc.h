/**
 ******************************************************************************
 * @file    adc.h
 * <AUTHOR> Name]
 * @version V1.0
 * @date    2025-01-31
 * @brief   STM32F407 ADC1模拟数字转换器驱动头文件
 *          
 *          本文件定义了ADC1的初始化和数据采集接口
 *          主要用于电压监测和模拟信号采集
 * 
 * @note    硬件连接:
 *          PC0 -> ADC1_IN10 (电压检测输入)
 *          
 *          ADC配置参数:
 *          - 12位分辨率 (0-4095)
 *          - 参考电压: 3.3V
 *          - 单次转换模式
 *          - 右对齐数据格式
 ******************************************************************************
 */

#ifndef __ADC_H
#define __ADC_H

#include "sys.h"

/* 函数声明 ------------------------------------------------------------------*/

/**
 * @brief  ADC1初始化
 * @param  None
 * @retval None
 * @note   配置ADC1在PC0引脚进行12位精度的电压采集
 *         采样时间设置为480个周期，提高转换精度
 */
void ADC1_Init(void);

/**
 * @brief  获取ADC多次采样的平均值
 * @param  ADC_Channel: ADC通道号 (0-15)
 * @param  times: 采样次数 (建议10-50次)
 * @retval ADC转换结果的平均值 (0-4095)
 * @note   通过多次采样并求平均值来提高测量精度
 *         每次采样间隔5ms，减少噪声干扰
 *         返回值需要根据具体应用转换为实际电压值
 */
u16 Get_adc_Average(u8 ADC_Channel, u8 times);

#endif
