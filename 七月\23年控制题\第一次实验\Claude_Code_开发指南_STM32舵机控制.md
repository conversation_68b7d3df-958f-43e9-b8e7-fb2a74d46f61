# Claude Code 开发指南：STM32F407ZGT6控制总线舵机项目

> **文档创建日期**: 2025年7月17日  
> **项目目标**: 实现按键K1控制HTS-25L总线舵机30度步进旋转  
> **开发平台**: STM32F407ZGT6 + Keil 5 + 标准库  
> **协作模式**: Augment分析规划 + Claude Code编码实现  

---

## 🎯 项目概述

### 核心功能需求
- **触发方式**: 按下板载按键K1(PE3)
- **舵机动作**: 朝一个方向旋转30度
- **循环逻辑**: 转到头(240度)后复位到0度，继续循环
- **硬件配置**: STM32F407ZGT6 + HTS-25L总线舵机

### 技术指标
- **通信协议**: UART半双工，115200波特率
- **角度精度**: 0.24°/计数（0-1000对应0°-240°）
- **30度步进**: 每次增加125计数值
- **运动时间**: 1000ms保证平稳运动

---

## 🏗️ 硬件连接方案

### 引脚分配
```
STM32F407ZGT6 引脚连接：
├── 按键K1: PE3 (上拉输入，按下为低电平)
├── LED0: PF9 (推挽输出，低电平点亮)
├── LED1: PF10 (推挽输出，低电平点亮)
└── 舵机通信: PA2 (USART2半双工)

HTS-25L舵机连接：
├── VIN: 7.4V独立电源
├── GND: 与STM32共地
└── SIG: 连接PA2
```

### 舵机协议要点
```
帧格式: 0x55 0x55 ID Length Cmd Params Checksum
校验和: ~((ID+Length+Cmd+Params) & 0xFF)
角度转换: pos = (angle/240.0) * 1000
舵机ID: 1 (出厂默认)
```

---

## 📋 开发任务清单

### 🔧 Layer 1: 硬件配置 (优先级: 高)

#### ✅ 任务1.1: UART半双工通信配置
**目标**: 配置USART2为半双工模式，波特率115200
**技术要求**:
- 使用PA2引脚，配置为复用功能
- 半双工模式：发送完成后自动切换到接收
- 超时设置：发送20ms，接收100ms

**代码框架**:
```c
// 在User目录创建 hts25l_driver.h 和 hts25l_driver.c
void HTS25L_UART_Init(void);
int HTS25L_SendPacket(uint8_t id, uint8_t cmd, uint8_t *params, uint8_t param_len);
int HTS25L_ReadPacket(uint8_t expect_id, uint8_t expect_cmd, uint8_t *payload, uint8_t *len);
```

**验收标准**:
- [ ] 能正确发送协议帧到舵机
- [ ] 能接收舵机应答数据
- [ ] 通信无丢包和校验错误

#### ✅ 任务1.2: 按键和LED GPIO配置
**目标**: 配置PE3按键输入和PF9/PF10 LED输出
**技术要求**:
- PE3: 上拉输入，20ms软件消抖
- PF9/PF10: 推挽输出，共阳极LED控制

**代码框架**:
```c
// 在现有main.c的GPIO_Init_Config()函数中添加
void Button_Init(void);
uint8_t Button_Scan(void);  // 返回1表示K1按下
void LED_Control(uint8_t led, uint8_t state);  // led: 0/1, state: 0关闭/1点亮
```

**验收标准**:
- [ ] 按键按下能稳定检测，无抖动
- [ ] LED能正确指示系统状态

### ⚙️ Layer 2: 基础功能 (优先级: 高)

#### ✅ 任务2.1: HTS-25L协议帧封装
**目标**: 实现协议帧构建、校验和发送功能
**技术要求**:
- 支持MOVE_TIME_WRITE(0x01)和POS_READ(0x1C)指令
- 正确的校验和计算
- 小端序数据封装

**代码框架**:
```c
// HTS-25L指令定义
#define HTS25L_CMD_MOVE_TIME_WRITE  0x01
#define HTS25L_CMD_POS_READ         0x1C

// 核心函数
int HTS25L_MoveTimeWrite(uint8_t id, uint16_t pos, uint16_t time_ms);
int HTS25L_ReadPos(uint8_t id, uint16_t *pos);
uint16_t HTS25L_AngleDegToPos(float deg);  // 角度转位置
float HTS25L_PosToAngleDeg(uint16_t pos);  // 位置转角度
```

**验收标准**:
- [ ] 能发送正确的MOVE_TIME_WRITE指令
- [ ] 能接收和解析POS_READ应答
- [ ] 校验和计算100%正确

#### ✅ 任务2.2: 舵机位置读取功能
**目标**: 实现读取舵机当前位置并转换为角度
**技术要求**:
- 发送POS_READ指令获取2字节位置数据
- 位置到角度转换：angle = (pos/1000.0) * 240.0

**验收标准**:
- [ ] 能准确读取舵机当前位置
- [ ] 位置数据转换为角度值正确

### 🧠 Layer 3: 核心算法 (优先级: 高)

#### ✅ 任务3.1: 30度步进控制算法
**目标**: 实现每次按键30度旋转和边界复位逻辑
**技术要求**:
- 30度对应125计数增量
- 边界检测：0度(pos=0)到240度(pos=1000)
- 到达240度后复位到0度

**代码框架**:
```c
// 全局变量
static uint16_t current_target_pos = 0;  // 当前目标位置
static uint8_t servo_state = 0;  // 0:空闲 1:运动中

// 核心函数
void Servo_Step30Degree(void);  // 执行30度步进
uint8_t Servo_IsMoving(void);   // 检查是否在运动
void Servo_Reset(void);         // 复位到0度
```

**验收标准**:
- [ ] 每次按键精确旋转30度
- [ ] 到达240度后正确复位到0度
- [ ] 运动过程平稳无抖动

#### ✅ 任务3.2: 按键状态机实现
**目标**: 实现按键触发的状态机，防止重复触发
**技术要求**:
- 状态：IDLE、MOVING、READING
- 按键只在IDLE状态有效
- 运动完成后自动返回IDLE

**代码框架**:
```c
typedef enum {
    SERVO_STATE_IDLE = 0,
    SERVO_STATE_MOVING,
    SERVO_STATE_READING
} ServoState_t;

void Servo_StateMachine(void);  // 主状态机
void Servo_ProcessButton(void); // 按键处理
```

**验收标准**:
- [ ] 按键响应及时且无重复触发
- [ ] 舵机运动期间按键无效
- [ ] 状态切换逻辑正确

### 🔗 Layer 4: 系统集成 (优先级: 中)

#### ✅ 任务4.1: 主程序集成
**目标**: 集成所有功能到main.c主循环
**技术要求**:
- 基于现有main.c模板集成
- 保持LED心跳指示功能
- 添加舵机控制主循环

**代码框架**:
```c
int main(void)
{
    // 系统初始化
    System_Init();
    GPIO_Init_Config();
    HTS25L_UART_Init();
    
    // 舵机初始化
    Servo_Reset();
    
    while(1)
    {
        // 按键处理
        Servo_ProcessButton();
        
        // 状态机处理
        Servo_StateMachine();
        
        // LED心跳指示
        LED_Heartbeat();
        
        Delay_ms(10);
    }
}
```

**验收标准**:
- [ ] 系统启动后LED正常闪烁
- [ ] 按下K1按键舵机按预期动作
- [ ] 整体功能稳定可靠

#### ✅ 任务4.2: 错误处理和异常保护
**目标**: 添加通信超时和错误处理机制
**技术要求**:
- UART通信超时：100ms
- 舵机无响应重试机制
- LED指示不同错误状态

**验收标准**:
- [ ] 通信异常时系统不死机
- [ ] 能通过LED判断系统状态
- [ ] 异常恢复后功能正常

---

## 🛠️ 开发环境配置

### 工程文件结构
```
STM32F407ZGT6_Template/
├── User/
│   ├── main.c              (主程序 - 需要修改)
│   ├── hts25l_driver.h     (舵机驱动头文件 - 新建)
│   ├── hts25l_driver.c     (舵机驱动实现 - 新建)
│   └── stm32f4xx_conf.h    (需要启用USART)
├── System/
│   └── Delay.c             (延时函数 - 已有)
└── Library/                (标准库 - 已有)
```

### 编译配置要点
- **Device**: STM32F407ZGTx
- **Compiler**: ARM Compiler 5.06
- **Optimization**: Level 0 (调试阶段)
- **Include Paths**: .\User, .\System, .\Library

---

## 🔍 调试验证方法

### 分步测试策略
1. **通信测试**: 先测试UART发送接收
2. **协议测试**: 验证协议帧格式和校验
3. **单步测试**: 测试单次30度旋转
4. **循环测试**: 验证完整循环逻辑
5. **异常测试**: 测试错误处理机制

### LED状态指示
```c
// LED指示定义
#define LED_HEARTBEAT    0  // LED0心跳闪烁
#define LED_ERROR        1  // LED1错误指示

// 状态指示模式
// 正常: LED0慢闪，LED1熄灭
// 通信错误: LED0快闪，LED1常亮
// 运动中: LED0常亮，LED1熄灭
```

---

## ⚠️ 重要注意事项

### 硬件安全
- **供电要求**: 舵机使用7.4V独立电源，必须与STM32共地
- **ID设置**: 确保舵机ID设置为1，避免冲突
- **连接检查**: PA2与舵机SIG正确连接

### 软件要点
- **半双工时序**: 发送完成后立即切换接收状态
- **边界处理**: 严格检查0-1000位置范围
- **状态管理**: 确保状态机逻辑正确，避免死锁

### 调试建议
- **串口输出**: 可用USART1(PA9/PA10)输出调试信息
- **LED指示**: 充分利用LED指示系统状态
- **分步验证**: 逐个功能模块验证，确保稳定

---

## 🚀 开发时序建议

### 第一阶段 (3小时)
1. 完成任务1.1: UART半双工配置
2. 完成任务2.1: 协议帧封装
3. 验证基础通信功能

### 第二阶段 (4小时)
4. 完成任务3.1: 30度步进算法
5. 完成任务1.2: 按键LED配置
6. 完成任务2.2: 位置读取功能

### 第三阶段 (3.5小时)
7. 完成任务3.2: 按键状态机
8. 完成任务4.1: 主程序集成
9. 完成任务4.2: 错误处理

**总预估时间**: 10.5小时

---

---

## 📚 关键代码框架参考

### HTS-25L驱动头文件框架 (hts25l_driver.h)
```c
#ifndef HTS25L_DRIVER_H
#define HTS25L_DRIVER_H

#include "stm32f4xx.h"

// 协议常量定义
#define HTS25L_HEADER               0x55
#define HTS25L_BROADCAST_ID         0xFE
#define HTS25L_CMD_MOVE_TIME_WRITE  0x01
#define HTS25L_CMD_POS_READ         0x1C

// 角度位置转换
#define HTS25L_POS_MAX              1000
#define HTS25L_ANGLE_MAX_DEG        240.0f
#define HTS25L_STEP_30_DEG          125     // 30度对应的位置增量

// 驱动结构体
typedef struct {
    USART_TypeDef *USARTx;
    uint32_t timeout_ms;
} HTS25L_Driver_t;

// 公共接口函数
void HTS25L_Init(HTS25L_Driver_t *drv);
int HTS25L_MoveTimeWrite(HTS25L_Driver_t *drv, uint8_t id, uint16_t pos, uint16_t time_ms);
int HTS25L_ReadPos(HTS25L_Driver_t *drv, uint8_t id, uint16_t *pos);
uint16_t HTS25L_AngleDegToPos(float deg);
float HTS25L_PosToAngleDeg(uint16_t pos);

#endif
```

### 主程序状态机框架 (main.c关键部分)
```c
// 全局变量定义
static HTS25L_Driver_t hts_driver;
static uint16_t current_target_pos = 0;
static uint32_t last_button_time = 0;

typedef enum {
    SERVO_STATE_IDLE = 0,
    SERVO_STATE_MOVING,
    SERVO_STATE_READING
} ServoState_t;

static ServoState_t servo_state = SERVO_STATE_IDLE;

// 按键处理函数
void Servo_ProcessButton(void)
{
    static uint8_t last_key_state = 1;
    uint8_t current_key_state = GPIO_ReadInputDataBit(GPIOE, GPIO_Pin_3);

    // 检测按键下降沿
    if(last_key_state == 1 && current_key_state == 0)
    {
        if(servo_state == SERVO_STATE_IDLE)
        {
            // 触发30度步进
            Servo_Step30Degree();
        }
    }
    last_key_state = current_key_state;
}

// 30度步进函数
void Servo_Step30Degree(void)
{
    uint16_t next_pos = current_target_pos + HTS25L_STEP_30_DEG;

    // 边界检查和复位逻辑
    if(next_pos > HTS25L_POS_MAX)
    {
        next_pos = 0;  // 复位到0度
    }

    // 发送运动指令
    if(HTS25L_MoveTimeWrite(&hts_driver, 1, next_pos, 1000) == 0)
    {
        current_target_pos = next_pos;
        servo_state = SERVO_STATE_MOVING;
        // LED指示运动状态
        GPIO_ResetBits(GPIOF, GPIO_Pin_9);  // LED0常亮
    }
}
```

### UART半双工配置框架
```c
void HTS25L_UART_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;

    // 使能时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART2, ENABLE);

    // 配置PA2为复用功能
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_OD;  // 开漏输出
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;   // 上拉
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    GPIO_PinAFConfig(GPIOA, GPIO_PinSource2, GPIO_AF_USART2);

    // 配置USART2半双工模式
    USART_InitStructure.USART_BaudRate = 115200;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;
    USART_Init(USART2, &USART_InitStructure);

    // 使能半双工模式
    USART_HalfDuplexCmd(USART2, ENABLE);
    USART_Cmd(USART2, ENABLE);
}
```

---

## 🔧 stm32f4xx_conf.h 配置要求

确保在 `User/stm32f4xx_conf.h` 中启用以下外设：
```c
#define USE_STDPERIPH_DRIVER
// 启用以下外设库
#include "stm32f4xx_rcc.h"
#include "stm32f4xx_gpio.h"
#include "stm32f4xx_usart.h"
#include "misc.h"
```

---

## 📋 完整开发检查清单

### 🔍 开发前检查
- [ ] 确认舵机ID设置为1
- [ ] 验证7.4V电源和共地连接
- [ ] 检查PA2与舵机SIG连接
- [ ] 确认Keil工程配置正确

### 💻 编码实现检查
- [ ] 创建hts25l_driver.h和hts25l_driver.c文件
- [ ] 修改main.c集成舵机控制逻辑
- [ ] 配置stm32f4xx_conf.h启用USART
- [ ] 实现所有Layer 1-4的任务功能

### 🧪 测试验证检查
- [ ] 编译无错误和警告
- [ ] 下载程序到开发板成功
- [ ] LED心跳指示正常工作
- [ ] 按键K1响应正常
- [ ] 舵机30度步进动作正确
- [ ] 到达240度后正确复位
- [ ] 通信异常处理正常

### 🚀 最终验收检查
- [ ] 连续按键测试稳定性
- [ ] 长时间运行无死机
- [ ] 所有LED状态指示正确
- [ ] 舵机运动平稳无抖动
- [ ] 边界条件处理正确

---

## 📞 技术支持信息

### 关键技术参数速查
```
舵机型号: HTS-25L
通信波特率: 115200 bps
位置范围: 0-1000 (对应0°-240°)
30度步进: 125计数
运动时间: 1000ms
舵机ID: 1
```

### 常见问题解决
1. **通信无响应**: 检查波特率、共地、半双工配置
2. **校验和错误**: 验证校验算法实现
3. **按键无响应**: 检查GPIO配置和消抖逻辑
4. **舵机不动**: 确认供电和ID设置

---

**Claude Code，现在可以开始按照此指南进行编码实现！** 🎯

**建议开发顺序**: 任务1.1 → 任务2.1 → 任务3.1 → 任务1.2 → 其他任务
