/**
 ******************************************************************************
 * @file    main.c
 * <AUTHOR>
 * @version V4.0
 * @date    2025-08-02
 * @brief   STM32F407 K230通信瞄准控制系统
 *          
 *          本程序实现与K230视觉模块的通信和PID控制
 *          支持6字节协议接收和双轴电机精确控制
 * 
 * @note    系统配置:
 *          - 主控: STM32F407ZGT6 @ 168MHz
 *          - 通信: UART1 115200bps 与K230连接
 *          - 驱动: D36A双路步进电机驱动器 (1/16细分)
 *          
 *          控制逻辑:
 *          - 接收K230发送的6字节控制协议
 *          - PID算法处理像素偏差
 *          - 水平轴电机速度自适应控制
 ******************************************************************************
 * @attention
 * 本程序用于2025年全国大学生电子设计竞赛E题：简易自行瞄准装置
 * K230视觉通信版本 - 支持实时瞄准控制
 ******************************************************************************
 */

#include "sys.h"
#include "../HAREWARE/K230_COMM/k230_comm.h"
#include "../HAREWARE/KEY/KEY.h"

// 全局系统时间计数器 (毫秒)
volatile uint32_t system_tick_ms = 0;

// 瞄准系统状态定义
typedef enum {
	AIM_STATE_IDLE = 0,     // 空闲状态，等待按键
	AIM_STATE_WORKING,      // 工作状态，正在瞄准
	AIM_STATE_COMPLETED     // 完成状态，瞄准成功
} AimState_t;

// 全局状态变量
static AimState_t aim_state = AIM_STATE_IDLE;
static volatile bool aim_completed_flag = false;  // 瞄准完成标志

/**
 * @brief  瞄准完成回调函数（由K230_COMM模块调用）
 * @param  None
 * @retval None
 * @note   当K230检测到瞄准完成时，会调用此函数设置完成标志
 */
void Set_Aim_Completed_Flag(void)
{
	aim_completed_flag = true;
}

/**
 * @brief  主函数 - K230通信瞄准控制系统
 * @param  None
 * @retval None
 * @note   程序入口点，实现K230通信和电机控制集成
 */
int main(void)
{
	/* ===== 系统基础初始化 ===== */
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2); // 设置中断分组为2
	delay_init(168);                                 // 延时初始化(F407为168MHz)
	uart_init(115200);                               // K230通信串口初始化
	
	/* ===== 按键初始化 ===== */
	Key_Init();                                      // K0按键初始化(PE4)
	
	/* ===== 电机控制系统初始化 ===== */
	if(!Motor_System_Init()) {
		// 初始化失败则进入死循环
		while(1) {
			delay_ms(1000);
		}
	}
	
	/* ===== K230通信模块初始化 ===== */
	if(!K230_Comm_Init()) {
		// 初始化失败则进入死循环
		while(1) {
			delay_ms(1000);
		}
	}
	
	/* ===== 系统就绪，开始运行 ===== */
	// 使能水平轴电机
	Motor_Enable(MOTOR_AXIS_HORIZONTAL);
	
	// 设置默认速度
	Motor_SetSpeed(MOTOR_AXIS_HORIZONTAL, MOTOR_HORIZONTAL_SPEED_MEDIUM);
	
	delay_ms(1000); // 系统稳定延时
	
	/* ===== 主循环：按键控制和瞄准系统 ===== */
	while(1)
	{
		// 按键检测和状态控制
		if(Key_Scan_Debounce()) {
			printf("按键被按下! 当前状态: %d\n", aim_state);  // 调试信息
			switch(aim_state) {
				case AIM_STATE_IDLE:
					// 空闲状态，按键启动瞄准
					aim_state = AIM_STATE_WORKING;
					printf("START\n");  // 发送启动指令给K230
					printf("发送START指令给K230\n");  // 调试信息
					Motor_Enable(MOTOR_AXIS_HORIZONTAL);  // 使能电机
					break;
					
				case AIM_STATE_WORKING:
					// 工作状态，按键停止瞄准
					aim_state = AIM_STATE_IDLE;
					printf("STOP\n");   // 发送停止指令给K230
					printf("发送STOP指令给K230\n");  // 调试信息
					Motor_Disable(MOTOR_AXIS_HORIZONTAL); // 禁用电机
					break;
					
				case AIM_STATE_COMPLETED:
					// 完成状态，按键重置为空闲
					aim_state = AIM_STATE_IDLE;
					printf("STOP\n");   // 发送停止指令给K230
					printf("重置为空闲状态\n");  // 调试信息
					break;
			}
		}
		
		// 只在工作状态处理K230数据
		if(aim_state == AIM_STATE_WORKING) {
			// 处理K230数据包
			K230_Process_Frame();
			
			// 检查通信超时
			K230_Check_Timeout();
			
			// 检查瞄准完成标志
			if(aim_completed_flag) {
				aim_state = AIM_STATE_COMPLETED;
				aim_completed_flag = false;  // 清除标志
				Motor_Disable(MOTOR_AXIS_HORIZONTAL); // 禁用电机
				printf("瞄准任务完成，系统自动停止\n");
			}
		}
		
		// 短暂延时，避免过度占用CPU
		delay_ms(10);
	}
}
