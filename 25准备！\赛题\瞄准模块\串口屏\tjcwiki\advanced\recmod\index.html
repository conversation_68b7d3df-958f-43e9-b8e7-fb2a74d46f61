<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>主动解析模式应用详解 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
        <script src="../../_static/jquery.js"></script>
        <script src="../../_static/underscore.js"></script>
        <script src="../../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../../_static/doctools.js"></script>
        <script src="../../_static/translations.js"></script>
        <script src="../../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../../_static/custom.js"></script>
    <script src="../../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../../genindex.html" />
    <link rel="search" title="搜索" href="../../search.html" />
    <link rel="next" title="主动解析基本知识" href="recmod_base.html" />
    <link rel="prev" title="系统键盘常见问题" href="../keyboard/keyboard_problem.html" />
    <link href="../../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="../index.html">串口屏高级应用详解</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../keyboard/index.html">系统键盘</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">主动解析模式应用详解</a><ul>
<li class="toctree-l3"><a class="reference internal" href="recmod_base.html">主动解析基本知识</a></li>
<li class="toctree-l3"><a class="reference internal" href="fixed_hex/index.html">解析定长hex格式指令-自定义协议</a></li>
<li class="toctree-l3"><a class="reference internal" href="unfixed_hex/index.html">解析不定长hex格式指令-自定义协议</a></li>
<li class="toctree-l3"><a class="reference internal" href="recmod_ascii/index.html">解析字符串格式指令</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../hmi_download_protocol.html">HMI下载协议详解/OTA升级</a></li>
<li class="toctree-l2"><a class="reference internal" href="../crc.html">串口指令增加CRC校验</a></li>
<li class="toctree-l2"><a class="reference internal" href="../crc2.html">程序中使用CRC校验数据</a></li>
<li class="toctree-l2"><a class="reference internal" href="../transmit_data.html">运行中串口传输文件到内存或SD卡</a></li>
<li class="toctree-l2"><a class="reference internal" href="../SDcard.html">SD卡读写文件流程</a></li>
<li class="toctree-l2"><a class="reference internal" href="../expandIO.html">拓展IO资料</a></li>
<li class="toctree-l2"><a class="reference internal" href="../get_property.html">获取控件自身的属性</a></li>
<li class="toctree-l2"><a class="reference internal" href="../wepo_repo.html">读写用户存储区</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getv.html">获取串口屏设备唯一序列号</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="../index.html">串口屏高级应用详解</a> &raquo;</li>
      <li>主动解析模式应用详解</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>主动解析模式应用详解<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="recmod_base.html">主动解析基本知识</a><ul>
<li class="toctree-l2"><a class="reference internal" href="recmod_base.html#id2">淘晶驰字符串指令和主动解析</a></li>
<li class="toctree-l2"><a class="reference internal" href="recmod_base.html#id3">主动解析原理</a></li>
<li class="toctree-l2"><a class="reference internal" href="recmod_base.html#id4">主动解析基本步骤</a></li>
<li class="toctree-l2"><a class="reference internal" href="recmod_base.html#recmod">串口数据解析模式系统变量recmod</a></li>
<li class="toctree-l2"><a class="reference internal" href="recmod_base.html#usize">串口缓冲区数据大小系统变量usize</a></li>
<li class="toctree-l2"><a class="reference internal" href="recmod_base.html#u-index">串口缓冲区数据组u[index]</a></li>
<li class="toctree-l2"><a class="reference internal" href="recmod_base.html#ucopy">串口缓冲区数据拷贝指令ucopy</a></li>
<li class="toctree-l2"><a class="reference internal" href="recmod_base.html#exit-recmod">如何退出主动解析</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="fixed_hex/index.html">解析定长hex格式指令-自定义协议</a><ul>
<li class="toctree-l2"><a class="reference internal" href="fixed_hex/fixed1.html">例1：帧头为0x55,帧尾为3个0xff,每次传输灯的序号和对应的状态</a></li>
<li class="toctree-l2"><a class="reference internal" href="fixed_hex/fixed2.html">例2：帧头为0x55 ，帧尾为3个0xff，一次性传输4个灯的状态</a></li>
<li class="toctree-l2"><a class="reference internal" href="fixed_hex/fixed3.html">例3：帧头为0x33 ，帧尾为0d 0a，每次传输灯的序号和对应的状态</a></li>
<li class="toctree-l2"><a class="reference internal" href="fixed_hex/fixed4.html">例4：没有帧头 ，帧尾为0d 0a，每次传输灯的序号和对应的状态</a></li>
<li class="toctree-l2"><a class="reference internal" href="fixed_hex/fixed5.html">例5：帧头为0x33 ，没有帧尾，每次传输灯的序号和对应的状态</a></li>
<li class="toctree-l2"><a class="reference internal" href="fixed_hex/fixed6.html">例6：没有帧头，帧尾为3个0xff，每次传输4个灯的状态</a></li>
<li class="toctree-l2"><a class="reference internal" href="fixed_hex/fixed7.html">例7：帧头为0x55，没有帧尾，每次传输4个灯的状态</a></li>
<li class="toctree-l2"><a class="reference internal" href="fixed_hex/fixed8.html">例8：没有帧头也没有帧尾，每次传输4个灯的状态</a></li>
<li class="toctree-l2"><a class="reference internal" href="fixed_hex/fixed9.html">例9：一个字节有8位，其实可以用1个字节传输8个灯的开关状态</a></li>
<li class="toctree-l2"><a class="reference internal" href="fixed_hex/fixed10.html">例10：帧头为0x33 ，无帧尾，使用modbus_crc16校验，一次性传输4个灯的状态</a></li>
<li class="toctree-l2"><a class="reference internal" href="fixed_hex/fixed11.html">例11：有两种帧头为0x47和0x4E ，帧尾为0x0d，0x0a，每帧长度22字节</a></li>
<li class="toctree-l2"><a class="reference internal" href="fixed_hex/fixed101.html">例101：帧头为55 aa，定长hex-点灯1</a></li>
<li class="toctree-l2"><a class="reference internal" href="fixed_hex/fixed102.html">例102：帧头为55 aa，定长hex-点灯2</a></li>
<li class="toctree-l2"><a class="reference internal" href="fixed_hex/fixed103.html">例103：定长hex-主动解析样例工程</a></li>
<li class="toctree-l2"><a class="reference internal" href="fixed_hex/fixed104.html">例104：电化学氧气模组SC-03-O2-主动解析样例工程</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="unfixed_hex/index.html">解析不定长hex格式指令-自定义协议</a><ul>
<li class="toctree-l2"><a class="reference internal" href="unfixed_hex/unfixed1.html">不定长-帧头为0x55 ，帧尾为3个0xff</a></li>
<li class="toctree-l2"><a class="reference internal" href="unfixed_hex/unfixed2.html">帧头为0xFF ，帧尾为0d 0a</a></li>
<li class="toctree-l2"><a class="reference internal" href="unfixed_hex/unfixed3.html">传输不定长的字符串</a></li>
<li class="toctree-l2"><a class="reference internal" href="unfixed_hex/unfixed4.html">解析不定长hex协议-海陵科LD-2410生命存在感应模组</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="recmod_ascii/index.html">解析字符串格式指令</a><ul>
<li class="toctree-l2"><a class="reference internal" href="recmod_ascii/recmod_ascii_attention.html">解析AT指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="recmod_ascii/recmod_ascii_json.html">接收json数据字符串</a></li>
<li class="toctree-l2"><a class="reference internal" href="recmod_ascii/recmod_ascii_2.html">解析不定长字符串(以回车换行结尾)</a></li>
</ul>
</li>
</ul>
</div>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="../keyboard/keyboard_problem.html" class="btn btn-neutral float-left" title="系统键盘常见问题" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="recmod_base.html" class="btn btn-neutral float-right" title="主动解析基本知识" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>