2025-08-12T13:26:26.401Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T13:26:26.403Z [INFO]: 地址: http://localhost:3001
2025-08-12T13:26:26.403Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T13:26:26.403Z [INFO]: 健康检查: http://localhost:3001/health
2025-08-12T13:26:26.403Z [INFO]: 模型列表: http://localhost:3001/v1/models
2025-08-12T13:26:49.186Z [INFO]: GET /health - 127.0.0.1
2025-08-12T13:27:26.328Z [INFO]: GET /v1/models - 127.0.0.1
2025-08-12T13:28:20.587Z [INFO]: 收到 SIGINT 信号，正在关闭服务器...
2025-08-12T13:28:32.455Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T13:28:32.457Z [INFO]: 地址: http://localhost:3001
2025-08-12T13:28:32.457Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T13:28:32.458Z [INFO]: 健康检查: http://localhost:3001/health
2025-08-12T13:28:32.458Z [INFO]: 模型列表: http://localhost:3001/v1/models
2025-08-12T13:28:51.555Z [INFO]: GET /v1/models - 127.0.0.1
2025-08-12T13:29:28.401Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T13:29:28.401Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T13:29:28.429Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T13:29:28.429Z [INFO]: Kiro 请求 - 模型: claude-sonnet-4, 消息数: 1
2025-08-12T13:29:30.555Z [WARN]: 真实 Kiro API 调用失败，使用模拟响应: getaddrinfo ENOTFOUND api.kiro.ai
2025-08-12T13:40:01.459Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T13:40:01.461Z [INFO]: 地址: http://localhost:3001
2025-08-12T13:40:01.461Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T13:40:01.461Z [INFO]: 健康检查: http://localhost:3001/health
2025-08-12T13:40:01.461Z [INFO]: 模型列表: http://localhost:3001/v1/models
2025-08-12T13:40:19.139Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T13:40:19.139Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T13:40:19.168Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T13:40:19.168Z [WARN]: Kiro 授权 token 已过期
2025-08-12T13:45:06.823Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T13:45:06.825Z [INFO]: 地址: http://localhost:3001
2025-08-12T13:45:06.825Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T13:45:06.826Z [INFO]: 健康检查: http://localhost:3001/health
2025-08-12T13:45:06.826Z [INFO]: 模型列表: http://localhost:3001/v1/models
2025-08-12T13:45:24.807Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T13:45:24.807Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T13:45:24.836Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T13:45:24.836Z [INFO]: Kiro 请求 - 模型: claude-sonnet-4, 消息数: 1
2025-08-12T13:45:27.586Z [WARN]: 真实 Kiro API 调用失败，使用模拟响应: getaddrinfo ENOTFOUND api.kiro.ai
2025-08-12T13:48:31.078Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T13:48:31.079Z [INFO]: 地址: http://localhost:3001
2025-08-12T13:48:31.079Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T13:48:31.079Z [INFO]: 健康检查: http://localhost:3001/health
2025-08-12T13:48:31.079Z [INFO]: 模型列表: http://localhost:3001/v1/models
2025-08-12T13:48:51.627Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T13:48:51.628Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T13:48:51.653Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T13:48:51.653Z [INFO]: Kiro 请求 - 模型: claude-sonnet-4, 消息数: 1
2025-08-12T13:49:41.470Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T13:49:41.471Z [INFO]: 地址: http://localhost:3001
2025-08-12T13:49:41.471Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T13:49:41.472Z [INFO]: 健康检查: http://localhost:3001/health
2025-08-12T13:49:41.472Z [INFO]: 模型列表: http://localhost:3001/v1/models
2025-08-12T13:49:58.384Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T13:49:58.385Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T13:49:58.413Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T13:49:58.413Z [WARN]: Kiro 授权 token 已过期
2025-08-12T13:56:19.531Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T13:56:19.532Z [INFO]: 地址: http://127.0.0.1:3002
2025-08-12T13:56:19.532Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T13:56:19.533Z [INFO]: 健康检查: http://127.0.0.1:3002/health
2025-08-12T13:56:19.533Z [INFO]: 模型列表: http://127.0.0.1:3002/v1/models
2025-08-12T13:57:33.539Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T13:57:33.541Z [INFO]: 地址: http://127.0.0.1:3000
2025-08-12T13:57:33.541Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T13:57:33.541Z [INFO]: 健康检查: http://127.0.0.1:3000/health
2025-08-12T13:57:33.541Z [INFO]: 模型列表: http://127.0.0.1:3000/v1/models
2025-08-12T13:57:56.527Z [INFO]: GET /health - 127.0.0.1
2025-08-12T13:58:08.573Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T13:58:08.573Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T13:58:08.600Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T13:58:08.600Z [WARN]: Kiro 授权 token 已过期
2025-08-12T14:03:01.359Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T14:03:01.361Z [INFO]: 地址: http://127.0.0.1:3000
2025-08-12T14:03:01.361Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T14:03:01.362Z [INFO]: 健康检查: http://127.0.0.1:3000/health
2025-08-12T14:03:01.362Z [INFO]: 模型列表: http://127.0.0.1:3000/v1/models
2025-08-12T14:03:20.140Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T14:03:20.141Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T14:03:20.177Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:03:20.177Z [WARN]: Kiro 授权 token 已过期
2025-08-12T14:05:57.785Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T14:05:57.787Z [INFO]: 地址: http://127.0.0.1:3000
2025-08-12T14:05:57.787Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T14:05:57.787Z [INFO]: 健康检查: http://127.0.0.1:3000/health
2025-08-12T14:05:57.787Z [INFO]: 模型列表: http://127.0.0.1:3000/v1/models
2025-08-12T14:06:15.871Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T14:06:15.871Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T14:06:15.899Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:06:15.900Z [WARN]: Kiro 授权 token 已过期
2025-08-12T14:07:36.694Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T14:07:36.696Z [INFO]: 地址: http://127.0.0.1:3000
2025-08-12T14:07:36.696Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T14:07:36.696Z [INFO]: 健康检查: http://127.0.0.1:3000/health
2025-08-12T14:07:36.697Z [INFO]: 模型列表: http://127.0.0.1:3000/v1/models
2025-08-12T14:07:54.117Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T14:07:54.118Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T14:07:54.144Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:07:54.145Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:07:54 GMT+0800 (GMT+08:00)
