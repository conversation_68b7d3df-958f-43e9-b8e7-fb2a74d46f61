2025-08-12T13:26:26.401Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T13:26:26.403Z [INFO]: 地址: http://localhost:3001
2025-08-12T13:26:26.403Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T13:26:26.403Z [INFO]: 健康检查: http://localhost:3001/health
2025-08-12T13:26:26.403Z [INFO]: 模型列表: http://localhost:3001/v1/models
2025-08-12T13:26:49.186Z [INFO]: GET /health - 127.0.0.1
2025-08-12T13:27:26.328Z [INFO]: GET /v1/models - 127.0.0.1
2025-08-12T13:28:20.587Z [INFO]: 收到 SIGINT 信号，正在关闭服务器...
2025-08-12T13:28:32.455Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T13:28:32.457Z [INFO]: 地址: http://localhost:3001
2025-08-12T13:28:32.457Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T13:28:32.458Z [INFO]: 健康检查: http://localhost:3001/health
2025-08-12T13:28:32.458Z [INFO]: 模型列表: http://localhost:3001/v1/models
2025-08-12T13:28:51.555Z [INFO]: GET /v1/models - 127.0.0.1
2025-08-12T13:29:28.401Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T13:29:28.401Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T13:29:28.429Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T13:29:28.429Z [INFO]: Kiro 请求 - 模型: claude-sonnet-4, 消息数: 1
2025-08-12T13:29:30.555Z [WARN]: 真实 Kiro API 调用失败，使用模拟响应: getaddrinfo ENOTFOUND api.kiro.ai
2025-08-12T13:40:01.459Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T13:40:01.461Z [INFO]: 地址: http://localhost:3001
2025-08-12T13:40:01.461Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T13:40:01.461Z [INFO]: 健康检查: http://localhost:3001/health
2025-08-12T13:40:01.461Z [INFO]: 模型列表: http://localhost:3001/v1/models
2025-08-12T13:40:19.139Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T13:40:19.139Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T13:40:19.168Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T13:40:19.168Z [WARN]: Kiro 授权 token 已过期
2025-08-12T13:45:06.823Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T13:45:06.825Z [INFO]: 地址: http://localhost:3001
2025-08-12T13:45:06.825Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T13:45:06.826Z [INFO]: 健康检查: http://localhost:3001/health
2025-08-12T13:45:06.826Z [INFO]: 模型列表: http://localhost:3001/v1/models
2025-08-12T13:45:24.807Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T13:45:24.807Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T13:45:24.836Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T13:45:24.836Z [INFO]: Kiro 请求 - 模型: claude-sonnet-4, 消息数: 1
2025-08-12T13:45:27.586Z [WARN]: 真实 Kiro API 调用失败，使用模拟响应: getaddrinfo ENOTFOUND api.kiro.ai
2025-08-12T13:48:31.078Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T13:48:31.079Z [INFO]: 地址: http://localhost:3001
2025-08-12T13:48:31.079Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T13:48:31.079Z [INFO]: 健康检查: http://localhost:3001/health
2025-08-12T13:48:31.079Z [INFO]: 模型列表: http://localhost:3001/v1/models
2025-08-12T13:48:51.627Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T13:48:51.628Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T13:48:51.653Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T13:48:51.653Z [INFO]: Kiro 请求 - 模型: claude-sonnet-4, 消息数: 1
