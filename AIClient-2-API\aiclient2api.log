2025-08-12T13:26:26.401Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T13:26:26.403Z [INFO]: 地址: http://localhost:3001
2025-08-12T13:26:26.403Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T13:26:26.403Z [INFO]: 健康检查: http://localhost:3001/health
2025-08-12T13:26:26.403Z [INFO]: 模型列表: http://localhost:3001/v1/models
2025-08-12T13:26:49.186Z [INFO]: GET /health - 127.0.0.1
2025-08-12T13:27:26.328Z [INFO]: GET /v1/models - 127.0.0.1
2025-08-12T13:28:20.587Z [INFO]: 收到 SIGINT 信号，正在关闭服务器...
2025-08-12T13:28:32.455Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T13:28:32.457Z [INFO]: 地址: http://localhost:3001
2025-08-12T13:28:32.457Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T13:28:32.458Z [INFO]: 健康检查: http://localhost:3001/health
2025-08-12T13:28:32.458Z [INFO]: 模型列表: http://localhost:3001/v1/models
2025-08-12T13:28:51.555Z [INFO]: GET /v1/models - 127.0.0.1
2025-08-12T13:29:28.401Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T13:29:28.401Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T13:29:28.429Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T13:29:28.429Z [INFO]: Kiro 请求 - 模型: claude-sonnet-4, 消息数: 1
2025-08-12T13:29:30.555Z [WARN]: 真实 Kiro API 调用失败，使用模拟响应: getaddrinfo ENOTFOUND api.kiro.ai
2025-08-12T13:40:01.459Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T13:40:01.461Z [INFO]: 地址: http://localhost:3001
2025-08-12T13:40:01.461Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T13:40:01.461Z [INFO]: 健康检查: http://localhost:3001/health
2025-08-12T13:40:01.461Z [INFO]: 模型列表: http://localhost:3001/v1/models
2025-08-12T13:40:19.139Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T13:40:19.139Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T13:40:19.168Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T13:40:19.168Z [WARN]: Kiro 授权 token 已过期
2025-08-12T13:45:06.823Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T13:45:06.825Z [INFO]: 地址: http://localhost:3001
2025-08-12T13:45:06.825Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T13:45:06.826Z [INFO]: 健康检查: http://localhost:3001/health
2025-08-12T13:45:06.826Z [INFO]: 模型列表: http://localhost:3001/v1/models
2025-08-12T13:45:24.807Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T13:45:24.807Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T13:45:24.836Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T13:45:24.836Z [INFO]: Kiro 请求 - 模型: claude-sonnet-4, 消息数: 1
2025-08-12T13:45:27.586Z [WARN]: 真实 Kiro API 调用失败，使用模拟响应: getaddrinfo ENOTFOUND api.kiro.ai
2025-08-12T13:48:31.078Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T13:48:31.079Z [INFO]: 地址: http://localhost:3001
2025-08-12T13:48:31.079Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T13:48:31.079Z [INFO]: 健康检查: http://localhost:3001/health
2025-08-12T13:48:31.079Z [INFO]: 模型列表: http://localhost:3001/v1/models
2025-08-12T13:48:51.627Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T13:48:51.628Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T13:48:51.653Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T13:48:51.653Z [INFO]: Kiro 请求 - 模型: claude-sonnet-4, 消息数: 1
2025-08-12T13:49:41.470Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T13:49:41.471Z [INFO]: 地址: http://localhost:3001
2025-08-12T13:49:41.471Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T13:49:41.472Z [INFO]: 健康检查: http://localhost:3001/health
2025-08-12T13:49:41.472Z [INFO]: 模型列表: http://localhost:3001/v1/models
2025-08-12T13:49:58.384Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T13:49:58.385Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T13:49:58.413Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T13:49:58.413Z [WARN]: Kiro 授权 token 已过期
2025-08-12T13:56:19.531Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T13:56:19.532Z [INFO]: 地址: http://127.0.0.1:3002
2025-08-12T13:56:19.532Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T13:56:19.533Z [INFO]: 健康检查: http://127.0.0.1:3002/health
2025-08-12T13:56:19.533Z [INFO]: 模型列表: http://127.0.0.1:3002/v1/models
2025-08-12T13:57:33.539Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T13:57:33.541Z [INFO]: 地址: http://127.0.0.1:3000
2025-08-12T13:57:33.541Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T13:57:33.541Z [INFO]: 健康检查: http://127.0.0.1:3000/health
2025-08-12T13:57:33.541Z [INFO]: 模型列表: http://127.0.0.1:3000/v1/models
2025-08-12T13:57:56.527Z [INFO]: GET /health - 127.0.0.1
2025-08-12T13:58:08.573Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T13:58:08.573Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T13:58:08.600Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T13:58:08.600Z [WARN]: Kiro 授权 token 已过期
2025-08-12T14:03:01.359Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T14:03:01.361Z [INFO]: 地址: http://127.0.0.1:3000
2025-08-12T14:03:01.361Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T14:03:01.362Z [INFO]: 健康检查: http://127.0.0.1:3000/health
2025-08-12T14:03:01.362Z [INFO]: 模型列表: http://127.0.0.1:3000/v1/models
2025-08-12T14:03:20.140Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T14:03:20.141Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T14:03:20.177Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:03:20.177Z [WARN]: Kiro 授权 token 已过期
2025-08-12T14:05:57.785Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T14:05:57.787Z [INFO]: 地址: http://127.0.0.1:3000
2025-08-12T14:05:57.787Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T14:05:57.787Z [INFO]: 健康检查: http://127.0.0.1:3000/health
2025-08-12T14:05:57.787Z [INFO]: 模型列表: http://127.0.0.1:3000/v1/models
2025-08-12T14:06:15.871Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T14:06:15.871Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T14:06:15.899Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:06:15.900Z [WARN]: Kiro 授权 token 已过期
2025-08-12T14:07:36.694Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T14:07:36.696Z [INFO]: 地址: http://127.0.0.1:3000
2025-08-12T14:07:36.696Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T14:07:36.696Z [INFO]: 健康检查: http://127.0.0.1:3000/health
2025-08-12T14:07:36.697Z [INFO]: 模型列表: http://127.0.0.1:3000/v1/models
2025-08-12T14:07:54.117Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T14:07:54.118Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T14:07:54.144Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:07:54.145Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:07:54 GMT+0800 (GMT+08:00)
2025-08-12T14:13:21.727Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T14:13:21.728Z [INFO]: 地址: http://127.0.0.1:3000
2025-08-12T14:13:21.728Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T14:13:21.729Z [INFO]: 健康检查: http://127.0.0.1:3000/health
2025-08-12T14:13:21.729Z [INFO]: 模型列表: http://127.0.0.1:3000/v1/models
2025-08-12T14:13:43.862Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T14:13:43.863Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T14:13:43.896Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:13:43.896Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:13:43 GMT+0800 (GMT+08:00)
2025-08-12T14:13:43.897Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:13:43.897Z [INFO]: Kiro 请求 - 模型: claude-sonnet-4, 消息数: 1
2025-08-12T14:13:43.897Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:13:43 GMT+0800 (GMT+08:00)
2025-08-12T14:13:43.897Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:17:58.548Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T14:17:58.550Z [INFO]: 地址: http://127.0.0.1:3000
2025-08-12T14:17:58.550Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T14:17:58.550Z [INFO]: 健康检查: http://127.0.0.1:3000/health
2025-08-12T14:17:58.550Z [INFO]: 模型列表: http://127.0.0.1:3000/v1/models
2025-08-12T14:18:16.822Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T14:18:16.823Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T14:18:16.851Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:18:16.852Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:18:16 GMT+0800 (GMT+08:00)
2025-08-12T14:18:16.852Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:18:16.852Z [INFO]: Kiro 请求 - 模型: claude-sonnet-4, 消息数: 1
2025-08-12T14:18:16.852Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:18:16 GMT+0800 (GMT+08:00)
2025-08-12T14:18:16.852Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:18:19.740Z [WARN]: 真实 Kiro API 调用失败，使用模拟响应: getaddrinfo ENOTFOUND qbusiness.us-east-1.amazonaws.com
2025-08-12T14:18:22.707Z [ERROR]: 调用 Kiro API 失败: Cannot read properties of undefined (reading 'length')
2025-08-12T14:18:22.707Z [ERROR]: Kiro 请求处理失败: Cannot read properties of undefined (reading 'length')
2025-08-12T14:19:19.893Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T14:19:19.895Z [INFO]: 地址: http://127.0.0.1:3000
2025-08-12T14:19:19.895Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T14:19:19.895Z [INFO]: 健康检查: http://127.0.0.1:3000/health
2025-08-12T14:19:19.895Z [INFO]: 模型列表: http://127.0.0.1:3000/v1/models
2025-08-12T14:19:43.908Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T14:19:43.908Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T14:19:43.935Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:19:43.936Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:19:43 GMT+0800 (GMT+08:00)
2025-08-12T14:19:43.936Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:19:43.936Z [INFO]: Kiro 请求 - 模型: claude-sonnet-4, 消息数: 1
2025-08-12T14:19:43.937Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:19:43 GMT+0800 (GMT+08:00)
2025-08-12T14:19:43.937Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:19:47.285Z [WARN]: 真实 Kiro API 调用失败，使用模拟响应: Request failed with status code 403
2025-08-12T14:19:49.611Z [ERROR]: 调用 Kiro API 失败: Cannot read properties of undefined (reading 'length')
2025-08-12T14:19:49.612Z [ERROR]: Kiro 请求处理失败: Cannot read properties of undefined (reading 'length')
2025-08-12T14:20:56.444Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T14:20:56.446Z [INFO]: 地址: http://127.0.0.1:3000
2025-08-12T14:20:56.446Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T14:20:56.447Z [INFO]: 健康检查: http://127.0.0.1:3000/health
2025-08-12T14:20:56.447Z [INFO]: 模型列表: http://127.0.0.1:3000/v1/models
2025-08-12T14:21:15.584Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T14:21:15.585Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T14:21:15.612Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:21:15.613Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:21:15 GMT+0800 (GMT+08:00)
2025-08-12T14:21:15.613Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:21:15.613Z [INFO]: Kiro 请求 - 模型: claude-sonnet-4, 消息数: 1
2025-08-12T14:21:15.613Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:21:15 GMT+0800 (GMT+08:00)
2025-08-12T14:21:15.613Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:21:18.221Z [WARN]: 真实 Kiro API 调用失败，使用模拟响应: Request failed with status code 403
2025-08-12T14:23:54.694Z [INFO]: GET /health - 127.0.0.1
2025-08-12T14:23:54.703Z [INFO]: GET /v1/models - 127.0.0.1
2025-08-12T14:23:54.708Z [INFO]: POST /v1/chat/completions - 127.0.0.1
2025-08-12T14:23:54.708Z [INFO]: 收到聊天请求，模型: claude-sonnet-4
2025-08-12T14:23:54.709Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:23:54.709Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:23:54 GMT+0800 (GMT+08:00)
2025-08-12T14:23:54.709Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:23:54.709Z [INFO]: Kiro 请求 - 模型: claude-sonnet-4, 消息数: 1
2025-08-12T14:23:54.709Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:23:54 GMT+0800 (GMT+08:00)
2025-08-12T14:23:54.709Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:23:57.242Z [WARN]: 真实 Kiro API 调用失败，使用模拟响应: Request failed with status code 403
2025-08-12T14:26:03.940Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:26:03.984Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:26:04.015Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:26:04.046Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:26:04.088Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:26:04.117Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:26:04.150Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:26:04.186Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:26:04.407Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:26:06.271Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:26:06.736Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:26:06.742Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:26:46.053Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:26:46.320Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:26:46.325Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:30:12.660Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T14:30:12.661Z [INFO]: 地址: http://127.0.0.1:3000
2025-08-12T14:30:12.661Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T14:30:12.661Z [INFO]: 健康检查: http://127.0.0.1:3000/health
2025-08-12T14:30:12.662Z [INFO]: 模型列表: http://127.0.0.1:3000/v1/models
2025-08-12T14:31:25.074Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:31:25.074Z [INFO]: 收到 Claude Messages 请求，模型: claude-sonnet-4
2025-08-12T14:31:25.102Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:31:25.103Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:31:25 GMT+0800 (GMT+08:00)
2025-08-12T14:31:25.103Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:31:25.104Z [INFO]: Kiro Messages 请求 - 模型: claude-sonnet-4, 消息数: 1
2025-08-12T14:31:25.104Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:31:25 GMT+0800 (GMT+08:00)
2025-08-12T14:31:25.104Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:31:27.765Z [WARN]: 真实 Kiro API 调用失败，使用模拟响应: Request failed with status code 403
2025-08-12T14:32:25.894Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:32:25.895Z [INFO]: 收到 Claude Messages 请求，模型: claude-3-5-haiku-20241022
2025-08-12T14:32:25.895Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:32:25.895Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:32:25 GMT+0800 (GMT+08:00)
2025-08-12T14:32:25.895Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:32:25.895Z [INFO]: Kiro Messages 请求 - 模型: claude-3-5-haiku-20241022, 消息数: 1
2025-08-12T14:32:25.896Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:32:25 GMT+0800 (GMT+08:00)
2025-08-12T14:32:25.896Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:32:26.297Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:32:26.297Z [INFO]: 收到 Claude Messages 请求，模型: claude-sonnet-4-20250514
2025-08-12T14:32:26.298Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:32:26.298Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:32:26 GMT+0800 (GMT+08:00)
2025-08-12T14:32:26.298Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:32:26.298Z [INFO]: Kiro Messages 请求 - 模型: claude-sonnet-4-20250514, 消息数: 1
2025-08-12T14:32:26.299Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:32:26 GMT+0800 (GMT+08:00)
2025-08-12T14:32:26.299Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:32:28.447Z [WARN]: 真实 Kiro API 调用失败，使用模拟响应: Request failed with status code 403
2025-08-12T14:32:28.833Z [WARN]: 真实 Kiro API 调用失败，使用模拟响应: Request failed with status code 403
2025-08-12T14:32:31.666Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:32:31.666Z [INFO]: 收到 Claude Messages 请求，模型: claude-sonnet-4-20250514
2025-08-12T14:32:31.667Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:32:31.667Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:32:31 GMT+0800 (GMT+08:00)
2025-08-12T14:32:31.667Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:32:31.668Z [INFO]: Kiro Messages 请求 - 模型: claude-sonnet-4-20250514, 消息数: 1
2025-08-12T14:32:31.668Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:32:31 GMT+0800 (GMT+08:00)
2025-08-12T14:32:31.668Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:32:31.980Z [WARN]: 真实 Kiro API 调用失败，使用模拟响应: Request failed with status code 403
2025-08-12T14:33:37.281Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:33:37.281Z [INFO]: 收到 Claude Messages 请求，模型: claude-3-5-haiku-20241022
2025-08-12T14:33:37.282Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:33:37.282Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:33:37 GMT+0800 (GMT+08:00)
2025-08-12T14:33:37.282Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:33:37.282Z [INFO]: Kiro Messages 请求 - 模型: claude-3-5-haiku-20241022, 消息数: 1
2025-08-12T14:33:37.282Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:33:37 GMT+0800 (GMT+08:00)
2025-08-12T14:33:37.282Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:33:37.592Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:33:37.592Z [INFO]: 收到 Claude Messages 请求，模型: claude-sonnet-4-20250514
2025-08-12T14:33:37.592Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:33:37.593Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:33:37 GMT+0800 (GMT+08:00)
2025-08-12T14:33:37.593Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:33:37.593Z [INFO]: Kiro Messages 请求 - 模型: claude-sonnet-4-20250514, 消息数: 3
2025-08-12T14:33:37.593Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:33:37 GMT+0800 (GMT+08:00)
2025-08-12T14:33:37.593Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:33:40.489Z [WARN]: 真实 Kiro API 调用失败，使用模拟响应: Request failed with status code 403
2025-08-12T14:33:40.812Z [WARN]: 真实 Kiro API 调用失败，使用模拟响应: Request failed with status code 403
2025-08-12T14:33:43.132Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:33:43.132Z [INFO]: 收到 Claude Messages 请求，模型: claude-sonnet-4-20250514
2025-08-12T14:33:43.132Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:33:43.132Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:33:43 GMT+0800 (GMT+08:00)
2025-08-12T14:33:43.132Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:33:43.133Z [INFO]: Kiro Messages 请求 - 模型: claude-sonnet-4-20250514, 消息数: 3
2025-08-12T14:33:43.133Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:33:43 GMT+0800 (GMT+08:00)
2025-08-12T14:33:43.133Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:33:43.446Z [WARN]: 真实 Kiro API 调用失败，使用模拟响应: Request failed with status code 403
2025-08-12T14:37:56.107Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T14:37:56.109Z [INFO]: 地址: http://127.0.0.1:3000
2025-08-12T14:37:56.109Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T14:37:56.109Z [INFO]: 健康检查: http://127.0.0.1:3000/health
2025-08-12T14:37:56.110Z [INFO]: 模型列表: http://127.0.0.1:3000/v1/models
2025-08-12T14:38:12.594Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:38:12.594Z [INFO]: 收到 Claude Messages 请求，模型: claude-sonnet-4
2025-08-12T14:38:12.621Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:38:12.621Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:38:12 GMT+0800 (GMT+08:00)
2025-08-12T14:38:12.622Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:38:12.622Z [INFO]: Kiro Messages 请求 - 模型: claude-sonnet-4, 消息数: 1
2025-08-12T14:38:12.622Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:38:12 GMT+0800 (GMT+08:00)
2025-08-12T14:38:12.622Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:38:12.622Z [INFO]: 调用 Kiro API: https://codewhisperer.us-east-1.amazonaws.com/v1/messages
2025-08-12T14:38:14.091Z [INFO]: ✅ 成功调用真实 Kiro API
2025-08-12T14:39:08.743Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T14:39:08.745Z [INFO]: 地址: http://127.0.0.1:3000
2025-08-12T14:39:08.745Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T14:39:08.745Z [INFO]: 健康检查: http://127.0.0.1:3000/health
2025-08-12T14:39:08.745Z [INFO]: 模型列表: http://127.0.0.1:3000/v1/models
2025-08-12T14:39:25.829Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:39:25.829Z [INFO]: 收到 Claude Messages 请求，模型: claude-sonnet-4
2025-08-12T14:39:25.857Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:39:25.857Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:39:25 GMT+0800 (GMT+08:00)
2025-08-12T14:39:25.857Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:39:25.858Z [INFO]: Kiro Messages 请求 - 模型: claude-sonnet-4, 消息数: 1
2025-08-12T14:39:25.858Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:39:25 GMT+0800 (GMT+08:00)
2025-08-12T14:39:25.858Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:39:25.858Z [INFO]: 调用 Kiro API: https://codewhisperer.us-east-1.amazonaws.com/v1/messages
2025-08-12T14:39:27.324Z [INFO]: Kiro API 响应状态: 200
2025-08-12T14:39:27.324Z [INFO]: ✅ 成功调用真实 Kiro API
2025-08-12T14:40:32.152Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T14:40:32.154Z [INFO]: 地址: http://127.0.0.1:3000
2025-08-12T14:40:32.154Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T14:40:32.154Z [INFO]: 健康检查: http://127.0.0.1:3000/health
2025-08-12T14:40:32.154Z [INFO]: 模型列表: http://127.0.0.1:3000/v1/models
2025-08-12T14:40:50.454Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:40:50.454Z [INFO]: 收到 Claude Messages 请求，模型: claude-sonnet-4
2025-08-12T14:40:50.482Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:40:50.482Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:40:50 GMT+0800 (GMT+08:00)
2025-08-12T14:40:50.483Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:40:50.483Z [INFO]: Kiro Messages 请求 - 模型: claude-sonnet-4, 消息数: 1
2025-08-12T14:40:50.483Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:40:50 GMT+0800 (GMT+08:00)
2025-08-12T14:40:50.483Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:40:50.484Z [INFO]: 调用 Kiro API: https://codewhisperer.us-east-1.amazonaws.com/v1/messages
2025-08-12T14:40:51.935Z [INFO]: Kiro API 响应状态: 200
2025-08-12T14:40:51.935Z [INFO]: Kiro API 响应数据: {
  "Output": {
    "__type": "com.amazon.coral.service#UnknownOperationException",
    "message": "The requested operation is not recognized by the service."
  },
  "Version": "1.0"
}
2025-08-12T14:40:51.935Z [INFO]: 转换后的响应: {
  "id": "kiro-1755009651935",
  "object": "chat.completion",
  "created": 1755009651,
  "model": "claude-sonnet-4",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "抱歉，无法获取响应。"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 0,
    "completion_tokens": 0,
    "total_tokens": 0
  }
}
2025-08-12T14:40:51.935Z [INFO]: ✅ 成功调用真实 Kiro API
2025-08-12T14:41:51.486Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T14:41:51.487Z [INFO]: 地址: http://127.0.0.1:3000
2025-08-12T14:41:51.488Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T14:41:51.488Z [INFO]: 健康检查: http://127.0.0.1:3000/health
2025-08-12T14:41:51.488Z [INFO]: 模型列表: http://127.0.0.1:3000/v1/models
2025-08-12T14:42:15.931Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:42:15.931Z [INFO]: 收到 Claude Messages 请求，模型: claude-sonnet-4
2025-08-12T14:42:15.960Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:42:15.960Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:42:15 GMT+0800 (GMT+08:00)
2025-08-12T14:42:15.961Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:42:15.961Z [INFO]: Kiro Messages 请求 - 模型: claude-sonnet-4, 消息数: 1
2025-08-12T14:42:15.961Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:42:15 GMT+0800 (GMT+08:00)
2025-08-12T14:42:15.961Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:42:15.961Z [INFO]: 调用 Kiro API: https://codewhisperer.us-east-1.amazonaws.com/v1/chat/completions
2025-08-12T14:42:17.435Z [INFO]: Kiro API 响应状态: 200
2025-08-12T14:42:17.436Z [INFO]: Kiro API 响应数据: {
  "Output": {
    "__type": "com.amazon.coral.service#UnknownOperationException",
    "message": "The requested operation is not recognized by the service."
  },
  "Version": "1.0"
}
2025-08-12T14:42:17.436Z [INFO]: 转换后的响应: {
  "id": "kiro-1755009737436",
  "object": "chat.completion",
  "created": 1755009737,
  "model": "claude-sonnet-4",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "抱歉，无法获取响应。"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 0,
    "completion_tokens": 0,
    "total_tokens": 0
  }
}
2025-08-12T14:42:17.436Z [INFO]: ✅ 成功调用真实 Kiro API
2025-08-12T14:43:47.357Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T14:43:47.359Z [INFO]: 地址: http://127.0.0.1:3000
2025-08-12T14:43:47.359Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T14:43:47.359Z [INFO]: 健康检查: http://127.0.0.1:3000/health
2025-08-12T14:43:47.359Z [INFO]: 模型列表: http://127.0.0.1:3000/v1/models
2025-08-12T14:44:11.370Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:44:11.370Z [INFO]: 收到 Claude Messages 请求，模型: claude-sonnet-4
2025-08-12T14:44:11.396Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:44:11.396Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:44:11 GMT+0800 (GMT+08:00)
2025-08-12T14:44:11.396Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:44:11.397Z [INFO]: Kiro Messages 请求 - 模型: claude-sonnet-4, 消息数: 1
2025-08-12T14:44:11.397Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:44:11 GMT+0800 (GMT+08:00)
2025-08-12T14:44:11.397Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:44:11.397Z [INFO]: 调用 Kiro API: https://q.us-east-1.amazonaws.com/v1/conversations
2025-08-12T14:44:11.397Z [INFO]: 请求头: {
  "Content-Type": "application/x-amz-json-1.1",
  "Authorization": "Bearer aoaAAAAAGibRm4HYuapowDHRZlLZuR8UI7UAgHY34cA7useV-kdiKPsU_ziaZjHbo3Vs7MST6uQNcU4yxjM0XlhEcBkc0:MGUCMB5Q16rOCL+aSCoEbHr3ijaxWG4akSYGlztHbqjEU+k7RTvvMK99NYSEIEJahCczXQIxAMFdU2BG/M7QaX60Z93fRK+0K3+33BCPAWemjbv8XsebP8w3ejfZvrMAgTGhdV3+TQ",
  "User-Agent": "Kiro/1.0.0",
  "x-amzn-codewhisperer-profile-arn": "arn:aws:codewhisperer:us-east-1:699475941385:profile/EHGA3GRVQMUK",
  "X-Amz-Target": "AWSQDeveloperService.SendMessage"
}
2025-08-12T14:44:11.397Z [INFO]: 请求体: {
  "userMessage": "Hello, this is a test message. Please reply briefly to confirm you received it.",
  "conversationId": "conv-1755009851397",
  "parentMessageId": "msg-1755009851397",
  "clientToken": "client-1755009851397"
}
2025-08-12T14:44:13.114Z [ERROR]: ❌ 真实 Kiro API 调用失败: Request failed with status code 404
2025-08-12T14:44:13.114Z [ERROR]: 调用 Kiro API 失败: Kiro API 调用失败: Request failed with status code 404
2025-08-12T14:44:13.114Z [ERROR]: Kiro Messages 请求处理失败: Kiro API 调用失败: Request failed with status code 404
2025-08-12T14:45:45.296Z [INFO]: AIClient-2-API 服务器已启动
2025-08-12T14:45:45.297Z [INFO]: 地址: http://127.0.0.1:3000
2025-08-12T14:45:45.298Z [INFO]: 模型提供商: claude-kiro-oauth
2025-08-12T14:45:45.298Z [INFO]: 健康检查: http://127.0.0.1:3000/health
2025-08-12T14:45:45.298Z [INFO]: 模型列表: http://127.0.0.1:3000/v1/models
2025-08-12T14:46:08.709Z [INFO]: POST /v1/messages - 127.0.0.1
2025-08-12T14:46:08.709Z [INFO]: 收到 Claude Messages 请求，模型: claude-sonnet-4
2025-08-12T14:46:08.736Z [INFO]: Kiro 授权 token 加载成功
2025-08-12T14:46:08.737Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:46:08 GMT+0800 (GMT+08:00)
2025-08-12T14:46:08.737Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:46:08.737Z [INFO]: Kiro Messages 请求 - 模型: claude-sonnet-4, 消息数: 1
2025-08-12T14:46:08.737Z [WARN]: Kiro 授权 token 已过期 - 过期时间: Tue Aug 12 2025 21:49:33 GMT+0800 (GMT+08:00), 当前时间: Tue Aug 12 2025 22:46:08 GMT+0800 (GMT+08:00)
2025-08-12T14:46:08.737Z [INFO]: 暂时跳过过期检查，尝试使用现有 token
2025-08-12T14:46:08.738Z [INFO]: 调用 Kiro API: https://bedrock-runtime.us-east-1.amazonaws.com/model/anthropic.claude-3-5-sonnet-20241022-v2:0/invoke
2025-08-12T14:46:08.738Z [INFO]: 请求头: {
  "Content-Type": "application/json",
  "Authorization": "Bearer aoaAAAAAGibRm4HYuapowDHRZlLZuR8UI7UAgHY34cA7useV-kdiKPsU_ziaZjHbo3Vs7MST6uQNcU4yxjM0XlhEcBkc0:MGUCMB5Q16rOCL+aSCoEbHr3ijaxWG4akSYGlztHbqjEU+k7RTvvMK99NYSEIEJahCczXQIxAMFdU2BG/M7QaX60Z93fRK+0K3+33BCPAWemjbv8XsebP8w3ejfZvrMAgTGhdV3+TQ",
  "User-Agent": "Kiro/1.0.0",
  "x-amzn-bedrock-accept": "application/json",
  "x-amzn-bedrock-content-type": "application/json"
}
2025-08-12T14:46:08.738Z [INFO]: 请求体: {
  "anthropic_version": "bedrock-2023-05-31",
  "max_tokens": 100,
  "messages": [
    {
      "role": "user",
      "content": "Hello, this is a test message. Please reply briefly to confirm you received it."
    }
  ],
  "temperature": 0.7
}
2025-08-12T14:46:11.731Z [ERROR]: ❌ 真实 Kiro API 调用失败: Request failed with status code 403
2025-08-12T14:46:11.731Z [ERROR]: 调用 Kiro API 失败: Kiro API 调用失败: Request failed with status code 403
2025-08-12T14:46:11.731Z [ERROR]: Kiro Messages 请求处理失败: Kiro API 调用失败: Request failed with status code 403
