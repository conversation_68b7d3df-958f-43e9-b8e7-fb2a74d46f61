<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>crcputs-crc校验一个变量/常量 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="crcputh-crc校验一组Hex" href="crcputh.html" />
    <link rel="prev" title="crcrest-复位crc初始值" href="crcrest.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../widgets/index.html">控件详解</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">基本指令集</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="index.html#id2">常用指令集</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id3">高级指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id4">文件操作指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id5">不常用指令</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html#crc">CRC校验指令</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="crcrest.html">crcrest-复位crc初始值</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">crcputs-crc校验一个变量/常量</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#crcputs-1">crcputs-示例1</a></li>
<li class="toctree-l4"><a class="reference internal" href="#crcputs-2">crcputs-示例2</a></li>
<li class="toctree-l4"><a class="reference internal" href="#crcputs-3">crcputs-示例3</a></li>
<li class="toctree-l4"><a class="reference internal" href="#crcputs-4">crcputs-示例4</a></li>
<li class="toctree-l4"><a class="reference internal" href="#crcputs">crcputs指令-相关链接</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id1">crcputs指令-样例工程下载</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="crcputh.html">crcputh-crc校验一组Hex</a></li>
<li class="toctree-l3"><a class="reference internal" href="crcputu.html">crcputu-crc校验一段串口缓冲区数据</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#gui">GUI绘图指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#hmi">HMI颜色代号表</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">指令集</a> &raquo;</li>
      <li>crcputs-crc校验一个变量/常量</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="crcputs-crc">
<h1>crcputs-crc校验一个变量/常量<a class="headerlink" href="#crcputs-crc" title="此标题的永久链接"></a></h1>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">crcputs</span> <span class="n">att</span><span class="p">,</span><span class="n">length</span>

<span class="n">att</span><span class="p">:</span><span class="n">变量名称</span>

<span class="n">length</span><span class="p">:</span><span class="n">需要校验的数据长度</span><span class="p">(</span><span class="mi">0</span><span class="n">为自动长度</span><span class="p">)</span>
</pre></div>
</div>
<section id="crcputs-1">
<h2>crcputs-示例1<a class="headerlink" href="#crcputs-1" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="c1">//复位CRC初始值为0xffff，以便后续检验数据</span>
<span class="linenos">2</span><span class="n">crcrest</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="mh">0xffff</span><span class="w"></span>
<span class="linenos">3</span><span class="c1">//CRC校验字符串变量t0.txt</span>
<span class="linenos">4</span><span class="n">crcputs</span><span class="w"> </span><span class="n">t0</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
<span class="linenos">5</span><span class="c1">//发送校验值</span>
<span class="linenos">6</span><span class="n">prints</span><span class="w"> </span><span class="n">crcval</span><span class="p">,</span><span class="mi">2</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/crcputs_1.jpg" src="../_images/crcputs_1.jpg" />
</section>
<section id="crcputs-2">
<h2>crcputs-示例2<a class="headerlink" href="#crcputs-2" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="c1">//复位CRC初始值为0xffff，以便后续检验数据</span>
<span class="linenos">2</span><span class="n">crcrest</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="mh">0xffff</span><span class="w"></span>
<span class="linenos">3</span><span class="c1">//CRC校验字符串常量&quot;abc&quot;</span>
<span class="linenos">4</span><span class="n">crcputs</span><span class="w"> </span><span class="s">&quot;abc&quot;</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
<span class="linenos">5</span><span class="c1">//发送校验值</span>
<span class="linenos">6</span><span class="n">prints</span><span class="w"> </span><span class="n">crcval</span><span class="p">,</span><span class="mi">2</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/crcputs_2.jpg" src="../_images/crcputs_2.jpg" />
</section>
<section id="crcputs-3">
<h2>crcputs-示例3<a class="headerlink" href="#crcputs-3" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="c1">//复位CRC初始值为0xffff，以便后续检验数据</span>
<span class="linenos">2</span><span class="n">crcrest</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="mh">0xffff</span><span class="w"></span>
<span class="linenos">3</span><span class="c1">//CRC校验n0.val的低2字节</span>
<span class="linenos">4</span><span class="n">crcputs</span><span class="w"> </span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="p">,</span><span class="mi">2</span><span class="w"></span>
<span class="linenos">5</span><span class="c1">//发送校验值</span>
<span class="linenos">6</span><span class="n">prints</span><span class="w"> </span><span class="n">crcval</span><span class="p">,</span><span class="mi">2</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/crcputs_3.jpg" src="../_images/crcputs_3.jpg" />
</section>
<section id="crcputs-4">
<h2>crcputs-示例4<a class="headerlink" href="#crcputs-4" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="c1">//复位CRC初始值为0xffff，以便后续检验数据</span>
<span class="linenos"> 2</span><span class="n">crcrest</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="mh">0xffff</span><span class="w"></span>
<span class="linenos"> 3</span><span class="c1">//CRC校验字符串变量t0.txt</span>
<span class="linenos"> 4</span><span class="n">crcputs</span><span class="w"> </span><span class="n">t0</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
<span class="linenos"> 5</span><span class="c1">//CRC校验字符串常量&quot;abc&quot;</span>
<span class="linenos"> 6</span><span class="n">crcputs</span><span class="w"> </span><span class="s">&quot;abc&quot;</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
<span class="linenos"> 7</span><span class="c1">//CRC校验n0.val的低2字节</span>
<span class="linenos"> 8</span><span class="n">crcputs</span><span class="w"> </span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="p">,</span><span class="mi">2</span><span class="w"></span>
<span class="linenos"> 9</span><span class="c1">//发送校验值</span>
<span class="linenos">10</span><span class="n">prints</span><span class="w"> </span><span class="n">crcval</span><span class="p">,</span><span class="mi">2</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/crcputs_4.jpg" src="../_images/crcputs_4.jpg" />
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>开始校验前必须使用crcrest初始化crc校验功能。</p>
<p>使用crcputs或crcputh或crcputu校验指定数据,检验完毕读取系统变量crcval获得校验结果。</p>
<p>完整的CRC校验实例代码请参考: <a class="reference internal" href="../advanced/crc2.html#crc"><span class="std std-ref">程序中使用CRC校验数据</span></a></p>
</div>
</section>
<section id="crcputs">
<h2>crcputs指令-相关链接<a class="headerlink" href="#crcputs" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="crcputh.html#crcputh-crchex"><span class="std std-ref">crcputh-crc校验一组Hex</span></a></p>
<p><a class="reference internal" href="crcputu.html#crcputu-crc"><span class="std std-ref">crcputu-crc校验一段串口缓冲区数据</span></a></p>
<p><a class="reference internal" href="crcrest.html#crcrest-crc"><span class="std std-ref">crcrest-复位crc初始值</span></a></p>
<p><a class="reference internal" href="../variables/crcval.html#crcval-crc"><span class="std std-ref">crcval-crc校验结果</span></a></p>
</section>
<section id="id1">
<h2>crcputs指令-样例工程下载<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/串口屏高级应用/CRC校验/CRC校验.HMI">《CRC校验》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/串口屏高级应用/CRC校验/crc校验例程一对.zip">《crc校验例程一对》演示工程下载</a></p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="crcrest.html" class="btn btn-neutral float-left" title="crcrest-复位crc初始值" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="crcputh.html" class="btn btn-neutral float-right" title="crcputh-crc校验一组Hex" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>