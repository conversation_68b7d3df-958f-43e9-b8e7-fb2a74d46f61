#include "sys.h"
#include "usart.h"
#include "delay.h"
#include "led.h"
////////////////////////////////////////////////////////////////////////////////// 	 
//���ʹ��ucos,����������ͷ�ļ�����.
#if SYSTEM_SUPPORT_OS
#include "includes.h"					//ucos ʹ��	  
#endif
//////////////////////////////////////////////////////////////////////////////////	 
//������ֻ��ѧϰʹ�ã�δ���������ɣ��������������κ���;
//ALIENTEK STM32F4̽���߿�����
//����1��ʼ��		   
//����ԭ��@ALIENTEK
//������̳:www.openedv.com
//�޸�����:2014/6/10
//�汾��V1.5
//��Ȩ���У�����ؾ���
//Copyright(C) �������������ӿƼ����޹�˾ 2009-2019
//All rights reserved
//********************************************************************************
//V1.3�޸�˵�� 
//֧����Ӧ��ͬƵ���µĴ��ڲ���������.
//�����˶�printf��֧��
//�����˴��ڽ��������.
//������printf��һ���ַ���ʧ��bug
//V1.4�޸�˵��
//1,�޸Ĵ��ڳ�ʼ��IO��bug
//2,�޸���USART_RX_STA,ʹ�ô����������ֽ���Ϊ2��14�η�
//3,������USART_REC_LEN,���ڶ��崮������������յ��ֽ���(������2��14�η�)
//4,�޸���EN_USART1_RX��ʹ�ܷ�ʽ
//V1.5�޸�˵��
//1,�����˶�UCOSII��֧��
////////////////////////////////////////////////////////////////////////////////// 	  
 

//////////////////////////////////////////////////////////////////
//�������´���,֧��printf����,������Ҫѡ��use MicroLIB	  
#if 1
#pragma import(__use_no_semihosting)             
//��׼����Ҫ��֧�ֺ���                 
struct __FILE 
{ 
	int handle; 
}; 

FILE __stdout;       
//����_sys_exit()�Ա���ʹ�ð�����ģʽ    
void _sys_exit(int x) 
{ 
	x = x; 
} 
//�ض���fputc���� 
int fputc(int ch, FILE *f)
{ 	
	while((USART1->SR&0X40)==0);//ѭ������,ֱ���������   
	USART1->DR = (u8) ch;      
	return ch;
}
#endif
 
#if EN_USART1_RX   //���ʹ���˽���
//����1�жϷ������
//ע��,��ȡUSARTx->SR�ܱ���Ī������Ĵ���   	
u8 USART_RX_BUF[USART_REC_LEN];     //接收缓冲,最大USART_REC_LEN个字节.
//接收状态
//bit15：	接收完成标志
//bit14：	接收到0x0d
//bit13~0：	接收到的有效字节数目
u16 USART_RX_STA=0;       //接收状态标记

// 舵机协议相关变量
u8 SERVO_RX_BUF[SERVO_MAX_PACKET_LEN]; //舵机接收缓冲
u8 SERVO_RX_STA = 0;					//舵机接收状态: 0-等待帧头1, 1-等待帧头2, 2-接收数据
u8 SERVO_RX_CNT = 0;					//舵机接收计数器
u8 SERVO_PKT_LEN = 0;					//舵机数据包长度

// 串口屏通信相关变量
u8 SCREEN_RX_BUF[SCREEN_REC_LEN];		//串口屏接收缓冲
u8 SCREEN_RX_STA = 0;					//串口屏接收状态
u8 SCREEN_RX_CNT = 0;					//串口屏接收计数

// 舵机位置跟踪变量
u16 SERVO1_POSITION = 500;				//舵机1当前位置，初始值为中位
u16 SERVO2_POSITION = 500;				//舵机2当前位置，初始值为中位	

// 激光云台系统全局变量
LaserSystemState laser_system_state = LASER_STATE_UNCALIBRATED;
CurrentPosition current_position = CURRENT_POS_UNKNOWN;
u8 calibration_step = 0;                    // 标定步骤 (0-3)
ServoAngles calibration_points[4];          // 4个标定点的舵机角度
Point2D calibration_coords[4] = {           // A4纸四角坐标 (以中心为原点)
    {-148.5f, +105.0f},  // 左上角
    {+148.5f, +105.0f},  // 右上角  
    {+148.5f, -105.0f},  // 右下角
    {-148.5f, -105.0f}   // 左下角
};
ServoAngles point_A_angles = {500, 500};    // A点舵机角度
ServoAngles point_B_angles = {500, 500};    // B点舵机角度
Point2D point_A_coord = {0, 0};             // A点坐标
Point2D point_B_coord = {0, 0};             // B点坐标

//��ʼ��IO ����1 
//bound:������
void uart_init(u32 bound){
   //GPIO�˿�����
  GPIO_InitTypeDef GPIO_InitStructure;
	USART_InitTypeDef USART_InitStructure;
	NVIC_InitTypeDef NVIC_InitStructure;
	
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA,ENABLE); //ʹ��GPIOAʱ��
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1,ENABLE);//ʹ��USART1ʱ��
 
	//����1��Ӧ���Ÿ���ӳ��
	GPIO_PinAFConfig(GPIOA,GPIO_PinSource9,GPIO_AF_USART1); //GPIOA9����ΪUSART1
	GPIO_PinAFConfig(GPIOA,GPIO_PinSource10,GPIO_AF_USART1); //GPIOA10����ΪUSART1
	
	//USART1�˿�����
  GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9 | GPIO_Pin_10; //GPIOA9��GPIOA10
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;//���ù���
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;	//�ٶ�50MHz
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP; //���츴�����
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP; //����
	GPIO_Init(GPIOA,&GPIO_InitStructure); //��ʼ��PA9��PA10

   //USART1 ��ʼ������
	USART_InitStructure.USART_BaudRate = bound;//����������
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;//�ֳ�Ϊ8λ���ݸ�ʽ
	USART_InitStructure.USART_StopBits = USART_StopBits_1;//һ��ֹͣλ
	USART_InitStructure.USART_Parity = USART_Parity_No;//����żУ��λ
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;//��Ӳ������������
	USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;	//�շ�ģʽ
  USART_Init(USART1, &USART_InitStructure); //��ʼ������1
	
  USART_Cmd(USART1, ENABLE);  //ʹ�ܴ���1 
	
	//USART_ClearFlag(USART1, USART_FLAG_TC);
	
#if EN_USART1_RX	
	USART_ITConfig(USART1, USART_IT_RXNE, ENABLE);//��������ж�

	//Usart1 NVIC ����
  NVIC_InitStructure.NVIC_IRQChannel = USART1_IRQn;//����1�ж�ͨ��
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=3;//��ռ���ȼ�3
	NVIC_InitStructure.NVIC_IRQChannelSubPriority =3;		//�����ȼ�3
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;			//IRQͨ��ʹ��
	NVIC_Init(&NVIC_InitStructure);	//����ָ���Ĳ�����ʼ��VIC�Ĵ�����

#endif
	
}


void USART1_IRQHandler(void)                	//串口1中断服务程序
{
	u8 Res;
#if SYSTEM_SUPPORT_OS 		//如果SYSTEM_SUPPORT_OS为真，则需要支持OS.
	OSIntEnter();    
#endif
	if(USART_GetITStatus(USART1, USART_IT_RXNE) != RESET)  //接收中断
	{
		Res = USART_ReceiveData(USART1);	//读取接收到的数据
		
		// 舵机协议包状态机处理
		switch(SERVO_RX_STA)
		{
			case 0:	// 等待第一个帧头 0x55
				if(Res == SERVO_FRAME_HEADER)
				{
					SERVO_RX_STA = 1;
					SERVO_RX_CNT = 0;
				}
				break;
				
			case 1:	// 等待第二个帧头 0x55
				if(Res == SERVO_FRAME_HEADER)
				{
					SERVO_RX_STA = 2;
					SERVO_RX_BUF[0] = SERVO_FRAME_HEADER;
					SERVO_RX_BUF[1] = SERVO_FRAME_HEADER;
					SERVO_RX_CNT = 2;
				}
				else
				{
					SERVO_RX_STA = 0; // 重新开始
				}
				break;
				
			case 2:	// 接收数据包
				SERVO_RX_BUF[SERVO_RX_CNT] = Res;
				SERVO_RX_CNT++;
				
				if(SERVO_RX_CNT == 5) // 收到ID、Length、Cmd
				{
					SERVO_PKT_LEN = SERVO_RX_BUF[3] + 3; // Length + 帧头(2) + 校验(1)
					if(SERVO_PKT_LEN > SERVO_MAX_PACKET_LEN)
					{
						SERVO_RX_STA = 0; // 包长度错误，重新开始
						break;
					}
				}
				
				if(SERVO_RX_CNT >= 5 && SERVO_RX_CNT >= SERVO_PKT_LEN)
				{
					// 数据包接收完成，处理数据包
					Servo_ProcessPacket();
					// 重新开始等待下一包
					SERVO_RX_STA = 0;
				}
				
				if(SERVO_RX_CNT >= SERVO_MAX_PACKET_LEN)
				{
					SERVO_RX_STA = 0; // 防止溢出
				}
				break;
		}
  } 
#if SYSTEM_SUPPORT_OS 	//如果SYSTEM_SUPPORT_OS为真，则需要支持OS.
	OSIntExit();  											 
#endif
} 
#endif	

// 舵机控制函数实现

// 串口发送数组
void USART_SendArray(u8 *arr, u16 len) 
{
	u16 i;
	for (i = 0; i < len; i++) 
	{
		while(USART_GetFlagStatus(USART1, USART_FLAG_TXE) != SET);
		USART_SendData(USART1, arr[i]);
	}
	while(USART_GetFlagStatus(USART1, USART_FLAG_TC) != SET); // 等待发送完成
}

// 计算舵机协议校验和
u8 Servo_CalculateChecksum(u8 *data, u8 len) 
{
	u8 sum = 0;
	u8 i;
	for (i = 0; i < len; i++) 
	{
		sum += data[i];
	}
	return ~sum; // 取反
}

// 设置舵机为电机模式并控制转动速度
void Servo_SetMotorMode(u8 id, s16 speed) 
{
	u8 cmd_packet[10];
	
	// 构建命令包: 55 55 ID 07 1D 01 00 speed_low speed_high checksum
	cmd_packet[0] = 0x55;								// 帧头1
	cmd_packet[1] = 0x55;								// 帧头2
	cmd_packet[2] = id;									// 舵机ID
	cmd_packet[3] = 0x07;								// 数据长度
	cmd_packet[4] = 0x1D;								// 指令(SERVO_OR_MOTOR_MODE_WRITE = 29 = 0x1D)
	cmd_packet[5] = 0x01;								// 参数1: 电机模式
	cmd_packet[6] = 0x00;								// 参数2: 空值
	cmd_packet[7] = (u8)(speed & 0xFF);					// 参数3: 速度低字节
	cmd_packet[8] = (u8)((speed >> 8) & 0xFF);			// 参数4: 速度高字节
	
	// 计算校验和 (从ID开始到速度高字节)
	cmd_packet[9] = Servo_CalculateChecksum(&cmd_packet[2], 7);
	
	// 发送命令包
	USART_SendArray(cmd_packet, 10);
}

// 装载舵机电机(上电状态)
void Servo_LoadMotor(u8 id) 
{
	u8 cmd_packet[7];
	
	// 构建命令包: 55 55 ID 04 1F 01 checksum
	cmd_packet[0] = 0x55;								// 帧头1
	cmd_packet[1] = 0x55;								// 帧头2
	cmd_packet[2] = id;									// 舵机ID
	cmd_packet[3] = 0x04;								// 数据长度
	cmd_packet[4] = 0x1F;								// 指令(SERVO_LOAD_OR_UNLOAD_WRITE = 31 = 0x1F)
	cmd_packet[5] = 0x01;								// 参数1: 1=装载电机，0=卸载电机
	
	// 计算校验和 (从ID开始到参数)
	cmd_packet[6] = Servo_CalculateChecksum(&cmd_packet[2], 4);
	
	// 发送命令包
	USART_SendArray(cmd_packet, 7);
}

// 舵机位置控制(位置模式，带时间)
void Servo_MoveToPosition(u8 id, u16 position, u16 time) 
{
	u8 cmd_packet[10];
	
	// 构建命令包: 55 55 ID 07 01 pos_low pos_high time_low time_high checksum
	cmd_packet[0] = 0x55;								// 帧头1
	cmd_packet[1] = 0x55;								// 帧头2
	cmd_packet[2] = id;									// 舵机ID
	cmd_packet[3] = 0x07;								// 数据长度
	cmd_packet[4] = 0x01;								// 指令(SERVO_MOVE_TIME_WRITE = 1)
	cmd_packet[5] = (u8)(position & 0xFF);				// 参数1: 角度低字节
	cmd_packet[6] = (u8)((position >> 8) & 0xFF);		// 参数2: 角度高字节  
	cmd_packet[7] = (u8)(time & 0xFF);					// 参数3: 时间低字节
	cmd_packet[8] = (u8)((time >> 8) & 0xFF);			// 参数4: 时间高字节
	
	// 计算校验和 (从ID开始到时间高字节)
	cmd_packet[9] = Servo_CalculateChecksum(&cmd_packet[2], 7);
	
	// 发送命令包
	USART_SendArray(cmd_packet, 10);
}

// 读取舵机当前位置
void Servo_ReadPosition(u8 id)
{
	u8 cmd_packet[6];

	// 构建命令包: 55 55 ID 03 1C checksum (SERVO_POS_READ = 28)
	cmd_packet[0] = 0x55;								// 帧头1
	cmd_packet[1] = 0x55;								// 帧头2
	cmd_packet[2] = id;									// 舵机ID
	cmd_packet[3] = 0x03;								// 数据长度
	cmd_packet[4] = 0x1C;								// 指令(SERVO_POS_READ = 28)

	// 计算校验和 (从ID开始到指令)
	cmd_packet[5] = Servo_CalculateChecksum(&cmd_packet[2], 3);

	// 发送命令包
	USART_SendArray(cmd_packet, 6);
}

// 处理接收到的舵机数据包
u8 Servo_ProcessPacket(void) 
{
	u8 checksum;
	u16 position;
	u8 servo_id;
	
	// 检查数据包长度和校验和
	if(SERVO_RX_CNT < 6) return 0; // 最小包长度检查
	
	// 提取舵机ID
	servo_id = SERVO_RX_BUF[2];
	
	// 计算校验和
	checksum = Servo_CalculateChecksum(&SERVO_RX_BUF[2], SERVO_RX_CNT - 3);
	if(checksum != SERVO_RX_BUF[SERVO_RX_CNT - 1]) {
		return 0; // 校验失败
	}
	
	// 判断是否为位置读取响应 (指令0x1C，SERVO_POS_READ)
	if(SERVO_RX_BUF[4] == 0x1C && SERVO_RX_CNT >= 8) {
		// 提取位置数据 (低字节在前，高字节在后)
		position = SERVO_RX_BUF[5] | (SERVO_RX_BUF[6] << 8);
		
		// 更新对应舵机的位置变量
		if(servo_id == 1) {
			SERVO1_POSITION = position;
		} else if(servo_id == 2) {
			SERVO2_POSITION = position;
		}
		
		return 1; // 成功处理
	}
	
	return 0;
}

// UART2初始化函数(串口屏通信)
void uart2_init(u32 bound)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	USART_InitTypeDef USART_InitStructure;
	NVIC_InitTypeDef NVIC_InitStructure;
	
	// 使能UART2和GPIOA时钟
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART2, ENABLE);
 
	// PA2复用为USART2_TX, PA3复用为USART2_RX
	GPIO_PinAFConfig(GPIOA, GPIO_PinSource2, GPIO_AF_USART2);
	GPIO_PinAFConfig(GPIOA, GPIO_PinSource3, GPIO_AF_USART2);
	
	// UART2端口配置
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2 | GPIO_Pin_3;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
	GPIO_Init(GPIOA, &GPIO_InitStructure);

	// UART2初始化设置
	USART_InitStructure.USART_BaudRate = bound;
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;
	USART_InitStructure.USART_StopBits = USART_StopBits_1;
	USART_InitStructure.USART_Parity = USART_Parity_No;
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
	USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
  	USART_Init(USART2, &USART_InitStructure);
  	
#if EN_USART2_RX
	// Usart2 NVIC配置
	NVIC_InitStructure.NVIC_IRQChannel = USART2_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 3;
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 3;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_Init(&NVIC_InitStructure);
	
	USART_ITConfig(USART2, USART_IT_RXNE, ENABLE);
#endif
	
	USART_Cmd(USART2, ENABLE);
}

// UART2中断处理函数
void USART2_IRQHandler(void)
{
	u8 Res;
#if SYSTEM_SUPPORT_OS
	OSIntEnter();    
#endif
	if(USART_GetITStatus(USART2, USART_IT_RXNE) != RESET)
	{
		Res = USART_ReceiveData(USART2);
		
		// 串口屏数据接收处理
		if(SCREEN_RX_CNT < (SCREEN_REC_LEN-1))
		{
			SCREEN_RX_BUF[SCREEN_RX_CNT] = Res;
			SCREEN_RX_CNT++;
			
			// 检查是否为串口屏数据包结束标志 0xFF 0xFF 0xFF
			if(SCREEN_RX_CNT >= 3)
			{
				if(SCREEN_RX_BUF[SCREEN_RX_CNT-3] == 0xFF && 
				   SCREEN_RX_BUF[SCREEN_RX_CNT-2] == 0xFF && 
				   SCREEN_RX_BUF[SCREEN_RX_CNT-1] == 0xFF)
				{
					SCREEN_RX_STA = 1;  // 标记接收完成
				}
			}
		}
		else
		{
			SCREEN_RX_CNT = 0;  // 缓冲区溢出，重置
		}
	}
#if SYSTEM_SUPPORT_OS
	OSIntExit();  
#endif
}

// UART2发送数组
void USART2_SendArray(u8 *arr, u16 len)
{
	u16 i;
	for (i = 0; i < len; i++)
	{
		while(USART_GetFlagStatus(USART2, USART_FLAG_TXE) != SET);
		USART_SendData(USART2, arr[i]);
	}
	while(USART_GetFlagStatus(USART2, USART_FLAG_TC) != SET);
}

// 串口屏命令处理 - 激光云台专用按键布局
void Screen_ProcessCommand(void)
{
	u8 component_id;
	
	if(SCREEN_RX_STA == 1 && SCREEN_RX_CNT >= 6)
	{
		// 检查是否为控件事件 0x65
		if(SCREEN_RX_BUF[0] == 0x65)
		{
			component_id = SCREEN_RX_BUF[2];
			
			// 激光云台控制按键 - 从ID=1开始的新布局
			switch(component_id)
			{
				case 1:  // 开始标定按钮
					LaserSystem_StartCalibration();
					break;
					
				case 2:  // 记录标定点按钮（标定过程中使用）
					if(laser_system_state == LASER_STATE_CALIBRATING) {
						LaserSystem_RecordCalibrationPoint();
					}
					break;
					
				case 3:  // 卸载舵机按钮
					{
						u8 cmd_packet[7];
						
						// 卸载舵机1
						cmd_packet[0] = 0x55;
						cmd_packet[1] = 0x55;
						cmd_packet[2] = 1;           // 舵机1 ID
						cmd_packet[3] = 0x04;        // 数据长度
						cmd_packet[4] = 0x1F;        // 指令(SERVO_LOAD_OR_UNLOAD_WRITE)
						cmd_packet[5] = 0x00;        // 参数: 0=卸载电机
						cmd_packet[6] = Servo_CalculateChecksum(&cmd_packet[2], 4);
						USART_SendArray(cmd_packet, 7);
						delay_ms(100);  // 增加延迟确保命令执行
						
						// 卸载舵机2
						cmd_packet[2] = 2;           // 舵机2 ID
						cmd_packet[6] = Servo_CalculateChecksum(&cmd_packet[2], 4);
						USART_SendArray(cmd_packet, 7);
						delay_ms(100);  // 增加延迟确保命令执行
					}
					break;
					
				case 4:  // 装载舵机按钮
					Servo_LoadMotor(1);
					delay_ms(200);  // 增加延迟确保舵机稳定装载
					Servo_LoadMotor(2);
					delay_ms(200);  // 增加延迟确保舵机稳定装载
					break;
					
				case 5:  // 记录A点按钮
					LaserSystem_RecordPointA();
					break;
					
				case 6:  // 记录B点按钮
					LaserSystem_RecordPointB();
					break;
					
				case 7:  // 移动到A点按钮（快速移动）
					LaserSystem_MoveToA();
					break;
					
				case 8:  // 移动到B点按钮（快速移动）
					LaserSystem_MoveToB();
					break;
					
				case 9:  // 直线运行按钮（核心功能）
					if(current_position == CURRENT_POS_A) {
						LaserSystem_MoveLinearToB();  // 当前在A点，直线移动到B点
					} else if(current_position == CURRENT_POS_B) {
						LaserSystem_MoveLinearToA();  // 当前在B点，直线移动到A点
					} else {
						// 位置未知，先移动到A点
						LaserSystem_MoveToA();
					}
					break;
			}
		}
		
		// 清除接收状态
		SCREEN_RX_STA = 0;
		SCREEN_RX_CNT = 0;
	}
}

//===============================================================================
// 激光云台控制系统函数实现
//===============================================================================

// 初始化激光云台系统
void LaserSystem_Init(void)
{
	u8 cmd_packet[7];
	
	laser_system_state = LASER_STATE_UNCALIBRATED;
	current_position = CURRENT_POS_UNKNOWN;
	calibration_step = 0;
	
	// 初始化A/B点为中心位置
	point_A_angles.servo1_angle = 500;
	point_A_angles.servo2_angle = 500;
	point_B_angles.servo1_angle = 500;
	point_B_angles.servo2_angle = 500;
	
	// 延迟一下确保串口屏初始化完成
	delay_ms(500);
	
	// 确保舵机处于卸载状态（上电默认状态）
	// 卸载舵机1
	cmd_packet[0] = 0x55;
	cmd_packet[1] = 0x55;
	cmd_packet[2] = 1;           // 舵机1 ID
	cmd_packet[3] = 0x04;        // 数据长度
	cmd_packet[4] = 0x1F;        // 指令(SERVO_LOAD_OR_UNLOAD_WRITE)
	cmd_packet[5] = 0x00;        // 参数: 0=卸载电机
	cmd_packet[6] = Servo_CalculateChecksum(&cmd_packet[2], 4);
	USART_SendArray(cmd_packet, 7);
	delay_ms(100);
	
	// 卸载舵机2
	cmd_packet[2] = 2;           // 舵机2 ID
	cmd_packet[6] = Servo_CalculateChecksum(&cmd_packet[2], 4);
	USART_SendArray(cmd_packet, 7);
	delay_ms(100);
	
	// 更新屏幕显示为"请先标定"
	LaserSystem_UpdateScreenStatus();
}

// 开始标定
void LaserSystem_StartCalibration(void)
{
	u8 cmd_packet[7];
	
	if(laser_system_state != LASER_STATE_UNCALIBRATED) {
		// 重新标定，重置状态
		calibration_step = 0;
	}
	
	laser_system_state = LASER_STATE_CALIBRATING;
	calibration_step = 0;
	current_position = CURRENT_POS_UNKNOWN;
	
	// 直接卸载舵机，允许手动调节（不要先装载，避免冲突）
	// 卸载舵机1
	cmd_packet[0] = 0x55;
	cmd_packet[1] = 0x55;
	cmd_packet[2] = 1;           // 舵机1 ID
	cmd_packet[3] = 0x04;        // 数据长度
	cmd_packet[4] = 0x1F;        // 指令(SERVO_LOAD_OR_UNLOAD_WRITE)
	cmd_packet[5] = 0x00;        // 参数: 0=卸载电机
	cmd_packet[6] = Servo_CalculateChecksum(&cmd_packet[2], 4);
	USART_SendArray(cmd_packet, 7);
	delay_ms(100);  // 增加延迟确保命令执行
	
	// 卸载舵机2
	cmd_packet[2] = 2;           // 舵机2 ID
	cmd_packet[6] = Servo_CalculateChecksum(&cmd_packet[2], 4);
	USART_SendArray(cmd_packet, 7);
	delay_ms(100);  // 增加延迟确保命令执行
	
	// 更新屏幕显示为"0/4"
	LaserSystem_UpdateScreenStatus();
}

// 记录标定点
void LaserSystem_RecordCalibrationPoint(void)
{
	if(laser_system_state != LASER_STATE_CALIBRATING || calibration_step >= 4) {
		return;
	}
	
	// 读取当前舵机位置
	Servo_ReadPosition(1);
	delay_ms(100);
	Servo_ReadPosition(2);
	delay_ms(100);
	
	// 记录当前位置
	calibration_points[calibration_step].servo1_angle = SERVO1_POSITION;
	calibration_points[calibration_step].servo2_angle = SERVO2_POSITION;
	
	calibration_step++;
	
	// 更新显示进度
	LaserSystem_UpdateScreenStatus();
	
	// 检查是否标定完成
	if(calibration_step >= 4) {
		laser_system_state = LASER_STATE_CALIBRATED;
		
		// 重新装载舵机（标定完成后需要重新上电控制）
		Servo_LoadMotor(1);
		delay_ms(200);  // 增加延迟确保舵机稳定装载
		Servo_LoadMotor(2);
		delay_ms(200);  // 增加延迟确保舵机稳定装载
		
		// 最终更新显示为"标定完毕"
		LaserSystem_UpdateScreenStatus();
	}
}

// 双线性插值算法 - 将舵机角度转换为坐标
Point2D LaserSystem_AnglesToCoords(ServoAngles angles)
{
	Point2D result = {0, 0};
	float s1_range, s2_range;
	float s1_t = 0, s2_t = 0;
	float v1_range, v2_range;
	float v1_t = 0, v2_t = 0;
	float avg_h_t, avg_v_t;
	
	if(laser_system_state != LASER_STATE_CALIBRATED) {
		return result;  // 未标定时返回(0,0)
	}
	
	// 简化的双线性插值 - 基于4个标定点
	// 将舵机角度范围映射到标定点范围
	s1_range = calibration_points[1].servo1_angle - calibration_points[0].servo1_angle; // 上边：右-左
	s2_range = calibration_points[2].servo1_angle - calibration_points[3].servo1_angle; // 下边：右-左
	
	if(s1_range != 0) {
		s1_t = (float)(angles.servo1_angle - calibration_points[0].servo1_angle) / s1_range;
	}
	if(s2_range != 0) {
		s2_t = (float)(angles.servo1_angle - calibration_points[3].servo1_angle) / s2_range;
	}
	
	// 限制范围
	if(s1_t < 0) s1_t = 0;
	if(s1_t > 1) s1_t = 1;
	if(s2_t < 0) s2_t = 0;
	if(s2_t > 1) s2_t = 1;
	
	// 垂直方向插值
	v1_range = calibration_points[3].servo2_angle - calibration_points[0].servo2_angle; // 左边：下-上
	v2_range = calibration_points[2].servo2_angle - calibration_points[1].servo2_angle; // 右边：下-上
	
	if(v1_range != 0) {
		v1_t = (float)(angles.servo2_angle - calibration_points[0].servo2_angle) / v1_range;
	}
	if(v2_range != 0) {
		v2_t = (float)(angles.servo2_angle - calibration_points[1].servo2_angle) / v2_range;
	}
	
	// 限制范围
	if(v1_t < 0) v1_t = 0;
	if(v1_t > 1) v1_t = 1;
	if(v2_t < 0) v2_t = 0;
	if(v2_t > 1) v2_t = 1;
	
	// 双线性插值计算坐标
	avg_h_t = (s1_t + s2_t) / 2.0f;  // 水平插值参数平均
	avg_v_t = (v1_t + v2_t) / 2.0f;  // 垂直插值参数平均
	
	result.x = calibration_coords[0].x + avg_h_t * (calibration_coords[1].x - calibration_coords[0].x);
	result.y = calibration_coords[0].y + avg_v_t * (calibration_coords[3].y - calibration_coords[0].y);
	
	return result;
}

// 坐标转换为舵机角度（逆运算）
u8 LaserSystem_CoordsToAngles(Point2D coord, ServoAngles *angles)
{
	float x_ratio, y_ratio;
	float servo1_top, servo1_bottom;
	float servo2_left, servo2_right;
	
	if(laser_system_state != LASER_STATE_CALIBRATED || angles == 0) {
		return 0;  // 未标定或参数错误
	}
	
	// 计算坐标在A4纸面上的相对位置 (0-1)
	x_ratio = (coord.x - calibration_coords[0].x) / (calibration_coords[1].x - calibration_coords[0].x);
	y_ratio = (coord.y - calibration_coords[0].y) / (calibration_coords[3].y - calibration_coords[0].y);
	
	// 限制范围
	if(x_ratio < 0) x_ratio = 0;
	if(x_ratio > 1) x_ratio = 1;
	if(y_ratio < 0) y_ratio = 0;
	if(y_ratio > 1) y_ratio = 1;
	
	// 基于标定点进行双线性插值
	// 水平方向插值
	servo1_top = calibration_points[0].servo1_angle + 
		x_ratio * (calibration_points[1].servo1_angle - calibration_points[0].servo1_angle);
	servo1_bottom = calibration_points[3].servo1_angle + 
		x_ratio * (calibration_points[2].servo1_angle - calibration_points[3].servo1_angle);
	
	// 垂直方向插值
	servo2_left = calibration_points[0].servo2_angle + 
		y_ratio * (calibration_points[3].servo2_angle - calibration_points[0].servo2_angle);
	servo2_right = calibration_points[1].servo2_angle + 
		y_ratio * (calibration_points[2].servo2_angle - calibration_points[1].servo2_angle);
	
	// 最终插值
	angles->servo1_angle = (u16)(servo1_top + y_ratio * (servo1_bottom - servo1_top));
	angles->servo2_angle = (u16)(servo2_left + x_ratio * (servo2_right - servo2_left));
	
	// 范围检查
	if(angles->servo1_angle > 1000) angles->servo1_angle = 1000;
	if(angles->servo2_angle > 1000) angles->servo2_angle = 1000;
	
	return 1;  // 成功
}

// 移动到指定舵机角度
void LaserSystem_MoveToPoint(ServoAngles target_angles)
{
	u16 angle1_diff, angle2_diff;
	u16 max_diff, move_time;
	
	laser_system_state = LASER_STATE_MOVING;
	
	// 计算移动时间 - 根据角度差距调整
	angle1_diff = (target_angles.servo1_angle > SERVO1_POSITION) ? 
		(target_angles.servo1_angle - SERVO1_POSITION) : (SERVO1_POSITION - target_angles.servo1_angle);
	angle2_diff = (target_angles.servo2_angle > SERVO2_POSITION) ? 
		(target_angles.servo2_angle - SERVO2_POSITION) : (SERVO2_POSITION - target_angles.servo2_angle);
	
	max_diff = (angle1_diff > angle2_diff) ? angle1_diff : angle2_diff;
	move_time = 300 + max_diff;  // 基础时间300ms + 根据角度差调整
	if(move_time > 2000) move_time = 2000;  // 最大2秒
	
	// 发送移动指令
	Servo_MoveToPosition(1, target_angles.servo1_angle, move_time);
	delay_ms(50);
	Servo_MoveToPosition(2, target_angles.servo2_angle, move_time);
	
	// 等待移动完成
	delay_ms(move_time + 100);
	
	// 更新当前位置
	SERVO1_POSITION = target_angles.servo1_angle;
	SERVO2_POSITION = target_angles.servo2_angle;
	
	laser_system_state = LASER_STATE_READY;
	LaserSystem_UpdateScreenStatus();
}

// 记录A点
void LaserSystem_RecordPointA(void)
{
	if(laser_system_state != LASER_STATE_CALIBRATED && laser_system_state != LASER_STATE_READY) {
		return;  // 必须先完成标定
	}
	
	// 读取当前舵机位置
	Servo_ReadPosition(1);
	delay_ms(100);
	Servo_ReadPosition(2);
	delay_ms(100);
	
	// 记录A点角度
	point_A_angles.servo1_angle = SERVO1_POSITION;
	point_A_angles.servo2_angle = SERVO2_POSITION;
	
	// 转换为坐标
	point_A_coord = LaserSystem_AnglesToCoords(point_A_angles);
	
	current_position = CURRENT_POS_A;
	laser_system_state = LASER_STATE_READY;
	LaserSystem_UpdateScreenStatus();
}

// 记录B点
void LaserSystem_RecordPointB(void)
{
	if(laser_system_state != LASER_STATE_CALIBRATED && laser_system_state != LASER_STATE_READY) {
		return;  // 必须先完成标定
	}
	
	// 读取当前舵机位置
	Servo_ReadPosition(1);
	delay_ms(100);
	Servo_ReadPosition(2);
	delay_ms(100);
	
	// 记录B点角度
	point_B_angles.servo1_angle = SERVO1_POSITION;
	point_B_angles.servo2_angle = SERVO2_POSITION;
	
	// 转换为坐标
	point_B_coord = LaserSystem_AnglesToCoords(point_B_angles);
	
	current_position = CURRENT_POS_B;
	laser_system_state = LASER_STATE_READY;
	LaserSystem_UpdateScreenStatus();
}

// 移动到A点
void LaserSystem_MoveToA(void)
{
	if(laser_system_state != LASER_STATE_READY) {
		return;
	}
	
	LaserSystem_MoveToPoint(point_A_angles);
	current_position = CURRENT_POS_A;
}

// 移动到B点
void LaserSystem_MoveToB(void)
{
	if(laser_system_state != LASER_STATE_READY) {
		return;
	}
	
	LaserSystem_MoveToPoint(point_B_angles);
	current_position = CURRENT_POS_B;
}

// 更新串口屏状态显示
void LaserSystem_UpdateScreenStatus(void)
{
	u8 cmd_str[60];
	u8 len;
	
	// 更新标定完成度显示 (使用t0文本框)
	len = 0;
	cmd_str[len++] = 't';
	cmd_str[len++] = '0';  // 标定完成度文本框
	cmd_str[len++] = '.';
	cmd_str[len++] = 't';
	cmd_str[len++] = 'x';
	cmd_str[len++] = 't';
	cmd_str[len++] = '=';
	cmd_str[len++] = '"';
	
	// 根据标定状态显示不同的完成度提示
	switch(laser_system_state) {
		case LASER_STATE_UNCALIBRATED:
			// "请先标定"
			cmd_str[len++] = 0xC7; cmd_str[len++] = 0xEB;  // 请
			cmd_str[len++] = 0xCF; cmd_str[len++] = 0xC8;  // 先
			cmd_str[len++] = 0xB1; cmd_str[len++] = 0xEA;  // 标
			cmd_str[len++] = 0xB6; cmd_str[len++] = 0xA8;  // 定
			break;
			
		case LASER_STATE_CALIBRATING:
			// 显示标定进度 "1/4", "2/4", "3/4", "4/4"
			cmd_str[len++] = '0' + calibration_step;
			cmd_str[len++] = '/';
			cmd_str[len++] = '4';
			break;
			
		case LASER_STATE_CALIBRATED:
		case LASER_STATE_READY:
			// "标定完毕"
			cmd_str[len++] = 0xB1; cmd_str[len++] = 0xEA;  // 标
			cmd_str[len++] = 0xB6; cmd_str[len++] = 0xA8;  // 定
			cmd_str[len++] = 0xCD; cmd_str[len++] = 0xEA;  // 完
			cmd_str[len++] = 0xB1; cmd_str[len++] = 0xCF;  // 毕
			break;
			
		case LASER_STATE_MOVING:
			// "运动中"
			cmd_str[len++] = 0xD4; cmd_str[len++] = 0xCB;  // 运
			cmd_str[len++] = 0xB6; cmd_str[len++] = 0xAF;  // 动
			cmd_str[len++] = 0xD6; cmd_str[len++] = 0xD0;  // 中
			break;
	}
	
	cmd_str[len++] = '"';
	cmd_str[len++] = 0xFF;
	cmd_str[len++] = 0xFF;
	cmd_str[len++] = 0xFF;
	
	USART2_SendArray(cmd_str, len);
	delay_ms(50);
	
	// 更新当前位置显示 (使用t1文本框)
	len = 0;
	cmd_str[len++] = 't';
	cmd_str[len++] = '1';  // 当前位置文本框
	cmd_str[len++] = '.';
	cmd_str[len++] = 't';
	cmd_str[len++] = 'x';
	cmd_str[len++] = 't';
	cmd_str[len++] = '=';
	cmd_str[len++] = '"';
	
	// "当前位置："
	cmd_str[len++] = 0xB5; cmd_str[len++] = 0xB1;  // 当
	cmd_str[len++] = 0xC7; cmd_str[len++] = 0xB0;  // 前
	cmd_str[len++] = 0xCE; cmd_str[len++] = 0xBB;  // 位
	cmd_str[len++] = 0xD6; cmd_str[len++] = 0xC3;  // 置
	cmd_str[len++] = ':';
	
	switch(current_position) {
		case CURRENT_POS_A:
			cmd_str[len++] = 'A'; 
			cmd_str[len++] = 0xB5; cmd_str[len++] = 0xE3;  // 点
			break;
		case CURRENT_POS_B:
			cmd_str[len++] = 'B';
			cmd_str[len++] = 0xB5; cmd_str[len++] = 0xE3;  // 点
			break;
		case CURRENT_POS_UNKNOWN:
		default:
			// "未知"
			cmd_str[len++] = 0xCE; cmd_str[len++] = 0xB4;  // 未
			cmd_str[len++] = 0xD6; cmd_str[len++] = 0xAA;  // 知
			break;
	}
	
	cmd_str[len++] = '"';
	cmd_str[len++] = 0xFF;
	cmd_str[len++] = 0xFF;
	cmd_str[len++] = 0xFF;
	
	USART2_SendArray(cmd_str, len);
}

 



// 直线轨迹规划 - 从A点移动到B点
void LaserSystem_MoveLinearToB(void)
{
	float dx, dy, line_length;
	u8 num_points;
	u16 step_time;
	u8 i;
	float t;
	Point2D current_coord;
	ServoAngles target_angles;
	u16 total_angle1_diff, total_angle2_diff, max_total_diff;
	
	if(laser_system_state != LASER_STATE_READY) {
		return;
	}
	
	laser_system_state = LASER_STATE_MOVING;
	
	// 计算A到B的直线距离
	dx = point_B_coord.x - point_A_coord.x;
	dy = point_B_coord.y - point_A_coord.y;
	line_length = dx*dx + dy*dy;  // 使用平方距离避免sqrt
	
	// 根据距离决定插值点数量 (每5mm一个点，最少10个点)
	num_points = (u8)(line_length / 25.0f) + 10;  // 增加插值点数
	if(num_points > 30) num_points = 30;  // 最多30个点
	
	// 计算总的角度变化量，用于确定合理的运动时间
	total_angle1_diff = (point_B_angles.servo1_angle > point_A_angles.servo1_angle) ? 
		(point_B_angles.servo1_angle - point_A_angles.servo1_angle) : 
		(point_A_angles.servo1_angle - point_B_angles.servo1_angle);
	total_angle2_diff = (point_B_angles.servo2_angle > point_A_angles.servo2_angle) ? 
		(point_B_angles.servo2_angle - point_A_angles.servo2_angle) : 
		(point_A_angles.servo2_angle - point_B_angles.servo2_angle);
	
	// 基于最大角度变化计算每步时间，确保平滑运动
	max_total_diff = (total_angle1_diff > total_angle2_diff) ? total_angle1_diff : total_angle2_diff;
	step_time = 80 + (max_total_diff / num_points);  // 基础80ms + 根据角度调整
	if(step_time > 300) step_time = 300;  // 最大300ms
	if(step_time < 80) step_time = 80;    // 最小80ms
	
	// 逐点移动，实现直线轨迹
	for(i = 1; i <= num_points; i++) {
		// 计算当前插值点的坐标
		t = (float)i / (float)num_points;  // 插值参数 0-1
		current_coord.x = point_A_coord.x + t * dx;
		current_coord.y = point_A_coord.y + t * dy;
		
		// 转换为舵机角度
		if(LaserSystem_CoordsToAngles(current_coord, &target_angles)) {
			// 同时发送两个舵机命令，减少时间差
			Servo_MoveToPosition(1, target_angles.servo1_angle, step_time);
			Servo_MoveToPosition(2, target_angles.servo2_angle, step_time);  // 立即发送，不延迟
			
			// 等待移动完成
			delay_ms(step_time + 20);
			
			// 更新当前位置跟踪
			SERVO1_POSITION = target_angles.servo1_angle;
			SERVO2_POSITION = target_angles.servo2_angle;
		}
	}
	
	current_position = CURRENT_POS_B;
	laser_system_state = LASER_STATE_READY;
	LaserSystem_UpdateScreenStatus();
}

// 直线轨迹规划 - 从B点移动到A点
void LaserSystem_MoveLinearToA(void)
{
	float dx, dy, line_length;
	u8 num_points;
	u16 step_time;
	u8 i;
	float t;
	Point2D current_coord;
	ServoAngles target_angles;
	u16 total_angle1_diff, total_angle2_diff, max_total_diff;
	
	if(laser_system_state != LASER_STATE_READY) {
		return;
	}
	
	laser_system_state = LASER_STATE_MOVING;
	
	// 计算B到A的直线距离
	dx = point_A_coord.x - point_B_coord.x;
	dy = point_A_coord.y - point_B_coord.y;
	line_length = dx*dx + dy*dy;  // 使用平方距离避免sqrt
	
	// 根据距离决定插值点数量 (每5mm一个点，最少10个点)
	num_points = (u8)(line_length / 25.0f) + 10;  // 增加插值点数
	if(num_points > 30) num_points = 30;  // 最多30个点
	
	// 计算总的角度变化量，用于确定合理的运动时间
	total_angle1_diff = (point_A_angles.servo1_angle > point_B_angles.servo1_angle) ? 
		(point_A_angles.servo1_angle - point_B_angles.servo1_angle) : 
		(point_B_angles.servo1_angle - point_A_angles.servo1_angle);
	total_angle2_diff = (point_A_angles.servo2_angle > point_B_angles.servo2_angle) ? 
		(point_A_angles.servo2_angle - point_B_angles.servo2_angle) : 
		(point_B_angles.servo2_angle - point_A_angles.servo2_angle);
	
	// 基于最大角度变化计算每步时间，确保平滑运动
	max_total_diff = (total_angle1_diff > total_angle2_diff) ? total_angle1_diff : total_angle2_diff;
	step_time = 80 + (max_total_diff / num_points);  // 基础80ms + 根据角度调整
	if(step_time > 300) step_time = 300;  // 最大300ms
	if(step_time < 80) step_time = 80;    // 最小80ms
	
	// 逐点移动，实现直线轨迹
	for(i = 1; i <= num_points; i++) {
		// 计算当前插值点的坐标
		t = (float)i / (float)num_points;  // 插值参数 0-1
		current_coord.x = point_B_coord.x + t * dx;
		current_coord.y = point_B_coord.y + t * dy;
		
		// 转换为舵机角度
		if(LaserSystem_CoordsToAngles(current_coord, &target_angles)) {
			// 同时发送两个舵机命令，减少时间差
			Servo_MoveToPosition(1, target_angles.servo1_angle, step_time);
			Servo_MoveToPosition(2, target_angles.servo2_angle, step_time);  // 立即发送，不延迟
			
			// 等待移动完成
			delay_ms(step_time + 20);
			
			// 更新当前位置跟踪
			SERVO1_POSITION = target_angles.servo1_angle;
			SERVO2_POSITION = target_angles.servo2_angle;
		}
	}
	
	current_position = CURRENT_POS_A;
	laser_system_state = LASER_STATE_READY;
	LaserSystem_UpdateScreenStatus();
}
