# 技术参数速查表：STM32舵机控制项目

> **快速查阅**: 开发过程中的关键参数和代码片段  
> **更新日期**: 2025年7月17日  

---

## 🔧 硬件参数

### STM32F407ZGT6引脚分配
| 功能 | 引脚 | 端口 | 配置 | 说明 |
|------|------|------|------|------|
| 舵机通信 | PA2 | GPIOA | 复用功能(USART2) | 半双工模式 |
| 按键K1 | PE3 | GPIOE | 上拉输入 | 按下为低电平 |
| LED0 | PF9 | GPIOF | 推挽输出 | 低电平点亮 |
| LED1 | PF10 | GPIOF | 推挽输出 | 低电平点亮 |

### HTS-25L舵机参数
| 参数 | 数值 | 说明 |
|------|------|------|
| 通信波特率 | 115200 bps | 半双工UART |
| 工作电压 | 7.4V | 独立供电 |
| 位置范围 | 0-1000 | 对应0°-240° |
| 舵机ID | 1 | 出厂默认 |
| 30度步进 | 125计数 | 30°/240°×1000 |
| 运动时间 | 1000ms | 平稳运动 |

---

## 📡 通信协议

### 协议帧格式
```
0x55 0x55 | ID | Length | Cmd | Param1 | Param2 | ... | Checksum
```

### 关键指令
| 指令名称 | 指令码 | Length | 参数说明 |
|----------|--------|--------|----------|
| MOVE_TIME_WRITE | 0x01 | 7 | Pos_L, Pos_H, Time_L, Time_H |
| POS_READ | 0x1C | 3 | 无参数 |

### 校验和计算
```c
uint8_t calc_checksum(uint8_t id, uint8_t length, uint8_t cmd, uint8_t *params, uint8_t param_len)
{
    uint16_t sum = id + length + cmd;
    for(uint8_t i = 0; i < param_len; i++) {
        sum += params[i];
    }
    return (uint8_t)(~(sum & 0xFF));
}
```

---

## 💻 关键代码片段

### UART半双工初始化
```c
void HTS25L_UART_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;
    
    // 使能时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART2, ENABLE);
    
    // 配置PA2
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_OD;  // 开漏
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;   // 上拉
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    GPIO_PinAFConfig(GPIOA, GPIO_PinSource2, GPIO_AF_USART2);
    
    // 配置USART2
    USART_InitStructure.USART_BaudRate = 115200;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;
    USART_Init(USART2, &USART_InitStructure);
    
    // 使能半双工
    USART_HalfDuplexCmd(USART2, ENABLE);
    USART_Cmd(USART2, ENABLE);
}
```

### 角度位置转换
```c
// 角度转位置
uint16_t HTS25L_AngleDegToPos(float deg)
{
    if(deg < 0) deg = 0;
    if(deg > 240.0f) deg = 240.0f;
    return (uint16_t)((deg / 240.0f) * 1000.0f + 0.5f);
}

// 位置转角度
float HTS25L_PosToAngleDeg(uint16_t pos)
{
    if(pos > 1000) pos = 1000;
    return ((float)pos / 1000.0f) * 240.0f;
}
```

### 按键消抖
```c
uint8_t Button_Scan(void)
{
    static uint8_t key_state = 1;  // 上次状态
    uint8_t current_state = GPIO_ReadInputDataBit(GPIOE, GPIO_Pin_3);
    
    if(key_state == 1 && current_state == 0)  // 检测下降沿
    {
        Delay_ms(20);  // 消抖
        if(GPIO_ReadInputDataBit(GPIOE, GPIO_Pin_3) == 0)
        {
            key_state = 0;
            return 1;  // 按键按下
        }
    }
    else if(key_state == 0 && current_state == 1)
    {
        key_state = 1;  // 按键释放
    }
    
    return 0;  // 无按键
}
```

### 30度步进逻辑
```c
void Servo_Step30Degree(void)
{
    uint16_t next_pos = current_target_pos + 125;  // 30度步进
    
    // 边界检查
    if(next_pos > 1000)
    {
        next_pos = 0;  // 复位到0度
    }
    
    // 发送运动指令
    if(HTS25L_MoveTimeWrite(&hts_driver, 1, next_pos, 1000) == 0)
    {
        current_target_pos = next_pos;
        servo_state = SERVO_STATE_MOVING;
    }
}
```

---

## 🔍 调试参数

### 超时设置
```c
#define UART_TX_TIMEOUT_MS    20
#define UART_RX_TIMEOUT_MS    100
#define SERVO_MOVE_TIMEOUT_MS 1200  // 运动超时
```

### LED状态定义
```c
typedef enum {
    LED_STATE_HEARTBEAT = 0,  // 心跳闪烁
    LED_STATE_MOVING,         // 运动指示
    LED_STATE_ERROR,          // 错误指示
    LED_STATE_COMM_ERROR      // 通信错误
} LED_State_t;
```

### 错误代码
```c
#define ERROR_NONE           0
#define ERROR_UART_TIMEOUT   1
#define ERROR_CHECKSUM       2
#define ERROR_SERVO_NO_RESP  3
#define ERROR_INVALID_POS    4
```

---

## 📋 测试用例

### 基础通信测试
```c
// 测试1：发送位置读取指令
uint16_t pos;
int result = HTS25L_ReadPos(&hts_driver, 1, &pos);
// 期望：result = 0, pos为有效值(0-1000)

// 测试2：发送运动指令
result = HTS25L_MoveTimeWrite(&hts_driver, 1, 500, 1000);
// 期望：result = 0, 舵机运动到中位
```

### 功能测试序列
```c
// 测试序列：完整30度步进循环
uint16_t test_positions[] = {0, 125, 250, 375, 500, 625, 750, 875, 1000, 0};
for(int i = 0; i < 10; i++) {
    HTS25L_MoveTimeWrite(&hts_driver, 1, test_positions[i], 1000);
    Delay_ms(1200);  // 等待运动完成
}
```

---

## ⚠️ 常见问题解决

### 通信问题
| 问题 | 可能原因 | 解决方法 |
|------|----------|----------|
| 无应答 | 波特率错误 | 检查115200设置 |
| 校验错误 | 校验算法错误 | 验证校验和计算 |
| 数据乱码 | 半双工时序 | 检查发送/接收切换 |

### 运动问题
| 问题 | 可能原因 | 解决方法 |
|------|----------|----------|
| 不运动 | 供电问题 | 检查7.4V电源 |
| 运动异常 | 位置参数错误 | 检查0-1000范围 |
| 抖动 | 时间参数过小 | 增加运动时间 |

### 按键问题
| 问题 | 可能原因 | 解决方法 |
|------|----------|----------|
| 无响应 | GPIO配置错误 | 检查上拉设置 |
| 重复触发 | 消抖不足 | 增加消抖时间 |
| 状态错乱 | 状态机错误 | 检查状态切换逻辑 |

---

## 🚀 性能优化建议

### 代码优化
- 使用DMA提高UART传输效率
- 实现中断驱动的按键检测
- 添加看门狗防止死机

### 功能扩展
- 支持多个舵机控制
- 添加位置反馈闭环控制
- 实现运动轨迹规划

---

**此速查表涵盖了开发过程中的所有关键参数，建议开发时随时参考！** 📚
