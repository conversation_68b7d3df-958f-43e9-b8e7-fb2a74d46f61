#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "OLED.h"
#include "Servo.h"
#include "LED.h"
#include "Key.h"

// Simple integer to string conversion function
void IntToString(uint16_t num, char* str) {
    uint8_t i = 0;
    uint8_t temp[6];

    if (num == 0) {
        str[0] = '0';
        str[1] = '\0';
        return;
    }

    // Extract digits
    while (num > 0) {
        temp[i] = num % 10 + '0';
        num /= 10;
        i++;
    }

    // Reverse string
    for (uint8_t j = 0; j < i; j++) {
        str[j] = temp[i - 1 - j];
    }
    str[i] = '\0';
}

int main(void)
{
    uint16_t servo_position;
    uint16_t servo_angle_int;  // Use integer angle
    char angle_str[16];
    char num_str[8];

    /*Module initialization*/
    OLED_Init();        // OLED initialization
    LED_Init();         // LED initialization
    Servo_Init();       // Servo initialization
    Key_Init();         // Key initialization

    /*Display static strings*/
    OLED_ShowString(1, 1, "HTS-25L Test");
    OLED_ShowString(2, 1, "Protocol:0x55");
    OLED_ShowString(3, 1, "Baud:115200");
    OLED_ShowString(4, 1, "Press PA5...");

    // Wait for system stable
    Delay_ms(2000);

    // Indicate start sending commands
    LED_ON();
    OLED_ShowString(4, 1, "Sending...");

    // Test servo ID1 - 44 degrees, 1000ms movement time
    OLED_ShowString(2, 1, "ID1: 44 deg");
    Servo_SetPositionWithTime(1, 44.0f, 1000);
    Delay_ms(1500);  // Wait for movement complete

    // Test servo ID2 - 112 degrees, 1000ms movement time
    OLED_ShowString(3, 1, "ID2:112 deg");
    Servo_SetPositionWithTime(2, 112.0f, 1000);
    Delay_ms(1500);  // Wait for movement complete

    // Indicate command sending complete
    LED_OFF();
    OLED_ShowString(4, 1, "Press PA5...");

    while (1)
    {
        // Check if PA5 key is pressed
        if (Key_PA5_GetPressed()) {
            LED_ON();
            OLED_ShowString(4, 1, "Reading...");

            if (Servo_ReadPosition(1, &servo_position) == 0) {
                // Convert to integer angle (avoid floating point)
                servo_angle_int = (servo_position * 240) / 1000;

                // Manually build string "ID1: XXX deg"
                angle_str[0] = 'I';
                angle_str[1] = 'D';
                angle_str[2] = '1';
                angle_str[3] = ':';
                angle_str[4] = ' ';

                // Convert angle number to string
                IntToString(servo_angle_int, num_str);

                // Concatenate strings
                uint8_t i = 5;
                uint8_t j = 0;
                while (num_str[j] != '\0') {
                    angle_str[i++] = num_str[j++];
                }
                angle_str[i++] = ' ';
                angle_str[i++] = 'd';
                angle_str[i++] = 'e';
                angle_str[i++] = 'g';
                angle_str[i] = '\0';

                OLED_ShowString(4, 1, angle_str);
            } else {
                OLED_ShowString(4, 1, "Read Error");
            }

            LED_OFF();
            Delay_ms(100);
        }

        Delay_ms(50);  // Main loop delay
    }
}
