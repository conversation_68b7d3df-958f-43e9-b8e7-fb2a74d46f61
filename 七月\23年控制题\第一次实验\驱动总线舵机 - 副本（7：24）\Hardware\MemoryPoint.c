#include "MemoryPoint.h"
#include "Servo.h"
#include "Delay.h"
#include "OLED.h"
#include "Bluetooth.h"
#include <stdio.h>

// 启用蓝牙功能
#define BLUETOOTH_ENABLED

// 全局变量
static MemoryPoint_t memory_point = {0};
static MemoryState_t current_state = MEMORY_STATE_IDLE;

/**
 * 记忆点控制模块初始化
 */
void MemoryPoint_Init(void)
{
    // 清空记忆点数据
    memory_point.pan_angle = 0.0f;
    memory_point.tilt_angle = 0.0f;
    memory_point.is_valid = 0;
    memory_point.timestamp = 0;
    
    // 设置初始状态
    current_state = MEMORY_STATE_IDLE;
    
    // 发送初始化完成信息
    #ifdef BLUETOOTH_ENABLED
    Bluetooth_SendMessage("MemoryPoint: Init OK");
    #endif
}

/**
 * 舵机卸载功能 (PB0按键功能)
 * 卸载两个舵机，允许手动调整
 */
MemoryResult_t MemoryPoint_UnloadServos(void)
{
    ServoError_t result1, result2;
    
    // 卸载水平舵机
    result1 = Servo_SetTorqueEnable(SERVO_PAN_ID, 0);
    Delay_ms(50);
    
    // 卸载垂直舵机  
    result2 = Servo_SetTorqueEnable(SERVO_TILT_ID, 0);
    Delay_ms(50);
    
    if (result1 == SERVO_OK && result2 == SERVO_OK) {
        current_state = MEMORY_STATE_UNLOADED;
        
        // OLED显示状态
        OLED_ShowString(1, 1, "Status: Unloaded");
        OLED_ShowString(2, 1, "Manual adjust OK");

        // 蓝牙反馈
        #ifdef BLUETOOTH_ENABLED
        Bluetooth_SendMessage("Servo unloaded, manual adjust OK");
        #endif
        
        return MEMORY_OK;
    } else {
        current_state = MEMORY_STATE_ERROR;
        
        OLED_ShowString(1, 1, "Error: Unload failed");

        #ifdef BLUETOOTH_ENABLED
        Bluetooth_SendMessage("Error: Servo unload failed");
        #endif
        
        return MEMORY_ERROR_SERVO_COMM;
    }
}

/**
 * 记录记忆点功能 (PB1按键功能)
 * 上载舵机并读取当前角度作为记忆点
 */
MemoryResult_t MemoryPoint_RecordPosition(void)
{
    ServoError_t result1, result2;
    float pan_angle, tilt_angle;
    
    current_state = MEMORY_STATE_RECORDING;
    
    // 上载水平舵机
    result1 = Servo_SetTorqueEnable(SERVO_PAN_ID, 1);
    Delay_ms(MEMORY_RECORD_DELAY_MS);
    
    // 上载垂直舵机
    result2 = Servo_SetTorqueEnable(SERVO_TILT_ID, 1);
    Delay_ms(MEMORY_RECORD_DELAY_MS);
    
    if (result1 != SERVO_OK || result2 != SERVO_OK) {
        current_state = MEMORY_STATE_ERROR;
        OLED_ShowString(1, 1, "Error: Load failed");
        return MEMORY_ERROR_SERVO_COMM;
    }
    
    // 读取当前位置
    result1 = Servo_ReadPosition(SERVO_PAN_ID, &pan_angle);
    Delay_ms(50);
    result2 = Servo_ReadPosition(SERVO_TILT_ID, &tilt_angle);
    Delay_ms(50);
    
    if (result1 == SERVO_OK && result2 == SERVO_OK) {
        // 保存记忆点
        memory_point.pan_angle = pan_angle;
        memory_point.tilt_angle = tilt_angle;
        memory_point.is_valid = 1;
        memory_point.timestamp = 0; // 简化处理
        
        current_state = MEMORY_STATE_IDLE;
        
        // OLED显示
        char buffer[50];
        OLED_ShowString(1, 1, "Memory Recorded:");
        sprintf(buffer, "Pan: %.1f deg", pan_angle);
        OLED_ShowString(2, 1, buffer);
        sprintf(buffer, "Tilt: %.1f deg", tilt_angle);
        OLED_ShowString(3, 1, buffer);

        // 蓝牙反馈
        #ifdef BLUETOOTH_ENABLED
        sprintf(buffer, "Memory recorded: Pan%.1f Tilt%.1f", pan_angle, tilt_angle);
        Bluetooth_SendMessage(buffer);
        #endif
        
        return MEMORY_OK;
    } else {
        current_state = MEMORY_STATE_ERROR;
        OLED_ShowString(1, 1, "Error: Read failed");

        #ifdef BLUETOOTH_ENABLED
        Bluetooth_SendMessage("Error: Position read failed");
        #endif
        
        return MEMORY_ERROR_SERVO_COMM;
    }
}

/**
 * 回到记忆点功能 (PB11按键功能)
 * 立刻移动到记忆点位置
 */
MemoryResult_t MemoryPoint_ReturnToMemory(void)
{
    if (!memory_point.is_valid) {
        OLED_ShowString(1, 1, "Error: No memory");

        #ifdef BLUETOOTH_ENABLED
        Bluetooth_SendMessage("Error: No valid memory point");
        #endif
        
        return MEMORY_ERROR_INVALID_POINT;
    }
    
    current_state = MEMORY_STATE_RETURNING;
    
    // 确保舵机已上载
    Servo_SetTorqueEnable(SERVO_PAN_ID, 1);
    Servo_SetTorqueEnable(SERVO_TILT_ID, 1);
    Delay_ms(50);
    
    // 同时移动到记忆点位置
    Servo_SetPositionWithTime(SERVO_PAN_ID, memory_point.pan_angle, MEMORY_RETURN_TIME_MS);
    Servo_SetPositionWithTime(SERVO_TILT_ID, memory_point.tilt_angle, MEMORY_RETURN_TIME_MS);
    
    // OLED显示
    OLED_ShowString(1, 1, "Returning...");
    char buffer[50];
    sprintf(buffer, "Target: %.1f,%.1f", memory_point.pan_angle, memory_point.tilt_angle);
    OLED_ShowString(2, 1, buffer);
    
    // 蓝牙反馈
    #ifdef BLUETOOTH_ENABLED
    sprintf(buffer, "Returning to: %.1f,%.1f", memory_point.pan_angle, memory_point.tilt_angle);
    Bluetooth_SendMessage(buffer);
    #endif

    // 等待移动完成
    Delay_ms(MEMORY_RETURN_TIME_MS + 200);

    current_state = MEMORY_STATE_IDLE;
    OLED_ShowString(1, 1, "Arrived");

    #ifdef BLUETOOTH_ENABLED
    Bluetooth_SendMessage("Arrived at memory point");
    #endif
    
    return MEMORY_OK;
}

/**
 * 获取当前状态
 */
MemoryState_t MemoryPoint_GetState(void)
{
    return current_state;
}

/**
 * 获取记忆点数据指针
 */
MemoryPoint_t* MemoryPoint_GetData(void)
{
    return &memory_point;
}

/**
 * 检查记忆点是否有效
 */
uint8_t MemoryPoint_IsValid(void)
{
    return memory_point.is_valid;
}

/**
 * 清除记忆点
 */
void MemoryPoint_ClearMemory(void)
{
    memory_point.is_valid = 0;
    memory_point.pan_angle = 0.0f;
    memory_point.tilt_angle = 0.0f;
    memory_point.timestamp = 0;
    
    OLED_ShowString(1, 1, "Memory cleared");

    #ifdef BLUETOOTH_ENABLED
    Bluetooth_SendMessage("Memory point cleared");
    #endif
}

/**
 * 状态更新函数 (在主循环中调用)
 */
void MemoryPoint_Update(void)
{
    // 这里可以添加状态监控、超时检测等逻辑
    // 当前简化实现
}

/**
 * 获取状态字符串 (调试用)
 */
const char* MemoryPoint_GetStateString(void)
{
    switch (current_state) {
        case MEMORY_STATE_IDLE:      return "Idle";
        case MEMORY_STATE_UNLOADED:  return "Unloaded";
        case MEMORY_STATE_RECORDING: return "Recording";
        case MEMORY_STATE_RETURNING: return "Returning";
        case MEMORY_STATE_ERROR:     return "Error";
        default:                     return "Unknown";
    }
}

/**
 * 获取结果字符串 (调试用)
 */
const char* MemoryPoint_GetResultString(MemoryResult_t result)
{
    switch (result) {
        case MEMORY_OK:                  return "OK";
        case MEMORY_ERROR_INVALID_POINT: return "Invalid Point";
        case MEMORY_ERROR_SERVO_COMM:    return "Servo Error";
        case MEMORY_ERROR_TIMEOUT:       return "Timeout";
        case MEMORY_ERROR_STATE:         return "State Error";
        default:                         return "Unknown Error";
    }
}

/**
 * 打印状态信息 (调试用)
 */
void MemoryPoint_PrintStatus(void)
{
    char buffer[50];
    sprintf(buffer, "State:%s Memory:%s",
            MemoryPoint_GetStateString(),
            memory_point.is_valid ? "Valid" : "Invalid");
    
    #ifdef BLUETOOTH_ENABLED
    Bluetooth_SendMessage(buffer);
    #endif
}
