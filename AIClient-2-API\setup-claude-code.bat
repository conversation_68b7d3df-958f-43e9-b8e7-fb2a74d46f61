@echo off
echo ========================================
echo    配置 Claude Code 使用 Kiro 模型
echo ========================================
echo.

echo 🔧 设置环境变量...
setx ANTHROPIC_BASE_URL "http://127.0.0.1:3000"
setx ANTHROPIC_AUTH_TOKEN "123456"

echo.
echo ✅ 环境变量设置完成！
echo.
echo 📋 配置信息:
echo    ANTHROPIC_BASE_URL = http://127.0.0.1:3000
echo    ANTHROPIC_AUTH_TOKEN = 123456
echo.
echo 📝 下一步操作:
echo    1. 确保 AIClient-2-API 服务器正在运行
echo    2. 重启 VS Code 或 Claude Code
echo    3. 开始使用 Kiro 的 Claude Sonnet 4 模型！
echo.
echo 🚀 启动服务器命令:
echo    node src/api-server.js --host 127.0.0.1 --port 3000 --model-provider claude-kiro-oauth --api-key 123456
echo.
pause
