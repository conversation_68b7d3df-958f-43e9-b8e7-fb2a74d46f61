#MicroXplorer Configuration settings - do not modify
ADC1.Channel-4\#ChannelRegularConversion=ADC_CHANNEL_VREFINT
ADC1.Channel-5\#ChannelRegularConversion=ADC_CHANNEL_8
ADC1.ClockPrescaler=ADC_CLOCK_SYNC_PCLK_DIV8
ADC1.ContinuousConvMode=DISABLE
ADC1.EOCSelection=ADC_EOC_SEQ_CONV
ADC1.IPParameters=Rank-4\#ChannelRegularConversion,master,Channel-4\#ChannelRegularConversion,SamplingTime-4\#ChannelRegularConversion,NbrOfConversionFlag,ClockPrescaler,ContinuousConvMode,Rank-5\#ChannelRegularConversion,Channel-5\#ChannelRegularConversion,SamplingTime-5\#ChannelRegularConversion,NbrOfConversion,EOCSelection
ADC1.NbrOfConversion=2
ADC1.NbrOfConversionFlag=1
ADC1.Rank-4\#ChannelRegularConversion=1
ADC1.Rank-5\#ChannelRegularConversion=2
ADC1.SamplingTime-4\#ChannelRegularConversion=ADC_SAMPLETIME_480CYCLES
ADC1.SamplingTime-5\#ChannelRegularConversion=ADC_SAMPLETIME_480CYCLES
ADC1.master=1
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.ADC1.7.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC1.7.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC1.7.Instance=DMA2_Stream0
Dma.ADC1.7.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.ADC1.7.MemInc=DMA_MINC_ENABLE
Dma.ADC1.7.Mode=DMA_CIRCULAR
Dma.ADC1.7.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.ADC1.7.PeriphInc=DMA_PINC_DISABLE
Dma.ADC1.7.Priority=DMA_PRIORITY_LOW
Dma.ADC1.7.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.I2C2_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.I2C2_RX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.I2C2_RX.0.Instance=DMA1_Stream2
Dma.I2C2_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.I2C2_RX.0.MemInc=DMA_MINC_ENABLE
Dma.I2C2_RX.0.Mode=DMA_NORMAL
Dma.I2C2_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.I2C2_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.I2C2_RX.0.Priority=DMA_PRIORITY_VERY_HIGH
Dma.I2C2_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.I2C2_TX.1.Direction=DMA_MEMORY_TO_PERIPH
Dma.I2C2_TX.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.I2C2_TX.1.Instance=DMA1_Stream7
Dma.I2C2_TX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.I2C2_TX.1.MemInc=DMA_MINC_ENABLE
Dma.I2C2_TX.1.Mode=DMA_NORMAL
Dma.I2C2_TX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.I2C2_TX.1.PeriphInc=DMA_PINC_DISABLE
Dma.I2C2_TX.1.Priority=DMA_PRIORITY_VERY_HIGH
Dma.I2C2_TX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.Request0=I2C2_RX
Dma.Request1=I2C2_TX
Dma.Request2=USART3_RX
Dma.Request3=USART3_TX
Dma.Request4=SPI2_TX
Dma.Request5=USART2_RX
Dma.Request6=USART2_TX
Dma.Request7=ADC1
Dma.Request8=UART5_RX
Dma.RequestsNb=9
Dma.SPI2_TX.4.Direction=DMA_MEMORY_TO_PERIPH
Dma.SPI2_TX.4.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.SPI2_TX.4.Instance=DMA1_Stream4
Dma.SPI2_TX.4.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.SPI2_TX.4.MemInc=DMA_MINC_ENABLE
Dma.SPI2_TX.4.Mode=DMA_NORMAL
Dma.SPI2_TX.4.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.SPI2_TX.4.PeriphInc=DMA_PINC_DISABLE
Dma.SPI2_TX.4.Priority=DMA_PRIORITY_LOW
Dma.SPI2_TX.4.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.UART5_RX.8.Direction=DMA_PERIPH_TO_MEMORY
Dma.UART5_RX.8.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.UART5_RX.8.Instance=DMA1_Stream0
Dma.UART5_RX.8.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART5_RX.8.MemInc=DMA_MINC_ENABLE
Dma.UART5_RX.8.Mode=DMA_NORMAL
Dma.UART5_RX.8.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART5_RX.8.PeriphInc=DMA_PINC_DISABLE
Dma.UART5_RX.8.Priority=DMA_PRIORITY_MEDIUM
Dma.UART5_RX.8.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART2_RX.5.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART2_RX.5.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART2_RX.5.Instance=DMA1_Stream5
Dma.USART2_RX.5.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART2_RX.5.MemInc=DMA_MINC_ENABLE
Dma.USART2_RX.5.Mode=DMA_NORMAL
Dma.USART2_RX.5.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART2_RX.5.PeriphInc=DMA_PINC_DISABLE
Dma.USART2_RX.5.Priority=DMA_PRIORITY_MEDIUM
Dma.USART2_RX.5.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART2_TX.6.Direction=DMA_MEMORY_TO_PERIPH
Dma.USART2_TX.6.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART2_TX.6.Instance=DMA1_Stream6
Dma.USART2_TX.6.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART2_TX.6.MemInc=DMA_MINC_ENABLE
Dma.USART2_TX.6.Mode=DMA_NORMAL
Dma.USART2_TX.6.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART2_TX.6.PeriphInc=DMA_PINC_DISABLE
Dma.USART2_TX.6.Priority=DMA_PRIORITY_MEDIUM
Dma.USART2_TX.6.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART3_RX.2.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART3_RX.2.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART3_RX.2.Instance=DMA1_Stream1
Dma.USART3_RX.2.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART3_RX.2.MemInc=DMA_MINC_ENABLE
Dma.USART3_RX.2.Mode=DMA_CIRCULAR
Dma.USART3_RX.2.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART3_RX.2.PeriphInc=DMA_PINC_DISABLE
Dma.USART3_RX.2.Priority=DMA_PRIORITY_HIGH
Dma.USART3_RX.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART3_TX.3.Direction=DMA_MEMORY_TO_PERIPH
Dma.USART3_TX.3.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART3_TX.3.Instance=DMA1_Stream3
Dma.USART3_TX.3.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART3_TX.3.MemInc=DMA_MINC_ENABLE
Dma.USART3_TX.3.Mode=DMA_NORMAL
Dma.USART3_TX.3.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART3_TX.3.PeriphInc=DMA_PINC_DISABLE
Dma.USART3_TX.3.Priority=DMA_PRIORITY_HIGH
Dma.USART3_TX.3.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
FREERTOS.BinarySemaphores01=packet_tx_idle,Static,packet_tx_ControlBlock;packet_rx_not_empty,Static,packet_rx_not_emptyControlBlock;mpu6050_data_ready,Static,mpu6050_data_readyControlBlock;sbus_data_ready_01_,Static,sbus_data_ready_01_ControlBlock;spi_tx_finished,Static,spi_tx_finishedControlBlock;bluetooth_tx_idle,Static,bluetooth_tx_idleControlBlock;serial_servo_rx_complete,Static,serial_servo_rx_completeControlBlock
FREERTOS.Events01=sbus_data_ready_event_,Static,sbus_data_ready_event_ControlBlock
FREERTOS.FootprintOK=true
FREERTOS.INCLUDE_vTaskCleanUpResources=1
FREERTOS.INCLUDE_xEventGroupSetBitFromISR=1
FREERTOS.IPParameters=Tasks01,Queues01,FootprintOK,Timers01,configUSE_NEWLIB_REENTRANT,configENABLE_FPU,BinarySemaphores01,configUSE_IDLE_HOOK,configUSE_TICK_HOOK,configTIMER_TASK_STACK_DEPTH,configTIMER_QUEUE_LENGTH,Events01,configTOTAL_HEAP_SIZE,INCLUDE_vTaskCleanUpResources,INCLUDE_xEventGroupSetBitFromISR
FREERTOS.Queues01=packet_tx_queue,64,void*,0,Static,packet_tx_queueBuffer,packet_tx_queueControlBlock;lvgl_event_queue,16,32,1,Dynamic,NULL,NULL;moving_ctrl_queue,32,char,0,Static,moving_ctrl_queueBuffer,moving_ctrl_queueControlBlock;bluetooth_tx_queue,8,8,1,Dynamic,NULL,NULL
FREERTOS.Tasks01=defaultTask,16,256,StartDefaultTask,Default,NULL,Dynamic,NULL,NULL;imu_task,32,256,imu_task_entry,As weak,NULL,Dynamic,NULL,NULL;packet_tx_task,24,256,packet_tx_task_entry,As weak,NULL,Dynamic,NULL,NULL;packet_rx_task,24,256,packet_rx_task_entry,As weak,NULL,Dynamic,NULL,NULL;sbus_rx_task,24,256,sbus_rx_task_entry,As weak,NULL,Dynamic,NULL,NULL;gui_task,8,1500,gui_task_entry,As weak,NULL,Dynamic,NULL,NULL;app_task,24,512,app_task_entry,As weak,NULL,Dynamic,NULL,NULL;bluetooth_task,24,128,bluetooth_task_entry,As weak,NULL,Dynamic,NULL,NULL
FREERTOS.Timers01=button_timer,button_timer_callback,osTimerPeriodic,As weak,NULL,Static,button_timerControlBlock;led_timer,led_timer_callback,osTimerPeriodic,As weak,NULL,Static,led_timerControlBlock;lvgl_timer,lvgl_timer_callback,osTimerPeriodic,As weak,NULL,Static,lvgl_timerControlBlock;buzzer_timer,buzzer_timer_callback,osTimerPeriodic,As weak,NULL,Static,buzzer_timerControlBlock;battery_check_timer,battery_check_timer_callback,osTimerPeriodic,As weak,NULL,Static,battery_check_timerControlBlock
FREERTOS.configENABLE_FPU=1
FREERTOS.configTIMER_QUEUE_LENGTH=10
FREERTOS.configTIMER_TASK_STACK_DEPTH=6144
FREERTOS.configTOTAL_HEAP_SIZE=20480
FREERTOS.configUSE_IDLE_HOOK=1
FREERTOS.configUSE_NEWLIB_REENTRANT=0
FREERTOS.configUSE_TICK_HOOK=1
File.Version=6
GPIO.groupedBy=Group By Peripherals
I2C2.DutyCycle=I2C_DUTYCYCLE_16_9
I2C2.I2C_Mode=I2C_Fast
I2C2.IPParameters=I2C_Mode,DutyCycle
KeepUserPlacement=false
Mcu.CPN=STM32F407VET6
Mcu.Family=STM32F4
Mcu.IP0=ADC1
Mcu.IP1=CRC
Mcu.IP10=TIM2
Mcu.IP11=TIM3
Mcu.IP12=TIM4
Mcu.IP13=TIM5
Mcu.IP14=TIM7
Mcu.IP15=TIM9
Mcu.IP16=TIM10
Mcu.IP17=TIM11
Mcu.IP18=TIM12
Mcu.IP19=TIM13
Mcu.IP2=DMA
Mcu.IP20=UART5
Mcu.IP21=USART1
Mcu.IP22=USART2
Mcu.IP23=USART3
Mcu.IP24=USART6
Mcu.IP25=USB_HOST
Mcu.IP26=USB_OTG_HS
Mcu.IP3=FREERTOS
Mcu.IP4=I2C2
Mcu.IP5=NVIC
Mcu.IP6=RCC
Mcu.IP7=SPI2
Mcu.IP8=SYS
Mcu.IP9=TIM1
Mcu.IPNb=27
Mcu.Name=STM32F407V(E-G)Tx
Mcu.Package=LQFP100
Mcu.Pin0=PE5
Mcu.Pin1=PE6
Mcu.Pin10=PE9
Mcu.Pin11=PE10
Mcu.Pin12=PE11
Mcu.Pin13=PE13
Mcu.Pin14=PE14
Mcu.Pin15=PB10
Mcu.Pin16=PB11
Mcu.Pin17=PB12
Mcu.Pin18=PB13
Mcu.Pin19=PB14
Mcu.Pin2=PH0-OSC_IN
Mcu.Pin20=PB15
Mcu.Pin21=PD8
Mcu.Pin22=PD9
Mcu.Pin23=PD11
Mcu.Pin24=PD12
Mcu.Pin25=PD13
Mcu.Pin26=PD14
Mcu.Pin27=PC6
Mcu.Pin28=PC7
Mcu.Pin29=PC8
Mcu.Pin3=PH1-OSC_OUT
Mcu.Pin30=PC9
Mcu.Pin31=PA8
Mcu.Pin32=PA9
Mcu.Pin33=PA10
Mcu.Pin34=PA11
Mcu.Pin35=PA12
Mcu.Pin36=PA13
Mcu.Pin37=PA14
Mcu.Pin38=PA15
Mcu.Pin39=PC12
Mcu.Pin4=PC3
Mcu.Pin40=PD2
Mcu.Pin41=PD3
Mcu.Pin42=PD5
Mcu.Pin43=PD6
Mcu.Pin44=PB3
Mcu.Pin45=PB4
Mcu.Pin46=PB5
Mcu.Pin47=PB6
Mcu.Pin48=PB7
Mcu.Pin49=PB8
Mcu.Pin5=PA0-WKUP
Mcu.Pin50=PB9
Mcu.Pin51=PE0
Mcu.Pin52=PE1
Mcu.Pin53=VP_ADC1_Vref_Input
Mcu.Pin54=VP_CRC_VS_CRC
Mcu.Pin55=VP_FREERTOS_VS_CMSIS_V2
Mcu.Pin56=VP_SYS_VS_tim14
Mcu.Pin57=VP_TIM1_VS_ClockSourceINT
Mcu.Pin58=VP_TIM7_VS_ClockSourceINT
Mcu.Pin59=VP_TIM9_VS_ClockSourceINT
Mcu.Pin6=PA1
Mcu.Pin60=VP_TIM10_VS_ClockSourceINT
Mcu.Pin61=VP_TIM11_VS_ClockSourceINT
Mcu.Pin62=VP_TIM12_VS_ClockSourceINT
Mcu.Pin63=VP_TIM12_VS_no_output1
Mcu.Pin64=VP_TIM13_VS_ClockSourceINT
Mcu.Pin65=VP_TIM13_VS_no_output1
Mcu.Pin66=VP_USB_HOST_VS_USB_HOST_HID_HS
Mcu.Pin7=PB0
Mcu.Pin8=PE7
Mcu.Pin9=PE8
Mcu.PinsNb=67
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407VETx
MxCube.Version=6.8.1
MxDb.Version=DB.6.0.81
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.DMA1_Stream0_IRQn=true\:5\:0\:false\:true\:true\:1\:true\:true\:true\:true
NVIC.DMA1_Stream1_IRQn=true\:5\:0\:false\:true\:true\:2\:true\:false\:true\:true
NVIC.DMA1_Stream2_IRQn=true\:5\:0\:false\:true\:true\:3\:true\:false\:true\:true
NVIC.DMA1_Stream3_IRQn=true\:5\:0\:false\:true\:true\:4\:true\:false\:true\:true
NVIC.DMA1_Stream4_IRQn=true\:5\:0\:false\:true\:true\:5\:true\:true\:true\:true
NVIC.DMA1_Stream5_IRQn=true\:5\:0\:false\:true\:true\:6\:true\:true\:true\:true
NVIC.DMA1_Stream6_IRQn=true\:5\:0\:false\:true\:true\:7\:true\:true\:true\:true
NVIC.DMA1_Stream7_IRQn=true\:5\:0\:false\:true\:true\:16\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.EXTI15_10_IRQn=true\:5\:0\:false\:true\:true\:13\:true\:true\:false\:true
NVIC.ForceEnableDMAVector=false
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:true\:false
NVIC.OTG_HS_IRQn=true\:5\:0\:false\:true\:true\:21\:true\:false\:true\:true
NVIC.PendSV_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:false\:false\:false\:false\:false
NVIC.SavedPendsvIrqHandlerGenerated=true
NVIC.SavedSvcallIrqHandlerGenerated=true
NVIC.SavedSystickIrqHandlerGenerated=true
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:true\:false
NVIC.TIM2_IRQn=true\:5\:0\:false\:true\:true\:8\:true\:true\:false\:true
NVIC.TIM3_IRQn=true\:5\:0\:false\:true\:true\:9\:true\:true\:false\:true
NVIC.TIM4_IRQn=true\:5\:0\:false\:true\:true\:10\:true\:true\:false\:true
NVIC.TIM5_IRQn=true\:5\:0\:false\:true\:true\:17\:true\:true\:false\:true
NVIC.TIM7_IRQn=true\:5\:0\:false\:true\:true\:19\:true\:true\:false\:true
NVIC.TIM8_BRK_TIM12_IRQn=true\:5\:0\:false\:true\:true\:14\:true\:true\:false\:true
NVIC.TIM8_TRG_COM_TIM14_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:true\:true
NVIC.TIM8_UP_TIM13_IRQn=true\:5\:0\:false\:true\:true\:15\:true\:true\:false\:true
NVIC.TimeBase=TIM8_TRG_COM_TIM14_IRQn
NVIC.TimeBaseIP=TIM14
NVIC.UART5_IRQn=true\:5\:0\:true\:true\:true\:18\:true\:true\:true\:true
NVIC.USART2_IRQn=true\:5\:0\:false\:true\:true\:11\:true\:true\:true\:true
NVIC.USART3_IRQn=true\:5\:0\:false\:true\:true\:12\:true\:true\:true\:true
NVIC.USART6_IRQn=true\:5\:0\:false\:true\:true\:20\:true\:true\:false\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
PA0-WKUP.GPIOParameters=GPIO_Speed,GPIO_PuPd
PA0-WKUP.GPIO_PuPd=GPIO_PULLUP
PA0-WKUP.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PA0-WKUP.Locked=true
PA0-WKUP.Signal=S_TIM5_CH1
PA1.GPIOParameters=GPIO_Speed,GPIO_PuPd
PA1.GPIO_PuPd=GPIO_PULLUP
PA1.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PA1.Locked=true
PA1.Signal=S_TIM5_CH2
PA10.GPIOParameters=GPIO_Label
PA10.GPIO_Label=DBG_RX
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA11.GPIOParameters=GPIO_Speed,PinState,GPIO_Label
PA11.GPIO_Label=PWM_SERVO_1
PA11.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA11.Locked=true
PA11.PinState=GPIO_PIN_RESET
PA11.Signal=GPIO_Output
PA12.GPIOParameters=GPIO_Speed,GPIO_Label
PA12.GPIO_Label=PWM_SERVO_2
PA12.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA12.Locked=true
PA12.Signal=GPIO_Output
PA13.Locked=true
PA13.Signal=SYS_JTMS-SWDIO
PA14.Locked=true
PA14.Signal=SYS_JTCK-SWCLK
PA15.GPIOParameters=GPIO_Speed,GPIO_PuPd
PA15.GPIO_PuPd=GPIO_PULLUP
PA15.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PA15.Locked=true
PA15.Signal=S_TIM2_CH1_ETR
PA8.GPIOParameters=GPIO_Speed,PinState,GPIO_Label
PA8.GPIO_Label=BUZZER
PA8.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PA8.Locked=true
PA8.PinState=GPIO_PIN_RESET
PA8.Signal=GPIO_Output
PA9.GPIOParameters=GPIO_Label
PA9.GPIO_Label=DBG_TX
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB0.GPIOParameters=GPIO_Label
PB0.GPIO_Label=BATTERY
PB0.Locked=true
PB0.Signal=ADCx_IN8
PB10.GPIOParameters=GPIO_Pu
PB10.GPIO_Pu=GPIO_PULLUP
PB10.Locked=true
PB10.Mode=I2C
PB10.Signal=I2C2_SCL
PB11.GPIOParameters=GPIO_Pu
PB11.GPIO_Pu=GPIO_PULLUP
PB11.Locked=true
PB11.Mode=I2C
PB11.Signal=I2C2_SDA
PB12.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PB12.GPIO_Label=IMU_ITR
PB12.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_RISING
PB12.GPIO_PuPd=GPIO_PULLDOWN
PB12.Locked=true
PB12.Signal=GPXTI12
PB13.Locked=true
PB13.Mode=TX_Only_Simplex_Unidirect_Master
PB13.Signal=SPI2_SCK
PB14.Locked=true
PB14.Mode=Host_FS
PB14.Signal=USB_OTG_HS_DM
PB15.Locked=true
PB15.Mode=Host_FS
PB15.Signal=USB_OTG_HS_DP
PB3.GPIOParameters=GPIO_Speed,GPIO_PuPd
PB3.GPIO_PuPd=GPIO_PULLUP
PB3.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB3.Locked=true
PB3.Signal=S_TIM2_CH2
PB4.GPIOParameters=GPIO_Speed
PB4.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB4.Signal=S_TIM3_CH1
PB5.GPIOParameters=GPIO_Speed
PB5.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB5.Locked=true
PB5.Signal=S_TIM3_CH2
PB6.Signal=S_TIM4_CH1
PB7.Signal=S_TIM4_CH2
PB8.Signal=S_TIM10_CH1
PB9.GPIOParameters=GPIO_Speed
PB9.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB9.Signal=S_TIM11_CH1
PC12.Mode=Asynchronous
PC12.Signal=UART5_TX
PC3.Locked=true
PC3.Mode=TX_Only_Simplex_Unidirect_Master
PC3.Signal=SPI2_MOSI
PC6.GPIOParameters=GPIO_PuPd,GPIO_Label
PC6.GPIO_Label=SERIAL_SERVO_TX
PC6.GPIO_PuPd=GPIO_PULLUP
PC6.Locked=true
PC6.Mode=Asynchronous
PC6.Signal=USART6_TX
PC7.GPIOParameters=GPIO_PuPd,GPIO_Label
PC7.GPIO_Label=SERIAL_SERVO_RX
PC7.GPIO_PuPd=GPIO_PULLUP
PC7.Locked=true
PC7.Mode=Asynchronous
PC7.Signal=USART6_RX
PC8.GPIOParameters=GPIO_Speed,PinState,GPIO_Label
PC8.GPIO_Label=PWM_SERVO_3
PC8.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PC8.Locked=true
PC8.PinState=GPIO_PIN_SET
PC8.Signal=GPIO_Output
PC9.GPIOParameters=GPIO_Speed,GPIO_Label
PC9.GPIO_Label=PWM_SERVO_4
PC9.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PC9.Locked=true
PC9.Signal=GPIO_Output
PD11.GPIOParameters=GPIO_Speed,PinState,GPIO_Label
PD11.GPIO_Label=LCD_BLK
PD11.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PD11.Locked=true
PD11.PinState=GPIO_PIN_SET
PD11.Signal=GPIO_Output
PD12.GPIOParameters=GPIO_Speed,PinState,GPIO_Label
PD12.GPIO_Label=LCD_CS
PD12.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PD12.Locked=true
PD12.PinState=GPIO_PIN_SET
PD12.Signal=GPIO_Output
PD13.GPIOParameters=GPIO_Speed,GPIO_Label
PD13.GPIO_Label=LCD_DC
PD13.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PD13.Locked=true
PD13.Signal=GPIO_Output
PD14.GPIOParameters=GPIO_Speed,PinState,GPIO_Label
PD14.GPIO_Label=LCD_RES
PD14.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PD14.Locked=true
PD14.PinState=GPIO_PIN_SET
PD14.Signal=GPIO_Output
PD2.GPIOParameters=GPIO_Label
PD2.GPIO_Label=SBUS_RX
PD2.Locked=true
PD2.Mode=Asynchronous
PD2.Signal=UART5_RX
PD3.GPIOParameters=GPIO_Label
PD3.GPIO_Label=MOTOR_ENABLE
PD3.Locked=true
PD3.Signal=GPIO_Input
PD5.GPIOParameters=GPIO_Label
PD5.GPIO_Label=BLE_TX
PD5.Locked=true
PD5.Mode=Asynchronous
PD5.Signal=USART2_TX
PD6.GPIOParameters=GPIO_Label
PD6.GPIO_Label=BLE_RX
PD6.Locked=true
PD6.Mode=Asynchronous
PD6.Signal=USART2_RX
PD8.GPIOParameters=GPIO_PuPd,GPIO_Label
PD8.GPIO_Label=MASTER_TX
PD8.GPIO_PuPd=GPIO_NOPULL
PD8.Mode=Asynchronous
PD8.Signal=USART3_TX
PD9.GPIOParameters=GPIO_PuPd,GPIO_Label
PD9.GPIO_Label=MASTER_RX
PD9.GPIO_PuPd=GPIO_NOPULL
PD9.Mode=Asynchronous
PD9.Signal=USART3_RX
PE0.GPIOParameters=GPIO_Label
PE0.GPIO_Label=KEY2
PE0.Locked=true
PE0.Signal=GPIO_Input
PE1.GPIOParameters=GPIO_Label
PE1.GPIO_Label=KEY1
PE1.Locked=true
PE1.Signal=GPIO_Input
PE10.GPIOParameters=GPIO_Speed,PinState,GPIO_Label
PE10.GPIO_Label=LED_SYS
PE10.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PE10.Locked=true
PE10.PinState=GPIO_PIN_RESET
PE10.Signal=GPIO_Output
PE11.GPIOParameters=GPIO_Speed,GPIO_Label
PE11.GPIO_Label=TIM1_CH2
PE11.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PE11.Signal=S_TIM1_CH2
PE13.GPIOParameters=GPIO_Speed,GPIO_Label
PE13.GPIO_Label=TIM1_CH3
PE13.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PE13.Signal=S_TIM1_CH3
PE14.GPIOParameters=GPIO_Speed,GPIO_Label
PE14.GPIO_Label=TIM1_CH4
PE14.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PE14.Signal=S_TIM1_CH4
PE5.Signal=S_TIM9_CH1
PE6.Locked=true
PE6.Signal=S_TIM9_CH2
PE7.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label
PE7.GPIO_Label=SERIAL_SERVO_TX_EN
PE7.GPIO_PuPd=GPIO_NOPULL
PE7.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PE7.Locked=true
PE7.PinState=GPIO_PIN_SET
PE7.Signal=GPIO_Output
PE8.GPIOParameters=GPIO_Speed,PinState,GPIO_Label
PE8.GPIO_Label=SERIAL_SERVO_RX_EN
PE8.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PE8.Locked=true
PE8.PinState=GPIO_PIN_SET
PE8.Signal=GPIO_Output
PE9.GPIOParameters=GPIO_Speed,GPIO_Label
PE9.GPIO_Label=TIM1_CH1
PE9.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PE9.Signal=S_TIM1_CH1
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=true
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407VETx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.27.1
ProjectManager.FreePins=true
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=0
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=STM32CubeIDE
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=RosRobotControllerM4.ioc
ProjectManager.ProjectName=RosRobotControllerM4
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=SPI,TIM,UART,USART
ProjectManager.StackSize=0x600
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.TemplateDestinationPath=D\:\\Mobile_Robot_Controller_Firmware
ProjectManager.TemplateSourcePath=C\:\\Program Files\\STMicroelectronics\\STM32Cube\\STM32CubeMX\\db\\extra_templates
ProjectManager.ToolChainLocation=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-MX_GPIO_Init-GPIO-false-HAL-true,2-MX_DMA_Init-DMA-false-HAL-true,3-SystemClock_Config-RCC-false-HAL-false,4-MX_USART1_UART_Init-USART1-false-HAL-true,5-MX_SPI2_Init-SPI2-false-HAL-true,6-MX_TIM1_Init-TIM1-false-HAL-true,7-MX_TIM2_Init-TIM2-false-HAL-true,8-MX_TIM5_Init-TIM5-false-HAL-true,9-MX_TIM9_Init-TIM9-false-HAL-true,10-MX_TIM10_Init-TIM10-false-HAL-true,11-MX_TIM11_Init-TIM11-false-HAL-true,12-MX_I2C2_Init-I2C2-false-HAL-true,13-MX_UART5_Init-UART5-false-HAL-true,14-MX_USART2_UART_Init-USART2-false-HAL-true,15-MX_USART3_UART_Init-USART3-false-HAL-true,16-MX_USART6_UART_Init-USART6-false-HAL-true,17-MX_TIM7_Init-TIM7-false-HAL-true,18-MX_TIM3_Init-TIM3-false-HAL-true,19-MX_TIM4_Init-TIM4-false-HAL-true,20-MX_TIM13_Init-TIM13-false-HAL-true,21-MX_CRC_Init-CRC-false-HAL-true,22-MX_USB_HOST_Init-USB_HOST-false-HAL-false,23-MX_TIM12_Init-TIM12-false-HAL-true,24-MX_ADC1_Init-ADC1-false-HAL-true
RCC.48MHZClocksFreq_Value=48000000
RCC.AHBFreq_Value=168000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=42000000
RCC.APB1TimFreq_Value=84000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=84000000
RCC.APB2TimFreq_Value=168000000
RCC.CortexFreq_Value=168000000
RCC.EnbaleCSS=true
RCC.EthernetFreq_Value=168000000
RCC.FCLKCortexFreq_Value=168000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=8000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=192000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EnbaleCSS,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSE_VALUE,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQ,PLLQCLKFreq_Value,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=168000000
RCC.PLLCLKFreq_Value=168000000
RCC.PLLM=4
RCC.PLLN=168
RCC.PLLQ=7
RCC.PLLQCLKFreq_Value=48000000
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=4000000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=384000000
RCC.VCOInputFreq_Value=2000000
RCC.VCOOutputFreq_Value=336000000
RCC.VcooutputI2S=192000000
SH.ADCx_IN8.0=ADC1_IN8,IN8
SH.ADCx_IN8.ConfNb=1
SH.GPXTI12.0=GPIO_EXTI12
SH.GPXTI12.ConfNb=1
SH.S_TIM10_CH1.0=TIM10_CH1,PWM Generation1 CH1
SH.S_TIM10_CH1.ConfNb=1
SH.S_TIM11_CH1.0=TIM11_CH1,PWM Generation1 CH1
SH.S_TIM11_CH1.ConfNb=1
SH.S_TIM1_CH1.0=TIM1_CH1,PWM Generation1 CH1
SH.S_TIM1_CH1.ConfNb=1
SH.S_TIM1_CH2.0=TIM1_CH2,PWM Generation2 CH2
SH.S_TIM1_CH2.ConfNb=1
SH.S_TIM1_CH3.0=TIM1_CH3,PWM Generation3 CH3
SH.S_TIM1_CH3.ConfNb=1
SH.S_TIM1_CH4.0=TIM1_CH4,PWM Generation4 CH4
SH.S_TIM1_CH4.ConfNb=1
SH.S_TIM2_CH1_ETR.0=TIM2_CH1,Encoder_Interface
SH.S_TIM2_CH1_ETR.ConfNb=1
SH.S_TIM2_CH2.0=TIM2_CH2,Encoder_Interface
SH.S_TIM2_CH2.ConfNb=1
SH.S_TIM3_CH1.0=TIM3_CH1,Encoder_Interface
SH.S_TIM3_CH1.ConfNb=1
SH.S_TIM3_CH2.0=TIM3_CH2,Encoder_Interface
SH.S_TIM3_CH2.ConfNb=1
SH.S_TIM4_CH1.0=TIM4_CH1,Encoder_Interface
SH.S_TIM4_CH1.ConfNb=1
SH.S_TIM4_CH2.0=TIM4_CH2,Encoder_Interface
SH.S_TIM4_CH2.ConfNb=1
SH.S_TIM5_CH1.0=TIM5_CH1,Encoder_Interface
SH.S_TIM5_CH1.ConfNb=1
SH.S_TIM5_CH2.0=TIM5_CH2,Encoder_Interface
SH.S_TIM5_CH2.ConfNb=1
SH.S_TIM9_CH1.0=TIM9_CH1,PWM Generation1 CH1
SH.S_TIM9_CH1.ConfNb=1
SH.S_TIM9_CH2.0=TIM9_CH2,PWM Generation2 CH2
SH.S_TIM9_CH2.ConfNb=1
SPI2.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_2
SPI2.CLKPhase=SPI_PHASE_1EDGE
SPI2.CLKPolarity=SPI_POLARITY_LOW
SPI2.CalculateBaudRate=21.0 MBits/s
SPI2.DataSize=SPI_DATASIZE_8BIT
SPI2.Direction=SPI_DIRECTION_2LINES
SPI2.FirstBit=SPI_FIRSTBIT_MSB
SPI2.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate,CLKPolarity,CLKPhase,BaudRatePrescaler,DataSize,FirstBit
SPI2.Mode=SPI_MODE_MASTER
SPI2.VirtualType=VM_MASTER
TIM1.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM1.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM1.Channel-PWM\ Generation2\ CH2=TIM_CHANNEL_2
TIM1.Channel-PWM\ Generation3\ CH3=TIM_CHANNEL_3
TIM1.Channel-PWM\ Generation4\ CH4=TIM_CHANNEL_4
TIM1.IPParameters=Prescaler,Period,AutoReloadPreload,Channel-PWM Generation1 CH1,Channel-PWM Generation2 CH2,Channel-PWM Generation3 CH3,Channel-PWM Generation4 CH4,Pulse-PWM Generation1 CH1,OCFastMode_PWM-PWM Generation1 CH1,Pulse-PWM Generation2 CH2,OCFastMode_PWM-PWM Generation2 CH2,Pulse-PWM Generation3 CH3,OCFastMode_PWM-PWM Generation3 CH3,Pulse-PWM Generation4 CH4,OCFastMode_PWM-PWM Generation4 CH4
TIM1.OCFastMode_PWM-PWM\ Generation1\ CH1=TIM_OCFAST_ENABLE
TIM1.OCFastMode_PWM-PWM\ Generation2\ CH2=TIM_OCFAST_ENABLE
TIM1.OCFastMode_PWM-PWM\ Generation3\ CH3=TIM_OCFAST_ENABLE
TIM1.OCFastMode_PWM-PWM\ Generation4\ CH4=TIM_OCFAST_ENABLE
TIM1.Period=999
TIM1.Prescaler=839
TIM1.Pulse-PWM\ Generation1\ CH1=0
TIM1.Pulse-PWM\ Generation2\ CH2=0
TIM1.Pulse-PWM\ Generation3\ CH3=0
TIM1.Pulse-PWM\ Generation4\ CH4=0
TIM10.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM10.Channel=TIM_CHANNEL_1
TIM10.IPParameters=Channel,Prescaler,Period,OCFastMode_PWM,AutoReloadPreload
TIM10.OCFastMode_PWM=TIM_OCFAST_ENABLE
TIM10.Period=999
TIM10.Prescaler=839
TIM11.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM11.Channel=TIM_CHANNEL_1
TIM11.IPParameters=Channel,Prescaler,Period,AutoReloadPreload,Pulse,OCFastMode_PWM
TIM11.OCFastMode_PWM=TIM_OCFAST_ENABLE
TIM11.Period=999
TIM11.Prescaler=839
TIM11.Pulse=0
TIM12.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM12.Channel-Output\ Compare1\ No\ Output=TIM_CHANNEL_1
TIM12.IPParameters=Prescaler,Period,AutoReloadPreload,Channel-Output Compare1 No Output,OC1Preload,OCMode_1,Pulse-Output Compare1 No Output
TIM12.OC1Preload=ENABLE
TIM12.OCMode_1=TIM_OCMODE_TIMING
TIM12.Period=99
TIM12.Prescaler=839
TIM12.Pulse-Output\ Compare1\ No\ Output=50
TIM13.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM13.Channel=TIM_CHANNEL_1
TIM13.IPParameters=Prescaler,Period,AutoReloadPreload,Channel,OC1Preload,Pulse
TIM13.OC1Preload=ENABLE
TIM13.Period=4999
TIM13.Prescaler=83
TIM13.Pulse=0
TIM2.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM2.EncoderMode=TIM_ENCODERMODE_TI12
TIM2.IPParameters=EncoderMode,AutoReloadPreload,Period
TIM2.Period=60000
TIM3.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM3.EncoderMode=TIM_ENCODERMODE_TI12
TIM3.IPParameters=Period,AutoReloadPreload,EncoderMode
TIM3.Period=60000
TIM4.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM4.EncoderMode=TIM_ENCODERMODE_TI12
TIM4.IPParameters=Period,AutoReloadPreload,EncoderMode
TIM4.Period=60000
TIM5.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM5.EncoderMode=TIM_ENCODERMODE_TI12
TIM5.IPParameters=EncoderMode,Period,AutoReloadPreload
TIM5.Period=60000
TIM7.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM7.IPParameters=Prescaler,Period,AutoReloadPreload
TIM7.Period=9999
TIM7.Prescaler=83
TIM9.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM9.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM9.Channel-PWM\ Generation2\ CH2=TIM_CHANNEL_2
TIM9.IPParameters=Prescaler,Period,Channel-PWM Generation1 CH1,OCFastMode_PWM-PWM Generation1 CH1,AutoReloadPreload,Channel-PWM Generation2 CH2,OCFastMode_PWM-PWM Generation2 CH2
TIM9.OCFastMode_PWM-PWM\ Generation1\ CH1=TIM_OCFAST_ENABLE
TIM9.OCFastMode_PWM-PWM\ Generation2\ CH2=TIM_OCFAST_ENABLE
TIM9.Period=999
TIM9.Prescaler=839
UART5.BaudRate=100000
UART5.IPParameters=VirtualMode,Mode,BaudRate,Parity,StopBits,WordLength
UART5.Mode=MODE_RX
UART5.Parity=PARITY_EVEN
UART5.StopBits=UART_STOPBITS_2
UART5.VirtualMode=Asynchronous
UART5.WordLength=WORDLENGTH_9B
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
USART2.BaudRate=9600
USART2.IPParameters=VirtualMode,BaudRate
USART2.VirtualMode=VM_ASYNC
USART3.BaudRate=1000000
USART3.IPParameters=VirtualMode,BaudRate
USART3.VirtualMode=VM_ASYNC
USART6.IPParameters=VirtualMode
USART6.VirtualMode=VM_ASYNC
USB_HOST.IPParameters=USBH_HandleTypeDef-HID_HS,VirtualModeHS,USBH_DEBUG_LEVEL-HID_HS,USBH_PROCESS_PRIO-HID_HS,USBH_PROCESS_STACK_SIZE-HID_HS,USBH_MAX_NUM_ENDPOINTS-HID_HS,USBH_MAX_SIZE_CONFIGURATION-HID_HS,USBH_MAX_DATA_BUFFER-HID_HS,USBH_MAX_NUM_CONFIGURATION-HID_HS,USBH_MAX_NUM_SUPPORTED_CLASS-HID_HS,USBH_MAX_NUM_INTERFACES-HID_HS,USBH_KEEP_CFG_DESCRIPTOR-HID_HS
USB_HOST.USBH_DEBUG_LEVEL-HID_HS=0
USB_HOST.USBH_HandleTypeDef-HID_HS=hUsbHostHS
USB_HOST.USBH_KEEP_CFG_DESCRIPTOR-HID_HS=1
USB_HOST.USBH_MAX_DATA_BUFFER-HID_HS=512
USB_HOST.USBH_MAX_NUM_CONFIGURATION-HID_HS=2
USB_HOST.USBH_MAX_NUM_ENDPOINTS-HID_HS=2
USB_HOST.USBH_MAX_NUM_INTERFACES-HID_HS=2
USB_HOST.USBH_MAX_NUM_SUPPORTED_CLASS-HID_HS=1
USB_HOST.USBH_MAX_SIZE_CONFIGURATION-HID_HS=512
USB_HOST.USBH_PROCESS_PRIO-HID_HS=osPriorityNormal
USB_HOST.USBH_PROCESS_STACK_SIZE-HID_HS=2048
USB_HOST.VirtualModeHS=Hid
USB_OTG_HS.IPParameters=VirtualMode-Host_FS,dma_enable-Host_FS,Sof_enable-Host_FS
USB_OTG_HS.Sof_enable-Host_FS=DISABLE
USB_OTG_HS.VirtualMode-Host_FS=Host_FS
USB_OTG_HS.dma_enable-Host_FS=DISABLE
VP_ADC1_Vref_Input.Mode=IN-Vrefint
VP_ADC1_Vref_Input.Signal=ADC1_Vref_Input
VP_CRC_VS_CRC.Mode=CRC_Activate
VP_CRC_VS_CRC.Signal=CRC_VS_CRC
VP_FREERTOS_VS_CMSIS_V2.Mode=CMSIS_V2
VP_FREERTOS_VS_CMSIS_V2.Signal=FREERTOS_VS_CMSIS_V2
VP_SYS_VS_tim14.Mode=TIM14
VP_SYS_VS_tim14.Signal=SYS_VS_tim14
VP_TIM10_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM10_VS_ClockSourceINT.Signal=TIM10_VS_ClockSourceINT
VP_TIM11_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM11_VS_ClockSourceINT.Signal=TIM11_VS_ClockSourceINT
VP_TIM12_VS_ClockSourceINT.Mode=Internal
VP_TIM12_VS_ClockSourceINT.Signal=TIM12_VS_ClockSourceINT
VP_TIM12_VS_no_output1.Mode=Output Compare1 No Output
VP_TIM12_VS_no_output1.Signal=TIM12_VS_no_output1
VP_TIM13_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM13_VS_ClockSourceINT.Signal=TIM13_VS_ClockSourceINT
VP_TIM13_VS_no_output1.Mode=Output Compare1 No Output
VP_TIM13_VS_no_output1.Signal=TIM13_VS_no_output1
VP_TIM1_VS_ClockSourceINT.Mode=Internal
VP_TIM1_VS_ClockSourceINT.Signal=TIM1_VS_ClockSourceINT
VP_TIM7_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM7_VS_ClockSourceINT.Signal=TIM7_VS_ClockSourceINT
VP_TIM9_VS_ClockSourceINT.Mode=Internal
VP_TIM9_VS_ClockSourceINT.Signal=TIM9_VS_ClockSourceINT
VP_USB_HOST_VS_USB_HOST_HID_HS.Mode=HID_HS
VP_USB_HOST_VS_USB_HOST_HID_HS.Signal=USB_HOST_VS_USB_HOST_HID_HS
board=custom
rtos.0.ip=FREERTOS
