@echo off
echo Testing Keil compilation...
echo Current directory: %CD%

REM Try to find Keil installation
if exist "C:\Keil_v5\UV4\UV4.exe" (
    echo Found Keil at C:\Keil_v5\UV4\UV4.exe
    "C:\Keil_v5\UV4\UV4.exe" -b Project.uvprojx -o compile_test.log
) else if exist "E:\DIAN\Keil5 MDK\UV4\UV4.exe" (
    echo Found Keil at E:\DIAN\Keil5 MDK\UV4\UV4.exe
    "E:\DIAN\Keil5 MDK\UV4\UV4.exe" -b Project.uvprojx -o compile_test.log
) else (
    echo Keil not found in common locations
    echo Please check Keil installation path
)

echo Compilation test completed.
pause
