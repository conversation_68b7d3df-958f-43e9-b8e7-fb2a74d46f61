/**
 ******************************************************************************
 * @file    ATD5984.h
 * <AUTHOR> Name]
 * @version V1.0
 * @date    2025-01-31
 * @brief   ATD5984水平电机控制头文件 - 简化版
 *          
 *          本文件定义了水平电机(电机A)的控制接口
 *          实现固定动作序列：顺时针90deg -> 逆时针188deg -> 顺时针90deg
 * 
 * @note    引脚分配 (STM32F407ZGT6):
 *          水平电机A: STEP=PC8(TIM8_CH3), DIR=PD3, SLEEP=PD2
 *          垂直电机B: STEP=PA8(TIM1_CH1), DIR=PB12, SLEEP=PC12
 *          
 *          ATD5984驱动器特性:
 *          - 1/16微步细分
 *          - 最大电流1.44A
 *          - 12-24V供电
 *          - 脉冲+方向控制模式
 ******************************************************************************
 */

#ifndef __ATD5984_H
#define __ATD5984_H

#include "sys.h"
#include "delay.h"

/* 角度控制宏定义 ------------------------------------------------------------*/
#define STEPS_PER_DEGREE    8.889f  // 步/度 *正确值：1.8°步距角+1/16细分=3200步/圈，0.1125deg/步*
#define DIR_CW              0       // 顺时针方向 *修正：尝试反转逻辑*
#define DIR_CCW             1       // 逆时针方向 *修正：尝试反转逻辑*

/* 细分模式参考表 (42步进电机, 1.8°步距角) -------------------------------*/
/*
 * 42步进电机细分模式对照表 (步距角1.8°)：
 * 1/1细分:   200步/圈   → 0.556步/度 (1.8deg/步)
 * 1/2细分:   400步/圈   → 1.111步/度 (0.9deg/步)
 * 1/4细分:   800步/圈   → 2.222步/度 (0.45deg/步)
 * 1/8细分:   1600步/圈  → 4.444步/度 (0.225deg/步)
 * 1/16细分:  3200步/圈  → 8.889步/度 (0.1125deg/步) *当前使用*
 * 1/32细分:  6400步/圈  → 17.778步/度 (0.05625deg/步)
 * 
 * D36A拨码开关设置 (SW1-SW3):
 * 000 = 1/16细分 (推荐，平衡精度和速度)
 * 001 = 1/8细分
 * 010 = 1/4细分  
 * 011 = 1/2细分
 * 100 = 1/1细分
 * 101 = 1/32细分 (最高精度)
 */

/* 函数声明 ------------------------------------------------------------------*/

/**
 * @brief  水平电机A初始化
 * @param  None
 * @retval None
 * @note   初始化水平电机控制引脚，设置默认状态：
 *         - 电机A使能 (SLEEP=HIGH)
 *         - 电机A正向 (DIR=LOW)
 *         - 所有控制引脚配置为推挽输出，50MHz
 */
void ATD5984_Init(void);

/**
 * @brief  初始化水平电机PWM信号发生器
 * @param  arr: 自动重装载值 (决定PWM频率)
 * @param  psc: 预分频系数 (决定定时器时钟)
 * @retval None
 * @note   使用TIM8的CH3(PC8)输出PWM连接到水平电机A
 *         PWM频率 = 168MHz / ((psc+1) * (arr+1))
 *         推荐: arr=10499, psc=6 -> 频率=1600Hz (中速平衡)
 *         占空比固定为50% (pulse = arr/2)
 */
void STEP12_PWM_Init(u16 arr, u16 psc);

/**
 * @brief  水平电机A旋转指定角度
 * @param  angle: 旋转角度 (度，正数为顺时针，负数为逆时针)
 * @retval None
 * @note   根据角度自动计算步数和方向，执行旋转动作
 *         内部包含适当延时确保动作完成
 */
void Motor_A_Rotate(float angle);

/**
 * @brief  方向控制测试函数
 * @param  None
 * @retval None
 * @note   执行简单的左右旋转测试，验证方向控制正确性
 *         用于校准DIR信号极性
 */
void Motor_A_DirectionTest(void);

/**
 * @brief  步数校准测试函数
 * @param  None
 * @retval None
 * @note   执行360deg旋转测试，用于校准STEPS_PER_DEGREE值
 *         通过实际测量验证步数计算的准确性
 */
void Motor_A_StepsCalibration(void);

/**
 * @brief  动态设置TIM8 PWM频率
 * @param  target_freq: 目标频率 (Hz)，有效范围800-3000Hz
 * @retval None
 * @note   运行时动态调节PWM频率，实现变速控制
 *         自动计算最优ARR和PSC组合，保持50%占空比
 *         支持无缝切换，不中断PWM输出
 */
void TIM8_SetFrequency(uint16_t target_freq);

/**
 * @brief  获取当前TIM8 PWM频率
 * @param  None
 * @retval 当前PWM频率值 (Hz)
 * @note   返回最后一次设置的频率值
 *         用于调试和状态监控
 */
uint16_t TIM8_GetCurrentFrequency(void);

/**
 * @brief  初始化垂直电机B的PWM信号发生器
 * @param  arr: 自动重装载值 (决定PWM频率)
 * @param  psc: 预分频系数 (决定定时器时钟)
 * @retval None
 * @note   使用TIM1的CH1(PA8)输出PWM连接到垂直电机B
 *         独立于TIM8，实现双电机独立频率控制
 */
void STEP_B_PWM_Init(u16 arr, u16 psc);

/**
 * @brief  设置TIM1 PWM频率（电机B专用）
 * @param  target_freq: 目标频率 (Hz)，有效范围50-200Hz
 * @retval None
 * @note   电机B超低速范围，适用于90°范围内精密控制
 *         独立于TIM8频率，实现真正的并行控制
 */
void TIM1_SetFrequency(uint16_t target_freq);

/**
 * @brief  获取当前TIM1 PWM频率
 * @param  None
 * @retval 当前PWM频率值 (Hz)
 * @note   返回TIM1最后一次设置的频率值
 */
uint16_t TIM1_GetCurrentFrequency(void);

/**
 * @brief  电机B旋转指定角度
 * @param  angle: 旋转角度 (度，正数为顺时针，负数为逆时针)
 * @retval None
 * @note   电机B专用旋转控制，使用超低速精密模式
 *         引脚控制: DIR=PB12, SLEEP=PC12, STEP=PC9(TIM8_CH4)
 */
void Motor_B_Rotate(float angle);

/* 双电机速度档位常量定义 ------------------------------------------------------*/

/* 电机A速度档位 (中低速范围，适合快速定位) */
#define MOTOR_A_SPEED_1    400   // 慢速档位
#define MOTOR_A_SPEED_2    600   // 中慢速档位  
#define MOTOR_A_SPEED_3    900   // 中速档位
#define MOTOR_A_SPEED_4    1143  // 中快速档位
#define MOTOR_A_SPEED_5    1500  // 快速档位

/* 电机B速度档位 (超低速范围，适合精密控制) */
#define MOTOR_B_SPEED_1    50    // 超精密模式 (90°需16秒)
#define MOTOR_B_SPEED_2    75    // 精密模式 (90°需10.7秒)
#define MOTOR_B_SPEED_3    100   // 慢速模式 (90°需8秒)
#define MOTOR_B_SPEED_4    150   // 中慢速模式 (90°需5.3秒)
#define MOTOR_B_SPEED_5    200   // 标准慢速模式 (90°需4秒)

/* 竖直电机B控制配置 ----------------------------------------------------------*/

/**
 * @brief  竖直电机B默认工作状态配置
 * @note   1: 第二问模式(默认卸载状态)
 *         0: 第三问模式(默认上载状态)
 *         用于在不同比赛题目间快速切换电机状态
 */
#define MOTOR_B_DEFAULT_STATE_DISABLE  1

/**
 * @brief  竖直电机B上载使能
 * @param  None
 * @retval None
 * @note   设置PC12=LOW，使能竖直电机B
 *         用于第三问需要竖直电机工作的场景
 */
void Motor_B_Enable(void);

/**
 * @brief  竖直电机B卸载禁用
 * @param  None
 * @retval None
 * @note   设置PC12=HIGH，禁用竖直电机B
 *         用于第二问竖直电机不需要工作的场景
 */
void Motor_B_Disable(void);

/* 水平电机A使能控制 ----------------------------------------------------------*/

/**
 * @brief  水平电机A上载使能
 * @param  None
 * @retval None
 * @note   设置PD2=HIGH，使能水平电机A
 *         用于需要水平电机工作的场景
 */
void Motor_A_Enable(void);

/**
 * @brief  水平电机A卸载禁用
 * @param  None
 * @retval None
 * @note   设置PD2=LOW，禁用水平电机A
 *         用于水平电机不需要工作的场景
 */
void Motor_A_Disable(void);

#endif
