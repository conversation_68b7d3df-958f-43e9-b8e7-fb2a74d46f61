#include "../../SYSTEM/sys/sys.h"
#include "KEY.h"

/* 宏定义 --------------------------------------------------------------------*/
#define KEY PEin(4)    // 按键状态读取宏，PE4引脚输入值

/**
 * @brief  按键输入初始化函数
 * @param  None
 * @retval None
 * @note   配置PE4为输入模式，使能内部上拉电阻
 */
void Key_Init(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	
	/* 使能GPIOE时钟 */
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOE, ENABLE);
	
	/* 配置PE4为输入模式 */
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4;       // PE4引脚
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;    // 输入模式
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;    // 内部上拉电阻使能
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz; // 响应速度(输入模式下无影响)
	GPIO_Init(GPIOE, &GPIO_InitStructure);
}

/**
 * @brief  按键状态扫描函数
 * @param  None
 * @retval 按键状态: 1=按下, 0=释放
 * @note   读取PE4引脚电平，低电平表示按键按下
 *         无去抖处理，需要调用方自行处理
 */
u8 Key_Scan(void)
{
	u8 tmp;
	
	/* 读取按键状态并取反逻辑 */
	if(KEY == 0)     // 按键按下时，PE4为低电平
		tmp = 1;     // 返回1表示按下
	else             // 按键释放时，PE4为高电平(上拉)
		tmp = 0;     // 返回0表示释放
	
	return tmp;
}

/**
 * @brief  按键状态扫描带防抖功能
 * @param  None
 * @retval 按键事件: 1=按下事件, 0=无事件
 * @note   只在按键按下的瞬间返回1，适合状态切换
 *         内部维护按键状态，实现防抖和边沿检测
 */
u8 Key_Scan_Debounce(void)
{
	static u8 key_state = 0;  // 按键状态 0=释放, 1=按下
	static u8 key_count = 0;  // 防抖计数器
	u8 key_current;
	u8 key_event = 0;
	
	/* 读取当前按键状态 */
	key_current = (KEY == 0) ? 1 : 0;
	
	/* 状态机防抖处理 */
	if(key_current != key_state) {
		key_count++;
		if(key_count >= 3) {  // 连续3次采样一致才认为状态改变
			if(key_state == 0 && key_current == 1) {
				key_event = 1;  // 检测到按下事件
			}
			key_state = key_current;
			key_count = 0;
		}
	} else {
		key_count = 0;  // 状态一致，清零计数器
	}
	
	return key_event;
}
