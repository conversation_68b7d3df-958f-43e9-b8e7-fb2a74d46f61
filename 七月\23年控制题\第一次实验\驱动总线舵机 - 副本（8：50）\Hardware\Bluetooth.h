#ifndef __BLUETOOTH_H
#define __BLUETOOTH_H

#include "stm32f10x.h"
#include "SystemDiagnostics.h"
#include "StateMachine.h"
#include "Geometry.h"
#include <stdio.h>
#include <string.h>

// JDY-31蓝牙模块配置
#define BLUETOOTH_BAUDRATE          9600        // JDY-31默认波特率
#define BLUETOOTH_NAME              "LaserGimbal_v1"
#define BLUETOOTH_PIN               "1234"

// 串口缓冲区配置
#define BT_RX_BUFFER_SIZE           256
#define BT_TX_BUFFER_SIZE           512
#define BT_CMD_BUFFER_SIZE          128

// 数据传输配置
#define BT_STATUS_REPORT_INTERVAL   5000        // 状态报告间隔(ms)
#define BT_ERROR_REPORT_IMMEDIATE   1           // 错误立即报告
#define BT_MAX_RETRY_COUNT          3           // 最大重试次数

// 蓝牙连接状态
typedef enum {
    BT_DISCONNECTED = 0,
    BT_CONNECTING,
    BT_CONNECTED,
    BT_ERROR
} BluetoothStatus_t;

// 数据报告类型
typedef enum {
    BT_REPORT_SYSTEM_STATUS = 0x01,     // 系统状态报告
    BT_REPORT_ERROR_LOG = 0x02,         // 错误日志报告
    BT_REPORT_PERFORMANCE = 0x03,       // 性能数据报告
    BT_REPORT_POSITION = 0x04,          // 位置信息报告
    BT_REPORT_DIAGNOSTIC = 0x05,        // 诊断信息报告
    BT_REPORT_HEARTBEAT = 0x06          // 心跳包
} BluetoothReportType_t;

// 蓝牙数据包结构
typedef struct {
    uint8_t header;                     // 包头 0xAA
    uint8_t type;                       // 报告类型
    uint16_t length;                    // 数据长度
    uint8_t data[BT_TX_BUFFER_SIZE-8];  // 数据内容
    uint16_t checksum;                  // 校验和
    uint8_t tail;                       // 包尾 0x55
} BluetoothPacket_t;

// 蓝牙控制结构体
typedef struct {
    BluetoothStatus_t status;           // 连接状态
    uint32_t last_status_report;        // 上次状态报告时间
    uint32_t last_heartbeat;            // 上次心跳时间
    uint32_t error_count;               // 错误计数
    uint32_t packet_sent;               // 发送包计数
    uint32_t packet_received;           // 接收包计数
    
    // 接收缓冲区
    uint8_t rx_buffer[BT_RX_BUFFER_SIZE];
    uint16_t rx_head;
    uint16_t rx_tail;
    uint8_t rx_complete;
    
    // 发送缓冲区
    uint8_t tx_buffer[BT_TX_BUFFER_SIZE];
    uint16_t tx_head;
    uint16_t tx_tail;
    uint8_t tx_busy;
    
    // 命令处理
    uint8_t cmd_buffer[BT_CMD_BUFFER_SIZE];
    uint8_t cmd_ready;
} BluetoothControl_t;

// 系统状态报告结构体
typedef struct {
    SystemState_t current_state;        // 当前系统状态
    uint8_t point_a_recorded;           // A点是否记录
    uint8_t point_b_recorded;           // B点是否记录
    uint8_t is_moving;                  // 是否在移动
    float current_pan_angle;            // 当前Pan角度
    float current_tilt_angle;           // 当前Tilt角度
    float system_efficiency;            // 系统效率
    uint32_t runtime;                   // 运行时间
    DiagnosticLevel_t diag_level;       // 诊断等级
} SystemStatusReport_t;

// 错误报告结构体
typedef struct {
    uint32_t timestamp;                 // 时间戳
    uint8_t module_id;                  // 模块ID
    uint8_t error_code;                 // 错误代码
    char error_message[64];             // 错误描述
    DiagnosticLevel_t severity;         // 严重程度
} ErrorReport_t;

// 基础功能函数
void Bluetooth_Init(void);
void Bluetooth_Update(void);
BluetoothStatus_t Bluetooth_GetStatus(void);

// 数据发送函数 (优化版 - 按需发送中文信息)
void Bluetooth_SendKeyAction(LaserGimbalState_t* gimbal_state, char* key_action);
void Bluetooth_SendErrorReport(ErrorReport_t* error);
void Bluetooth_SendStartupInfo(void);
void Bluetooth_SendPerformanceData(SystemPerformanceStats_t* perf);
void Bluetooth_SendPositionInfo(ServoAngle_t servo_angle, WallPoint_t wall_point);
void Bluetooth_SendDiagnosticInfo(SystemDiagnostics_t* diag);

// 数据接收和命令处理
void Bluetooth_ProcessReceivedData(void);
void Bluetooth_ParseCommand(char* cmd);

// 低级通信函数
void Bluetooth_SendPacket(BluetoothReportType_t type, uint8_t* data, uint16_t length);
void Bluetooth_SendString(char* str);
void Bluetooth_SendByte(uint8_t byte);

// AT命令配置函数
void Bluetooth_ConfigureModule(void);
void Bluetooth_SetName(char* name);
void Bluetooth_SetPin(char* pin);
void Bluetooth_TestConnection(void);

// 工具函数
uint16_t Bluetooth_CalculateChecksum(uint8_t* data, uint16_t length);
void Bluetooth_FormatStatusReport(SystemStatusReport_t* status, char* buffer, uint16_t buffer_size);
void Bluetooth_FormatErrorReport(ErrorReport_t* error, char* buffer, uint16_t buffer_size);

// 调试和监控函数
void Bluetooth_PrintStatistics(void);
void Bluetooth_PrintConnectionInfo(void);

// 中断服务函数声明
void USART2_IRQHandler(void);

// 全局控制变量
extern BluetoothControl_t bluetooth_ctrl;

#endif
