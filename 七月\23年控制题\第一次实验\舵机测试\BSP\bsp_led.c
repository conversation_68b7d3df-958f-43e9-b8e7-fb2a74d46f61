/**
  ******************************************************************************
  * @file    bsp_led.c
  * <AUTHOR> Analysis Expert
  * @version V1.0
  * @date    2025-07-17
  * @brief   LED板级支持包实现
  ******************************************************************************
  */

#include "bsp_led.h"

/**
  * @brief  LED初始化
  * @param  None
  * @retval None
  */
void BSP_LED_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 使能GPIO时钟
    RCC_AHB1PeriphClockCmd(LED_GPIO_CLK, ENABLE);
    
    // 配置LED引脚
    GPIO_InitStructure.GPIO_Pin = LED_HEARTBEAT_PIN | LED_MOVING_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(LED_GPIO_PORT, &GPIO_InitStructure);
    
    // 初始状态：LED熄灭（共阳极，高电平熄灭）
    GPIO_SetBits(LED_GPIO_PORT, LED_HEARTBEAT_PIN | LED_MOVING_PIN);
}

/**
  * @brief  点亮LED
  * @param  led: LED编号
  * @retval None
  */
void BSP_LED_On(uint8_t led)
{
    switch(led)
    {
        case LED_HEARTBEAT:
            GPIO_ResetBits(LED_GPIO_PORT, LED_HEARTBEAT_PIN);  // 低电平点亮
            break;
        case LED_MOVING:
            GPIO_ResetBits(LED_GPIO_PORT, LED_MOVING_PIN);
            break;
        default:
            break;
    }
}

/**
  * @brief  熄灭LED
  * @param  led: LED编号
  * @retval None
  */
void BSP_LED_Off(uint8_t led)
{
    switch(led)
    {
        case LED_HEARTBEAT:
            GPIO_SetBits(LED_GPIO_PORT, LED_HEARTBEAT_PIN);    // 高电平熄灭
            break;
        case LED_MOVING:
            GPIO_SetBits(LED_GPIO_PORT, LED_MOVING_PIN);
            break;
        default:
            break;
    }
}

/**
  * @brief  切换LED状态
  * @param  led: LED编号
  * @retval None
  */
void BSP_LED_Toggle(uint8_t led)
{
    switch(led)
    {
        case LED_HEARTBEAT:
            if(GPIO_ReadOutputDataBit(LED_GPIO_PORT, LED_HEARTBEAT_PIN))
                GPIO_ResetBits(LED_GPIO_PORT, LED_HEARTBEAT_PIN);
            else
                GPIO_SetBits(LED_GPIO_PORT, LED_HEARTBEAT_PIN);
            break;
        case LED_MOVING:
            if(GPIO_ReadOutputDataBit(LED_GPIO_PORT, LED_MOVING_PIN))
                GPIO_ResetBits(LED_GPIO_PORT, LED_MOVING_PIN);
            else
                GPIO_SetBits(LED_GPIO_PORT, LED_MOVING_PIN);
            break;
        default:
            break;
    }
}
