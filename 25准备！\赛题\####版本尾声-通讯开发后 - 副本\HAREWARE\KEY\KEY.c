#include "../../SYSTEM/sys/sys.h"
#include "KEY.h"

/* 宏定义 --------------------------------------------------------------------*/
#define KEY PBin(14)    // 按键状态读取宏，PB14引脚输入值

/**
 * @brief  按键输入初始化函数
 * @param  None
 * @retval None
 * @note   配置PB14为输入模式，使能内部上拉电阻
 */
void Key_Init(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	
	/* 使能GPIOB时钟 */
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOB, ENABLE);
	
	/* 配置PB14为输入模式 */
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_14;      // PB14引脚
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;    // 输入模式
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;    // 内部上拉电阻使能
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz; // 响应速度(输入模式下无影响)
	GPIO_Init(GPIOB, &GPIO_InitStructure);
}

/**
 * @brief  按键状态扫描函数
 * @param  None
 * @retval 按键状态: 1=按下, 0=释放
 * @note   读取PB14引脚电平，低电平表示按键按下
 *         无去抖处理，需要调用方自行处理
 */
u8 Key_Scan(void)
{
	u8 tmp;
	
	/* 读取按键状态并取反逻辑 */
	if(KEY == 0)     // 按键按下时，PB14为低电平
		tmp = 1;     // 返回1表示按下
	else             // 按键释放时，PB14为高电平(上拉)
		tmp = 0;     // 返回0表示释放
	
	return tmp;
}
