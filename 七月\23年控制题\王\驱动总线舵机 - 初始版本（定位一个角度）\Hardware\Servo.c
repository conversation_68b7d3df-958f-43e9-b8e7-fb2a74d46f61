#include "servo.h"
#include "usart.h"
#include "Delay.h"

void Servo_Init(void) {
    USART1_Init();
    Delay_ms(100);
}

uint16_t Servo_AngleToPosition(float angle) {
    if (angle < 0) angle = 0;
    if (angle > SERVO_MAX_ANGLE) angle = SERVO_MAX_ANGLE;
    return (uint16_t)(angle * SERVO_MAX_POSITION / SERVO_MAX_ANGLE);
}

float Servo_PositionToAngle(uint16_t position) {
    if (position > SERVO_MAX_POSITION) position = SERVO_MAX_POSITION;
    return ((float)position / (float)SERVO_MAX_POSITION) * SERVO_MAX_ANGLE;
}

uint8_t Servo_CalculateChecksum(uint8_t* data, uint8_t length) {
    uint16_t sum = 0;
    for (uint8_t i = 0; i < length; i++) {
        sum += data[i];
    }
    return (~sum) & 0xFF;
}

void Servo_SendCommand(uint8_t id, uint8_t cmd, uint8_t* params, uint8_t param_len) {
    uint8_t buffer[16];
    uint8_t index = 0;

    // 帧头
    buffer[index++] = SERVO_HEADER;
    buffer[index++] = SERVO_HEADER;

    buffer[index++] = id;
    buffer[index++] = param_len + 3;   // Length = Cmd + Params + Checksum（长度字段本身不参与计算）

    buffer[index++] = cmd;

    // 参数部分
    for (uint8_t i = 0; i < param_len; i++) {
        buffer[index++] = params[i];
    }

    // 校验和（不包含帧头）
    uint8_t checksum = Servo_CalculateChecksum(&buffer[2], param_len + 3);
    buffer[index++] = checksum;

    USART1_SendBuffer(buffer, index);
    Delay_ms(10);
}

void Servo_SetPosition(uint8_t id, float angle) {
    Servo_SetPositionWithTime(id, angle, 1000);  // 默认1000ms
}

void Servo_SetPositionWithTime(uint8_t id, float angle, uint16_t time_ms) {
    uint16_t pos = Servo_AngleToPosition(angle);
    uint8_t params[4];

    params[0] = pos & 0xFF;
    params[1] = (pos >> 8) & 0xFF;
    params[2] = time_ms & 0xFF;
    params[3] = (time_ms >> 8) & 0xFF;

    Servo_SendCommand(id, SERVO_CMD_MOVE_TIME_WRITE, params, 4);
}

int Servo_ReadPosition(uint8_t id, uint16_t *position) {
    uint8_t tx_buffer[6];
    uint8_t rx_buffer[8];
    uint8_t index = 0;
    uint16_t timeout = 1000;  // Add timeout counter

    // Prepare read command
    tx_buffer[index++] = SERVO_HEADER;
    tx_buffer[index++] = SERVO_HEADER;
    tx_buffer[index++] = id;
    tx_buffer[index++] = 3;  // Length: cmd + checksum
    tx_buffer[index++] = SERVO_CMD_POS_READ;

    // Calculate checksum
    uint8_t checksum = Servo_CalculateChecksum(&tx_buffer[2], 3);
    tx_buffer[index++] = checksum;

    // Send command
    USART1_SendBuffer(tx_buffer, index);

    // Wait for response
    Delay_ms(50);  // Increase wait time

    // Simplified receive - read fixed length
    for (int i = 0; i < 8; i++) {
        // Add timeout check
        timeout = 1000;
        while (USART_GetFlagStatus(USART1, USART_FLAG_RXNE) == RESET && timeout > 0) {
            timeout--;
            if (timeout == 0) return -1;  // Timeout return error
        }
        rx_buffer[i] = USART_ReceiveData(USART1);
    }

    // Parse response
    if (rx_buffer[0] == SERVO_HEADER && rx_buffer[1] == SERVO_HEADER &&
        rx_buffer[2] == id && rx_buffer[4] == SERVO_CMD_POS_READ) {
        *position = (uint16_t)(rx_buffer[5] | (rx_buffer[6] << 8));
        return 0;  // Success
    }

    return -1;  // Error
}
