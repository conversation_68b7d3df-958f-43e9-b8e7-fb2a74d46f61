#include "../../lvgl.h"

/*******************************************************************************
 * Size: 14 px
 * Bpp: 4
 * Opts: --no-compress --no-prefilter --bpp 4 --size 14 --font <PERSON>-Medium.ttf -r 0x20-0x7F,0xB0,0x2022 --font <PERSON>ont<PERSON>wesome5-Solid+Brands+Regular.woff -r 61441,61448,61451,61452,61452,61453,61457,61459,61461,61465,61468,61473,61478,61479,61480,61502,61512,61515,61516,61517,61521,61522,61523,61524,61543,61544,61550,61552,61553,61556,61559,61560,61561,61563,61587,61589,61636,61637,61639,61671,61674,61683,61724,61732,61787,61931,62016,62017,62018,62019,62020,62087,62099,62212,62189,62810,63426,63650 --format lvgl -o lv_font_montserrat_14.c --force-fast-kern-format
 ******************************************************************************/

#ifndef LV_FONT_MONTSERRAT_14
#define LV_FONT_MONTSERRAT_14 1
#endif

#if LV_FONT_MONTSERRAT_14

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t gylph_bitmap[] = {
    /* U+20 " " */

    /* U+21 "!" */
    0xe, 0xa0, 0xd9, 0xd, 0x90, 0xc8, 0xc, 0x80,
    0xb7, 0xa, 0x60, 0x11, 0xb, 0x80, 0xd9,

    /* U+22 "\"" */
    0x1f, 0x9, 0x91, 0xf0, 0x88, 0x1f, 0x8, 0x80,
    0xf0, 0x88, 0x0, 0x0, 0x0,

    /* U+23 "#" */
    0x0, 0xd, 0x20, 0x3c, 0x0, 0x0, 0xf, 0x0,
    0x69, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xf7, 0x1,
    0x5c, 0x11, 0xa6, 0x10, 0x0, 0x69, 0x0, 0xc3,
    0x0, 0x0, 0x88, 0x0, 0xd2, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xf0, 0x12, 0xc5, 0x23, 0xe2, 0x20,
    0x0, 0xd2, 0x3, 0xc0, 0x0, 0x0, 0xf0, 0x4,
    0xb0, 0x0,

    /* U+24 "$" */
    0x0, 0x0, 0x70, 0x0, 0x0, 0x0, 0xe, 0x0,
    0x0, 0x0, 0x0, 0xe0, 0x0, 0x0, 0x19, 0xef,
    0xea, 0x30, 0xc, 0xd6, 0xe6, 0xa7, 0x2, 0xf4,
    0xe, 0x0, 0x0, 0x1f, 0x80, 0xe0, 0x0, 0x0,
    0x6f, 0xef, 0x50, 0x0, 0x0, 0x16, 0xff, 0xe5,
    0x0, 0x0, 0xe, 0xa, 0xf0, 0x1, 0x0, 0xe0,
    0x5f, 0x13, 0xf8, 0x5e, 0x6e, 0xb0, 0x5, 0xcf,
    0xfe, 0x91, 0x0, 0x0, 0xe, 0x0, 0x0, 0x0,
    0x0, 0x70, 0x0, 0x0,

    /* U+25 "%" */
    0x8, 0xdd, 0x30, 0x0, 0xa7, 0x0, 0x4b, 0x2,
    0xd0, 0x4, 0xc0, 0x0, 0x77, 0x0, 0xd0, 0x1d,
    0x20, 0x0, 0x4b, 0x3, 0xd0, 0xa7, 0x0, 0x0,
    0x7, 0xdc, 0x34, 0xc4, 0xcc, 0x30, 0x0, 0x0,
    0x1d, 0x2d, 0x22, 0xd0, 0x0, 0x0, 0xa6, 0x3b,
    0x0, 0xb3, 0x0, 0x5, 0xc0, 0x3a, 0x0, 0xa3,
    0x0, 0x1d, 0x20, 0xd, 0x0, 0xd0, 0x0, 0xa6,
    0x0, 0x4, 0xcc, 0x40,

    /* U+26 "&" */
    0x0, 0x4d, 0xfc, 0x30, 0x0, 0x0, 0xf7, 0x18,
    0xc0, 0x0, 0x1, 0xf2, 0x5, 0xd0, 0x0, 0x0,
    0xbb, 0x6e, 0x40, 0x0, 0x0, 0x5f, 0xf3, 0x0,
    0x0, 0x7, 0xe6, 0xdb, 0x3, 0x80, 0x2f, 0x30,
    0x1d, 0xba, 0xa0, 0x5f, 0x0, 0x1, 0xdf, 0x40,
    0x1f, 0xb4, 0x48, 0xfe, 0xc0, 0x3, 0xbf, 0xfc,
    0x40, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+27 "'" */
    0x1f, 0x1, 0xf0, 0x1f, 0x0, 0xf0, 0x0, 0x0,

    /* U+28 "(" */
    0x3, 0xf1, 0xb, 0x90, 0xf, 0x40, 0x4f, 0x0,
    0x7d, 0x0, 0x9b, 0x0, 0xaa, 0x0, 0xaa, 0x0,
    0x9b, 0x0, 0x7d, 0x0, 0x4f, 0x0, 0xf, 0x40,
    0xb, 0x90, 0x3, 0xf1,

    /* U+29 ")" */
    0x5e, 0x0, 0xe, 0x60, 0x8, 0xc0, 0x4, 0xf0,
    0x1, 0xf3, 0x0, 0xf5, 0x0, 0xe6, 0x0, 0xe6,
    0x0, 0xf5, 0x1, 0xf3, 0x4, 0xf0, 0x8, 0xc0,
    0xe, 0x60, 0x5e, 0x0,

    /* U+2A "*" */
    0x0, 0x93, 0x0, 0x88, 0xa6, 0xc2, 0x9, 0xfe,
    0x40, 0x4d, 0xdd, 0xb1, 0x42, 0x93, 0x50, 0x0,
    0x52, 0x0,

    /* U+2B "+" */
    0x0, 0x4, 0x50, 0x0, 0x0, 0x8, 0xa0, 0x0,
    0x0, 0x8, 0xa0, 0x0, 0x1f, 0xff, 0xff, 0xf3,
    0x3, 0x39, 0xb3, 0x30, 0x0, 0x8, 0xa0, 0x0,
    0x0, 0x8, 0xa0, 0x0,

    /* U+2C "," */
    0x1, 0x3, 0xf6, 0x1e, 0x60, 0xe1, 0x2c, 0x0,

    /* U+2D "-" */
    0x0, 0x0, 0x3, 0xff, 0xf9, 0x3, 0x33, 0x10,

    /* U+2E "." */
    0x0, 0x3, 0xf5, 0x2e, 0x40,

    /* U+2F "/" */
    0x0, 0x0, 0xe, 0x40, 0x0, 0x4, 0xe0, 0x0,
    0x0, 0x9a, 0x0, 0x0, 0xe, 0x40, 0x0, 0x4,
    0xf0, 0x0, 0x0, 0x9a, 0x0, 0x0, 0xe, 0x40,
    0x0, 0x3, 0xf0, 0x0, 0x0, 0x9a, 0x0, 0x0,
    0xe, 0x50, 0x0, 0x3, 0xf0, 0x0, 0x0, 0x9a,
    0x0, 0x0, 0xe, 0x50, 0x0, 0x3, 0xf0, 0x0,
    0x0,

    /* U+30 "0" */
    0x0, 0x4c, 0xfe, 0x70, 0x0, 0x4f, 0xb6, 0x8f,
    0x90, 0xd, 0xb0, 0x0, 0x5f, 0x32, 0xf4, 0x0,
    0x0, 0xe7, 0x4f, 0x20, 0x0, 0xc, 0xa4, 0xf2,
    0x0, 0x0, 0xca, 0x2f, 0x40, 0x0, 0xe, 0x70,
    0xdb, 0x0, 0x5, 0xf2, 0x4, 0xfb, 0x68, 0xf9,
    0x0, 0x4, 0xcf, 0xe7, 0x0,

    /* U+31 "1" */
    0xef, 0xfb, 0x44, 0xcb, 0x0, 0xab, 0x0, 0xab,
    0x0, 0xab, 0x0, 0xab, 0x0, 0xab, 0x0, 0xab,
    0x0, 0xab, 0x0, 0xab,

    /* U+32 "2" */
    0x7, 0xdf, 0xea, 0x10, 0x8e, 0x85, 0x7e, 0xc0,
    0x1, 0x0, 0x6, 0xf1, 0x0, 0x0, 0x6, 0xf0,
    0x0, 0x0, 0xd, 0x90, 0x0, 0x0, 0xcc, 0x0,
    0x0, 0x1c, 0xc0, 0x0, 0x1, 0xdb, 0x0, 0x0,
    0x1d, 0xe5, 0x44, 0x42, 0x7f, 0xff, 0xff, 0xf9,

    /* U+33 "3" */
    0x7f, 0xff, 0xff, 0xf0, 0x24, 0x44, 0x5f, 0x90,
    0x0, 0x0, 0xbc, 0x0, 0x0, 0x9, 0xe1, 0x0,
    0x0, 0x3f, 0xd8, 0x10, 0x0, 0x4, 0x6d, 0xd0,
    0x0, 0x0, 0x2, 0xf4, 0x10, 0x0, 0x2, 0xf4,
    0xbd, 0x75, 0x7d, 0xd0, 0x19, 0xdf, 0xea, 0x10,

    /* U+34 "4" */
    0x0, 0x0, 0xd, 0xa0, 0x0, 0x0, 0x0, 0xac,
    0x0, 0x0, 0x0, 0x7, 0xe1, 0x0, 0x0, 0x0,
    0x4f, 0x40, 0x10, 0x0, 0x2, 0xf6, 0x0, 0xf5,
    0x0, 0x1d, 0xa0, 0x0, 0xf5, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xf3, 0x13, 0x33, 0x33, 0xf7, 0x30,
    0x0, 0x0, 0x0, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0xf5, 0x0,

    /* U+35 "5" */
    0x9, 0xff, 0xff, 0xf0, 0xa, 0xb4, 0x44, 0x40,
    0xc, 0x80, 0x0, 0x0, 0xe, 0x70, 0x0, 0x0,
    0xf, 0xff, 0xfc, 0x40, 0x3, 0x34, 0x5c, 0xf2,
    0x0, 0x0, 0x0, 0xf7, 0x10, 0x0, 0x0, 0xf7,
    0x8e, 0x85, 0x6c, 0xf1, 0x8, 0xdf, 0xfb, 0x30,

    /* U+36 "6" */
    0x0, 0x2a, 0xef, 0xd5, 0x0, 0x3f, 0xd6, 0x57,
    0x40, 0xc, 0xc0, 0x0, 0x0, 0x2, 0xf4, 0x0,
    0x0, 0x0, 0x4f, 0x5b, 0xff, 0xa1, 0x4, 0xfe,
    0x84, 0x5d, 0xd0, 0x3f, 0x80, 0x0, 0x3f, 0x30,
    0xe8, 0x0, 0x3, 0xf2, 0x6, 0xf8, 0x45, 0xdc,
    0x0, 0x5, 0xdf, 0xe9, 0x10,

    /* U+37 "7" */
    0x9f, 0xff, 0xff, 0xfd, 0x9d, 0x44, 0x44, 0xe9,
    0x9c, 0x0, 0x4, 0xf2, 0x0, 0x0, 0xb, 0xb0,
    0x0, 0x0, 0x2f, 0x40, 0x0, 0x0, 0xad, 0x0,
    0x0, 0x1, 0xf6, 0x0, 0x0, 0x8, 0xe0, 0x0,
    0x0, 0xe, 0x80, 0x0, 0x0, 0x6f, 0x10, 0x0,

    /* U+38 "8" */
    0x1, 0x9e, 0xfe, 0x91, 0x0, 0xbe, 0x63, 0x6e,
    0xc0, 0xf, 0x60, 0x0, 0x6f, 0x0, 0xcc, 0x20,
    0x2b, 0xc0, 0x2, 0xef, 0xff, 0xe2, 0x0, 0xdc,
    0x42, 0x4c, 0xd0, 0x5f, 0x20, 0x0, 0x1f, 0x55,
    0xf2, 0x0, 0x2, 0xf5, 0xe, 0xd5, 0x35, 0xde,
    0x0, 0x1a, 0xef, 0xea, 0x10,

    /* U+39 "9" */
    0x3, 0xbf, 0xea, 0x20, 0x2f, 0xa4, 0x4b, 0xe1,
    0x8e, 0x0, 0x0, 0xe9, 0x9d, 0x0, 0x0, 0xdd,
    0x4f, 0x71, 0x29, 0xff, 0x7, 0xff, 0xfc, 0x9e,
    0x0, 0x2, 0x10, 0xac, 0x0, 0x0, 0x2, 0xf6,
    0x7, 0x65, 0x8f, 0xb0, 0xa, 0xef, 0xd7, 0x0,

    /* U+3A ":" */
    0x2e, 0x53, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0x52, 0xe4,

    /* U+3B ";" */
    0x2e, 0x53, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0x52, 0xf6, 0xd, 0x21, 0xd0, 0x1,
    0x0,

    /* U+3C "<" */
    0x0, 0x0, 0x0, 0x31, 0x0, 0x0, 0x6c, 0xf2,
    0x1, 0x8e, 0xd6, 0x0, 0xf, 0xc3, 0x0, 0x0,
    0xa, 0xfb, 0x40, 0x0, 0x0, 0x17, 0xee, 0x70,
    0x0, 0x0, 0x5, 0xc3, 0x0, 0x0, 0x0, 0x0,

    /* U+3D "=" */
    0x1f, 0xff, 0xff, 0xf3, 0x3, 0x33, 0x33, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xf3, 0x3, 0x33, 0x33, 0x30,

    /* U+3E ">" */
    0x4, 0x0, 0x0, 0x0, 0xe, 0xd7, 0x10, 0x0,
    0x0, 0x5c, 0xf9, 0x20, 0x0, 0x0, 0x2a, 0xf2,
    0x0, 0x3, 0xaf, 0xb1, 0x6, 0xde, 0x82, 0x0,
    0x1c, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+3F "?" */
    0x7, 0xdf, 0xea, 0x10, 0x9e, 0x74, 0x6e, 0xc0,
    0x1, 0x0, 0x6, 0xf0, 0x0, 0x0, 0x9, 0xc0,
    0x0, 0x0, 0x8e, 0x20, 0x0, 0x6, 0xf2, 0x0,
    0x0, 0x8, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0x80, 0x0, 0x0, 0xc, 0x90, 0x0,

    /* U+40 "@" */
    0x0, 0x0, 0x6c, 0xef, 0xda, 0x40, 0x0, 0x0,
    0x3d, 0xa4, 0x10, 0x16, 0xd9, 0x0, 0x1, 0xe5,
    0x9, 0xee, 0x98, 0xca, 0x90, 0xa, 0x80, 0xbd,
    0x43, 0xaf, 0xc0, 0xd3, 0xf, 0x13, 0xf2, 0x0,
    0xc, 0xc0, 0x69, 0x3c, 0x6, 0xd0, 0x0, 0x7,
    0xc0, 0x3b, 0x4b, 0x6, 0xd0, 0x0, 0x7, 0xc0,
    0x2c, 0x3c, 0x3, 0xf2, 0x0, 0xc, 0xc0, 0x4a,
    0xf, 0x10, 0xbd, 0x43, 0x9e, 0xe3, 0xc5, 0xa,
    0x80, 0x9, 0xee, 0x91, 0xcf, 0x90, 0x1, 0xe5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3d, 0xa4,
    0x10, 0x28, 0x0, 0x0, 0x0, 0x0, 0x7c, 0xef,
    0xd9, 0x10, 0x0,

    /* U+41 "A" */
    0x0, 0x0, 0xc, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xd7, 0x0, 0x0, 0x0, 0x0, 0xb9, 0x6e,
    0x0, 0x0, 0x0, 0x2, 0xf2, 0xe, 0x50, 0x0,
    0x0, 0x9, 0xa0, 0x7, 0xd0, 0x0, 0x0, 0x1f,
    0x30, 0x0, 0xf4, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0xe7, 0x33, 0x33, 0x4f, 0x20,
    0x5, 0xf0, 0x0, 0x0, 0xc, 0x90, 0xc, 0x90,
    0x0, 0x0, 0x6, 0xf1,

    /* U+42 "B" */
    0x8f, 0xff, 0xfe, 0xc4, 0x8, 0xe3, 0x33, 0x4b,
    0xf2, 0x8e, 0x0, 0x0, 0x1f, 0x58, 0xe0, 0x0,
    0x18, 0xf1, 0x8f, 0xff, 0xff, 0xf8, 0x8, 0xe3,
    0x33, 0x37, 0xf6, 0x8e, 0x0, 0x0, 0x9, 0xc8,
    0xe0, 0x0, 0x0, 0x9d, 0x8e, 0x33, 0x34, 0x7f,
    0x78, 0xff, 0xff, 0xfd, 0x70,

    /* U+43 "C" */
    0x0, 0x7, 0xcf, 0xfb, 0x40, 0x0, 0xcf, 0x96,
    0x6a, 0xf5, 0xa, 0xe2, 0x0, 0x0, 0x30, 0x1f,
    0x60, 0x0, 0x0, 0x0, 0x4f, 0x20, 0x0, 0x0,
    0x0, 0x4f, 0x20, 0x0, 0x0, 0x0, 0x1f, 0x60,
    0x0, 0x0, 0x0, 0xa, 0xe2, 0x0, 0x0, 0x30,
    0x1, 0xcf, 0x96, 0x6a, 0xf5, 0x0, 0x7, 0xdf,
    0xfb, 0x40,

    /* U+44 "D" */
    0x8f, 0xff, 0xfe, 0xa4, 0x0, 0x8e, 0x44, 0x46,
    0xcf, 0x70, 0x8e, 0x0, 0x0, 0x7, 0xf3, 0x8e,
    0x0, 0x0, 0x0, 0xda, 0x8e, 0x0, 0x0, 0x0,
    0x9d, 0x8e, 0x0, 0x0, 0x0, 0x9d, 0x8e, 0x0,
    0x0, 0x0, 0xda, 0x8e, 0x0, 0x0, 0x7, 0xf3,
    0x8e, 0x44, 0x46, 0xbf, 0x70, 0x8f, 0xff, 0xfe,
    0xa4, 0x0,

    /* U+45 "E" */
    0x8f, 0xff, 0xff, 0xf6, 0x8e, 0x44, 0x44, 0x41,
    0x8e, 0x0, 0x0, 0x0, 0x8e, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xc0, 0x8e, 0x33, 0x33, 0x20,
    0x8e, 0x0, 0x0, 0x0, 0x8e, 0x0, 0x0, 0x0,
    0x8e, 0x44, 0x44, 0x42, 0x8f, 0xff, 0xff, 0xf9,

    /* U+46 "F" */
    0x8f, 0xff, 0xff, 0xf6, 0x8e, 0x44, 0x44, 0x41,
    0x8e, 0x0, 0x0, 0x0, 0x8e, 0x0, 0x0, 0x0,
    0x8e, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xc0,
    0x8e, 0x33, 0x33, 0x20, 0x8e, 0x0, 0x0, 0x0,
    0x8e, 0x0, 0x0, 0x0, 0x8e, 0x0, 0x0, 0x0,

    /* U+47 "G" */
    0x0, 0x7, 0xcf, 0xfc, 0x50, 0x0, 0xcf, 0x96,
    0x6a, 0xf6, 0xa, 0xe2, 0x0, 0x0, 0x20, 0x1f,
    0x60, 0x0, 0x0, 0x0, 0x4f, 0x20, 0x0, 0x0,
    0x0, 0x4f, 0x20, 0x0, 0x0, 0xb9, 0x1f, 0x60,
    0x0, 0x0, 0xb9, 0xa, 0xe3, 0x0, 0x0, 0xb9,
    0x0, 0xcf, 0x96, 0x6a, 0xf8, 0x0, 0x7, 0xdf,
    0xfc, 0x60,

    /* U+48 "H" */
    0x8e, 0x0, 0x0, 0x8, 0xe8, 0xe0, 0x0, 0x0,
    0x8e, 0x8e, 0x0, 0x0, 0x8, 0xe8, 0xe0, 0x0,
    0x0, 0x8e, 0x8f, 0xff, 0xff, 0xff, 0xe8, 0xe3,
    0x33, 0x33, 0x9e, 0x8e, 0x0, 0x0, 0x8, 0xe8,
    0xe0, 0x0, 0x0, 0x8e, 0x8e, 0x0, 0x0, 0x8,
    0xe8, 0xe0, 0x0, 0x0, 0x8e,

    /* U+49 "I" */
    0x8e, 0x8e, 0x8e, 0x8e, 0x8e, 0x8e, 0x8e, 0x8e,
    0x8e, 0x8e,

    /* U+4A "J" */
    0x2, 0xff, 0xff, 0xc0, 0x4, 0x44, 0xbc, 0x0,
    0x0, 0x9, 0xc0, 0x0, 0x0, 0x9c, 0x0, 0x0,
    0x9, 0xc0, 0x0, 0x0, 0x9c, 0x0, 0x0, 0x9,
    0xc0, 0x20, 0x0, 0xba, 0xe, 0xb5, 0x8f, 0x60,
    0x3c, 0xfe, 0x80,

    /* U+4B "K" */
    0x8e, 0x0, 0x0, 0x4f, 0x50, 0x8e, 0x0, 0x4,
    0xf6, 0x0, 0x8e, 0x0, 0x3f, 0x70, 0x0, 0x8e,
    0x3, 0xf8, 0x0, 0x0, 0x8e, 0x2e, 0xc0, 0x0,
    0x0, 0x8e, 0xec, 0xf6, 0x0, 0x0, 0x8f, 0xb0,
    0x7f, 0x30, 0x0, 0x8e, 0x0, 0xa, 0xe1, 0x0,
    0x8e, 0x0, 0x0, 0xcc, 0x0, 0x8e, 0x0, 0x0,
    0x1e, 0xa0,

    /* U+4C "L" */
    0x8e, 0x0, 0x0, 0x0, 0x8e, 0x0, 0x0, 0x0,
    0x8e, 0x0, 0x0, 0x0, 0x8e, 0x0, 0x0, 0x0,
    0x8e, 0x0, 0x0, 0x0, 0x8e, 0x0, 0x0, 0x0,
    0x8e, 0x0, 0x0, 0x0, 0x8e, 0x0, 0x0, 0x0,
    0x8e, 0x44, 0x44, 0x40, 0x8f, 0xff, 0xff, 0xf2,

    /* U+4D "M" */
    0x8e, 0x0, 0x0, 0x0, 0x8, 0xe8, 0xf7, 0x0,
    0x0, 0x2, 0xfe, 0x8f, 0xf1, 0x0, 0x0, 0xbf,
    0xe8, 0xdc, 0xa0, 0x0, 0x4f, 0x9e, 0x8d, 0x2f,
    0x30, 0xd, 0x87, 0xe8, 0xd0, 0x9c, 0x6, 0xe0,
    0x7e, 0x8d, 0x1, 0xe7, 0xe5, 0x7, 0xe8, 0xd0,
    0x6, 0xfc, 0x0, 0x7e, 0x8d, 0x0, 0xa, 0x20,
    0x7, 0xe8, 0xd0, 0x0, 0x0, 0x0, 0x7e,

    /* U+4E "N" */
    0x8e, 0x10, 0x0, 0x8, 0xe8, 0xfc, 0x0, 0x0,
    0x8e, 0x8f, 0xf9, 0x0, 0x8, 0xe8, 0xe6, 0xf6,
    0x0, 0x8e, 0x8e, 0x9, 0xf3, 0x8, 0xe8, 0xe0,
    0xc, 0xe1, 0x8e, 0x8e, 0x0, 0x1e, 0xb8, 0xe8,
    0xe0, 0x0, 0x3f, 0xee, 0x8e, 0x0, 0x0, 0x6f,
    0xe8, 0xe0, 0x0, 0x0, 0xae,

    /* U+4F "O" */
    0x0, 0x7, 0xcf, 0xeb, 0x50, 0x0, 0x0, 0xcf,
    0x96, 0x6b, 0xf9, 0x0, 0xa, 0xe2, 0x0, 0x0,
    0x5f, 0x60, 0x1f, 0x60, 0x0, 0x0, 0xa, 0xd0,
    0x4f, 0x20, 0x0, 0x0, 0x6, 0xf0, 0x4f, 0x20,
    0x0, 0x0, 0x6, 0xf0, 0x1f, 0x60, 0x0, 0x0,
    0xa, 0xd0, 0xa, 0xe2, 0x0, 0x0, 0x5f, 0x60,
    0x0, 0xcf, 0x96, 0x6b, 0xfa, 0x0, 0x0, 0x7,
    0xdf, 0xeb, 0x50, 0x0,

    /* U+50 "P" */
    0x8f, 0xff, 0xfd, 0x70, 0x8, 0xe4, 0x45, 0x8f,
    0xb0, 0x8e, 0x0, 0x0, 0x5f, 0x38, 0xe0, 0x0,
    0x1, 0xf5, 0x8e, 0x0, 0x0, 0x3f, 0x48, 0xe0,
    0x1, 0x4d, 0xd0, 0x8f, 0xff, 0xff, 0xb2, 0x8,
    0xe3, 0x33, 0x10, 0x0, 0x8e, 0x0, 0x0, 0x0,
    0x8, 0xe0, 0x0, 0x0, 0x0,

    /* U+51 "Q" */
    0x0, 0x7, 0xcf, 0xeb, 0x50, 0x0, 0x0, 0xcf,
    0x96, 0x6b, 0xf9, 0x0, 0xa, 0xe2, 0x0, 0x0,
    0x5f, 0x60, 0x1f, 0x60, 0x0, 0x0, 0xa, 0xd0,
    0x4f, 0x20, 0x0, 0x0, 0x6, 0xf0, 0x4f, 0x20,
    0x0, 0x0, 0x5, 0xf0, 0x1f, 0x60, 0x0, 0x0,
    0xa, 0xd0, 0xa, 0xe2, 0x0, 0x0, 0x5f, 0x60,
    0x1, 0xdf, 0x85, 0x5a, 0xfa, 0x0, 0x0, 0x8,
    0xdf, 0xfc, 0x50, 0x0, 0x0, 0x0, 0x1, 0xec,
    0x21, 0x94, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0x12, 0x0,

    /* U+52 "R" */
    0x8f, 0xff, 0xfd, 0x70, 0x8, 0xe4, 0x45, 0x8f,
    0xb0, 0x8e, 0x0, 0x0, 0x5f, 0x38, 0xe0, 0x0,
    0x1, 0xf5, 0x8e, 0x0, 0x0, 0x3f, 0x38, 0xe0,
    0x1, 0x4d, 0xd0, 0x8f, 0xff, 0xff, 0xc2, 0x8,
    0xe3, 0x33, 0xda, 0x0, 0x8e, 0x0, 0x2, 0xf6,
    0x8, 0xe0, 0x0, 0x5, 0xf3,

    /* U+53 "S" */
    0x1, 0x9e, 0xfd, 0xa2, 0x0, 0xce, 0x64, 0x6b,
    0x70, 0x2f, 0x40, 0x0, 0x0, 0x1, 0xf8, 0x0,
    0x0, 0x0, 0x6, 0xfd, 0x95, 0x0, 0x0, 0x1,
    0x6a, 0xee, 0x50, 0x0, 0x0, 0x0, 0x9f, 0x0,
    0x20, 0x0, 0x5, 0xf1, 0x3f, 0xa5, 0x47, 0xeb,
    0x0, 0x4b, 0xef, 0xe9, 0x10,

    /* U+54 "T" */
    0xff, 0xff, 0xff, 0xff, 0x24, 0x44, 0xbd, 0x44,
    0x40, 0x0, 0x9, 0xc0, 0x0, 0x0, 0x0, 0x9c,
    0x0, 0x0, 0x0, 0x9, 0xc0, 0x0, 0x0, 0x0,
    0x9c, 0x0, 0x0, 0x0, 0x9, 0xc0, 0x0, 0x0,
    0x0, 0x9c, 0x0, 0x0, 0x0, 0x9, 0xc0, 0x0,
    0x0, 0x0, 0x9c, 0x0, 0x0,

    /* U+55 "U" */
    0x9c, 0x0, 0x0, 0xb, 0xa9, 0xc0, 0x0, 0x0,
    0xba, 0x9c, 0x0, 0x0, 0xb, 0xa9, 0xc0, 0x0,
    0x0, 0xba, 0x9c, 0x0, 0x0, 0xb, 0xa9, 0xc0,
    0x0, 0x0, 0xba, 0x8e, 0x0, 0x0, 0xd, 0x94,
    0xf4, 0x0, 0x3, 0xf5, 0xc, 0xf8, 0x68, 0xfd,
    0x0, 0x9, 0xef, 0xe9, 0x10,

    /* U+56 "V" */
    0xc, 0xb0, 0x0, 0x0, 0xa, 0xc0, 0x5f, 0x20,
    0x0, 0x1, 0xf5, 0x0, 0xe9, 0x0, 0x0, 0x8e,
    0x0, 0x8, 0xf0, 0x0, 0xe, 0x70, 0x0, 0x1f,
    0x60, 0x5, 0xf1, 0x0, 0x0, 0xad, 0x0, 0xc9,
    0x0, 0x0, 0x3, 0xf4, 0x3f, 0x30, 0x0, 0x0,
    0xc, 0xba, 0xc0, 0x0, 0x0, 0x0, 0x5f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0xee, 0x0, 0x0,

    /* U+57 "W" */
    0x6f, 0x10, 0x0, 0xd, 0xb0, 0x0, 0x2, 0xf2,
    0x1f, 0x60, 0x0, 0x3f, 0xf1, 0x0, 0x8, 0xd0,
    0xb, 0xb0, 0x0, 0x8b, 0xe6, 0x0, 0xd, 0x70,
    0x6, 0xf0, 0x0, 0xe6, 0x9b, 0x0, 0x2f, 0x20,
    0x1, 0xf5, 0x3, 0xf1, 0x4f, 0x10, 0x8d, 0x0,
    0x0, 0xca, 0x9, 0xb0, 0xe, 0x60, 0xd8, 0x0,
    0x0, 0x6f, 0xe, 0x60, 0x9, 0xb2, 0xf3, 0x0,
    0x0, 0x1f, 0x9f, 0x10, 0x4, 0xf9, 0xd0, 0x0,
    0x0, 0xc, 0xfb, 0x0, 0x0, 0xef, 0x80, 0x0,
    0x0, 0x7, 0xf6, 0x0, 0x0, 0x9f, 0x30, 0x0,

    /* U+58 "X" */
    0x3f, 0x50, 0x0, 0xd, 0xa0, 0x8, 0xf2, 0x0,
    0x9d, 0x0, 0x0, 0xcc, 0x4, 0xf3, 0x0, 0x0,
    0x2f, 0x9e, 0x70, 0x0, 0x0, 0x6, 0xfc, 0x0,
    0x0, 0x0, 0x9, 0xfe, 0x10, 0x0, 0x0, 0x4f,
    0x4d, 0xb0, 0x0, 0x1, 0xe8, 0x2, 0xf7, 0x0,
    0xb, 0xd0, 0x0, 0x7f, 0x20, 0x7f, 0x20, 0x0,
    0xb, 0xd0,

    /* U+59 "Y" */
    0xc, 0xb0, 0x0, 0x0, 0x9c, 0x0, 0x2f, 0x50,
    0x0, 0x2f, 0x30, 0x0, 0x9e, 0x0, 0xc, 0x90,
    0x0, 0x0, 0xe8, 0x5, 0xf1, 0x0, 0x0, 0x5,
    0xf3, 0xe6, 0x0, 0x0, 0x0, 0xc, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0x40, 0x0, 0x0, 0x0,
    0x2, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x2f, 0x30,
    0x0, 0x0, 0x0, 0x2, 0xf3, 0x0, 0x0,

    /* U+5A "Z" */
    0x4f, 0xff, 0xff, 0xff, 0x91, 0x44, 0x44, 0x4a,
    0xf3, 0x0, 0x0, 0x3, 0xf6, 0x0, 0x0, 0x1,
    0xea, 0x0, 0x0, 0x0, 0xcc, 0x0, 0x0, 0x0,
    0x9e, 0x10, 0x0, 0x0, 0x6f, 0x40, 0x0, 0x0,
    0x3f, 0x70, 0x0, 0x0, 0x1e, 0xd4, 0x44, 0x44,
    0x36, 0xff, 0xff, 0xff, 0xfc,

    /* U+5B "[" */
    0x8f, 0xf6, 0x8d, 0x31, 0x8d, 0x0, 0x8d, 0x0,
    0x8d, 0x0, 0x8d, 0x0, 0x8d, 0x0, 0x8d, 0x0,
    0x8d, 0x0, 0x8d, 0x0, 0x8d, 0x0, 0x8d, 0x0,
    0x8d, 0x31, 0x8f, 0xf6,

    /* U+5C "\\" */
    0x5d, 0x0, 0x0, 0x0, 0xf2, 0x0, 0x0, 0xb,
    0x80, 0x0, 0x0, 0x6d, 0x0, 0x0, 0x1, 0xf2,
    0x0, 0x0, 0xb, 0x80, 0x0, 0x0, 0x6d, 0x0,
    0x0, 0x1, 0xf2, 0x0, 0x0, 0xb, 0x70, 0x0,
    0x0, 0x6d, 0x0, 0x0, 0x1, 0xf2, 0x0, 0x0,
    0xb, 0x70, 0x0, 0x0, 0x6d, 0x0, 0x0, 0x1,
    0xf2,

    /* U+5D "]" */
    0xbf, 0xf3, 0x25, 0xf3, 0x2, 0xf3, 0x2, 0xf3,
    0x2, 0xf3, 0x2, 0xf3, 0x2, 0xf3, 0x2, 0xf3,
    0x2, 0xf3, 0x2, 0xf3, 0x2, 0xf3, 0x2, 0xf3,
    0x25, 0xf3, 0xbf, 0xf3,

    /* U+5E "^" */
    0x0, 0xad, 0x0, 0x0, 0x1e, 0xc4, 0x0, 0x8,
    0x85, 0xa0, 0x0, 0xe2, 0xe, 0x10, 0x5b, 0x0,
    0x97, 0xb, 0x50, 0x2, 0xe0,

    /* U+5F "_" */
    0xee, 0xee, 0xee, 0xe0,

    /* U+60 "`" */
    0xb, 0xc0, 0x0, 0x9, 0xb0,

    /* U+61 "a" */
    0x4, 0xcf, 0xea, 0x10, 0xb, 0x74, 0x5d, 0xb0,
    0x0, 0x0, 0x4, 0xf0, 0x4, 0xce, 0xee, 0xf2,
    0x1f, 0x82, 0x14, 0xf2, 0x4f, 0x10, 0x4, 0xf2,
    0x1f, 0x70, 0x3d, 0xf2, 0x4, 0xdf, 0xd7, 0xf2,

    /* U+62 "b" */
    0xba, 0x0, 0x0, 0x0, 0xba, 0x0, 0x0, 0x0,
    0xba, 0x0, 0x0, 0x0, 0xba, 0x8e, 0xfc, 0x30,
    0xbf, 0xd5, 0x4b, 0xf3, 0xbe, 0x10, 0x0, 0xca,
    0xba, 0x0, 0x0, 0x7e, 0xba, 0x0, 0x0, 0x7e,
    0xbe, 0x10, 0x0, 0xca, 0xbf, 0xd5, 0x5b, 0xf3,
    0xb9, 0x8e, 0xfc, 0x30,

    /* U+63 "c" */
    0x0, 0x7d, 0xfd, 0x60, 0x9, 0xf7, 0x48, 0xf4,
    0x2f, 0x50, 0x0, 0x20, 0x5f, 0x0, 0x0, 0x0,
    0x5f, 0x0, 0x0, 0x0, 0x2f, 0x50, 0x0, 0x20,
    0x9, 0xf7, 0x48, 0xf4, 0x0, 0x7d, 0xfd, 0x60,

    /* U+64 "d" */
    0x0, 0x0, 0x0, 0x1f, 0x40, 0x0, 0x0, 0x1,
    0xf4, 0x0, 0x0, 0x0, 0x1f, 0x40, 0x8, 0xef,
    0xc4, 0xf4, 0xa, 0xf7, 0x48, 0xff, 0x42, 0xf5,
    0x0, 0x7, 0xf4, 0x5f, 0x0, 0x0, 0x2f, 0x45,
    0xf0, 0x0, 0x1, 0xf4, 0x2f, 0x50, 0x0, 0x6f,
    0x40, 0xae, 0x63, 0x7e, 0xf4, 0x0, 0x8e, 0xfc,
    0x4f, 0x40,

    /* U+65 "e" */
    0x0, 0x8e, 0xfc, 0x40, 0xa, 0xd5, 0x38, 0xf4,
    0x2f, 0x20, 0x0, 0x8c, 0x5f, 0xee, 0xee, 0xff,
    0x5f, 0x21, 0x11, 0x11, 0x2f, 0x70, 0x0, 0x10,
    0x9, 0xf8, 0x46, 0xe4, 0x0, 0x7d, 0xfe, 0x80,

    /* U+66 "f" */
    0x0, 0x9e, 0xe3, 0x5, 0xf4, 0x41, 0x8, 0xc0,
    0x0, 0xcf, 0xff, 0xf0, 0x29, 0xd3, 0x20, 0x8,
    0xd0, 0x0, 0x8, 0xd0, 0x0, 0x8, 0xd0, 0x0,
    0x8, 0xd0, 0x0, 0x8, 0xd0, 0x0, 0x8, 0xd0,
    0x0,

    /* U+67 "g" */
    0x0, 0x7e, 0xfc, 0x4e, 0x60, 0xaf, 0x74, 0x7f,
    0xf6, 0x2f, 0x50, 0x0, 0x5f, 0x65, 0xf0, 0x0,
    0x0, 0xf6, 0x5f, 0x0, 0x0, 0xf, 0x62, 0xf6,
    0x0, 0x6, 0xf6, 0x9, 0xf7, 0x47, 0xff, 0x50,
    0x7, 0xef, 0xc4, 0xf5, 0x0, 0x0, 0x0, 0x3f,
    0x20, 0xcb, 0x64, 0x6e, 0xb0, 0x3, 0xae, 0xfd,
    0x80, 0x0,

    /* U+68 "h" */
    0xba, 0x0, 0x0, 0x0, 0xba, 0x0, 0x0, 0x0,
    0xba, 0x0, 0x0, 0x0, 0xba, 0x8e, 0xfb, 0x20,
    0xbf, 0xc5, 0x6d, 0xd0, 0xbe, 0x0, 0x3, 0xf2,
    0xba, 0x0, 0x0, 0xf4, 0xba, 0x0, 0x0, 0xf5,
    0xba, 0x0, 0x0, 0xf5, 0xba, 0x0, 0x0, 0xf5,
    0xba, 0x0, 0x0, 0xf5,

    /* U+69 "i" */
    0xba, 0xa8, 0x0, 0xba, 0xba, 0xba, 0xba, 0xba,
    0xba, 0xba, 0xba,

    /* U+6A "j" */
    0x0, 0xa, 0xb0, 0x0, 0x99, 0x0, 0x0, 0x0,
    0x0, 0xab, 0x0, 0xa, 0xb0, 0x0, 0xab, 0x0,
    0xa, 0xb0, 0x0, 0xab, 0x0, 0xa, 0xb0, 0x0,
    0xab, 0x0, 0xa, 0xb0, 0x0, 0xaa, 0x6, 0x4e,
    0x71, 0xdf, 0xa0,

    /* U+6B "k" */
    0xba, 0x0, 0x0, 0x0, 0xba, 0x0, 0x0, 0x0,
    0xba, 0x0, 0x0, 0x0, 0xba, 0x0, 0x1c, 0xc0,
    0xba, 0x1, 0xcc, 0x0, 0xba, 0x1c, 0xd1, 0x0,
    0xbb, 0xcf, 0x60, 0x0, 0xbf, 0xdb, 0xe1, 0x0,
    0xbd, 0x11, 0xdc, 0x0, 0xba, 0x0, 0x3f, 0x70,
    0xba, 0x0, 0x7, 0xf3,

    /* U+6C "l" */
    0xba, 0xba, 0xba, 0xba, 0xba, 0xba, 0xba, 0xba,
    0xba, 0xba, 0xba,

    /* U+6D "m" */
    0xb9, 0x9e, 0xfa, 0x15, 0xdf, 0xd4, 0xb, 0xfb,
    0x45, 0xed, 0xe6, 0x4a, 0xf2, 0xbe, 0x0, 0x6,
    0xf6, 0x0, 0xe, 0x7b, 0xa0, 0x0, 0x4f, 0x20,
    0x0, 0xc9, 0xba, 0x0, 0x3, 0xf1, 0x0, 0xc,
    0x9b, 0xa0, 0x0, 0x3f, 0x10, 0x0, 0xc9, 0xba,
    0x0, 0x3, 0xf1, 0x0, 0xc, 0x9b, 0xa0, 0x0,
    0x3f, 0x10, 0x0, 0xc9,

    /* U+6E "n" */
    0xb9, 0x9e, 0xfb, 0x20, 0xbf, 0xb4, 0x5d, 0xd0,
    0xbe, 0x0, 0x3, 0xf2, 0xba, 0x0, 0x0, 0xf4,
    0xba, 0x0, 0x0, 0xf5, 0xba, 0x0, 0x0, 0xf5,
    0xba, 0x0, 0x0, 0xf5, 0xba, 0x0, 0x0, 0xf5,

    /* U+6F "o" */
    0x0, 0x7d, 0xfd, 0x60, 0x0, 0x9f, 0x74, 0x8f,
    0x70, 0x2f, 0x50, 0x0, 0x7f, 0x5, 0xf0, 0x0,
    0x1, 0xf3, 0x5f, 0x0, 0x0, 0x2f, 0x32, 0xf5,
    0x0, 0x7, 0xf0, 0x9, 0xf7, 0x48, 0xf7, 0x0,
    0x7, 0xdf, 0xd6, 0x0,

    /* U+70 "p" */
    0xb9, 0x8e, 0xfc, 0x30, 0xbf, 0xc4, 0x3a, 0xf3,
    0xbe, 0x10, 0x0, 0xca, 0xba, 0x0, 0x0, 0x7e,
    0xba, 0x0, 0x0, 0x7e, 0xbe, 0x10, 0x0, 0xca,
    0xbf, 0xd5, 0x5b, 0xf3, 0xba, 0x7e, 0xfc, 0x30,
    0xba, 0x0, 0x0, 0x0, 0xba, 0x0, 0x0, 0x0,
    0xba, 0x0, 0x0, 0x0,

    /* U+71 "q" */
    0x0, 0x8e, 0xfc, 0x3f, 0x40, 0xaf, 0x74, 0x8e,
    0xf4, 0x2f, 0x50, 0x0, 0x7f, 0x45, 0xf0, 0x0,
    0x1, 0xf4, 0x5f, 0x0, 0x0, 0x2f, 0x42, 0xf5,
    0x0, 0x7, 0xf4, 0xa, 0xf7, 0x48, 0xff, 0x40,
    0x8, 0xef, 0xc4, 0xf4, 0x0, 0x0, 0x0, 0x1f,
    0x40, 0x0, 0x0, 0x1, 0xf4, 0x0, 0x0, 0x0,
    0x1f, 0x40,

    /* U+72 "r" */
    0xb9, 0x8e, 0x4b, 0xfd, 0x71, 0xbe, 0x10, 0xb,
    0xb0, 0x0, 0xba, 0x0, 0xb, 0xa0, 0x0, 0xba,
    0x0, 0xb, 0xa0, 0x0,

    /* U+73 "s" */
    0x5, 0xdf, 0xea, 0x13, 0xf7, 0x35, 0xa0, 0x6f,
    0x0, 0x0, 0x1, 0xee, 0x96, 0x10, 0x1, 0x6a,
    0xef, 0x30, 0x0, 0x0, 0xd9, 0x6c, 0x64, 0x6f,
    0x62, 0xae, 0xfd, 0x70,

    /* U+74 "t" */
    0x8, 0xd0, 0x0, 0x8, 0xd0, 0x0, 0xcf, 0xff,
    0xf0, 0x29, 0xd3, 0x20, 0x8, 0xd0, 0x0, 0x8,
    0xd0, 0x0, 0x8, 0xd0, 0x0, 0x8, 0xd0, 0x0,
    0x5, 0xf5, 0x51, 0x0, 0x9f, 0xe3,

    /* U+75 "u" */
    0xc8, 0x0, 0x2, 0xf3, 0xc8, 0x0, 0x2, 0xf3,
    0xc8, 0x0, 0x2, 0xf3, 0xc8, 0x0, 0x2, 0xf3,
    0xc9, 0x0, 0x3, 0xf3, 0xab, 0x0, 0x7, 0xf3,
    0x5f, 0x83, 0x7e, 0xf3, 0x6, 0xdf, 0xc5, 0xf3,

    /* U+76 "v" */
    0xd, 0x90, 0x0, 0xa, 0xa0, 0x6e, 0x0, 0x1,
    0xf3, 0x0, 0xf5, 0x0, 0x7d, 0x0, 0x9, 0xc0,
    0xe, 0x60, 0x0, 0x3f, 0x24, 0xf0, 0x0, 0x0,
    0xc8, 0xb9, 0x0, 0x0, 0x6, 0xff, 0x30, 0x0,
    0x0, 0xf, 0xc0, 0x0,

    /* U+77 "w" */
    0xc8, 0x0, 0x6, 0xf1, 0x0, 0xd, 0x56, 0xd0,
    0x0, 0xcf, 0x60, 0x3, 0xf0, 0x1f, 0x30, 0x2f,
    0x9c, 0x0, 0x9a, 0x0, 0xb8, 0x7, 0xb2, 0xf1,
    0xe, 0x40, 0x5, 0xe0, 0xd6, 0xc, 0x74, 0xe0,
    0x0, 0xf, 0x6f, 0x0, 0x7c, 0x99, 0x0, 0x0,
    0xaf, 0xa0, 0x1, 0xff, 0x40, 0x0, 0x5, 0xf4,
    0x0, 0xb, 0xe0, 0x0,

    /* U+78 "x" */
    0x4f, 0x30, 0x7, 0xe1, 0x9, 0xd0, 0x2f, 0x40,
    0x0, 0xd9, 0xd9, 0x0, 0x0, 0x3f, 0xd0, 0x0,
    0x0, 0x4f, 0xe1, 0x0, 0x1, 0xe7, 0xbb, 0x0,
    0xb, 0xb0, 0x1e, 0x70, 0x7e, 0x10, 0x5, 0xf3,

    /* U+79 "y" */
    0xd, 0x90, 0x0, 0xa, 0xa0, 0x6f, 0x0, 0x1,
    0xf3, 0x0, 0xf6, 0x0, 0x7d, 0x0, 0x9, 0xc0,
    0xd, 0x60, 0x0, 0x2f, 0x34, 0xf0, 0x0, 0x0,
    0xc9, 0xa9, 0x0, 0x0, 0x5, 0xff, 0x20, 0x0,
    0x0, 0xe, 0xc0, 0x0, 0x0, 0x0, 0xe5, 0x0,
    0x0, 0x94, 0xad, 0x0, 0x0, 0x1b, 0xfc, 0x20,
    0x0, 0x0,

    /* U+7A "z" */
    0x5f, 0xff, 0xff, 0xa1, 0x33, 0x37, 0xf4, 0x0,
    0x1, 0xe7, 0x0, 0x0, 0xcb, 0x0, 0x0, 0x8e,
    0x10, 0x0, 0x4f, 0x40, 0x0, 0x1e, 0xa3, 0x33,
    0x26, 0xff, 0xff, 0xfc,

    /* U+7B "{" */
    0x0, 0x6e, 0xa0, 0xf, 0x92, 0x1, 0xf4, 0x0,
    0x1f, 0x40, 0x1, 0xf4, 0x0, 0x3f, 0x30, 0x3f,
    0xc0, 0x0, 0x6f, 0x30, 0x1, 0xf4, 0x0, 0x1f,
    0x40, 0x1, 0xf4, 0x0, 0x1f, 0x40, 0x0, 0xfa,
    0x20, 0x5, 0xea,

    /* U+7C "|" */
    0x8b, 0x8b, 0x8b, 0x8b, 0x8b, 0x8b, 0x8b, 0x8b,
    0x8b, 0x8b, 0x8b, 0x8b, 0x8b, 0x8b,

    /* U+7D "}" */
    0xbe, 0x50, 0x2, 0xae, 0x0, 0x5, 0xf0, 0x0,
    0x5f, 0x0, 0x5, 0xf0, 0x0, 0x4f, 0x10, 0x0,
    0xdf, 0x10, 0x4f, 0x50, 0x5, 0xf0, 0x0, 0x5f,
    0x0, 0x5, 0xf0, 0x0, 0x5f, 0x0, 0x2a, 0xe0,
    0xb, 0xe4, 0x0,

    /* U+7E "~" */
    0x7, 0xec, 0x40, 0xb4, 0x1e, 0x25, 0xdf, 0xc0,
    0x1, 0x0, 0x0, 0x0,

    /* U+B0 "°" */
    0x4, 0xcc, 0x30, 0x2b, 0x1, 0xc0, 0x57, 0x0,
    0x93, 0x2b, 0x1, 0xc0, 0x5, 0xcc, 0x30,

    /* U+2022 "•" */
    0x6, 0xa1, 0xf, 0xf6, 0xb, 0xe2,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x7b, 0xfb, 0x0,
    0x0, 0x0, 0x4, 0x9d, 0xff, 0xff, 0xd0, 0x0,
    0x3, 0xaf, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xdf, 0xd0, 0x0, 0xa,
    0xff, 0xff, 0xb6, 0x10, 0xed, 0x0, 0x0, 0xaf,
    0x94, 0x0, 0x0, 0xe, 0xd0, 0x0, 0xa, 0xf1,
    0x0, 0x0, 0x0, 0xed, 0x0, 0x0, 0xaf, 0x10,
    0x0, 0x0, 0xe, 0xd0, 0x0, 0xa, 0xf1, 0x0,
    0x0, 0x45, 0xfd, 0x0, 0x0, 0xaf, 0x10, 0x1,
    0xef, 0xff, 0xd0, 0x17, 0x9d, 0xf1, 0x0, 0x5f,
    0xff, 0xfc, 0xe, 0xff, 0xff, 0x10, 0x0, 0xaf,
    0xfd, 0x31, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x1,
    0x0, 0x3, 0xbd, 0xa3, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F008 "" */
    0x50, 0x18, 0x88, 0x88, 0x88, 0x84, 0x5, 0xfa,
    0xbf, 0xdd, 0xdd, 0xdd, 0xfd, 0xaf, 0xe4, 0x7f,
    0x10, 0x0, 0x0, 0xca, 0x4e, 0xe0, 0x4f, 0x10,
    0x0, 0x0, 0xc8, 0xe, 0xfe, 0xef, 0x10, 0x0,
    0x0, 0xcf, 0xef, 0xe0, 0x3f, 0xee, 0xee, 0xee,
    0xf8, 0xe, 0xf6, 0x8f, 0x76, 0x66, 0x66, 0xeb,
    0x6f, 0xf8, 0xaf, 0x10, 0x0, 0x0, 0xcc, 0x8f,
    0xe0, 0x3f, 0x10, 0x0, 0x0, 0xc8, 0xe, 0xfc,
    0xdf, 0x65, 0x55, 0x55, 0xee, 0xcf, 0xc2, 0x5f,
    0xff, 0xff, 0xff, 0xf9, 0x2c,

    /* U+F00B "" */
    0x57, 0x75, 0x5, 0x77, 0x77, 0x77, 0x75, 0xff,
    0xff, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xe,
    0xff, 0xff, 0xff, 0xfe, 0x1, 0x10, 0x0, 0x11,
    0x11, 0x11, 0x10, 0xef, 0xfe, 0xe, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0x68, 0x87, 0x7, 0x88, 0x88, 0x88, 0x86, 0x68,
    0x87, 0x7, 0x88, 0x88, 0x88, 0x86, 0xff, 0xff,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xdf, 0xfd, 0xd, 0xff,
    0xff, 0xff, 0xfd,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xe2, 0x2d, 0x60, 0x0, 0x1,
    0xdf, 0xfe, 0x20, 0xdf, 0xf7, 0x0, 0x1d, 0xff,
    0xe2, 0x0, 0x8f, 0xff, 0x71, 0xdf, 0xfe, 0x20,
    0x0, 0x8, 0xff, 0xfe, 0xff, 0xe2, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7d, 0x20, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x60, 0x0,
    0xb, 0xe2, 0xef, 0xf6, 0x0, 0xbf, 0xf8, 0x4f,
    0xff, 0x6b, 0xff, 0xd1, 0x4, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x5f, 0xff, 0xe1, 0x0, 0x0, 0xbf,
    0xff, 0xf6, 0x0, 0xb, 0xff, 0xdf, 0xff, 0x60,
    0xbf, 0xfd, 0x14, 0xff, 0xf5, 0xcf, 0xd1, 0x0,
    0x4f, 0xf6, 0x17, 0x10, 0x0, 0x3, 0x60,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0x21, 0xff, 0x12, 0xf7, 0x0, 0x6, 0xff, 0x61,
    0xff, 0x16, 0xff, 0x60, 0x1f, 0xf9, 0x1, 0xff,
    0x10, 0x9f, 0xf1, 0x6f, 0xe0, 0x1, 0xff, 0x10,
    0xe, 0xf6, 0xaf, 0x80, 0x1, 0xff, 0x10, 0x8,
    0xfa, 0xcf, 0x60, 0x1, 0xff, 0x10, 0x6, 0xfc,
    0xaf, 0x80, 0x0, 0xaa, 0x0, 0x8, 0xfb, 0x7f,
    0xd0, 0x0, 0x0, 0x0, 0xd, 0xf7, 0x1f, 0xf8,
    0x0, 0x0, 0x0, 0x8f, 0xf1, 0x7, 0xff, 0x91,
    0x0, 0x2a, 0xff, 0x70, 0x0, 0x9f, 0xff, 0xee,
    0xff, 0xf9, 0x0, 0x0, 0x5, 0xcf, 0xff, 0xfd,
    0x50, 0x0, 0x0, 0x0, 0x2, 0x44, 0x20, 0x0,
    0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xc0, 0x0, 0x0, 0x3, 0xd6, 0xdf,
    0xff, 0xfd, 0x6d, 0x30, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x5f, 0xff, 0xff, 0xaa, 0xff,
    0xff, 0xf5, 0x1a, 0xff, 0xf4, 0x0, 0x4f, 0xff,
    0xa1, 0x3, 0xff, 0xd0, 0x0, 0xd, 0xff, 0x30,
    0x4, 0xff, 0xf0, 0x0, 0xf, 0xff, 0x40, 0x4f,
    0xff, 0xfb, 0x22, 0xbf, 0xff, 0xf4, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x9, 0xfe, 0xff,
    0xff, 0xff, 0xef, 0x90, 0x0, 0x50, 0x5e, 0xff,
    0xe5, 0x5, 0x0, 0x0, 0x0, 0xc, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x77, 0x40, 0x0,
    0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x3, 0x10, 0x3, 0x41, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xf5, 0xd, 0xf5, 0x0,
    0x0, 0x0, 0x1b, 0xfd, 0xff, 0x8d, 0xf5, 0x0,
    0x0, 0x2, 0xdf, 0xb1, 0x2d, 0xff, 0xf5, 0x0,
    0x0, 0x4f, 0xf8, 0x3e, 0xc2, 0xbf, 0xf5, 0x0,
    0x7, 0xff, 0x55, 0xff, 0xfe, 0x39, 0xfe, 0x40,
    0x9f, 0xe3, 0x8f, 0xff, 0xff, 0xf5, 0x6f, 0xf6,
    0xac, 0x2a, 0xff, 0xff, 0xff, 0xff, 0x73, 0xe6,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x6f, 0xff, 0xd7, 0x7f, 0xff, 0xf2, 0x0,
    0x0, 0x6f, 0xff, 0x90, 0xd, 0xff, 0xf2, 0x0,
    0x0, 0x6f, 0xff, 0x90, 0xd, 0xff, 0xf2, 0x0,
    0x0, 0x4f, 0xff, 0x70, 0xb, 0xff, 0xe1, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x33, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xe2, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x2d, 0xff, 0xe2, 0x0, 0x0, 0x79, 0x99,
    0x82, 0xde, 0x28, 0x99, 0x97, 0xff, 0xff, 0xfb,
    0x22, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xb3, 0xcf, 0xac, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xca,

    /* U+F01C "" */
    0x0, 0x6, 0xbb, 0xbb, 0xbb, 0xba, 0x30, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x0,
    0x0, 0xef, 0x30, 0x0, 0x0, 0x6, 0xfb, 0x0,
    0x9, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x50,
    0x4f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xe1,
    0xdf, 0x84, 0x42, 0x0, 0x0, 0x34, 0x4b, 0xf9,
    0xff, 0xff, 0xfd, 0x0, 0x1, 0xff, 0xff, 0xfb,
    0xff, 0xff, 0xff, 0x98, 0x8b, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x33, 0x0,
    0x1, 0x8d, 0xff, 0xc6, 0x0, 0xef, 0x0, 0x4e,
    0xff, 0xff, 0xff, 0xe4, 0xdf, 0x4, 0xff, 0xb3,
    0x0, 0x4c, 0xff, 0xff, 0xe, 0xf9, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0x6f, 0xc0, 0x0, 0x1, 0xff,
    0xff, 0xff, 0x8e, 0x50, 0x0, 0x1, 0xde, 0xee,
    0xed, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x22, 0x22, 0x22, 0x0, 0x0, 0x0, 0x21, 0xff,
    0xff, 0xff, 0x10, 0x0, 0x8, 0xf8, 0xff, 0xfb,
    0xbc, 0x10, 0x0, 0x1e, 0xf4, 0xff, 0xfc, 0x10,
    0x0, 0x1, 0xdf, 0xc0, 0xfe, 0xef, 0xe8, 0x44,
    0x8e, 0xfe, 0x10, 0xfe, 0x1a, 0xff, 0xff, 0xff,
    0xc1, 0x0, 0xfd, 0x0, 0x28, 0xbb, 0x94, 0x0,
    0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x2, 0x70, 0x0, 0x2, 0xef, 0x0,
    0x2, 0xef, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x34, 0x47, 0xff, 0xf0,
    0x0, 0x5, 0xff, 0x0, 0x0, 0x5, 0xc0, 0x0,
    0x0, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x2, 0x70, 0x0, 0x0, 0x0, 0x2,
    0xef, 0x0, 0x0, 0x0, 0x2, 0xef, 0xf0, 0x0,
    0xd, 0xff, 0xff, 0xff, 0x2, 0x20, 0xff, 0xff,
    0xff, 0xf0, 0x8e, 0x1f, 0xff, 0xff, 0xff, 0x0,
    0xe7, 0xff, 0xff, 0xff, 0xf0, 0x3f, 0x5f, 0xff,
    0xff, 0xff, 0x8, 0x90, 0x34, 0x47, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0x70, 0x0,
    0x0, 0x0, 0x2, 0x70, 0x0, 0x5, 0xfa, 0x0,
    0x0, 0x0, 0x2e, 0xf0, 0x0, 0x81, 0x4f, 0x60,
    0x0, 0x2, 0xef, 0xf0, 0x1, 0xdd, 0x7, 0xf0,
    0xdf, 0xff, 0xff, 0xf0, 0x32, 0x1e, 0x80, 0xf6,
    0xff, 0xff, 0xff, 0xf0, 0x8e, 0x27, 0xe0, 0xb9,
    0xff, 0xff, 0xff, 0xf0, 0xe, 0x73, 0xf1, 0x9b,
    0xff, 0xff, 0xff, 0xf0, 0x3f, 0x54, 0xf0, 0x9a,
    0xff, 0xff, 0xff, 0xf0, 0x89, 0xa, 0xc0, 0xd8,
    0x34, 0x47, 0xff, 0xf0, 0x0, 0x7f, 0x43, 0xf3,
    0x0, 0x0, 0x5f, 0xf0, 0x2, 0xf6, 0xc, 0xb0,
    0x0, 0x0, 0x5, 0xc0, 0x0, 0x0, 0xbf, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xe3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x10, 0x0,

    /* U+F03E "" */
    0x37, 0x88, 0x88, 0x88, 0x88, 0x88, 0x73, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0x32,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x7f,
    0xff, 0xfd, 0xff, 0xff, 0xfd, 0x10, 0xcf, 0xff,
    0xa0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x7, 0xff, 0xff, 0xf3, 0x5f, 0xa0, 0x0, 0x0,
    0xcf, 0xff, 0x30, 0x3, 0x0, 0x0, 0x0, 0xcf,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xff, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9,

    /* U+F048 "" */
    0x4, 0x30, 0x0, 0x0, 0x31, 0x1f, 0xe0, 0x0,
    0x6, 0xf9, 0x1f, 0xe0, 0x0, 0x7f, 0xfa, 0x1f,
    0xe0, 0x9, 0xff, 0xfa, 0x1f, 0xe0, 0xaf, 0xff,
    0xfa, 0x1f, 0xeb, 0xff, 0xff, 0xfa, 0x1f, 0xff,
    0xff, 0xff, 0xfa, 0x1f, 0xff, 0xff, 0xff, 0xfa,
    0x1f, 0xe6, 0xff, 0xff, 0xfa, 0x1f, 0xe0, 0x5f,
    0xff, 0xfa, 0x1f, 0xe0, 0x4, 0xff, 0xfa, 0x1f,
    0xe0, 0x0, 0x3e, 0xfa, 0xf, 0xd0, 0x0, 0x2,
    0xd7, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xfb,
    0x20, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xe6, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xfc, 0x30, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe6, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xb2, 0x0, 0xf,
    0xff, 0xff, 0xfd, 0x40, 0x0, 0x0, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0xf, 0xff, 0xa1, 0x0,
    0x0, 0x0, 0x0, 0x6a, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F04C "" */
    0x14, 0x44, 0x20, 0x1, 0x44, 0x42, 0xd, 0xff,
    0xff, 0x10, 0xdf, 0xff, 0xf1, 0xff, 0xff, 0xf3,
    0xf, 0xff, 0xff, 0x3f, 0xff, 0xff, 0x40, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xf4, 0xf, 0xff, 0xff,
    0x4f, 0xff, 0xff, 0x40, 0xff, 0xff, 0xf4, 0xff,
    0xff, 0xf4, 0xf, 0xff, 0xff, 0x4f, 0xff, 0xff,
    0x40, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xf4, 0xf,
    0xff, 0xff, 0x4f, 0xff, 0xff, 0x40, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0xf4, 0xf, 0xff, 0xff, 0x4f,
    0xff, 0xff, 0x30, 0xff, 0xff, 0xf3, 0x9f, 0xff,
    0xc0, 0x9, 0xff, 0xfc, 0x0,

    /* U+F04D "" */
    0x14, 0x44, 0x44, 0x44, 0x44, 0x42, 0xd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0,

    /* U+F051 "" */
    0x2, 0x10, 0x0, 0x0, 0x42, 0xf, 0xe2, 0x0,
    0x3, 0xfb, 0xf, 0xfe, 0x30, 0x4, 0xfb, 0xf,
    0xff, 0xf4, 0x4, 0xfb, 0xf, 0xff, 0xff, 0x54,
    0xfb, 0xf, 0xff, 0xff, 0xfa, 0xfb, 0xf, 0xff,
    0xff, 0xff, 0xfb, 0xf, 0xff, 0xff, 0xff, 0xfb,
    0xf, 0xff, 0xff, 0xd6, 0xfb, 0xf, 0xff, 0xfd,
    0x14, 0xfb, 0xf, 0xff, 0xc1, 0x4, 0xfb, 0xf,
    0xfb, 0x0, 0x4, 0xfb, 0xc, 0xa0, 0x0, 0x3,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x12, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x1, 0xef,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x1d, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x3, 0x99, 0x99, 0x99, 0x99, 0x99, 0x50, 0x5,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x70, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd1,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0x90, 0x0, 0x0, 0x3f, 0xfc, 0x0, 0x0, 0x3f,
    0xfd, 0x10, 0x0, 0x3f, 0xfd, 0x10, 0x0, 0x3f,
    0xfd, 0x10, 0x0, 0x1f, 0xfd, 0x10, 0x0, 0x0,
    0xcf, 0xf4, 0x0, 0x0, 0x0, 0xcf, 0xf4, 0x0,
    0x0, 0x0, 0xcf, 0xf4, 0x0, 0x0, 0x0, 0xcf,
    0xf4, 0x0, 0x0, 0x0, 0xcf, 0xe0, 0x0, 0x0,
    0x0, 0xa4, 0x0,

    /* U+F054 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcd, 0x10, 0x0,
    0x0, 0x1f, 0xfd, 0x10, 0x0, 0x0, 0x3f, 0xfd,
    0x10, 0x0, 0x0, 0x3f, 0xfd, 0x10, 0x0, 0x0,
    0x3f, 0xfd, 0x10, 0x0, 0x0, 0x3f, 0xfd, 0x0,
    0x0, 0x8, 0xff, 0x90, 0x0, 0x8, 0xff, 0x90,
    0x0, 0x8, 0xff, 0x90, 0x0, 0x8, 0xff, 0x90,
    0x0, 0x2, 0xff, 0x90, 0x0, 0x0, 0x7, 0x80,
    0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x4, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf7, 0x0, 0x0,
    0x6, 0x99, 0x9a, 0xff, 0xc9, 0x99, 0x80, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x3d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x1, 0x11, 0x3f, 0xf7,
    0x11, 0x10, 0x0, 0x0, 0x3, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xd3, 0x0, 0x0, 0x0,

    /* U+F068 "" */
    0x69, 0x99, 0x99, 0x99, 0x99, 0x98, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F06E "" */
    0x0, 0x0, 0x1, 0x56, 0x64, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xbf, 0xfe, 0xef, 0xf9, 0x10, 0x0,
    0x0, 0x7f, 0xfa, 0x10, 0x3, 0xdf, 0xe4, 0x0,
    0x8, 0xff, 0xa0, 0x9, 0xb4, 0x1e, 0xff, 0x50,
    0x4f, 0xff, 0x20, 0xb, 0xff, 0x26, 0xff, 0xe1,
    0xef, 0xff, 0x9, 0xcf, 0xff, 0x63, 0xff, 0xfa,
    0xbf, 0xff, 0x9, 0xff, 0xff, 0x54, 0xff, 0xf6,
    0x1e, 0xff, 0x51, 0xdf, 0xfb, 0x9, 0xff, 0xb0,
    0x3, 0xef, 0xe2, 0x4, 0x30, 0x5f, 0xfc, 0x10,
    0x0, 0x2c, 0xff, 0x95, 0x6a, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x49, 0xdf, 0xfd, 0x92, 0x0, 0x0,

    /* U+F070 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcd, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf5, 0x0, 0x14, 0x66, 0x40,
    0x0, 0x0, 0x0, 0x4, 0xef, 0xac, 0xff, 0xef,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xa1,
    0x0, 0x4d, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x9f,
    0xf5, 0xab, 0x31, 0xef, 0xf4, 0x0, 0x7, 0xb1,
    0x5, 0xff, 0xff, 0xe1, 0x7f, 0xfe, 0x10, 0xf,
    0xfe, 0x30, 0x2d, 0xff, 0xf5, 0x4f, 0xff, 0x90,
    0xc, 0xff, 0xe0, 0x0, 0xaf, 0xf6, 0x5f, 0xff,
    0x60, 0x2, 0xff, 0xf4, 0x0, 0x6, 0xff, 0xef,
    0xfb, 0x0, 0x0, 0x4f, 0xfd, 0x10, 0x0, 0x3e,
    0xff, 0xc0, 0x0, 0x0, 0x2, 0xdf, 0xe8, 0x54,
    0x1, 0xbf, 0xe3, 0x0, 0x0, 0x0, 0x5, 0xae,
    0xff, 0x60, 0x7, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4e, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xa1,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3e, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfc, 0xcf,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfb, 0x0,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xc0,
    0xf, 0xff, 0x70, 0x0, 0x0, 0x4, 0xff, 0xfd,
    0x1, 0xff, 0xff, 0x10, 0x0, 0x0, 0xdf, 0xff,
    0xe0, 0x2f, 0xff, 0xfa, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0x9b, 0xff, 0xff, 0xf3, 0x0, 0x1f, 0xff,
    0xff, 0xb0, 0xe, 0xff, 0xff, 0xc0, 0xa, 0xff,
    0xff, 0xfe, 0x24, 0xff, 0xff, 0xff, 0x60, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x6,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcb, 0x30,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x36, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0x80, 0xdd, 0xdb,
    0x0, 0x0, 0x8d, 0xef, 0xf8, 0xff, 0xff, 0xb0,
    0x7, 0xff, 0xff, 0xfd, 0x55, 0x6f, 0xf4, 0x6f,
    0xf8, 0xaf, 0xe2, 0x0, 0x5, 0x74, 0xff, 0x90,
    0x7e, 0x20, 0x0, 0x0, 0x3f, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xb2, 0x50, 0x4a, 0x0,
    0x1, 0x2e, 0xfd, 0x1d, 0xf4, 0x8f, 0xb0, 0xff,
    0xff, 0xd1, 0xb, 0xff, 0xff, 0xfb, 0xff, 0xfe,
    0x20, 0x0, 0xcf, 0xff, 0xfb, 0x12, 0x21, 0x0,
    0x0, 0x2, 0x9f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5b, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x7, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x8, 0xff, 0x95, 0xff,
    0xb0, 0x0, 0x8, 0xff, 0x90, 0x5, 0xff, 0xb0,
    0x7, 0xff, 0x90, 0x0, 0x5, 0xff, 0xb0, 0x9f,
    0x90, 0x0, 0x0, 0x5, 0xfd, 0x0, 0x40, 0x0,
    0x0, 0x0, 0x3, 0x10,

    /* U+F078 "" */
    0x4c, 0x20, 0x0, 0x0, 0x0, 0xb6, 0xb, 0xfe,
    0x20, 0x0, 0x0, 0xcf, 0xf0, 0x2e, 0xfe, 0x20,
    0x0, 0xcf, 0xf4, 0x0, 0x2e, 0xfe, 0x20, 0xcf,
    0xf4, 0x0, 0x0, 0x2e, 0xfe, 0xcf, 0xf4, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x13, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x8, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf3, 0x8, 0xbb, 0xbb, 0xbb,
    0x90, 0x0, 0xb, 0xff, 0xff, 0x39, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x8f, 0xcf, 0xcf, 0xf0, 0x0,
    0x0, 0xa, 0xf1, 0x0, 0x38, 0x2f, 0x94, 0x80,
    0x0, 0x0, 0xa, 0xf1, 0x0, 0x0, 0x2f, 0x90,
    0x0, 0x0, 0x0, 0xa, 0xf1, 0x0, 0x0, 0x2f,
    0x90, 0x0, 0x0, 0x3, 0xa, 0xf1, 0x30, 0x0,
    0x2f, 0x90, 0x0, 0x0, 0x1f, 0xcb, 0xf8, 0xf8,
    0x0, 0x2f, 0xeb, 0xbb, 0xbb, 0x39, 0xff, 0xff,
    0xe2, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xb0, 0x9f,
    0xfd, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xd1, 0x0,

    /* U+F07B "" */
    0x37, 0x88, 0x87, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xfd, 0xcc, 0xcc, 0xb6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xdd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x1d, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x1, 0x1c, 0xff, 0xc1, 0x10,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xc0, 0x0, 0x0, 0x79, 0x99,
    0x3b, 0xff, 0xb3, 0x99, 0x97, 0xff, 0xff, 0xb2,
    0x44, 0x2b, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xdd,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xb3, 0xcf, 0xac, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xca,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xc7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xef, 0xf3, 0x0, 0x0, 0x4a, 0x30, 0x2,
    0xdf, 0xf8, 0x0, 0x5, 0xdf, 0xfe, 0x15, 0xef,
    0xfb, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xa2, 0x0, 0x0,
    0x0, 0x2, 0xba, 0x85, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F0C4 "" */
    0x4, 0x86, 0x0, 0x0, 0x0, 0x10, 0x6, 0xff,
    0xfa, 0x0, 0x2, 0xdf, 0xd1, 0xef, 0x3c, 0xf1,
    0x1, 0xdf, 0xfa, 0xe, 0xe0, 0xaf, 0x21, 0xdf,
    0xfa, 0x0, 0x9f, 0xef, 0xf6, 0xdf, 0xfa, 0x0,
    0x0, 0x8d, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x48, 0xef,
    0xff, 0xf6, 0x0, 0x0, 0x6f, 0xff, 0xfb, 0xff,
    0xf6, 0x0, 0xe, 0xf3, 0xcf, 0x23, 0xff, 0xf6,
    0x0, 0xee, 0xa, 0xf2, 0x4, 0xff, 0xf6, 0x9,
    0xfe, 0xfc, 0x0, 0x4, 0xff, 0xf1, 0x8, 0xda,
    0x10, 0x0, 0x2, 0x62, 0x0,

    /* U+F0C5 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xf9, 0x87, 0x0, 0x0, 0x8, 0xff,
    0xff, 0x98, 0xf7, 0x8, 0xa6, 0x8f, 0xff, 0xf9,
    0x59, 0x90, 0xff, 0xa8, 0xff, 0xff, 0xfc, 0xcc,
    0xf, 0xfa, 0x8f, 0xff, 0xff, 0xff, 0xf1, 0xff,
    0xa8, 0xff, 0xff, 0xff, 0xff, 0x1f, 0xfa, 0x8f,
    0xff, 0xff, 0xff, 0xf1, 0xff, 0xa8, 0xff, 0xff,
    0xff, 0xff, 0x1f, 0xfa, 0x8f, 0xff, 0xff, 0xff,
    0xf1, 0xff, 0xa8, 0xff, 0xff, 0xff, 0xff, 0x1f,
    0xfa, 0x7f, 0xff, 0xff, 0xff, 0xf0, 0xff, 0xe3,
    0x12, 0x22, 0x22, 0x21, 0xf, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0xac, 0xcc, 0xcc, 0xcb, 0x50,
    0x0, 0x0,

    /* U+F0C7 "" */
    0x49, 0x99, 0x99, 0x99, 0x95, 0x0, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0xfd, 0x22, 0x22,
    0x22, 0x4f, 0xf6, 0xf, 0xc0, 0x0, 0x0, 0x1,
    0xff, 0xf3, 0xfc, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0x6f, 0xc0, 0x0, 0x0, 0x2, 0xff, 0xf6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x6f, 0xff, 0xff,
    0xdc, 0xff, 0xff, 0xf6, 0xff, 0xff, 0xb0, 0x5,
    0xff, 0xff, 0x6f, 0xff, 0xf6, 0x0, 0xf, 0xff,
    0xf6, 0xff, 0xff, 0xc0, 0x6, 0xff, 0xff, 0x6f,
    0xff, 0xff, 0xed, 0xff, 0xff, 0xf6, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x10,

    /* U+F0E7 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xf0, 0x0, 0x4, 0xff, 0xff, 0xd0, 0x0, 0x6,
    0xff, 0xff, 0x80, 0x0, 0x8, 0xff, 0xff, 0x30,
    0x0, 0xa, 0xff, 0xff, 0xaa, 0xa6, 0xc, 0xff,
    0xff, 0xff, 0xf8, 0xe, 0xff, 0xff, 0xff, 0xe1,
    0xb, 0xdd, 0xdf, 0xff, 0x60, 0x0, 0x0, 0x4f,
    0xfd, 0x0, 0x0, 0x0, 0x7f, 0xf3, 0x0, 0x0,
    0x0, 0xbf, 0xa0, 0x0, 0x0, 0x0, 0xff, 0x10,
    0x0, 0x0, 0x3, 0xf8, 0x0, 0x0, 0x0, 0x3,
    0xc0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x4, 0x55,
    0xef, 0xb5, 0x52, 0x0, 0x0, 0xff, 0xfd, 0x1f,
    0xff, 0xb0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0xff, 0xff, 0x53, 0x33, 0x20, 0x0,
    0xf, 0xff, 0x97, 0xff, 0xfb, 0x57, 0x0, 0xff,
    0xf8, 0xaf, 0xff, 0xc6, 0xf8, 0xf, 0xff, 0x8a,
    0xff, 0xfc, 0x4a, 0xa1, 0xff, 0xf8, 0xaf, 0xff,
    0xe3, 0x22, 0xf, 0xff, 0x8a, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xf8, 0xaf, 0xff, 0xff, 0xff, 0x4f,
    0xff, 0x8a, 0xff, 0xff, 0xff, 0xf4, 0x35, 0x52,
    0xaf, 0xff, 0xff, 0xff, 0x40, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xfe, 0x20,

    /* U+F0F3 "" */
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0x0, 0x0, 0x0, 0x0, 0x1, 0x8f,
    0xfa, 0x30, 0x0, 0x0, 0x2, 0xef, 0xff, 0xff,
    0x50, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x3,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0x20, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x2, 0x22,
    0x22, 0x22, 0x22, 0x21, 0x0, 0x0, 0x8, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xa2, 0x0,
    0x0, 0x0,

    /* U+F11C "" */
    0x5b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xa3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xfc, 0xc, 0x30, 0xe1, 0x1d, 0xd, 0x11, 0xfc,
    0xfc, 0xb, 0x30, 0xe0, 0x1d, 0xd, 0x10, 0xfc,
    0xff, 0xfe, 0xff, 0xef, 0xfe, 0xfe, 0xef, 0xfc,
    0xff, 0xf1, 0x5a, 0x8, 0x70, 0xa0, 0x5f, 0xfc,
    0xff, 0xf3, 0x7b, 0x29, 0x92, 0xc2, 0x7f, 0xfc,
    0xff, 0xbf, 0xcb, 0xbb, 0xbb, 0xbf, 0xcb, 0xfc,
    0xfc, 0xb, 0x20, 0x0, 0x0, 0xd, 0x0, 0xfc,
    0xff, 0xcf, 0xcc, 0xcc, 0xcc, 0xcf, 0xcc, 0xfb,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xdf, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x18, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x29, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x3b, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x4, 0x9a, 0xaa, 0xaf, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xb3, 0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x35, 0x55, 0x55, 0x2, 0x0, 0xf, 0xff, 0xff,
    0xf2, 0xf4, 0x0, 0xff, 0xff, 0xff, 0x2f, 0xf4,
    0xf, 0xff, 0xff, 0xf2, 0xff, 0xf3, 0xff, 0xff,
    0xff, 0x32, 0x22, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x8a, 0xaa, 0xaa,
    0xaa, 0xaa, 0x30,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x24, 0x55, 0x31, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xaf, 0xff, 0xff, 0xff, 0xc7,
    0x0, 0x0, 0x2, 0xbf, 0xff, 0xfe, 0xde, 0xff,
    0xff, 0xf6, 0x0, 0x5f, 0xff, 0xb5, 0x10, 0x0,
    0x3, 0x8e, 0xff, 0xb0, 0xdf, 0xd3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xf5, 0x18, 0x0, 0x5,
    0xae, 0xfe, 0xc8, 0x10, 0x4, 0x60, 0x0, 0x2,
    0xdf, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0xc, 0xff, 0x95, 0x34, 0x7d, 0xff, 0x40, 0x0,
    0x0, 0x2, 0xa2, 0x0, 0x0, 0x0, 0x77, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x96, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xda, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x5b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xba,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0xfc, 0x12, 0x22, 0x22, 0x22, 0x22,
    0x22, 0xf, 0xf7, 0xfc, 0x5f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x2c, 0xfa, 0xfc, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x21, 0xfa, 0xfc, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x27, 0xfa, 0xfc, 0x26,
    0x66, 0x66, 0x66, 0x66, 0x66, 0x1f, 0xfa, 0xfe,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbf, 0xb1,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F241 "" */
    0x5b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xba,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0xfc, 0x12, 0x22, 0x22, 0x22, 0x21,
    0x0, 0xf, 0xf7, 0xfc, 0x5f, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0xc, 0xfa, 0xfc, 0x5f, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x1, 0xfa, 0xfc, 0x5f, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x7, 0xfa, 0xfc, 0x26,
    0x66, 0x66, 0x66, 0x63, 0x0, 0xf, 0xfa, 0xfe,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbf, 0xb1,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F242 "" */
    0x5b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xba,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0xfc, 0x12, 0x22, 0x22, 0x10, 0x0,
    0x0, 0xf, 0xf7, 0xfc, 0x5f, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0xc, 0xfa, 0xfc, 0x5f, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x1, 0xfa, 0xfc, 0x5f, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x7, 0xfa, 0xfc, 0x26,
    0x66, 0x66, 0x50, 0x0, 0x0, 0xf, 0xfa, 0xfe,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbf, 0xb1,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F243 "" */
    0x5b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xba,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0xfc, 0x12, 0x22, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf7, 0xfc, 0x5f, 0xff, 0x30, 0x0,
    0x0, 0x0, 0xc, 0xfa, 0xfc, 0x5f, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x1, 0xfa, 0xfc, 0x5f, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x7, 0xfa, 0xfc, 0x26,
    0x66, 0x10, 0x0, 0x0, 0x0, 0xf, 0xfa, 0xfe,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbf, 0xb1,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F244 "" */
    0x5b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xba,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf7, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xfa, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xfa, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xfa, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfa, 0xfe,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbf, 0xb1,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x7, 0xb2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xdf, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa9, 0x3d, 0xf5,
    0x0, 0x0, 0x0, 0x4, 0x40, 0x2, 0xe0, 0x0,
    0x10, 0x0, 0x0, 0x0, 0xaf, 0xf8, 0xb, 0x60,
    0x0, 0x0, 0x0, 0x6c, 0x30, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xaf, 0xf9,
    0x0, 0xc, 0x50, 0x0, 0x0, 0x6d, 0x40, 0x5,
    0x50, 0x0, 0x4, 0xc0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc4, 0x3e, 0xe8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xef, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0x7,
    0xef, 0xff, 0xb3, 0x0, 0x0, 0xaf, 0xfd, 0x8f,
    0xff, 0x20, 0x4, 0xff, 0xfd, 0x9, 0xff, 0xb0,
    0xa, 0xfe, 0xfd, 0x12, 0xaf, 0xf0, 0xe, 0xf5,
    0x5d, 0x2c, 0xe, 0xf3, 0xf, 0xff, 0x33, 0x12,
    0x9f, 0xf5, 0xf, 0xff, 0xf3, 0x7, 0xff, 0xf6,
    0xf, 0xff, 0xe2, 0x6, 0xff, 0xf6, 0xf, 0xfe,
    0x24, 0x13, 0x7f, 0xf5, 0xd, 0xf5, 0x7d, 0x2c,
    0xd, 0xf3, 0xa, 0xff, 0xfd, 0x11, 0xbf, 0xf0,
    0x3, 0xff, 0xfe, 0xb, 0xff, 0xa0, 0x0, 0x7f,
    0xfe, 0xbf, 0xfe, 0x10, 0x0, 0x3, 0xac, 0xdc,
    0x81, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x34, 0x43, 0x0, 0x0, 0x5, 0x66,
    0x7f, 0xff, 0xf9, 0x66, 0x50, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x35, 0x66, 0x66, 0x66, 0x66,
    0x66, 0x50, 0x1c, 0xcc, 0xcc, 0xcc, 0xcc, 0xc4,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x2f,
    0xf3, 0xfb, 0x7f, 0x6d, 0xf6, 0x2, 0xff, 0x2f,
    0xb7, 0xf5, 0xdf, 0x60, 0x2f, 0xf2, 0xfb, 0x7f,
    0x5d, 0xf6, 0x2, 0xff, 0x2f, 0xb7, 0xf5, 0xdf,
    0x60, 0x2f, 0xf2, 0xfb, 0x7f, 0x5d, 0xf6, 0x2,
    0xff, 0x2f, 0xb7, 0xf5, 0xdf, 0x60, 0x2f, 0xf3,
    0xfb, 0x7f, 0x6d, 0xf6, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x7, 0xbc, 0xcc, 0xcc, 0xcc,
    0x90, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x4, 0x39, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x39, 0xff, 0xa0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0x39, 0xb0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xa8, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x17, 0x88, 0x88, 0x88, 0x88, 0x87,
    0x40, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x3e, 0xff, 0xff, 0xcf, 0xff,
    0xcf, 0xff, 0xf7, 0x3, 0xef, 0xff, 0xf9, 0x8,
    0xf8, 0x9, 0xff, 0xf8, 0x3e, 0xff, 0xff, 0xfe,
    0x20, 0x40, 0x2e, 0xff, 0xf8, 0xdf, 0xff, 0xff,
    0xff, 0xe1, 0x1, 0xef, 0xff, 0xf8, 0x9f, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x8f, 0xff, 0xf8, 0x9,
    0xff, 0xff, 0xf9, 0x2, 0xc2, 0x9, 0xff, 0xf8,
    0x0, 0x9f, 0xff, 0xfe, 0x4e, 0xfe, 0x4e, 0xff,
    0xf8, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc1,

    /* U+F7C2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xef,
    0xff, 0xff, 0xe2, 0x3, 0xfb, 0xfb, 0xce, 0xbf,
    0xa4, 0xff, 0x1d, 0x3, 0xa1, 0xfa, 0xff, 0xf1,
    0xd0, 0x3a, 0x1f, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xad,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x29, 0xaa, 0xaa,
    0xaa, 0xa8, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf1, 0x0,
    0x8, 0x20, 0x0, 0x0, 0x1, 0xff, 0x10, 0xb,
    0xf7, 0x0, 0x0, 0x0, 0x2f, 0xf1, 0xc, 0xff,
    0x94, 0x44, 0x44, 0x45, 0xff, 0x1b, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x0, 0x7f, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0
};

/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 60, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 60, .box_w = 3, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15, .adv_w = 88, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 28, .adv_w = 157, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 78, .adv_w = 139, .box_w = 9, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 146, .adv_w = 189, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 206, .adv_w = 154, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 261, .adv_w = 47, .box_w = 3, .box_h = 5, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 269, .adv_w = 75, .box_w = 4, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 297, .adv_w = 76, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 325, .adv_w = 90, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 343, .adv_w = 130, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 371, .adv_w = 51, .box_w = 3, .box_h = 5, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 379, .adv_w = 86, .box_w = 5, .box_h = 3, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 387, .adv_w = 51, .box_w = 3, .box_h = 3, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 392, .adv_w = 79, .box_w = 7, .box_h = 14, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 441, .adv_w = 149, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 486, .adv_w = 83, .box_w = 4, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 506, .adv_w = 129, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 546, .adv_w = 128, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 586, .adv_w = 150, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 636, .adv_w = 129, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 676, .adv_w = 138, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 721, .adv_w = 134, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 761, .adv_w = 144, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 806, .adv_w = 138, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 846, .adv_w = 51, .box_w = 3, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 858, .adv_w = 51, .box_w = 3, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 875, .adv_w = 130, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 907, .adv_w = 130, .box_w = 8, .box_h = 6, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 931, .adv_w = 130, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 963, .adv_w = 128, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1003, .adv_w = 232, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1094, .adv_w = 164, .box_w = 12, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1154, .adv_w = 170, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1199, .adv_w = 162, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1249, .adv_w = 185, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1299, .adv_w = 150, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1339, .adv_w = 142, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1379, .adv_w = 173, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1429, .adv_w = 182, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1474, .adv_w = 69, .box_w = 2, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1484, .adv_w = 115, .box_w = 7, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1519, .adv_w = 161, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1569, .adv_w = 133, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1609, .adv_w = 214, .box_w = 11, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1664, .adv_w = 182, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1709, .adv_w = 188, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1769, .adv_w = 162, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1814, .adv_w = 188, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1892, .adv_w = 163, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1937, .adv_w = 139, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1982, .adv_w = 131, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2027, .adv_w = 177, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2072, .adv_w = 159, .box_w = 11, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2127, .adv_w = 252, .box_w = 16, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2207, .adv_w = 151, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2257, .adv_w = 145, .box_w = 11, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2312, .adv_w = 147, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2357, .adv_w = 75, .box_w = 4, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2385, .adv_w = 79, .box_w = 7, .box_h = 14, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 2434, .adv_w = 75, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2462, .adv_w = 131, .box_w = 7, .box_h = 6, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 2483, .adv_w = 112, .box_w = 7, .box_h = 1, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2487, .adv_w = 134, .box_w = 5, .box_h = 2, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 2492, .adv_w = 134, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2524, .adv_w = 153, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2568, .adv_w = 128, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2600, .adv_w = 153, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2650, .adv_w = 137, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2682, .adv_w = 79, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2715, .adv_w = 155, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2765, .adv_w = 153, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2809, .adv_w = 62, .box_w = 2, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2820, .adv_w = 64, .box_w = 5, .box_h = 14, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 2855, .adv_w = 138, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2899, .adv_w = 62, .box_w = 2, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2910, .adv_w = 237, .box_w = 13, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2962, .adv_w = 153, .box_w = 8, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2994, .adv_w = 142, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3030, .adv_w = 153, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3074, .adv_w = 153, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3124, .adv_w = 92, .box_w = 5, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3144, .adv_w = 112, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3172, .adv_w = 93, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3202, .adv_w = 152, .box_w = 8, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3234, .adv_w = 125, .box_w = 9, .box_h = 8, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 3270, .adv_w = 201, .box_w = 13, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3322, .adv_w = 124, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3354, .adv_w = 125, .box_w = 9, .box_h = 11, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 3404, .adv_w = 117, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3432, .adv_w = 79, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3467, .adv_w = 67, .box_w = 2, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3481, .adv_w = 79, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3516, .adv_w = 130, .box_w = 8, .box_h = 3, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 3528, .adv_w = 94, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 3543, .adv_w = 70, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 3549, .adv_w = 224, .box_w = 15, .box_h = 15, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 3662, .adv_w = 224, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3739, .adv_w = 224, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3830, .adv_w = 224, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3907, .adv_w = 154, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3962, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4067, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4172, .adv_w = 252, .box_w = 16, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4276, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4381, .adv_w = 252, .box_w = 16, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4469, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4574, .adv_w = 112, .box_w = 7, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4616, .adv_w = 168, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4682, .adv_w = 252, .box_w = 16, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4794, .adv_w = 224, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4871, .adv_w = 196, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 4941, .adv_w = 196, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5039, .adv_w = 196, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5124, .adv_w = 196, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5209, .adv_w = 196, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 5279, .adv_w = 196, .box_w = 14, .box_h = 13, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 5370, .adv_w = 140, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5429, .adv_w = 140, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5488, .adv_w = 196, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5573, .adv_w = 196, .box_w = 13, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 5599, .adv_w = 252, .box_w = 16, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5687, .adv_w = 280, .box_w = 18, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5822, .adv_w = 252, .box_w = 17, .box_h = 15, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5950, .adv_w = 224, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6041, .adv_w = 196, .box_w = 13, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 6093, .adv_w = 196, .box_w = 13, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 6145, .adv_w = 280, .box_w = 18, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6244, .adv_w = 224, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6321, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6426, .adv_w = 224, .box_w = 15, .box_h = 15, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6539, .adv_w = 196, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6624, .adv_w = 196, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6722, .adv_w = 196, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6807, .adv_w = 140, .box_w = 10, .box_h = 15, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6882, .adv_w = 196, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6980, .adv_w = 196, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7078, .adv_w = 252, .box_w = 16, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7166, .adv_w = 224, .box_w = 16, .box_h = 15, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 7286, .adv_w = 168, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7369, .adv_w = 280, .box_w = 18, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7486, .adv_w = 280, .box_w = 18, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7576, .adv_w = 280, .box_w = 18, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7666, .adv_w = 280, .box_w = 18, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7756, .adv_w = 280, .box_w = 18, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7846, .adv_w = 280, .box_w = 18, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7936, .adv_w = 280, .box_w = 18, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8044, .adv_w = 196, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8134, .adv_w = 196, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8232, .adv_w = 224, .box_w = 15, .box_h = 15, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 8345, .adv_w = 280, .box_w = 18, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8444, .adv_w = 168, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8527, .adv_w = 225, .box_w = 15, .box_h = 10, .ofs_x = 0, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x1f72, 0xef51, 0xef58, 0xef5b, 0xef5c, 0xef5d, 0xef61,
    0xef63, 0xef65, 0xef69, 0xef6c, 0xef71, 0xef76, 0xef77, 0xef78,
    0xef8e, 0xef98, 0xef9b, 0xef9c, 0xef9d, 0xefa1, 0xefa2, 0xefa3,
    0xefa4, 0xefb7, 0xefb8, 0xefbe, 0xefc0, 0xefc1, 0xefc4, 0xefc7,
    0xefc8, 0xefc9, 0xefcb, 0xefe3, 0xefe5, 0xf014, 0xf015, 0xf017,
    0xf037, 0xf03a, 0xf043, 0xf06c, 0xf074, 0xf0ab, 0xf13b, 0xf190,
    0xf191, 0xf192, 0xf193, 0xf194, 0xf1d7, 0xf1e3, 0xf23d, 0xf254,
    0xf4aa, 0xf712, 0xf7f2
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 176, .range_length = 63475, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 59, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/

/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 0, 13, 14, 15, 16, 17,
    18, 19, 12, 20, 20, 0, 0, 0,
    21, 22, 23, 24, 25, 22, 26, 27,
    28, 29, 29, 30, 31, 32, 29, 29,
    22, 33, 34, 35, 3, 36, 30, 37,
    37, 38, 39, 40, 41, 42, 43, 0,
    44, 0, 45, 46, 47, 48, 49, 50,
    51, 45, 52, 52, 53, 48, 45, 45,
    46, 46, 54, 55, 56, 57, 51, 58,
    58, 59, 58, 60, 41, 0, 0, 9,
    61, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 12,
    18, 19, 20, 21, 21, 0, 0, 0,
    22, 23, 24, 25, 23, 25, 25, 25,
    23, 25, 25, 26, 25, 25, 25, 25,
    23, 25, 23, 25, 3, 27, 28, 29,
    29, 30, 31, 32, 33, 34, 35, 0,
    36, 0, 37, 38, 39, 39, 39, 0,
    39, 38, 40, 41, 38, 38, 42, 42,
    39, 42, 39, 42, 43, 44, 45, 46,
    46, 47, 46, 48, 0, 0, 35, 9,
    49, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 2, 0, 0, 0,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 10, 0, 6, -5, 0, 0,
    0, 0, -12, -13, 2, 11, 5, 4,
    -9, 2, 11, 1, 9, 2, 7, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 13, 2, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 4, 0, -7, 0, 0, 0, 0,
    0, -4, 4, 4, 0, 0, -2, 0,
    -2, 2, 0, -2, 0, -2, -1, -4,
    0, 0, 0, 0, -2, 0, 0, -3,
    -3, 0, 0, -2, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    -2, 0, -3, 0, -6, 0, -27, 0,
    0, -4, 0, 4, 7, 0, 0, -4,
    2, 2, 7, 4, -4, 4, 0, 0,
    -13, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -8, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -6, -3, -11, 0, -9,
    -2, 0, 0, 0, 0, 0, 9, 0,
    -7, -2, -1, 1, 0, -4, 0, 0,
    -2, -17, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -18, -2, 9,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -9, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 7,
    0, 2, 0, 0, -4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 9, 2,
    1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -8, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 2,
    4, 2, 7, -2, 0, 0, 4, -2,
    -7, -31, 2, 6, 4, 0, -3, 0,
    8, 0, 7, 0, 7, 0, -21, 0,
    -3, 7, 0, 7, -2, 4, 2, 0,
    0, 1, -2, 0, 0, -4, 18, 0,
    18, 0, 7, 0, 9, 3, 4, 7,
    0, 0, 0, -8, 0, 0, 0, 0,
    1, -2, 0, 2, -4, -3, -4, 2,
    0, -2, 0, 0, 0, -9, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -15, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, -12, 0, -14, 0, 0, 0,
    0, -2, 0, 22, -3, -3, 2, 2,
    -2, 0, -3, 2, 0, 0, -12, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -22, 0, 2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -14, 0, 13, 0, 0, -8, 0,
    7, 0, -15, -22, -15, -4, 7, 0,
    0, -15, 0, 3, -5, 0, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 6, 7, -27, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 11, 0, 2, 0, 0, 0,
    0, 0, 2, 2, -3, -4, 0, -1,
    -1, -2, 0, 0, -2, 0, 0, 0,
    -4, 0, -2, 0, -5, -4, 0, -6,
    -7, -7, -4, 0, -4, 0, -4, 0,
    0, 0, 0, -2, 0, 0, 2, 0,
    2, -2, 0, 1, 0, 0, 0, 2,
    -2, 0, 0, 0, -2, 2, 2, -1,
    0, 0, 0, -4, 0, -1, 0, 0,
    0, 0, 0, 1, 0, 3, -2, 0,
    -3, 0, -4, 0, 0, -2, 0, 7,
    0, 0, -2, 0, 0, 0, 0, 0,
    -1, 1, -2, -2, 0, 0, -2, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, -1, 0, -2, -3, 0,
    0, 0, 0, 0, 1, 0, 0, -2,
    0, -2, -2, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -1, 0, 0,
    0, 0, -2, -3, 0, -3, 0, -7,
    -2, -7, 4, 0, 0, -4, 2, 4,
    6, 0, -6, -1, -3, 0, -1, -11,
    2, -2, 2, -12, 2, 0, 0, 1,
    -12, 0, -12, -2, -19, -2, 0, -11,
    0, 4, 6, 0, 3, 0, 0, 0,
    0, 0, 0, -4, -3, 0, -7, 0,
    0, 0, -2, 0, 0, 0, -2, 0,
    0, 0, 0, 0, -1, -1, 0, -1,
    -3, 0, 0, 0, 0, 0, 0, 0,
    -2, -2, 0, -2, -3, -2, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, -2, 0, -3,
    0, -2, 0, -4, 2, 0, 0, -3,
    1, 2, 2, 0, 0, 0, 0, 0,
    0, -2, 0, 0, 0, 0, 0, 2,
    0, 0, -2, 0, -2, -2, -3, 0,
    0, 0, 0, 0, 0, 0, 2, 0,
    -2, 0, 0, 0, 0, -2, -3, 0,
    -4, 0, 7, -2, 1, -7, 0, 0,
    6, -11, -12, -9, -4, 2, 0, -2,
    -15, -4, 0, -4, 0, -4, 3, -4,
    -14, 0, -6, 0, 0, 1, -1, 2,
    -2, 0, 2, 0, -7, -9, 0, -11,
    -5, -5, -5, -7, -3, -6, 0, -4,
    -6, 1, 0, 1, 0, -2, 0, 0,
    0, 2, 0, 2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    0, -1, 0, -1, -2, 0, -4, -5,
    -5, -1, 0, -7, 0, 0, 0, 0,
    0, 0, -2, 0, 0, 0, 0, 1,
    -1, 0, 0, 0, 2, 0, 0, 0,
    0, 0, 0, 0, 0, 11, 0, 0,
    0, 0, 0, 0, 2, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, 0, 2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    -4, 0, 0, 0, 0, -11, -7, 0,
    0, 0, -3, -11, 0, 0, -2, 2,
    0, -6, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, 0, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 2, 0, -4, 0,
    0, 0, 0, 3, 0, 2, -4, -4,
    0, -2, -2, -3, 0, 0, 0, 0,
    0, 0, -7, 0, -2, 0, -3, -2,
    0, -5, -6, -7, -2, 0, -4, 0,
    -7, 0, 0, 0, 0, 18, 0, 0,
    1, 0, 0, -3, 0, 2, 0, -10,
    0, 0, 0, 0, 0, -21, -4, 7,
    7, -2, -9, 0, 2, -3, 0, -11,
    -1, -3, 2, -16, -2, 3, 0, 3,
    -8, -3, -8, -7, -9, 0, 0, -13,
    0, 13, 0, 0, -1, 0, 0, 0,
    -1, -1, -2, -6, -7, 0, -21, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, -1, -2, -3, 0, 0,
    -4, 0, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -4, 0, 0, 4,
    -1, 3, 0, -5, 2, -2, -1, -6,
    -2, 0, -3, -2, -2, 0, -3, -4,
    0, 0, -2, -1, -2, -4, -3, 0,
    0, -2, 0, 2, -2, 0, -5, 0,
    0, 0, -4, 0, -4, 0, -4, -4,
    2, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 2, 0, -3, 0, -2, -3,
    -7, -2, -2, -2, -1, -2, -3, -1,
    0, 0, 0, 0, 0, -2, -2, -2,
    0, 0, 0, 0, 3, -2, 0, -2,
    0, 0, 0, -2, -3, -2, -2, -3,
    -2, 0, 2, 9, -1, 0, -6, 0,
    -2, 4, 0, -2, -9, -3, 3, 0,
    0, -11, -4, 2, -4, 2, 0, -2,
    -2, -7, 0, -3, 1, 0, 0, -4,
    0, 0, 0, 2, 2, -4, -4, 0,
    -4, -2, -3, -2, -2, 0, -4, 1,
    -4, -4, 7, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, -3,
    0, 0, -2, -2, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, -1, 0, 0,
    0, 0, 0, -2, 0, 0, 0, 0,
    -3, 0, -4, 0, 0, 0, -7, 0,
    2, -5, 4, 0, -2, -11, 0, 0,
    -5, -2, 0, -9, -6, -6, 0, 0,
    -10, -2, -9, -9, -11, 0, -6, 0,
    2, 15, -3, 0, -5, -2, -1, -2,
    -4, -6, -4, -8, -9, -5, -2, 0,
    0, -2, 0, 1, 0, 0, -16, -2,
    7, 5, -5, -8, 0, 1, -7, 0,
    -11, -2, -2, 4, -21, -3, 1, 0,
    0, -15, -3, -12, -2, -16, 0, 0,
    -16, 0, 13, 1, 0, -2, 0, 0,
    0, 0, -1, -2, -9, -2, 0, -15,
    0, 0, 0, 0, -7, 0, -2, 0,
    -1, -6, -11, 0, 0, -1, -3, -7,
    -2, 0, -2, 0, 0, 0, 0, -10,
    -2, -7, -7, -2, -4, -6, -2, -4,
    0, -4, -2, -7, -3, 0, -3, -4,
    -2, -4, 0, 1, 0, -2, -7, 0,
    4, 0, -4, 0, 0, 0, 0, 3,
    0, 2, -4, 9, 0, -2, -2, -3,
    0, 0, 0, 0, 0, 0, -7, 0,
    -2, 0, -3, -2, 0, -5, -6, -7,
    -2, 0, -4, 2, 9, 0, 0, 0,
    0, 18, 0, 0, 1, 0, 0, -3,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, -4, 0, 0, 0, 0, 0, -1,
    0, 0, 0, -2, -2, 0, 0, -4,
    -2, 0, 0, -4, 0, 4, -1, 0,
    0, 0, 0, 0, 0, 1, 0, 0,
    0, 0, 3, 4, 2, -2, 0, -7,
    -4, 0, 7, -7, -7, -4, -4, 9,
    4, 2, -19, -2, 4, -2, 0, -2,
    2, -2, -8, 0, -2, 2, -3, -2,
    -7, -2, 0, 0, 7, 4, 0, -6,
    0, -12, -3, 6, -3, -9, 1, -3,
    -7, -7, -2, 9, 2, 0, -3, 0,
    -6, 0, 2, 7, -5, -8, -9, -6,
    7, 0, 1, -16, -2, 2, -4, -2,
    -5, 0, -5, -8, -3, -3, -2, 0,
    0, -5, -5, -2, 0, 7, 5, -2,
    -12, 0, -12, -3, 0, -8, -13, -1,
    -7, -4, -7, -6, 6, 0, 0, -3,
    0, -4, -2, 0, -2, -4, 0, 4,
    -7, 2, 0, 0, -12, 0, -2, -5,
    -4, -2, -7, -6, -7, -5, 0, -7,
    -2, -5, -4, -7, -2, 0, 0, 1,
    11, -4, 0, -7, -2, 0, -2, -4,
    -5, -6, -6, -9, -3, -4, 4, 0,
    -3, 0, -11, -3, 1, 4, -7, -8,
    -4, -7, 7, -2, 1, -21, -4, 4,
    -5, -4, -8, 0, -7, -9, -3, -2,
    -2, -2, -5, -7, -1, 0, 0, 7,
    6, -2, -15, 0, -13, -5, 5, -9,
    -15, -4, -8, -9, -11, -7, 4, 0,
    0, 0, 0, -3, 0, 0, 2, -3,
    4, 2, -4, 4, 0, 0, -7, -1,
    0, -1, 0, 1, 1, -2, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 2, 7, 0, 0, -3, 0, 0,
    0, 0, -2, -2, -3, 0, 0, 0,
    1, 2, 0, 0, 0, 0, 2, 0,
    -2, 0, 9, 0, 4, 1, 1, -3,
    0, 4, 0, 0, 0, 2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 7, 0, 6, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -13, 0, -2, 4, 0, 7,
    0, 0, 22, 3, -4, -4, 2, 2,
    -2, 1, -11, 0, 0, 11, -13, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -15, 9, 31, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -13, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, 0, 0, -4,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, -6, 0,
    0, 1, 0, 0, 2, 29, -4, -2,
    7, 6, -6, 2, 0, 0, 2, 2,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -29, 6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    0, 0, 0, -6, 0, 0, 0, 0,
    -5, -1, 0, 0, 0, -5, 0, -3,
    0, -11, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -15, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, -2, 0, 0, -4, 0, -3, 0,
    -6, 0, 0, 0, -4, 2, -3, 0,
    0, -6, -2, -5, 0, 0, -6, 0,
    -2, 0, -11, 0, -2, 0, 0, -18,
    -4, -9, -2, -8, 0, 0, -15, 0,
    -6, -1, 0, 0, 0, 0, 0, 0,
    0, 0, -3, -4, -2, -4, 0, 0,
    0, 0, -5, 0, -5, 3, -2, 4,
    0, -2, -5, -2, -4, -4, 0, -3,
    -1, -2, 2, -6, -1, 0, 0, 0,
    -20, -2, -3, 0, -5, 0, -2, -11,
    -2, 0, 0, -2, -2, 0, 0, 0,
    0, 2, 0, -2, -4, -2, 4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 3, 0, 0, 0, 0, 0,
    0, -5, 0, -2, 0, 0, 0, -4,
    2, 0, 0, 0, -6, -2, -4, 0,
    0, -6, 0, -2, 0, -11, 0, 0,
    0, 0, -22, 0, -4, -8, -11, 0,
    0, -15, 0, -2, -3, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -3, -1,
    -3, 1, 0, 0, 4, -3, 0, 7,
    11, -2, -2, -7, 3, 11, 4, 5,
    -6, 3, 9, 3, 6, 5, 6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 14, 11, -4, -2, 0, -2,
    18, 10, 18, 0, 0, 0, 2, 0,
    0, 8, 0, 0, -4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 3,
    0, 0, 0, 0, -19, -3, -2, -9,
    -11, 0, 0, -15, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    3, 0, 0, 0, 0, -19, -3, -2,
    -9, -11, 0, 0, -9, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, -5, 2, 0, -2,
    2, 4, 2, -7, 0, 0, -2, 2,
    0, 2, 0, 0, 0, 0, -6, 0,
    -2, -2, -4, 0, -2, -9, 0, 14,
    -2, 0, -5, -2, 0, -2, -4, 0,
    -2, -6, -4, -3, 0, 0, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, 0, 0, 0, 0, 0,
    0, 0, 3, 0, 0, 0, 0, -19,
    -3, -2, -9, -11, 0, 0, -15, 0,
    0, 0, 0, 0, 0, 11, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -4, 0, -7, -3, -2, 7, -2, -2,
    -9, 1, -1, 1, -2, -6, 0, 5,
    0, 2, 1, 2, -5, -9, -3, 0,
    -9, -4, -6, -9, -9, 0, -4, -4,
    -3, -3, -2, -2, -3, -2, 0, -2,
    -1, 3, 0, 3, -2, 0, 7, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -2, -2, -2, 0, 0,
    -6, 0, -1, 0, -4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -13, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, -2, 0, -3,
    0, 0, 0, 0, -2, 0, 0, -4,
    -2, 2, 0, -4, -4, -2, 0, -6,
    -2, -5, -2, -3, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -15, 0, 7, 0, 0, -4, 0,
    0, 0, 0, -3, 0, -2, 0, 0,
    -1, 0, 0, -2, 0, -5, 0, 0,
    9, -3, -7, -7, 2, 2, 2, 0,
    -6, 2, 3, 2, 7, 2, 7, -2,
    -6, 0, 0, -9, 0, 0, -7, -6,
    0, 0, -4, 0, -3, -4, 0, -3,
    0, -3, 0, -2, 3, 0, -2, -7,
    -2, 8, 0, 0, -2, 0, -4, 0,
    0, 3, -5, 0, 2, -2, 2, 0,
    0, -7, 0, -2, -1, 0, -2, 2,
    -2, 0, 0, 0, -9, -3, -5, 0,
    -7, 0, 0, -11, 0, 8, -2, 0,
    -4, 0, 1, 0, -2, 0, -2, -7,
    0, -2, 2, 0, 0, 0, 0, -2,
    0, 0, 2, -3, 1, 0, 0, -3,
    -2, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -14, 0, 5, 0,
    0, -2, 0, 0, 0, 0, 0, 0,
    -2, -2, 0, 0, 0, 4, 0, 5,
    0, 0, 0, 0, 0, -14, -13, 1,
    10, 7, 4, -9, 2, 9, 0, 8,
    0, 4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 12, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0
};

/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 61,
    .right_class_cnt     = 49,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

/*Store all the custom data of the font*/
static lv_font_fmt_txt_dsc_t font_dsc = {
    .glyph_bitmap = gylph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0
};

/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
lv_font_t lv_font_montserrat_14 = {
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 16,          /*The maximum line height required by the font*/
    .base_line = 3,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0)
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};

#endif /*#if LV_FONT_MONTSERRAT_14*/
