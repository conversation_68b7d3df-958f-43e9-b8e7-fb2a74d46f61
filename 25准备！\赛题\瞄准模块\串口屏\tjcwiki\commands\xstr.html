<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>xstr-写字指令 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="fill-区域填充指令" href="fill.html" />
    <link rel="prev" title="xpic-高级切图指令" href="xpic.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../widgets/index.html">控件详解</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">基本指令集</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="index.html#id2">常用指令集</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id3">高级指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id4">文件操作指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id5">不常用指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#crc">CRC校验指令</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html#gui">GUI绘图指令</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="cls.html">cls-清屏指令</a></li>
<li class="toctree-l3"><a class="reference internal" href="pic.html">pic-刷图指令</a></li>
<li class="toctree-l3"><a class="reference internal" href="picq.html">picq-切图指令</a></li>
<li class="toctree-l3"><a class="reference internal" href="xpic.html">xpic-高级切图指令</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">xstr-写字指令</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id1">xstr-示例</a></li>
<li class="toctree-l4"><a class="reference internal" href="#xstr-c">xstr-c语言示例</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id2">xstr指令-样例工程下载</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id5">xstr指令-相关链接</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="fill.html">fill-区域填充指令</a></li>
<li class="toctree-l3"><a class="reference internal" href="line.html">line-画线指令</a></li>
<li class="toctree-l3"><a class="reference internal" href="draw.html">draw-画空心矩形</a></li>
<li class="toctree-l3"><a class="reference internal" href="cir.html">cir-画空心圆</a></li>
<li class="toctree-l3"><a class="reference internal" href="cirs.html">cirs-画实心圆</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#hmi">HMI颜色代号表</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">指令集</a> &raquo;</li>
      <li>xstr-写字指令</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="xstr">
<h1>xstr-写字指令<a class="headerlink" href="#xstr" title="此标题的永久链接"></a></h1>
<p>在当前页面写字，不支持跨页面写字，不能写在页面的前初始化事件中</p>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>不推荐使用GUI绘图指令,绘图指令不要写在页面的前初始化事件中，否则在页面渲染完成后，将会被页面控件（每个页面ID为0的控件是与页面名称相同的页面控件）完全覆盖。绘图指令在跳转页面后会丢失。绘图指令和控件有重叠时，当控件刷新时，和控件重叠的部分会丢失。</p>
</div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span>xstr x,y,w,h,fontid,pointcolor,backcolor,xcenter,ycenter,sta,string

x:起始点坐标x；

y:起始点坐标y；

w:区域宽度；

h:区域高度；

fontid:字库ID；

pointcolor:字体颜色；

backcolor:背景色(sta设置为切图或图片时，backcolor表示图片ID);

xcenter:水平对齐方式(0为左对齐，1为居中，2为右对齐)；

ycenter: 垂直对齐方式(0为上对齐，1为居中，2为下对齐)；

sta:背景填充方式(0为切图，1为单色，2为图片，3为无背景,sta设置为切图或图片时，backcolor表示图片ID)

string:字符内容；
</pre></div>
</div>
<section id="id1">
<h2>xstr-示例<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="n">xstr</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">100</span><span class="p">,</span><span class="mi">30</span><span class="p">,</span><span class="mi">1</span><span class="p">,</span><span class="n">RED</span><span class="p">,</span><span class="n">BLACK</span><span class="p">,</span><span class="mi">1</span><span class="p">,</span><span class="mi">1</span><span class="p">,</span><span class="mi">1</span><span class="p">,</span><span class="s">&quot;中国&quot;</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/xstr_1.jpg" src="../_images/xstr_1.jpg" />
<p>实例解释：使用字库1在起始坐标(0,0)，宽度100,高度30这个区域写出”中国”，字体色为RED，背景色为BLACK(如果不想写背景色(即无背景)可以设置sta参数为3),水平对齐方式为居中，垂直对齐方式也为居中。</p>
</section>
<section id="xstr-c">
<h2>xstr-c语言示例<a class="headerlink" href="#xstr-c" title="此标题的永久链接"></a></h2>
<p>使用字库1在起始坐标(0,0)，宽度100,高度30这个区域写出”中国”，字体色为RED，背景色为BLACK(如果不想写背景色(即无背景)可以设置sta参数为3),水平对齐方式为居中，垂直对齐方式也为居中。</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="n">posX</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="n">posY</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="n">width</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span><span class="w"> </span><span class="n">height</span><span class="o">=</span><span class="mi">30</span><span class="p">,</span><span class="w"> </span><span class="n">fontid</span><span class="o">=</span><span class="mi">40</span><span class="p">,</span><span class="w"> </span><span class="n">pointcolor</span><span class="o">=</span><span class="mi">15</span><span class="p">,</span><span class="w"> </span><span class="n">backcolor</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="n">xcenter</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="n">ycenter</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="n">sta</span><span class="o">=</span><span class="mi">1</span><span class="p">;</span><span class="w"></span>
<span class="linenos">2</span><span class="w"> </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;xstr %d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%s</span><span class="se">\xff\xff\xff</span><span class="s">&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">posX</span><span class="p">,</span><span class="w"> </span><span class="n">posY</span><span class="p">,</span><span class="w"> </span><span class="n">width</span><span class="p">,</span><span class="w"> </span><span class="n">height</span><span class="p">,</span><span class="w"> </span><span class="n">fontid</span><span class="p">,</span><span class="w"> </span><span class="n">pointcolor</span><span class="p">,</span><span class="w"> </span><span class="n">backcolor</span><span class="p">,</span><span class="w"> </span><span class="n">xcenter</span><span class="p">,</span><span class="w"> </span><span class="n">ycenter</span><span class="p">,</span><span class="w"> </span><span class="n">sta</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;中国&quot;</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>字符写到超过设定的w（宽度）以后将自动换行，如果换行到h（高度）之后还有剩下的字符没写完，将会被忽略。</p>
<p>绘图指令不要写在页面的前初始化事件中，否则在页面渲染完成后，将会被页面控件（每个页面ID为0的控件是与页面名称相同的页面控件）完全覆盖</p>
</div>
</section>
<section id="id2">
<h2>xstr指令-样例工程下载<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/基本指令集/绘图指令/绘图指令.HMI">《绘图指令》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/基本指令集/绘图指令/绘图指令2.HMI">《绘图指令演示工程2》下载</a></p>
</section>
<section id="id5">
<h2>xstr指令-相关链接<a class="headerlink" href="#id5" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="index.html#hmi"><span class="std std-ref">HMI颜色代号表</span></a></p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="xpic.html" class="btn btn-neutral float-left" title="xpic-高级切图指令" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="fill.html" class="btn btn-neutral float-right" title="fill-区域填充指令" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>