#ifndef __AUTO_MOVEMENT_H
#define __AUTO_MOVEMENT_H

#include "stdint.h"
#include "Geometry.h"
#include "Servo.h"
#include "Timer.h"
#include "ManualRecord.h"

// 自动往返状态定义
typedef enum {
    AUTO_STATE_IDLE = 0,            // 空闲状态
    AUTO_STATE_PREPARING,           // 准备阶段
    AUTO_STATE_MOVING_TO_START,     // 移动到起始点
    AUTO_STATE_PATH_MOVING,         // 路径移动中
    AUTO_STATE_DIRECTION_CHANGE,    // 方向切换
    AUTO_STATE_PAUSED,              // 暂停状态
    AUTO_STATE_STOPPING,            // 停止中
    AUTO_STATE_ERROR                // 错误状态
} AutoMovementState_t;

// 移动模式定义
typedef enum {
    MOVE_MODE_SINGLE = 0,           // 单次移动 (A→B或B→A)
    MOVE_MODE_CONTINUOUS,           // 连续往返
    MOVE_MODE_COUNT_LIMITED         // 限定次数往返
} MovementMode_t;

// 自动往返配置参数
#define AUTO_MOVE_TOTAL_TIME_MS     2000    // 总移动时间 2秒
#define AUTO_MOVE_STEP_TIME_MS      20      // 每步时间 20ms
#define AUTO_MOVE_TOTAL_STEPS       100     // 总步数
#define AUTO_DIRECTION_PAUSE_MS     1000    // 方向切换暂停时间
#define AUTO_PREPARE_DELAY_MS       500     // 准备阶段延时
#define AUTO_STOP_DELAY_MS          300     // 停止延时

// 自动往返控制结构体
typedef struct {
    AutoMovementState_t current_state;      // 当前状态
    MovementMode_t movement_mode;           // 移动模式
    
    // 点位数据
    WallPoint_t point_a;                    // A点坐标
    WallPoint_t point_b;                    // B点坐标
    ServoAngle_t servo_a;                   // A点舵机角度
    ServoAngle_t servo_b;                   // B点舵机角度
    uint8_t points_valid;                   // 点位数据是否有效
    
    // 路径控制
    PathInterpolation_t path;               // 路径插值对象
    uint8_t current_direction;              // 当前方向 (0=A→B, 1=B→A)
    uint16_t current_step;                  // 当前步数
    uint16_t total_steps;                   // 总步数
    
    // 时间控制
    uint32_t state_start_time;              // 状态开始时间
    uint32_t last_step_time;                // 上次步进时间
    uint32_t movement_start_time;           // 移动开始时间
    
    // 计数控制
    uint16_t cycle_count;                   // 往返次数计数
    uint16_t max_cycles;                    // 最大往返次数 (0=无限制)
    
    // 状态标志
    uint8_t is_active;                      // 是否激活
    uint8_t is_paused;                      // 是否暂停
    uint8_t user_stop_requested;           // 用户请求停止
    uint8_t emergency_stop;                 // 紧急停止
    
    // 性能统计
    uint32_t total_move_time;               // 总移动时间
    float average_speed;                    // 平均速度 (mm/s)
    uint16_t completed_cycles;              // 已完成往返次数
} AutoMovementControl_t;

// 错误代码定义
typedef enum {
    AUTO_OK = 0,
    AUTO_ERROR_NOT_READY,
    AUTO_ERROR_INVALID_POINTS,
    AUTO_ERROR_SERVO_COMM,
    AUTO_ERROR_GEOMETRY_CALC,
    AUTO_ERROR_SYSTEM_BUSY,
    AUTO_ERROR_USER_STOP,
    AUTO_ERROR_EMERGENCY_STOP
} AutoMovementError_t;

// 主要控制接口
AutoMovementError_t AutoMovement_Init(AutoMovementControl_t* ctrl);
AutoMovementError_t AutoMovement_Start(AutoMovementControl_t* ctrl, MovementMode_t mode);
AutoMovementError_t AutoMovement_Update(AutoMovementControl_t* ctrl);
AutoMovementError_t AutoMovement_Stop(AutoMovementControl_t* ctrl);
AutoMovementError_t AutoMovement_Pause(AutoMovementControl_t* ctrl);
AutoMovementError_t AutoMovement_Resume(AutoMovementControl_t* ctrl);

// 点位管理
AutoMovementError_t AutoMovement_SetPoints(AutoMovementControl_t* ctrl, 
                                          WallPoint_t point_a, WallPoint_t point_b,
                                          ServoAngle_t servo_a, ServoAngle_t servo_b);
AutoMovementError_t AutoMovement_LoadPointsFromRecord(AutoMovementControl_t* ctrl);
uint8_t AutoMovement_ArePointsValid(AutoMovementControl_t* ctrl);

// 移动控制
AutoMovementError_t AutoMovement_SetDirection(AutoMovementControl_t* ctrl, uint8_t direction);
AutoMovementError_t AutoMovement_SetMaxCycles(AutoMovementControl_t* ctrl, uint16_t max_cycles);
AutoMovementError_t AutoMovement_ToggleDirection(AutoMovementControl_t* ctrl);

// 状态查询
AutoMovementState_t AutoMovement_GetState(AutoMovementControl_t* ctrl);
uint8_t AutoMovement_IsActive(AutoMovementControl_t* ctrl);
uint8_t AutoMovement_IsPaused(AutoMovementControl_t* ctrl);
float AutoMovement_GetProgress(AutoMovementControl_t* ctrl);
uint16_t AutoMovement_GetCycleCount(AutoMovementControl_t* ctrl);

// 性能监控
float AutoMovement_GetAverageSpeed(AutoMovementControl_t* ctrl);
uint32_t AutoMovement_GetTotalTime(AutoMovementControl_t* ctrl);
float AutoMovement_GetCurrentSpeed(AutoMovementControl_t* ctrl);

// 内部状态机函数
AutoMovementError_t AutoMovement_TransitionTo(AutoMovementControl_t* ctrl, AutoMovementState_t new_state);
AutoMovementError_t AutoMovement_HandlePreparing(AutoMovementControl_t* ctrl);
AutoMovementError_t AutoMovement_HandleMovingToStart(AutoMovementControl_t* ctrl);
AutoMovementError_t AutoMovement_HandlePathMoving(AutoMovementControl_t* ctrl);
AutoMovementError_t AutoMovement_HandleDirectionChange(AutoMovementControl_t* ctrl);

// 路径执行函数
AutoMovementError_t AutoMovement_ExecuteNextStep(AutoMovementControl_t* ctrl);
AutoMovementError_t AutoMovement_MoveToPosition(ServoAngle_t target_angle, uint16_t time_ms);
AutoMovementError_t AutoMovement_ValidateMovement(AutoMovementControl_t* ctrl);

// 用户交互
void AutoMovement_ShowStatus(AutoMovementControl_t* ctrl);
void AutoMovement_ShowProgress(AutoMovementControl_t* ctrl);
const char* AutoMovement_GetStateString(AutoMovementState_t state);
const char* AutoMovement_GetModeString(MovementMode_t mode);

// 安全和错误处理
AutoMovementError_t AutoMovement_EmergencyStop(AutoMovementControl_t* ctrl);
AutoMovementError_t AutoMovement_CheckSafety(AutoMovementControl_t* ctrl);
void AutoMovement_HandleError(AutoMovementControl_t* ctrl, AutoMovementError_t error);

#endif
