# D36A双路步进电机驱动板开发资料总结

## 📋 现有资料清单

### 核心文档
- **D36A驱动用户手册_V1.5_2025.7.29.pdf** - 主要技术文档
- **D36A双路步进电机驱动模块(2024.11.20).pdf** - 产品介绍文档
- **D36A驱动问题排查和检测方法.pdf** - 故障诊断文档

### 芯片手册
- **ATD5984.PDF** - 核心驱动芯片数据手册
- **RT8279.PDF** - 电源管理芯片
- **RT9013-33GB.PDF** - 3.3V线性稳压芯片

### 视频教程
- **D36A双路步进电机驱动介绍.mp4** - 产品介绍视频
- **D36A驱动控制使用与例程讲解.mp4** - 使用教程视频

### 参数配置
- **电阻调节电流参数表.xlsx** - 电流档位配置表

## 🎯 核心技术特征

### 硬件架构
- **主控芯片**: 2个ATD5984步进电机驱动芯片
- **支持细分**: 全步、1/2、1/4、1/8、1/16、1/32步进模式
- **电流控制**: 最大输出1.44A，8档可调
- **电源管理**: 板载5V和3.3V稳压电路
- **散热设计**: 散热风扇+导热胶固定

### 接口定义
| 引脚 | 功能 | 说明 |
|------|------|------|
| ST1/ST2 | 步进控制 | PWM上升沿有效 |
| DIR1/DIR2 | 方向控制 | 高/低电平切换方向 |
| EN1/EN2 | 使能控制 | 低电平休眠，高电平使能 |
| ADC | 电压监测 | 读取电源电压的1/11 |
| 5V | 电源输出 | 为单片机供电 |
| GND | 电源地 | 公共地线 |

### 拨码开关配置

#### 细分设置(123开关)
| MS1 | MS2 | MS3 | 细分倍数 |
|-----|-----|-----|----------|
| 1 | 1 | 1 | 不细分 |
| 0 | 1 | 1 | 1/2细分 |
| 0 | 1 | 0 | 1/4细分 |
| 1 | 0 | 0 | 1/8细分 |
| 0 | 0 | 0 | 1/16细分 |
| 1 | 0 | 1 | 1/32细分 |

#### 电流设置(456开关)
| CUR1 | CUR2 | CUR3 | 输出电流(A) |
|------|------|------|-------------|
| 0 | 0 | 0 | 1.44 |
| 0 | 0 | 1 | 1.22 |
| 0 | 1 | 0 | 0.93 |
| 0 | 1 | 1 | 0.83 |
| 1 | 0 | 0 | 0.77 |
| 1 | 0 | 1 | 0.70 |
| 0 | 1 | 1 | 0.59 |
| 1 | 1 | 1 | 0.55 |

*注：1=导通(ON)，0=断开(OFF)*

## 🔧 关键开发要点

### 控制信号要求
- **PWM频率计算**: 根据细分倍数和目标转速计算
  - 不细分: 1圈=200步，420RPM最大 → 1400Hz
  - 1/32细分: 1圈=6400步，420RPM最大 → 44800Hz
- **时序要求**: ST引脚上升沿有效，需要PWM信号
- **方向控制**: DIR引脚高低电平切换

### 电路连接规范
- **电源**: 12V外部电源供电，板载5V/3.3V输出
- **信号**: 5V逻辑电平兼容
- **接地**: 确保单片机与驱动板共地

### 调试验证方法
- **万用表**: 无法准确测量相电压(方波频率太快)
- **示波器**: 观察A+/A-输出波形，应为互补方波
- **正常波形**: 最大值=电源电压，A+高电平时A-为低电平

## ⚠️ 重要注意事项

### 硬件保护
1. **散热**: ATD5984发热，避免直接触碰
2. **风扇**: 防止杜邦线卡住风扇导致堵转
3. **接线**: 注意引脚丝印，避免电源接反

### 配置限制
- 拨码开关同时控制双路电机的细分和电流
- 细分开关123带上拉电阻，断开=高电平，导通=低电平
- 散热片用导热胶固定，避免用力破坏

### 软件开发
- **平台支持**: STM32F103、Arduino UNO示例代码
- **PWM生成**: 使用定时器生成合适频率的PWM
- **ADC监测**: 实时监测电源电压(实际值×11)

## 📁 开发建议的补充资料

### 建议增加的资料
1. **原理图文件** - 便于理解电路设计
2. **PCB Layout文件** - 了解走线和热管理
3. **元器件清单(BOM)** - 备件和替换参考
4. **机械尺寸图** - 结构设计参考
5. **热设计分析报告** - 散热性能参数
6. **EMC测试报告** - 电磁兼容性能
7. **更多平台例程** - TI MSPM0G3507等

### 技术深入资料
- **ATD5984详细应用笔记**
- **步进电机选型指南**
- **PWM频率与转速关系计算表**
- **不同负载下的性能曲线**
- **多轴联动控制示例**

## 🎯 瞄准模块应用建议

### 双轴云台控制
- **水平轴**: 使用ST1/DIR1/EN1控制
- **俯仰轴**: 使用ST2/DIR2/EN2控制  
- **细分推荐**: 1/16或1/32提高精度
- **电流设置**: 根据负载选择合适档位

### 系统集成注意
- **供电方案**: 12V统一供电，5V给单片机
- **信号隔离**: 必要时考虑光耦隔离
- **接地处理**: 确保系统共地，避免干扰
- **线缆管理**: 合理布线，避免影响云台运动

---
*文档生成时间: 2025-07-30*