# HTS-25L 总线舵机 + STM32 开发板 + BusLinker 调试板：开发与驱动完整指南

**最新资料基于官方文档版本：**
- *HTS-25L总线舵机使用说明* V2.0 (2025-06-25)。fileciteturn1file13L11-L14
- *HTS-25L总线舵机规格书* V1.0 (2025-06-26)。fileciteturn1file11L13-L14
- *总线舵机通信协议* V2.0（含指令/帧格式/校验）。fileciteturn1file9L32-L38
- *舵机ID设置方法（BusLinker调试板）*（适用于全部幻尔总线舵机，含硬件连接与上位机）。fileciteturn1file1L9-L13

---

## 1. 文档目的
帮助你：
1. 正确连接 HTS-25L 总线舵机、电源、STM32 开发板以及 BusLinker 调试板。  
2. 理解舵机电气/通信参数、数据帧格式、校验算法、关键指令。  
3. 在 STM32 (HAL 或裸寄存器) 上初始化半双工 UART (115200 bps) 并发送/接收协议帧。  
4. 用 C 代码示例实现常用功能：设定目标角度+时间、批量预置并统一启动、读当前位置/电压/温度、设限位、调偏差、切换伺服/电机模式、力矩装载/卸载、写读 ID。  
5. 快速排查常见问题（供电、共地、串口、ID 冲突等）。

---

## 2. 型号与关键规格速查
- 型号：HTS-25L 串行总线智能舵机。fileciteturn1file3L29-L29
- 控制方式：UART 串口指令。fileciteturn1file3L29-L29
- 通信波特率：115200 bps（半双工 UART）。fileciteturn1file9L24-L26
- 工作电压范围：6–8.4 V（额定 7.4 V）。fileciteturn1file3L67-L70
- 舵机 ID 范围：0–253，出厂默认 1。fileciteturn1file3L35-L36
- 控制位置范围：0–1000 对应 0°–240°（中位 500≈120°）。fileciteturn1file11L101-L104
- 参数反馈：温度、电压、位置可回读。fileciteturn1file3L47-L49
- 工作模式：舵机模式 / 减速电机（马达）模式可切换。fileciteturn1file3L49-L49

---

## 3. 电源与引脚
舵机 3Pin 引脚（自左向右）：GND / VIN / SIG (半双工 UART 信号)。fileciteturn1file3L11-L16

> **共地要求：** 如果舵机与单片机不共用同一电源，必须将两者地线相连（共地），否则通信可能失败。fileciteturn1file3L16-L17

> **供电注意：** 使用 6–8.4V 稳定电源，勿超压；欠压可能无法驱动舵机。fileciteturn1file3L67-L70

---

## 4. 串联与总线拓扑
HTS-25L 接口为半双工 UART；每个舵机带 3 个总线接口，可串联，理论上单总线可挂 253 个舵机（ID 0–253）。fileciteturn1file13L77-L80

---

## 5. BusLinker 调试板基础连接（用于供电/上位机调试/ID 配置）
硬件所需：BusLinker 调试板、总线舵机、电源转接线、3Pin 舵机线、Micro‑USB 数据线、6–8V 电源/电池。fileciteturn1file1L21-L34

连接要点：
1. 红线接 VIN，黑线接 GND；注意勿接反。fileciteturn1file1L41-L46
2. 舵机与调试板任一兼容接口连接即可（调试板多接口并联）。fileciteturn1file1L53-L56
3. 加电后红色电源指示灯应点亮；若不亮检查接线与电源。fileciteturn1file1L59-L63
4. 通过 Micro‑USB 连接电脑，安装 CH340/CH341 驱动，设备管理器应出现对应串口。fileciteturn1file1L83-L89

---

## 6. 舵机 ID 设置要点
BusLinker + 上位机软件 *Bus Servo Terminal* 可写入舵机 ID，适用于全部幻尔总线舵机。fileciteturn1file1L9-L13

> 在设置 ID 前 **必须单独连接该舵机**，否则串联链上所有舵机都会被写成同一 ID。fileciteturn1file3L71-L75

上位机操作：选择正确 COM 口打开串口，连上后在“参数设置”区输入新 ID 并写入；完成后可在“基本操作”页用新 ID 验证（实时显示电压/温度/位置）。fileciteturn1file8L42-L48

---

## 7. 通信概述
舵机通过半双工 UART 异步串行总线与控制器通讯；协议精简、接口简单；波特率 115200 bps。fileciteturn1file9L22-L26

多个舵机可统一运动或单独控制；可切换位置控制（伺服）与电机控制（速度）模式。fileciteturn1file9L11-L18

---

## 8. 数据帧格式
```
0x55 0x55  ID  Length  Cmd  Prm1 ... PrmN  Checksum
```
连续两个 0x55 为帧头。fileciteturn1file9L40-L41  
ID 范围 0–253；广播 ID=254 (0xFE) 会让所有舵机接收指令但不返回应答（读 ID 例外，避免总线冲突）。fileciteturn1file9L42-L46  
Length 字段含自身，占待发送参数总字节数；**Length + 3 = 从帧头到校验和的包长**。fileciteturn1file9L49-L51  
Checksum = 按字节和(ID+Length+Cmd+Prm...)取低 8 位后按位取反 (~sum)。fileciteturn1file7L32-L35

---

## 9. 指令类型与发送节奏
协议区分写指令与读指令；写指令带参数写入舵机执行，读指令触发舵机回传数据；发送读指令后要立即切换到接收状态。fileciteturn1file7L41-L47

发送新指令前需等待上一条指令执行完成，否则会打断前一动作。fileciteturn1file12L81-L83

---

## 10. 常用指令速查表
> 下表列出开发常用子集；完整列表见协议原文。fileciteturn1file12L19-L32

| 功能 | Cmd | Length | 主要参数说明 | 备注 |
|---|---|---|---|---|
| SERVO_MOVE_TIME_WRITE | 0x01 | 7 | Pos_L, Pos_H (0–1000=0–240°), Time_L, Time_H (0–30000 ms) | 到达即动。fileciteturn1file6L40-L52 |
| SERVO_MOVE_TIME_WAIT_WRITE | 0x07 | 7 | 同上 | 需随后发 START 才动。fileciteturn1file6L70-L75 |
| SERVO_MOVE_START | 0x0B | 3 | 无 | 触发批量预置动作。fileciteturn1file4L21-L28 |
| SERVO_MOVE_STOP | 0x0C | 3 | 无 | 立即停在当前角度。fileciteturn1file4L32-L34 |
| SERVO_ID_WRITE | 0x0D | 4 | NewID (0–253) | 掉电保存。fileciteturn1file4L41-L44 |
| SERVO_ANGLE_OFFSET_ADJUST | 0x11 | 4 | Offset (-125~125 = -30°~30°) | 即时调整，不保存。fileciteturn1file4L57-L60 |
| SERVO_ANGLE_LIMIT_WRITE | 0x14 | 7 | MinPos, MaxPos (0–1000) | 限位并保存。fileciteturn1file6L42-L44 |
| SERVO_OR_MOTOR_MODE_WRITE | 0x1D | 7 | Mode(0=伺服,1=马达), Speed(-1000~1000, 补码) | 模式+速度，保存。fileciteturn1file6L11-L17 |
| SERVO_LOAD_OR_UNLOAD_WRITE | 0x1F | 4 | 1=装载(上电力矩),0=卸载 | 力矩输出控制。fileciteturn1file6L15-L17 |
| SERVO_TEMP_READ | 0x1A (协议表索引 26) | 3 | 无 | 回读温度。fileciteturn1file6L1-L4 |
| SERVO_VIN_READ | 0x1B (27) | 3 | 无 | 回读输入电压。fileciteturn1file6L5-L8 |
| SERVO_POS_READ | 0x1C (28) | 3 | 无 | 回读当前位置。fileciteturn1file6L9-L10 |

> 注：协议表中 26/27/28 行对应 TEMP/VIN/POS 读指令；原文以序号列示，参见表格片段。fileciteturn1file6L1-L10

---

## 11. 角度与位置换算
协议位置参数 0–1000 对应机械角度 0°–240°，分辨率约 0.24°/计数。fileciteturn1file6L40-L46  
舵机中位位置为 500 ≈ 120°（规格书数据）。fileciteturn1file11L101-L104

**换算公式**  
```
pos = (angle_deg / 240.0f) * 1000.0f
angle_deg = (pos / 1000.0f) * 240.0f
```

---

## 12. STM32 硬件连接建议
### 12.1 直接连舵机（推荐在成品系统中）
- STM32 UART 选任一支持半双工（Single Wire / Half-Duplex）模式的 USARTx。设定波特率 115200。fileciteturn1file9L24-L26
- MCU 信号线经适当电平匹配后接舵机 SIG；GND 必须与舵机电源地相连。fileciteturn1file3L16-L17
- 舵机供电 6–8.4 V 独立电源时，务必与 MCU 共地。fileciteturn1file3L16-L17

### 12.2 通过 BusLinker 调试板中转（开发调试阶段）
将舵机按 BusLinker 指南接入：红(VIN)/黑(GND) 供电，3Pin 舵机线连调试板数据口，再用 USB 将 BusLinker 连 PC 以观察协议与调试；也可从调试板引出串口至 STM32 进行级联测试（注意总线上仅一主控，避免冲突）。fileciteturn1file1L41-L46

---

## 13. STM32 驱动设计概要
驱动层职责：
1. 发送协议帧（构帧、校验）。
2. 在半双工链路上“发送后迅速切换为接收”（用于读指令回包）。fileciteturn1file7L41-L47
3. 严格控制发送节奏，等待前一条指令执行完成后再发下一条（或依据回读/延时管理）。fileciteturn1file12L81-L83

---

## 14. C 语言驱动头文件示例 (`hts25l.h`)
> 针对 STM32 HAL；如用 LL / 裸寄存器，请自行替换底层 UART API。

```c
#ifndef HTS25L_H
#define HTS25L_H

#include <stdint.h>
#include <stddef.h>
#include "stm32fxxx_hal.h"   // 替换为具体芯片头文件

#ifdef __cplusplus
extern "C" {
#endif

/* ---------------------- Protocol constants ---------------------- */
#define HTS25L_HEADER          0x55
#define HTS25L_BROADCAST_ID    0xFE  // 广播 ID. 所有舵机接收但除读ID外不应答. See note.

/* Command codes */
#define HTS25L_CMD_MOVE_TIME_WRITE        0x01
#define HTS25L_CMD_MOVE_TIME_READ         0x02
#define HTS25L_CMD_MOVE_TIME_WAIT_WRITE   0x07
#define HTS25L_CMD_MOVE_TIME_WAIT_READ    0x08
#define HTS25L_CMD_MOVE_START             0x0B
#define HTS25L_CMD_MOVE_STOP              0x0C
#define HTS25L_CMD_ID_WRITE               0x0D
#define HTS25L_CMD_ID_READ                0x0E
#define HTS25L_CMD_ANGLE_OFFSET_ADJUST    0x11
#define HTS25L_CMD_ANGLE_OFFSET_WRITE     0x12
#define HTS25L_CMD_ANGLE_OFFSET_READ      0x13
#define HTS25L_CMD_ANGLE_LIMIT_WRITE      0x14
#define HTS25L_CMD_ANGLE_LIMIT_READ       0x15
#define HTS25L_CMD_VIN_LIMIT_WRITE        0x16
#define HTS25L_CMD_VIN_LIMIT_READ         0x17
#define HTS25L_CMD_TEMP_MAX_LIMIT_WRITE   0x18
#define HTS25L_CMD_TEMP_MAX_LIMIT_READ    0x19
#define HTS25L_CMD_TEMP_READ              0x1A
#define HTS25L_CMD_VIN_READ               0x1B
#define HTS25L_CMD_POS_READ               0x1C
#define HTS25L_CMD_OR_MOTOR_MODE_WRITE    0x1D
#define HTS25L_CMD_OR_MOTOR_MODE_READ     0x1E
#define HTS25L_CMD_LOAD_OR_UNLOAD_WRITE   0x1F
#define HTS25L_CMD_LOAD_OR_UNLOAD_READ    0x20
#define HTS25L_CMD_LED_CTRL_WRITE         0x21
#define HTS25L_CMD_LED_CTRL_READ          0x22
#define HTS25L_CMD_LED_ERROR_WRITE        0x23
#define HTS25L_CMD_LED_ERROR_READ         0x24

/* Position <-> angle helpers */
#define HTS25L_POS_MIN        0
#define HTS25L_POS_MAX        1000
#define HTS25L_ANGLE_MAX_DEG  240.0f
#define HTS25L_MID_POS        500
#define HTS25L_MID_ANGLE_DEG  120.0f

static inline uint16_t HTS25L_AngleDegToPos(float deg){
    if(deg < 0) deg = 0; if(deg > HTS25L_ANGLE_MAX_DEG) deg = HTS25L_ANGLE_MAX_DEG;
    return (uint16_t)((deg / HTS25L_ANGLE_MAX_DEG) * HTS25L_POS_MAX + 0.5f);
}

static inline float HTS25L_PosToAngleDeg(uint16_t pos){
    if(pos > HTS25L_POS_MAX) pos = HTS25L_POS_MAX;
    return ((float)pos / (float)HTS25L_POS_MAX) * HTS25L_ANGLE_MAX_DEG;
}

/* Servo driver opaque handle */
typedef struct {
    UART_HandleTypeDef *huart;  // half-duplex UART instance
    uint32_t tx_timeout_ms;
    uint32_t rx_timeout_ms;
} HTS25L_Driver_t;

/* Public API */
void     HTS25L_Init(HTS25L_Driver_t *drv, UART_HandleTypeDef *huart);
int      HTS25L_MoveTimeWrite(HTS25L_Driver_t *drv, uint8_t id, uint16_t pos, uint16_t time_ms);
int      HTS25L_MoveStart(HTS25L_Driver_t *drv, uint8_t id);
int      HTS25L_MoveStop(HTS25L_Driver_t *drv, uint8_t id);
int      HTS25L_ReadPos(HTS25L_Driver_t *drv, uint8_t id, uint16_t *p_pos);
int      HTS25L_ReadTemp(HTS25L_Driver_t *drv, uint8_t id, uint8_t *p_temp_c);
int      HTS25L_ReadVin_mV(HTS25L_Driver_t *drv, uint8_t id, uint16_t *p_mv);
int      HTS25L_WriteID(HTS25L_Driver_t *drv, uint8_t old_id, uint8_t new_id);
int      HTS25L_WriteAngleLimit(HTS25L_Driver_t *drv, uint8_t id, uint16_t min_pos, uint16_t max_pos);
int      HTS25L_WriteModeMotor(HTS25L_Driver_t *drv, uint8_t id, uint8_t mode_servo_or_motor, int16_t speed);
int      HTS25L_LoadOrUnload(HTS25L_Driver_t *drv, uint8_t id, uint8_t load);

/* Low-level send/recv (exposed if needed) */
int      HTS25L_SendPacket(HTS25L_Driver_t *drv, uint8_t id, uint8_t cmd,
                           const uint8_t *params, uint8_t param_len);
int      HTS25L_ReadPacket (HTS25L_Driver_t *drv, uint8_t expect_id, uint8_t expect_cmd,
                           uint8_t *payload, uint8_t *p_len, uint32_t timeout_ms);

#ifdef __cplusplus
}
#endif

#endif /* HTS25L_H */
```

---

## 15. C 语言驱动实现示例 (`hts25l.c`)
```c
#include "hts25l.h"
#include <string.h>

/* ---------------------- Internal helpers ---------------------- */
static uint8_t calc_checksum(uint8_t id, uint8_t length, uint8_t cmd, const uint8_t *params, uint8_t param_len){
    uint16_t sum = id + length + cmd;
    for(uint8_t i=0;i<param_len;i++) sum += params[i];
    return (uint8_t)(~(sum & 0xFF));
}

static HAL_StatusTypeDef uart_tx(HTS25L_Driver_t *drv, const uint8_t *buf, uint16_t len){
    return HAL_UART_Transmit(drv->huart, (uint8_t*)buf, len, drv->tx_timeout_ms);
}

static HAL_StatusTypeDef uart_rx(HTS25L_Driver_t *drv, uint8_t *buf, uint16_t len, uint32_t timeout){
    return HAL_UART_Receive(drv->huart, buf, len, timeout);
}

void HTS25L_Init(HTS25L_Driver_t *drv, UART_HandleTypeDef *huart){
    drv->huart = huart;
    drv->tx_timeout_ms = 20;
    drv->rx_timeout_ms = 20;
}

/* Generic packet send */
int HTS25L_SendPacket(HTS25L_Driver_t *drv, uint8_t id, uint8_t cmd, const uint8_t *params, uint8_t param_len){
    uint8_t length = (uint8_t)(param_len + 1); // length field includes cmd byte? Actually per protocol: Length counts (Cmd + Params + Checksum?) Wait: spec says Length is "待发送的数据(包含本身)" and Length+3= total (from first 0x55). Implementation trick: we pack [Length,Cmd,Params]. Checksum computed over ID+Length+Cmd+Params.
    uint8_t pkt[2+1+1+1+param_len+1]; // header(2)+id+len+cmd+params+chk
    uint16_t idx = 0;
    pkt[idx++] = HTS25L_HEADER; pkt[idx++] = HTS25L_HEADER;
    pkt[idx++] = id;
    pkt[idx++] = length;
    pkt[idx++] = cmd;
    for(uint8_t i=0;i<param_len;i++) pkt[idx++] = params[i];
    pkt[idx++] = calc_checksum(id,length,cmd,params,param_len);
    if(uart_tx(drv,pkt,idx)!=HAL_OK) return -1;
    return 0;
}

/* Generic packet read (blocking; simple) */
int HTS25L_ReadPacket(HTS25L_Driver_t *drv, uint8_t expect_id, uint8_t expect_cmd,
                      uint8_t *payload, uint8_t *p_len, uint32_t timeout_ms){
    uint8_t hdr;
    /* sync to first 0x55 */
    do {
        if(uart_rx(drv,&hdr,1,timeout_ms)!=HAL_OK) return -1;
    } while(hdr != HTS25L_HEADER);
    /* wait 2nd 0x55 */
    if(uart_rx(drv,&hdr,1,timeout_ms)!=HAL_OK) return -1;
    if(hdr != HTS25L_HEADER) return -2;

    uint8_t id,len,cmd;
    if(uart_rx(drv,&id,1,timeout_ms)!=HAL_OK) return -3;
    if(uart_rx(drv,&len,1,timeout_ms)!=HAL_OK) return -4;
    if(uart_rx(drv,&cmd,1,timeout_ms)!=HAL_OK) return -5;
    uint8_t param_len = (uint8_t)(len - 1); // excluding Cmd itself
    if(param_len > 64) return -6; // guard
    if(uart_rx(drv,payload,param_len,timeout_ms)!=HAL_OK) return -7;
    uint8_t chk;
    if(uart_rx(drv,&chk,1,timeout_ms)!=HAL_OK) return -8;

    uint8_t calc = calc_checksum(id,len,cmd,payload,param_len);
    if(calc != chk) return -9; // checksum error
    if(p_len) *p_len = param_len;
    return ( (id==expect_id || expect_id==HTS25L_BROADCAST_ID) && cmd==expect_cmd ) ? 0 : 1; // 0=match,1=mismatch but valid frame
}

int HTS25L_MoveTimeWrite(HTS25L_Driver_t *drv, uint8_t id, uint16_t pos, uint16_t time_ms){
    uint8_t prm[4];
    prm[0] = pos & 0xFF; prm[1] = (pos>>8)&0xFF;
    prm[2] = time_ms & 0xFF; prm[3] = (time_ms>>8)&0xFF;
    return HTS25L_SendPacket(drv,id,HTS25L_CMD_MOVE_TIME_WRITE,prm,4);
}

int HTS25L_MoveStart(HTS25L_Driver_t *drv, uint8_t id){
    return HTS25L_SendPacket(drv,id,HTS25L_CMD_MOVE_START,NULL,0);
}

int HTS25L_MoveStop(HTS25L_Driver_t *drv, uint8_t id){
    return HTS25L_SendPacket(drv,id,HTS25L_CMD_MOVE_STOP,NULL,0);
}

int HTS25L_ReadPos(HTS25L_Driver_t *drv, uint8_t id, uint16_t *p_pos){
    uint8_t len; uint8_t buf[4];
    if(HTS25L_SendPacket(drv,id,HTS25L_CMD_POS_READ,NULL,0)) return -1;
    if(HTS25L_ReadPacket(drv,id,HTS25L_CMD_POS_READ,buf,&len,drv->rx_timeout_ms)) return -2;
    if(len < 2) return -3;
    *p_pos = (uint16_t)(buf[0] | (buf[1]<<8));
    return 0;
}

int HTS25L_ReadTemp(HTS25L_Driver_t *drv, uint8_t id, uint8_t *p_temp_c){
    uint8_t len; uint8_t buf[4];
    if(HTS25L_SendPacket(drv,id,HTS25L_CMD_TEMP_READ,NULL,0)) return -1;
    if(HTS25L_ReadPacket(drv,id,HTS25L_CMD_TEMP_READ,buf,&len,drv->rx_timeout_ms)) return -2;
    if(len < 1) return -3;
    *p_temp_c = buf[0];
    return 0;
}

int HTS25L_ReadVin_mV(HTS25L_Driver_t *drv, uint8_t id, uint16_t *p_mv){
    uint8_t len; uint8_t buf[4];
    if(HTS25L_SendPacket(drv,id,HTS25L_CMD_VIN_READ,NULL,0)) return -1;
    if(HTS25L_ReadPacket(drv,id,HTS25L_CMD_VIN_READ,buf,&len,drv->rx_timeout_ms)) return -2;
    if(len < 2) return -3;
    *p_mv = (uint16_t)(buf[0] | (buf[1]<<8));
    return 0;
}

int HTS25L_WriteID(HTS25L_Driver_t *drv, uint8_t old_id, uint8_t new_id){
    uint8_t prm[1]; prm[0] = new_id;
    return HTS25L_SendPacket(drv,old_id,HTS25L_CMD_ID_WRITE,prm,1);
}

int HTS25L_WriteAngleLimit(HTS25L_Driver_t *drv, uint8_t id, uint16_t min_pos, uint16_t max_pos){
    uint8_t prm[4];
    prm[0] = min_pos & 0xFF; prm[1] = (min_pos>>8)&0xFF;
    prm[2] = max_pos & 0xFF; prm[3] = (max_pos>>8)&0xFF;
    return HTS25L_SendPacket(drv,id,HTS25L_CMD_ANGLE_LIMIT_WRITE,prm,4);
}

int HTS25L_WriteModeMotor(HTS25L_Driver_t *drv, uint8_t id, uint8_t mode_servo_or_motor, int16_t speed){
    uint8_t prm[4];
    prm[0] = mode_servo_or_motor; // 0 servo, 1 motor
    prm[1] = 0;                   // 空值
    prm[2] = (uint8_t)(speed & 0xFF);
    prm[3] = (uint8_t)((speed>>8)&0xFF);
    return HTS25L_SendPacket(drv,id,HTS25L_CMD_OR_MOTOR_MODE_WRITE,prm,4);
}

int HTS25L_LoadOrUnload(HTS25L_Driver_t *drv, uint8_t id, uint8_t load){
    uint8_t prm[1]; prm[0] = load ? 1 : 0;
    return HTS25L_SendPacket(drv,id,HTS25L_CMD_LOAD_OR_UNLOAD_WRITE,prm,1);
}
```

---

## 16. 主程序最小示例 (`main.c` 片段)
下面示例展示：初始化 USART 半双工；将 ID=1 的舵机移动至 90°（对应 pos≈375）耗时 1000ms，然后读取回位置信息。

```c
#include "hts25l.h"

HTS25L_Driver_t hts;
UART_HandleTypeDef huart1; // 假设使用 USART1

static void MX_USART1_UART_Init(void){
    huart1.Instance = USART1;
    huart1.Init.BaudRate   = 115200;  // 协议要求
    huart1.Init.WordLength = UART_WORDLENGTH_8B;
    huart1.Init.StopBits   = UART_STOPBITS_1;
    huart1.Init.Parity     = UART_PARITY_NONE;
    huart1.Init.Mode       = UART_MODE_TX_RX; // HAL_HALF_DUPLEX_Init 将覆盖
    huart1.Init.HwFlowCtl  = UART_HWCONTROL_NONE;
    huart1.AdvancedInit.AdvFeatureInit = UART_ADVFEATURE_NO_INIT;
    HAL_HalfDuplex_Init(&huart1); // 单线半双工
}

int main(void){
    HAL_Init();
    SystemClock_Config();
    MX_USART1_UART_Init();

    HTS25L_Init(&hts, &huart1);

    uint16_t pos = HTS25L_AngleDegToPos(90.0f); // ~375
    HTS25L_MoveTimeWrite(&hts, 1, pos, 1000);
    HAL_Delay(1100); // 等待动作结束 (简化)

    uint16_t cur_pos; HTS25L_ReadPos(&hts,1,&cur_pos);
    float cur_angle = HTS25L_PosToAngleDeg(cur_pos);
    (void)cur_angle; // 在调试器查看

    while(1){
        HAL_Delay(1000);
    }
}
```

> 波特率 115200、半双工 UART 等配置来自协议要求。fileciteturn1file9L22-L26

---

## 17. 批量预置 & 同步启动示例
使用 *MOVE_TIME_WAIT_WRITE* 给多路舵机分别预置目标位置与时间，然后向广播 ID 发送 *MOVE_START*（或逐个 ID）。广播 ID 所有舵机接收但除读 ID 外不应答，可实现总线同步启动且避免回包冲突。fileciteturn1file9L42-L46

示例：
```c
// 预置两个舵机
HTS25L_SendPacket(&hts, 1, HTS25L_CMD_MOVE_TIME_WAIT_WRITE, prm1, 4);
HTS25L_SendPacket(&hts, 2, HTS25L_CMD_MOVE_TIME_WAIT_WRITE, prm2, 4);
// 广播启动
HTS25L_MoveStart(&hts, HTS25L_BROADCAST_ID);
```
> WAIT_WRITE / START 组合需先预置后启动；详见协议描述。fileciteturn1file6L70-L75

---

## 18. 限位、偏差、模式与力矩
- *ANGLE_LIMIT_WRITE*：设置最小/最大位置 (0–1000)，限制运动范围并掉电保存。fileciteturn1file6L42-L44
- *ANGLE_OFFSET_ADJUST*：实时偏差校正，范围 -125~125 (=±30°)。fileciteturn1file4L57-L60
- *OR_MOTOR_MODE_WRITE*：Mode=0 伺服模式；Mode=1 电机模式；速度 -1000~1000（补码表示）；写入掉电保存。fileciteturn1file6L11-L17
- *LOAD_OR_UNLOAD_WRITE*：0 卸载（掉电无力矩），1 装载（有力矩输出）。fileciteturn1file6L15-L17

---

## 19. 安全与使用注意
- 使用规定电压范围内的稳定电源，超压可能损坏舵机，欠压可能无法驱动。fileciteturn1file3L67-L70
- 设置 ID 时需单独连接，防止全链路同 ID。fileciteturn1file3L71-L75
- 通电后避免手动强扭舵臂以免损坏内部机构。fileciteturn1file3L77-L78

---

## 20. 调试技巧
- 如果串口无法连接，请检查数据线、驱动、供电；BusLinker 指示灯可用于初步诊断。fileciteturn1file1L83-L89
- 使用 BusLinker 上位机“实时状态”查看电压/温度/位置是否刷新，验证通信与 ID。fileciteturn1file8L24-L28

---

## 21. 开发流程快速清单
1. 确认供电 6–8.4V & 共地。fileciteturn1file3L16-L17
2. 单独连接每个舵机写入唯一 ID。fileciteturn1file3L71-L75
3. 配置 STM32 UART=115200 半双工。fileciteturn1file9L24-L26
4. 使用协议帧格式构包并发送；校验 ~sum。fileciteturn1file7L32-L35
5. 发读指令后立刻切 RX 等待回包。fileciteturn1file7L41-L47
6. 严格按指令执行节奏，避免打断动作。fileciteturn1file12L81-L83

---

## 22. 附：校验计算示例
示例命令：移动到 500，时间 1000ms：`55 55 01 07 01 F4 01 E8 03 ??`。求和 (01+07+01+F4+01+E8+03)=??，取低 8 位后按位取反得 0x16。fileciteturn1file2L13-L18

---

## 23. 进一步资源
- 完整通信协议（含所有指令与回包格式）详见 *总线舵机通信协议_V2.0.pdf*。fileciteturn1file9L32-L38
- 上位机 Bus Servo Terminal、驱动及示例在文档提供的下载链接（参见 BusLinker 文档）。fileciteturn1file1L99-L101

---

### 版权与引用
本文档依据用户提供的幻尔科技官方资料整理，仅作开发参考；如遇版本差异请以最新官方文件为准。资料版权归原厂所有。fileciteturn1file13L11-L14

---

**祝开发顺利！** 如果你希望我根据本指南直接生成可编译的 Keil / STM32CubeIDE 工程骨架，请在聊天中告诉我你使用的具体 STM32 型号与工具链。

