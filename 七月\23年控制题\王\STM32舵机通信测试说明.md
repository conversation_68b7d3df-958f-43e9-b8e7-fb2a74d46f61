# STM32舵机通信测试说明

## 📋 **测试目的**

本次实验的唯一目的：**测试STM32核心板与HTS-25L总线舵机能否正常进行串口通信**

## 🎯 **功能描述**

- **读取频率**：5Hz (每200ms读取一次)
- **目标舵机**：ID=1的HTS-25L总线舵机
- **显示内容**：仅显示舵机角度，通信失败显示"error"
- **其他功能**：全部注释掉，专注通信测试

## 🔧 **硬件连接**

### **STM32F103C8T6连接**
```
STM32引脚 → HTS-25L舵机
PA2 (TX)  → 舵机驱动板RXD
PA3 (RX)  → 舵机驱动板TXD
GND       → 舵机驱动板GND
5V        → 舵机驱动板VCC

OLED显示:
PB6       → OLED_SCL
PB7       → OLED_SDA
```

### **舵机设置**
- **舵机型号**：HTS-25L总线舵机
- **舵机ID**：必须设置为1
- **通信协议**：HTS-25L标准协议
- **波特率**：115200 bps

## 📊 **OLED显示效果**

### **正常通信**
```
┌─────────────────┐
│   Servo Test    │
│                 │
│ ID1: 127.5 deg  │
│                 │
└─────────────────┘
```

### **通信失败**
```
┌─────────────────┐
│   Servo Test    │
│                 │
│ ID1: error      │
│                 │
└─────────────────┘
```

## ⚙️ **通信协议详解**

### **读取角度指令**
**发送数据包**：
```
55 55 01 03 1C [CHECKSUM]
```
- `55 55`: 帧头
- `01`: 舵机ID
- `03`: 数据长度
- `1C`: 读取位置指令
- `[CHECKSUM]`: 校验和

**接收数据包**：
```
55 55 01 05 1C [POS_L] [POS_H] [CHECKSUM]
```
- `55 55`: 帧头
- `01`: 舵机ID
- `05`: 数据长度
- `1C`: 读取位置指令回复
- `[POS_L]`: 位置低字节
- `[POS_H]`: 位置高字节
- `[CHECKSUM]`: 校验和

### **角度转换公式**
```c
// 位置值转角度 (0-1000 对应 0-240度)
float angle = (float)position * 240.0f / 1000.0f;
```

## 🔍 **测试步骤**

### **1. 硬件准备**
1. 按照连接图连接STM32与舵机驱动板
2. 确保舵机ID设置为1
3. 检查电源供电是否充足
4. 确认OLED显示屏连接正确

### **2. 软件烧录**
1. 编译修改后的工程文件
2. 将程序烧录到STM32F103C8T6
3. 复位STM32，观察OLED显示

### **3. 通信测试**
1. **启动观察**：OLED应显示"Servo Test"
2. **角度读取**：每200ms更新一次角度显示
3. **通信状态**：正常显示角度值，异常显示"error"

## 📈 **预期结果**

### **成功标准**
- ✅ OLED正常显示"Servo Test"标题
- ✅ 角度数值每200ms更新一次
- ✅ 显示的角度值在0-240度范围内
- ✅ 角度值随舵机物理位置变化而变化

### **失败情况**
- ❌ 持续显示"ID1: error"
- ❌ 角度值不更新或显示异常
- ❌ OLED无显示或显示乱码

## 🔧 **代码修改要点**

### **新增功能**
1. **Servo.h**: 添加`SERVO_CMD_POS_READ`和`Servo_ReadRealTimeAngle()`
2. **Servo.c**: 实现实时角度读取函数
3. **main.c**: 简化为纯通信测试功能

### **注释功能**
- 串口屏命令处理 (LED控制、舵机控制)
- 按键处理功能
- 复杂的OLED显示内容
- 所有非必要的系统功能

### **核心逻辑**
```c
// 主循环：每10ms执行一次
while (1) {
    Read_And_Display_Angle();  // 5Hz读取角度
    Delay_ms(10);
}

// 角度读取：每200ms执行一次
static uint32_t counter = 0;
counter++;
if (counter >= 20) {  // 10ms * 20 = 200ms
    counter = 0;
    // 执行角度读取和显示
}
```

## ⚠️ **故障排除**

### **1. 显示"error"**
**可能原因**：
- 舵机ID不是1
- 串口连接错误 (TX/RX接反)
- 舵机供电不足
- 波特率不匹配

**解决方法**：
- 检查舵机ID设置
- 确认TX/RX交叉连接
- 检查5V供电是否充足
- 确认波特率为115200

### **2. OLED无显示**
**可能原因**：
- OLED连接错误
- I2C通信异常
- 程序未正常运行

**解决方法**：
- 检查PB6/PB7连接
- 确认OLED供电正常
- 重新烧录程序

### **3. 角度值异常**
**可能原因**：
- 数据解析错误
- 协议格式不匹配
- 校验和计算错误

**解决方法**：
- 检查协议实现
- 验证数据包格式
- 确认校验和算法

## 📊 **测试验收**

### **基本功能验收**
- [ ] OLED正常显示测试界面
- [ ] 角度数值每200ms更新
- [ ] 手动转动舵机，角度值相应变化
- [ ] 通信异常时显示"error"

### **通信质量验收**
- [ ] 连续运行5分钟无异常
- [ ] 角度读取成功率 > 95%
- [ ] 显示的角度值与实际位置一致
- [ ] 系统稳定，无死机现象

## 🎯 **测试结论**

本测试完成后，可以得出以下结论：

1. **通信协议**：STM32与HTS-25L舵机的串口通信是否正常
2. **硬件连接**：串口连接和电源供电是否可靠
3. **软件实现**：协议解析和数据处理是否正确
4. **系统稳定性**：长时间运行的稳定性如何

---

**版本信息**：V1.0  
**测试日期**：2024-07-22  
**适用硬件**：STM32F103C8T6 + HTS-25L舵机  
**技术支持**：控制系统专家团队
