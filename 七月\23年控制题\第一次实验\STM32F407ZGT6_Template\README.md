# STM32F407ZGT6开发板工程模板

## 📋 项目概述

本工程模板基于STM32F407ZGT6微控制器，使用Keil uVision5开发环境和STM32标准外设库。该模板已经过测试验证，可以成功编译、下载和运行。

## 🔧 硬件配置

### 主控芯片
- **MCU**: STM32F407ZGT6
- **内核**: ARM Cortex-M4
- **主频**: 168MHz
- **Flash**: 1MB
- **SRAM**: 192KB

### 引脚配置
- **LED0**: PF9 (共阳极，低电平点亮)
- **LED1**: PF10 (共阳极，低电平点亮)
- **按键K0**: PE4 (低电平有效，内部上拉)
- **按键K1**: PE3 (低电平有效，内部上拉)

### 时钟配置
- **系统时钟**: 168MHz
- **AHB时钟**: 168MHz
- **APB1时钟**: 42MHz
- **APB2时钟**: 84MHz

## 📁 工程结构

```
STM32F407ZGT6_Template/
├── User/                    # 用户代码
│   ├── main.c              # 主程序文件
│   ├── stm32f4xx.h         # STM32F4xx头文件
│   ├── stm32f4xx_conf.h    # 外设库配置文件
│   ├── stm32f4xx_it.c      # 中断服务程序
│   └── stm32f4xx_it.h      # 中断服务程序头文件
├── System/                  # 系统功能模块
│   ├── Delay.c             # 延时函数实现
│   └── Delay.h             # 延时函数头文件
├── Start/                   # 启动文件
│   ├── startup_stm32f40_41xxx.s  # 启动汇编文件
│   ├── system_stm32f4xx.c  # 系统初始化
│   └── core_cm4.h          # Cortex-M4核心头文件
├── Library/                 # STM32标准外设库
│   └── stm32f4xx_*.c/h     # 各外设驱动文件
└── DebugConfig/            # 调试配置
    └── *.dbgconf           # 调试器配置文件
```

## 🚀 快速开始

### 1. 环境要求
- **IDE**: Keil uVision5
- **编译器**: ARM Compiler 5.06 或更高版本
- **调试器**: ST-Link V2/V3
- **Pack**: STM32F4xx_DFP.2.14.0 或更高版本

### 2. 使用步骤

1. **复制模板**
   ```
   复制整个工程文件夹到你的项目目录
   ```

2. **重命名工程**
   - 将工程文件夹重命名为你的项目名称
   - 重命名 `.uvprojx` 文件为你的项目名称

3. **打开工程**
   ```
   双击 .uvprojx 文件在Keil中打开工程
   ```

4. **配置调试器**
   - Project → Options for Target
   - Debug → Use: ST-Link Debugger
   - Settings → Port: SW, Max Clock: 1.8MHz

5. **编译下载**
   ```
   F7: 编译
   F8: 下载到目标板
   ```

## 💡 代码说明

### 主要函数

#### `System_Init()`
- 系统初始化函数
- 配置系统时钟为168MHz
- 初始化SysTick延时

#### `GPIO_Init_Config()`
- GPIO初始化配置
- 配置LED和按键引脚
- 设置引脚模式和电气特性

#### `main()`
- 主程序入口
- 调用系统初始化
- 进入主循环

### 延时函数

#### `Delay_Init(u8 SYSCLK)`
- 延时功能初始化
- 参数：系统时钟频率(MHz)

#### `Delay_ms(u16 nms)`
- 毫秒级延时
- 参数：延时时间(ms)

#### `Delay_us(u32 nus)`
- 微秒级延时
- 参数：延时时间(us)

## 🔨 开发指南

### 1. 添加新的外设
1. 在 `stm32f4xx_conf.h` 中取消对应外设头文件的注释
2. 在 `User/` 目录下创建外设驱动文件
3. 在 `main.c` 中添加初始化调用

### 2. 中断处理
- 在 `stm32f4xx_it.c` 中添加中断服务函数
- 在 `stm32f4xx_it.h` 中添加函数声明
- 配置NVIC中断优先级

### 3. 调试技巧
- 使用Keil的仿真调试功能
- 添加Watch窗口监视变量
- 设置断点进行单步调试

## ⚠️ 注意事项

1. **时钟配置**: 确保外部晶振为25MHz
2. **引脚冲突**: 添加新功能前检查引脚是否已被占用
3. **中断优先级**: 合理配置中断优先级避免冲突
4. **堆栈大小**: 根据需要调整堆栈大小
5. **编译优化**: 调试时建议关闭编译优化

## 📞 技术支持

如有问题，请检查：
1. 硬件连接是否正确
2. 调试器驱动是否安装
3. 工程配置是否正确
4. 代码语法是否有误

## 📝 更新日志

- **v1.0** (2024-07-17): 初始版本，基础GPIO和延时功能

---

**模板作者**: LHQ  
**创建日期**: 2024年7月17日  
**适用平台**: STM32F407ZGT6开发板
