# Claude Code 任务清单：STM32舵机控制项目

> **快速参考**: 按优先级顺序完成以下8个任务  
> **总预估时间**: 10.5小时  
> **验收标准**: 每个任务完成后进行功能测试  

---

## 🎯 任务执行顺序

### 第一阶段：基础通信 (3小时)

#### ✅ 任务1.1: UART半双工通信配置 [2小时] ⭐⭐⭐
**文件**: 新建 `User/hts25l_driver.h` 和 `User/hts25l_driver.c`
**核心功能**:
- 配置USART2半双工模式，PA2引脚，115200波特率
- 实现发送后自动切换接收状态

**关键代码**:
```c
void HTS25L_UART_Init(void);  // UART初始化
int HTS25L_SendPacket(uint8_t id, uint8_t cmd, uint8_t *params, uint8_t len);
int HTS25L_ReadPacket(uint8_t *payload, uint8_t *len);
```

**测试方法**: 发送简单指令，检查是否有应答

---

#### ✅ 任务2.1: HTS-25L协议帧封装 [1小时] ⭐⭐⭐
**文件**: 继续完善 `User/hts25l_driver.c`
**核心功能**:
- 实现协议帧构建：0x55 0x55 ID Length Cmd Params Checksum
- 校验和算法：~((ID+Length+Cmd+Params) & 0xFF)

**关键代码**:
```c
int HTS25L_MoveTimeWrite(uint8_t id, uint16_t pos, uint16_t time_ms);
int HTS25L_ReadPos(uint8_t id, uint16_t *pos);
uint8_t calc_checksum(uint8_t id, uint8_t len, uint8_t cmd, uint8_t *params, uint8_t param_len);
```

**测试方法**: 发送MOVE_TIME_WRITE指令，观察舵机是否运动

---

### 第二阶段：功能实现 (4小时)

#### ✅ 任务3.1: 30度步进控制算法 [2小时] ⭐⭐⭐
**文件**: 在 `User/main.c` 中添加控制逻辑
**核心功能**:
- 30度步进：每次增加125计数 (30°/240°×1000)
- 边界处理：0→30→60→...→240→0循环

**关键代码**:
```c
static uint16_t current_target_pos = 0;
void Servo_Step30Degree(void);
void Servo_Reset(void);
```

**测试方法**: 手动调用函数，验证30度步进和复位逻辑

---

#### ✅ 任务1.2: 按键和LED GPIO配置 [1小时] ⭐⭐
**文件**: 修改 `User/main.c` 的 `GPIO_Init_Config()` 函数
**核心功能**:
- PE3按键：上拉输入，20ms消抖
- PF9/PF10 LED：状态指示

**关键代码**:
```c
uint8_t Button_Scan(void);  // 返回1表示K1按下
void LED_Control(uint8_t led, uint8_t state);
```

**测试方法**: 按键控制LED亮灭

---

#### ✅ 任务2.2: 舵机位置读取功能 [1小时] ⭐⭐
**文件**: 完善 `User/hts25l_driver.c`
**核心功能**:
- 读取舵机当前位置
- 位置角度转换

**关键代码**:
```c
uint16_t HTS25L_AngleDegToPos(float deg);  // 角度→位置
float HTS25L_PosToAngleDeg(uint16_t pos);  // 位置→角度
```

**测试方法**: 读取位置并转换为角度值显示

---

### 第三阶段：系统集成 (3.5小时)

#### ✅ 任务3.2: 按键状态机实现 [2小时] ⭐⭐
**文件**: 在 `User/main.c` 中实现状态机
**核心功能**:
- 状态：IDLE、MOVING、READING
- 按键只在IDLE状态有效

**关键代码**:
```c
typedef enum {
    SERVO_STATE_IDLE = 0,
    SERVO_STATE_MOVING,
    SERVO_STATE_READING
} ServoState_t;

void Servo_StateMachine(void);
void Servo_ProcessButton(void);
```

**测试方法**: 验证运动期间按键无效，状态切换正确

---

#### ✅ 任务4.1: 主程序集成 [1小时] ⭐⭐⭐
**文件**: 完善 `User/main.c` 主循环
**核心功能**:
- 集成所有功能模块
- 保持LED心跳指示

**关键代码**:
```c
int main(void)
{
    // 初始化
    System_Init();
    GPIO_Init_Config();
    HTS25L_UART_Init();
    
    while(1)
    {
        Servo_ProcessButton();
        Servo_StateMachine();
        LED_Heartbeat();
        Delay_ms(10);
    }
}
```

**测试方法**: 完整功能测试，按K1控制舵机30度步进

---

#### ✅ 任务4.2: 错误处理和异常保护 [0.5小时] ⭐
**文件**: 在各个模块中添加错误处理
**核心功能**:
- 通信超时处理
- LED错误状态指示

**关键代码**:
```c
#define COMM_TIMEOUT_MS  100
void Error_Handler(uint8_t error_code);
void LED_ErrorIndication(uint8_t error_type);
```

**测试方法**: 断开舵机连接，验证错误处理

---

## 🔧 开发环境准备

### 必需文件修改
1. **新建文件**:
   - `User/hts25l_driver.h` (舵机驱动头文件)
   - `User/hts25l_driver.c` (舵机驱动实现)

2. **修改文件**:
   - `User/main.c` (集成主控制逻辑)
   - `User/stm32f4xx_conf.h` (启用USART外设)

### 关键配置参数
```c
// 舵机参数
#define SERVO_ID            1
#define SERVO_BAUDRATE      115200
#define STEP_30_DEGREE      125
#define MOVE_TIME_MS        1000

// 引脚定义
#define SERVO_UART          USART2
#define SERVO_GPIO_PIN      GPIO_Pin_2
#define BUTTON_K1_PIN       GPIO_Pin_3
#define LED0_PIN            GPIO_Pin_9
#define LED1_PIN            GPIO_Pin_10
```

---

## 🧪 测试验证要点

### 分步测试策略
1. **任务1.1完成后**: 测试UART通信基础功能
2. **任务2.1完成后**: 测试协议帧发送和舵机响应
3. **任务3.1完成后**: 测试30度步进算法
4. **任务1.2完成后**: 测试按键和LED功能
5. **任务2.2完成后**: 测试位置读取功能
6. **任务3.2完成后**: 测试状态机逻辑
7. **任务4.1完成后**: 测试完整系统功能
8. **任务4.2完成后**: 测试异常处理

### LED状态指示
- **正常运行**: LED0慢闪(心跳)，LED1熄灭
- **舵机运动**: LED0常亮，LED1熄灭
- **通信错误**: LED0快闪，LED1常亮
- **系统异常**: LED0和LED1交替闪烁

---

## ⚠️ 重要提醒

### 硬件连接检查
- [ ] 舵机7.4V独立供电
- [ ] STM32与舵机共地连接
- [ ] PA2正确连接舵机SIG
- [ ] 舵机ID确认为1

### 软件开发要点
- [ ] 半双工时序：发送完立即切换接收
- [ ] 校验和计算：严格按协议实现
- [ ] 边界检查：位置值0-1000范围
- [ ] 状态管理：避免状态机死锁

### 调试建议
- 使用LED指示系统状态
- 可添加USART1串口调试输出
- 分模块测试，确保每步正确
- 注意半双工通信时序

---

**开始编码吧！按照任务顺序逐步实现，每完成一个任务就进行测试验证。** 🚀
