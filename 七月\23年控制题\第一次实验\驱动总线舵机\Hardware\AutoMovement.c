#include "AutoMovement.h"
#include "Key.h"
#include "LED.h"
#include "OLED.h"
#include "Delay.h"
#include <stdio.h>
#include <stddef.h>
#include <math.h>

/**
 * 初始化自动往返控制器
 * @param ctrl 控制器结构体指针
 * @return 错误代码
 */
AutoMovementError_t AutoMovement_Init(AutoMovementControl_t* ctrl)
{
    if (ctrl == NULL) return AUTO_ERROR_NOT_READY;
    
    // 初始化状态
    ctrl->current_state = AUTO_STATE_IDLE;
    ctrl->movement_mode = MOVE_MODE_CONTINUOUS;
    
    // 清空点位数据
    ctrl->point_a.x = 0.0f;
    ctrl->point_a.y = 0.0f;
    ctrl->point_b.x = 0.0f;
    ctrl->point_b.y = 0.0f;
    ctrl->servo_a.pan = 120.0f;
    ctrl->servo_a.tilt = 120.0f;
    ctrl->servo_b.pan = 120.0f;
    ctrl->servo_b.tilt = 120.0f;
    ctrl->points_valid = 0;
    
    // 初始化路径控制
    ctrl->current_direction = 0;  // A→B
    ctrl->current_step = 0;
    ctrl->total_steps = AUTO_MOVE_TOTAL_STEPS;
    
    // 初始化时间控制
    ctrl->state_start_time = 0;
    ctrl->last_step_time = 0;
    ctrl->movement_start_time = 0;
    
    // 初始化计数控制
    ctrl->cycle_count = 0;
    ctrl->max_cycles = 0;  // 无限制
    
    // 初始化状态标志
    ctrl->is_active = 0;
    ctrl->is_paused = 0;
    ctrl->user_stop_requested = 0;
    ctrl->emergency_stop = 0;
    
    // 初始化性能统计
    ctrl->total_move_time = 0;
    ctrl->average_speed = 0.0f;
    ctrl->completed_cycles = 0;
    
    return AUTO_OK;
}

/**
 * 开始自动往返移动 - 激光云台终极功能
 * @param ctrl 控制器结构体指针
 * @param mode 移动模式
 * @return 错误代码
 */
AutoMovementError_t AutoMovement_Start(AutoMovementControl_t* ctrl, MovementMode_t mode)
{
    if (ctrl == NULL) return AUTO_ERROR_NOT_READY;
    
    // 检查系统状态
    if (ctrl->is_active) {
        return AUTO_ERROR_SYSTEM_BUSY;
    }
    
    // 加载点位数据
    AutoMovementError_t error = AutoMovement_LoadPointsFromRecord(ctrl);
    if (error != AUTO_OK) return error;
    
    // 验证点位有效性
    if (!AutoMovement_ArePointsValid(ctrl)) {
        return AUTO_ERROR_INVALID_POINTS;
    }
    
    // 设置移动模式
    ctrl->movement_mode = mode;
    ctrl->is_active = 1;
    ctrl->is_paused = 0;
    ctrl->user_stop_requested = 0;
    ctrl->emergency_stop = 0;
    
    // 重置统计数据
    ctrl->cycle_count = 0;
    ctrl->completed_cycles = 0;
    ctrl->movement_start_time = Timer_GetTick();
    
    // 转换到准备状态
    return AutoMovement_TransitionTo(ctrl, AUTO_STATE_PREPARING);
}

/**
 * 更新自动往返控制器 - 主状态机
 * @param ctrl 控制器结构体指针
 * @return 错误代码
 */
AutoMovementError_t AutoMovement_Update(AutoMovementControl_t* ctrl)
{
    if (ctrl == NULL) return AUTO_ERROR_NOT_READY;
    
    // 检查紧急停止
    if (ctrl->emergency_stop) {
        return AutoMovement_EmergencyStop(ctrl);
    }
    
    // 检查用户停止请求
    if (ctrl->user_stop_requested) {
        return AutoMovement_Stop(ctrl);
    }
    
    // 检查暂停状态
    if (ctrl->is_paused) {
        return AUTO_OK;  // 暂停中，不执行状态机
    }
    
    // 安全检查
    AutoMovementError_t error = AutoMovement_CheckSafety(ctrl);
    if (error != AUTO_OK) {
        AutoMovement_HandleError(ctrl, error);
        return error;
    }
    
    // 执行状态机
    switch (ctrl->current_state) {
        case AUTO_STATE_IDLE:
            // 空闲状态，无需处理
            break;
            
        case AUTO_STATE_PREPARING:
            error = AutoMovement_HandlePreparing(ctrl);
            break;
            
        case AUTO_STATE_MOVING_TO_START:
            error = AutoMovement_HandleMovingToStart(ctrl);
            break;
            
        case AUTO_STATE_PATH_MOVING:
            error = AutoMovement_HandlePathMoving(ctrl);
            break;
            
        case AUTO_STATE_DIRECTION_CHANGE:
            error = AutoMovement_HandleDirectionChange(ctrl);
            break;
            
        case AUTO_STATE_STOPPING:
            // 停止过程中
            if (Timer_IsTimeout(ctrl->state_start_time, AUTO_STOP_DELAY_MS)) {
                error = AutoMovement_TransitionTo(ctrl, AUTO_STATE_IDLE);
            }
            break;
            
        case AUTO_STATE_ERROR:
            // 错误状态，等待用户干预
            break;
            
        default:
            error = AUTO_ERROR_SYSTEM_BUSY;
            break;
    }
    
    // 更新显示
    AutoMovement_ShowStatus(ctrl);
    
    return error;
}

/**
 * 状态转换函数
 * @param ctrl 控制器结构体指针
 * @param new_state 新状态
 * @return 错误代码
 */
AutoMovementError_t AutoMovement_TransitionTo(AutoMovementControl_t* ctrl, AutoMovementState_t new_state)
{
    if (ctrl == NULL) return AUTO_ERROR_NOT_READY;
    
    ctrl->current_state = new_state;
    ctrl->state_start_time = Timer_GetTick();
    
    // 状态进入处理
    switch (new_state) {
        case AUTO_STATE_IDLE:
            ctrl->is_active = 0;
            LED_OFF();
            break;
            
        case AUTO_STATE_PREPARING:
            LED_ON();  // 指示准备中
            break;
            
        case AUTO_STATE_MOVING_TO_START:
            // 移动到起始位置
            break;
            
        case AUTO_STATE_PATH_MOVING:
            // 开始路径移动
            Timer_StartPathMovement();
            ctrl->last_step_time = Timer_GetTick();
            break;
            
        case AUTO_STATE_DIRECTION_CHANGE:
            // 方向切换暂停
            Timer_StopPathMovement();
            break;
            
        case AUTO_STATE_STOPPING:
            Timer_StopPathMovement();
            break;
            
        case AUTO_STATE_ERROR:
            Timer_StopPathMovement();
            LED_ON();  // 错误指示
            break;
            
        default:
            break;
    }
    
    return AUTO_OK;
}

/**
 * 处理准备阶段
 * @param ctrl 控制器结构体指针
 * @return 错误代码
 */
AutoMovementError_t AutoMovement_HandlePreparing(AutoMovementControl_t* ctrl)
{
    if (ctrl == NULL) return AUTO_ERROR_NOT_READY;
    
    // 等待准备延时
    if (Timer_IsTimeout(ctrl->state_start_time, AUTO_PREPARE_DELAY_MS)) {
        // 初始化路径插值
        GeometryError_t geo_error = Path_Initialize(&ctrl->path, ctrl->point_a, ctrl->point_b);
        if (geo_error != GEOMETRY_OK) {
            return AUTO_ERROR_GEOMETRY_CALC;
        }
        
        // 设置路径方向
        Path_SetDirection(&ctrl->path, ctrl->current_direction);
        
        // 转换到移动到起始点状态
        return AutoMovement_TransitionTo(ctrl, AUTO_STATE_MOVING_TO_START);
    }
    
    return AUTO_OK;
}

/**
 * 处理移动到起始点
 * @param ctrl 控制器结构体指针
 * @return 错误代码
 */
AutoMovementError_t AutoMovement_HandleMovingToStart(AutoMovementControl_t* ctrl)
{
    if (ctrl == NULL) return AUTO_ERROR_NOT_READY;
    
    // 移动到起始位置
    ServoAngle_t start_angle = (ctrl->current_direction == 0) ? ctrl->servo_a : ctrl->servo_b;
    
    AutoMovementError_t error = AutoMovement_MoveToPosition(start_angle, 1000);
    if (error != AUTO_OK) return error;
    
    // 等待移动完成
    if (Timer_IsTimeout(ctrl->state_start_time, 1500)) {  // 1.5秒等待移动完成
        // 转换到路径移动状态
        return AutoMovement_TransitionTo(ctrl, AUTO_STATE_PATH_MOVING);
    }
    
    return AUTO_OK;
}

/**
 * 处理路径移动
 * @param ctrl 控制器结构体指针
 * @return 错误代码
 */
AutoMovementError_t AutoMovement_HandlePathMoving(AutoMovementControl_t* ctrl)
{
    if (ctrl == NULL) return AUTO_ERROR_NOT_READY;
    
    // 检查是否到了下一步时间
    if (Timer_IsPathStepReady()) {
        AutoMovementError_t error = AutoMovement_ExecuteNextStep(ctrl);
        if (error != AUTO_OK) return error;
        
        Timer_ResetPathStep();
    }
    
    // 检查路径是否完成
    if (Path_IsComplete(&ctrl->path)) {
        // 路径完成，准备切换方向
        return AutoMovement_TransitionTo(ctrl, AUTO_STATE_DIRECTION_CHANGE);
    }
    
    return AUTO_OK;
}

/**
 * 处理方向切换
 * @param ctrl 控制器结构体指针
 * @return 错误代码
 */
AutoMovementError_t AutoMovement_HandleDirectionChange(AutoMovementControl_t* ctrl)
{
    if (ctrl == NULL) return AUTO_ERROR_NOT_READY;
    
    // 等待方向切换延时
    if (Timer_IsTimeout(ctrl->state_start_time, AUTO_DIRECTION_PAUSE_MS)) {
        // 切换方向
        ctrl->current_direction = !ctrl->current_direction;
        Path_SetDirection(&ctrl->path, ctrl->current_direction);
        
        // 更新往返计数
        if (ctrl->current_direction == 0) {  // 回到A→B方向，完成一个往返周期
            ctrl->completed_cycles++;
        }
        
        // 检查是否达到最大往返次数
        if (ctrl->max_cycles > 0 && ctrl->completed_cycles >= ctrl->max_cycles) {
            return AutoMovement_Stop(ctrl);
        }
        
        // 继续路径移动
        return AutoMovement_TransitionTo(ctrl, AUTO_STATE_PATH_MOVING);
    }
    
    return AUTO_OK;
}

/**
 * 执行下一步移动
 * @param ctrl 控制器结构体指针
 * @return 错误代码
 */
AutoMovementError_t AutoMovement_ExecuteNextStep(AutoMovementControl_t* ctrl)
{
    if (ctrl == NULL) return AUTO_ERROR_NOT_READY;

    ServoAngle_t next_servo;
    GeometryError_t error = Path_GetNextStep(&ctrl->path, &next_servo);

    if (error == GEOMETRY_OK) {
        // 移动到下一个位置
        AutoMovementError_t move_error = AutoMovement_MoveToPosition(next_servo, AUTO_MOVE_STEP_TIME_MS);
        if (move_error != AUTO_OK) return move_error;

        ctrl->current_step = ctrl->path.current_step;
        ctrl->last_step_time = Timer_GetTick();

        return AUTO_OK;
    } else {
        return AUTO_ERROR_GEOMETRY_CALC;
    }
}

/**
 * 移动舵机到指定位置
 * @param target_angle 目标角度
 * @param time_ms 移动时间
 * @return 错误代码
 */
AutoMovementError_t AutoMovement_MoveToPosition(ServoAngle_t target_angle, uint16_t time_ms)
{
    Servo_SetPositionWithTime(SERVO_PAN_ID, target_angle.pan, time_ms);
    Servo_SetPositionWithTime(SERVO_TILT_ID, target_angle.tilt, time_ms);
    return AUTO_OK;
}

/**
 * 从手动记录模块加载点位数据
 * @param ctrl 控制器结构体指针
 * @return 错误代码
 */
AutoMovementError_t AutoMovement_LoadPointsFromRecord(AutoMovementControl_t* ctrl)
{
    if (ctrl == NULL) return AUTO_ERROR_NOT_READY;

    // 检查点位是否已记录
    if (!ManualRecord_IsPointSaved(1) || !ManualRecord_IsPointSaved(2)) {
        return AUTO_ERROR_INVALID_POINTS;
    }

    // 加载A点数据
    RecordError_t error1 = ManualRecord_LoadPointData(1, &ctrl->point_a, &ctrl->servo_a);
    if (error1 != RECORD_OK) return AUTO_ERROR_INVALID_POINTS;

    // 加载B点数据
    RecordError_t error2 = ManualRecord_LoadPointData(2, &ctrl->point_b, &ctrl->servo_b);
    if (error2 != RECORD_OK) return AUTO_ERROR_INVALID_POINTS;

    ctrl->points_valid = 1;
    return AUTO_OK;
}

/**
 * 检查点位数据有效性
 * @param ctrl 控制器结构体指针
 * @return 1=有效, 0=无效
 */
uint8_t AutoMovement_ArePointsValid(AutoMovementControl_t* ctrl)
{
    if (ctrl == NULL) return 0;

    if (!ctrl->points_valid) return 0;

    // 检查几何有效性
    if (!Geometry_IsWallPointValid(ctrl->point_a) ||
        !Geometry_IsWallPointValid(ctrl->point_b)) {
        return 0;
    }

    // 检查两点距离
    float distance = Geometry_CalculateDistance(ctrl->point_a, ctrl->point_b);
    if (distance < 100.0f || distance > 2000.0f) {  // 距离在100mm-2000mm之间
        return 0;
    }

    return 1;
}

/**
 * 停止自动往返
 * @param ctrl 控制器结构体指针
 * @return 错误代码
 */
AutoMovementError_t AutoMovement_Stop(AutoMovementControl_t* ctrl)
{
    if (ctrl == NULL) return AUTO_ERROR_NOT_READY;

    ctrl->user_stop_requested = 1;

    // 转换到停止状态
    return AutoMovement_TransitionTo(ctrl, AUTO_STATE_STOPPING);
}

/**
 * 获取移动进度
 * @param ctrl 控制器结构体指针
 * @return 进度百分比 (0.0-1.0)
 */
float AutoMovement_GetProgress(AutoMovementControl_t* ctrl)
{
    if (ctrl == NULL || !ctrl->is_active) return 0.0f;

    if (ctrl->current_state == AUTO_STATE_PATH_MOVING) {
        return (float)ctrl->current_step / (float)ctrl->total_steps;
    }

    return 0.0f;
}

/**
 * 显示系统状态
 * @param ctrl 控制器结构体指针
 */
void AutoMovement_ShowStatus(AutoMovementControl_t* ctrl)
{
    if (ctrl == NULL) return;

    // 显示当前状态
    const char* state_str = AutoMovement_GetStateString(ctrl->current_state);
    char status_str[16];
    sprintf(status_str, "S:%s", state_str);
    OLED_ShowString(1, 1, status_str);

    // 显示移动信息
    if (ctrl->is_active) {
        // 显示方向和进度
        char dir_str[16];
        sprintf(dir_str, "%s %.0f%%",
                ctrl->current_direction ? "B->A" : "A->B",
                AutoMovement_GetProgress(ctrl) * 100);
        OLED_ShowString(2, 1, dir_str);

        // 显示往返次数
        char cycle_str[16];
        sprintf(cycle_str, "Cycle:%d", ctrl->completed_cycles);
        OLED_ShowString(3, 1, cycle_str);

        // 显示时间信息
        uint32_t elapsed = (Timer_GetTick() - ctrl->movement_start_time) * 20; // 20ms per tick
        char time_str[16];
        sprintf(time_str, "T:%lus", elapsed / 1000);
        OLED_ShowString(4, 1, time_str);
    } else {
        OLED_ShowString(2, 1, "Ready");
        OLED_ShowString(3, 1, "Press PB1");
        OLED_ShowString(4, 1, "to start");
    }
}

/**
 * 安全检查
 * @param ctrl 控制器结构体指针
 * @return 错误代码
 */
AutoMovementError_t AutoMovement_CheckSafety(AutoMovementControl_t* ctrl)
{
    if (ctrl == NULL) return AUTO_ERROR_NOT_READY;

    // 检查点位数据
    if (!AutoMovement_ArePointsValid(ctrl)) {
        return AUTO_ERROR_INVALID_POINTS;
    }

    return AUTO_OK;
}

/**
 * 获取状态字符串
 * @param state 状态
 * @return 状态字符串
 */
const char* AutoMovement_GetStateString(AutoMovementState_t state)
{
    switch (state) {
        case AUTO_STATE_IDLE:           return "Idle";
        case AUTO_STATE_PREPARING:      return "Prep";
        case AUTO_STATE_MOVING_TO_START: return "Start";
        case AUTO_STATE_PATH_MOVING:    return "Move";
        case AUTO_STATE_DIRECTION_CHANGE: return "Turn";
        case AUTO_STATE_PAUSED:         return "Pause";
        case AUTO_STATE_STOPPING:      return "Stop";
        case AUTO_STATE_ERROR:          return "Error";
        default:                        return "Unknown";
    }
}

/**
 * 检查是否处于活动状态
 * @param ctrl 控制器结构体指针
 * @return 1=活动, 0=非活动
 */
uint8_t AutoMovement_IsActive(AutoMovementControl_t* ctrl)
{
    if (ctrl == NULL) return 0;
    return ctrl->is_active;
}

/**
 * 紧急停止函数
 * @param ctrl 控制器结构体指针
 * @return 错误代码
 */
AutoMovementError_t AutoMovement_EmergencyStop(AutoMovementControl_t* ctrl)
{
    if (ctrl == NULL) return AUTO_ERROR_NOT_READY;
    
    // 设置紧急停止标志
    ctrl->emergency_stop = 1;
    ctrl->is_active = 0;
    ctrl->is_paused = 0;
    
    // 停止路径移动定时
    Timer_StopPathMovement();
    
    // 转换到错误状态
    return AutoMovement_TransitionTo(ctrl, AUTO_STATE_ERROR);
}

/**
 * 错误处理函数
 * @param ctrl 控制器结构体指针
 * @param error 错误代码
 */
void AutoMovement_HandleError(AutoMovementControl_t* ctrl, AutoMovementError_t error)
{
    if (ctrl == NULL) return;
    
    // 根据错误类型进行不同处理
    switch (error) {
        case AUTO_ERROR_EMERGENCY_STOP:
            // 紧急停止，立即停止所有操作
            ctrl->emergency_stop = 1;
            ctrl->is_active = 0;
            Timer_StopPathMovement();
            break;
            
        case AUTO_ERROR_SERVO_COMM:
            // 舵机通信错误，停止移动
            ctrl->is_active = 0;
            Timer_StopPathMovement();
            break;
            
        case AUTO_ERROR_GEOMETRY_CALC:
            // 几何计算错误，停止移动
            ctrl->is_active = 0;
            Timer_StopPathMovement();
            break;
            
        case AUTO_ERROR_INVALID_POINTS:
            // 点位无效，停止移动
            ctrl->is_active = 0;
            ctrl->points_valid = 0;
            break;
            
        case AUTO_ERROR_USER_STOP:
            // 用户停止，正常停止
            ctrl->user_stop_requested = 1;
            break;
            
        default:
            // 其他错误，停止移动
            ctrl->is_active = 0;
            break;
    }
    
    // 转换到错误状态
    AutoMovement_TransitionTo(ctrl, AUTO_STATE_ERROR);
}
