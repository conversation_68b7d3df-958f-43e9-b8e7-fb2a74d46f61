Component: ARM Compiler 5.06 update 3 (build 300) Tool: armlink [4d35c9]

==============================================================================

Section Cross References

    main.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_Init
    main.o(.text) refers to common.o(.text) for Stm32_Clock_Init
    main.o(.text) refers to led.o(.text) for LED_Init
    main.o(.text) refers to key.o(.text) for KEY_Init
    main.o(.text) refers to exti.o(.text) for EXTIX_Init
    stm32f4xx_it.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_IncTick
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to exti.o(.text) for EXTI9_5_IRQHandler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(.text) for SystemInit
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    led.o(.text) refers to stm32f4xx_hal_gpio.o(.text) for HAL_GPIO_Init
    key.o(.text) refers to stm32f4xx_hal_gpio.o(.text) for HAL_GPIO_Init
    key.o(.text) refers to key.o(.data) for keyup_data
    exti.o(.text) refers to stm32f4xx_hal_gpio.o(.text) for HAL_GPIO_Init
    exti.o(.text) refers to stm32f4xx_hal_cortex.o(.text) for HAL_NVIC_SetPriority
    exti.o(.text) refers to common.o(.text) for delay_ms
    stm32f4xx_hal.o(.text) refers to stm32f4xx_hal_cortex.o(.text) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(.text) refers to stm32f4xx_hal_msp.o(.text) for HAL_MspInit
    stm32f4xx_hal.o(.text) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_gpio.o(.text) refers to exti.o(.text) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_pwr_ex.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.text) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(.text) refers to stm32f4xx_hal_gpio.o(.text) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(.text) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    common.o(.text) refers to stm32f4xx_hal_rcc.o(.text) for HAL_RCC_OscConfig
    common.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetREVID
    common.o(.text) refers to stm32f4xx_hal_cortex.o(.text) for HAL_SYSTICK_CLKSourceConfig
    common.o(.text) refers to common.o(.data) for fac_us
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing led.o(.rrx_text), (6 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing exti.o(.rev16_text), (4 bytes).
    Removing exti.o(.revsh_text), (4 bytes).
    Removing exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(.text), (476 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text), (256 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text), (1140 bytes).
    Removing common.o(.rev16_text), (4 bytes).
    Removing common.o(.revsh_text), (4 bytes).
    Removing common.o(.rrx_text), (6 bytes).
    Removing common.o(.emb_text), (16 bytes).

49 unused section(s) (total 2098 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ..\Common\common.c                       0x00000000   Number         0  common.o ABSOLUTE
    ..\Main\main.c                           0x00000000   Number         0  main.o ABSOLUTE
    ..\Main\stm32f4xx_it.c                   0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Startup_config\startup_stm32f407xx.s  0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    ..\Startup_config\stm32f4xx_hal_msp.c    0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Startup_config\system_stm32f4xx.c     0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\USER\EXTI\exti.c                      0x00000000   Number         0  exti.o ABSOLUTE
    ..\USER\KEY\key.c                        0x00000000   Number         0  key.o ABSOLUTE
    ..\USER\LED\led.c                        0x00000000   Number         0  led.o ABSOLUTE
    ..\\Common\\common.c                     0x00000000   Number         0  common.o ABSOLUTE
    ..\\Main\\main.c                         0x00000000   Number         0  main.o ABSOLUTE
    ..\\Main\\stm32f4xx_it.c                 0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\src\\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\\Startup_config\\stm32f4xx_hal_msp.c  0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\\Startup_config\\system_stm32f4xx.c   0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\\USER\\EXTI\\exti.c                   0x00000000   Number         0  exti.o ABSOLUTE
    ..\\USER\\KEY\\key.c                     0x00000000   Number         0  key.o ABSOLUTE
    ..\\USER\\LED\\led.c                     0x00000000   Number         0  led.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x080001fc   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x080001fe   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000202   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000204   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000206   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000208   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000208   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000208   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800020e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800020e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000212   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000212   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800021a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800021c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800021c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000220   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000228   Section        0  main.o(.text)
    .text                                    0x08000274   Section        0  stm32f4xx_it.o(.text)
    .text                                    0x08000294   Section        0  system_stm32f4xx.o(.text)
    .text                                    0x080003b4   Section        0  stm32f4xx_hal_msp.o(.text)
    .text                                    0x080003b8   Section       64  startup_stm32f407xx.o(.text)
    $v0                                      0x080003b8   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x080003f8   Section        0  led.o(.text)
    .text                                    0x08000454   Section        0  key.o(.text)
    .text                                    0x08000578   Section        0  exti.o(.text)
    .text                                    0x08000664   Section        0  stm32f4xx_hal.o(.text)
    .text                                    0x08000858   Section        0  stm32f4xx_hal_cortex.o(.text)
    __NVIC_SetPriority                       0x08000859   Thumb Code    32  stm32f4xx_hal_cortex.o(.text)
    __NVIC_GetPriorityGrouping               0x08000c09   Thumb Code    12  stm32f4xx_hal_cortex.o(.text)
    .text                                    0x08000c28   Section        0  stm32f4xx_hal_gpio.o(.text)
    .text                                    0x08000fdc   Section        0  stm32f4xx_hal_rcc.o(.text)
    .text                                    0x080018c8   Section        0  common.o(.text)
    .text                                    0x08001c24   Section      238  lludivv7m.o(.text)
    .text                                    0x08001d12   Section        0  heapauxi.o(.text)
    .text                                    0x08001d18   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001d62   Section        0  exit.o(.text)
    .text                                    0x08001d74   Section        8  libspace.o(.text)
    .text                                    0x08001d7c   Section        0  sys_exit.o(.text)
    .text                                    0x08001d88   Section        2  use_no_semi.o(.text)
    .text                                    0x08001d8a   Section        0  indicate_semi.o(.text)
    x$fpl$fpinit                             0x08001d8a   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08001d8a   Number         0  fpinit.o(x$fpl$fpinit)
    .constdata                               0x08001d94   Section       24  system_stm32f4xx.o(.constdata)
    .data                                    0x20000000   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000004   Section        6  key.o(.data)
    .data                                    0x2000000c   Section        9  stm32f4xx_hal.o(.data)
    .data                                    0x20000015   Section        1  common.o(.data)
    fac_us                                   0x20000015   Data           1  common.o(.data)
    .bss                                     0x20000018   Section       96  libspace.o(.bss)
    HEAP                                     0x20000078   Section      512  startup_stm32f407xx.o(HEAP)
    Heap_Mem                                 0x20000078   Data         512  startup_stm32f407xx.o(HEAP)
    STACK                                    0x20000278   Section     1024  startup_stm32f407xx.o(STACK)
    Stack_Mem                                0x20000278   Data        1024  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x20000678   Data           0  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x080001fd   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080001ff   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_alloca_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000205   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000209   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000209   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000209   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800020f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800020f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000213   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000213   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800021b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800021d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800021d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000221   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    main                                     0x08000229   Thumb Code    72  main.o(.text)
    NMI_Handler                              0x08000275   Thumb Code     2  stm32f4xx_it.o(.text)
    HardFault_Handler                        0x08000277   Thumb Code     4  stm32f4xx_it.o(.text)
    MemManage_Handler                        0x0800027b   Thumb Code     4  stm32f4xx_it.o(.text)
    BusFault_Handler                         0x0800027f   Thumb Code     4  stm32f4xx_it.o(.text)
    UsageFault_Handler                       0x08000283   Thumb Code     4  stm32f4xx_it.o(.text)
    SVC_Handler                              0x08000287   Thumb Code     2  stm32f4xx_it.o(.text)
    DebugMon_Handler                         0x08000289   Thumb Code     2  stm32f4xx_it.o(.text)
    PendSV_Handler                           0x0800028b   Thumb Code     2  stm32f4xx_it.o(.text)
    SysTick_Handler                          0x0800028d   Thumb Code     8  stm32f4xx_it.o(.text)
    SystemInit                               0x08000295   Thumb Code    82  system_stm32f4xx.o(.text)
    SystemCoreClockUpdate                    0x080002e7   Thumb Code   174  system_stm32f4xx.o(.text)
    HAL_MspInit                              0x080003b5   Thumb Code     2  stm32f4xx_hal_msp.o(.text)
    HAL_MspDeInit                            0x080003b7   Thumb Code     2  stm32f4xx_hal_msp.o(.text)
    Reset_Handler                            0x080003b9   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART1_IRQHandler                        0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART2_IRQHandler                        0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x080003d3   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x080003d5   Thumb Code     0  startup_stm32f407xx.o(.text)
    LED_Init                                 0x080003f9   Thumb Code    84  led.o(.text)
    KEY_Init                                 0x08000455   Thumb Code    66  key.o(.text)
    key_scan                                 0x08000497   Thumb Code   192  key.o(.text)
    EXTIX_Init                               0x08000579   Thumb Code    78  exti.o(.text)
    EXTI9_5_IRQHandler                       0x080005c7   Thumb Code    68  exti.o(.text)
    HAL_GPIO_EXTI_Callback                   0x0800060b   Thumb Code    66  exti.o(.text)
    HAL_InitTick                             0x08000667   Thumb Code    64  stm32f4xx_hal.o(.text)
    HAL_Init                                 0x080006a7   Thumb Code    44  stm32f4xx_hal.o(.text)
    HAL_DeInit                               0x080006d5   Thumb Code    64  stm32f4xx_hal.o(.text)
    HAL_IncTick                              0x08000715   Thumb Code    16  stm32f4xx_hal.o(.text)
    HAL_GetTick                              0x08000725   Thumb Code     6  stm32f4xx_hal.o(.text)
    HAL_GetTickPrio                          0x0800072b   Thumb Code     6  stm32f4xx_hal.o(.text)
    HAL_SetTickFreq                          0x08000731   Thumb Code    32  stm32f4xx_hal.o(.text)
    HAL_GetTickFreq                          0x08000751   Thumb Code     6  stm32f4xx_hal.o(.text)
    HAL_Delay                                0x08000757   Thumb Code    36  stm32f4xx_hal.o(.text)
    HAL_SuspendTick                          0x0800077b   Thumb Code    18  stm32f4xx_hal.o(.text)
    HAL_ResumeTick                           0x0800078d   Thumb Code    18  stm32f4xx_hal.o(.text)
    HAL_GetHalVersion                        0x0800079f   Thumb Code     4  stm32f4xx_hal.o(.text)
    HAL_GetREVID                             0x080007a3   Thumb Code     8  stm32f4xx_hal.o(.text)
    HAL_GetDEVID                             0x080007ab   Thumb Code    10  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_EnableDBGSleepMode            0x080007b5   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_DisableDBGSleepMode           0x080007c3   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_EnableDBGStopMode             0x080007d1   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_DisableDBGStopMode            0x080007df   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_EnableDBGStandbyMode          0x080007ed   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_DisableDBGStandbyMode         0x080007fb   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_EnableCompensationCell               0x08000809   Thumb Code     8  stm32f4xx_hal.o(.text)
    HAL_DisableCompensationCell              0x08000811   Thumb Code     8  stm32f4xx_hal.o(.text)
    HAL_GetUIDw0                             0x08000819   Thumb Code     6  stm32f4xx_hal.o(.text)
    HAL_GetUIDw1                             0x0800081f   Thumb Code     8  stm32f4xx_hal.o(.text)
    HAL_GetUIDw2                             0x08000827   Thumb Code     8  stm32f4xx_hal.o(.text)
    HAL_NVIC_SetPriorityGrouping             0x08000879   Thumb Code    36  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_SetPriority                     0x0800089d   Thumb Code   124  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_EnableIRQ                       0x08000919   Thumb Code    32  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_DisableIRQ                      0x08000939   Thumb Code    62  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_SystemReset                     0x08000977   Thumb Code    64  stm32f4xx_hal_cortex.o(.text)
    HAL_SYSTICK_Config                       0x080009b7   Thumb Code    52  stm32f4xx_hal_cortex.o(.text)
    HAL_MPU_Disable                          0x080009eb   Thumb Code    42  stm32f4xx_hal_cortex.o(.text)
    HAL_MPU_Enable                           0x08000a15   Thumb Code    60  stm32f4xx_hal_cortex.o(.text)
    HAL_MPU_ConfigRegion                     0x08000a51   Thumb Code    90  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_GetPriorityGrouping             0x08000aab   Thumb Code     8  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_GetPriority                     0x08000ab3   Thumb Code   138  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_SetPendingIRQ                   0x08000b3d   Thumb Code    32  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_GetPendingIRQ                   0x08000b5d   Thumb Code    46  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_ClearPendingIRQ                 0x08000b8b   Thumb Code    30  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_GetActive                       0x08000ba9   Thumb Code    46  stm32f4xx_hal_cortex.o(.text)
    HAL_SYSTICK_CLKSourceConfig              0x08000bd7   Thumb Code    40  stm32f4xx_hal_cortex.o(.text)
    HAL_SYSTICK_Callback                     0x08000bff   Thumb Code     2  stm32f4xx_hal_cortex.o(.text)
    HAL_SYSTICK_IRQHandler                   0x08000c01   Thumb Code     8  stm32f4xx_hal_cortex.o(.text)
    HAL_GPIO_Init                            0x08000c29   Thumb Code   466  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_DeInit                          0x08000dfb   Thumb Code   316  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_ReadPin                         0x08000f37   Thumb Code    16  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_WritePin                        0x08000f47   Thumb Code    12  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_TogglePin                       0x08000f53   Thumb Code    18  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_LockPin                         0x08000f65   Thumb Code    46  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_EXTI_IRQHandler                 0x08000f95   Thumb Code    28  stm32f4xx_hal_gpio.o(.text)
    HAL_RCC_OscConfig                        0x08000fe1   Thumb Code  1086  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetSysClockFreq                  0x0800141f   Thumb Code   164  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_ClockConfig                      0x080014c3   Thumb Code   388  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_MCOConfig                        0x08001647   Thumb Code   186  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_EnableCSS                        0x08001701   Thumb Code     8  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_DisableCSS                       0x08001709   Thumb Code     8  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetHCLKFreq                      0x08001711   Thumb Code     6  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetPCLK1Freq                     0x08001717   Thumb Code    24  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetPCLK2Freq                     0x0800172f   Thumb Code    24  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetOscConfig                     0x08001747   Thumb Code   278  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetClockConfig                   0x0800185d   Thumb Code    66  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_CSSCallback                      0x0800189f   Thumb Code     2  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_NMI_IRQHandler                   0x080018a1   Thumb Code    30  stm32f4xx_hal_rcc.o(.text)
    GPIO_group_OUT                           0x080018c9   Thumb Code   454  common.o(.text)
    GPIO_bits_OUT                            0x08001a8f   Thumb Code    92  common.o(.text)
    Stm32_Clock_Init                         0x08001aeb   Thumb Code   190  common.o(.text)
    delay_init                               0x08001ba9   Thumb Code    16  common.o(.text)
    delay_us                                 0x08001bb9   Thumb Code    68  common.o(.text)
    delay_ms                                 0x08001bfd   Thumb Code    24  common.o(.text)
    __aeabi_uldivmod                         0x08001c25   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x08001c25   Thumb Code   238  lludivv7m.o(.text)
    __use_two_region_memory                  0x08001d13   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08001d15   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08001d17   Thumb Code     2  heapauxi.o(.text)
    __user_setup_stackheap                   0x08001d19   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08001d63   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x08001d75   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08001d75   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08001d75   Thumb Code     0  libspace.o(.text)
    _sys_exit                                0x08001d7d   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08001d89   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08001d89   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08001d8b   Thumb Code     0  indicate_semi.o(.text)
    _fp_init                                 0x08001d8b   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08001d93   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08001d93   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    AHBPrescTable                            0x08001d94   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x08001da4   Data           8  system_stm32f4xx.o(.constdata)
    Region$$Table$$Base                      0x08001dac   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08001dcc   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f4xx.o(.data)
    keydown_data                             0x20000004   Data           1  key.o(.data)
    keyup_data                               0x20000005   Data           1  key.o(.data)
    key_time                                 0x20000006   Data           2  key.o(.data)
    key_tem                                  0x20000008   Data           1  key.o(.data)
    key_bak                                  0x20000009   Data           1  key.o(.data)
    uwTick                                   0x2000000c   Data           4  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000010   Data           4  stm32f4xx_hal.o(.data)
    uwTickFreq                               0x20000014   Data           1  stm32f4xx_hal.o(.data)
    __libspace_start                         0x20000018   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000078   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00001de4, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Base: 0x08000000, Size: 0x00001dcc, Max: 0x00100000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x00000188   Data   RO          322    RESET               startup_stm32f407xx.o
    0x08000188   0x00000008   Code   RO          619  * !!!main             c_w.l(__main.o)
    0x08000190   0x00000034   Code   RO          786    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x0000001a   Code   RO          788    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x00000002   PAD
    0x080001e0   0x0000001c   Code   RO          790    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x00000002   Code   RO          656    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001fe   0x00000004   Code   RO          668    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          671    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          674    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          676    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          678    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          681    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          683    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          685    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          687    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          689    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          691    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          693    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          695    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          697    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          699    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          701    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          705    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          707    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          709    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO          711    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000202   0x00000002   Code   RO          712    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000204   0x00000002   Code   RO          743    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000206   0x00000000   Code   RO          769    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000206   0x00000000   Code   RO          771    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000206   0x00000000   Code   RO          774    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000206   0x00000000   Code   RO          777    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000206   0x00000000   Code   RO          779    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000206   0x00000000   Code   RO          782    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000206   0x00000002   Code   RO          783    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000208   0x00000000   Code   RO          621    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000208   0x00000000   Code   RO          627    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000208   0x00000006   Code   RO          639    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800020e   0x00000000   Code   RO          629    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800020e   0x00000004   Code   RO          630    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000212   0x00000000   Code   RO          632    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000212   0x00000008   Code   RO          633    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800021a   0x00000002   Code   RO          660    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800021c   0x00000000   Code   RO          716    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800021c   0x00000004   Code   RO          717    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000220   0x00000006   Code   RO          718    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000226   0x00000002   PAD
    0x08000228   0x0000004c   Code   RO            4    .text               main.o
    0x08000274   0x00000020   Code   RO          238    .text               stm32f4xx_it.o
    0x08000294   0x00000120   Code   RO          268    .text               system_stm32f4xx.o
    0x080003b4   0x00000004   Code   RO          299    .text               stm32f4xx_hal_msp.o
    0x080003b8   0x00000040   Code   RO          323    .text               startup_stm32f407xx.o
    0x080003f8   0x0000005c   Code   RO          330    .text               led.o
    0x08000454   0x00000124   Code   RO          354    .text               key.o
    0x08000578   0x000000ec   Code   RO          384    .text               exti.o
    0x08000664   0x000001f4   Code   RO          411    .text               stm32f4xx_hal.o
    0x08000858   0x000003d0   Code   RO          438    .text               stm32f4xx_hal_cortex.o
    0x08000c28   0x000003b4   Code   RO          466    .text               stm32f4xx_hal_gpio.o
    0x08000fdc   0x000008ec   Code   RO          538    .text               stm32f4xx_hal_rcc.o
    0x080018c8   0x0000035c   Code   RO          587    .text               common.o
    0x08001c24   0x000000ee   Code   RO          615    .text               c_w.l(lludivv7m.o)
    0x08001d12   0x00000006   Code   RO          617    .text               c_w.l(heapauxi.o)
    0x08001d18   0x0000004a   Code   RO          643    .text               c_w.l(sys_stackheap_outer.o)
    0x08001d62   0x00000012   Code   RO          645    .text               c_w.l(exit.o)
    0x08001d74   0x00000008   Code   RO          657    .text               c_w.l(libspace.o)
    0x08001d7c   0x0000000c   Code   RO          713    .text               c_w.l(sys_exit.o)
    0x08001d88   0x00000002   Code   RO          732    .text               c_w.l(use_no_semi.o)
    0x08001d8a   0x00000000   Code   RO          734    .text               c_w.l(indicate_semi.o)
    0x08001d8a   0x0000000a   Code   RO          728    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08001d94   0x00000018   Data   RO          269    .constdata          system_stm32f4xx.o
    0x08001dac   0x00000020   Data   RO          784    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Base: 0x20000000, Size: 0x00000678, Max: 0x00020000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x00000004   Data   RW          270    .data               system_stm32f4xx.o
    0x20000004   0x00000006   Data   RW          355    .data               key.o
    0x2000000a   0x00000002   PAD
    0x2000000c   0x00000009   Data   RW          412    .data               stm32f4xx_hal.o
    0x20000015   0x00000001   Data   RW          588    .data               common.o
    0x20000016   0x00000002   PAD
    0x20000018   0x00000060   Zero   RW          658    .bss                c_w.l(libspace.o)
    0x20000078   0x00000200   Zero   RW          321    HEAP                startup_stm32f407xx.o
    0x20000278   0x00000400   Zero   RW          320    STACK               startup_stm32f407xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       860         48          0          1          0       3317   common.o
       236         24          0          0          0       1575   exti.o
       292         34          0          6          0       1827   key.o
        92          8          0          0          0       1019   led.o
        76          4          0          0          0     646899   main.o
        64         26        392          0       1536        908   startup_stm32f407xx.o
       500         42          0          9          0       6172   stm32f4xx_hal.o
       976         20          0          0          0      36418   stm32f4xx_hal_cortex.o
       948         44          0          0          0       3783   stm32f4xx_hal_gpio.o
         4          0          0          0          0        664   stm32f4xx_hal_msp.o
      2284         66          0          0          0       5467   stm32f4xx_hal_rcc.o
        32          0          0          0          0       1466   stm32f4xx_it.o
       288         32         24          4          0       1865   system_stm32f4xx.o

    ----------------------------------------------------------------------
      6652        <USER>        <GROUP>         24       1536     711380   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         0          0          0          4          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         6          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       238          0          0          0          0        100   lludivv7m.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        10          0          0          0          0        116   fpinit.o

    ----------------------------------------------------------------------
       528         <USER>          <GROUP>          0         96        800   Library Totals
         4          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       514         16          0          0         96        684   c_w.l
        10          0          0          0          0        116   fz_wm.l

    ----------------------------------------------------------------------
       528         <USER>          <GROUP>          0         96        800   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      7180        364        448         24       1632     709872   Grand Totals
      7180        364        448         24       1632     709872   ELF Image Totals
      7180        364        448         24          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 7628 (   7.45kB)
    Total RW  Size (RW Data + ZI Data)              1656 (   1.62kB)
    Total ROM Size (Code + RO Data + RW Data)       7652 (   7.47kB)

==============================================================================

