#ifndef __SERVO_H
#define __SERVO_H

#include "stdint.h"

// 协议定义
#define SERVO_HEADER                0x55
#define SERVO_MAX_ANGLE             240.0f
#define SERVO_MAX_POSITION          1000
#define SERVO_TIMEOUT_MS            100

// 舵机ID定义 - 激光云台项目
#define SERVO_PAN_ID                1       // 水平舵机ID
#define SERVO_TILT_ID               2       // 垂直舵机ID

// HTS-25L指令定义
#define SERVO_CMD_MOVE_TIME_WRITE   0x01    // 写入位置和时间
#define SERVO_CMD_MOVE_TIME_READ    0x02    // 读取位置和时间
#define SERVO_CMD_MOVE_TIME_WAIT_WRITE 0x07 // 写入位置和时间(等待)
#define SERVO_CMD_MOVE_TIME_WAIT_READ  0x08 // 读取位置和时间(等待)
#define SERVO_CMD_MOVE_START        0x0B    // 开始运动
#define SERVO_CMD_MOVE_STOP         0x0C    // 停止运动
#define SERVO_CMD_ID_WRITE          0x0D    // 写入ID
#define SERVO_CMD_ID_READ           0x0E    // 读取ID
#define SERVO_CMD_ANGLE_OFFSET_ADJUST 0x11  // 角度偏移调整
#define SERVO_CMD_ANGLE_OFFSET_WRITE  0x12  // 写入角度偏移
#define SERVO_CMD_ANGLE_OFFSET_READ   0x13  // 读取角度偏移
#define SERVO_CMD_ANGLE_LIMIT_WRITE   0x14  // 写入角度限制
#define SERVO_CMD_ANGLE_LIMIT_READ    0x15  // 读取角度限制
#define SERVO_CMD_VIN_LIMIT_WRITE     0x16  // 写入电压限制
#define SERVO_CMD_VIN_LIMIT_READ      0x17  // 读取电压限制
#define SERVO_CMD_TEMP_MAX_LIMIT_WRITE 0x18 // 写入最高温度限制
#define SERVO_CMD_TEMP_MAX_LIMIT_READ  0x19 // 读取最高温度限制
#define SERVO_CMD_TEMP_READ           0x1A  // 读取温度
#define SERVO_CMD_VIN_READ            0x1B  // 读取电压
#define SERVO_CMD_POS_READ            0x1C  // 读取位置
#define SERVO_CMD_OR_MOTOR_MODE_WRITE 0x1D  // 写入电机模式
#define SERVO_CMD_OR_MOTOR_MODE_READ  0x1E  // 读取电机模式
#define SERVO_CMD_LOAD_OR_UNLOAD_WRITE 0x1F // 写入加载卸载
#define SERVO_CMD_LOAD_OR_UNLOAD_READ  0x20 // 读取加载卸载
#define SERVO_CMD_LED_CTRL_WRITE      0x21  // 写入LED控制
#define SERVO_CMD_LED_CTRL_READ       0x22  // 读取LED控制

// 舵机状态定义
#define SERVO_LOAD_STATE              1     // 加载状态(有扭矩)
#define SERVO_UNLOAD_STATE            0     // 卸载状态(无扭矩)

// 错误代码定义
typedef enum {
    SERVO_OK = 0,
    SERVO_ERROR_TIMEOUT,
    SERVO_ERROR_CHECKSUM,
    SERVO_ERROR_INVALID_RESPONSE,
    SERVO_ERROR_COMMUNICATION
} ServoError_t;

// 舵机状态结构体
typedef struct {
    uint16_t position;      // 当前位置 (0-1000)
    float angle;           // 当前角度 (0-240°)
    uint8_t temperature;   // 温度 (°C)
    uint16_t voltage;      // 电压 (mV)
    uint8_t load_state;    // 加载状态
    uint8_t led_state;     // LED状态
} ServoStatus_t;

// 基础功能
void Servo_Init(void);
void Servo_SetPosition(uint8_t id, float angle);
void Servo_SetPositionWithTime(uint8_t id, float angle, uint16_t time_ms);

// 扩展功能 - 激光云台项目专用
ServoError_t Servo_ReadPosition(uint8_t id, float* angle);
ServoError_t Servo_SetTorqueEnable(uint8_t id, uint8_t enable);
ServoError_t Servo_GetStatus(uint8_t id, ServoStatus_t* status);
ServoError_t Servo_ReadTemperature(uint8_t id, uint8_t* temperature);
ServoError_t Servo_ReadVoltage(uint8_t id, uint16_t* voltage);

// 内部函数
uint16_t Servo_AngleToPosition(float angle);
float Servo_PositionToAngle(uint16_t position);
uint8_t Servo_CalculateChecksum(uint8_t* data, uint8_t length);
void Servo_SendCommand(uint8_t id, uint8_t cmd, uint8_t* params, uint8_t param_len);
ServoError_t Servo_ReceiveResponse(uint8_t id, uint8_t cmd, uint8_t* data, uint8_t* data_len);

#endif
