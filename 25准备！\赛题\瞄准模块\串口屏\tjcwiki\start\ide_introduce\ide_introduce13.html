<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>13.设备 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
        <script src="../../_static/jquery.js"></script>
        <script src="../../_static/underscore.js"></script>
        <script src="../../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../../_static/doctools.js"></script>
        <script src="../../_static/translations.js"></script>
        <script src="../../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../../_static/custom.js"></script>
    <script src="../../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../../genindex.html" />
    <link rel="search" title="搜索" href="../../search.html" />
    <link rel="next" title="到手测试" href="../first_test.html" />
    <link rel="prev" title="12.下载窗口" href="ide_introduce12.html" />
    <link href="../../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="../index.html">快速入门</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../download_ide.html">下载和安装上位机软件</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html">上位机基本功能介绍</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="ide_introduce1.html">1.菜单栏</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce2.html">2.工具栏</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce3.html">3.工具箱</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce4.html">4.资源文件窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce5.html">5.界面</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce6.html">6.特殊控件窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce7.html">7.输出窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce8.html">8.事件编辑窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce9.html">9.页面窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce10.html">10.属性窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce11.html">11.调试窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce12.html">12.下载窗口</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">13.设备</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id2">设置窗口</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id3">设备窗口</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id4">显示窗口</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id5">工程窗口</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../first_test.html">到手测试</a></li>
<li class="toctree-l2"><a class="reference internal" href="../create_project/index.html">创建工程</a></li>
<li class="toctree-l2"><a class="reference internal" href="../codeSpace.html">代码编写</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="../index.html">快速入门</a> &raquo;</li>
          <li><a href="index.html">上位机基本功能介绍</a> &raquo;</li>
      <li>13.设备</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>13.设备<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<img alt="../../_images/device0.png" src="../../_images/device0.png" />
<p>设备窗口在工具栏中“设备”按钮，点击就可以进入到设备窗口。</p>
<img alt="../../_images/device1.png" src="../../_images/device1.png" />
<section id="id2">
<h2>设置窗口<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<dl class="simple">
<dt>1.设备</dt><dd><p>可以在此选择串口屏系列和串口屏型号。</p>
</dd>
<dt>2.显示</dt><dd><p>设置显示相关的选项</p>
</dd>
<dt>3.工程</dt><dd><p>设置工程相关参数</p>
</dd>
</dl>
</section>
<section id="id3">
<h2>设备窗口<a class="headerlink" href="#id3" title="此标题的永久链接"></a></h2>
<img alt="../../_images/device2.png" src="../../_images/device2.png" />
<dl class="simple">
<dt>1.产品系列</dt><dd><p>软件会根据推出产品系列实时更新目前拥有的产品系列，选择产品型号，下方会出现具体的产品型号。</p>
</dd>
<dt>2.产品型号</dt><dd><p>选择产品系列会在该窗口出现工程具体型号（产品型号命名）。选择工程具体型号，点击ok。软件会根据型号自动生成工程窗口。</p>
</dd>
</dl>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>下载工程进屏，如果工程型号和屏型号不匹配会出现model does not match。</p>
</div>
</section>
<section id="id4">
<h2>显示窗口<a class="headerlink" href="#id4" title="此标题的永久链接"></a></h2>
<img alt="../../_images/device3.png" src="../../_images/device3.png" />
<dl class="simple">
<dt>1.显示方向</dt><dd><p>屏幕支持0 90 180 270四个显示方向。</p>
</dd>
</dl>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>工程不支持运行中改变显示方向。</p>
</div>
<dl class="simple">
<dt>2.字符编码</dt><dd><p>工程字符编码，目前支持：ascii，gb2312，big5，shift-jis，utf-8。</p>
</dd>
</dl>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>字符编码不一致时会出现乱码。</p>
</div>
</section>
<section id="id5">
<h2>工程窗口<a class="headerlink" href="#id5" title="此标题的永久链接"></a></h2>
<img alt="../../_images/device4.png" src="../../_images/device4.png" />
<dl class="simple">
<dt>1.工程打开代码</dt><dd><p>请勿随意设置。设置打开工程是否需要密码，默认是没有密码的（设置密码后如果忘记密码，工程将无法找回，请慎用）。</p>
</dd>
<dt>2.tft文件一次性烧录</dt><dd><p>仅x系列支持。设置sd卡里tft文件是否烧录后修改文件名，用于一次性烧录工程到屏上，烧录完毕无需取出SD卡。</p>
</dd>
<dt>3.编译时忽略图片资源</dt><dd><p>特殊功能使用，请勿勾选。</p>
</dd>
<dt>4.编译时忽略字库资源</dt><dd><p>特殊功能使用，请勿勾选。</p>
</dd>
<dt>5.内存文件存储区大小</dt><dd><p>请勿设置。如需使用请参考 <a class="reference internal" href="../../widgets/DataRecord.html#id24"><span class="std std-ref">将数据记录控件保存在内存中</span></a></p>
</dd>
</dl>
<hr class="docutils" />
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="ide_introduce12.html" class="btn btn-neutral float-left" title="12.下载窗口" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="../first_test.html" class="btn btn-neutral float-right" title="到手测试" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>