<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>按钮控件 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="进度条控件" href="Progress_bar.html" />
    <link rel="prev" title="虚拟浮点数控件" href="Xfloat.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">控件详解</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="component.html">认识控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Page.html">页面控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Text.html">文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Scrolling_text.html">滚动文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Number.html">数字控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Xfloat.html">虚拟浮点数控件</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">按钮控件</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#id3">按钮控件-使用详解</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id4">通过按钮控件跳转页面</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id5">修改按钮控件显示文字的大小和样式</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id6">让按钮在开始和停止之间切换</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id7">按钮禁用触摸功能</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id8">通过按钮控件发出串口数据</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id9">使用按钮实现长按功能</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id10">使用按钮实现双击功能</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id11">使用按钮在多个状态之间切换</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id12">使用按钮在多个状态之间切换2</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#id13">按钮控件-样例工程下载</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id17">按钮控件-相关链接</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id18">按钮控件-属性详解</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="Progress_bar.html">进度条控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Picture.html">图片控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Crop.html">切图控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Hotspot.html">触摸热区控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="TouchCap.html">触摸捕捉控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Gauge.html">指针控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Waveform.html">曲线波形控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Slider.html">滑块控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Timer.html">定时器控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Variable.html">变量控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Dual-state_button.html">双态按钮控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Checkbox.html">复选框控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Radio.html">单选框控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="QRcode.html">二维码控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Switch.html">状态开关控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="ComboBox.html">下拉框控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="TextSelect.html">选择文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="SLText.html">滑动文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="DataRecord.html">数据记录控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="FileBrowser.html">文件浏览器控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="FileStream.html">文件流控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Gmov.html">动画控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Video.html">视频控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Audio.html">音频控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="ExPicture.html">外部图片控件</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">所有控件详解</a> &raquo;</li>
      <li>按钮控件</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>按钮控件<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>视频教程： <a class="reference external" href="https://www.bilibili.com/video/BV1jA4m1P7kT">按钮控件</a></p>
</div>
<p>用于在串口屏上显示按钮，使用前需要提前导入字库，制作字库请参考 <a class="reference internal" href="../start/create_project/create_project_5.html#id1"><span class="std std-ref">创建字库和导入字库</span></a></p>
<p>如何修改显示的字体大小：需要提前导入不同大小的字库，需要修改控件显示的字体大小时，通过上位机编辑或者通过指令修改控件的font属性即可，请参考 <a class="reference internal" href="../QA/QA45.html#id1"><span class="std std-ref">如何修改控件显示的字体</span></a></p>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>导入字库请参考:  <a class="reference internal" href="../QA/QA115.html#id1"><span class="std std-ref">如何导入字库</span></a></p>
<p>导入图片请参考:  <a class="reference internal" href="../QA/QA116.html#id1"><span class="std std-ref">如何导入图片</span></a></p>
<p>导入动画请参考:  <a class="reference internal" href="../QA/QA117.html#id1"><span class="std std-ref">如何导入动画</span></a></p>
<p>导入视频请参考:  <a class="reference internal" href="../QA/QA118.html#id1"><span class="std std-ref">如何导入视频</span></a></p>
<p>导入音频请参考:  <a class="reference internal" href="../QA/QA119.html#id1"><span class="std std-ref">如何导入音频</span></a></p>
</div>
<section id="id3">
<h2>按钮控件-使用详解<a class="headerlink" href="#id3" title="此标题的永久链接"></a></h2>
<section id="id4">
<h3>通过按钮控件跳转页面<a class="headerlink" href="#id4" title="此标题的永久链接"></a></h3>
<img alt="../_images/button_1.jpg" src="../_images/button_1.jpg" />
<p>如上图所示，该工程有三个页面，分别为main，page0，page1，main页面的页面id为0，page0页面的页面id为1，page1页面的页面id为2</p>
<p>在main页面新建一个按钮控件，按钮控件的txt属性改为“跳转页面”，在按钮控件的弹起事件编写以下代码</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span> //推荐此方法
<span class="linenos">2</span> page page1  //跳转到page1页面
</pre></div>
</div>
<img alt="../_images/button_2.jpg" src="../_images/button_2.jpg" />
<p>因为page1页面的页面id是2，因此也可以这么写</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span> //不推荐此方法，当插入页面导致页面顺序改变时会导致页面跳转逻辑混乱
<span class="linenos">2</span> page <span class="m">2</span>  //跳转到id为2的页面（也就是page1页面）
</pre></div>
</div>
<img alt="../_images/button_4.jpg" src="../_images/button_4.jpg" />
</section>
<section id="id5">
<h3>修改按钮控件显示文字的大小和样式<a class="headerlink" href="#id5" title="此标题的永久链接"></a></h3>
<p>我们需要提前导入不同大小的字库，需要修改按钮控件显示大小时，通过上位机进行编辑或者通过指令进行修改即可，请参考 <a class="reference internal" href="../QA/QA45.html#id1"><span class="std std-ref">如何修改控件显示的字体</span></a></p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span>//串口屏控件事件代码（通常写在串口屏控件的按下或弹起事件中）
<span class="linenos"> 2</span>b0.font<span class="o">=</span><span class="m">1</span>   //将b0控件的字体设置为1号字体
<span class="linenos"> 3</span>main.b0.font<span class="o">=</span><span class="m">1</span>    //将main页面的b0控件的字体设置为1号字体，b0控件的vscope属性必须设置为全局
<span class="linenos"> 4</span>
<span class="linenos"> 5</span>
<span class="linenos"> 6</span>//单片机发送指令通过串口修改调用的字库id
<span class="linenos"> 7</span>int <span class="nv">font_id</span> <span class="o">=</span> <span class="m">1</span><span class="p">;</span>   //字库编码
<span class="linenos"> 8</span>char tjcstr<span class="o">[</span><span class="m">100</span><span class="o">]</span><span class="p">;</span>
<span class="linenos"> 9</span>sprintf<span class="o">(</span>tjcstr, <span class="s2">&quot;main.b0.font=%d\xff\xff\xff&quot;</span>,font_id<span class="o">)</span><span class="p">;</span>
<span class="linenos">10</span>printf<span class="o">(</span>tjcstr<span class="o">)</span><span class="p">;</span>   //单片机需要配置printf重定向到串口
<span class="linenos">11</span>
<span class="linenos">12</span>
<span class="linenos">13</span>//arduino发送指令通过串口修改调用的字库id
<span class="linenos">14</span>int <span class="nv">font_id</span> <span class="o">=</span> <span class="m">1</span><span class="p">;</span>   //字库编码
<span class="linenos">15</span>char tjcstr<span class="o">[</span><span class="m">100</span><span class="o">]</span><span class="p">;</span>
<span class="linenos">16</span>sprintf<span class="o">(</span>tjcstr, <span class="s2">&quot;main.b0.font=%d\xff\xff\xff&quot;</span>,font_id<span class="o">)</span><span class="p">;</span>
<span class="linenos">17</span>Serial.print<span class="o">(</span>tjcstr<span class="o">)</span><span class="p">;</span>
</pre></div>
</div>
</section>
<section id="id6">
<h3>让按钮在开始和停止之间切换<a class="headerlink" href="#id6" title="此标题的永久链接"></a></h3>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="k">if</span><span class="o">(</span>b0.txt<span class="o">==</span><span class="s2">&quot;开始&quot;</span><span class="o">)</span>
<span class="linenos">2</span><span class="o">{</span>
<span class="linenos">3</span>     b0.txt<span class="o">=</span><span class="s2">&quot;停止&quot;</span>
<span class="linenos">4</span><span class="o">}</span><span class="k">else</span>
<span class="linenos">5</span><span class="o">{</span>
<span class="linenos">6</span>     b0.txt<span class="o">=</span><span class="s2">&quot;开始&quot;</span>
<span class="linenos">7</span><span class="o">}</span>
</pre></div>
</div>
</section>
<section id="id7">
<h3>按钮禁用触摸功能<a class="headerlink" href="#id7" title="此标题的永久链接"></a></h3>
<p>使用tsw指令来实现触摸的使能和失能</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="o">//</span><span class="n">禁用b0的触摸</span>
<span class="n">tsw</span> <span class="n">b0</span><span class="p">,</span><span class="mi">0</span>

<span class="o">//</span><span class="n">开启b0的触摸</span>
<span class="n">tsw</span> <span class="n">b0</span><span class="p">,</span><span class="mi">1</span>
</pre></div>
</div>
<p>建议使用tsw时，配合修改按键的外观（修改颜色或者修改调用的图片）来让用户能够一眼了解按钮是否可用</p>
<p>当然也有其他的方法来实现类似的功能</p>
<p>例如：</p>
<p>使用文本控件替代按钮控件</p>
<p>在文本控件的按下或者弹起事件里，判断文本的bco或者pic属性，只有属性为自己需要时才可触发对应的代码</p>
<img alt="../_images/button_5.jpg" src="../_images/button_5.jpg" />
<img alt="../_images/button_6.jpg" src="../_images/button_6.jpg" />
<p>我们只需修改控件的bco属性为其他值，此时不符合条件，不会执行if里面的内容，也就相当于“禁用”这个功能了</p>
</section>
<section id="id8">
<h3>通过按钮控件发出串口数据<a class="headerlink" href="#id8" title="此标题的永久链接"></a></h3>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span>//将n0.val的值从串口发送出来
<span class="linenos">2</span>prints n0.val,0
<span class="linenos">3</span>
<span class="linenos">4</span>//将t0.txt的值从串口发送出来
<span class="linenos">5</span>prints t0.txt,0
<span class="linenos">6</span>
<span class="linenos">7</span>//发送回车换行
<span class="linenos">8</span>printh 0d 0a
</pre></div>
</div>
<p><a class="reference internal" href="../commands/prints.html#prints"><span class="std std-ref">prints-从串口打印一个变量/常量</span></a></p>
<p><a class="reference internal" href="../commands/printh.html#printh-16"><span class="std std-ref">printh-从串口打印16进制</span></a></p>
<p><a class="reference internal" href="../advanced/keyboard/keyboard_custom/keyboard_custom6.html#ok"><span class="std std-ref">在键盘页面按下OK键时将参数通过串口发送出去</span></a></p>
</section>
<section id="id9">
<h3>使用按钮实现长按功能<a class="headerlink" href="#id9" title="此标题的永久链接"></a></h3>
<p>需配合定时器使用</p>
<p>1.新建一个定时器tm0，en属性设置为0，tim属性设置为你想要长按的时间，例如2秒，则设置为2000。</p>
<p>2.在按钮的按下事件中写上如下代码：</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span> //打开定时器
<span class="linenos">2</span> tm0.en<span class="o">=</span><span class="m">1</span>
</pre></div>
</div>
<p>3.在按钮的弹起事件中写上如下代码：</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span> //弹起事件代表已经松手了，所以让定时器立即停止工作
<span class="linenos">2</span> tm0.en<span class="o">=</span><span class="m">0</span>
</pre></div>
</div>
<p>4.在定时器tm0的定时事件中先关闭定时器，然后写上想操作的长按事件代码，比如:</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span> //长按时间到了,先关闭定时器
<span class="linenos">2</span> tm0.en<span class="o">=</span><span class="m">0</span>
<span class="linenos">3</span> //接下来可以写自己需要长按后执行的代码，例如跳转到main页面
<span class="linenos">4</span> page main
</pre></div>
</div>
</section>
<section id="id10">
<h3>使用按钮实现双击功能<a class="headerlink" href="#id10" title="此标题的永久链接"></a></h3>
<p>需配合定时器使用和数字控件使用</p>
<p>1.新建一个定时器tm0，en属性设置为0，tim属性设置双击的间隔，例如400毫秒，则设置为400（定时器定时间隔最低50ms）。新建一个数字控件n0，用于记录双击次数</p>
<p>2.在按钮的按下事件中写上如下代码：</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span> //打开定时器
<span class="linenos">2</span> tm0.en<span class="o">=</span><span class="m">1</span>
<span class="linenos">3</span> n0.val++
<span class="linenos">4</span> <span class="k">if</span><span class="o">(</span>n0.val&gt;<span class="o">=</span><span class="m">2</span><span class="o">)</span>
<span class="linenos">5</span> <span class="o">{</span>
<span class="linenos">6</span>     //触发了双击，跳转页面
<span class="linenos">7</span>     page main
<span class="linenos">8</span> <span class="o">}</span>
</pre></div>
</div>
<p>3.在定时器tm0的定时事件中编写以下代码</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span> //关闭定时器
<span class="linenos">2</span> tm0.en<span class="o">=</span><span class="m">0</span>
<span class="linenos">3</span>
<span class="linenos">4</span> //清空n0.val
<span class="linenos">5</span> n0.val<span class="o">=</span><span class="m">0</span>
</pre></div>
</div>
<p>调试ok后可以将数字控件改为变量控件</p>
</section>
<section id="id11">
<h3>使用按钮在多个状态之间切换<a class="headerlink" href="#id11" title="此标题的永久链接"></a></h3>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="k">if</span><span class="o">(</span>b0.txt<span class="o">==</span><span class="s2">&quot;状态1&quot;</span><span class="o">)</span>
<span class="linenos"> 2</span><span class="o">{</span>
<span class="linenos"> 3</span>   b0.txt<span class="o">=</span><span class="s2">&quot;状态2&quot;</span>
<span class="linenos"> 4</span><span class="o">}</span><span class="k">else</span> <span class="k">if</span><span class="o">(</span>b0.txt<span class="o">==</span><span class="s2">&quot;状态2&quot;</span><span class="o">)</span>
<span class="linenos"> 5</span><span class="o">{</span>
<span class="linenos"> 6</span>   b0.txt<span class="o">=</span><span class="s2">&quot;状态3&quot;</span>
<span class="linenos"> 7</span><span class="o">}</span><span class="k">else</span> <span class="k">if</span><span class="o">(</span>b0.txt<span class="o">==</span><span class="s2">&quot;状态3&quot;</span><span class="o">)</span>
<span class="linenos"> 8</span><span class="o">{</span>
<span class="linenos"> 9</span>   b0.txt<span class="o">=</span><span class="s2">&quot;状态4&quot;</span>
<span class="linenos">10</span><span class="o">}</span><span class="k">else</span> <span class="k">if</span><span class="o">(</span>b0.txt<span class="o">==</span><span class="s2">&quot;状态4&quot;</span><span class="o">)</span>
<span class="linenos">11</span><span class="o">{</span>
<span class="linenos">12</span>   b0.txt<span class="o">=</span><span class="s2">&quot;状态5&quot;</span>
<span class="linenos">13</span><span class="o">}</span><span class="k">else</span>
<span class="linenos">14</span><span class="o">{</span>
<span class="linenos">15</span>   b0.txt<span class="o">=</span><span class="s2">&quot;状态1&quot;</span>
<span class="linenos">16</span><span class="o">}</span>
</pre></div>
</div>
</section>
<section id="id12">
<h3>使用按钮在多个状态之间切换2<a class="headerlink" href="#id12" title="此标题的永久链接"></a></h3>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="k">if</span><span class="o">(</span>b0.txt<span class="o">==</span><span class="s2">&quot;开始&quot;</span><span class="o">)</span>
<span class="linenos"> 2</span><span class="o">{</span>
<span class="linenos"> 3</span>   b0.txt<span class="o">=</span><span class="s2">&quot;暂停&quot;</span>
<span class="linenos"> 4</span><span class="o">}</span><span class="k">else</span> <span class="k">if</span><span class="o">(</span>b0.txt<span class="o">==</span><span class="s2">&quot;暂停&quot;</span><span class="o">)</span>
<span class="linenos"> 5</span><span class="o">{</span>
<span class="linenos"> 6</span>   b0.txt<span class="o">=</span><span class="s2">&quot;停止&quot;</span>
<span class="linenos"> 7</span><span class="o">}</span><span class="k">else</span>
<span class="linenos"> 8</span><span class="o">{</span>
<span class="linenos"> 9</span>   b0.txt<span class="o">=</span><span class="s2">&quot;开始&quot;</span>
<span class="linenos">10</span><span class="o">}</span>
</pre></div>
</div>
</section>
</section>
<section id="id13">
<h2>按钮控件-样例工程下载<a class="headerlink" href="#id13" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/按钮控件/按钮控件.HMI">《按钮控件》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/按钮控件/按钮长按发送数据.HMI">《按钮长按发送数据》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/按钮控件/一个按钮同时实现点按和长按.HMI">《一个按钮同时实现点按和长按》演示工程下载</a></p>
</section>
<section id="id17">
<h2>按钮控件-相关链接<a class="headerlink" href="#id17" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="../QA/QA10.html#id1"><span class="std std-ref">哪些控件属性可以运行中修改，哪些不能运行中修改，绿色属性和黑色属性有什么区别？</span></a></p>
<p><a class="reference internal" href="../QA/QA11.html#txtval"><span class="std std-ref">txt属性和val属性有什么区别，为什么有些属性赋值要加双引号，有些不用加双引号</span></a></p>
<p><a class="reference internal" href="../grammar/global_variable.html#id1"><span class="std std-ref">跨页面赋值，全局变量操作</span></a></p>
</section>
<section id="id18">
<h2>按钮控件-属性详解<a class="headerlink" href="#id18" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="component.html#id6"><span class="std std-ref">控件属性解析</span></a></p>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>绿色属性可以通过上位机或者串口屏指令进行修改，黑色属性只能在上位机中修改或者不可修改，可通过上位机进行修改指“选中控件后通过属性栏修改控件的属性”</p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">type属性</span></code> -控件类型，固定值，不同类型的控件type值不同，相同类型的控件type值相同，可读，不可通过上位机修改，不可通过指令修改。参考： <a class="reference internal" href="component.html#id10"><span class="std std-ref">控件属性-控件id对照表</span></a></p>
<p><code class="docutils literal notranslate"><span class="pre">id属性</span></code> -控件ID，可通过上位机左上角的上下箭头置顶或置底，可读，可通过上位机修改左上角的箭头置顶或置地间接修改，不可通过指令修改。参考： <a class="reference internal" href="../QA/QA28.html#id1"><span class="std std-ref">如何更改控件的前后图层关系</span></a></p>
<p><code class="docutils literal notranslate"><span class="pre">objname属性</span></code> -控件名称。不可读，可通过上位机进行修改，不可通过指令更改。</p>
<p><code class="docutils literal notranslate"><span class="pre">vscope属性</span></code> -内存占用(私有占用只能在当前页面被访问，全局占用可以在所有页面被访问)，当设置为私有时，跳转页面后，该控件占用的内存会被释放，重新返回该页面后该控件会恢复到最初的设置。可读，可通过上位机进行修改，不可通过指令更改。参考：<a class="reference internal" href="../grammar/global_variable.html#id1"><span class="std std-ref">跨页面赋值，全局变量操作</span></a></p>
<p><code class="docutils literal notranslate"><span class="pre">drag属性</span></code> -是否支持拖动:0-否;1-是。仅x系列支持。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">aph属性</span></code> -不透明度(0-127)，0为完全透明，127为完全不透明。仅x系列支持。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">effect属性</span></code> -加载特效:0-立即加载;1-上边飞入;2-下边飞入;3-左边飞入;4-右边飞入;5-左上角飞入;6-右上角飞入;7-左下角飞入;8-右下角飞入。仅x系列支持，在上位机中设置为立即加载时，无法通过指令变为其他特效，当在上位机中设置为非立即加载的特效时，可以变为立即加载，也可以再改为其他特效</p>
<p><code class="docutils literal notranslate"><span class="pre">sta属性</span></code> -背景填充方式:0-切图;1-单色;2-图片;3-透明（仅x系列支持透明）。可读，可通过上位机修改，不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">picc属性</span></code> -按钮没按下时切图背景(必须是全屏图片)，sta为切图时才有这个属性。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">picc2属性</span></code> -按钮按下时切图背景(必须是全屏图片)，sta为切图时才有这个属性。 可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">bco属性</span></code> -按钮没按下时背景色，sta为单色时才有这个属性。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">bco2属性</span></code> -按钮按下时背景色 ，sta为单色时才有这个属性。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">style属性</span></code> -显示风格:0-平面;1-边框;2-3D_Down;3-3D_Up;4-3D_Auto，sta为单色时才有这个属性。可读，可通过上位机修改，不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">borderc属性</span></code> -边框颜色。当style设置为边框时可用。可读，可通过上位机修改，x系列可通过指令修改，其他系列不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">borderw属性</span></code> 边框粗细。当style设置为边框时可用。最大值:255。可读，可通过上位机修改，x系列可通过指令修改，其他系列不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">pic属性</span></code> -按钮没按下时背景图片，sta为图片时才有这个属性。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">pic2属性</span></code> -按钮按下时背景图片，sta为图片时才有这个属性。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">pco属性</span></code> -按钮没按下时字体色。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">pco2属性</span></code> -按钮按下时字体色。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">font属性</span></code> -控件调用的字库id，调用不同的字库会显示不同的字体或字号。可读，可通过上位机修改，可通过指令修改。参考：1、 <a class="reference internal" href="../start/create_project/create_project_5.html#id1"><span class="std std-ref">创建字库和导入字库</span></a>  2、   <a class="reference internal" href="../QA/QA8.html#id1"><span class="std std-ref">指定字库</span></a></p>
<p><code class="docutils literal notranslate"><span class="pre">xcen属性</span></code> -水平对齐:0-靠左;1-居中;2-靠右。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">ycen属性</span></code> -垂直对齐:0-靠上;1-居中;2-靠下。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">txt属性</span></code> -字符内容（按钮上显示的文本）。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">txt_maxl属性</span></code> -控件分配的内存空间。GB2312下，ascii字符占用1个字节，一个汉字占2字节。UTF8下，ascii字符占用1个字节，一个汉字占3字节。可读，可通过上位机修改，不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">isbr属性</span></code> -是否自动换行:0-否;1-是。可读，可通过上位机修改，可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">spax属性</span></code> -字符横向间距(最小0,最大255)。可读，可通过上位机修改，x系列可通过指令修改，其他系列不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">spay属性</span></code> -字符纵向间距(最小0,最大255)。可读，可通过上位机修改，x系列可通过指令修改，其他系列不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">x属性</span></code> -控件的X坐标。可读，可通过上位机修改，x系列可通过指令修改，其他系列不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">y属性</span></code> -控件的Y坐标。可读，可通过上位机修改，x系列可通过指令修改，其他系列不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">w属性</span></code> -控件的宽度。可读，可通过上位机修改，不可通过指令修改。</p>
<p><code class="docutils literal notranslate"><span class="pre">h属性</span></code> -控件的高度。可读，可通过上位机修改，不可通过指令修改。</p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="Xfloat.html" class="btn btn-neutral float-left" title="虚拟浮点数控件" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="Progress_bar.html" class="btn btn-neutral float-right" title="进度条控件" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>