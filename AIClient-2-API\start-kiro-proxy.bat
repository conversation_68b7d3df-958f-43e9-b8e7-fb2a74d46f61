@echo off
title <PERSON><PERSON> Claude Proxy Server
echo ========================================
echo      Kiro Claude 代理服务器
echo ========================================
echo.
echo 🚀 启动 AIClient-2-API 服务器...
echo    地址: http://127.0.0.1:3000
echo    模型: <PERSON>ro Claude Sonnet 4
echo.
echo 💡 使用说明:
echo    1. 保持此窗口打开
echo    2. 在 Claude Code 中使用 Kiro 模型
echo    3. 按 Ctrl+C 停止服务器
echo.
echo ========================================
echo.

node src/api-server.js --host 127.0.0.1 --port 3000 --model-provider claude-kiro-oauth --api-key 123456
