# 关键代码示例与配置参数

## 系统配置参数

### 时钟配置
```c
// 系统时钟: 168MHz (STM32F407默认)
#define SYSTEM_CLOCK_HZ    168000000

// 延时系统配置
delay_init(168);  // 系统时钟168MHz

// 中断优先级分组
NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
```

### PWM配置参数
```c
// PWM频率计算: f = 168MHz / ((psc+1) * (arr+1))
#define PWM_ARR    7199  // 自动重装载值
#define PWM_PSC    6     // 预分频值
// 实际频率: 168MHz / (7 × 7200) = 3333.33 Hz

// PWM占空比配置
#define PWM_PULSE_50_PERCENT    3600    // 50%占空比
#define PWM_PULSE_25_PERCENT    1800    // 25%占空比
#define PWM_PULSE_75_PERCENT    5400    // 75%占空比

// PWM初始化调用
STEP12_PWM_Init(PWM_ARR, PWM_PSC);
```

### 串口配置
```c
// USART1配置参数
#define UART_BAUDRATE    115200
#define UART_TX_PIN      GPIO_Pin_9   // PA9
#define UART_RX_PIN      GPIO_Pin_10  // PA10

// 初始化调用
uart_init(UART_BAUDRATE);
```

### ADC配置
```c
// ADC采样配置
#define ADC_CHANNEL           ADC_Channel_10  // PC0
#define ADC_SAMPLE_TIMES      10              // 采样次数
#define ADC_VOLTAGE_DIVIDER   11              // 分压比例
#define ADC_REFERENCE_VOLTAGE 3.3             // 参考电压

// 电压计算公式
voltage = (float)(adc_val * ADC_REFERENCE_VOLTAGE * ADC_VOLTAGE_DIVIDER / 4096);
```

## 引脚定义

### GPIO引脚映射
```c
// 电机A控制引脚
#define MOTOR_A_STEP_PIN     GPIO_Pin_8   // PC8  (TIM8_CH3)
#define MOTOR_A_DIR_PIN      GPIO_Pin_13  // PC13
#define MOTOR_A_SLEEP_PIN    GPIO_Pin_2   // PD2

// 电机B控制引脚  
#define MOTOR_B_STEP_PIN     GPIO_Pin_9   // PC9  (TIM8_CH4)
#define MOTOR_B_DIR_PIN      GPIO_Pin_12  // PB12 (优化后引脚)
#define MOTOR_B_SLEEP_PIN    GPIO_Pin_12  // PC12

// 系统功能引脚
#define UART_TX_PIN          GPIO_Pin_9   // PA9
#define UART_RX_PIN          GPIO_Pin_10  // PA10
#define ADC_INPUT_PIN        GPIO_Pin_0   // PC0
#define KEY_INPUT_PIN        GPIO_Pin_14  // PB14
```

### GPIO位操作宏
```c
// 电机A控制宏
#define MOTOR_A_DIR_CW()     PCout(13) = 1    // 顺时针
#define MOTOR_A_DIR_CCW()    PCout(13) = 0    // 逆时针
#define MOTOR_A_ENABLE()     PDout(2) = 0     // 使能 (低电平有效)
#define MOTOR_A_DISABLE()    PDout(2) = 1     // 失能

// 电机B控制宏
#define MOTOR_B_DIR_CW()     PBout(12) = 1    // 顺时针
#define MOTOR_B_DIR_CCW()    PBout(12) = 0    // 逆时针
#define MOTOR_B_ENABLE()     PCout(12) = 0    // 使能 (低电平有效)
#define MOTOR_B_DISABLE()    PCout(12) = 1    // 失能

// 按键检测宏
#define KEY_PRESSED()        (PBin(14) == 0)  // 按键按下检测
```

## 核心功能代码

### 1. ATD5984初始化完整代码
```c
void ATD5984_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    /* 使能相关GPIO时钟 */
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOB, ENABLE);  // PB12时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOC, ENABLE);  // PC12,PC13时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOD, ENABLE);  // PD2时钟
    
    /* 配置GPIOC引脚 (PC12-电机B使能, PC13-电机A方向) */
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_12 | GPIO_Pin_13;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOC, &GPIO_InitStructure);
    
    /* 配置GPIOD引脚 (PD2-电机A使能) */
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOD, &GPIO_InitStructure);
    
    /* 配置GPIOB引脚 (PB12-电机B方向) */
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_12;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOB, &GPIO_InitStructure);
    
    /* 设置初始状态 */
    GPIO_SetBits(GPIOC, GPIO_Pin_12);     // 步进电机B失能 (SLEEP=HIGH)
    GPIO_ResetBits(GPIOC, GPIO_Pin_13);   // 步进电机A正向 (DIR=LOW) 
    GPIO_SetBits(GPIOB, GPIO_Pin_12);     // 步进电机B反向 (DIR=HIGH)
    GPIO_SetBits(GPIOD, GPIO_Pin_2);      // 步进电机A失能 (SLEEP=HIGH)
}
```

### 2. PWM初始化完整代码
```c
void STEP12_PWM_Init(u16 arr, u16 psc)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
    TIM_OCInitTypeDef TIM_OCInitStructure;
    
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM8, ENABLE);
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOC, ENABLE);
    
    GPIO_PinAFConfig(GPIOC, GPIO_PinSource8, GPIO_AF_TIM8);
    GPIO_PinAFConfig(GPIOC, GPIO_PinSource9, GPIO_AF_TIM8);
    
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8 | GPIO_Pin_9;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(GPIOC, &GPIO_InitStructure);
    
    TIM_TimeBaseStructure.TIM_Prescaler = psc;
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseStructure.TIM_Period = arr;
    TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;
    TIM_TimeBaseInit(TIM8, &TIM_TimeBaseStructure);
    
    TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM1;
    TIM_OCInitStructure.TIM_OutputState = TIM_OutputState_Enable;
    TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_High;
    TIM_OCInitStructure.TIM_Pulse = 3600; // 50%占空比
    
    TIM_OC3Init(TIM8, &TIM_OCInitStructure); // CH3 - PC8 - Motor A
    TIM_OC4Init(TIM8, &TIM_OCInitStructure); // CH4 - PC9 - Motor B
    
    TIM_OC3PreloadConfig(TIM8, TIM_OCPreload_Enable);
    TIM_OC4PreloadConfig(TIM8, TIM_OCPreload_Enable);
    TIM_ARRPreloadConfig(TIM8, ENABLE);
    
    TIM_CtrlPWMOutputs(TIM8, ENABLE);
    TIM_Cmd(TIM8, ENABLE);
}
```

### 3. ADC采样代码
```c
u16 Get_adc_Average(u8 times, u8 ch)
{
    u32 temp_val = 0;
    u8 t;
    
    for(t = 0; t < times; t++) {
        temp_val += Get_Adc(ch);
        delay_ms(5);
    }
    
    return temp_val / times;
}

u16 Get_Adc(u8 ch)
{
    ADC_RegularChannelConfig(ADC1, ch, 1, ADC_SampleTime_480Cycles);
    ADC_SoftwareStartConv(ADC1);
    while(!ADC_GetFlagStatus(ADC1, ADC_FLAG_EOC));
    return ADC_GetConversionValue(ADC1);
}
```

## 扩展功能代码模板

### 1. 电机步进控制函数
```c
/**
 * @brief  控制电机步进
 * @param  motor: 电机选择 (MOTOR_A 或 MOTOR_B)
 * @param  steps: 步数
 * @param  direction: 方向 (1=CW, 0=CCW)
 * @param  speed: 速度 (us延时)
 */
void Motor_Step_Control(u8 motor, u32 steps, u8 direction, u16 speed)
{
    if(motor == MOTOR_A) {
        // 设置方向
        if(direction) MOTOR_A_DIR_CW();
        else MOTOR_A_DIR_CCW();
        
        // 使能电机
        MOTOR_A_ENABLE();
        
        // 执行步进 (PWM自动产生脉冲)
        for(u32 i = 0; i < steps; i++) {
            delay_us(speed);
        }
        
        // 关闭电机
        MOTOR_A_DISABLE();
    }
    else if(motor == MOTOR_B) {
        // 类似电机A的控制逻辑
        if(direction) MOTOR_B_DIR_CW();
        else MOTOR_B_DIR_CCW();
        
        MOTOR_B_ENABLE();
        
        for(u32 i = 0; i < steps; i++) {
            delay_us(speed);
        }
        
        MOTOR_B_DISABLE();
    }
}

// 使用示例
#define MOTOR_A  0
#define MOTOR_B  1
#define CW       1  // 顺时针
#define CCW      0  // 逆时针

Motor_Step_Control(MOTOR_A, 1600, CW, 300);   // 电机A顺时针转半圈
Motor_Step_Control(MOTOR_B, 3200, CCW, 500);  // 电机B逆时针转一圈
```

### 2. 坐标控制系统
```c
// 机械参数定义
#define STEPS_PER_DEGREE    17.78f  // 3200步/180° = 17.78步/度
#define MAX_ANGLE_X         90.0f   // X轴最大角度
#define MAX_ANGLE_Y         45.0f   // Y轴最大角度

/**
 * @brief  角度转步数
 * @param  angle: 角度 (-90° ~ +90°)
 * @retval 对应的步数
 */
u32 Angle_To_Steps(float angle)
{
    return (u32)(fabs(angle) * STEPS_PER_DEGREE);
}

/**
 * @brief  移动到指定角度
 * @param  angle_x: X轴目标角度
 * @param  angle_y: Y轴目标角度
 */
void Move_To_Angle(float angle_x, float angle_y)
{
    // 范围检查
    if(fabs(angle_x) > MAX_ANGLE_X || fabs(angle_y) > MAX_ANGLE_Y) {
        printf("Angle out of range!\\r\\n");
        return;
    }
    
    // 计算步数和方向
    u32 steps_x = Angle_To_Steps(angle_x);
    u32 steps_y = Angle_To_Steps(angle_y);
    u8 dir_x = (angle_x >= 0) ? CW : CCW;
    u8 dir_y = (angle_y >= 0) ? CW : CCW;
    
    // 执行运动
    printf("Moving to: X=%.2f°, Y=%.2f°\\r\\n", angle_x, angle_y);
    Motor_Step_Control(MOTOR_A, steps_x, dir_x, 300);
    Motor_Step_Control(MOTOR_B, steps_y, dir_y, 300);
    printf("Movement completed.\\r\\n");
}

// 使用示例
Move_To_Angle(30.0, -15.0);  // X轴30°, Y轴-15°
```

### 3. PID控制器模板
```c
// PID参数结构体
typedef struct {
    float Kp, Ki, Kd;          // PID参数
    float target, current;      // 目标值和当前值
    float error, last_error;    // 误差
    float integral, derivative; // 积分和微分
    float output;              // 输出
    float integral_limit;      // 积分限幅
    float output_limit;        // 输出限幅
} PID_Controller_TypeDef;

/**
 * @brief  PID控制器初始化
 * @param  pid: PID控制器指针
 * @param  kp, ki, kd: PID参数
 */
void PID_Init(PID_Controller_TypeDef *pid, float kp, float ki, float kd)
{
    pid->Kp = kp;
    pid->Ki = ki;
    pid->Kd = kd;
    pid->error = 0;
    pid->last_error = 0;
    pid->integral = 0;
    pid->derivative = 0;
    pid->output = 0;
    pid->integral_limit = 100.0f;
    pid->output_limit = 1000.0f;
}

/**
 * @brief  PID控制计算
 * @param  pid: PID控制器指针
 * @param  target: 目标值
 * @param  current: 当前值
 * @retval PID输出值
 */
float PID_Calculate(PID_Controller_TypeDef *pid, float target, float current)
{
    pid->target = target;
    pid->current = current;
    pid->error = target - current;
    
    // 积分计算
    pid->integral += pid->error;
    
    // 积分限幅
    if(pid->integral > pid->integral_limit) 
        pid->integral = pid->integral_limit;
    else if(pid->integral < -pid->integral_limit) 
        pid->integral = -pid->integral_limit;
    
    // 微分计算
    pid->derivative = pid->error - pid->last_error;
    
    // PID输出计算
    pid->output = pid->Kp * pid->error + 
                  pid->Ki * pid->integral + 
                  pid->Kd * pid->derivative;
    
    // 输出限幅
    if(pid->output > pid->output_limit) 
        pid->output = pid->output_limit;
    else if(pid->output < -pid->output_limit) 
        pid->output = -pid->output_limit;
    
    pid->last_error = pid->error;
    
    return pid->output;
}

// 使用示例
PID_Controller_TypeDef pid_x, pid_y;

void PID_Control_Init(void)
{
    PID_Init(&pid_x, 2.0f, 0.1f, 0.5f);  // X轴PID参数
    PID_Init(&pid_y, 2.0f, 0.1f, 0.5f);  // Y轴PID参数
}

void PID_Control_Process(float target_x, float target_y, float current_x, float current_y)
{
    float output_x = PID_Calculate(&pid_x, target_x, current_x);
    float output_y = PID_Calculate(&pid_y, target_y, current_y);
    
    // 将PID输出转换为电机控制指令
    s32 steps_x = (s32)(output_x * 0.1f);  // 比例因子
    s32 steps_y = (s32)(output_y * 0.1f);
    
    if(abs(steps_x) > 5) {  // 死区处理
        u8 dir_x = (steps_x > 0) ? CW : CCW;
        Motor_Step_Control(MOTOR_A, abs(steps_x), dir_x, 200);
    }
    
    if(abs(steps_y) > 5) {
        u8 dir_y = (steps_y > 0) ? CW : CCW;
        Motor_Step_Control(MOTOR_B, abs(steps_y), dir_y, 200);
    }
}
```

### 4. 串口指令解析系统
```c
// 指令结构体
typedef struct {
    u8 cmd_type;        // 指令类型
    s16 param1, param2; // 参数1, 参数2
    u8 checksum;        // 校验和
} Command_TypeDef;

/**
 * @brief  解析串口指令
 * @param  data: 接收到的数据
 * @param  len: 数据长度
 * @retval 解析结果
 */
u8 Parse_UART_Command(u8 *data, u8 len)
{
    if(len < 6) return 0;  // 数据长度不足
    
    Command_TypeDef cmd;
    cmd.cmd_type = data[0];
    cmd.param1 = (data[1] << 8) | data[2];
    cmd.param2 = (data[3] << 8) | data[4];
    cmd.checksum = data[5];
    
    // 校验和验证
    u8 calc_checksum = cmd.cmd_type ^ (cmd.param1 >> 8) ^ (cmd.param1 & 0xFF) ^ 
                       (cmd.param2 >> 8) ^ (cmd.param2 & 0xFF);
    
    if(calc_checksum != cmd.checksum) {
        printf("Checksum error!\\r\\n");
        return 0;
    }
    
    // 执行指令
    switch(cmd.cmd_type) {
        case 0x01:  // 移动指令
            Move_To_Angle((float)cmd.param1 / 100.0f, (float)cmd.param2 / 100.0f);
            break;
        case 0x02:  // 步进指令
            Motor_Step_Control(MOTOR_A, cmd.param1, (cmd.param2 > 0) ? CW : CCW, 300);
            break;
        case 0x03:  // 状态查询
            printf("Status: X=%d, Y=%d\\r\\n", cmd.param1, cmd.param2);
            break;
        default:
            printf("Unknown command: 0x%02X\\r\\n", cmd.cmd_type);
            return 0;
    }
    
    return 1;  // 成功
}
```

## 调试与测试代码

### 1. 系统状态监测
```c
void Print_System_Status(void)
{
    printf("\\r\\n=== System Status ===\\r\\n");
    printf("System Clock: %d MHz\\r\\n", SystemCoreClock / 1000000);
    printf("PWM Frequency: %.2f kHz\\r\\n", 168000.0f / ((PWM_PSC + 1) * (PWM_ARR + 1)));
    printf("Motor A: DIR=%d, SLEEP=%d\\r\\n", PCin(13), PDin(2));
    printf("Motor B: DIR=%d, SLEEP=%d\\r\\n", PBin(12), PCin(12));
    printf("Key State: %d\\r\\n", PBin(14));
    printf("ADC Value: %d\\r\\n", Get_adc_Average(5, 5));
    printf("Voltage: %.2f V\\r\\n", (float)(Get_adc_Average(5, 5) * 3.3 * 11 / 4096));
    printf("====================\\r\\n\\r\\n");
}
```

### 2. 电机测试序列
```c
void Motor_Test_Sequence(void)
{
    printf("Starting motor test sequence...\\r\\n");
    
    // 测试电机A
    printf("Testing Motor A...\\r\\n");
    MOTOR_A_ENABLE();
    MOTOR_A_DIR_CW();
    delay_ms(2000);  // 顺时针2秒
    MOTOR_A_DIR_CCW();
    delay_ms(2000);  // 逆时针2秒
    MOTOR_A_DISABLE();
    
    delay_ms(1000);
    
    // 测试电机B
    printf("Testing Motor B...\\r\\n");
    MOTOR_B_ENABLE();
    MOTOR_B_DIR_CW();
    delay_ms(2000);  // 顺时针2秒
    MOTOR_B_DIR_CCW();
    delay_ms(2000);  // 逆时针2秒
    MOTOR_B_DISABLE();
    
    printf("Motor test completed.\\r\\n");
}
```

### 3. PWM参数调试
```c
void PWM_Parameter_Test(void)
{
    u16 test_frequencies[][2] = {
        {7199, 6},   // 3.33 kHz
        {3599, 6},   // 6.67 kHz
        {1799, 6},   // 13.33 kHz
        {14399, 6},  // 1.67 kHz
    };
    
    for(u8 i = 0; i < 4; i++) {
        printf("Testing frequency %d...\\r\\n", i + 1);
        
        // 重新配置PWM
        TIM_Cmd(TIM8, DISABLE);
        TIM8->ARR = test_frequencies[i][0];
        TIM8->PSC = test_frequencies[i][1];
        TIM_Cmd(TIM8, ENABLE);
        
        printf("ARR=%d, PSC=%d, Freq=%.2f kHz\\r\\n", 
               test_frequencies[i][0], test_frequencies[i][1],
               168000.0f / ((test_frequencies[i][1] + 1) * (test_frequencies[i][0] + 1)));
        
        // 测试运行
        MOTOR_A_ENABLE();
        delay_ms(3000);
        MOTOR_A_DISABLE();
        
        delay_ms(2000);
    }
    
    // 恢复默认配置
    TIM_Cmd(TIM8, DISABLE);
    TIM8->ARR = PWM_ARR;
    TIM8->PSC = PWM_PSC;
    TIM_Cmd(TIM8, ENABLE);
    
    printf("PWM test completed, restored to default.\\r\\n");
}
```

## 常用宏定义集合

```c
// 系统配置宏
#define SYSTEM_CLOCK_MHZ     168
#define UART_BAUDRATE        115200
#define ADC_SAMPLE_TIMES     10

// 电机参数宏
#define STEPS_PER_REVOLUTION 3200    // 1/16细分后每圈步数
#define DEGREES_PER_STEP     0.1125f // 每步对应角度
#define MAX_SPEED_HZ         5000    // 最大步进频率

// 安全限制宏
#define MAX_ANGLE_X          90.0f
#define MAX_ANGLE_Y          45.0f
#define MIN_STEP_DELAY       100     // 最小步进延时(us)

// 调试开关宏
#define DEBUG_ENABLE         1
#define VERBOSE_OUTPUT       0

#if DEBUG_ENABLE
    #define DEBUG_PRINTF(fmt, ...) printf("[DEBUG] " fmt "\\r\\n", ##__VA_ARGS__)
#else
    #define DEBUG_PRINTF(fmt, ...)
#endif

// 数学工具宏
#define ABS(x)              ((x) > 0 ? (x) : -(x))
#define MAX(a, b)           ((a) > (b) ? (a) : (b))
#define MIN(a, b)           ((a) < (b) ? (a) : (b))
#define CONSTRAIN(x, a, b)  ((x) < (a) ? (a) : ((x) > (b) ? (b) : (x)))

// 角度转换宏
#define DEG_TO_RAD(deg)     ((deg) * 3.14159f / 180.0f)
#define RAD_TO_DEG(rad)     ((rad) * 180.0f / 3.14159f)
```

---

**说明**: 以上代码示例基于实际项目整理，可直接复制使用。所有配置参数均经过实际测试验证。

**更新日期**: 2025-08-01  
**版本信息**: v1.0 成功驱动版本  
**适用平台**: STM32F407ZGT6 + D36A + 42步进电机