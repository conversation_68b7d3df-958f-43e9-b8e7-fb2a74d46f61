#ifndef __USART_H
#define __USART_H
#include "stdio.h"	
#include "stm32f4xx_conf.h"
#include "sys.h" 
//////////////////////////////////////////////////////////////////////////////////	 
//������ֻ��ѧϰʹ�ã�δ���������ɣ��������������κ���;
//Mini STM32������
//����1��ʼ��		   
//����ԭ��@ALIENTEK
//������̳:www.openedv.csom
//�޸�����:2011/6/14
//�汾��V1.4
//��Ȩ���У�����ؾ���
//Copyright(C) ����ԭ�� 2009-2019
//All rights reserved
//********************************************************************************
//V1.3�޸�˵�� 
//֧����Ӧ��ͬƵ���µĴ��ڲ���������.
//�����˶�printf��֧��
//�����˴��ڽ��������.
//������printf��һ���ַ���ʧ��bug
//V1.4�޸�˵��
//1,�޸Ĵ��ڳ�ʼ��IO��bug
//2,�޸���USART_RX_STA,ʹ�ô����������ֽ���Ϊ2��14�η�
//3,������USART_REC_LEN,���ڶ��崮������������յ��ֽ���(������2��14�η�)
//4,�޸���EN_USART1_RX��ʹ�ܷ�ʽ
////////////////////////////////////////////////////////////////////////////////// 	
#define USART_REC_LEN  			200  	//定义最大接收字节数 200
#define EN_USART1_RX 			1		//使能（1）/禁止（0）串口1接收

// 舵机协议相关定义
#define SERVO_FRAME_HEADER		0x55	//舵机数据包帧头
#define SERVO_MAX_PACKET_LEN	20		//舵机数据包最大长度
	  	
extern u8  USART_RX_BUF[USART_REC_LEN]; //接收缓冲,最大USART_REC_LEN个字节.末字节为换行符 
extern u16 USART_RX_STA;         		//接收状态标记
extern u8  SERVO_RX_BUF[SERVO_MAX_PACKET_LEN]; //舵机接收缓冲
extern u8  SERVO_RX_STA;				//舵机接收状态: 0-等待帧头1, 1-等待帧头2, 2-接收数据
extern u8  SERVO_RX_CNT;				//舵机接收计数器
extern u8  SERVO_PKT_LEN;				//舵机数据包长度	
//串口相关函数声明，请不要注释下面这句话
void uart_init(u32 bound);

// 舵机控制函数声明
void USART_SendArray(u8 *arr, u16 len);
u8 Servo_CalculateChecksum(u8 *data, u8 len);
void Servo_SetMotorMode(u8 id, s16 speed);
void Servo_LoadMotor(u8 id);
void Servo_MoveToPosition(u8 id, u16 position, u16 time);
void Servo_ReadPosition(u8 id);  // 读取舵机当前位置
u8 Servo_ProcessPacket(void);

// UART2串口屏通信相关定义
#define EN_USART2_RX                   1               //使能（1）/禁止（0）串口2接收
#define SCREEN_REC_LEN                 50              //串口屏接收缓冲长度

extern u8  SCREEN_RX_BUF[SCREEN_REC_LEN];             //串口屏接收缓冲
extern u8  SCREEN_RX_STA;                             //串口屏接收状态
extern u8  SCREEN_RX_CNT;                             //串口屏接收计数

// 舵机位置跟踪变量
extern u16 SERVO1_POSITION;                          //舵机1当前位置
extern u16 SERVO2_POSITION;                          //舵机2当前位置

// UART2串口屏通信函数声明
void uart2_init(u32 bound);
void USART2_SendArray(u8 *arr, u16 len);
void Screen_ProcessCommand(void);

// 激光云台控制系统数据结构
typedef struct {
    float x;  // X坐标 (mm)
    float y;  // Y坐标 (mm)
} Point2D;

typedef struct {
    u16 servo1_angle;  // 舵机1角度 (0-1000 对应 0-240度)
    u16 servo2_angle;  // 舵机2角度 (0-1000 对应 0-240度)
} ServoAngles;

// 激光云台系统状态
typedef enum {
    LASER_STATE_UNCALIBRATED = 0,  // 未标定
    LASER_STATE_CALIBRATING,       // 标定中
    LASER_STATE_CALIBRATED,        // 已标定
    LASER_STATE_RECORDING_A,       // 记录A点
    LASER_STATE_RECORDING_B,       // 记录B点
    LASER_STATE_READY,            // 就绪状态
    LASER_STATE_MOVING            // 运动中
} LaserSystemState;

typedef enum {
    CURRENT_POS_UNKNOWN = 0,
    CURRENT_POS_A,
    CURRENT_POS_B
} CurrentPosition;

// 激光云台系统全局变量
extern LaserSystemState laser_system_state;
extern CurrentPosition current_position;
extern u8 calibration_step;                    // 标定步骤 (0-3)
extern ServoAngles calibration_points[4];      // 4个标定点的舵机角度
extern Point2D calibration_coords[4];          // 4个标定点的坐标 (A4纸四角)
extern ServoAngles point_A_angles;             // A点舵机角度
extern ServoAngles point_B_angles;             // B点舵机角度
extern Point2D point_A_coord;                  // A点坐标
extern Point2D point_B_coord;                  // B点坐标

// 激光云台控制函数声明
void LaserSystem_Init(void);
void LaserSystem_StartCalibration(void);
void LaserSystem_RecordCalibrationPoint(void);
void LaserSystem_RecordPointA(void);
void LaserSystem_RecordPointB(void);
void LaserSystem_MoveToA(void);
void LaserSystem_MoveToB(void);
u8 LaserSystem_CoordsToAngles(Point2D coord, ServoAngles *angles);
Point2D LaserSystem_AnglesToCoords(ServoAngles angles);
void LaserSystem_MoveToPoint(ServoAngles target_angles);
void LaserSystem_UpdateScreenStatus(void);
void LaserSystem_MoveLinearToB(void);  // 直线轨迹从A到B
void LaserSystem_MoveLinearToA(void);  // 直线轨迹从B到A

#endif


