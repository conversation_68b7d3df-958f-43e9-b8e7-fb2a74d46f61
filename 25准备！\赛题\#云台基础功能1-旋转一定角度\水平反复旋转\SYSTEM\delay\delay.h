/**
 ******************************************************************************
 * @file    delay.h
 * <AUTHOR> Team & [Your Name]
 * @version V1.1
 * @date    2025-01-31
 * @brief   STM32F407系统延时函数头文件
 *          
 *          本文件定义了基于SysTick滴答定时器的精确延时接口
 *          支持微秒级和毫秒级延时，可兼容RTOS环境
 * 
 * @note    延时原理:
 *          - 使用Cortex-M4内核的SysTick定时器
 *          - 24位向下计数器，时钟源为HCLK
 *          - STM32F407主频168MHz，提供高精度延时
 *          - 支持UCOSII等实时操作系统
 *          
 *          延时精度:
 *          - delay_us: 微秒级延时，误差<1us
 *          - delay_ms: 毫秒级延时，基于delay_us实现
 *          
 *          使用限制:
 *          - 最大单次延时受SysTick位宽限制
 *          - 延时期间会占用CPU，不适合长时间延时
 ******************************************************************************
 * @attention
 * 本程序基于正点原子STM32F407开发板例程修改
 * 原作者: 正点原子@ALIENTEK (www.openedv.com)
 * 修改: 添加详细注释和STM32F407适配
 ******************************************************************************
 */

#ifndef __DELAY_H
#define __DELAY_H

#include <sys.h>

/* 函数声明 ------------------------------------------------------------------*/

/**
 * @brief  延时系统初始化  
 * @param  SYSCLK: 系统时钟频率 (MHz)
 * @retval None
 * @note   必须在使用延时函数前调用此函数进行初始化
 *         SYSCLK参数应与实际系统时钟频率一致
 *         STM32F407默认主频为168MHz
 */
void delay_init(u8 SYSCLK);

/**
 * @brief  毫秒级延时函数
 * @param  nms: 延时时间 (毫秒)
 * @retval None
 * @note   精确延时nms毫秒，基于delay_us实现
 *         适用于一般的时序控制和程序延时
 *         建议单次延时不超过几秒钟
 */
void delay_ms(u16 nms);

/**
 * @brief  微秒级延时函数
 * @param  nus: 延时时间 (微秒)
 * @retval None
 * @note   精确延时nus微秒，使用SysTick定时器实现
 *         适用于对时序要求严格的场合
 *         最小延时约1us，最大单次延时约798ms (24位计数器限制)
 */
void delay_us(u32 nus);

#endif
