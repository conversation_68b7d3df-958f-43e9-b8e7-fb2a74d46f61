@echo off
echo ========================================
echo   AIClient-2-API Kiro Claude Sonnet 4
echo ========================================
echo.

echo 正在启动 AIClient-2-API 服务...
echo 服务地址: http://localhost:3000
echo 模型提供商: kiro-api
echo.

echo 可用端点:
echo   健康检查: GET  http://localhost:3000/health
echo   模型列表: GET  http://localhost:3000/v1/models
echo   聊天完成: POST http://localhost:3000/v1/chat/completions
echo.

echo 按 Ctrl+C 停止服务
echo.

node src/api-server.js --model-provider kiro-api --kiro-oauth-creds-file ./kiro-credentials.json

pause
