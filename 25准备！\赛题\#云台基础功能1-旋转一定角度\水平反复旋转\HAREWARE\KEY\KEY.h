/**
 ******************************************************************************
 * @file    KEY.h
 * <AUTHOR> Name]
 * @version V1.0
 * @date    2025-01-31
 * @brief   按键输入检测驱动头文件
 *          
 *          本文件定义了按键输入检测的初始化和扫描接口
 *          支持单个按键的状态检测和去抖处理
 * 
 * @note    硬件连接:
 *          PB14 -> 按键输入 (内部上拉，按下时为低电平)
 *          
 *          按键特性:
 *          - 内部上拉电阻使能
 *          - 按下时引脚为低电平(0)
 *          - 释放时引脚为高电平(1)
 *          - 无硬件去抖，依靠软件处理
 ******************************************************************************
 */

#ifndef __KEY_H
#define __KEY_H

#include "sys.h"

/* 函数声明 ------------------------------------------------------------------*/

/**
 * @brief  按键输入初始化
 * @param  None
 * @retval None
 * @note   配置PB14为输入模式，使能内部上拉电阻
 *         按键连接到地，按下时引脚读取为低电平
 */
void Key_Init(void);

/**
 * @brief  按键状态扫描
 * @param  None
 * @retval 按键状态: 1=按下, 0=释放
 * @note   实时读取按键状态，无去抖处理
 *         返回值: 1表示按键被按下，0表示按键未按下
 *         如需去抖，调用方应添加延时或状态确认
 */
u8 Key_Scan(void);

#endif
