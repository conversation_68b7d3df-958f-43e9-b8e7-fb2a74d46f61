/**
 ******************************************************************************
 * @file    motor_control.h
 * <AUTHOR>
 * @version V1.0
 * @date    2025-08-01
 * @brief   统一电机控制接口头文件
 *          
 *          本模块提供统一的双轴电机控制接口，封装底层ATD5984驱动
 *          支持轴选择、统一的控制函数和错误处理机制
 * 
 * @note    硬件支持:
 *          - STM32F407ZGT6主控
 *          - D36A双路步进电机驱动器 
 *          - 42步进电机 1/16细分模式
 *          
 *          轴定义:
 *          - 水平轴: 电机A (TIM8_CH3/PC8, DIR=PD3, SLEEP=PD2)
 *          - 垂直轴: 电机B (TIM1_CH1/PA8, DIR=PB12, SLEEP=PC12)
 ******************************************************************************
 */

#ifndef __MOTOR_CONTROL_H
#define __MOTOR_CONTROL_H

#include "stm32f4xx.h"
#include <stdint.h>
#include <stdbool.h>

/* 前向声明ATD5984函数，避免循环包含 */
void ATD5984_Init(void);
void STEP12_PWM_Init(u16 arr, u16 psc);
void STEP_B_PWM_Init(u16 arr, u16 psc);
void Motor_A_Enable(void);
void Motor_A_Disable(void);
void Motor_B_Enable(void);
void Motor_B_Disable(void);
void Motor_A_Rotate(float angle);
void Motor_B_Rotate(float angle);
void TIM8_SetFrequency(uint16_t target_freq);
void TIM1_SetFrequency(uint16_t target_freq);
uint16_t TIM8_GetCurrentFrequency(void);
uint16_t TIM1_GetCurrentFrequency(void);

/* 轴选择枚举定义 -------------------------------------------------------------*/

/**
 * @brief  电机轴选择枚举
 * @note   用于统一接口中指定控制的电机轴
 */
typedef enum {
    MOTOR_AXIS_HORIZONTAL = 0,  /**< 水平轴（电机A） */
    MOTOR_AXIS_VERTICAL = 1     /**< 垂直轴（电机B） */
} MotorAxis_t;

/* 控制模式枚举定义 -----------------------------------------------------------*/

/**
 * @brief  电机控制模式枚举
 * @note   用于切换不同的控制策略
 */
typedef enum {
    MOTOR_MODE_MANUAL = 0,      /**< 手动控制模式 */
    MOTOR_MODE_AUTO = 1,        /**< 自动控制模式 */
    MOTOR_MODE_SEARCH = 2       /**< 搜索模式 */
} MotorMode_t;

/* 电机状态枚举定义 -----------------------------------------------------------*/

/**
 * @brief  电机运行状态枚举
 * @note   用于查询电机当前状态
 */
typedef enum {
    MOTOR_STATE_DISABLED = 0,   /**< 电机禁用状态 */
    MOTOR_STATE_IDLE = 1,       /**< 电机空闲状态 */
    MOTOR_STATE_MOVING = 2,     /**< 电机运动状态 */
    MOTOR_STATE_ERROR = 3       /**< 电机错误状态 */
} MotorState_t;

/* 速度档位常量定义 -----------------------------------------------------------*/

/** 水平轴速度档位 (Hz) */
#define MOTOR_HORIZONTAL_SPEED_SLOW     400   /**< 慢速档位 */
#define MOTOR_HORIZONTAL_SPEED_MEDIUM   600   /**< 中速档位 */
#define MOTOR_HORIZONTAL_SPEED_FAST     900   /**< 快速档位 */
#define MOTOR_HORIZONTAL_SPEED_HIGH     1143  /**< 高速档位 */
#define MOTOR_HORIZONTAL_SPEED_MAX      1500  /**< 最大速度档位 */

/** 垂直轴速度档位 (Hz) */
#define MOTOR_VERTICAL_SPEED_PRECISION  50    /**< 精密档位 */
#define MOTOR_VERTICAL_SPEED_SLOW       75    /**< 慢速档位 */
#define MOTOR_VERTICAL_SPEED_MEDIUM     100   /**< 中速档位 */
#define MOTOR_VERTICAL_SPEED_FAST       150   /**< 快速档位 */
#define MOTOR_VERTICAL_SPEED_MAX        200   /**< 最大速度档位 */

/* 函数声明 ------------------------------------------------------------------*/

/* 系统初始化接口 */

/**
 * @brief  电机控制系统初始化
 * @param  None
 * @retval bool: true=初始化成功, false=初始化失败
 * @note   初始化双轴电机控制系统，包括硬件初始化和默认配置
 */
bool Motor_System_Init(void);

/* 基础控制接口 */

/**
 * @brief  电机使能控制
 * @param  axis: 电机轴选择 (MOTOR_AXIS_HORIZONTAL/VERTICAL)
 * @retval bool: true=操作成功, false=操作失败
 * @note   使能指定电机，设置SLEEP信号为高电平
 */
bool Motor_Enable(MotorAxis_t axis);

/**
 * @brief  电机禁用控制
 * @param  axis: 电机轴选择 (MOTOR_AXIS_HORIZONTAL/VERTICAL)
 * @retval bool: true=操作成功, false=操作失败
 * @note   禁用指定电机，设置SLEEP信号为低电平
 */
bool Motor_Disable(MotorAxis_t axis);

/**
 * @brief  设置电机转速
 * @param  axis: 电机轴选择 (MOTOR_AXIS_HORIZONTAL/VERTICAL)
 * @param  speed_hz: 目标转速 (Hz)
 * @retval bool: true=设置成功, false=设置失败
 * @note   动态设置电机PWM频率
 *         水平轴有效范围: 400-1500Hz
 *         垂直轴有效范围: 50-200Hz
 */
bool Motor_SetSpeed(MotorAxis_t axis, uint16_t speed_hz);

/**
 * @brief  电机角度旋转控制
 * @param  axis: 电机轴选择 (MOTOR_AXIS_HORIZONTAL/VERTICAL)
 * @param  angle: 旋转角度 (度，正数顺时针，负数逆时针)
 * @retval bool: true=操作成功, false=操作失败
 * @note   控制电机旋转指定角度，自动计算步数和方向
 */
bool Motor_Rotate(MotorAxis_t axis, float angle);

/**
 * @brief  立即停止电机
 * @param  axis: 电机轴选择 (MOTOR_AXIS_HORIZONTAL/VERTICAL)
 * @retval bool: true=停止成功, false=停止失败
 * @note   立即停止指定电机的PWM输出
 */
bool Motor_Stop(MotorAxis_t axis);

/**
 * @brief  紧急停止所有电机
 * @param  None
 * @retval None
 * @note   立即停止所有电机运动，用于紧急情况
 */
void Motor_Emergency_Stop(void);

/* 状态查询接口 */

/**
 * @brief  获取电机当前状态
 * @param  axis: 电机轴选择 (MOTOR_AXIS_HORIZONTAL/VERTICAL)
 * @retval MotorState_t: 电机当前状态
 * @note   查询指定电机的运行状态
 */
MotorState_t Motor_GetState(MotorAxis_t axis);

/**
 * @brief  获取电机当前转速
 * @param  axis: 电机轴选择 (MOTOR_AXIS_HORIZONTAL/VERTICAL)
 * @retval uint16_t: 当前转速 (Hz)，0表示停止
 * @note   返回指定电机的当前PWM频率
 */
uint16_t Motor_GetSpeed(MotorAxis_t axis);

/**
 * @brief  检查电机是否在运动中
 * @param  axis: 电机轴选择 (MOTOR_AXIS_HORIZONTAL/VERTICAL)
 * @retval bool: true=运动中, false=已停止
 * @note   检查指定电机是否正在执行运动指令
 */
bool Motor_IsMoving(MotorAxis_t axis);

#endif
