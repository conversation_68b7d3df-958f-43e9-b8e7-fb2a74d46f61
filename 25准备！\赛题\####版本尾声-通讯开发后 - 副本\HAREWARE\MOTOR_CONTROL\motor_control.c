#include "../../SYSTEM/sys/sys.h"
#include "motor_control.h"
#include "../ATD5984/ATD5984.h"
#include "../../SYSTEM/delay/delay.h"
#include "../../SYSTEM/usart/usart.h"

/* 全局变量：电机状态管理 */
static MotorState_t motor_state[2] = {MOTOR_STATE_DISABLED, MOTOR_STATE_DISABLED};
// static MotorMode_t system_mode = MOTOR_MODE_MANUAL; // 暂时未使用，预留扩展

/**
 * @brief  电机控制系统初始化
 * @param  None
 * @retval bool: true=初始化成功, false=初始化失败
 * @note   统一初始化双轴电机控制系统
 */
bool Motor_System_Init(void)
{
    printf("Motor Control System initializing...\r\n");
    
    /* 调用底层驱动初始化 */
    ATD5984_Init();            // 双电机控制引脚初始化
    STEP12_PWM_Init(10499, 6); // TIM8_CH3初始化 (电机A)
    STEP_B_PWM_Init(10499, 6); // TIM1_CH1初始化 (电机B)
    
    /* 设置初始状态 */
    motor_state[MOTOR_AXIS_HORIZONTAL] = MOTOR_STATE_IDLE;
    motor_state[MOTOR_AXIS_VERTICAL] = MOTOR_STATE_IDLE;
    
    printf("Motor Control System initialized successfully\r\n");
    return true;
}

/**
 * @brief  电机使能控制
 * @param  axis: 电机轴选择
 * @retval bool: true=操作成功, false=操作失败
 */
bool Motor_Enable(MotorAxis_t axis)
{
    if(axis >= 2) return false; // 参数检查
    
    if(axis == MOTOR_AXIS_HORIZONTAL) {
        Motor_A_Enable();
        motor_state[MOTOR_AXIS_HORIZONTAL] = MOTOR_STATE_IDLE;
        printf("Motor A (Horizontal) Enabled\r\n");
    } else {
        Motor_B_Enable();
        motor_state[MOTOR_AXIS_VERTICAL] = MOTOR_STATE_IDLE;
        printf("Motor B (Vertical) Enabled\r\n");
    }
    
    return true;
}

/**
 * @brief  电机禁用控制
 * @param  axis: 电机轴选择
 * @retval bool: true=操作成功, false=操作失败
 */
bool Motor_Disable(MotorAxis_t axis)
{
    if(axis >= 2) return false; // 参数检查
    
    // 先停止运动再禁用
    Motor_Stop(axis);
    
    if(axis == MOTOR_AXIS_HORIZONTAL) {
        Motor_A_Disable();
        motor_state[MOTOR_AXIS_HORIZONTAL] = MOTOR_STATE_DISABLED;
        printf("Motor A (Horizontal) Disabled\r\n");
    } else {
        Motor_B_Disable();
        motor_state[MOTOR_AXIS_VERTICAL] = MOTOR_STATE_DISABLED;
        printf("Motor B (Vertical) Disabled\r\n");
    }
    
    return true;
}

/**
 * @brief  设置电机转速
 * @param  axis: 电机轴选择
 * @param  speed_hz: 目标转速 (Hz)
 * @retval bool: true=设置成功, false=设置失败
 */
bool Motor_SetSpeed(MotorAxis_t axis, uint16_t speed_hz)
{
    if(axis >= 2) return false; // 参数检查
    
    if(axis == MOTOR_AXIS_HORIZONTAL) {
        // 水平轴速度范围检查
        if(speed_hz < 400 || speed_hz > 1500) {
            printf("Motor A speed out of range: %d Hz (valid: 400-1500)\r\n", speed_hz);
            return false;
        }
        TIM8_SetFrequency(speed_hz);
        printf("Motor A speed set to %d Hz\r\n", speed_hz);
    } else {
        // 垂直轴速度范围检查
        if(speed_hz < 50 || speed_hz > 200) {
            printf("Motor B speed out of range: %d Hz (valid: 50-200)\r\n", speed_hz);
            return false;
        }
        TIM1_SetFrequency(speed_hz);
        printf("Motor B speed set to %d Hz\r\n", speed_hz);
    }
    
    return true;
}

/**
 * @brief  电机角度旋转控制
 * @param  axis: 电机轴选择
 * @param  angle: 旋转角度 (度)
 * @retval bool: true=操作成功, false=操作失败
 */
bool Motor_Rotate(MotorAxis_t axis, float angle)
{
    if(axis >= 2) return false; // 参数检查
    
    // 检查电机是否已使能
    if(motor_state[axis] == MOTOR_STATE_DISABLED) {
        printf("Motor %s is disabled, please enable first\r\n", 
               (axis == MOTOR_AXIS_HORIZONTAL) ? "A" : "B");
        return false;
    }
    
    // 设置运动状态
    motor_state[axis] = MOTOR_STATE_MOVING;
    
    if(axis == MOTOR_AXIS_HORIZONTAL) {
        Motor_A_Rotate(angle);
        printf("Motor A rotated %.1f degrees\r\n", angle);
    } else {
        Motor_B_Rotate(angle);
        printf("Motor B rotated %.1f degrees\r\n", angle);
    }
    
    // 恢复空闲状态
    motor_state[axis] = MOTOR_STATE_IDLE;
    
    return true;
}

/**
 * @brief  立即停止电机
 * @param  axis: 电机轴选择
 * @retval bool: true=停止成功, false=停止失败
 */
bool Motor_Stop(MotorAxis_t axis)
{
    if(axis >= 2) return false; // 参数检查
    
    if(axis == MOTOR_AXIS_HORIZONTAL) {
        TIM_CCxCmd(TIM8, TIM_Channel_3, DISABLE);  // 停止PWM输出
        printf("Motor A stopped\r\n");
    } else {
        TIM_CCxCmd(TIM1, TIM_Channel_1, DISABLE);  // 停止PWM输出
        printf("Motor B stopped\r\n");
    }
    
    // 更新状态
    if(motor_state[axis] != MOTOR_STATE_DISABLED) {
        motor_state[axis] = MOTOR_STATE_IDLE;
    }
    
    return true;
}

/**
 * @brief  紧急停止所有电机
 * @param  None
 * @retval None
 */
void Motor_Emergency_Stop(void)
{
    printf("EMERGENCY STOP - All motors stopping!\r\n");
    
    // 停止所有PWM输出
    TIM_CCxCmd(TIM8, TIM_Channel_3, DISABLE);
    TIM_CCxCmd(TIM1, TIM_Channel_1, DISABLE);
    
    // 禁用所有电机
    Motor_A_Disable();
    Motor_B_Disable();
    
    // 更新状态
    motor_state[MOTOR_AXIS_HORIZONTAL] = MOTOR_STATE_DISABLED;
    motor_state[MOTOR_AXIS_VERTICAL] = MOTOR_STATE_DISABLED;
    
    printf("Emergency stop completed\r\n");
}

/**
 * @brief  获取电机当前状态
 * @param  axis: 电机轴选择
 * @retval MotorState_t: 电机当前状态
 */
MotorState_t Motor_GetState(MotorAxis_t axis)
{
    if(axis >= 2) return MOTOR_STATE_ERROR;
    return motor_state[axis];
}

/**
 * @brief  获取电机当前转速
 * @param  axis: 电机轴选择
 * @retval uint16_t: 当前转速 (Hz)
 */
uint16_t Motor_GetSpeed(MotorAxis_t axis)
{
    if(axis >= 2) return 0;
    
    if(axis == MOTOR_AXIS_HORIZONTAL) {
        return TIM8_GetCurrentFrequency();
    } else {
        return TIM1_GetCurrentFrequency();
    }
}

/**
 * @brief  检查电机是否在运动中
 * @param  axis: 电机轴选择
 * @retval bool: true=运动中, false=已停止
 */
bool Motor_IsMoving(MotorAxis_t axis)
{
    if(axis >= 2) return false;
    return (motor_state[axis] == MOTOR_STATE_MOVING);
}
