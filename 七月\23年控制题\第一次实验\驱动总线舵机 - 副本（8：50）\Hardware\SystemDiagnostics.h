#ifndef __SYSTEM_DIAGNOSTICS_H
#define __SYSTEM_DIAGNOSTICS_H

#include "stdint.h"
#include "Servo.h"
#include "Geometry.h"
#include "Timer.h"
#include "Key.h"

// 系统诊断结果定义
typedef enum {
    DIAG_OK = 0,
    DIAG_WARNING,
    DIAG_ERROR,
    DIAG_CRITICAL
} DiagnosticLevel_t;

// 系统配置定义
#define TEST_MODE_NO_HARDWARE   1           // 测试模式：无硬件连接

// 舵机角度范围定义 - 激光云台项目
#define PAN_ANGLE_MIN           30.0f       // Pan舵机最小角度
#define PAN_ANGLE_MAX           210.0f      // Pan舵机最大角度
#define TILT_ANGLE_MIN          30.0f       // Tilt舵机最小角度
#define TILT_ANGLE_MAX          210.0f      // Tilt舵机最大角度

// 系统模块状态
typedef struct {
    uint8_t servo_pan_ok;           // Pan舵机状态
    uint8_t servo_tilt_ok;          // Tilt舵机状态
    uint8_t timer_ok;               // 定时器状态
    uint8_t key_ok;                 // 按键状态
    uint8_t oled_ok;                // OLED状态
    uint8_t geometry_ok;            // 几何算法状态
    uint8_t memory_ok;              // 内存状态
} SystemModuleStatus_t;

// 性能统计数据
typedef struct {
    uint32_t total_runtime;         // 总运行时间 (ms)
    uint32_t servo_commands_sent;   // 发送的舵机命令数
    uint32_t servo_errors;          // 舵机通信错误数
    uint32_t key_presses;           // 按键按下次数
    uint32_t state_transitions;     // 状态转换次数
    uint32_t geometry_calculations; // 几何计算次数
    uint32_t path_steps_executed;   // 执行的路径步数
    float average_step_time;        // 平均步进时间 (ms)
    float system_efficiency;        // 系统效率 (%)
} SystemPerformanceStats_t;

// 系统配置参数
typedef struct {
    float position_tolerance;       // 位置容差 (mm)
    uint16_t servo_timeout;         // 舵机超时时间 (ms)
    uint16_t movement_speed;        // 移动速度 (steps/s)
    uint8_t debug_level;            // 调试级别 (0-3)
    uint8_t auto_calibration;       // 自动校准开关
    uint8_t safety_checks;          // 安全检查开关
} SystemConfiguration_t;

// 错误记录结构
typedef struct {
    uint32_t timestamp;             // 错误时间戳
    uint8_t module_id;              // 模块ID
    uint8_t error_code;             // 错误代码
    char description[32];           // 错误描述
} ErrorRecord_t;

#define MAX_ERROR_RECORDS 10

// 系统诊断控制结构
typedef struct {
    SystemModuleStatus_t module_status;
    SystemPerformanceStats_t performance;
    SystemConfiguration_t config;
    ErrorRecord_t error_log[MAX_ERROR_RECORDS];
    uint8_t error_count;
    uint32_t last_diagnostic_time;
    uint8_t diagnostic_enabled;
} SystemDiagnostics_t;

// 主要诊断接口
void SystemDiag_Init(SystemDiagnostics_t* diag);
DiagnosticLevel_t SystemDiag_RunFullCheck(SystemDiagnostics_t* diag);
DiagnosticLevel_t SystemDiag_QuickCheck(SystemDiagnostics_t* diag);
void SystemDiag_Update(SystemDiagnostics_t* diag);

// 模块诊断函数
DiagnosticLevel_t SystemDiag_CheckServos(SystemDiagnostics_t* diag);
DiagnosticLevel_t SystemDiag_CheckTimer(SystemDiagnostics_t* diag);
DiagnosticLevel_t SystemDiag_CheckKeys(SystemDiagnostics_t* diag);
DiagnosticLevel_t SystemDiag_CheckGeometry(SystemDiagnostics_t* diag);
DiagnosticLevel_t SystemDiag_CheckMemory(SystemDiagnostics_t* diag);

// 性能监控函数
void SystemDiag_RecordServoCommand(SystemDiagnostics_t* diag);
void SystemDiag_RecordServoError(SystemDiagnostics_t* diag);
void SystemDiag_RecordKeyPress(SystemDiagnostics_t* diag);
void SystemDiag_RecordStateTransition(SystemDiagnostics_t* diag);
void SystemDiag_RecordGeometryCalc(SystemDiagnostics_t* diag);
void SystemDiag_RecordPathStep(SystemDiagnostics_t* diag, float step_time);

// 错误记录函数
void SystemDiag_LogError(SystemDiagnostics_t* diag, uint8_t module_id, 
                        uint8_t error_code, const char* description);
void SystemDiag_ClearErrorLog(SystemDiagnostics_t* diag);
uint8_t SystemDiag_GetErrorCount(SystemDiagnostics_t* diag);

// 配置管理函数
void SystemDiag_SetConfig(SystemDiagnostics_t* diag, SystemConfiguration_t* config);
SystemConfiguration_t* SystemDiag_GetConfig(SystemDiagnostics_t* diag);
void SystemDiag_LoadDefaultConfig(SystemDiagnostics_t* diag);

// 校准和优化函数
DiagnosticLevel_t SystemDiag_CalibrateServos(SystemDiagnostics_t* diag);
DiagnosticLevel_t SystemDiag_OptimizePerformance(SystemDiagnostics_t* diag);
DiagnosticLevel_t SystemDiag_ValidateAccuracy(SystemDiagnostics_t* diag);

// 报告生成函数
void SystemDiag_GenerateReport(SystemDiagnostics_t* diag);
void SystemDiag_ShowStatus(SystemDiagnostics_t* diag);
void SystemDiag_ShowPerformance(SystemDiagnostics_t* diag);
void SystemDiag_ShowErrors(SystemDiagnostics_t* diag);

// 系统维护函数
void SystemDiag_ResetStatistics(SystemDiagnostics_t* diag);
void SystemDiag_BackupConfiguration(SystemDiagnostics_t* diag);
void SystemDiag_RestoreConfiguration(SystemDiagnostics_t* diag);

// 调试辅助函数
const char* SystemDiag_GetLevelString(DiagnosticLevel_t level);
const char* SystemDiag_GetModuleName(uint8_t module_id);
void SystemDiag_PrintDiagnostic(DiagnosticLevel_t level, const char* message);

// 模块ID定义
#define MODULE_SERVO_PAN    1
#define MODULE_SERVO_TILT   2
#define MODULE_TIMER        3
#define MODULE_KEY          4
#define MODULE_OLED         5
#define MODULE_GEOMETRY     6
#define MODULE_MEMORY       7
#define MODULE_SYSTEM       8

#endif
