# STM32F407双路步进电机控制系统 - 项目技术总结

## 项目概述

### 项目背景
- **竞赛题目**: 2025年全国大学生电子设计竞赛E题 - 简易自行瞄准装置
- **开发阶段**: 突破瓶颈期阶段 → 实验1：成功驱动
- **项目状态**: ✅ 双路步进电机成功驱动，电机抖动问题已解决
- **开发周期**: 2025年1月 - 持续开发中

### 核心功能
- **双路步进电机精确控制**: 实现2DOF云台系统的水平和垂直轴控制
- **PWM脉冲生成**: 基于TIM8双通道PWM，3.33kHz频率，50%占空比
- **电压监测系统**: ADC1实时监测系统电压，11倍分压精度
- **串口通信**: USART1 @ 115200bps，支持printf重定向
- **定时任务管理**: TIM2 10ms周期中断，系统任务调度

## 系统架构

### 硬件架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   STM32F407ZGT6 │────│ D36A双路驱动器   │────│  42步进电机×2   │
│   @168MHz       │    │ ATD5984芯片     │    │  二相混合式     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └──────────────│  24V电源系统    │──────────────┘
                        │  功率供电       │
                        └─────────────────┘
```

### 软件架构
```
应用层: main.c (主循环控制)
├── ADC电压采集与监测
├── 串口数据输出
└── 系统状态管理

硬件抽象层: HARDWARE/
├── ATD5984.c/h    - 步进电机驱动控制
├── ADC.c/h        - 模数转换器驱动
├── KEY.c/h        - 按键输入检测
└── TIM.c/h        - 定时器PWM控制

系统服务层: SYSTEM/
├── delay.c/h      - SysTick精密延时
├── sys.c/h        - 系统配置与GPIO位操作
└── usart.c/h      - 串口通信服务

底层驱动: FWLIB/
└── STM32F4xx标准外设库
```

## 硬件配置详解

### 主控芯片: STM32F407ZGT6
- **处理器**: ARM Cortex-M4 @ 168MHz
- **Flash**: 1MB，RAM: 192KB
- **封装**: LQFP144，丰富的外设资源
- **FPU**: 硬件浮点单元，提升数学运算性能

### 电机驱动: D36A双路步进电机驱动器
- **驱动芯片**: ATD5984
- **细分精度**: 1/16细分 (0.1125°/步)
- **电流控制**: 8级可调 (0.55A-1.44A)
- **供电范围**: 12-24V (推荐24V)
- **控制方式**: STEP/DIR脉冲方向控制

### 步进电机: 42二相混合式步进电机
- **步距角**: 1.8° (200步/圈)
- **相数**: 二相四线/六线
- **实际分辨率**: 0.1125°/步 (1/16细分后3200步/圈)
- **线序标准**: A+红, A-蓝, B+绿, B-黑

## 引脚分配方案

### 电机A (水平轴)
```
PC8  → STEP-A  (TIM8_CH3 PWM脉冲)
PC13 → DIR-A   (方向控制GPIO)
PD2  → SLEEP-A (使能控制GPIO，低电平使能)
```

### 电机B (垂直轴)
```
PC9  → STEP-B  (TIM8_CH4 PWM脉冲)
PB12 → DIR-B   (方向控制GPIO) ← 优化后引脚，便于接线
PC12 → SLEEP-B (使能控制GPIO，低电平使能)
```

### 系统外设
```
PA9/PA10 → USART1 (TX/RX)
PC0      → ADC1_IN10 (电压检测)
PB14     → KEY (按键输入)
```

## 软件技术特色

### 1. PWM精确控制
```c
// PWM频率计算: f = 168MHz / ((psc+1) * (arr+1))
f = 168,000,000 / ((6+1) * (7199+1)) = 3333.33 Hz

// 50%占空比配置
TIM_Pulse = 3600  // 7200/2 = 50%占空比
```

### 2. GPIO位带操作
- 基于Cortex-M4位带特性实现原子GPIO操作
- 类似51单片机的位操作便利性
- 高效的IO控制性能

### 3. 中断驱动架构
- **TIM2中断**: 10ms周期系统任务调度
- **USART1中断**: 异步串口数据接收
- **SysTick**: 1ms系统时基，精确延时

### 4. 模块化设计
- 清晰的层次化架构
- 标准化的接口设计
- 便于功能扩展和维护

## 关键技术突破

### 问题1: 电机抖动问题
**原因分析**: 
- 42步进电机线序接错导致磁场方向错误
- PWM占空比过低 (0.7%) 导致驱动力不足

**解决方案**:
```c
// 修正PWM占空比从0.7%提升到50%
TIM_OCInitStructure.TIM_Pulse = 3600; // 50%占空比
```
- 按照标准线序重新接线: A+红, A-蓝, B+绿, B-黑
- 验证电机线序与驱动器输出匹配

### 问题2: GPIO引脚冲突
**原因分析**: 
- PE7引脚位置不便于接线
- 需要优化PCB布线和接线便利性

**解决方案**:
- 将电机B方向控制从PE7改为PB12
- 选择右侧引脚便于接线操作
- 更新相关GPIO时钟和初始化配置

### 问题3: 系统稳定性
**技术措施**:
- ADC多次采样平均滤波
- 串口中断缓冲区设计
- 看门狗保护机制预留
- 电源电压实时监测

## 测试验证结果

### 功能测试
- ✅ 双路步进电机独立控制
- ✅ PWM频率3.33kHz稳定输出
- ✅ 方向控制正常响应
- ✅ 使能信号有效控制
- ✅ 串口通信115200bps正常
- ✅ ADC电压监测精确
- ✅ 系统启动稳定

### 性能指标
- **步进精度**: 0.1125°/步
- **最大转速**: 可调 (受PWM频率限制)
- **响应时间**: <1ms (GPIO切换)
- **系统功耗**: 监测中
- **通信延时**: <10ms (串口)

## 代码特色

### 1. 详细中文注释
- Doxygen标准格式
- 硬件连接说明
- 参数配置解释
- 使用注意事项

### 2. 错误处理机制
- ADC采样异常处理
- 串口通信错误恢复
- GPIO状态验证

### 3. 可扩展架构
- 预留K230视觉接口
- 支持更多传感器接入
- 模块化设计便于升级

## 应用场景

### 当前应用
- 电机驱动功能验证
- PWM控制参数调试
- 系统稳定性测试

### 扩展应用 (规划中)
- K230视觉追踪集成
- 激光器控制模块
- 自动瞄准算法实现
- 串口屏显示界面
- PID闭环控制系统

## 项目价值

### 技术价值
1. **成功解决电机抖动难题**: 从根本原因分析到系统性解决方案
2. **建立标准化开发流程**: 从硬件设计到软件实现的完整链路
3. **积累调试经验**: PWM参数优化、引脚冲突解决等实战经验
4. **代码工程化水平**: 详细注释、模块化设计、标准化接口

### 竞赛价值
- 为E题"简易自行瞄准装置"奠定坚实的硬件控制基础
- 验证了双轴云台控制的可行性
- 为后续集成K230视觉系统准备了成熟的电机控制平台

### 学习价值
- STM32F4高级应用开发经验
- 步进电机控制系统设计
- 嵌入式系统工程化开发
- 问题分析和解决能力提升

## 下一步发展规划

### 即将实现
1. **K230视觉模块集成**: 目标检测和激光点追踪
2. **PID控制算法**: 激光点精确回中控制
3. **串口屏显示**: 实时状态监测界面
4. **自动瞄准逻辑**: 从检测到瞄准的完整流程

### 长期目标
- 完整的自动瞄准装置系统
- 多目标识别和切换
- 智能化瞄准算法优化
- 用户交互界面完善

---

**开发者**: LHQ  
**创建时间**: 2025-08-01  
**版本**: v1.0 - 成功驱动里程碑版本  
**状态**: ✅ 稳定运行，为下一阶段开发做好准备