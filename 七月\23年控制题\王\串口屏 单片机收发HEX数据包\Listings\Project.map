Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(.text) for Reset_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_md.o(RESET) refers to serial.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to serial.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f10x_md.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_md.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(i.BKP_DeInit) refers to stm32f10x_rcc.o(i.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_can.o(i.CAN_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(i.CAN_GetITStatus) refers to stm32f10x_can.o(i.CheckITStatus) for CheckITStatus
    stm32f10x_cec.o(i.CEC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_wwdg.o(i.WWDG_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    delay.o(i.Delay_ms) refers to delay.o(i.Delay_us) for Delay_us
    delay.o(i.Delay_s) refers to delay.o(i.Delay_ms) for Delay_ms
    led.o(i.LED1_GetStatus) refers to stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit) for GPIO_ReadOutputDataBit
    led.o(i.LED1_OFF) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    led.o(i.LED1_ON) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED1_Turn) refers to stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit) for GPIO_ReadOutputDataBit
    led.o(i.LED1_Turn) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED1_Turn) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    led.o(i.LED_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    key.o(i.Key_GetNum) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    key.o(i.Key_GetNum) refers to delay.o(i.Delay_ms) for Delay_ms
    key.o(i.Key_GetState) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    key.o(i.Key_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    key.o(i.Key_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_SendByte) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Start) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Stop) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_I2C_Init) for OLED_I2C_Init
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_SetCursor) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for OLED_F8x16
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    serial.o(i.Serial1_GetRxFlag) refers to serial.o(.data) for Serial1_RxFlag
    serial.o(i.Serial1_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    serial.o(i.Serial1_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    serial.o(i.Serial1_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    serial.o(i.Serial1_Init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    serial.o(i.Serial1_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    serial.o(i.Serial1_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    serial.o(i.Serial2_GetRxFlag) refers to serial.o(.data) for Serial2_RxFlag
    serial.o(i.Serial2_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    serial.o(i.Serial2_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    serial.o(i.Serial2_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    serial.o(i.Serial2_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    serial.o(i.Serial2_Init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    serial.o(i.Serial2_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    serial.o(i.Serial2_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    serial.o(i.Serial2_SendArray) refers to serial.o(i.Serial2_SendByte) for Serial2_SendByte
    serial.o(i.Serial2_SendByte) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    serial.o(i.Serial2_SendByte) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    serial.o(i.Serial_GetRxFlag) refers to serial.o(.data) for Serial_RxFlag
    serial.o(i.Serial_Init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    serial.o(i.Serial_Init) refers to serial.o(i.Serial1_Init) for Serial1_Init
    serial.o(i.Serial_Init) refers to serial.o(i.Serial2_Init) for Serial2_Init
    serial.o(i.Serial_Printf) refers to printfa.o(i.__0vsprintf) for vsprintf
    serial.o(i.Serial_Printf) refers to serial.o(i.Serial_SendString) for Serial_SendString
    serial.o(i.Serial_SendArray) refers to serial.o(i.Serial_SendByte) for Serial_SendByte
    serial.o(i.Serial_SendByte) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    serial.o(i.Serial_SendByte) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    serial.o(i.Serial_SendNumber) refers to serial.o(i.Serial_Pow) for Serial_Pow
    serial.o(i.Serial_SendNumber) refers to serial.o(i.Serial_SendByte) for Serial_SendByte
    serial.o(i.Serial_SendPacket) refers to serial.o(i.Serial_SendByte) for Serial_SendByte
    serial.o(i.Serial_SendPacket) refers to serial.o(i.Serial_SendArray) for Serial_SendArray
    serial.o(i.Serial_SendPacket) refers to serial.o(.data) for Serial_TxPacket
    serial.o(i.Serial_SendString) refers to serial.o(i.Serial_SendByte) for Serial_SendByte
    serial.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    serial.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    serial.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    serial.o(i.USART1_IRQHandler) refers to serial.o(.data) for Serial1_RxPacket
    serial.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    serial.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    serial.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    serial.o(i.USART2_IRQHandler) refers to serial.o(.data) for timeout_counter
    serial.o(i.USART2_IRQHandler) refers to serial.o(.bss) for Serial2_RxPacket
    serial.o(i.fputc) refers to serial.o(i.Serial_SendByte) for Serial_SendByte
    servo.o(i.Servo_AngleToPosition) refers to cfcmple.o(.text) for __aeabi_cfcmple
    servo.o(i.Servo_AngleToPosition) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    servo.o(i.Servo_AngleToPosition) refers to fmul.o(.text) for __aeabi_fmul
    servo.o(i.Servo_AngleToPosition) refers to fdiv.o(.text) for __aeabi_fdiv
    servo.o(i.Servo_AngleToPosition) refers to ffixui.o(.text) for __aeabi_f2uiz
    servo.o(i.Servo_GetBothAngles) refers to servo.o(.data) for servo1_current_angle
    servo.o(i.Servo_GetServoInfo) refers to servo.o(.data) for servo1_current_angle
    servo.o(i.Servo_Init) refers to delay.o(i.Delay_ms) for Delay_ms
    servo.o(i.Servo_MoveToTop) refers to servo.o(i.Servo_SetPositionWithTime) for Servo_SetPositionWithTime
    servo.o(i.Servo_MoveToTop) refers to delay.o(i.Delay_ms) for Delay_ms
    servo.o(i.Servo_PositionToAngle) refers to ffltui.o(.text) for __aeabi_ui2f
    servo.o(i.Servo_PositionToAngle) refers to fmul.o(.text) for __aeabi_fmul
    servo.o(i.Servo_PositionToAngle) refers to fdiv.o(.text) for __aeabi_fdiv
    servo.o(i.Servo_ReadCurrentAngle) refers to servo.o(.data) for servo1_current_angle
    servo.o(i.Servo_ResetToOrigin) refers to servo.o(i.Servo_SetPositionWithTime) for Servo_SetPositionWithTime
    servo.o(i.Servo_ResetToOrigin) refers to delay.o(i.Delay_ms) for Delay_ms
    servo.o(i.Servo_RunTrack) refers to servo.o(i.Servo_SetPositionWithTime) for Servo_SetPositionWithTime
    servo.o(i.Servo_RunTrack) refers to delay.o(i.Delay_ms) for Delay_ms
    servo.o(i.Servo_RunTrack) refers to servo.o(i.Servo_SmoothMove) for Servo_SmoothMove
    servo.o(i.Servo_RunTrack) refers to main.o(i.Check_ResetCommand) for Check_ResetCommand
    servo.o(i.Servo_RunTrack) refers to main.o(i.Get_TrackInterruptFlag) for Get_TrackInterruptFlag
    servo.o(i.Servo_RunTrack) refers to servo.o(.data) for servo1_current_angle
    servo.o(i.Servo_SendCommand) refers to servo.o(i.Servo_CalculateChecksum) for Servo_CalculateChecksum
    servo.o(i.Servo_SendCommand) refers to serial.o(i.Serial2_SendArray) for Serial2_SendArray
    servo.o(i.Servo_SendCommand) refers to delay.o(i.Delay_ms) for Delay_ms
    servo.o(i.Servo_SendCommandDebug) refers to servo.o(i.Servo_CalculateChecksum) for Servo_CalculateChecksum
    servo.o(i.Servo_SendCommandDebug) refers to serial.o(i.Serial2_SendArray) for Serial2_SendArray
    servo.o(i.Servo_SendCommandDebug) refers to delay.o(i.Delay_ms) for Delay_ms
    servo.o(i.Servo_SetPosition) refers to servo.o(i.Servo_SetPositionWithTime) for Servo_SetPositionWithTime
    servo.o(i.Servo_SetPositionWithTime) refers to servo.o(i.Servo_AngleToPosition) for Servo_AngleToPosition
    servo.o(i.Servo_SetPositionWithTime) refers to servo.o(i.Servo_SendCommandDebug) for Servo_SendCommandDebug
    servo.o(i.Servo_SetPositionWithTime) refers to servo.o(.data) for servo1_current_angle
    servo.o(i.Servo_SmoothMove) refers to fadd.o(.text) for __aeabi_frsub
    servo.o(i.Servo_SmoothMove) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    servo.o(i.Servo_SmoothMove) refers to fdiv.o(.text) for __aeabi_fdiv
    servo.o(i.Servo_SmoothMove) refers to ffixui.o(.text) for __aeabi_f2uiz
    servo.o(i.Servo_SmoothMove) refers to ffltui.o(.text) for __aeabi_ui2f
    servo.o(i.Servo_SmoothMove) refers to main.o(i.Check_ResetCommand) for Check_ResetCommand
    servo.o(i.Servo_SmoothMove) refers to main.o(i.Get_TrackInterruptFlag) for Get_TrackInterruptFlag
    servo.o(i.Servo_SmoothMove) refers to fmul.o(.text) for __aeabi_fmul
    servo.o(i.Servo_SmoothMove) refers to cfcmple.o(.text) for __aeabi_cfcmple
    servo.o(i.Servo_SmoothMove) refers to servo.o(i.Servo_SetPositionWithTime) for Servo_SetPositionWithTime
    servo.o(i.Servo_SmoothMove) refers to delay.o(i.Delay_ms) for Delay_ms
    servo.o(i.Servo_SmoothMove) refers to servo.o(.data) for servo1_current_angle
    main.o(i.Check_ResetCommand) refers to serial.o(i.Serial1_GetRxFlag) for Serial1_GetRxFlag
    main.o(i.Check_ResetCommand) refers to main.o(i.Set_TrackInterruptFlag) for Set_TrackInterruptFlag
    main.o(i.Check_ResetCommand) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.Check_ResetCommand) refers to delay.o(i.Delay_ms) for Delay_ms
    main.o(i.Check_ResetCommand) refers to main.o(i.Servo_ResetWithDebug) for Servo_ResetWithDebug
    main.o(i.Check_ResetCommand) refers to serial.o(.data) for Serial1_RxPacket
    main.o(i.Clear_TrackInterruptFlag) refers to main.o(.data) for track_interrupt_flag
    main.o(i.Clear_TrackRunningFlag) refers to main.o(.data) for track_running_flag
    main.o(i.Display_ServoAngles) refers to servo.o(i.Servo_GetBothAngles) for Servo_GetBothAngles
    main.o(i.Display_ServoAngles) refers to ffixui.o(.text) for __aeabi_f2uiz
    main.o(i.Display_ServoAngles) refers to ffltui.o(.text) for __aeabi_ui2f
    main.o(i.Display_ServoAngles) refers to fadd.o(.text) for __aeabi_frsub
    main.o(i.Display_ServoAngles) refers to fmul.o(.text) for __aeabi_fmul
    main.o(i.Display_ServoAngles) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.Display_ServoAngles) refers to main.o(.data) for servo2_angle
    main.o(i.Get_TrackInterruptFlag) refers to main.o(.data) for track_interrupt_flag
    main.o(i.Get_TrackRunningFlag) refers to main.o(.data) for track_running_flag
    main.o(i.Process_KeyCommand) refers to key.o(i.Key_GetNum) for Key_GetNum
    main.o(i.Process_KeyCommand) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.Process_KeyCommand) refers to main.o(i.Servo_ResetWithDebug) for Servo_ResetWithDebug
    main.o(i.Process_SerialCommand) refers to serial.o(i.Serial1_GetRxFlag) for Serial1_GetRxFlag
    main.o(i.Process_SerialCommand) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    main.o(i.Process_SerialCommand) refers to led.o(i.LED1_ON) for LED1_ON
    main.o(i.Process_SerialCommand) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.Process_SerialCommand) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    main.o(i.Process_SerialCommand) refers to led.o(i.LED1_OFF) for LED1_OFF
    main.o(i.Process_SerialCommand) refers to main.o(i.Get_TrackRunningFlag) for Get_TrackRunningFlag
    main.o(i.Process_SerialCommand) refers to main.o(i.Servo_ResetWithDebug) for Servo_ResetWithDebug
    main.o(i.Process_SerialCommand) refers to main.o(i.Servo_MoveToTopWithDebug) for Servo_MoveToTopWithDebug
    main.o(i.Process_SerialCommand) refers to main.o(i.Clear_TrackInterruptFlag) for Clear_TrackInterruptFlag
    main.o(i.Process_SerialCommand) refers to main.o(i.Set_TrackRunningFlag) for Set_TrackRunningFlag
    main.o(i.Process_SerialCommand) refers to main.o(i.Servo_RunTrackWithDebug) for Servo_RunTrackWithDebug
    main.o(i.Process_SerialCommand) refers to main.o(i.Clear_TrackRunningFlag) for Clear_TrackRunningFlag
    main.o(i.Process_SerialCommand) refers to oled.o(i.OLED_ShowHexNum) for OLED_ShowHexNum
    main.o(i.Process_SerialCommand) refers to delay.o(i.Delay_ms) for Delay_ms
    main.o(i.Process_SerialCommand) refers to main.o(.data) for rx_count
    main.o(i.Process_SerialCommand) refers to serial.o(.data) for Serial1_RxPacket
    main.o(i.Servo_MoveToTopWithDebug) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.Servo_MoveToTopWithDebug) refers to delay.o(i.Delay_ms) for Delay_ms
    main.o(i.Servo_MoveToTopWithDebug) refers to servo.o(i.Servo_SetPositionWithTime) for Servo_SetPositionWithTime
    main.o(i.Servo_MoveToTopWithDebug) refers to main.o(.data) for servo1_angle
    main.o(i.Servo_ResetWithDebug) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.Servo_ResetWithDebug) refers to delay.o(i.Delay_ms) for Delay_ms
    main.o(i.Servo_ResetWithDebug) refers to servo.o(i.Servo_SetPositionWithTime) for Servo_SetPositionWithTime
    main.o(i.Servo_ResetWithDebug) refers to main.o(.data) for servo1_angle
    main.o(i.Servo_RunTrackWithDebug) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.Servo_RunTrackWithDebug) refers to delay.o(i.Delay_ms) for Delay_ms
    main.o(i.Servo_RunTrackWithDebug) refers to servo.o(i.Servo_RunTrack) for Servo_RunTrack
    main.o(i.Servo_RunTrackWithDebug) refers to main.o(i.Get_TrackInterruptFlag) for Get_TrackInterruptFlag
    main.o(i.Servo_RunTrackWithDebug) refers to main.o(i.Clear_TrackInterruptFlag) for Clear_TrackInterruptFlag
    main.o(i.Servo_RunTrackWithDebug) refers to main.o(.data) for led_status
    main.o(i.Set_TrackInterruptFlag) refers to main.o(.data) for track_interrupt_flag
    main.o(i.Set_TrackRunningFlag) refers to main.o(.data) for track_running_flag
    main.o(i.System_Init) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.System_Init) refers to led.o(i.LED_Init) for LED_Init
    main.o(i.System_Init) refers to key.o(i.Key_Init) for Key_Init
    main.o(i.System_Init) refers to serial.o(i.Serial_Init) for Serial_Init
    main.o(i.System_Init) refers to servo.o(i.Servo_Init) for Servo_Init
    main.o(i.System_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    main.o(i.System_Init) refers to delay.o(i.Delay_ms) for Delay_ms
    main.o(i.System_Init) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    main.o(i.System_Init) refers to led.o(i.LED1_OFF) for LED1_OFF
    main.o(i.System_Init) refers to servo.o(i.Servo_GetBothAngles) for Servo_GetBothAngles
    main.o(i.System_Init) refers to main.o(.data) for led_status
    main.o(i.Update_Display) refers to main.o(i.Display_ServoAngles) for Display_ServoAngles
    main.o(i.Update_Display) refers to main.o(.data) for display_counter
    main.o(i.main) refers to main.o(i.System_Init) for System_Init
    main.o(i.main) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.main) refers to main.o(i.Process_SerialCommand) for Process_SerialCommand
    main.o(i.main) refers to main.o(i.Process_KeyCommand) for Process_KeyCommand
    main.o(i.main) refers to main.o(i.Update_Display) for Update_Display
    main.o(i.main) refers to delay.o(i.Delay_ms) for Delay_ms
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to serial.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to serial.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to serial.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to serial.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cfcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cfrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f10x_md.o(HEAP), (512 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing system_stm32f10x.o(.data), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (40 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_Cmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (92 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_Init), (80 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_RegularChannelConfig), (184 bytes).
    Removing stm32f10x_adc.o(i.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearFlag), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_DeInit), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetFlagStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetITStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_RTCOutputConfig), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ReadBackupRegister), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_SetRTCCalibrationValue), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_WriteBackupRegister), (28 bytes).
    Removing stm32f10x_can.o(i.CAN_CancelTransmit), (48 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearFlag), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearITPendingBit), (168 bytes).
    Removing stm32f10x_can.o(i.CAN_DBGFreeze), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_FilterInit), (264 bytes).
    Removing stm32f10x_can.o(i.CAN_GetFlagStatus), (120 bytes).
    Removing stm32f10x_can.o(i.CAN_GetITStatus), (288 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLSBTransmitErrorCounter), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLastErrorCode), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f10x_can.o(i.CAN_ITConfig), (18 bytes).
    Removing stm32f10x_can.o(i.CAN_Init), (276 bytes).
    Removing stm32f10x_can.o(i.CAN_MessagePending), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_OperatingModeRequest), (162 bytes).
    Removing stm32f10x_can.o(i.CAN_Receive), (240 bytes).
    Removing stm32f10x_can.o(i.CAN_SlaveStartBank), (52 bytes).
    Removing stm32f10x_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f10x_can.o(i.CAN_TTComModeCmd), (118 bytes).
    Removing stm32f10x_can.o(i.CAN_Transmit), (294 bytes).
    Removing stm32f10x_can.o(i.CAN_TransmitStatus), (160 bytes).
    Removing stm32f10x_can.o(i.CAN_WakeUp), (48 bytes).
    Removing stm32f10x_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearFlag), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearITPendingBit), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_Cmd), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_DeInit), (22 bytes).
    Removing stm32f10x_cec.o(i.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetITStatus), (40 bytes).
    Removing stm32f10x_cec.o(i.CEC_ITConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_Init), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ReceiveDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_StartOfMessage), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcBlockCRC), (36 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcCRC), (16 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (52 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_Config), (32 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_Cmd), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_DeInit), (332 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (18 bytes).
    Removing stm32f10x_dma.o(i.DMA_Init), (60 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetITStatus), (40 bytes).
    Removing stm32f10x_exti.o(i.EXTI_Init), (148 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (196 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (168 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetBank1Status), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (48 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (64 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (84 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (108 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (104 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (38 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (64 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (128 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (136 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd), (52 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMInit), (230 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (114 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (132 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (144 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (42 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (56 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (58 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (38 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (26 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (236 bytes).
    Removing stm32f10x_i2c.o(i.I2C_NACKPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (64 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearFlag), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_EnterConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ExitConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetCounter), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetDivider), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetFlagStatus), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetITStatus), (36 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ITConfig), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetAlarm), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetCounter), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetPrescaler), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForLastTask), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForSynchro), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataConfig), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DeInit), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFlagStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetITStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetResponse), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ITConfig), (32 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_Init), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCommand), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetPowerState), (28 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (232 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (28 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (88 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (52 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (32 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_Init), (60 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (30 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (128 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (144 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_ARRPreloadConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearITPendingBit), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_Cmd), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CtrlPWMOutputs), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (488 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetITStatus), (34 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (172 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (160 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseInit), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_GetFlagStatus), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetPrescaler), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetWindowValue), (40 bytes).
    Removing delay.o(i.Delay_s), (24 bytes).
    Removing led.o(i.LED1_GetStatus), (16 bytes).
    Removing led.o(i.LED1_Turn), (36 bytes).
    Removing key.o(i.Key_GetState), (60 bytes).
    Removing oled.o(i.OLED_ShowBinNum), (62 bytes).
    Removing oled.o(i.OLED_ShowNum), (68 bytes).
    Removing oled.o(i.OLED_ShowSignedNum), (102 bytes).
    Removing serial.o(i.Serial2_GetRxFlag), (28 bytes).
    Removing serial.o(i.Serial_GetRxFlag), (28 bytes).
    Removing serial.o(i.Serial_Pow), (20 bytes).
    Removing serial.o(i.Serial_Printf), (36 bytes).
    Removing serial.o(i.Serial_SendArray), (26 bytes).
    Removing serial.o(i.Serial_SendByte), (32 bytes).
    Removing serial.o(i.Serial_SendNumber), (58 bytes).
    Removing serial.o(i.Serial_SendPacket), (28 bytes).
    Removing serial.o(i.Serial_SendString), (26 bytes).
    Removing serial.o(i.fputc), (16 bytes).
    Removing servo.o(i.Servo_GetServoInfo), (64 bytes).
    Removing servo.o(i.Servo_MoveToTop), (44 bytes).
    Removing servo.o(i.Servo_PositionToAngle), (48 bytes).
    Removing servo.o(i.Servo_ReadCurrentAngle), (48 bytes).
    Removing servo.o(i.Servo_ResetToOrigin), (44 bytes).
    Removing servo.o(i.Servo_SendCommand), (128 bytes).
    Removing servo.o(i.Servo_SetPosition), (20 bytes).
    Removing dadd.o(.text), (334 bytes).
    Removing dmul.o(.text), (228 bytes).
    Removing ddiv.o(.text), (222 bytes).
    Removing dfixul.o(.text), (48 bytes).
    Removing cdrcmple.o(.text), (48 bytes).
    Removing depilogue.o(.text), (186 bytes).

488 unused section(s) (total 20838 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  ffltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    Hardware\Key.c                           0x00000000   Number         0  key.o ABSOLUTE
    Hardware\LED.c                           0x00000000   Number         0  led.o ABSOLUTE
    Hardware\OLED.c                          0x00000000   Number         0  oled.o ABSOLUTE
    Hardware\Serial.c                        0x00000000   Number         0  serial.o ABSOLUTE
    Hardware\Servo.c                         0x00000000   Number         0  servo.o ABSOLUTE
    Library\misc.c                           0x00000000   Number         0  misc.o ABSOLUTE
    Library\stm32f10x_adc.c                  0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    Library\stm32f10x_bkp.c                  0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    Library\stm32f10x_can.c                  0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    Library\stm32f10x_cec.c                  0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    Library\stm32f10x_crc.c                  0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    Library\stm32f10x_dac.c                  0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    Library\stm32f10x_dbgmcu.c               0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    Library\stm32f10x_dma.c                  0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    Library\stm32f10x_exti.c                 0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    Library\stm32f10x_flash.c                0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    Library\stm32f10x_fsmc.c                 0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    Library\stm32f10x_gpio.c                 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    Library\stm32f10x_i2c.c                  0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    Library\stm32f10x_iwdg.c                 0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    Library\stm32f10x_pwr.c                  0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    Library\stm32f10x_rcc.c                  0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    Library\stm32f10x_rtc.c                  0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    Library\stm32f10x_sdio.c                 0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    Library\stm32f10x_spi.c                  0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    Library\stm32f10x_tim.c                  0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    Library\stm32f10x_usart.c                0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    Library\stm32f10x_wwdg.c                 0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    Start\\core_cm3.c                        0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\core_cm3.c                         0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\startup_stm32f10x_md.s             0x00000000   Number         0  startup_stm32f10x_md.o ABSOLUTE
    Start\system_stm32f10x.c                 0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    System\Delay.c                           0x00000000   Number         0  delay.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\stm32f10x_it.c                      0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    cfcmple.s                                0x00000000   Number         0  cfcmple.o ABSOLUTE
    cfrcmple.s                               0x00000000   Number         0  cfrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f10x_md.o(RESET)
    .ARM.Collect$$$$00000000                 0x080000ec   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080000ec   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080000f0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080000f4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080000f4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080000f4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x080000fc   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x080000fc   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x080000fc   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080000fc   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000100   Section       36  startup_stm32f10x_md.o(.text)
    .text                                    0x08000124   Section        0  fadd.o(.text)
    .text                                    0x080001d4   Section        0  fmul.o(.text)
    .text                                    0x08000238   Section        0  fdiv.o(.text)
    .text                                    0x080002b4   Section        0  ffltui.o(.text)
    .text                                    0x080002be   Section        0  ffixui.o(.text)
    .text                                    0x080002e8   Section       20  cfcmple.o(.text)
    .text                                    0x080002fc   Section       20  cfrcmple.o(.text)
    .text                                    0x08000310   Section        0  iusefp.o(.text)
    .text                                    0x08000310   Section        0  fepilogue.o(.text)
    .text                                    0x08000380   Section       36  init.o(.text)
    i.BusFault_Handler                       0x080003a4   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.Check_ResetCommand                     0x080003a8   Section        0  main.o(i.Check_ResetCommand)
    i.Clear_TrackInterruptFlag               0x08000448   Section        0  main.o(i.Clear_TrackInterruptFlag)
    i.Clear_TrackRunningFlag                 0x08000454   Section        0  main.o(i.Clear_TrackRunningFlag)
    i.DebugMon_Handler                       0x08000460   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.Delay_ms                               0x08000462   Section        0  delay.o(i.Delay_ms)
    i.Delay_us                               0x0800047a   Section        0  delay.o(i.Delay_us)
    i.Display_ServoAngles                    0x080004a8   Section        0  main.o(i.Display_ServoAngles)
    i.GPIO_Init                              0x080005ec   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_ReadInputDataBit                  0x08000702   Section        0  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x08000714   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08000718   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.GPIO_WriteBit                          0x0800071c   Section        0  stm32f10x_gpio.o(i.GPIO_WriteBit)
    i.Get_TrackInterruptFlag                 0x08000728   Section        0  main.o(i.Get_TrackInterruptFlag)
    i.Get_TrackRunningFlag                   0x08000734   Section        0  main.o(i.Get_TrackRunningFlag)
    i.HardFault_Handler                      0x08000740   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.Key_GetNum                             0x08000744   Section        0  key.o(i.Key_GetNum)
    i.Key_Init                               0x080007b8   Section        0  key.o(i.Key_Init)
    i.LED1_OFF                               0x080007e4   Section        0  led.o(i.LED1_OFF)
    i.LED1_ON                                0x080007f4   Section        0  led.o(i.LED1_ON)
    i.LED_Init                               0x08000804   Section        0  led.o(i.LED_Init)
    i.MemManage_Handler                      0x08000838   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x0800083c   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08000840   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x080008b0   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.OLED_Clear                             0x080008c4   Section        0  oled.o(i.OLED_Clear)
    i.OLED_I2C_Init                          0x080008f0   Section        0  oled.o(i.OLED_I2C_Init)
    i.OLED_I2C_SendByte                      0x08000940   Section        0  oled.o(i.OLED_I2C_SendByte)
    i.OLED_I2C_Start                         0x0800099c   Section        0  oled.o(i.OLED_I2C_Start)
    i.OLED_I2C_Stop                          0x080009d0   Section        0  oled.o(i.OLED_I2C_Stop)
    i.OLED_Init                              0x080009f8   Section        0  oled.o(i.OLED_Init)
    i.OLED_Pow                               0x08000aa6   Section        0  oled.o(i.OLED_Pow)
    i.OLED_SetCursor                         0x08000aba   Section        0  oled.o(i.OLED_SetCursor)
    i.OLED_ShowChar                          0x08000adc   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowHexNum                        0x08000b50   Section        0  oled.o(i.OLED_ShowHexNum)
    i.OLED_ShowString                        0x08000ba4   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_WriteCommand                      0x08000bcc   Section        0  oled.o(i.OLED_WriteCommand)
    i.OLED_WriteData                         0x08000bec   Section        0  oled.o(i.OLED_WriteData)
    i.PendSV_Handler                         0x08000c0c   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.Process_KeyCommand                     0x08000c10   Section        0  main.o(i.Process_KeyCommand)
    i.Process_SerialCommand                  0x08000c60   Section        0  main.o(i.Process_SerialCommand)
    i.RCC_APB1PeriphClockCmd                 0x08000eb0   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08000ed0   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x08000ef0   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x08000fc4   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.Serial1_GetRxFlag                      0x08000fc8   Section        0  serial.o(i.Serial1_GetRxFlag)
    i.Serial1_Init                           0x08000fe4   Section        0  serial.o(i.Serial1_Init)
    i.Serial2_Init                           0x0800108c   Section        0  serial.o(i.Serial2_Init)
    i.Serial2_SendArray                      0x08001134   Section        0  serial.o(i.Serial2_SendArray)
    i.Serial2_SendByte                       0x08001150   Section        0  serial.o(i.Serial2_SendByte)
    i.Serial_Init                            0x08001170   Section        0  serial.o(i.Serial_Init)
    i.Servo_AngleToPosition                  0x08001184   Section        0  servo.o(i.Servo_AngleToPosition)
    i.Servo_CalculateChecksum                0x080011c4   Section        0  servo.o(i.Servo_CalculateChecksum)
    Servo_CalculateChecksum                  0x080011c5   Thumb Code    30  servo.o(i.Servo_CalculateChecksum)
    i.Servo_GetBothAngles                    0x080011e4   Section        0  servo.o(i.Servo_GetBothAngles)
    i.Servo_Init                             0x08001200   Section        0  servo.o(i.Servo_Init)
    i.Servo_MoveToTopWithDebug               0x0800120c   Section        0  main.o(i.Servo_MoveToTopWithDebug)
    i.Servo_ResetWithDebug                   0x08001308   Section        0  main.o(i.Servo_ResetWithDebug)
    i.Servo_RunTrack                         0x08001400   Section        0  servo.o(i.Servo_RunTrack)
    i.Servo_RunTrackWithDebug                0x080014c4   Section        0  main.o(i.Servo_RunTrackWithDebug)
    i.Servo_SendCommandDebug                 0x080015cc   Section        0  servo.o(i.Servo_SendCommandDebug)
    Servo_SendCommandDebug                   0x080015cd   Thumb Code   144  servo.o(i.Servo_SendCommandDebug)
    i.Servo_SetPositionWithTime              0x0800165c   Section        0  servo.o(i.Servo_SetPositionWithTime)
    i.Servo_SmoothMove                       0x080016ac   Section        0  servo.o(i.Servo_SmoothMove)
    i.SetSysClock                            0x080017f8   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x080017f9   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x08001800   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x08001801   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.Set_TrackInterruptFlag                 0x080018e0   Section        0  main.o(i.Set_TrackInterruptFlag)
    i.Set_TrackRunningFlag                   0x080018ec   Section        0  main.o(i.Set_TrackRunningFlag)
    i.SysTick_Handler                        0x080018f8   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x080018fc   Section        0  system_stm32f10x.o(i.SystemInit)
    i.System_Init                            0x0800195c   Section        0  main.o(i.System_Init)
    i.USART1_IRQHandler                      0x080019c4   Section        0  serial.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08001a28   Section        0  serial.o(i.USART2_IRQHandler)
    i.USART_ClearITPendingBit                0x08001b3c   Section        0  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    i.USART_Cmd                              0x08001b5a   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetFlagStatus                    0x08001b72   Section        0  stm32f10x_usart.o(i.USART_GetFlagStatus)
    i.USART_GetITStatus                      0x08001b8c   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x08001be0   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08001c2c   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08001d04   Section        0  stm32f10x_usart.o(i.USART_ReceiveData)
    i.USART_SendData                         0x08001d0e   Section        0  stm32f10x_usart.o(i.USART_SendData)
    i.Update_Display                         0x08001d18   Section        0  main.o(i.Update_Display)
    i.UsageFault_Handler                     0x08001d40   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.__scatterload_copy                     0x08001d44   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08001d52   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08001d54   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.main                                   0x08001d64   Section        0  main.o(i.main)
    .constdata                               0x08001e08   Section     1520  oled.o(.constdata)
    .data                                    0x20000000   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000000   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000010   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x20000014   Section       24  serial.o(.data)
    RxState                                  0x20000023   Data           1  serial.o(.data)
    pRxPacket                                0x20000024   Data           1  serial.o(.data)
    timeout_counter                          0x20000028   Data           4  serial.o(.data)
    .data                                    0x2000002c   Section        8  servo.o(.data)
    servo1_current_angle                     0x2000002c   Data           4  servo.o(.data)
    servo2_current_angle                     0x20000030   Data           4  servo.o(.data)
    .data                                    0x20000034   Section       28  main.o(.data)
    led_status                               0x20000034   Data           1  main.o(.data)
    servo1_angle                             0x20000038   Data           4  main.o(.data)
    servo2_angle                             0x2000003c   Data           4  main.o(.data)
    system_counter                           0x20000040   Data           4  main.o(.data)
    track_interrupt_flag                     0x20000044   Data           1  main.o(.data)
    track_running_flag                       0x20000045   Data           1  main.o(.data)
    rx_count                                 0x20000048   Data           4  main.o(.data)
    display_counter                          0x2000004c   Data           4  main.o(.data)
    .bss                                     0x20000050   Section       32  serial.o(.bss)
    STACK                                    0x20000070   Section     1024  startup_stm32f10x_md.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f10x_md.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_md.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f10x_md.o(RESET)
    __main                                   0x080000ed   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080000ed   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080000f1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080000f5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080000f5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080000f5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080000f5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x080000fd   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x080000fd   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x08000101   Thumb Code     8  startup_stm32f10x_md.o(.text)
    ADC1_2_IRQHandler                        0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_RX1_IRQHandler                      0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_SCE_IRQHandler                      0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI0_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI15_10_IRQHandler                     0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI1_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI2_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI3_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI4_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI9_5_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    FLASH_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_ER_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_EV_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_ER_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_EV_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    PVD_IRQHandler                           0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RCC_IRQHandler                           0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTCAlarm_IRQHandler                      0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTC_IRQHandler                           0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI1_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI2_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TAMPER_IRQHandler                        0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_BRK_IRQHandler                      0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_CC_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_UP_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM2_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM3_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM4_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART3_IRQHandler                        0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USBWakeUp_IRQHandler                     0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    WWDG_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __aeabi_fadd                             0x08000125   Thumb Code   164  fadd.o(.text)
    __aeabi_fsub                             0x080001c9   Thumb Code     6  fadd.o(.text)
    __aeabi_frsub                            0x080001cf   Thumb Code     6  fadd.o(.text)
    __aeabi_fmul                             0x080001d5   Thumb Code   100  fmul.o(.text)
    __aeabi_fdiv                             0x08000239   Thumb Code   124  fdiv.o(.text)
    __aeabi_ui2f                             0x080002b5   Thumb Code    10  ffltui.o(.text)
    __aeabi_f2uiz                            0x080002bf   Thumb Code    40  ffixui.o(.text)
    __aeabi_cfcmpeq                          0x080002e9   Thumb Code     0  cfcmple.o(.text)
    __aeabi_cfcmple                          0x080002e9   Thumb Code    20  cfcmple.o(.text)
    __aeabi_cfrcmple                         0x080002fd   Thumb Code    20  cfrcmple.o(.text)
    __I$use$fp                               0x08000311   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x08000311   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x08000323   Thumb Code    92  fepilogue.o(.text)
    __scatterload                            0x08000381   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000381   Thumb Code     0  init.o(.text)
    BusFault_Handler                         0x080003a5   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    Check_ResetCommand                       0x080003a9   Thumb Code    74  main.o(i.Check_ResetCommand)
    Clear_TrackInterruptFlag                 0x08000449   Thumb Code     8  main.o(i.Clear_TrackInterruptFlag)
    Clear_TrackRunningFlag                   0x08000455   Thumb Code     8  main.o(i.Clear_TrackRunningFlag)
    DebugMon_Handler                         0x08000461   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    Delay_ms                                 0x08000463   Thumb Code    24  delay.o(i.Delay_ms)
    Delay_us                                 0x0800047b   Thumb Code    46  delay.o(i.Delay_us)
    Display_ServoAngles                      0x080004a9   Thumb Code   310  main.o(i.Display_ServoAngles)
    GPIO_Init                                0x080005ed   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_ReadInputDataBit                    0x08000703   Thumb Code    18  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x08000715   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08000719   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    GPIO_WriteBit                            0x0800071d   Thumb Code    10  stm32f10x_gpio.o(i.GPIO_WriteBit)
    Get_TrackInterruptFlag                   0x08000729   Thumb Code     6  main.o(i.Get_TrackInterruptFlag)
    Get_TrackRunningFlag                     0x08000735   Thumb Code     6  main.o(i.Get_TrackRunningFlag)
    HardFault_Handler                        0x08000741   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    Key_GetNum                               0x08000745   Thumb Code   112  key.o(i.Key_GetNum)
    Key_Init                                 0x080007b9   Thumb Code    40  key.o(i.Key_Init)
    LED1_OFF                                 0x080007e5   Thumb Code    12  led.o(i.LED1_OFF)
    LED1_ON                                  0x080007f5   Thumb Code    12  led.o(i.LED1_ON)
    LED_Init                                 0x08000805   Thumb Code    46  led.o(i.LED_Init)
    MemManage_Handler                        0x08000839   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    NMI_Handler                              0x0800083d   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x08000841   Thumb Code   100  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x080008b1   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    OLED_Clear                               0x080008c5   Thumb Code    42  oled.o(i.OLED_Clear)
    OLED_I2C_Init                            0x080008f1   Thumb Code    76  oled.o(i.OLED_I2C_Init)
    OLED_I2C_SendByte                        0x08000941   Thumb Code    88  oled.o(i.OLED_I2C_SendByte)
    OLED_I2C_Start                           0x0800099d   Thumb Code    48  oled.o(i.OLED_I2C_Start)
    OLED_I2C_Stop                            0x080009d1   Thumb Code    36  oled.o(i.OLED_I2C_Stop)
    OLED_Init                                0x080009f9   Thumb Code   174  oled.o(i.OLED_Init)
    OLED_Pow                                 0x08000aa7   Thumb Code    20  oled.o(i.OLED_Pow)
    OLED_SetCursor                           0x08000abb   Thumb Code    34  oled.o(i.OLED_SetCursor)
    OLED_ShowChar                            0x08000add   Thumb Code   110  oled.o(i.OLED_ShowChar)
    OLED_ShowHexNum                          0x08000b51   Thumb Code    84  oled.o(i.OLED_ShowHexNum)
    OLED_ShowString                          0x08000ba5   Thumb Code    40  oled.o(i.OLED_ShowString)
    OLED_WriteCommand                        0x08000bcd   Thumb Code    32  oled.o(i.OLED_WriteCommand)
    OLED_WriteData                           0x08000bed   Thumb Code    32  oled.o(i.OLED_WriteData)
    PendSV_Handler                           0x08000c0d   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    Process_KeyCommand                       0x08000c11   Thumb Code    38  main.o(i.Process_KeyCommand)
    Process_SerialCommand                    0x08000c61   Thumb Code   286  main.o(i.Process_SerialCommand)
    RCC_APB1PeriphClockCmd                   0x08000eb1   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08000ed1   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x08000ef1   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x08000fc5   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    Serial1_GetRxFlag                        0x08000fc9   Thumb Code    22  serial.o(i.Serial1_GetRxFlag)
    Serial1_Init                             0x08000fe5   Thumb Code   160  serial.o(i.Serial1_Init)
    Serial2_Init                             0x0800108d   Thumb Code   160  serial.o(i.Serial2_Init)
    Serial2_SendArray                        0x08001135   Thumb Code    26  serial.o(i.Serial2_SendArray)
    Serial2_SendByte                         0x08001151   Thumb Code    28  serial.o(i.Serial2_SendByte)
    Serial_Init                              0x08001171   Thumb Code    20  serial.o(i.Serial_Init)
    Servo_AngleToPosition                    0x08001185   Thumb Code    54  servo.o(i.Servo_AngleToPosition)
    Servo_GetBothAngles                      0x080011e5   Thumb Code    18  servo.o(i.Servo_GetBothAngles)
    Servo_Init                               0x08001201   Thumb Code    10  servo.o(i.Servo_Init)
    Servo_MoveToTopWithDebug                 0x0800120d   Thumb Code   138  main.o(i.Servo_MoveToTopWithDebug)
    Servo_ResetWithDebug                     0x08001309   Thumb Code   138  main.o(i.Servo_ResetWithDebug)
    Servo_RunTrack                           0x08001401   Thumb Code   172  servo.o(i.Servo_RunTrack)
    Servo_RunTrackWithDebug                  0x080014c5   Thumb Code   140  main.o(i.Servo_RunTrackWithDebug)
    Servo_SetPositionWithTime                0x0800165d   Thumb Code    72  servo.o(i.Servo_SetPositionWithTime)
    Servo_SmoothMove                         0x080016ad   Thumb Code   318  servo.o(i.Servo_SmoothMove)
    Set_TrackInterruptFlag                   0x080018e1   Thumb Code     8  main.o(i.Set_TrackInterruptFlag)
    Set_TrackRunningFlag                     0x080018ed   Thumb Code     8  main.o(i.Set_TrackRunningFlag)
    SysTick_Handler                          0x080018f9   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x080018fd   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    System_Init                              0x0800195d   Thumb Code    88  main.o(i.System_Init)
    USART1_IRQHandler                        0x080019c5   Thumb Code    78  serial.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08001a29   Thumb Code   252  serial.o(i.USART2_IRQHandler)
    USART_ClearITPendingBit                  0x08001b3d   Thumb Code    30  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    USART_Cmd                                0x08001b5b   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetFlagStatus                      0x08001b73   Thumb Code    26  stm32f10x_usart.o(i.USART_GetFlagStatus)
    USART_GetITStatus                        0x08001b8d   Thumb Code    84  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x08001be1   Thumb Code    74  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x08001c2d   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08001d05   Thumb Code    10  stm32f10x_usart.o(i.USART_ReceiveData)
    USART_SendData                           0x08001d0f   Thumb Code     8  stm32f10x_usart.o(i.USART_SendData)
    Update_Display                           0x08001d19   Thumb Code    34  main.o(i.Update_Display)
    UsageFault_Handler                       0x08001d41   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    __scatterload_copy                       0x08001d45   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08001d53   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08001d55   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    main                                     0x08001d65   Thumb Code    66  main.o(i.main)
    OLED_F8x16                               0x08001e08   Data        1520  oled.o(.constdata)
    Region$$Table$$Base                      0x080023f8   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08002418   Number         0  anon$$obj.o(Region$$Table)
    Serial1_RxPacket                         0x20000014   Data           4  serial.o(.data)
    Serial1_RxFlag                           0x20000018   Data           1  serial.o(.data)
    Serial2_RxFlag                           0x20000019   Data           1  serial.o(.data)
    Serial_TxPacket                          0x2000001a   Data           4  serial.o(.data)
    Serial_RxPacket                          0x2000001e   Data           4  serial.o(.data)
    Serial_RxFlag                            0x20000022   Data           1  serial.o(.data)
    Serial2_TxPacket                         0x20000050   Data          16  serial.o(.bss)
    Serial2_RxPacket                         0x20000060   Data          16  serial.o(.bss)
    __initial_sp                             0x20000470   Data           0  startup_stm32f10x_md.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00002468, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00002418, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO            3    RESET               startup_stm32f10x_md.o
    0x080000ec   0x080000ec   0x00000000   Code   RO         3811  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080000ec   0x080000ec   0x00000004   Code   RO         3856    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080000f0   0x080000f0   0x00000004   Code   RO         3859    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080000f4   0x080000f4   0x00000000   Code   RO         3861    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080000f4   0x080000f4   0x00000000   Code   RO         3863    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080000f4   0x080000f4   0x00000008   Code   RO         3864    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080000fc   0x080000fc   0x00000000   Code   RO         3866    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x080000fc   0x080000fc   0x00000000   Code   RO         3868    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x080000fc   0x080000fc   0x00000004   Code   RO         3857    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000100   0x08000100   0x00000024   Code   RO            4    .text               startup_stm32f10x_md.o
    0x08000124   0x08000124   0x000000b0   Code   RO         3842    .text               mf_w.l(fadd.o)
    0x080001d4   0x080001d4   0x00000064   Code   RO         3844    .text               mf_w.l(fmul.o)
    0x08000238   0x08000238   0x0000007c   Code   RO         3846    .text               mf_w.l(fdiv.o)
    0x080002b4   0x080002b4   0x0000000a   Code   RO         3848    .text               mf_w.l(ffltui.o)
    0x080002be   0x080002be   0x00000028   Code   RO         3850    .text               mf_w.l(ffixui.o)
    0x080002e6   0x080002e6   0x00000002   PAD
    0x080002e8   0x080002e8   0x00000014   Code   RO         3852    .text               mf_w.l(cfcmple.o)
    0x080002fc   0x080002fc   0x00000014   Code   RO         3854    .text               mf_w.l(cfrcmple.o)
    0x08000310   0x08000310   0x00000000   Code   RO         3875    .text               mc_w.l(iusefp.o)
    0x08000310   0x08000310   0x0000006e   Code   RO         3876    .text               mf_w.l(fepilogue.o)
    0x0800037e   0x0800037e   0x00000002   PAD
    0x08000380   0x08000380   0x00000024   Code   RO         3888    .text               mc_w.l(init.o)
    0x080003a4   0x080003a4   0x00000004   Code   RO         3748    i.BusFault_Handler  stm32f10x_it.o
    0x080003a8   0x080003a8   0x000000a0   Code   RO         3628    i.Check_ResetCommand  main.o
    0x08000448   0x08000448   0x0000000c   Code   RO         3629    i.Clear_TrackInterruptFlag  main.o
    0x08000454   0x08000454   0x0000000c   Code   RO         3630    i.Clear_TrackRunningFlag  main.o
    0x08000460   0x08000460   0x00000002   Code   RO         3749    i.DebugMon_Handler  stm32f10x_it.o
    0x08000462   0x08000462   0x00000018   Code   RO         3196    i.Delay_ms          delay.o
    0x0800047a   0x0800047a   0x0000002e   Code   RO         3198    i.Delay_us          delay.o
    0x080004a8   0x080004a8   0x00000144   Code   RO         3631    i.Display_ServoAngles  main.o
    0x080005ec   0x080005ec   0x00000116   Code   RO         1347    i.GPIO_Init         stm32f10x_gpio.o
    0x08000702   0x08000702   0x00000012   Code   RO         1351    i.GPIO_ReadInputDataBit  stm32f10x_gpio.o
    0x08000714   0x08000714   0x00000004   Code   RO         1354    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x08000718   0x08000718   0x00000004   Code   RO         1355    i.GPIO_SetBits      stm32f10x_gpio.o
    0x0800071c   0x0800071c   0x0000000a   Code   RO         1358    i.GPIO_WriteBit     stm32f10x_gpio.o
    0x08000726   0x08000726   0x00000002   PAD
    0x08000728   0x08000728   0x0000000c   Code   RO         3632    i.Get_TrackInterruptFlag  main.o
    0x08000734   0x08000734   0x0000000c   Code   RO         3633    i.Get_TrackRunningFlag  main.o
    0x08000740   0x08000740   0x00000004   Code   RO         3750    i.HardFault_Handler  stm32f10x_it.o
    0x08000744   0x08000744   0x00000074   Code   RO         3256    i.Key_GetNum        key.o
    0x080007b8   0x080007b8   0x0000002c   Code   RO         3258    i.Key_Init          key.o
    0x080007e4   0x080007e4   0x00000010   Code   RO         3221    i.LED1_OFF          led.o
    0x080007f4   0x080007f4   0x00000010   Code   RO         3222    i.LED1_ON           led.o
    0x08000804   0x08000804   0x00000034   Code   RO         3224    i.LED_Init          led.o
    0x08000838   0x08000838   0x00000004   Code   RO         3751    i.MemManage_Handler  stm32f10x_it.o
    0x0800083c   0x0800083c   0x00000002   Code   RO         3752    i.NMI_Handler       stm32f10x_it.o
    0x0800083e   0x0800083e   0x00000002   PAD
    0x08000840   0x08000840   0x00000070   Code   RO          137    i.NVIC_Init         misc.o
    0x080008b0   0x080008b0   0x00000014   Code   RO          138    i.NVIC_PriorityGroupConfig  misc.o
    0x080008c4   0x080008c4   0x0000002a   Code   RO         3286    i.OLED_Clear        oled.o
    0x080008ee   0x080008ee   0x00000002   PAD
    0x080008f0   0x080008f0   0x00000050   Code   RO         3287    i.OLED_I2C_Init     oled.o
    0x08000940   0x08000940   0x0000005c   Code   RO         3288    i.OLED_I2C_SendByte  oled.o
    0x0800099c   0x0800099c   0x00000034   Code   RO         3289    i.OLED_I2C_Start    oled.o
    0x080009d0   0x080009d0   0x00000028   Code   RO         3290    i.OLED_I2C_Stop     oled.o
    0x080009f8   0x080009f8   0x000000ae   Code   RO         3291    i.OLED_Init         oled.o
    0x08000aa6   0x08000aa6   0x00000014   Code   RO         3292    i.OLED_Pow          oled.o
    0x08000aba   0x08000aba   0x00000022   Code   RO         3293    i.OLED_SetCursor    oled.o
    0x08000adc   0x08000adc   0x00000074   Code   RO         3295    i.OLED_ShowChar     oled.o
    0x08000b50   0x08000b50   0x00000054   Code   RO         3296    i.OLED_ShowHexNum   oled.o
    0x08000ba4   0x08000ba4   0x00000028   Code   RO         3299    i.OLED_ShowString   oled.o
    0x08000bcc   0x08000bcc   0x00000020   Code   RO         3300    i.OLED_WriteCommand  oled.o
    0x08000bec   0x08000bec   0x00000020   Code   RO         3301    i.OLED_WriteData    oled.o
    0x08000c0c   0x08000c0c   0x00000002   Code   RO         3753    i.PendSV_Handler    stm32f10x_it.o
    0x08000c0e   0x08000c0e   0x00000002   PAD
    0x08000c10   0x08000c10   0x00000050   Code   RO         3634    i.Process_KeyCommand  main.o
    0x08000c60   0x08000c60   0x00000250   Code   RO         3635    i.Process_SerialCommand  main.o
    0x08000eb0   0x08000eb0   0x00000020   Code   RO         1775    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08000ed0   0x08000ed0   0x00000020   Code   RO         1777    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08000ef0   0x08000ef0   0x000000d4   Code   RO         1785    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x08000fc4   0x08000fc4   0x00000002   Code   RO         3754    i.SVC_Handler       stm32f10x_it.o
    0x08000fc6   0x08000fc6   0x00000002   PAD
    0x08000fc8   0x08000fc8   0x0000001c   Code   RO         3394    i.Serial1_GetRxFlag  serial.o
    0x08000fe4   0x08000fe4   0x000000a8   Code   RO         3395    i.Serial1_Init      serial.o
    0x0800108c   0x0800108c   0x000000a8   Code   RO         3397    i.Serial2_Init      serial.o
    0x08001134   0x08001134   0x0000001a   Code   RO         3398    i.Serial2_SendArray  serial.o
    0x0800114e   0x0800114e   0x00000002   PAD
    0x08001150   0x08001150   0x00000020   Code   RO         3399    i.Serial2_SendByte  serial.o
    0x08001170   0x08001170   0x00000014   Code   RO         3401    i.Serial_Init       serial.o
    0x08001184   0x08001184   0x00000040   Code   RO         3519    i.Servo_AngleToPosition  servo.o
    0x080011c4   0x080011c4   0x0000001e   Code   RO         3520    i.Servo_CalculateChecksum  servo.o
    0x080011e2   0x080011e2   0x00000002   PAD
    0x080011e4   0x080011e4   0x0000001c   Code   RO         3521    i.Servo_GetBothAngles  servo.o
    0x08001200   0x08001200   0x0000000a   Code   RO         3523    i.Servo_Init        servo.o
    0x0800120a   0x0800120a   0x00000002   PAD
    0x0800120c   0x0800120c   0x000000fc   Code   RO         3636    i.Servo_MoveToTopWithDebug  main.o
    0x08001308   0x08001308   0x000000f8   Code   RO         3637    i.Servo_ResetWithDebug  main.o
    0x08001400   0x08001400   0x000000c4   Code   RO         3528    i.Servo_RunTrack    servo.o
    0x080014c4   0x080014c4   0x00000108   Code   RO         3638    i.Servo_RunTrackWithDebug  main.o
    0x080015cc   0x080015cc   0x00000090   Code   RO         3530    i.Servo_SendCommandDebug  servo.o
    0x0800165c   0x0800165c   0x00000050   Code   RO         3532    i.Servo_SetPositionWithTime  servo.o
    0x080016ac   0x080016ac   0x0000014c   Code   RO         3533    i.Servo_SmoothMove  servo.o
    0x080017f8   0x080017f8   0x00000008   Code   RO           24    i.SetSysClock       system_stm32f10x.o
    0x08001800   0x08001800   0x000000e0   Code   RO           25    i.SetSysClockTo72   system_stm32f10x.o
    0x080018e0   0x080018e0   0x0000000c   Code   RO         3639    i.Set_TrackInterruptFlag  main.o
    0x080018ec   0x080018ec   0x0000000c   Code   RO         3640    i.Set_TrackRunningFlag  main.o
    0x080018f8   0x080018f8   0x00000002   Code   RO         3755    i.SysTick_Handler   stm32f10x_it.o
    0x080018fa   0x080018fa   0x00000002   PAD
    0x080018fc   0x080018fc   0x00000060   Code   RO           27    i.SystemInit        system_stm32f10x.o
    0x0800195c   0x0800195c   0x00000068   Code   RO         3641    i.System_Init       main.o
    0x080019c4   0x080019c4   0x00000064   Code   RO         3409    i.USART1_IRQHandler  serial.o
    0x08001a28   0x08001a28   0x00000114   Code   RO         3410    i.USART2_IRQHandler  serial.o
    0x08001b3c   0x08001b3c   0x0000001e   Code   RO         2957    i.USART_ClearITPendingBit  stm32f10x_usart.o
    0x08001b5a   0x08001b5a   0x00000018   Code   RO         2960    i.USART_Cmd         stm32f10x_usart.o
    0x08001b72   0x08001b72   0x0000001a   Code   RO         2963    i.USART_GetFlagStatus  stm32f10x_usart.o
    0x08001b8c   0x08001b8c   0x00000054   Code   RO         2964    i.USART_GetITStatus  stm32f10x_usart.o
    0x08001be0   0x08001be0   0x0000004a   Code   RO         2966    i.USART_ITConfig    stm32f10x_usart.o
    0x08001c2a   0x08001c2a   0x00000002   PAD
    0x08001c2c   0x08001c2c   0x000000d8   Code   RO         2967    i.USART_Init        stm32f10x_usart.o
    0x08001d04   0x08001d04   0x0000000a   Code   RO         2974    i.USART_ReceiveData  stm32f10x_usart.o
    0x08001d0e   0x08001d0e   0x00000008   Code   RO         2977    i.USART_SendData    stm32f10x_usart.o
    0x08001d16   0x08001d16   0x00000002   PAD
    0x08001d18   0x08001d18   0x00000028   Code   RO         3642    i.Update_Display    main.o
    0x08001d40   0x08001d40   0x00000004   Code   RO         3756    i.UsageFault_Handler  stm32f10x_it.o
    0x08001d44   0x08001d44   0x0000000e   Code   RO         3900    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08001d52   0x08001d52   0x00000002   Code   RO         3901    i.__scatterload_null  mc_w.l(handlers.o)
    0x08001d54   0x08001d54   0x0000000e   Code   RO         3902    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08001d62   0x08001d62   0x00000002   PAD
    0x08001d64   0x08001d64   0x000000a4   Code   RO         3643    i.main              main.o
    0x08001e08   0x08001e08   0x000005f0   Data   RO         3302    .constdata          oled.o
    0x080023f8   0x080023f8   0x00000020   Data   RO         3898    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08002418, Size: 0x00000470, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08002418   0x00000014   Data   RW         1805    .data               stm32f10x_rcc.o
    0x20000014   0x0800242c   0x00000018   Data   RW         3413    .data               serial.o
    0x2000002c   0x08002444   0x00000008   Data   RW         3534    .data               servo.o
    0x20000034   0x0800244c   0x0000001c   Data   RW         3644    .data               main.o
    0x20000050        -       0x00000020   Zero   RW         3412    .bss                serial.o
    0x20000070        -       0x00000400   Zero   RW            1    STACK               startup_stm32f10x_md.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

         0          0          0          0          0       4516   core_cm3.o
        70          0          0          0          0        978   delay.o
       160          8          0          0          0       1044   key.o
        84         14          0          0          0       1376   led.o
      2300        950          0         28          0       9130   main.o
       132         22          0          0          0     204887   misc.o
       838         22       1520          0          0       7984   oled.o
       818         72          0         24         32       5812   serial.o
       884         66          0          8          0       7559   servo.o
        36          8        236          0       1024        824   startup_stm32f10x_md.o
         0          0          0          0          0       1684   stm32f10x_adc.o
       314          0          0          0          0      12516   stm32f10x_gpio.o
        26          0          0          0          0       3974   stm32f10x_it.o
       276         32          0         20          0      13118   stm32f10x_rcc.o
       472          6          0          0          0      12739   stm32f10x_usart.o
       328         28          0          0          0      25077   system_stm32f10x.o

    ----------------------------------------------------------------------
      6760       <USER>       <GROUP>         80       1056     313218   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        22          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        20          0          0          0          0         68   cfcmple.o
        20          0          0          0          0         68   cfrcmple.o
       176          0          0          0          0        140   fadd.o
       124          0          0          0          0         88   fdiv.o
       110          0          0          0          0        168   fepilogue.o
        40          0          0          0          0         68   ffixui.o
        10          0          0          0          0         68   ffltui.o
       100          0          0          0          0         76   fmul.o

    ----------------------------------------------------------------------
       692         <USER>          <GROUP>          0          0        812   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

        86         16          0          0          0         68   mc_w.l
       600          0          0          0          0        744   mf_w.l

    ----------------------------------------------------------------------
       692         <USER>          <GROUP>          0          0        812   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      7452       1244       1788         80       1056     309814   Grand Totals
      7452       1244       1788         80       1056     309814   ELF Image Totals
      7452       1244       1788         80          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 9240 (   9.02kB)
    Total RW  Size (RW Data + ZI Data)              1136 (   1.11kB)
    Total ROM Size (Code + RO Data + RW Data)       9320 (   9.10kB)

==============================================================================

