..\obj\adc.o: ..\HAREWARE\ADC\adc.c
..\obj\adc.o: ..\HAREWARE\ADC\adc.h
..\obj\adc.o: ..\SYSTEM\sys\sys.h
..\obj\adc.o: ..\USER\stm32f4xx.h
..\obj\adc.o: ..\CORE\core_cm4.h
..\obj\adc.o: E:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
..\obj\adc.o: ..\CORE\core_cmInstr.h
..\obj\adc.o: ..\CORE\core_cmFunc.h
..\obj\adc.o: ..\CORE\core_cm4_simd.h
..\obj\adc.o: ..\USER\system_stm32f4xx.h
..\obj\adc.o: ..\USER\stm32f4xx_conf.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_adc.h
..\obj\adc.o: ..\USER\stm32f4xx.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_crc.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_dbgmcu.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_dma.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_exti.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_flash.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_gpio.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_i2c.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_iwdg.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_pwr.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_rcc.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_rtc.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_sdio.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_spi.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_syscfg.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_tim.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_usart.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_wwdg.h
..\obj\adc.o: ..\FWLIB\inc\misc.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_cryp.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_hash.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_rng.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_can.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_dac.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_dcmi.h
..\obj\adc.o: ..\FWLIB\inc\stm32f4xx_fsmc.h
..\obj\adc.o: ..\SYSTEM\delay\delay.h
..\obj\adc.o: ..\SYSTEM\sys\sys.h
..\obj\adc.o: ..\SYSTEM\usart\usart.h
..\obj\adc.o: E:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
..\obj\adc.o: ..\HAREWARE\ATD5984\ATD5984.h
..\obj\adc.o: E:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
..\obj\adc.o: E:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
..\obj\adc.o: E:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
