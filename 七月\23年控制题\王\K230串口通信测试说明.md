# K230串口通信测试说明

## 📋 **功能概述**

在原有的视觉识别系统基础上，新增了串口通信测试功能：
- **功能**：通过串口接收`'now'`命令，返回当前帧率
- **目的**：测试K230的串口通信是否正常工作
- **实现方式**：轮询模式，不影响视觉处理性能

## 🔌 **硬件连接**

### **K230端连接 (使用GH1.25-4P串口座)**
```
GH1.25-4P串口座 → 串口转TTL模块
T (GPIO11)      → TTL模块RX
R (GPIO12)      → TTL模块TX
G (GND)         → TTL模块GND
V (5V)          → TTL模块VCC
```

**引脚对应关系**：
- **V**: 5V输入电源口
- **R**: GPIO12 (UART2_RXD)
- **T**: GPIO11 (UART2_TXD)
- **G**: GND 接地点

### **电脑端连接**
```
串口转TTL模块 → 电脑USB口
使用串口助手软件进行通信测试
```

## ⚙️ **通信参数**

- **波特率**：115200 bps
- **数据位**：8位
- **停止位**：1位  
- **校验位**：无
- **流控制**：无

## 🎮 **测试步骤**

### **1. 硬件准备**
1. 按照连接图连接K230与串口转TTL模块
2. 将串口转TTL模块连接到电脑USB口
3. 确认连接无误，注意TX/RX交叉连接

### **2. 软件准备**
1. 在K230上运行修改后的`脱机运行屏幕+识别.py`
2. 在电脑上打开串口助手软件
3. 设置串口参数：115200, 8N1

### **3. 通信测试**
1. **启动程序**：运行K230程序，观察串口初始化信息
2. **发送命令**：在串口助手中发送`now`（不含引号）
3. **查看响应**：应该收到类似`FPS:25.6`的响应
4. **重复测试**：多次发送命令验证稳定性

## 📊 **预期结果**

### **正常情况**
```
发送: now
接收: FPS:25.6

发送: now  
接收: FPS:24.8

发送: now
接收: FPS:25.2
```

### **K230控制台输出**
```
初始化串口...
串口初始化完成，波特率: 115200
camera_test
收到命令: now, 回复帧率: 25.6
收到命令: now, 回复帧率: 24.8
```

## 🔧 **代码修改说明**

### **新增导入**
```python
from machine import UART, FPIOA
```

### **新增函数**
```python
def process_uart_command(uart, clock):
    """处理串口命令"""
    try:
        if uart.any():  # 检查是否有数据
            data = uart.readline()  # 读取一行数据
            if data:
                cmd = data.decode().strip()  # 解码并去除换行符
                if cmd.lower() == 'now':  # 检查是否为'now'命令
                    fps = clock.fps()  # 获取当前帧率
                    response = f"FPS:{fps:.1f}\r\n"  # 格式化响应
                    uart.write(response.encode())  # 发送响应
                    print(f"收到命令: {cmd}, 回复帧率: {fps:.1f}")
    except Exception as e:
        print(f"串口处理错误: {e}")
```

### **串口初始化 (使用GH1.25-4P串口座)**
```python
# 初始化串口通信 (使用GH1.25-4P串口座)
fpioa = FPIOA()
fpioa.set_function(11, fpioa.UART2_TXD)  # 设置GPIO11为UART2发送 (T引脚)
fpioa.set_function(12, fpioa.UART2_RXD)  # 设置GPIO12为UART2接收 (R引脚)

uart = UART(UART.UART2, baudrate=115200,
           bits=UART.EIGHTBITS,
           parity=UART.PARITY_NONE,
           stop=UART.STOPBITS_ONE)
```

### **主循环调用**
```python
while True:
    clock.tick()
    os.exitpoint()
    img = sensor.snapshot(chn=CAM_CHN_ID_0)
    
    # 处理串口命令 (新增功能)
    process_uart_command(uart, clock)
    
    # ... 原有的视觉处理代码 ...
```

## ⚠️ **注意事项**

### **硬件注意事项**
- 确保TX/RX引脚交叉连接（K230的T引脚连TTL的RX）
- GH1.25-4P串口座提供5V电源，但逻辑电平为3.3V
- 确保GND连接良好，避免通信异常
- 串口转TTL模块要选择3.3V/5V兼容的型号
- 使用标准的GH1.25-4P连接线或杜邦线连接

### **软件注意事项**
- 串口处理采用轮询模式，每10ms检查一次
- 命令不区分大小写，`now`、`NOW`、`Now`都可以
- 响应格式固定为`FPS:xx.x\r\n`
- 异常情况会在控制台输出错误信息

### **性能影响**
- 串口处理对视觉系统性能影响极小
- 帧率可能有±1fps的正常波动
- 不会影响原有的视觉识别功能

## 🔍 **故障排除**

### **1. 无响应**
- 检查硬件连接是否正确
- 确认串口参数设置（115200, 8N1）
- 检查K230程序是否正常启动

### **2. 乱码**
- 检查波特率设置是否一致
- 确认数据位、停止位、校验位设置
- 检查电压电平是否匹配

### **3. 间歇性通信**
- 检查GND连接是否良好
- 确认供电是否稳定
- 检查串口线缆质量

## 📈 **测试验收标准**

### **基本功能**
- ✅ 能够正确接收`now`命令
- ✅ 能够正确返回帧率数据
- ✅ 通信稳定，无丢包现象

### **性能要求**
- ✅ 响应时间 < 100ms
- ✅ 帧率波动 < ±2fps
- ✅ 视觉功能不受影响

### **稳定性要求**
- ✅ 连续测试30次无异常
- ✅ 长时间运行无通信中断
- ✅ 异常恢复能力良好

---

**版本信息**：V1.0  
**更新日期**：2024-07-22  
**适用硬件**：庐山派K230 + 串口转TTL模块  
**技术支持**：控制系统专家团队
