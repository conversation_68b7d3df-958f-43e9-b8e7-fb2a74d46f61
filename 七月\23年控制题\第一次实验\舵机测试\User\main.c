/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR> Analysis Expert
  * @version V1.0
  * @date    2025-07-17
  * @brief   STM32F407ZGT6 + HTS-25L舵机控制主程序
  ******************************************************************************
  * @attention
  *
  * 硬件配置：
  * - MCU: STM32F407ZGT6, 168MHz
  * - 舵机: HTS-25L (ID=1), 7.4V供电, 共地
  * - 通信: USART2半双工 PA2, 115200bps
  * - 按键: K1(PE3), 低电平有效
  * - LED: PF9心跳, PF10运动指示
  *
  * 功能：
  * - 按键K1每次30度步进 (0°→30°→...→240°→0°)
  * - 运动时间600ms, 等待700ms后允许下次按键
  * - 初始化时加载力矩、伺服模式、回零
  *
  ******************************************************************************
  */

#include "stm32f4xx.h"
#include "bsp_led.h"
#include "bsp_key.h"
#include "bsp_usart.h"
#include "hts25l_driver.h"
#include "app_servo.h"

/**
  * @brief  系统时钟配置为168MHz
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    RCC_HSEConfig(RCC_HSE_ON);
    while(RCC_GetFlagStatus(RCC_FLAG_HSERDY) == RESET);
    
    RCC_PLLConfig(RCC_PLLSource_HSE, 8, 336, 2, 7);  // 8MHz/8*336/2/7 = 168MHz
    RCC_PLLCmd(ENABLE);
    while(RCC_GetFlagStatus(RCC_FLAG_PLLRDY) == RESET);
    
    FLASH_SetLatency(FLASH_Latency_5);
    RCC_HCLKConfig(RCC_SYSCLK_Div1);   // AHB = 168MHz
    RCC_PCLK1Config(RCC_HCLK_Div4);    // APB1 = 42MHz
    RCC_PCLK2Config(RCC_HCLK_Div2);    // APB2 = 84MHz
    
    RCC_SYSCLKConfig(RCC_SYSCLKSource_PLLCLK);
    while(RCC_GetSYSCLKSource() != 0x08);
}

/**
  * @brief  SysTick配置为1ms中断
  * @param  None
  * @retval None
  */
void SysTick_Init(void)
{
    if (SysTick_Config(SystemCoreClock / 1000))  // 1ms
    {
        while (1);  // 配置失败
    }
}

/**
  * @brief  主函数
  * @param  None
  * @retval None
  */
int main(void)
{
    // 系统初始化
    SystemClock_Config();
    SysTick_Init();
    
    // BSP初始化
    BSP_LED_Init();
    BSP_Key_Init();
    BSP_USART2_Init();
    
    // 应用层初始化
    App_Servo_Init();
    
    // 主循环
    while (1)
    {
        App_Servo_Task();
    }
}

/**
  * @brief  SysTick中断处理函数
  * @param  None
  * @retval None
  */
void SysTick_Handler(void)
{
    static uint32_t led_counter = 0;
    
    // 系统滴答计数
    App_Servo_SysTick();
    
    // LED心跳闪烁 (500ms周期)
    led_counter++;
    if (led_counter >= 500)
    {
        led_counter = 0;
        BSP_LED_Toggle(LED_HEARTBEAT);
    }
}
